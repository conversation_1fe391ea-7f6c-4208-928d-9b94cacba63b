<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>hh</title>
		<script src="http://libs.baidu.com/jquery/2.0.0/jquery.min.js"></script>
		<script type="text/javascript" src="js/zoom-master/jquery.zoom.js"></script>
		
		<style>
			/* styles unrelated to zoom */
			* { border:0; margin:0; padding:0; }
			p { position:absolute; top:3px; right:28px; color:#555; font:bold 13px/1 sans-serif;}
		
			/* these styles are for the demo, but are not required for the plugin */
			.zoom {
				display:inline-block;
				position: relative;
			}
			
			/* magnifying glass icon */
			.zoom:after {
				content:'';
				display:block; 
				width:33px; 
				height:33px; 
				position:absolute; 
				top:0;
				right:0;
				background:url(icon.png);
			}
		
			.zoom img {
				display: block;
			}
		
			.zoom img::selection { background-color: transparent; }
		
			#ex2 img:hover { cursor: url(grab.cur), default; }
			#ex2 img:active { cursor: url(grabbed.cur), default; }
		</style>
	</head>
	<body>
		<span class="zoom" id="ex1">
			<img src="https://img1.baidu.com/it/u=3091723246,1718758483&fm=253&fmt=auto&app=138&f=JPEG?w=600&h=328" 
			width="450px" height="600px">
		</span>
		<span class="zoom" id="ex2">
			<img src="https://img1.baidu.com/it/u=3091723246,1718758483&fm=253&fmt=auto&app=138&f=JPEG?w=600&h=328" alt="">
		</span>
		<span class="zoom" id="ex3">
			<img src="https://img1.baidu.com/it/u=3091723246,1718758483&fm=253&fmt=auto&app=138&f=JPEG?w=600&h=328" alt="">
		</span>
		<span class="zoom" id="ex4">
			<img src="https://img1.baidu.com/it/u=3091723246,1718758483&fm=253&fmt=auto&app=138&f=JPEG?w=600&h=328" alt="">
		</span>
	</body>
	<script>
		$(document).ready(function(){
			$("#ex1").zoom();
			$("#ex2").zoom({on:'grab'});
			$("#ex3").zoom({on:'click'});
			$("#ex4").zoom({on:'toggle'});
		});
	</script>
</html>