<!DOCTYPE html>
<html>
<head>
	<meta charset='utf-8'/>
	<title>jQuery Zoom Demo</title>
	<style>
		/* styles unrelated to zoom */
		* { border:0; margin:0; padding:0; }
		p { position:absolute; top:3px; right:28px; color:#555; font:bold 13px/1 sans-serif;}

		/* these styles are for the demo, but are not required for the plugin */
		.zoom {
			display:inline-block;
			position: relative;
		}
		
		/* magnifying glass icon */
		.zoom:after {
			content:'';
			display:block; 
			width:33px; 
			height:33px; 
			position:absolute; 
			top:0;
			right:0;
			background:url(icon.png);
		}

		.zoom img {
			display: block;
		}

		.zoom img::selection { background-color: transparent; }

		#ex2 img:hover { cursor: url(grab.cur), default; }
		#ex2 img:active { cursor: url(grabbed.cur), default; }
	</style>
	<script src='http://ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js'></script>
	<script src='jquery.zoom.js'></script>
	<script>
		$(document).ready(function(){
			$('#ex1').zoom();
			$('#ex2').zoom({ on:'grab' });
			$('#ex3').zoom({ on:'click' });			 
			$('#ex4').zoom({ on:'toggle' });
		});
	</script>
</head>
<body>
	<span class='zoom' id='ex1'>
		<img src='daisy.jpg' width='555' height='320' alt='Daisy on the Ohoopee'/>
		<p>Hover</p>
	</span>
	<span class='zoom' id='ex2'>
		<img src='roxy.jpg' width='290' height='320' alt='Roxy on the Ohoopee'/>
		<p>Grab</p>
	</span>
	<span class='zoom' id='ex3'>
		<img src='daisy.jpg' width='555' height='320' alt='Daisy on the Ohoopee'/>
		<p>Click to activate</p>
	</span>
	<span class='zoom' id='ex4'>
		<img src='roxy.jpg' width='290' height='320' alt='Roxy on the Ohoopee'/>
		<p>Click to toggle</p>
	</span>
</body>
</html>
