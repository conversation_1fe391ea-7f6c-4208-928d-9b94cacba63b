<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title></title>
		<meta name="viewport" content="width=device-width,initial-scale=1.0"/>
		<!-- head 中 -->
		<link rel="stylesheet" href="https://cdn.bootcss.com/weui/1.1.3/style/weui.min.css">
		<link rel="stylesheet" href="https://cdn.bootcss.com/jquery-weui/1.2.1/css/jquery-weui.min.css">
		
		<link rel="stylesheet" href="css/icon/iconfont.css">
		<link rel="stylesheet" href="css/test.css"/>
	</head>
	<body>
	<div class="weui-tab">
	 <!-- <div class="weui-navbar">
	    <div class="weui-navbar__item weui-bar__item--on">
	      选项一
	    </div>
	    <div class="weui-navbar__item">
	      选项二
	    </div>
	    <div class="weui-navbar__item">
	      选项三
	    </div>
	  </div> -->
	  <div class="weui-tab__bd">
		<div id="tab1" class="weui-tab__bd-item weui-tab__bd-item--active">
			<div class="weui-navbar">
				<div class="weui-navbar__item weui-bar__item--on">
					热门商品
				</div>
				<div class="weui-navbar__item">
					天猫商品
				</div>
				<div class="weui-navbar__item ">
					聚划算
				</div>
				<div class="weui-navbar__item ">
					天猫超市
				</div>
				<div class="weui-navbar__item">
					天猫国际
				</div>
			</div>
			<!-- 轮播图 -->
			<div class="swiper-container" data-space-between='10' data-pagination='.swiper-pagination' data-autoplay="1000">
			  <div class="swiper-wrapper">
			    <div class="swiper-slide"><img src="//gqianniu.alicdn.com/bao/uploaded/i4//tfscom/i1/TB1n3rZHFXXXXX9XFXXXXXXXXXX_!!0-item_pic.jpg_320x320q60.jpg" alt=""></div>
			    <div class="swiper-slide"><img src="//gqianniu.alicdn.com/bao/uploaded/i4//tfscom/i4/TB10rkPGVXXXXXGapXXXXXXXXXX_!!0-item_pic.jpg_320x320q60.jpg" alt=""></div>
			    <div class="swiper-slide"><img src="//gqianniu.alicdn.com/bao/uploaded/i4//tfscom/i1/TB1kQI3HpXXXXbSXFXXXXXXXXXX_!!0-item_pic.jpg_320x320q60.jpg" alt=""></div>
			  </div>
			</div>
			<!-- 九宫格-->
			<div class="weui-grids">
			  <a href="" class="weui-grid js_grid">
			    <div class="weui-grid__icon">
			     <span class="icon iconfont">&#xe68d;</span>
				  </div>
			    <p class="weui-grid__label">
			      手机
			    </p>
			  </a>
			  <a href="" class="weui-grid js_grid">
			    <div class="weui-grid__icon">
			      <span class="icon iconfont">&#xe68e;</span>
			    </div>
			    <p class="weui-grid__label">
			      电脑
			    </p>
			  </a>
			  <a href="" class="weui-grid js_grid">
			    <div class="weui-grid__icon">
			      <span class="icon iconfont">&#xe68e;</span>
			    </div>
			    <p class="weui-grid__label">
			      游戏机
			    </p>
			  </a>
			  <a href="" class="weui-grid js_grid">
			    <div class="weui-grid__icon">
			      <span class="icon iconfont">&#xe68e;</span>
			    </div>
			    <p class="weui-grid__label">
			      游戏
			    </p>
			  </a>
			  <a href="" class="weui-grid js_grid">
			    <div class="weui-grid__icon">
			      <span class="icon iconfont">&#xe68e;</span>
			    </div>
			    <p class="weui-grid__label">
			      淘宝
			    </p>
			  </a>
			  <a href="" class="weui-grid js_grid">
			    <div class="weui-grid__icon">
			      <span class="icon iconfont">&#xe68e;</span>
			    </div>
			    <p class="weui-grid__label">
			      键盘
			    </p>
			  </a>
			  <a href="" class="weui-grid js_grid">
			    <div class="weui-grid__icon">
			      <span class="icon iconfont">&#xe68e;</span>
			    </div>
			    <p class="weui-grid__label">
			      鼠标
			    </p>
			  </a>
			  <a href="" class="weui-grid js_grid">
			    <div class="weui-grid__icon">
			      <span class="icon iconfont">&#xe68e;</span>
			    </div>
			    <p class="weui-grid__label">
			      我的
			    </p>
			  </a>
			  <a href="" class="weui-grid js_grid">
			    <div class="weui-grid__icon">
			      <span class="icon iconfont">&#xe68e;</span>
			    </div>
			    <p class="weui-grid__label">
			      影片
			    </p>
			  </a>
			</div>
		</div>
		<div id="tab2" class="weui-tab__bd-item">
		  <h1>页面二</h1>
		</div>
		<div id="tab3" class="weui-tab__bd-item">
		  <h1>页面三</h1>
		</div>
		<div id="tab4" class="weui-tab__bd-item">
		  <h1>页面四</h1>
		</div>
	  </div>
	
	  <div class="weui-tabbar">
	    <a href="#tab1" class="weui-tabbar__item weui-bar__item--on">
	      <span class="weui-badge" style="position: absolute;top: -.4em;right: 1em;">8</span>
	      <div class="weui-tabbar__icon">
	        <span class="icon iconfont">&#xe690;</span>
	      </div>
	      <p class="weui-tabbar__label">微信</p>
	    </a>
	    <a href="#tab2" class="weui-tabbar__item">
	      <div class="weui-tabbar__icon">
	       <span class="icon iconfont">&#xe694;</span>
	      </div>
	      <p class="weui-tabbar__label">通讯录</p>
	    </a>
	    <a href="#tab3" class="weui-tabbar__item">
	      <div class="weui-tabbar__icon">
	          <span class="icon iconfont">&#xe694;</span>
	      </div>
	      <p class="weui-tabbar__label">发现</p>
	    </a>
	    <a href="#tab4" class="weui-tabbar__item">
	      <div class="weui-tabbar__icon">
	            <span class="icon iconfont">&#xe694;</span>
	      </div>
	      <p class="weui-tabbar__label">我</p>
	    </a>
	  </div>
	</div>
		
	</body>

	<!-- body 最后 -->
	<script src="https://cdn.bootcss.com/jquery/1.11.0/jquery.min.js"></script>
	<script src="https://cdn.bootcss.com/jquery-weui/1.2.1/js/jquery-weui.min.js"></script>
	<!-- 如果使用了某些拓展插件还需要额外的JS -->
	<script src="https://cdn.bootcss.com/jquery-weui/1.2.1/js/swiper.min.js"></script>
	<script src="https://cdn.bootcss.com/jquery-weui/1.2.1/js/city-picker.min.js"></script>
	<script>
		$(".swiper-container").swiper({
			autoplay:true,
			speed:2500
		});
	</script>
	</html>