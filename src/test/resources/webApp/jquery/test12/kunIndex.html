<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>蔡徐坤首页</title>
    <link rel="stylesheet" href="../lib/bootstrap/css/bootstrap.css">
    <script src="../lib/jquery-3.5.1.min.js"></script>
    <script src="../lib/bootstrap/js/bootstrap.min.js"></script>

    <style>
        .dynamic-effect {
            animation: fadeEffect 2s infinite;
        }
        @keyframes fadeEffect {
            0% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }
        .carousel-inner div img{
            width: 100%;
            height: 300px;
            background: no-repeat center / cover;
        }
    </style>
</head>
<body>
<!-- Navbar -->
<nav class="navbar navbar-default">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand" href="#">蔡徐坤</a>
        </div>
    </div>
</nav>
<div class="row">
    <div class="col-md-2"></div>
    <div class="col-md-8">
        <div id="myCarousel"   class="carousel slide">
            <!-- 轮播（Carousel）指标 -->
            <ol class="carousel-indicators">
                <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
                <li data-target="#myCarousel" data-slide-to="1"></li>
                <li data-target="#myCarousel" data-slide-to="2"></li>
            </ol>
            <!-- 轮播（Carousel）项目 -->
            <div class="carousel-inner">
                <div class="item active">
                    <img src="https://img2.baidu.com/it/u=2867515989,2488076150&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800" alt="Image 1">
                </div>
                <div class="item">
                    <img src="https://img2.baidu.com/it/u=1153211692,308490162&fm=253&fmt=auto&app=120&f=JPEG?w=1280&h=800" alt="Image 2">
                </div>
                <div class="item">
                    <img src="https://img1.baidu.com/it/u=4103099682,3350373209&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500" alt="Image 3">
                </div>
            </div>
            <!-- 轮播（Carousel）导航 -->
            <a class="left carousel-control" href="#myCarousel" role="button" data-slide="prev">
                <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
                <span class="sr-only">Previous</span>
            </a>
            <a class="right carousel-control" href="#myCarousel" role="button" data-slide="next">
                <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
                <span class="sr-only">Next</span>
            </a>
        </div>
    </div>
    <div class="col-md-2" style="text-align: center">
        <p style="font-size: 5px">点击画个爱心给坤坤</p>
        <img  id="love"  width="45px" height="45px" src="img/love.webp" >
        <p style="font-size: 5px" id="kunLoves"></p>
    </div>
</div>


<!-- Dynamic Effects -->
<div class="container">
    <h2 class="dynamic-effect">蔡徐坤的最新专辑正在火热预售中！</h2>
    <p class="text-center dynamic-effect">别错过这个精彩的音乐盛宴！</p>
</div>


<!-- Footer -->
<footer class="text-center">
    <p>&copy; 2023 蔡徐坤. All rights reserved.</p>
</footer>


<script>

    //轮播
    $("#myCarousel").carousel({
        interval: 2000
    });
    $(function () {
        let iKunNumber = localStorage.getItem("iKunNumber");
        if (!iKunNumber){
            localStorage.setItem("iKunNumber",6666);
        }
        $("#kunLoves").text("坤坤收到❤"+iKunNumber);
    })
    // 爱心数增加
    $("div > img[id=love]").click(function (e) {
        let iKunNumber = localStorage.getItem("iKunNumber");
        iKunNumber = parseInt(iKunNumber)+1;
        $("#kunLoves").text("坤坤收到❤"+iKunNumber);
        localStorage.setItem("iKunNumber",iKunNumber);
        loveUp(e);
    })
    // 爱心上浮
    var a_idx = 0;
    function loveUp(e) {
     /*   let arr = new Array("❤kun❤","❤kun❤","❤kun❤","❤kun❤");
        let span = $("<span></span>").text(arr["❤kun❤"]);
        a_idx = (a_idx + 1) % arr.length;*/

        let span = $("<span></span>").text("❤kun❤");
        let x = e.pageX,
            y = e.pageY;
        span.css({
            "z-index": 999999999999999999999999999999999999999999999999999999999999999999999,
            "top": y - 20,
            "left": x,
            "position": "absolute",
            "font-weight": "bold",
            "color": "rgb("+~~(255*Math.random())+","+~~(255*Math.random())+","+~~(255*Math.random())+")"
        });
        $("body").append(span);
        //动画效果
        span.animate({
                "top": y - 260,
                "opacity": 0
        }, 1500, function() {
            span.remove();
            });
    }
</script>

</body>
</html>