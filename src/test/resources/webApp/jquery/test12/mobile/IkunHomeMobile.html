<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8" name="viewport" content="width=device-width,initial-scale=1.0">


    <title>iKunHome</title>
    <link rel="stylesheet" href="https://cdn.bootcss.com/weui/1.1.3/style/weui.min.css">
    <link rel="stylesheet" href="https://cdn.bootcss.com/jquery-weui/1.2.1/css/jquery-weui.min.css">
    <link rel="stylesheet" href="css/icon/iconfont.css">
    <link rel="stylesheet" href="css/test.css"/>

    <!-- body 最后 -->
    <script src="../../lib/jquery-3.5.1.min.js"></script>
    <script src="https://cdn.bootcss.com/jquery-weui/1.2.1/js/jquery-weui.min.js"></script>

    <!-- 如果使用了某些拓展插件还需要额外的JS -->
    <script src="https://cdn.bootcss.com/jquery-weui/1.2.1/js/swiper.min.js"></script>
    <script src="https://cdn.bootcss.com/jquery-weui/1.2.1/js/city-picker.min.js"></script>
<style>
    .toggleBC{
        background-color: #53ff6d;
    }

</style>
</head>
<body>
<div class="page">
    <header class="page__hd">
        <h1 class="page__title">iKun Home</h1>
    </header>
</div>
<div class="weui-tab">
    <div class="weui-tab__bd">
<!--        页面1-->
        <div id="tab1" class="weui-tab__bd-item weui-tab__bd-item--active weui-panel weui-panel_access">
            <!-- 轮播图 -->
            <div class="swiper-container" data-space-between='10' data-pagination='.swiper-pagination' data-autoplay="1000">
                <div class="swiper-wrapper">
                    <div class="swiper-slide"><img src="../img/bg1.jpg" alt=""></div>
                    <div class="swiper-slide"><img src="../img/bg2.jpg" alt=""></div>
                    <div class="swiper-slide"><img src="../img/bg3.jpg" alt=""></div>
                </div>
            </div>
            <!-- 九宫格-->
            <div class="weui-grids">

            </div>

        </div>
        <!--        页面2-->
        <div id="tab2" class="weui-tab__bd-item">
            <div class="weui-panel__hd">iKun语录 -- 点击语录可复制文本</div>
             <div class="weui-panel__bd">
                 <!-- 动态加载区-->
                 <div class="weui-panel__ft mainSayBody">

                 </div>
                 <div class="weui-panel__ft">
                     <a href="javascript:void(0);" class="weui-cell weui-cell_access weui-cell_link">
                         <div id="more" class="weui-cell__bd ">查看更多</div>
                         <span class="weui-cell__ft"></span>
                     </a>
                 </div>
             </div>
        </div>
    </div>


<!--    底部-->
    <div  class="weui-tabbar">
        <a href="#tab1" class="weui-tabbar__item weui-bar__item--on">
<!--            <span class="weui-badge" style="position: absolute;top: -.4em;right: 1em;">8</span>-->
            <div class="weui-tabbar__icon">
                <span class="icon iconfont">&#xe690;</span>
            </div>
            <p class="weui-tabbar__label">首页</p>
        </a>
        <a href="#tab2" id="iKunSay" class="weui-tabbar__item">
            <div class="weui-tabbar__icon">
                <span class="icon iconfont">&#xe694;</span>
            </div>
            <p class="weui-tabbar__label">iKun语录</p>
        </a>
    </div>

</div>

</body>
<!--页面1-->
<script>
    $(".swiper-container").swiper({
        autoplay:true,
        speed:2500
    });

    $(function () {

        indexIcon()
    })
    function indexIcon() {
        const arr = ["坤坤周边","坤坤签名","坤坤演唱会","坤坤小游戏","坤坤麻将","坤坤律师函","坤坤唱跳","坤坤rap","坤坤打篮球"]
        let icons = []
        $.ajax({
            url:"css/icon/iconfont.json",
            type:"get",
            contentType:"json",
            async:false,
            success:rs=>{
                icons = rs.glyphs;
            }
        })
        arr.forEach(data=>{
            let randomNumber = Math.floor(Math.random() * icons.length);
            let font_class = (icons[randomNumber].font_class);
            let a = $('<a href="#" class="weui-grid js_grid"></a>');
            let div = $('<div class="weui-grid__icon"></div>').append($('  <span class="icon iconfont"></span>').addClass("icon-"+font_class))
            let p = $(' <p class="weui-grid__label"></p>').text(data);
            a.append(div).append(p).appendTo($('.weui-grids'));
        })
    }
    $(document).on("click",".weui-grid",function () {
        $(this).toggleClass("toggleBC")
    })

</script>
<!--页面二-->
<script>

    //复制
    $(document).on("click",".mainSayBody:has(a) ",function () {
        let text = $(this).find("p").text();
        let input = document.createElement("textarea");
        input.value = text;
        document.body.appendChild(input);
        input.select();
        document.execCommand("copy");
        document.body.removeChild(input);
        $.toast("已复制到剪贴板！");

    })
    //点击导航栏时
    $("#iKunSay").click(function () {
        let data = getIKunSay(0,3);
        data.forEach(item=>{
            addSay(item);
        })
    })
    let curPage = 0;
    const pageSize = 3;
    // 点击获取更多数据
    $("#more").click(function () {
        //分页处理
        curPage += 1;
        let data = getIKunSay(curPage,pageSize);
        if (data){
            data.forEach(item=>{
                addSay(item);
            })
        }
        //已经没有数据
        if (data.length==0 || data.length<pageSize){
            $(".weui-panel__ft").children().find(".weui-cell__bd").text("已经加载全部数据");
        }
    })
    //获取数据
    function getIKunSay(startPage,pageSize) {
        let pageData = [];
        $.ajax({
            url:"../json/iKunSay.json",
            type:"get",
            contentType:"json",
            async:false,
            success:rs=>{
                if (rs.code=200){
                    const data = rs.data;
                    if (data.length<(startPage*pageSize)){
                        pageData = [];
                    }else {
                        for (let j=0,i = startPage*pageSize; i < (startPage+1)*pageSize && i<data.length; i++,j++) {
                            pageData[j] = data[i];
                        }
                    }
                }
            }
        })

        return pageData;
    }
    //数据渲染
    function addSay(data) {
        let a = $('<a href="javascript:void(0);" class="weui-media-box weui-media-box_appmsg">');
        let div = $('<div class="weui-media-box__hd">')
            .append($(' <img style="width: 63px;height: 63px" class="weui-media-box__thumb">').attr("src",data.imgAddress));
        let div2 = $('<div class="weui-media-box__bd">').append($(' <h4 class="weui-media-box__title">').text(data.name));
        let p  = $('<p class="weui-media-box__desc">').text(data.content);
        a.append(div).append(div2).append(p).appendTo($('.mainSayBody'));
    }
    //使标签至底
    $(".weui-panel__ft:last-child").css("padding-bottom","70px")
</script>

</html>