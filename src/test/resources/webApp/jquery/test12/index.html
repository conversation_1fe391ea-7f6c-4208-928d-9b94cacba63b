<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>ikunHome</title>
    <link rel="stylesheet" href="../lib/bootstrap/css/bootstrap.css">
    <script src="../lib/jquery-3.5.1.min.js"></script>
    <script src="../lib/bootstrap/js/bootstrap.min.js"></script>
</head>

<body>
<div class="row">
    <div class="col-md-12" >
        <nav class="navbar  navbar-inverse">
            <div class="container-fluid">
                <!-- Brand and toggle get grouped for better mobile display -->
                <div class="navbar-header">
                    <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                        <span class="sr-only">Toggle navigation</span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>
                    <a class="navbar-brand"  href="#">Ikun Home</a>
                </div>

                <!-- Collect the nav links, forms, and other content for toggling -->
                <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
                    <ul class="nav navbar-nav"  >
                        <li role="presentation" id="kunIndex"  class="active" >
                            <a href="#">首页</a>
                        </li>
                        <li role="presentation" id="kunShow">
                            <a class="nav-link"  href="#">坤坤的表演</a>
                        </li>
                        <li role="presentation" id="kunAbout">
                            <a class="nav-link"  href="#">关于坤坤</a>
                        </li>
                        <li role="presentation">
                            <a class="nav-link" href="javascript:window.location.href =kuniMG">查看ikun专属壁纸</a>
                        </li>
                    </ul>

                </div><!-- /.navbar-collapse -->
            </div><!-- /.container-fluid -->
        </nav>
        </ul>
    </div>
</div>
<!--主显示区-->
<div class="show_main" >

</div>
</body>
<script>
    const kuniMG = "https://p6.itc.cn/images01/20220623/3820703255234c36bd4b9cdbf1ed6e99.jpeg";
    const aboutIkun = "https://baike.baidu.com/link?url=fK9gRhjG_g7E-PrLTcM0yl9sY6iB4-pUaHXrKeksh_zG_ue0S8ZyGlGz34I2icQx7WSR1YQ3FWMZ_DQw_Tr2PdX_LV3hcS7ZUvbjt6wBDGu";

    let thisHref = window.location.href;
    thisHref = thisHref.substring(0,thisHref.indexOf("?"))
    thisHref = thisHref.substring(0,thisHref.lastIndexOf("/")+1)

    $(".navbar-nav li").click(function () {
        $(".active").removeClass("active");
       $(this).addClass("active");

        let htmlName =$(this).attr("id");
        if (htmlName){
            toshow(thisHref+(htmlName+".html"))
        }
    })
    $(function () {
        toshow(thisHref+"kunIndex.html")
    })
    function toshow(page){
        $(".show_main").load((page));
    }
</script>
</html>