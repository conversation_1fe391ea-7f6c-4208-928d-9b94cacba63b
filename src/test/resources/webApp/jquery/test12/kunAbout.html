<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>关于坤坤</title>
    <style>
        .highlight {
            font-weight: bold;
        }
        .font_toBig{
            font-size: 16px;
            font-weight: bold;
            color: red;
        }
        .jqzoom {
            border: 1px solid;
            float: left;
        }

        * {
            margin: 0;
            padding: 0;
        }
    </style>
    <link href="../lib/zoom/zoom.css" rel="stylesheet">
    <script src="../lib/zoom/zoom.js"></script>
</head>
<body>

<div class="row " style="border: #b9def0 solid">
    <div class="col-md-3">
        <div class="jqzoom">
            <img src="img/kunImg.jpg" data-action="zoom"  class="img-responsive"
                 jqimg="img/kunImg.jpg" alt="坤坤">
        </div>
    </div>

    <div class="col-md-9">
        <table style="border: #b9def0 solid;margin-top: 3%">
            <tr>
                <th>中文名</th>
                <td>蔡徐坤</td>
                <th>外文名</th>
                <td>KUN</td>
            </tr>
            <tr>
                <th>国籍</th>
                <td>中国</td>
                <th>民族</th>
                <td>汉族</td>
            </tr>
            <tr>
                <th>出生地</th>
                <td>浙江温州</td>
                <th>出生日期</th>
                <td>1998年8月2日</td>
            </tr>
            <tr>
                <th>星座</th>
                <td>狮子座</td>
                <th>血型</th>
                <td>A型</td>
            </tr>
            <tr>
                <th>身高</th>
                <td>184 cm</td>
                <th>体重</th>
                <td>60 kg</td>
            </tr>
            <tr>
                <th>职业</th>
                <td>歌手、演员、音乐制作人</td>
                <th>经纪公司</th>
                <td>蔡徐坤工作室</td>
            </tr>
            <tr>
                <th>户籍</th>
                <td>湖南吉首</td>
                <th>粉丝名</th>
                <td>IKUN</td>
            </tr>
            <tr>
                <th>性别</th>
                <td>男</td>
                <th>毕业学校</th>
                <td>深圳市南山区第二实验学校</td>
            </tr>
            <tr>
                <th>代表作品</th>
                <td colspan="3">重生、情人、Home、迷、YOUNG、没有意外、蒙着眼、Wait Wait Wait、Pull Up、You Can Be My GirlFriend、Hard To Get、IT'S YOU、Hug me</td>
            </tr>
            <tr>
                <th>主要成就</th>
                <td colspan="3">
                    第十五届MAHB年度先生盛典年度先生奖<br>
                    2019智族GQ年度人物盛典年度人气偶像奖<br>
                    中牙友好大使暨中牙杰出青年领袖人物<br>
                    2020TMEA音乐盛典年度最具号召力歌手、最具影响力创作歌手<br>
                    音乐盛典咪咕汇2020年度内地最佳男歌手、内地最受欢迎男歌手
                </td>
            </tr>
            <tr>
                <th colspan="8">
                   详细介绍<span style="color: pink;font-weight: bold">(点击黑色标签跳转)</span>
                </th>
            </tr>
            <tr>
             <td colspan="8" id="desc">

             </td>
            </tr>
        </table>
    </div>

</div>

</body>
<script>
    var descData = " 蔡徐坤（KUN），1998年8月2日出生于浙江省温州市，户籍湖南吉首 [139] ，中国内地男歌手、演员、原创音乐制作人 [1] 、MV导演 [107] 。\n" +
        "                 2012年8月，蔡徐坤参演的偶像剧《童话二分之一》播出，由此开始步入大众视线 [2] 。2018年1月，参加竞演类综艺节目《偶像练习生》并以总票数第一正式出道，成为限定男团NINE PERCENT队长 [3] ；8月，发行首张EP《1》 [4] ，获2018亚洲新歌榜年度盛典最受欢迎潜力男歌手奖 [5] ；随后，他还发行原创单曲《Wait Wait Wait》，并携手格莱美奖最佳MV获奖导演戴夫·迈尔斯打造歌曲MV。2019年1月，被授予“中牙友好大使暨中牙杰出青年领袖人物”称号 [8] ；3月，成功开启横跨三个国家、六座城市的海外公演《ONE》 [11-12]；7月，发行首张数字专辑《YOUNG》 [109] ；10月，携手中国儿童少年基金会共同设立“葵计划爱心基金”。\n" +
        "                 2020年3月，加盟《青春有你第二季》担任青春制作人代表 [15] ；4月9日，原创公益歌曲《Home》全网上线 [17] ；随后，加盟户外竞技真人秀《奔跑吧第四季》担任常驻MC [16] ；7月，获第27届东方风云榜最佳男歌手等三个奖项。2021年1月，获得第二届TMEA腾讯音乐娱乐盛典年度最具影响力唱作歌手和年度最具号召力歌手两个奖项，单曲《情人》获得年度十大金曲奖；4月13日，发行个人创作专辑《迷》 [105] ；7月17日，于北京凯迪拉克中心举办首场个人巡回演唱会，同年获第三届TMEA腾讯音乐娱乐盛典年度最具影响力制作人等多项荣誉 [48] 。\n" +
        "                 2023年1月24日，参加《奋进新征程——2023中国网络视听年度盛典》，演唱歌曲《默片》 [158] 。"
    $(document).ready(function () {
        loadDesc()
    })

    //放大镜
    $(document).ready(function() {
        $(".jqzoom").jqueryzoom({
            xzoom: 200, //放大图的宽度(默认是 200)
            yzoom: 200, //放大图的高度(默认是 200)
            offset: 10, //离原图的距离(默认是 10)
            position: "left", //放大图的定位(默认是 "right")
            preload: 1
        });
    });
    function loadDesc(){
        let text = descData;
        let start = 0;
        let end = 0;
        let temp = "";
        let p = $("<p>")
        while (start!=-1){
            start  =  text.indexOf("《");
            end = text.indexOf("》");
            temp = text.substring(0,end+1);

            let re  = temp.substring(start,temp.length);
            p.append(temp.substring(0,start));
            let src = "https://baike.baidu.com/item/"+re.substring(1,re.length-1)
            p.append($("<a></a>").attr("href",src).append($("<span></span>").addClass("highlight").text(re)))
            text = text.substring(end+1,text.length);
        }

        $("#desc").append(p)
    }

    $("table td").mouseover(function () {
        $(this).addClass("font_toBig")
        $(this).children().find(".highlight").css("color" , "royalblue");
    })
    $("table td").mouseleave(function () {
        $(this).removeClass("font_toBig")
        $(this).children().find(".highlight") .css("color" , "black");
    })

</script>
</html>