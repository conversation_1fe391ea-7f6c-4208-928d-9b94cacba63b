<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>坤坤的表演</title>

    <style>
        .comment-container {
            margin-top: 20px;
        }

        .comment-container .media-left {
            margin-right: 10px;
        }

        .comment-container {
            display: flex;
            flex-direction: row;
            justify-content: flex-start;
        }

        .comment-container .comment {
            margin-bottom: 10px;
        }

        .comment-container .comment .comment {
            margin-left: 40px;
        }

        .comment-container .panel-body:hover {
            cursor: pointer;
            background-color: #f5f5f5;
        }
        img{
            width: 63px;
            height: 63px;
        }
    </style>
</head>
<body>
<div class="row">
    <div class="col-md-12" style="text-align: center" >
        <video  autoplay loop="loop" src="video/kunShow.mp4" controls="true"></video>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="container">
            <h1>评论区</h1>
            <div id="comment-section" class="media-list">
                <!-- 评论内容将动态添加到这里 -->
            </div>
            <div class="form-group">
                <label for="comment">评论:</label>
                <textarea class="form-control" id="comment" rows="4" placeholder="请输入评论内容"></textarea>
            </div>
            <button type="button" class="btn btn-primary" id="submit-btn">提交评论</button>
        </div>
    </div>
</div>
</body>
<script>


    var myData = {
        "id": 66,
        "name": "ikun",
        "content": "ikun的评论 ikun的评论 ikun的评论 ikun的评论 ikun的评论 ikun的评论",
        "imgAddr": "img/icon3.png",
    }
    $(function () {
        getIndexData();
    })
    function getIndexData() {
        let AllData = [];
        $.ajax({
            url:"json/iKunShow.json",
            type:"get",
            async:false,
            contentType:"json",
            success:rs=>{
                //如果LS中有数据 则为数据源
                let LsData = localStorage.getItem("data");
                AllData = JSON.parse(LsData)
                if (!(AllData && AllData.length>0)){
                    //初始化or重置数据
                    AllData =rs.data;
                    localStorage.setItem("data",JSON.stringify(AllData));
                }
                console.log(AllData)
                AllData.forEach(function(comment) {
                    let commentHtml = createComment(comment);
                    $("#comment-section").append(commentHtml);
                });
            }
        })
    }

    function pushData(data ){
        let LsObj = localStorage.getItem("data");
        let LsData = JSON.parse(LsObj);
        LsData.push(data)
        localStorage.setItem("data",JSON.stringify(LsData))
    }
    // 提交评论按钮的点击事件
    $("#submit-btn").click(function() {
        let comment = myData;
        comment.content =  $("#comment").val();
        if (comment) {
            let commentHtml = createComment(comment);
            $("#comment-section").append(commentHtml);
            pushData(comment)
            $("#comment").val("");
        }
    });

    function createComment(comment) {
        let  commentHtml = $("<li>").addClass("media");
        let mediaLeft = $("<div>").addClass("media-left");
        let mediaObject = $('<img src="'+ comment.imgAddr+ '" >').addClass("media-object");
        let mediaBody = $("<div>").addClass("media-body");
        let heading = $("<h4>").addClass("media-heading").text(comment.name);
        let content = $("<p>").text(comment.content);

        mediaLeft.append(mediaObject);
        mediaBody.append(heading, content);
        commentHtml.append(mediaLeft, mediaBody);

        if (comment.replies && comment.replies.length > 0) {
            let repliesList = $("<ul>").addClass("media-list");
            comment.replies.forEach(function(reply) {
                let replyHtml = createComment(reply);
                repliesList.append(replyHtml);
            });
            mediaBody.append(repliesList);
        }

        return commentHtml;
    }
</script>
</html>