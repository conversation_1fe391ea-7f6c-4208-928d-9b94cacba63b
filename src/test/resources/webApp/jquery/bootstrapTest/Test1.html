<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>student system</title>
    <link rel="stylesheet" href="../lib/bootstrap/css/bootstrap.css">
    <script src="../lib/bootstrap/js/bootstrap.min.js"></script>
    <script src="../lib/jQuery-2.2.0.min.js"></script>
    <style>
        table tr th,td{
            text-align: center;
        }
        table tr th,td:last-child{
            width: 150px;
        }
    </style>
</head>
<body>
    <div class="row">
        <div class="col-md-2"></div>
        <div class="col-md-8">
            <nav class="navbar navbar-default" role="navigation">
                <div class="container-fluid">
                    <div class="navbar-header">
                        <a class="navbar-brand" href="#">学生管理系统</a>
                    </div>
                    <div>
                        <ul class="nav navbar-nav">
                            <li class="active"><a href="#">学生管理</a></li>
                            <li><a href="#">系统管理</a></li>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
        <div class="col-md-2"></div>
    </div>


  <div class="row">
      <div class="col-md-2"></div>
      <div class="col-md-8  test-center">
          <div class="row">
              <div class="col-md-4">
                  <div class="input-group">
                      <input id="searchText" type="text" class="form-control">
                      <span id="searchBtn" class="input-group-addon">搜索</span>
                  </div>
              </div>
              <div class="col-md-6"></div>
              <div class="col-md-2">
                  <a href="Test1Add.html">
                  <button type="button" class="btn btn-primary">添加</button>
                  </a>
              </div>
          </div>
      </div>
      <div class="col-md-2"></div>
  </div>

    <div class="row">
        <div class="col-md-2"></div>
        <div class="col-md-8  test-center">
            <table class="table table-bordered  table-hover table-striped">
                <caption>悬停表格布局</caption>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>姓名</th>
                        <th>性别</th>
                        <th>年龄</th>
                        <th>专业</th>
                        <th>Do</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                    <tr>
                        <td>1001</td>
                        <td>Tanmay</td>
                        <td>男</td>
                        <td>1</td>
                        <td>语文</td>
                        <td >
                            <button type="button" class="btn btn-info btn-xs ">修改</button>
                            <button type="button"  class="btn btn-danger btn-xs ">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    <div class="col-md-2"></div>
    </div>
</body>
<script>
    let stuData = [];//全局
    $(function () {
        $.ajax({
            url:"json/Test1JsonData.json",
            type:"get",
            contentType:"json",
            success:function (rs) {
                $("#tableBody").text("");//初始化表单
                let LsData = localStorage.getItem("data");
                console.log(JSON.parse(LsData))
                stuData = JSON.parse(LsData)
                if (!(stuData && stuData.length>0)){
                    //初始化or重置数据
                    stuData =rs.data;
                    localStorage.setItem("data",JSON.stringify(stuData));
                }
                show(stuData);
            }
        })
    })
    function show(data){
        let html = "";
        data.forEach(e=>{
            html +="<tr stuId='"+e.id+"'>\n" +
                "            <td>"+e.id+"</td>\n" +
                "            <td>"+e.name+"</td>\n" +
                "            <td>"+e.sex+"</td>\n" +
                "            <td>"+e.age+"</td>\n" +
                "            <td>"+e.subject+"</td>\n" +
                "            <td><button type='button'  class='edit btn btn-info btn-xs' stuId='"+e.id+"'>修改</button>\n" +
                "               <button type='button' class='del btn btn-danger btn-xs' stuId='"+e.id+"'>删除</button></td>\n" +
                "        </tr>"
        })
        $("#tableBody").prepend(html)
    }
    $(document).on("click",".del",function () {
        let id = $(this).closest("tr").attr("stuId");
        if (stuData){
            stuData.forEach((item,i)=>{
                if (item.id == id){
                    //delete
                    stuData.splice(i,1);
                    //重新存入LS
                    localStorage.setItem("data",JSON.stringify(stuData))
                    return;
                }
            })
        }
        $(this).closest("tr").remove();
    })
    $(document).on("click",".edit",function () {
        let trId =$(this).closest("tr").attr("stuId");
        if (stuData) {
            stuData.forEach(item=>{
                if (item.id == trId){
                    location.href="Test1Edit.html?"+item.id;
                }
            })
        }
    })
    //搜索
    $("#searchBtn").click(function () {
        $("#tableBody").text("");//初始化表单
        const searchText = $("#searchText").val();
        console.log(searchText);
        if (searchText==null || searchText==''){
            alert("输入关键词")
        }
        let data =[];
        stuData.forEach((item)=>{
            if (item.id.toString().indexOf(searchText)>-1 ||item.name.indexOf(searchText)>-1 || item.subject.indexOf(searchText)>-1){
                data.push(item)
            }
        })
        show(data);
    })
</script>
</html>

