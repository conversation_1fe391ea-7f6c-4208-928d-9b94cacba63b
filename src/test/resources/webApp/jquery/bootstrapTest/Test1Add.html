<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>student system---添加</title>
    <link rel="stylesheet" href="../lib/bootstrap/css/bootstrap.css">
    <script src="../lib/bootstrap/js/bootstrap.min.js"></script>
    <script src="../lib/jQuery-2.2.0.min.js"></script>
    <style>
    </style>
</head>
<body>
    <div class="row">
        <div class="col-md-2"></div>
        <div class="col-md-8">
            <nav class="navbar navbar-default" role="navigation">
                <div class="container-fluid">
                    <div class="navbar-header">
                        <a class="navbar-brand" href="#">学生管理系统</a>
                    </div>
                    <div>
                        <ul class="nav navbar-nav">
                            <li class="active"><a href="Test1.html">学生管理</a></li>
                            <li><a href="#">系统管理</a></li>
                        </ul>
                    </div>
                </div>
            </nav>
        </div>
        <div class="col-md-2"></div>
    </div>

<!-- input-->

    <div class="row">
        <div class="col-md-4"></div>
        <div class="col-md-4">
            <form action="Test1.html" method="get" name="addForm" role="form" onsubmit="return save()">
                <div class="form-group">
                    <label for="id">id</label>
                    <input type="text" class="form-control" id="id" placeholder="请输入名称">
                </div>
                <div class="form-group">
                    <label for="name">名称</label>
                    <input type="text" class="form-control" id="name" placeholder="请输入名称">
                </div>
                <div class="form-group">
                    <label for="name">性别</label>
                    <input type="text" class="form-control" id="sex" placeholder="请输入名称">
                </div>

                <div class="form-group">
                <label for="name">年龄</label>
                <input type="text" class="form-control" id="age" placeholder="请输入名称">
                </div>
                <div class="form-group">
                    <label for="name">专业</label>
                    <input type="text" class="form-control" id="subject" placeholder="请输入名称">
                </div>

                <button type="submit" class="btn btn-default">提交</button>
            </form>
        </div>
        <div class="col-md-4"></div>
    </div>


</body>
<script>
    function save() {
        const formData = document.addForm;
        //校验
        let id = formData.id.value;
        let name = formData.name.value;
        let sex = formData.sex.value;
        let age = formData.age.value;
        let subject = formData.subject.value;


        if (id && name && sex && subject){
            //push
            let LsData = localStorage.getItem("data");
            let data = JSON.parse(LsData);
            let stu = {
                id:id,
                name:name,
                sex:sex,
                age:age,
                subject:subject
            }
            data.push(stu)
            localStorage.setItem("data",JSON.stringify(data))
            console.log(localStorage.getItem("data"))
            return true;
        }
        alert("请填写完整")
        return false;
    }
</script>
</html>

