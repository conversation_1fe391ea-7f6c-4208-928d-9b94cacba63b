<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src="../../lib/jQuery-2.2.0.min.js"></script>
</head>
<body>
<div style="width: 300px;padding-left: 38%">
    <form id="myForm" name="myForm" onsubmit="return false">
        <fieldset>
            <legend>个人信息</legend>
            <div>
                <label for="name">姓名：</label>
                <input type="text" id="name" name="name">
            </div>
            <div>
                <label for="gender">性别：</label>
                <label><input id="gender" type="radio" name="gender" value="男" checked>男</label>
                <label><input type="radio" name="gender" value="女">女</label>
            </div>
            <div>
                <label for="age">年龄：</label>
                <input type="number" id="age" name="age">
            </div>
            <div>
                <label for="email">邮箱：</label>
                <input type="email" id="email" name="email" placeholder="<EMAIL>">
            </div>
            <div>
                <label>爱好：</label>
                <label><input type="checkbox" name="hobby" value="阅读">阅读</label>
                <label><input type="checkbox" name="hobby" value="运动">运动</label>
                <label><input type="checkbox" name="hobby" value="旅游">旅游</label>
            </div>
        </fieldset>
        <fieldset>
            <legend>留言信息</legend>
            <div>
                <label for="subject">主题：</label>
                <select id="subject" name="subject">
                    <option value="问题咨询">问题咨询</option>
                    <option value="意见反馈">意见反馈</option>
                    <option value="other">其他</option>
                </select>
            </div>
            <div>
                <label for="message">留言：</label>
                <textarea id="message" name="message" rows="5"></textarea>
            </div>
        </fieldset>
        <div style="text-align: center">
            <button id="btn" type="submit">提交</button>
            <button type="reset">重置</button>
        </div>
    </form>
</div>
</body>
<script>
    $("#btn").click(function () {
        var formDataArr = $('#myForm').serializeArray();
        let str = ""
        formDataArr.forEach(function (item) {
            let name = item.name;
            let value = item.value;
            if (name && !value){
                value = "";
            }
            str += name+":"+value;
        })
        console.log(str)
    })
</script>
</html>