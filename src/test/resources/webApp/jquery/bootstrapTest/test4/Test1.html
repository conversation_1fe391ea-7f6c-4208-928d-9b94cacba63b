<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>hhDemo</title>
</head>
<script src="../../lib/jQuery-2.2.0.min.js"></script>
<style>
    .toRed{
        color: red;
    }
</style>
<body>
<div id="container">
    <div class="item">Item 1</div>
    <div class="item">Item 2</div>
    <div class="item">Item 3</div>
    <div class="item">Item 4</div>
    <input type="radio">
</div>
</body>
<script>
    $(document).ready(function() {
        // 事件1 文字变红
        $("#container").on("click", ".item", function() {
            $(this).toggleClass("toRed");
        });

        // 效果2 放大
        $("#container").on("mouseenter", ".item", function() {
            $(this).animate({ fontSize: "24px" }, "fast");
        }).on("mouseleave", ".item", function() {
            $(this).animate({ fontSize: "16px" }, "fast");
        });
    });
</script>
</html>