<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>JQ</title>
    <script src="../../lib/jQuery-2.2.0.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        #box {
            width: 500px;
            height: 300px;

            transition: transform 0.5s ease-in-out;
            background-color: red;
            position: relative;
        }

        #box.active {
            transform: scale(1.5);
        }

    </style>
</head>
<body>
<div id="box"></div>
</body>
<script>

    var isMouseDown = false;

    $("#box").mousedown( function(event) {
        isMouseDown = true;
        box.classList.add('active');
    });
    $("#box").mouseup( function(event) {
        box.classList.remove('active');
        isMouseDown = false;
    })
    $("#box").mousemove(  function(event) {
        if (isMouseDown) {
            var x = event.pageX;
            var y = event.pageY;
            var circle = document.createElement("div");
            circle.style.width = "10px";
            circle.style.height = "10px";
            circle.style.backgroundColor = "#fff";
            circle.style.position = "absolute";
            circle.style.left = x - 5 + "px";
            circle.style.top = y - 5 + "px";
            circle.style.borderRadius = "50%";
            box.appendChild(circle);
        }
    });
</script>
</html>