<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Dom</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        #box {
            width: 500px;
            height: 300px;

            transition: transform 0.5s ease-in-out;
            background-color: red;
            position: relative;
        }

        #box.active {
            transform: scale(1.5);
        }

    </style>
</head>
<body>
<div id="box"></div>
</body>
<script>

    var box = document.getElementById('box');
    var isMouseDown = false;

    box.addEventListener('mousedown', function(event) {
        isMouseDown = true;
        box.classList.add('active');
    });
    box.addEventListener('onmouseup', function(event) {
        box.classList.remove('active');
        isMouseDown = false;
    });
    box.addEventListener('mousemove', function(event) {
        if (isMouseDown) {
            var x = event.pageX;
            var y = event.pageY;
            var circle = document.createElement("div");
            circle.style.width = "10px";
            circle.style.height = "10px";
            circle.style.backgroundColor = "#fff";
            circle.style.position = "absolute";
            circle.style.left = x - 5 + "px";
            circle.style.top = y - 5 + "px";
            circle.style.borderRadius = "50%";
            box.appendChild(circle);
        }
    });
</script>
</html>