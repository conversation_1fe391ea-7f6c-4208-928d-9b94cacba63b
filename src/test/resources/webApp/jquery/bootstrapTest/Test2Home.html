<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>菜鸟旅游</title>
    <!-- 旅游主题首页 -->
    <link rel="stylesheet" href="../lib/bootstrap/css/bootstrap.css">
    <script src="../lib/jQuery-2.2.0.min.js"></script>
    <script src="../lib/bootstrap/js/bootstrap.min.js"></script>
    <style>
        .dl nav {
            background:white;
        }
        .navbar-default .navbar-nav > li > a{
            color: black;
        }
        .navbar-default .navbar-brand {
            color: black;
        }
        .navbar-default .navbar-nav >.active
        > a,.navbar-default .navbar-nav >.active
        > a:hover,.navbar-default .navbar-nav >.active a:focus {
            color: red;
            background-color:#31b0d5;
        }
    </style>
</head>
<body>
<!-- 导航栏 -->
<div class="row">
    <div class="col-md-1"></div>
    <div class="col-md-9 dl">
        <nav class="navbar navbar-default" role="navigation">
            <div class="container-fluid">
                <div class="navbar-header">
                    <a class="navbar-brand" href="#">菜鸟旅游</a>
                </div>
                <div>
                    <!--向左对齐-->
                    <ul class="nav navbar-nav navbar-left">
                        <li class="active"><a href="#">首页</a></li>
                        <li><a href="#">旅游推荐</a></li>
                        <li><a href="#">旅游新闻</a></li>
                        <li><a href="#">旅游攻略</a></li>
                        <li><a href="#">热门景点</a></li>
                        <li><a href="#">关于我们</a></li>
                        <!-- <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                Java
                                <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a href="#">jmeter</a></li>
                                <li><a href="#">EJB</a></li>
                                <li><a href="#">Jasper Report</a></li>
                                <li class="divider"></li>
                                <li><a href="#">分离的链接</a></li>
                                <li class="divider"></li>
                                <li><a href="#">另一个分离的链接</a></li>
                            </ul>
                        </li> -->
                    </ul>
                    <!-- <form class="navbar-form navbar-left" role="search">
                        <button type="submit" class="btn btn-default">
                            向左对齐-提交按钮
                        </button>
                    </form>
                    <p class="navbar-text navbar-left">向左对齐-文本</p> -->
                    <!--向右对齐-->
                    <ul class="nav navbar-nav navbar-right">
                        <li><a href="#">登录</a></li>
                        <li><a href="#">注册</a></li>
                        <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                个人中心 <b class="caret"></b>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a href="#">个人信息</a></li>
                                <li><a href="#">修改密码</a></li>
                                <li><a href="#">退出登录</a></li>
                            </ul>
                        </li>
                    </ul>
                    <!-- <form class="navbar-form navbar-right" role="search">
                        <button type="submit" class="btn btn-default">
                            向右对齐-提交按钮
                        </button>
                    </form> -->
                    <!-- <p class="navbar-text navbar-right">向右对齐-文本</p> -->
                </div>
            </div>
        </nav>
    </div>
    <div class="col-md-1"></div>
</div>
<!-- 轮播图 -->
<div class="row">
    <div class="col-md-1"></div>
    <div class="col-md-9">
        <div id="myCarousel"   class="carousel slide">
            <!-- 轮播（Carousel）指标 -->
            <ol class="carousel-indicators">
                <li data-target="#myCarousel" data-slide-to="0" class="active"></li>
                <li data-target="#myCarousel" data-slide-to="1"></li>
                <li data-target="#myCarousel" data-slide-to="2"></li>
            </ol>
            <!-- 轮播（Carousel）项目 -->
            <div class="carousel-inner">
                <div class="item active">
                    <img decoding="async"  style="display: block; width: 100%; height: 100%" src="../img/Rotation/1.webp" alt="First slide">
                </div>
                <div class="item">
                    <img decoding="async"  style="display: block; width: 100%; height: 100%" src="../img/Rotation/2.webp" alt="Second slide">
                </div>
                <div class="item">
                    <img decoding="async"  style="display: block; width: 100%; height: 100%"src="../img/Rotation/3.webp" alt="Third slide">
                </div>
            </div>
            <!-- 轮播（Carousel）导航 -->
            <a class="left carousel-control" href="#myCarousel" role="button" data-slide="prev">
                <span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>
                <span class="sr-only">Previous</span>
            </a>
            <a class="right carousel-control" href="#myCarousel" role="button" data-slide="next">
                <span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>
                <span class="sr-only">Next</span>
            </a>
        </div>
    </div>
    <div class="col-md-1"></div>
</div>
<!-- 景点介绍 -->
<div class="row">
    <div class="col-md-1"></div>
    <div class="col-md-9">

        <div class="row">
            <div style="font-size: 20px;height: 48px;font-weight: 600;color:#66afe9">
                <span style="padding-left: 15px;display:inline-block;margin-top: 18px">景点介绍</span>
                <span style="padding-right: 15px;display:inline-block;margin-top: 18px;float: right">更多>>></span>
            </div>
            <div class="col-sm-6 col-md-3">
                <div class="thumbnail">
                    <img decoding="async" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F37e1cd08-55a5-32d3-a040-e79719de55e5%3FimageView2%2F2%2Fw%2F540%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1683424752&t=c7dc41133f6427a0fa79cb77e5b85248"
                         alt="通用的占位符缩略图">
                    <div class="caption">
                        <h3>经典1</h3>
                        <p>一些示例文本。一些示例文本。</p>
                    <!--    <p>
                            <a href="#" class="btn btn-primary" role="button">
                                按钮
                            </a>
                            <a href="#" class="btn btn-default" role="button">
                                按钮
                            </a>
                        </p>-->
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-3">
                <div class="thumbnail">
                    <img decoding="async" src="https://img1.baidu.com/it/u=1958982953,975282393&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=667"
                         alt="通用的占位符缩略图">
                    <div class="caption">
                        <h3>经典2</h3>
                        <p>一些示例文本。一些示例文本。</p>
                     <!--   <p>
                            <a href="#" class="btn btn-primary" role="button">
                                按钮
                            </a>
                            <a href="#" class="btn btn-default" role="button">
                                按钮
                            </a>
                        </p>-->
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-3">
                <div class="thumbnail">
                    <img decoding="async" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F5de2c4f3-d6e2-324d-b5ba-d674c60f1e56%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1683424802&t=c3e78d6358de2e41f43e418d733ce13f"
                         alt="通用的占位符缩略图">
                    <div class="caption">
                        <h3>静海</h3>
                        <p>一些示例文本。一些示例文本。</p>
                      <!--  <p>
                            <a href="#" class="btn btn-primary" role="button">
                                按钮
                            </a>
                            <a href="#" class="btn btn-default" role="button">
                                按钮
                            </a>
                        </p>-->
                    </div>
                </div>
            </div>
            <div class="col-sm-6 col-md-3">
                <div class="thumbnail">
                    <img decoding="async" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F454f804c-7b8d-3c77-aaa0-0d85d8f3cb29%3FimageView2%2F2%2Fw%2F540%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1683424752&t=a1b3406b9c4ce300a169415884405126"
                         alt="通用的占位符缩略图">
                    <div class="caption">
                        <h3>经典4</h3>
                        <p>一些示例文本。一些示例文本。</p>
                      <!--  <p>
                            <a href="#" class="btn btn-primary" role="button">
                                按钮
                            </a>
                            <a href="#" class="btn btn-default" role="button">
                                按钮
                            </a>
                        </p>-->
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="col-md-1"></div>

</div>
<!-- 旅游攻略 -->
<div class="row">
    <div class="col-md-1"></div>
    <div class="col-md-9">
        <!-- 左对齐 -->
        <div class="media">
            <div class="media-left">
                <img decoding="async" style="width: 500px;height: auto" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F01027c0173nwupw1xya011d0k7nchwj23e%3FimageView2%2F2%2Fw%2F540%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1683426180&t=a79d4c274da8e6cbeb67939fff4fed39" class="media-object" style="width:60px">
            </div>
            <div class="media-body">
                <h4 class="media-heading">新疆攻略</h4>
                <p>新疆维吾尔自治区（维吾尔语：شىنجاڭ ئۇيغۇر ئاپتونوم رايونى [78-79]  ），简称“新”，是中华人民共和国自治区，首府乌鲁木齐市，位于中国西北地区，是中国五个少数民族自治区之一。面积166.49万平方公里，是中国陆地面积最大的省级行政区，约占中国国土总面积的六分之一。2021年，自治区常住人口为2589万人。</p>
            </div>
        </div>

        <!-- 右对齐 -->
        <div class="media">
            <div class="media-body">
                <h4 class="media-heading">西藏攻略</h4>
                <p>西藏自治区，简称“藏”，首府拉萨市，位于中华人民共和国西南地区，是中国五个少数民族自治区之一。西藏位于青藏高原西南部，地处北纬26°50′至36°53′，东经78°25′至99°06′之间，平均海拔在4000米以上，素有“世界屋脊”之称。面积122.84万平方公里， [83]  约占全国总面积的1/8，在全国各省、市、自治区中仅次于新疆。 [2]  2021年，西藏常住人口366万人。 [73]  2022年西藏GDP为2132.64亿元，同比增长1.1%。</p>
            </div>
            <div class="media-right">
                <img decoding="async" style="width: 500px;height: auto" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F04ab75b3-cb26-3aca-9bd2-31957073b3bd%3FimageView2%2F2%2Fw%2F540%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1683426201&t=63518466184568140753873c35ea2840" class="media-object" style="width:60px">
            </div>
        </div>
    </div>
    <div class="col-md-1"></div>
</div>
</body>
</html>