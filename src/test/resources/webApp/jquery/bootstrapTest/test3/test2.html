<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ajax练习</title>
    <script src="../js/jquery-3.1.0.js"></script>
    <script>
        $(function () {
            $("input[type=button]").on("click", function () {
                //获取输入的值
                var realname = $("input[name=realname]").val();
                var role = $("select").val();
                //发送get请求
                $.ajax({
                    url: "http://localhost:8080/get",
                    data: {
                        realname: realname,
                        role: role
                    },
                    type: "GET",
                    success: function (data) {
                        console.log(123);
                    }
                })
            })
            //POST 请求
            $ ("button").on("click",function (){
                //获取元素的值
                var sex = $("input[name=sex]:checked").val();
                var aihao = $("input[name=aihao]");
                var aihaoArr = [];
                for(var i = 0;i < aihao.length;i++){
                    if(aihao.eq(i).prop("checked"))
                        aihaoArr.push(aihao.eq(i).val());
                }
                console.log(sex,aihaoArr);
                //发送post请求
                $.ajax({
                    url:"http://localhost:8080/post",
                    data:{
                        sex:sex,
                        aihao:aihaoArr
                    },
                    type:"post",
                    success:function(data){
                        console.log(123);
                    },
                    error:function(error){
                        console.log("发送错误")
                    }
                })
            })
        })
    </script>
</head>

<body>
<h1>GET请求</h1>
姓名：<input type="text" name="realname"><br>
角色：<select name="role" id="">
    <option value="">请选择角色</option>
    <option value="管理员">管理员</option>
    <option value="VIP用户">VIP用户</option>
    <option value="普通用户">普通用户</option>
</select><br>
<input type="button" value="提交">
<h1>POST 请求</h1>
性别:
男 <input type="radio" name="sex" value="男">
女 <input type="radio" name="sex" value="女"><br>
爱好:
羽毛球 <input type="checkbox" name="aihao" value="羽毛球">
排球 <input type="checkbox" name="aihao" value="排球">
篮球 <input type="checkbox" name="aihao" value="篮球">
乒乓球 <input type="checkbox" name="aihao" value="乒乓球">
<button>提交</button>

</body>

</html>