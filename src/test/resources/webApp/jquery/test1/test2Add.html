<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src="../lib/jQuery-2.2.0.min.js"></script>
</head>
<body>
<div  align="center">
    id:<input name="id"><br>
    name:<input name="name"><br>
    sex:<input name="sex"><br>
    age:<input name="age"><br>
    zhuanye:<input name="zhuanye"><br>
    <button style="margin: 10px 0px 10px 200px;width: 100px;height: 30px" id="save">保存</button>
</div>

</body>
<script>

    $("#save").click(function () {
        let id = $("[name='id']").val()
        let name = $("input[name='name']").val()
        let sex = $("input[name='sex']").val()
        let age = $("input[name='age']").val()
        let zhuanye = $("input[name='zhuanye']").val()

        let stu = {
            id:id,
            name:name,
            sex:sex,
            age:age,
            zhuanye:zhuanye
        }
        //push
        let LsData = localStorage.getItem("data");
        let data = JSON.parse(LsData);
        data.push(stu)
        localStorage.setItem("data",JSON.stringify(data))
        location.href="test2.html"
    })
</script>
</html>