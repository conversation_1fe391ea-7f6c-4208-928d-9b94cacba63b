<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src="../lib/jQuery-2.2.0.min.js"></script>
</head>
<body>
<div  align="center">
    id:<input name="id"><br>
    name:<input name="name"><br>
    sex:<input name="sex"><br>
    age:<input name="age"><br>
    zhuanye:<input name="zhuanye"><br>
    <button style="margin: 10px 0px 10px 200px;width: 100px;height: 30px" id="edit">修改</button>
</div>


</body>
<script>

    let index = 0;
    let data = null;
    $(function () {
        let id = location.search.substring(1,location.search.length);
        let dataOBJ = localStorage.getItem("data");
        data =JSON.parse(dataOBJ);
        data.forEach((item,i)=>{
             if (item.id==id){
                 console.log(item.name)
                 $("[name='id']").val(item.id)
                 $("input[name='name']").val(item.name)
                 $("input[name='sex']").val(item.sex)
                 $("input[name='age']").val(item.age)
                 $("input[name='zhuanye']").val(item.zhuanye)
                 index = i;
             }
         })
    })
    $("#edit").click(function () {
        let id = $("[name='id']").val()
        let name = $("input[name='name']").val()
        let sex = $("input[name='sex']").val()
        let age = $("input[name='age']").val()
        let zhuanye = $("input[name='zhuanye']").val()

        let stu = {
            id:id,
            name:name,
            sex:sex,
            age:age,
            zhuanye:zhuanye
        }
        //替换
        data.splice(index,1,stu);
        localStorage.setItem("data",JSON.stringify(data))
        location.href="test2.html"
    })
</script>
</html>