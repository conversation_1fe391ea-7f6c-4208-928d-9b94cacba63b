<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src="../lib/jQuery-2.2.0.min.js"></script>
    <style>
        body{
            color: coral;
            background: white;
            text-align: center;
        }
        .tb{
            width: 700px;
            padding: 0;
            margin: 0 auto;
        }
        th{
            font-weight: bold;
            color: black;
            border-right: 1px solid royalblue;
            border-bottom: 1px solid royalblue;
            border-top: 1px solid royalblue;
            letter-spacing: 2px;
            text-transform: uppercase;
            text-align: center;
            padding: 6px 6px 6px 12px;
            background: cornflowerblue;
        }
        td{
            border-right: 1px solid royalblue;
            border-bottom: 1px solid royalblue;
            color: red;
            padding: 6px 6px 6px 12px;
            background: azure;
        }
    #add{
        width: 100px;
        height: 30px;
        margin: 10px 0px 10px 400px;
    }
    </style>
</head>
<body>
<h1>学生列表</h1>
<div>
    <a href="test2Add.html" style="padding-right: 50px"><button id="add">添加</button></a>
    <table class="tb">
        <tr>
            <th style="border-left: 1px solid royalblue;">id</th>
            <th>名字</th>
            <th>性别</th>
            <th>年龄</th>
            <th>专业</th>
            <th>do</th>
        </tr>
        <tbody class="bb">

        </tbody>
    </table>
</div>
<script>
    let stuData = [];
    $(function () {
        $.ajax({
            url:"data.json",
            type:"get",
            contentType:"json",
            success:rs=>{
                //如果LS中有数据 则为数据源
                let LsData = localStorage.getItem("data");
                stuData = JSON.parse(LsData)
                if (!(stuData && stuData.length>0)){
                    //初始化or重置数据
                    stuData =rs.data;
                    localStorage.setItem("data",JSON.stringify(stuData));
                }
                let html = "";
                stuData.forEach(e=>{
                    html +="<tr stuId='"+e.id+"'>\n" +
                        "            <td style=' border-left: 1px solid royalblue;'>"+e.id+"</td>\n" +
                        "            <td>"+e.name+"</td>\n" +
                        "            <td>"+e.sex+"</td>\n" +
                        "            <td>"+e.age+"</td>\n" +
                        "            <td>"+e.zhuanye+"</td>\n" +
                        "            <td><button class='del' stuId='"+e.id+"'>删除</button>\n" +
                        "               <button class='edit' stuId='"+e.id+"'>修改</button></td>\n" +
                        "        </tr>"
                })
                $(".bb").html(html);
            }
        })
    })
    $(document).on("click",".del",function () {
        let id = $(this).closest("tr").attr("stuId");
       if (stuData){
           stuData.forEach((item,i)=>{
               if (item.id == id){
                   //delete
                   stuData.splice(i,1);
                   //从新存入LS
                   localStorage.setItem("data",JSON.stringify(stuData))
                   return;
               }
           })
       }
        $(this).closest("tr").remove();
    })
    $(document).on("click",".edit",function () {
        let trId =$(this).closest("tr").attr("stuId");
        if (stuData) {
            stuData.forEach(item=>{
                if (item.id == trId){
                    location.href="test2Up.html?"+item.id;
                }
            })
        }
    })
</script>
</body>
</html>