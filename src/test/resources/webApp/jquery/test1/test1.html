<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
</head>
<script src="../lib/jQuery-2.2.0.min.js"></script>
<body>
    <div class="box">
    </div>
</body>
<script>

    $(function () {

    })
    eventObject();
    function eventObject() {
        let isMove = false;
        let tempX,tempY;
        $(".box").on("mousedown",e=>{
            isMove=true;
            tempX = e.offsetX;
            tempY = e.offsetY;

        })
        $(".box").on("mousemove",e=>{
            if (isMove){
                let left = e.pageX-tempX;
                let top = e.pageY - tempY;
                $(".box").css({left,top})
            }
        })
        $(".box").on("mouseup",e=>{
            isMove = false;
        })
    }
</script>
<style>
    .box{
        width: 80px;height: 80px;
        position: absolute;
        background: orangered;
    }
</style>
</html>