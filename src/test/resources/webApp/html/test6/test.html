<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>打地鼠游戏</title>
    <style>
        .game {
            width: 320px;
            height: 320px;
            margin: 0 auto;
            position: relative;
        }
        .hole {
            width: 100px;
            height: 100px;
            border: 1px solid #000;
            float: left;
        }
        .mole {
            width: 100px;
            height: 100px;
            background-color: royalblue;
            border-radius: 50%;
            position: absolute;
        }
        .showClickSum{
            width: 320px;
            height: auto;
            margin: 0 auto;
            position: relative;
            text-align: center;
        }
        .showClickSum :last-child{
            font-size: 22px;
            font-family: bold;
            color: red;
        }

    </style>
</head>
<body>
<div class="game">
    <div class="hole"></div>
    <div class="hole"></div>
    <div class="hole"></div>
    <div class="hole"></div>
    <div class="hole"></div>
    <div class="hole"></div>
    <div class="hole"></div>
    <div class="hole"></div>
    <div class="hole"></div>
    <div class="mole"></div>
</div>
<div class="showClickSum">
    <span>打中次数</span>
    <span>0</span>
</div>
<script>
    let mole = document.querySelector('.mole');
    let holes = document.querySelectorAll('.hole');
    let lastSpan = document.querySelector('.showClickSum').lastElementChild;
    let sum = 0;//打中次数

    mole.addEventListener('click', function() {
        lastSpan.textContent = ++sum;
        this.style.display = 'none';
    });

    setInterval(function() {
        //随机下标
        let index = Math.floor(Math.random() * 9);
        //标签高亮
        let hole = holes[index];
        let holeX = hole.offsetLeft;
        let holeY = hole.offsetTop;

        mole.style.left = holeX + 'px';
        mole.style.top = holeY + 'px';
        mole.style.display = 'block';
    }, 1000);
    

</script>
</body>
</html>
