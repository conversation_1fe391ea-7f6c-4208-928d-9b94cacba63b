<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>hh</title>
    <!-- 新 Bootstrap 核心 CSS 文件 -->
    <link href="https://cdn.staticfile.org/twitter-bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet">

    <!-- jQuery文件。务必在bootstrap.min.js 之前引入 -->
    <script src="https://cdn.staticfile.org/jquery/2.1.1/jquery.min.js"></script>

    <!-- 最新的 Bootstrap 核心 JavaScript 文件 -->
    <script src="https://cdn.staticfile.org/twitter-bootstrap/3.3.7/js/bootstrap.min.js"></script>

写入
<style>
    body{
        margin: 0;
    }
   /* #tableMain{
        background: #66afe9;
        margin: auto;
    }
    #tableMain tr td{
        border: pink solid 1px;
    }

    #tableMain tr td:first-child{
        width: 80px;
        text-align-last: justify;
    }*/
    #modal_form  label{
        text-align-last: justify;
    }

    header , footer{
        width: 100%;text-align: center
    }
</style>
<body>
<header >
    <div class="row">
        <div class="col-md-12" >
            <nav class="navbar  navbar-inverse">
                <div class="container-fluid">
                    <!-- Brand and toggle get grouped for better mobile display -->
                    <div class="navbar-header">
                        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                            <span class="sr-only">Toggle navigation</span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </button>
                        <a class="navbar-brand"  href="#">Ikun Home</a>
                    </div>

                    <!-- Collect the nav links, forms, and other content for toggling -->
                    <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
                        <ul class="nav navbar-nav">
                            <li role="presentation" class="active" ><a href="#">列表</a></li>
                            <li role="presentation" >
                                <a class="nav-link" href="#" data-toggle="modal" data-target="#exampleModal" data-whatever="@mdo">添加信息</a>
                            </li>
                            <li role="presentation">
                                <a class="nav-link" href="javascript:window.location.href =kuniMG">查看ikun专属壁纸</a>
                            </li>
                        </ul>

                    </div><!-- /.navbar-collapse -->
                </div><!-- /.container-fluid -->
            </nav>
            </ul>
        </div>
    </div>
    <div class="row">
        <div class="col-md-3">
            <div>
         <!--       <iframe frameborder="no" border="0" marginwidth="0" marginheight="0" width=330 height=86
                        src="http://music.163.com/outchain/player?type=2&id=1919482168&auto=1&height=66"
                         autoplay allowfullscreen="true"></iframe>-->
                <button id="myBtn">我的  11 </button>
                <audio id="bgmusic"  muted controls="true"loop="loop" preload="auto" autoplay>
                    <source src="test.mp3" >
                   </audio>
            </div>
        </div>
        <div class="col-md-9">
            <h1 style="margin-right:  35%">Welcome to ikunHome</h1>
        </div>
    </div>

    <div >
       <img  width="200px" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F0ce2a2b3-ed7b-204e-27fb-e6f64b4bfc3f%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1684324395&t=804c4776be6ec8d3f9636ab3a09657dd">
       <img  width="200px" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F0ce2a2b3-ed7b-204e-27fb-e6f64b4bfc3f%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1684324395&t=804c4776be6ec8d3f9636ab3a09657dd">
       <img  width="200px" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F0ce2a2b3-ed7b-204e-27fb-e6f64b4bfc3f%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1684324395&t=804c4776be6ec8d3f9636ab3a09657dd">
       <img  width="200px" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F0ce2a2b3-ed7b-204e-27fb-e6f64b4bfc3f%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1684324395&t=804c4776be6ec8d3f9636ab3a09657dd">
       <img  width="200px" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F0ce2a2b3-ed7b-204e-27fb-e6f64b4bfc3f%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1684324395&t=804c4776be6ec8d3f9636ab3a09657dd">
       <img  width="200px" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F0ce2a2b3-ed7b-204e-27fb-e6f64b4bfc3f%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1684324395&t=804c4776be6ec8d3f9636ab3a09657dd">
       <img  width="200px" src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fci.xiaohongshu.com%2F0ce2a2b3-ed7b-204e-27fb-e6f64b4bfc3f%3FimageView2%2F2%2Fw%2F1080%2Fformat%2Fjpg&refer=http%3A%2F%2Fci.xiaohongshu.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1684324395&t=804c4776be6ec8d3f9636ab3a09657dd">
   </div>
</header>

<div class="modal fade" id="exampleModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">添加商品订单</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!--<form>
                    <div class="form-group">
                        <label for="recipient-name" class="col-form-label">Recipient:</label>
                        <input type="text" class="form-control" id="recipient-name">
                    </div>
                </form>-->
                <form id="modal_form" class="form-inline">
                    <div class="row">
                        <div class="form-group col-md-6">
                            <label for="name" style="width: 56px">姓名</label>
                            <input type="text"  class="form-control" name="name" id="name" >
                        </div>
                        <div class="form-group col-md-6">
                            <label for="tel">联系人</label>
                            <input type="tel" class="form-control" name="tel" id="tel">
                        </div>
                        <div class="form-group col-md-6">
                            <label for="productName">产品名称</label>
                            <input type="text" name="productName" class="form-control" id="productName">
                        </div>
                        <div class="form-group col-md-12">
                            <label >付款方式</label>
                            <label class="radio-inline">
                                <input type="radio" name="payWay" value ="微信" checked>微信
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="payWay" value="支付宝">支付宝
                            </label>
                            <label class="radio-inline">
                                <input type="radio" name="payWay" value="货到付款">货到付款
                            </label>
                           </div>
                        <div class="form-group col-md-12">
                            <label >关注产品</label>
                            <label class="checkbox-inline">
                                <input type="checkbox" name="product" value="家具">家具
                            </label>
                            <label class="checkbox-inline">
                                <input type="checkbox" name="product" value="家电">家电
                            </label>
                            <label class="checkbox-inline">
                                <input type="checkbox" name="product" value="服装">服装
                            </label>
                            <label class="checkbox-inline">
                                <input type="checkbox" name="product" value="化妆品">化妆品
                            </label>
                             </div>
                        <div class="form-group col-md-12">
                            <label style="width: 55px">地址</label>
                            <select style="width: 120px;text-align: center" id="address1" name="address1" class="form-control form-control-placeholder">
                                <option  value="" >请选择</option>
                            </select>
                            <select style="width: 120px;text-align: center;" id="address2" name="address2" class="form-control form-control-placeholder">
                                <option value="" >请选择省级</option>
                            </select>
                            <input class="form-control" type="text" name="address3" >
                        </div>
                        <div class="form-group col-md-12">
                            <label for="remark" style="width: 55px">备注</label>
                            <textarea id="remark" style="width:450px;height: 80px" name="remark"></textarea>
                        </div>
                    </div>
                </form>
               <!-- <form  class="form-group" name="myForm" >
                    <table id="tableMain">
                       &lt;!&ndash; <tr>
                            <td colspan="4" style="height: 20px;">
                                <h4 style="text-align-last: center;text-align: center">商品订单</h4>
                            </td>
                        </tr>&ndash;&gt;
                        <tr>
                            <td >
                                姓名
                            </td>
                            <td>
                                <input name="name">
                            </td>
                            <td>
                                联系人
                            </td>
                            <td>
                                <input name="tel">
                            </td>
                        </tr>
                        <tr>
                            <td>
                                产品名称
                            </td>
                            <td colspan="3">
                                <input name="productName">
                            </td>

                        </tr>
                        <tr>
                            <td>
                                付款方式
                            </td>
                            <td colspan="3">
                                <input type="radio" name="payWay" value ="vx">微信
                                <input type="radio" name="payWay" value="zfb">支付宝
                                <input type="radio" name="payWay" value="collect">货到付款
                            </td>
                        </tr>
                        <tr>
                            <td>
                                关注产品
                            </td>
                            <td colspan="3">
                                <input type="checkbox" name="product" value="jj">家具
                                <input type="checkbox" name="product" value="jd">家电
                                <input type="checkbox" name="product" value="fz">服装
                                <input type="checkbox" name="product" value="hzp">化妆品
                            </td>
                        </tr>
                        <tr>
                            <td>
                                地址
                            </td>
                            <td colspan="3">
                                <select name="address1">
                                    <option value="广东" >广东</option>
                                </select>
                                <select name="address2">
                                    <option value="广州" >广州</option>
                                </select>
                                <input name="address3" >
                            </td>
                        </tr>
                        <tr>
                            <td>
                                备注
                            </td>
                            <td colspan="3">
                                <textarea  style="width:450px;height: 80px" name="remark"></textarea>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="4" style="text-align-last: center">
                                <input type="button" onclick="mySubmit()" value="提交">
                                <input type="reset" value="重置">
                            </td>
                        </tr>
                    </table>

                </form>-->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" id="modal_Reset" class="btn btn-secondary">重置</button>
                <button type="button" id="modal_Submit" class="btn btn-primary">提交</button>
            </div>
        </div>
    </div>
</div>

<div class="row" style="text-align: center">
    <div class="col-md-3">
        <img  class="img-responsive" alt="Responsive image" src="1.gif">
    </div>
    <div class="col-md-6">
        <table id="tableMain2" class="table table-hover table-bordered">
          <thead>
              <tr  class="info">
                  <td>姓名</td> <td>联系人</td> <td>产品名称</td>
                  <td>付款方式</td> <td>关注产品</td> <td>地址</td>
                  <td>备注</td>
              </tr>
          </thead>
            <tbody>

            </tbody>
        </table>
    </div>
    <div class="col-md-3">
        <img class="img-responsive" alt="Responsive image" src="2.gif">
    </div>
</div>

<footer>
    <h1>this is footer</h1>
    <div id="footerDiv">
        <img  width="200px">
        <img  width="200px">
        <img  width="200px">
        <img  width="200px">
        <img  width="200px">
        <img  width="200px">
        <img  width="200px">
    </div>
    <audio src="https://www.bilibili.com/video/BV1jG4y1Y79S?t=5.7"></audio>
</footer>
</body>
<script>
    $("#myBtn").click(function () {
       $("#bgmusic").trigger('play');
    })

    let AllData = [
        {
            name:'张三',
            tel:'1232412412',
            productName:'冰箱',
            payWay:'微信',
            product:'家电',
            address:'广西 南宁 江安',
            remark:'hhhhhh'
    },
        {
            name:'张八',
            tel:'1241211',
            productName:'电脑',
            payWay:'微信',
            product:'家电',
            address:'广西 南宁 兴宁',
            remark:'hhhhhh'
        }
    ];
    const AllAddress = [
        {
            provincial:'广西省',
            level:["南宁市","柳州市","桂林市","河池市","百色市"]
        },
        {
            provincial:'广东省',
            level:["深圳市","广州市","东莞市","惠州市","梅州市"]
        }
    ];
    $(function () {
        $("#footerDiv img").attr("src",kuniMG);
        showData();
        initAddress();
    })
    const kuniMG = "https://p6.itc.cn/images01/20220623/3820703255234c36bd4b9cdbf1ed6e99.jpeg";
    const kunMusic = "https://music.163.com/#/song?id=1389094010&market=baiduqk";
    function initAddress(){
        let html = "";
        AllAddress.forEach(item=>{
            let provincial = item.provincial;
            html += '<option class="provincial" value="'+provincial+'">'+provincial+'</option>';
        })
        $("#address1").append(html);
    }
    $(document).on("change","#address1",function () {
        $("#address2").html("");
        var selected=$(this).children('option:selected').val();
        let html = "";
        if (selected && selected!=""){
            AllAddress.forEach(item=>{
                let provincial = item.provincial;
                if (provincial==selected){
                    let arr = item.level;
                    arr.forEach(item=>{
                        html += '<option value="'+item+'">'+item+'</option>';
                    })
                    return;
                }
            })
        }else {
            html = '<option value="">请选择省级</option>';

        }
        $("#address2").append(html);
    })
    function showData(){
        $("#tableMain2 tbody").html("");
        AllData.forEach(item =>{
            let html = "<tr style='text-align: center;'>\n" +
                "                <td >"+item.name+"</td> <td>"+item.tel+"</td>\n" +
                "                <td>"+item.productName+"</td> <td>"+item.payWay+"</td> <td>"+item.product+"</td>\n" +
                "                <td>"+item.address+"</td>" +
                "<td>"+item.remark+"</td>"
                "            </tr>"
            $("#tableMain2 tbody").append(html);
        })
    }
    $("#modal_Reset").click(function () {
        $("#modal_form")[0].reset();
    })
    $("#modal_Submit").click(function () {
        let formDataArr = $("#modal_form").serializeArray();
        let data = {};
        let product = "";
        let address = "";
        $.each(formDataArr,function (){
            let name = this.name;
            let value = this.value;

            if (name=='product'){
                product+=value+",";
            }else if ((''+name).indexOf("address")>-1){
                address+=value+" ";
            }else if (data[this.name]) {
                if (!data[this.name].push) {
                    data[this.name] = [data[this.name]];
                }
                data[this.name].push(this.value || '');
            } else {
                data[this.name] = this.value || '';
            }
        })
        data['product'] = product.substring(0,product.length-1);
        data['address'] = address.substring(0,address.length-1);;
        console.log(data)
        AllData.push(data);
        showData();
        $('#exampleModal').modal('hide')
        $("#modal_form")[0].reset();
    })
</script>
</html>