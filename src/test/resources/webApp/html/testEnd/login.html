<!DOCTYPE html>
<head>
    <meta charset="UTF-8">
    <title>学生管理系统登录</title>
    <link rel="stylesheet" href="css/login.css">
</head>
<body>
<div class="login-box">
    <h2>登入</h2>
    <form id="loginForm">
        <div class="user-box">
            <input type="text" name="username" required="">
            <label>用户名</label>
        </div>
        <div class="user-box">
            <input type="password" name="password" required="">
            <label>密码</label>
        </div>
        <input type="submit" name="submit" id="login" value="登录/注册">
    </form>
</div>
</body>
<script>
    //当前地址
    let appHome = window.location.href;
    appHome = appHome.substring(0,appHome.indexOf("?"))
    appHome = appHome.substring(0,appHome.lastIndexOf("/")+1)
    localStorage.setItem("appHome",appHome)

    const inputs = document.querySelectorAll(".user-box input");

    let users = JSON.parse(localStorage.getItem("users"));

    //初始user数据
    indexUser();
    function indexUser() {
        if (!users){
            const indexUsers = [{
                id:1,
                username:'admin',
                password:'123456'
            },{
                id:2,
                username:'123',
                password:'123'
            }
            ];
            localStorage.setItem("users", JSON.stringify(indexUsers))
        }
    }
    function focusFunc() {
        let parent = this.parentNode;
        parent.classList.add("focus");
    }

    function blurFunc() {
        let parent = this.parentNode;
        if (this.value == "") {
            parent.classList.remove("focus");
        }
    }

    inputs.forEach(input => {
        input.addEventListener("focus", focusFunc);
        input.addEventListener("blur", blurFunc);
    });
    //表单 校验 提交
    const form = document.getElementById('loginForm');
    form.addEventListener("submit",function (event) {
        event.preventDefault(); // 阻止默认的表单提交行为
        let username = this.username.value;
        let pwd = this.password.value;
        if (!username ||!pwd){
            alert("请输入账号密码")
        }

        let isNoUser = true;
        users.forEach(function (item) {
            if (item.username == username && item.password==pwd){
                location.href = appHome+"main.html";
                localStorage.setItem("onlineUser",username);
                isNoUser = false;
                return
            }else if (item.username==username){
                isNoUser = false;
                alert("密码错误");
                return;
            }
        })

        if (isNoUser && confirm("是否注册账号:"+username)){
            let newUser =  {id:users.length+1,
                username:username,
                password:pwd,
            };
            users.push(newUser);
            localStorage.setItem("users", JSON.stringify(users))
            location.href = appHome+"main.html";
            localStorage.setItem("onlineUser",username);
        }
    })
</script>
</html>