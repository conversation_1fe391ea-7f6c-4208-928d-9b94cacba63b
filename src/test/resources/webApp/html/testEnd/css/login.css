* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: #f2f2f2;
    font-family: Arial, sans-serif;
}

.login-box {
    width: 400px;
    background: #fff;
    margin: 100px auto;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.3);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.login-box h2 {
    margin-bottom: 30px;
    color: #333;
    font-weight: normal;
}

.user-box {
    position: relative;
}

.user-box input {
    width: 100%;
    padding: 10px 0;
    font-size: 16px;
    color: #333;
    margin-bottom: 30px;
    border: none;
    border-bottom: 2px solid #ccc;
    outline: none;
    background: transparent;
}

.user-box label {
    position: absolute;
    top: 0;
    left: 0;
    padding: 10px 0;
    font-size: 16px;
    color: #666;
    pointer-events: none;
    transition: 0.5s;
}

.user-box input:focus ~ label,
.user-box input:valid ~ label {
    top: -20px;
    left: 0;
    color: #333;
    font-size: 12px;
}

.login-box input[type="submit"] {
    background: #333;
    color: #fff;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: 0.3s;
}

.login-box input[type="submit"]:hover {
    background: #2aabd2;
    color: #333;
}

.login-box:before {
    content: '';
    position: absolute;
    top: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    background: #333;
    border-radius: 50%;
    transition: 0.3s;
}

.login-box:after {
    content: '';
    position: absolute;
    bottom: -50px;
    right: -50px;
    width: 200px;
    height: 200px;
    background: #333;
    border-radius: 50%;
    transition: 0.3s;
}

.login-box:hover:before {
    top: -100px;
    left: -100px;
}

.login-box:hover:after {
    bottom: -100px;
    right: -100px;
}