<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>添加学生</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        form {
            width: 300px;
            margin: 20px;
        }

        h1 {
            text-align: center;
        }

        label {
            display: block;
            margin-bottom: 5px;
        }

        input[type="text"], input[type="number"], select {
            width: 100%;
            padding: 5px;
            margin-bottom: 10px;
        }

        input[type="submit"] {
            width: 100%;
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }

        input[type="submit"]:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
<h1>添加学生</h1>
<form id="addStudentForm">
    <label for="idInput">编号:</label>
    <input type="text" id="idInput" name="id" required>

    <label for="nameInput">姓名:</label>
    <input type="text" id="nameInput" name="name" required>

    <label for="ageInput">年龄:</label>
    <input type="number" id="ageInput" name="age" required>

    <label for="genderSelect">性别:</label>
    <select id="genderSelect" name="gender" required>
        <option value="">请选择性别</option>
        <option value="男">男</option>
        <option value="女">女</option>
    </select>

    <input type="submit" value="添加学生">
</form>

<script>
    index()
    // 获取添加学生表单元素
    const form = document.getElementById('addStudentForm');
    function index(){
        let idInput = document.querySelector("#idInput");
        let arr = JSON.parse(localStorage.getItem("data"));
        const maxId = arr.reduce(function (data1,data2) {
           if (data1.id>data2.id){
               return data1.id
           }else {
               return data2.id
           }
        });
        idInput.value = parseInt(maxId)+1;
    }

    // 监听表单提交事件
    form.addEventListener('submit', function (event) {
        event.preventDefault(); // 阻止默认的表单提交行为

        // 获取表单输入的学生数据
        let id = document.getElementById('idInput').value;
        let name = document.getElementById('nameInput').value;
        let age = document.getElementById('ageInput').value;
        let gender = document.getElementById('genderSelect').value;

        // 创建新的学生对象
        let newStudent = {
            id: id,
            name: name,
            age: age,
            gender: gender
        };
        let LsData = localStorage.getItem("data");
        let stuData;
        if (LsData){
            stuData = JSON.parse(LsData)
        }else {
            stuData = [];
        }
        let isClash = false;
        stuData.forEach(function (item) {
            if (item.id==id){
                isClash = true;
                return;
            }
        })
        if (isClash){
            alert("编号冲突");
        }else {
            // 将新的学生对象添加到数据数组中
            stuData.push(newStudent);
            localStorage.setItem("data",JSON.stringify(stuData))
            console.log(stuData)
            // 清空表单数据
            form.reset();
            alert("学生添加成功！");
            location.href = localStorage.getItem("appHome")+"main.html";
        }
    });


</script>
</body>
</html>
