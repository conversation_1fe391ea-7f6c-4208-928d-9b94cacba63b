
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>学生管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            text-align: center;
        }

        table th, table td {
            border: 1px solid #ddd;
            padding: 8px;
        }

        table th {
            background-color: #f2f2f2;
        }

        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }

        button:hover {
            background-color: #45a049;
        }
        footer {
            width:99%;
            height:50px;
            position:absolute;
            top:100%;
            margin-top:-50px;
        }
    </style>
</head>
<body>
<div style="display: flex;width: 100%;height: 5%">
    <h1>学生管理系统</h1>
    <div  style="flex:1;justify-content: flex-end; display: flex;">
        <h2 id="usernameShow">欢迎 admin</h2>
        <button id="exit" style="height: 50%;margin-top: 1%">退出</button>
    </div>
</div>
<div >
    <button id="addBut" style="float: right" >添加学生</button>
    <table id="studentTable">
        <tr>
            <th>编号</th>
            <th>姓名</th>
            <th>年龄</th>
            <th>性别</th>
            <th>操作</th>
        </tr>
        <tbody id="tableBody">

        </tbody>
    </table>
</div>
<footer>
    <div style="float: right;">
        <span>学生管理系统@</span>
    </div>
</footer>
<script>
    checkLogin()
    index();
    const exitBut = document.getElementById('exit');
    exitBut.addEventListener("click",exitToLogin)

    const addBut = document.getElementById('addBut');
    addBut.addEventListener("click",toAddStu)

    //确认是否登入过
    function checkLogin(){
        let onlineUserName= localStorage.getItem("onlineUser");
        if (!onlineUserName){
            alert("请先登入");
            exitToLogin();
            return;
        }
        let h2= document.querySelector("#usernameShow");
        h2.textContent = "欢迎: "+onlineUserName;
    }


    function exitToLogin() {
        localStorage.setItem("onlineUser",'');
        location.href = localStorage.getItem("appHome")+"login.html";
    }
    function toAddStu() {
        location.href = localStorage.getItem("appHome")+"addStu.html";
    }

    //初始化数据
    function index() {
        let LsData = localStorage.getItem("data");
        stuData = JSON.parse(LsData);
        if (stuData){
            showData(stuData);
        }else {
            ajaxData();
        }
    }
    // 使用AJAX请求获取学生数据并动态添加到表格中
    function ajaxData() {
        let xhr = new XMLHttpRequest();
        xhr.open('GET', 'json/students.json', true);
        xhr.onload = function() {
            if (xhr.status === 200) {
                let jsonData = JSON.parse(xhr.responseText);
                let students = jsonData.data;
                localStorage.setItem("data",JSON.stringify(students));
                showData(students)
            }
        };
        xhr.send();
    }


    //显示数据
    function showData(students) {
        const tableBody = document.getElementById('tableBody');
        //清空
        tableBody.textContent = '';
        //排序
        students.sort((a, b) => a.id - b.id);
        //添加
        students.forEach(function(student) {
            let row = document.createElement('tr');
            let idCell = document.createElement('td');
            idCell.textContent = student.id;
            let nameCell = document.createElement('td');
            nameCell.textContent = student.name;
            let ageCell = document.createElement('td');
            ageCell.textContent = student.age;
            let genderCell = document.createElement('td');
            genderCell.textContent = student.gender;
            let operation = document.createElement('td');
            let delBut = document.createElement('button');
            delBut.textContent = "删除";
            delBut.setAttribute("id",student.id);
            delBut.addEventListener('click', function () {
                // 在这里编写点击事件的处理逻辑
                let id =  this.getAttribute("id")
                if(confirm("确定要删除吗？")){
                    let LsData = localStorage.getItem("data");
                    stuData = JSON.parse(LsData)
                    stuData.forEach(function (item,index) {
                        if (item.id==id){
                            stuData.splice(index, 1);
                            showData(stuData)
                            localStorage.setItem("data",JSON.stringify(stuData))
                            return ;
                        }
                    })
                }
            });
            operation.appendChild(delBut);

            row.appendChild(idCell);
            row.appendChild(nameCell);
            row.appendChild(ageCell);
            row.appendChild(genderCell);
            row.appendChild(operation);
            tableBody.appendChild(row);
        });
    }

</script>
</body>
</html>
