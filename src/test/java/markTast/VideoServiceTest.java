package markTast;

import boot.MyBootApplication;
import boot.test.io.service.VideoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

import static org.mockito.Mockito.*;
@SpringBootTest(classes = MyBootApplication.class)
class VideoServiceTest {

    @Autowired
    private VideoService videoService;



    @Test
    void testUploadChunk_success() throws Exception {
        String uploadId = "abc123";
        int chunkNumber = 1;
        int totalChunks = 2;

        MockMultipartFile chunkFile = new MockMultipartFile(
                "file",
                "chunk.bin",
                "application/octet-stream",
                "This is a test chunk".getBytes()
        );

        videoService.uploadChunk(chunkFile, uploadId, chunkNumber, totalChunks);

        // 验证是否成功保存分片文件

        // 如果是最后一个分片，会调用 uploadToMinio
        chunkNumber = 2;
        videoService.uploadChunk(chunkFile, uploadId, chunkNumber, totalChunks);

    }

    @Test
    void testLocalFileChunkUpload() throws Exception {
        Path videoPath = Paths.get("C:/Users/<USER>/Desktop/test.mp4");
        int chunkSize = 5 * 1024 * 1024;
        String uploadId = UUID.randomUUID().toString();
        int totalChunks = 0;

        try (InputStream inputStream = Files.newInputStream(videoPath)) {
            byte[] buffer = new byte[chunkSize];
            int bytesRead;
            int chunkNumber = 1;

            while ((bytesRead = inputStream.read(buffer)) > 0) {
                byte[] chunk = new byte[bytesRead];
                System.arraycopy(buffer, 0, chunk, 0, bytesRead);

                MultipartFile mockFile = new MockMultipartFile(
                        "file",
                        "test.mp4",
                        "video/mp4",
                        chunk
                );

                videoService.uploadChunk(mockFile, uploadId, chunkNumber++, totalChunks);
            }
            totalChunks = chunkNumber - 1;
            // 再次发送最后一个分片以触发合并
            MultipartFile lastChunk = new MockMultipartFile(
                    "file", "test.mp4", "video/mp4", new byte[0]);
            videoService.uploadChunk(lastChunk, uploadId, totalChunks, totalChunks);
        }
    }

}
