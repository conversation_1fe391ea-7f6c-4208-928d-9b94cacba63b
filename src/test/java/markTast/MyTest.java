package markTast;

import lombok.Data;
import org.junit.Test;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;

/**
 * @Description：
 * @Author：hh
 * @Date：2024/4/27 下午5:56
 **/
public class MyTest {



    @Test
    public void test(){
        ArrayList<Code> list = new ArrayList<>();
        LocalDateTime date = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        int year = date.getYear();
        boolean isSix =true;
        for (int i = 1, j = 1; i <= 49; i++,j++) {
            Code code = new Code();
            String codeNum = String.format("%02d", i);
            String sx = getSx(year);
            code.setCode(codeNum);
            code.setSx(sx);
            String color = "";
//            || codeNum.substring(0,1).equals(codeNum.substring(1,2))
            if (isSix ){
                if (j<=2)
                    color = "红色";
                else if (j<=4)
                    color = "蓝色";
                else
                    color = "绿色";
                if (j==6){
                    isSix =false;
                    j=0;
                }
            }else {
                if (j==1 ||(j==2 && i<=30)){
                    color = "红色";
                }else if (j==5 ||(j==4 && i>=21)){
                    color = "绿色";
                }else{
                    color = "蓝色";
                }
                if (j==5){
                    isSix =true;
                    j=0;
                }
            }
            code.setColor(color);
            list.add(code);
        }
        for (Code code : list) {
            System.out.println(code);
        }
    }
    public String getSx(int year){
        String[] sxArr = new String[]{"猴","鸡","狗","猪","鼠","牛","虎","兔","龙","蛇","马","羊"};
        return sxArr[year%12];
    }
    @Data
    class Code{
        private String code;
        private String sx;
        private String color;
    }
}
