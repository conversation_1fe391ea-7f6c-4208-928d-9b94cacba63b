package boot.test.io.service;

import boot.MyBootApplication;
import boot.test.io.mapper.VideoMapper;
import boot.test.io.model.Video;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = MyBootApplication.class)
public class VideoServiceTest2 {

    @Autowired
    private VideoService videoService;

    @Autowired
    private VideoMapper videoMapper;

    @Autowired
    private MinioClient minioClient;

    @Value("${upload.tempDir}")
    private String tempDir;

    @BeforeEach
    void setUp() throws Exception {
        // 创建临时目录
        Path tempPath = Paths.get(tempDir);
        if (Files.notExists(tempPath)) {
            Files.createDirectories(tempPath);
        }
    }


    @Test
    void testUploadChunk() throws Exception {
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.getBytes()).thenReturn("test-content".getBytes());
        when(mockFile.isEmpty()).thenReturn(false); // 确保不是空文件

        String fileName = UUID.randomUUID().toString();
        int totalChunks = 3;
        int currentChunk = 1;

        videoService.uploadChunk(mockFile, fileName, totalChunks, currentChunk);

        // 验证数据库插入操作是否被调用
        verify(videoMapper, times(1)).insert(any(Video.class));

        // 验证分片文件是否已正确写入磁盘
        Path chunkFilePath = Paths.get(tempDir, fileName + "_" + currentChunk);
        assertTrue(Files.exists(chunkFilePath));
        assertEquals("test-content", new String(Files.readAllBytes(chunkFilePath)));
    }

    @Test
    void testUploadChunk_FileIsEmpty() throws Exception {
        MultipartFile mockFile = mock(MultipartFile.class);
        when(mockFile.isEmpty()).thenReturn(true); // 模拟空文件

        String fileName = UUID.randomUUID().toString();
        int totalChunks = 3;
        int currentChunk = 1;

        assertThrows(IllegalArgumentException.class, () -> {
            videoService.uploadChunk(mockFile, fileName, totalChunks, currentChunk);
        });

        // 验证数据库插入操作没有被调用
        verify(videoMapper, never()).insert(any(Video.class));
    }


    @Test
    void testMergeChunksAndTranscode_Complete() throws Exception {
        String fileName = UUID.randomUUID().toString();
        int totalChunks = 2;

        // 创建两个分片文件
        for (int i = 1; i <= totalChunks; i++) {
            Path chunkPath = Paths.get(tempDir, fileName + "_" + i);
            Files.write(chunkPath, ("chunk-" + i).getBytes());
        }

        // 模拟 FFmpeg 调用成功
        Process mockProcess = mock(Process.class);
        when(mockProcess.waitFor()).thenReturn(0);

        ProcessBuilder mockBuilder = mock(ProcessBuilder.class);
        when(mockBuilder.start()).thenReturn(mockProcess);
        when(mockBuilder.command(anyList())).thenReturn(mockBuilder);
        when(mockBuilder.redirectErrorStream(anyBoolean())).thenReturn(mockBuilder);

        // 替换私有的 ProcessBuilder 实例为模拟对象（通过 PowerMock 或反射）

        videoService.mergeChunksAndTranscode(fileName, totalChunks);

        String transcodedFileName = fileName + "_transcoded.mp4";
        Path transcodedPath = Paths.get(tempDir, transcodedFileName);

        // 断言转码后的文件存在
        assertTrue(Files.exists(transcodedPath));

        // 验证 MinIO 上传是否被调用
        verify(minioClient, times(1)).putObject(any(PutObjectArgs.class));

        // 验证数据库更新状态
        verify(videoMapper, times(1)).updateById(any(Video.class));
    }

    @Test
    void testStreamVideo_RangeRequest() throws Exception {
        String fileName = UUID.randomUUID().toString();
        Path filePath = Paths.get(tempDir, fileName);
        Files.write(filePath, "video-content".getBytes());

        MockHttpServletRequest request = new MockHttpServletRequest();
        request.addHeader("Range", "bytes=0-5");

        MockHttpServletResponse response = new MockHttpServletResponse();

        videoService.streamVideo(response, fileName, request);

        // 验证响应状态和内容
        assertEquals(HttpServletResponse.SC_PARTIAL_CONTENT, response.getStatus());
        assertEquals("bytes 0-5/13", response.getHeader("Content-Range"));
        assertArrayEquals("video-content".substring(0, 6).getBytes(), response.getContentAsByteArray());
    }
}
