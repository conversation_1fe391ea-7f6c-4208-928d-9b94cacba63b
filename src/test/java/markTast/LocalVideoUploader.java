package markTast;

import boot.MyBootApplication;
import boot.test.io.service.VideoService;
import org.checkerframework.checker.units.qual.A;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.*;
import java.util.UUID;

@SpringBootTest(classes = MyBootApplication.class)
public class LocalVideoUploader {

    @Autowired
    private  VideoService videoService;
    @Value("${upload.tempDir}")
    private  String tempDir; // 分片存储路径
    private final int chunkSize = 1024 * 1024 * 5; // 每个分片大小，默认 5MB

     ThreadLocal<Object> threadLocal = new ThreadLocal<>();
    @Test
    public void uploadVideoInChunks() throws IOException {
        String filePath = "E:\\study\\code\\javaBase\\JavaDemo\\video\\test.mp4".replace("\\", "/");
        File file = new File(filePath);
        if (!file.exists()) {
            throw new IllegalArgumentException("文件不存在: " + filePath);
        }
        threadLocal.remove();
        String fileName = UUID.randomUUID().toString(); // 唯一标识整个文件
        int totalChunks = calculateTotalChunks(file.length());

        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[chunkSize];
            int bytesRead;
            int currentChunk = 0;

            while ((bytesRead = fis.read(buffer)) > 0) {
                currentChunk++;
                byte[] chunkData = new byte[bytesRead];
                System.arraycopy(buffer, 0, chunkData, 0, bytesRead);

                // 构造 MultipartFile
                MultipartFile chunkFile = new MockMultipartFile(
                        "file",
                        "chunk_" + currentChunk,
                        "application/octet-stream",
                        new ByteArrayInputStream(chunkData)
                );

                // 调用服务层上传分片
                videoService.uploadChunk(chunkFile, fileName, totalChunks, currentChunk);
            }
        }

        System.out.println("✅ 文件分片上传完成，文件标识符: " + fileName);
    }

    private int calculateTotalChunks(long fileSize) {
        return (int) Math.ceil((double) fileSize / chunkSize);
    }
}
