package test;

import org.junit.Test;

import java.io.OutputStreamWriter;
import java.sql.*;
import java.util.Collection;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @Description 位移运算 ++i和 i++
 * @Date 2022/10/28 15:18
 * @Param
 * @return
 **/
public class StrTest {


    @Test
    public void shiftTest(){
        int num= 22 >> 2;//
     
        System.out.println("除法:"+num);
        int num2= 10 << 3;//2的3次方
        System.out.println("乘法:"+num2);
    }

    @Test
    public void addTest(){
       int x = 1; int y =++x;
       System.out.println(x+":"+y);

       int a = 1; a=++a;
        System.out.println(a);
    }


    @Test
    public void test(){
        int count=0;
        for (float y=2.5f;y>-2.0f;y-=0.12f){
            for (float x=-2.3f;x<2.3f;x+=0.041f){
                float a = x*x+y*y-4f;
                if ((a*a*a-x*x*y*y*y)<-0.0f){
                    String str="臭-屁-屁";
                    int num =count%str.length();
                    System.err.print(str.charAt(num));
                    count++;
                }else{
                    System.err.print(" ");
                }
            }
            System.err.println();
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
    @Test
    public void tt(){
        for (int i = 1; i < 10; i++) {
            for (int j = 1; j <= i; j++) {
                System.out.print("  "+j+"*"+i+"="+i*j);
            }
            System.out.println();
        }
    }
}
