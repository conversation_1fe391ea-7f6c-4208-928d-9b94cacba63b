![img.png](img.png)

写法一：前面带#号，后面带文字，分别表示h1-h6,只到h6。超过6个#就不再是标题语法
# 2.1标题使用

    # 一级标题
    ## 二级标题
    ### 三级标题
    #### 四级标题
    ##### 五级标题
    ###### 六级标题

写法二：相当于标签闭合

    # 一级标题 #
    ## 二级标题 ##
    ### 三级标题 ###

# 2.2列表
## 2.2.1 无序列表：三种形式效果相同
    //形式一
    + a
    //形式二
    - b
    //形式三
    * c
##   2.2.2 有序列表
    //有序排列
    1. asd
    2. sdf

注意，数字后面的点只能是英文的点，有序列表的序号是根据第一行列表的数字顺序来的

## 2.2.3 嵌套列表
可以在有序列表中嵌套新的有序列表，也可以在无序列表中嵌套新的无序列表 </br>

列表可以嵌套，使用时在嵌套列表前按 tab 或 空格 来缩进,去控制列表的层数

# 2.3 引用说明区块
对某个部分做的内容做一些说明或者引用某某的话等，可以用这个语法 </br>

引用内容、说明内容。在语句前面加一个 > </br>
//引用说明区块还可以嵌套使用</br>
> 一级引用
```
> 一级引用
>> 二级引用
>>> 三级引用
>>>> 四级引用
>>>>> 五级引用
>>>>>> 六级引用
```
# 2.4 代码块
少量代码使用``将内容包起来，</br>
大量代码使用```将代码块包起来
```
prrintln("hello world")
``` 
# 2.5 链接
```
//行内式
[名称](链接路径)
    
//参数式
[名称]: 链接路径 "参数"
[名称]: 链接路径 (参数)
[名称]: <链接路径> "参数"
```
# 2.6 图片
```
//行内式
![名称](图片路径)
//参数式
![名称]: 图片路径 "参数"
![名称]: 图片路径 (参数)
![名称]: <图片路径> "参数"
```
# 2.7 分隔线
分割线可以由* - _（星号，减号，底线）这3个符号的至少3个符号表示，注意至少要3个，且不需要连续，有空格也可以
```
---
- - - 
***
* * *
_ _ _
```
# 2.8 表格
```
//例子一
|123|234|345|
|:-|:-:|-:|
|abc|bcd|cde|
|abc|bcd|cde|
|abc|bcd|cde|
//例子二
|123|234|345|
|:---|:---:|---:|
|abc|bcd|cde|
|abc|bcd|cde|
|abc|bcd|cde|
//例子三
123|234|345
:-|:-:|-:
abc|bcd|cde
abc|bcd|cde
abc|bcd|cde
```
### 例子一：
|123|234|345|
|:-|:-:|-:|
|abc|bcd|cde|
|abc|bcd|cde|
|abc|bcd|cde|


上述实例效果相同，得出亮点结论： </br>

表格的格式不一定要对的非常起，但是为了良好的变成风格，尽量对齐是最好的 </br>
分割线后面的冒号表示对齐方式，写在左边表示左对齐，右边为右对齐，两边都写表示居中
# 2.9 其他
**hh666666** //加粗
*hh666666** //斜体
__hh666666__ //加粗
_hh666666_ //斜体
~~hh666666~~ //删除线
//   / :可以用来转义 '*,/,-,>'等符号
