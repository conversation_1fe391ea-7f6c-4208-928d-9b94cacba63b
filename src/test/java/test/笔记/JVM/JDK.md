### JDK 8到21的LTS版本主要变化如下：

1. JDK 8：引入了Lambda表达式、函数式接口、方法引用、接口的默认方法和静态方法、Stream API、Optional类、新的日期时间API、重复注解、Base64编码解码、类型注解、类型推断优化、Nashorn JavaScript引擎和移除PermGen。

2. JDK 11：引入了基于嵌套的访问控制、新的String API、全新的HTTP客户端API、局部变量类型推断的增强、Epsilon—低开销垃圾回收器、ZGC：可伸缩低延迟垃圾收集器、废弃Nashorn JavaScript引擎、新增Files API、Optional API的增强、飞行记录器、运行单文件源码程序和删除Java EE和corba模块。

3. JDK 17：引入了增强型伪随机数生成器、新的macOS渲染管线、macOS/AArch64端口、移除Applet API、模式匹配的Swith表达式、密封的类和接口、隐藏类、移除实验性的AOT和JIT编译、废弃安全管理器和外部函数与内存 API（第二次孵化）。

4. JDK 21：引入了字符串模板、有序集合、分代ZGC、Record模式、switch模式匹配、未命名模式和变量、虚拟线程（正式特性）、未命名类和main方法、作用域值、向量 API（第六次孵化）和弃用Windows 32位x86端口。

这些变化包括新特性的引入、API的增强、性能的优化、安全性的提升以及一些旧特性的移除。