## 1 JVM结构介绍
### 结构图
![img_2.png](img_2.png)


JVM的内存模型定义了Java虚拟机在运行Java程序时如何使用内存。它的主要组成部分包括：
- 类加载子系统负责从文件系统、网络或其他来源加载类信息，将其转换为Java虚拟机运行时的数据结构，并在内存中生成对应的Class对象。类加载过程主要分为加载（Loading）、链接（Linking）和初始化（Initialization）三个阶段。
- 方法区（Method Area,线程共享）： 存储每个类的结构信息，如运行时常量池（Runtime Constant Pool）、字段和方法数据、
构造函数和特殊方法（如< init >）等(存储<font color='red'>静态变量</font> <font color='red'> 常量</font> <font color='red'>类信息</font>)。
- JVM的执行引擎是一个基于栈的执行引擎，它使用操作数栈来存储中间结果，并使用解释器来执行字节码。
  1. 解释器：将class文件字节码 转换为对应平台上的机器指令，并执行它们。
  2. JIT编译器：将<font color='red'>热点代码</font>转为本地的机器码并缓存起来，从而不再需要编译器编译执行，从而提高后续执行的速度。
  3. 垃圾回收器：负责回收不再使用的内存空间，以避免内存泄漏和内存溢出。

- 堆（Heap,线程共享）： 是JVM管理的最大的一块内存区域，它被所有线程共享。堆是Java垃圾收集器管理的主要区域，主要用于存储对象实例（以及成员变量）和数组。
- 栈（Stacks）： 每个线程在Java虚拟机中都有自己的栈，称为“线程栈”。栈帧存储<font color='red'>局部变量</font>、操作栈、动态链接、方法出口等信息。
每个方法从调用直至执行完毕的过程，就是一个栈帧在栈中入栈到出栈的过程。<br>
- 程序计数器（Program Counter Register）： 是一小块内存空间，它可以看作是当前线程所执行的字节码的行号指示器。
- 本地方法栈（Native Method Stack）： 与操作系统相关，它用于支持Java的Native方法（即C和C++的代码）的执行。

## 2 类加载子系统
- 流程图
![img_1.png](img_1.png)
1. 加载（Loading）
在这一阶段，类加载器负责读取Java类的二进制数据（通常是从.class文件中）并将其转换为java.lang.Class类的一个实例。
每个这样的实例代表了JVM中的一个类或接口。在加载阶段，JVM还会对类进行验证，确保其满足Java语言的规范要求。

2. 链接（Linking）
链接阶段进一步细分为验证（Verification）、准备（Preparation）和解析（Resolution）三个子阶段。
- 验证：<font color='red'> 确保被加载类的正确性，检查其格式、依赖、安全等方面 </font>，以防止运行时错误。要包括四种验证，
文件格式验证，元数据验证，字节码验证，符号引用验证。
- 准备： <font color='red'>为类的静态变量分配内存，并将其初始化为默认值（不是由程序员指定的初始值，比如int类型的默认值是0）</font>。
初始化值这里不包含用final修饰的static，因为final在编译的时候就会分配好了默认值，准备阶段会显示初始化。<br>
注意：这里不会为实例变量分配初始化，类变量会分配在方法区中，而实例变量是会随着对象一起分配到Java堆中
- 解析： <font color='red'> JVM将类、接口、字段和方法的符号引用替换为直接引用</font>，这是一个复杂的过程，涉及到查找这些符号引用所对应类的实体，
相当于将根据'包名.类名'去找到真正的类地址并对其引用。解析动作主要针对类或接口、字段、类方法、接口方法、方法类型等。
对应常量池中的CONSTANT Class info、CONSTANT Fieldref info、CONSTANT Methodref info等
符号引用示例 开头有#符号
![img_3.png](img_3.png)
3. 初始化（Initialization）
在初始化阶段，JVM负责执行<font color='red'>类构造器< clinit > </font>方法。这个方法由编译器自动收集类中所有类变量的赋值动作和静态代码块中的语句合并产生。
通过执行<clinit>方法，<font color='red'>类的静态变量将被初始化</font>为程序员指定的值，静态代码块也将被执行。

- 补充
类构造方法< clinit >() 特点
1. < clinit >()并不是程序员在Java代码中直接编写的方法，它是 Javac编译器 的自动生成物
2. < clinit >()方法是由编译器自动收集类中的所有类变量的赋值动作和静态语句块（static{}块）中的语句合并产生的，编译器收集的顺序是由语句在源文件中出现的顺序决定的。
3. 静态语句块中只能访问到定义在静态语句块之前的变量，定义在它之后的变量，在前面的静态语句块可以赋值，但是不能访问，如下例子
```
    static {
          i = 0;                //  给变量赋值可以正常编译通过
          System.out.print(i);  // 这句编译器会提示“非法向前引用”
      }
      static int i = 1;
``` 



## 3栈帧的组成每个栈帧包含以下几个主要部分： 
1. 局部变量表（Local Variables）： 存储方法参数和方法内部定义的局部变量。
2. 局部变量表的容量以变量槽（Variable Slot）为单位度量，
其中一个槽可以存储一个32位的数据类型（boolean、byte、char、short、int、float、reference、returnAddress），
两个槽可以存储一个64位的数据类型（long和double）。
3. 操作数栈（Operand Stack）： 是一个后进先出（LIFO）栈。JVM用它来存放操作指令的输入参数和输出结果，以及执行方法调用时的参数传递。
4. 动态链接（Dynamic Linking）： 每个栈帧内部都包含一个指向运行时常量池中该栈帧所属方法的引用，以支持方法调用过程中的动态链接。
例如，当一个方法调用另一个方法时，动态链接负责找到被调用方法的具体实现（也就是符号引用转为直接引用，加载方法函数）。
5. 方法返回地址（Return Address）： 当一个方法调用完成后，需要从当前方法返回到调用者方法中继续执行，这时会使用到方法返回地址来恢复调用者的执行点。



## 4 堆内存分配
![img_4.png](img_4.png)

内存分配是指在堆内存中为对象分配内存空间的过程。Java虚拟机使用一个称为“指针”的数据结构来表示对象的内存地址。
内存分配的步骤包括：确定对象的大小、为对象分配内存空间、将对象的字段初始化为默认值或指定值、
将对象引用插入到运行时数据区（如栈帧）中。
- 使用分代回收算法 年轻代和老年代 年轻代包含Eden区、From Survivor区、To Survivor区 又称S0 S1。
- 比例 年轻代1 : 2 老年代。而年轻代 Eden区 8 : 1 S0 : 1 S1。

### 内存分配策略
JVM的内存分配策略包括：
- 对象优先在Eden区分配： 如果对象小于等于Eden区的剩余空间，则直接在Eden区分配内存 对象在Eden区分配成功，则不需要进行垃圾收集。
当Eden区满时，会触发一次Minor GC， 活的对象将被移动到年轻代的两个幸存者区（S0和S1)。
复制算法是通过S0 S1体现的，假如数据在S0时 则GC后 将数据复制到S1中（S1时S1->S0）。
- 动态对象年龄判定： 如果对象在年轻代中已经存活了指定的时间（经过15次GC还存活），则将其移动到老年代。
- 大对象直接进入老年代： 如果对象大于等于Eden区的剩余空间，则直接在老年代分配内存。
- 空间分配担保： 如果老年代中的剩余空间不足以容纳新生代的对象，则需要进行一次Full GC。


## 5 垃圾回收
垃圾回收是一种自动内存管理技术，它用于回收不再使用的对象所占用的内存空间。
垃圾回收器会定期扫描堆内存，找到并回收那些不再使用的对象。垃圾回收可以减少内存泄漏和提高程序的性能。
### 垃圾回收器
垃圾回收器是JVM中的一个组件，它负责回收不再使用的对象所占用的内存空间。常见的垃圾回收器包括：
1. Serial 收集器
   特点： 单线程工作，会暂停所有用户线程来进行垃圾收集。适合单核CPU和小内存场景。
   适用场景： 客户端应用，小型应用。
2. Parallel 收集器（也称为 Throughput 收集器）JDK8默认
   特点： 使用多个线程并行地进行垃圾收集，以提高垃圾收集效率。
   适用场景： 适合多核CPU、对响应时间要求不高的服务器应用。
3. CMS（Concurrent Mark-Sweep）收集器
   特点： 采用并发标记清除算法，尽量减少停顿时间。在标记阶段和清除阶段与应用程序线程同时执行。
   适用场景： 适用于对响应时间要求较高的应用，如Web服务器等。
4. G1（Garbage-First）收集器 JDK9引入
   特点： G1收集器采用分代收集，以及堆内存分割为多个区域（Region）进行垃圾收集。它的目标是在给定时间内，尽量实现高吞吐量和低停顿时间。
   适用场景： 大内存、对响应时间要求较高的应用，如云端服务。
5. ZGC（Z Garbage Collector）
   特点： ZGC是一种低停顿时间的垃圾收集器，采用了柔性内存管理和并发垃圾收集算法，可以在数毫秒的停顿时间内处理几百GB的堆内存。
   适用场景： 需要极低停顿时间和高吞吐量的大型Java应用。
   在Java虚拟机（JVM）中，不同的垃圾回收器（GC）有各自的特点和适用场景。以下是您提到的垃圾回收器的特点：

在Java虚拟机（JVM）中，不同的垃圾回收器（GC）有各自的特点和适用场景。以下是您提到的垃圾回收器的特点：

**Serial GC**:
- 单线程执行，简单高效。
- 适用于单CPU环境，无线程交互开销²²。
- 进行垃圾收集时，会触发"Stop-The-World"，暂停所有业务线程²¹。

**Serial Old GC**:
- Serial GC的老年代版本，使用单线程标记-整理算法。
- 适合单CPU或者内存较小的应用场景。
- Serial 适用场景： 客户端应用，小型应用。
**ParNew GC**:
- Serial GC的多线程版本，主要用于新生代。
- 可以与CMS GC配合，减少停顿时间。

**CMS GC**:
- 目标是减少应用暂停时间，主要用于老年代。
- 采用并发标记清除算法，尽量减少Full GC发生的几率。

**Parallel GC**:
- 多线程执行，追求高吞吐量。
- 在新生代使用标记-复制，在老年代使用标记-压缩算法。
- 适用于多CPU环境，可以在JVM启动时通过参数`-XX:+UseParallelGC`启用。

**Parallel Old GC**:
- Parallel GC的老年代版本，使用多线程标记-整理算法²⁷。
- 适合需要高吞吐量且CPU资源充足的场景。
- 适用场景： 适合多核CPU、对响应时间要求不高的服务器应用。
**G1 GC**:
- 目标是提供一个可预测的停顿时间，适用于大堆内存。
- 将堆内存分为多个区域，采用标记-整理算法，减少内存碎片。

**ZGC**:
- 旨在实现极低延迟，适用于大堆内存³¹。
- 停顿时间不会随着堆或活跃对象大小增加。


  ![img_6.png](img_6.png)

### CMS
![img_7.png](img_7.png)
#### 初始标记：
- STW,暂停所有工作线程
- 然后标记出GC Roots能直接可达的对象
- 一旦标记完，就恢复工作线程继续执行
- 这个阶段比较短
#### 并发标记：
- 从上一个阶段标记出的对象，开始遍历整个老年代，标记出所有的可达对象
- 耗时会比较长
- 但是不需要STW,用户线程与垃圾收集线程一起执行
- 三色标记
#### 重新标记：
  - 上个阶段标记的对象，可能有误差，需要进行修正
  需要STW,但是时间也不是很长
  - 增量更新
####  并发清除：
  - 删除垃圾对象
  - 由于不需要移动对象，这个阶段也可以和用户线程一起执行，不需要STW
#### CMS问题总结
- 如果在并发标记、并发清理过程中，由于用户线程同时在执行，如果有新对象要进入老年代，但是空间又不
    够，那么就会导致"concurrent mode failure",此时就会利用Serial Old:来做一次垃圾收集，就会做一次全局
    的STW。
- 在并发清理过程中，可能产生新的垃圾，这些就是“浮动垃圾”，只能等到下一次GC时来清理。
- 由于采用的时标记-清除，所以会产生内存碎片，可以通过参数-XX:+UseCMSCompactAtFullCollection可以
    让JVM在执行完标记-清除后再做一次整理，也可以通过-XX:CMSFullGCsBeforeCompaction来指定多少次GC
    后来做整理，默认是0，表示每次GC后都整理。
### G1
![img_8.png](img_8.png)
![img_9.png](img_9.png)
#### 初始标记（同CMS的初始标记阶段）：
- STW,暂停所有工作线程 。 然后标记出GC Rootsi能直接可达的对象
- 一旦标记完，就恢复工作线程继续执行
- 这个阶段比较短
#### 并发标记（同CMS的初始标记阶段）：
- 从上一个阶段标记出的对象，开始遍历整个老年代，标记出所有的可达对象
- 耗时会比较长
但是不需要STW,用户线程与垃圾收集线程一起执行
- 三色标记
####  最终标记（同CMS的重新标记）：
- 上个阶段标记的对象，可能有误差，需要进行修正
- 需要STW,但是时间也不是很长
- 原始快照
#### 筛选回收（类似CMS的并发清除阶段）：
- 需要STW,来清除垃圾对象
- 可以通过-XX:MaxGCPauseMillisi来指定GC的STW停顿的时间，所以可能并不会回收掉所有垃圾对象，默
  认200ms
- 采用的复制算法，不会产生碎片（会把某个region.里的垃圾对象复制到另外空闲region区域，比如相邻的）

### 垃圾回收算法
垃圾回收算法包括：
- 标记-清除算法： 它是最基本的垃圾回收算法，分为两个阶段：标记阶段和清除阶段。标记阶段用于标记出不再使用的对象，
  清除阶段用于回收标记的对象所占用的内存空间。
- 复制算法： 它将可用内存按容量划分为大小相等的两块，每次只使用其中的一块。当这一块的内存耗尽时，
  将还存活的对象复制到另外一块内存上，然后将已使用过的内存空间一次性清理掉。这种算法适用于内存较小且对象存活率较低的场景。
- 标记-整理算法： 它与标记-清除算法类似，不同的是它在标记阶段完成后，不是直接清理可回收对象所占用的内存空间，
  而是让所有存活的对象都向一端移动，然后直接清理掉边界以外的内存空间。这种算法适用于内存较大的场景，可以减少内存碎片。
![img_5.png](img_5.png)

## 垃圾回收的性能优化
垃圾回收的性能优化包括：

- 选择合适的垃圾收集器： 根据应用程序的特性和性能需求选择合适的垃圾收集器。例如，
对于需要低延迟和高吞吐量的应用，可以选择ZGC或者G1；对于小型应用或测试环境，可以选择Serial GC。

- 调整堆大小： 通过调整堆大小来平衡垃圾收集和应用程序的性能。如果堆太小，
可能会导致频繁的垃圾收集，增加暂停时间；如果堆太大，可能会增加垃圾收集的时间和内存占用。

- 避免过度使用finalize方法： finalize方法会导致对象的回收时间延长，影响垃圾收集的性能。
尽量避免在应用程序中过度使用finalize方法（当对象被回收时，系统自动调用该对象的finalize方法，子类可以重写该方法，做一些释放资源的操作。）。
- 减少对象的创建和销毁： 减少对象的创建和销毁可以减少垃圾收集的压力。可以通过对象池、缓存和重用对象等技术来减少对象的创建和销毁。
- 使用局部变量： 尽量使用局部变量而不是成员变量，局部变量的生命周期比较短，可以更快地被回收。
- 优化对象的生命周期： 尽量使对象的生命周期与其作用域相匹配，及时释放不再使用的对象，避免内存泄漏。
- 手动触发垃圾收集： 在适当的时机手动触发垃圾收集，例如在系统空闲时触发垃圾收集，可以避免在关键时刻发生长时间的垃圾收集暂停。
- 使用优化的数据结构和算法： 使用高效的数据结构和算法可以减少对象的创建和销毁，从而减少垃圾收集的压力。
- 监控和调优： 使用性能监控工具和分析工具对垃圾收集进行监控和调优，及时发现和解决性能瓶颈。
## 参考资料
1. [Java虚拟机规范](https://docs.oracle.com/javase/specs/jvm/se17/html/jvms-2.html)

## 逃逸分析
逃逸分析是一种JVM的优化技术，它用于确定一个对象是否逃出当前作用域。如果一个对象可能被外部方法所引用，
那么它就可能发生逃逸。 逃逸分析可以优化内存管理和垃圾回收的性能。
如果能够确定一个对象指针不会逃逸出方法的作用域,那么就可以将该对象分配在栈上,而不是堆上。

## 类加载器 
虚拟机自带的加载器
1. 启动类加载器(Bootstap ClassLoader)
- 启动类加载器我们也称之为引导类加载器——Bootstap ClassLoader
- 这个类加载使用C/C++语言实现的，嵌套在JVM内部。
- 它用来加载Java的核心库（JAVA_HOME/jre/lib/rt.jar、resources.jar或sun.boot.class.path路径下的内容），
用于提供JVM自身需要的类
- 并不继承自java.lang.ClassLoader，没有父加载器 ,加载扩展类和应用程序类加载器，并作为他们的父类加载器
- 出于安全考虑，Bootstrap启动类加载器只加载包名为java、javax、sun等开头的类
2. 扩展类加载器(Extension ClassLoader)
- Java语言编写，由sun.misc.Launcher$ExtClassLoader实现
- 派生于ClassLoader类
- 父类加载器为启动类加载器
- 从java.ext.dirs系统属性所指定的目录中加载类库，或从JDK的安装目录的jre/lib/ext子目录（扩展目录）下加载类库。
- 如果用户创建的JAR放在此目录下，也会自动由扩展类加载器加载
3. 应用程序类加载器(AppClassLoader)
- 应用程序类加载器也被称之为系统类加载器——AppClassLoader

- Java语言编写，由sun.misc.LaunchersAppClassLoader实现
- 派生于ClassLoader类
- 父类加载器为扩展类加载器
- 它负责加载环境变量classpath或系统属性java.class.path指定路径下的类库 以及应用程序类和第三方依赖库
- 该类加载是程序中默认的类加载器，一般来说，Java应用的类都是由它来完成加载
- 通过classLoader.getSystemclassLoader()方法可以获取到该类加载器


### 双亲委派模型
#### 双亲委派机制原理
Java虚拟机对class文件采用的是按需加载的方式，也就是说当需要使用该类时才会将它的class文件加载到内存生成class对象。而且加载某个类的class文件时，Java虚拟机采用的是双亲委派模式，即把请求交由父类处理，它是一种任务委派模式

1. 如果一个类加载器收到了类加载请求，它并不会自己先去加载，而是把这个请求委托给父类的加载器去执行；
2. 如果父类加载器还存在其父类加载器，则进一步向上委托，依次递归，请求最终将到达顶层的启动类加载器；
3. 如果父类加载器可以完成类加载任务，就成功返回，倘若父类加载器无法完成此加载任务，子加载器才会尝试自己去加载，这就是双亲委派模式。
4. 父类加载器一层一层往下分配任务，如果子类加载器能加载，则加载此类，如果将加载任务分配至系统类加载器也无法加载此类，则抛出异常
![img_10.png](img_10.png)
#### 双亲委派机制的优势
- 避免类的重复加载
- 保护程序安全，防止核心API被随意篡改 
    - 自定义类：自定义java.lang.String 没有被加载。
    -  自定义类：java.lang.ShkStart（报错：阻止创建 java.lang开头的类）
#### 沙箱安全机制
自定义String类时：在加载自定义String类的时候会率先使用引导类加载器加载，而引导类加载器在加载的过程中会先加载jdk自带的文件（rt.jar包中java.lang.String.class），报错信息说没有main方法，就是因为加载的是rt.jar包中的String类。
这样可以保证对java核心源代码的保护，这就是沙箱安全机制。

### 如何判断两个class对象是否相同？
在JVM中表示两个class对象是否为同一个类存在两个必要条件：

1. 类的完整类名必须一致，包括包名
2. 加载这个类的ClassLoader（指ClassLoader实例对象）必须相同
   换句话说，在JVM中，即使这两个类对象（class对象）来源同一个Class文件，被同一个虚拟机所加载，但只要加载它们的ClassLoader实例对象不同，那么这两个类对象也是不相等的


                            版权声明：本文为博主原创文章，遵循 CC 4.0 BY-SA 版权协议，转载请附上原文出处链接和本声明。

原文链接：https://blog.csdn.net/shisi_ixz/article/details/132871392

这个更详细
https://blog.csdn.net/qq_46093575/article/details/127041456