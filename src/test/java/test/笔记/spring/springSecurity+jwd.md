## 认证过程
![img.png](img.png)
- 发起认证请求，携带用户名密码，请求被UsernamePasswordAuthenticationFilter拦截
- <span style="color:red;">在UsernamePasswordAuthenticationFilter的attemptAuthentication方法将请求的用户名和密码 ，封装为Authentication对象，交给AuthenticationManager进行认证</span>
- 认证成功，将认证信息存储SecurityContextHolder以及时调用RememberMe等，并回调AuthenticationSuccessHandler处理

这是黑马的
![img_1.png](img_1.png)

## AuthenticationManager、ProviderManager、AuthenticationProvider三者关系
从分析可以知道，AuthenticationManager是认证类的核心类，但是实际上在底层实际认证的时候，是不能离开ProviderManager和AuthenticationProvider的。

- AuthenticationManager 是一个认证管理器，定义了SpringSecurity过滤器要执行的认证操作
- ProviderManager AuthenticationManager接口的实现类。SpringSecurity认证时默认使用的就是ProviderManager。
- AuthenticationProvider 就是针对不同身份类型执行的具体身份认证。
- <span style="color:red;">ProviderManager内部维护着一个List<AuthenticationProvider>列表，存放着多个AuthenticationProvider，每个AuthenticationProvider负责一种认证。</span>
ProviderManager是AuthenticationManager的唯一实现，是SpringSecurity默认使用的实现。在默认情况下，AuthenticationManager 就是一个ProviderManager 。


在SpringSecurity中，允许系统同时支持多种不同的认证方式，eg：同时支持用户名/密码认证，RememberMe认证，手机号动态认证等，而不同的认证方式对应了不同的AuthenticationProvider，所以一个完整的认证流程，可能有多个AuthenticationProvider来提供。

多个AuthenticationProvider将组成一个list，这个列表由ProviderManager代理。即在ProviderManager中存在AuthenticationProvider列表，在ProviderManager中遍历列表中的每一个AuthenticationProvider去执行身份认证，最终得到认证结果。

ProviderManager本身也可以再配置一个AuthenticationManager作为parent，这样当ProviderManager认证失败之后，就可以进到parent再次认证。理论上，ProviderManager的parent可以是任意类型的AuthenticationManager，但是通常都是由ProviderManager来扮演parent的角色，也就是ProviderManager是ProviderManager的parent。

ProviderManager本身也可以有多个，多个ProviderManager共用一个parent。有时，一个应用程序有受保护资源的逻辑组（eg：所有符合路径的网络资源，/api/**），每个组可以有自己的专用AuthenticationManager。通常每个组都是一个ProviderManager，他们共用一个父级。然后，父级是一种全局资源，作为所有提供者的后备资源

<span style="color:red;"> 默认情况下AuthenticationProvider是由DaoAuthenticationProvider类来实现认证的，在DaoAuthenticationProvider认证时又通过UserDetailsService（retrieveUser（）方法）完成数据校验</span>

总结：AuthenticationManager是认证管理器，在SpringSecurity中有全局AuthenticationManager，也可以有局部AuthenticationManager。全局的AuthenticationManager用来对全局认证进行处理，局部的AuthenticationManager用来对某些特殊资源认证处理。当然无论是全局认证管理器还是局部认证管理器都是由ProviderManager来实现。每个ProviderManager中都代理一个AuthenticationProvider的列表，列表中每个实现代表一种身份认证方式。认证时底层数据源调用UserDetailsService实现。

这部分内容参考 https://blog.51cto.com/u_16213630/10446043

## 踩坑
    1. 可以不需要自己在controller中实现登录方法，默认是/login的，doFilter后到我们实现的UserDetailsService的类的loadUserByUsername来获取UserDetails类（包含账户密码，权限），如果需要自己实现则重写UsernamePasswordAuthenticationFilter的attemptAuthentication
参考 https://www.cnblogs.com/oeds/p/16317785.html
https://blog.51cto.com/u_16213712/7800659