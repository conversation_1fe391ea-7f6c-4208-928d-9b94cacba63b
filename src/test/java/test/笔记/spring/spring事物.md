# 事物的传播
1. 支持当前事务
   - REQUIRED：默认的事务传播级别，表示如果当前方法已在事务内，该方法就在当前事务中执行，否则，开启一个新的事务并在其上下文中执行。
   - SUPPORTED：当前方法在事务内，则在其上下文中执行该方法，否则，开启一个新的事务。
   - MANDATORY：必须在事务中执行，否则，将抛出异常。
2. 不支持当前事务
   - REQUIRES_NEW：无论当前是否有事务上下文，都会开启一个事务 。如果已经有一个事务在执行 ，则正在执行的事务将被挂起 ，新开启的事务会被执行。 事务之间相互独立，互不干扰。
   - NOT_SUPPORTED：不支持事务，如果当前存在事务上下文，则挂起当前事务，然后以非事务的方式执行。
   - NEVER：不能在事务中执行，如果当前存在事务上下文，则抛出异常。
3. 嵌套事务
   - NESTED：嵌套事务，如果当前已存在一个事务的上下文中，则在嵌套事务中执行，如果抛异常，则回滚嵌套事务，而不影响其他事务的操作。
   
### 图 前三个常用
![img_7.png](img/img_7.png)
![img_8.png](img/img_8.png)
# 使用事务注解@Transactional 为什么会失效？
 @Transactional是基于aop+mysql（需要支持事务的引擎）连接事物实现的 捕获到异常才会回滚

1. 我们在代码中做了ty操作没有将异常往外跑所以事务注解
 对应的aop就没有拦截认为方法是没有异常的则直接自动提交事务。
   - 解决办法：需要在我们的catch手动回滚事务。

       `TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();`
    
2. 使用了this. 调用了原始对象的方法 而不是Aop的代理对象
   - 解决办法：获取aop代理对象

3. @Transactional 注解只有作用到 public 方法上事务才生效，不推荐在接口上使用；
4. 避免同一个类中调用 @Transactional 注解的方法，这样会导致事务失效；
5. 正确的设置 @Transactional 的 rollbackFor 和 propagation 属性，否则事务可能会回滚失败;
6. 被 @Transactional 注解的方法所在的类必须被 Spring 管理，否则不生效；
7. 底层使用的数据库必须支持事务机制，否则不生效；

