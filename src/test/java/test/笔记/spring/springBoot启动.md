# 启动顺序
1. **初始化容器**
    - 设置初始化容器
    - 加载监听器
    - 找到 main 函数入口类

2. **准备环境（prepareContext()）**
    - 创建 `Environment` 对象
    - 加载上下文、环境信息、监听器、工厂对象

# @import相关
作用：导入其他配置类注入到spring IOC中

相关动态实现接口：
1. 当@import的类实现了ImportSelector（将返回类名数组，并动态注入IOC）
2. ImportBeanDefinitionRegistrar（重写registerBeanDefinitions（注册器）持有容器对象 可以将类注册进去，也可以对容器操作）


# 自动装配
自动装配的实现就是为了从spring.factories.文件中获取到对应的bean 对象，并目由spring容器来帮我们进行管理
1. 加载spring.factories文件中的类（LoadSpringFactories方法中找到这个文件并以字符串全路径方式）到configurations缓存(候选，并不代表都要实例)中
以及自定义封装组件或者start中的spring.factories 到configurations缓存中缓存中
   >tip：spring.factories中的类在org.springframework.boot.autoconfigure.EnableAutoConfiguration下面 ，分隔。
    configurations缓存中的类名，可@SpringBootApplication(exclude = {类名}) 排除掉，
     之后跟据META-INF/spring-autoconfigure-metadata.properties中的条件信（满足保留不满足remove）过滤filter一些用不到的配置
   > 
   >过滤条件2 注解方式 存在才加载，多条件都要满足才加载&
   > 
    ![img_5.png](img/img_5.png) 
      
2. 接下来讲由ConfigurationClassPostProcessor 首先会扫描所有标注了 @Configuration 注解的配置类。
这个扫描过程会检测这些配置类中的其他注解，例如 @ComponentScan、@Import 和 @Bean
3. spring先去找到 @ComponentScan -》 @import -》 @AutoConfigurationPackage -》 
@Import(AutoConfigurationPackages.Registrar.class)-》 @Import(AutoConfigurationImportSelector.class)（会有一个getlmports的方法个从主类开始递归解析注解） -》 
获取spring.factories.文件中的类（字符串全路径）-》 @EnableAutoConfiguration
4. 判断哪些是我们需要的 再根据字符串全路径，反射获取对应的类对象
![img_6.png](img/img_6.png)

## 📌补充点 条件注解详解 常见条件注解：
| 注解 | 作用 |
|------|------|
| `@ConditionalOnClass` | 指定类存在时才加载配置 |
| `@ConditionalOnMissingClass` | 指定类不存在时才加载配置 |
| `@ConditionalOnBean` | 容器中存在某个 Bean 时才加载 |
| `@ConditionalOnMissingBean` | 容器中没有某个 Bean 时才加载 |
| `@ConditionalOnProperty` | 某个配置属性存在且值匹配时才加载 |
| `@ConditionalOnWebApplication` | 当前是 Web 应用时才加载 |
| `@ConditionalOnNotWebApplication` | 当前不是 Web 应用时才加载 |
### 示例
```java
@Configuration
@ConditionalOnClass(name = "javax.servlet.http.HttpServlet")
public class MyWebConfiguration {
    @Bean
    public MyWebComponent myWebComponent() {
        return new MyWebComponent();
    }
}

```
## ✅ 补充点 3：Spring Boot 的事件监听机制（Event/Listener）

### 📌 内置事件列表：
| 事件名 | 触发时机 |
|--------|----------|
| `ApplicationStartingEvent` | 应用启动但尚未开始运行时 |
| `ApplicationEnvironmentPreparedEvent` | 环境对象已准备好，但在上下文创建之前 |
| `ApplicationContextInitializedEvent` | 上下文已初始化但尚未刷新 |
| `ApplicationPreparedEvent` | 上下文已加载完成，但尚未刷新 |
| `ContextRefreshedEvent` | 上下文刷新完成后触发 |
| `ApplicationReadyEvent` | 应用已启动并准备好处理请求 |
| `ApplicationFailedEvent` | 启动过程中发生异常 |
示例
```java
@Configuration
@ConditionalOnClass(name = "javax.servlet.http.HttpServlet")
public class MyWebConfiguration {
    @Bean
    public MyWebComponent myWebComponent() {
        return new MyWebComponent();
    }
}

```
# refresh中的13个方法
1. prepareRefresh - 做好准备工作（创建Environment对象，为spring后续的运行提供一些键值信息）
   此方法主要用于准备环境，为Spring容器的启动创建必要的环境配置信息。
    - `EnvironmentAware`：Bean 可以通过该接口获取 `Environment`。
2. obtainFreshBeanFactory - 创建或获取BeanFactory
   该方法负责初始化或获取一个新鲜的BeanFactory实例，为后续的Bean创建和管理提供基础。
    - `BeanFactoryAware`：Bean 可以通过该接口获取 `BeanFactory`。
3. prepareBeanFactory - 准备BeanFactory（为BeanFactory创建各个成员变量，EL表达式解析器、类型转换器、内置的BeanPostProcessor）
   此方法对BeanFactory进行配置和初始化，包括设置EL表达式解析器、类型转换器以及注册内置的BeanPostProcessor。
   - `BeanClassLoaderAware`：通过类加载器感知类路径。
   - <span style="color:red;">ResourceLoaderAware </span> / `ApplicationEventPublisherAware` / `MessageSourceAware`：部分内置处理器可能使用这些接口。
4. postProcessBeanFactory - 子类扩展BeanFactory
   允许子类在BeanFactory初始化后进行自定义处理，以满足特定的需求。

5. invokeBeanFactoryPostProcessors - 后处理器扩展BeanFactory（Bean工厂的后置处理器：ConfigurationClassPostProceessors，解析配置类的注解：@Configuration、@Bean等）
   调用所有注册的BeanFactory后处理器，通常用于处理配置类和Bean定义。
    - 自定义 `BeanFactoryPostProcessor` 实现类可以实现以下接口：
        - `BeanFactoryAware`
        - `EnvironmentAware`
        - `ResourceLoaderAware`
6. registerBeanPostProcessors - 准备Bean后处理器（常见的有三个，一个是解析@Autwired注解，一个是解析@Resource、一个是解析@Aspect，创建代理类的）
   在BeanFactory中注册Bean后处理器，这些处理器在Bean初始化前后执行额外的处理逻辑。
    - `BeanPostProcessor` 实现类（如 `AutowiredAnnotationBeanPostProcessor`）可能会实现：
        - `BeanFactoryAware`
        - `ResourceLoaderAware`
        - <span style="color:red;">ApplicationEventPublisherAware </span>

7. initMessageSource - 为ApplicationContext提供国际化功能
   初始化消息源，以便ApplicationContext支持国际化和本地化。
   - `MessageSourceAware`：某些 Bean 可以通过该接口获取 `MessageSource`。
8. initApplicationEventMulticaster - 为ApplicationContext提供事件发布器
   设置事件多播器，允许ApplicationContext发布事件给注册的监听器。
   - `ApplicationEventPublisherAware`：Bean 可以通过该接口获取事件发布能力。
9. onRefresh - 留给子类扩展
   由子类实现，以在刷新过程中执行特定的逻辑。
    - 子类可根据需要实现各种 `Aware` 接口，如：
        - `ApplicationEventPublisherAware`
        - <span style="color:red;"> MessageSourceAware</span>

10. registerListeners - 为ApplicationContext准备监听器
    注册所有必要的应用事件监听器，以便它们可以响应由ApplicationContext发布的事件。
    - `ApplicationListener` 实现类可实现：
        - `ApplicationEventPublisherAware`
        - `MessageSourceAware`
11. finishBeanFactoryInitialization - 初始化单例Bean,执行Bean后处理器扩展
    完成BeanFactory的初始化过程，包括初始化所有剩余的单例Beans，并应用所有已注册的Bean后处理器。
    - 所有实现了 `Aware` 接口的 Bean 在此阶段被回调，包括但不限于：
        - <span style="color:red;">BeanNameAware </span>
        - <span style="color:red;">ApplicationContextAware </span>
        - <span style="color:red;"> BeanFactoryAware</span>
        - `ResourceLoaderAware`
        - `ApplicationEventPublisherAware`
        - `MessageSourceAware`
        - <span style="color:red;"> EnvironmentAware</span>
        - <span style="color:red;">EmbeddedValueResolverAware </span>

12. finishRefresh - 准备生命周期管理器,发布ContextRefreshed事件
    完成刷新过程，初始化生命周期管理器，并发布ContextRefreshed事件，通知所有监听器上下文已刷新。
    - `LifecycleProcessor` 和监听器相关的 Bean 可能会实现：
        - `ApplicationEventPublisherAware`
        - `MessageSourceAware`


| 序号 | 方法名 | 作用（非常详细） | 简要说明 | Aware 接口作用 |
|------|--------|------------------|----------|----------------|
| 1 | `prepareRefresh()` | 初始化容器运行所需的环境对象（Environment），包括系统属性、JVM 参数、配置文件等，并设置启动时间。此方法还会校验是否设置了必要的占位符（如 `${}`），确保后续 Bean 加载不会出错。这是整个 Spring 容器启动的第一步，为后续流程打下基础。 | 准备上下文刷新所需环境信息 | `EnvironmentAware`：允许 Bean 获取当前应用的环境信息（如配置属性）。 |
| 2 | `obtainFreshBeanFactory()` | 创建或获取一个新的 `BeanFactory` 实例，用于后续 Bean 的加载与管理。该方法会销毁旧的 BeanFactory（如果存在），并创建新的实例，同时加载 BeanDefinition（从 XML 或注解中解析而来）。这一步是 Spring 容器初始化的核心步骤之一，决定了后续 Bean 的创建方式和生命周期。 | 创建或获取 BeanFactory | `BeanFactoryAware`：允许 Bean 获取当前使用的 BeanFactory，用于手动操作容器中的 Bean。 |
| 3 | `prepareBeanFactory(ConfigurableListableBeanFactory)` | 配置 BeanFactory 的基本属性，如类加载器、EL 表达式解析器、类型转换器、注册内置的 `BeanPostProcessor` 等。这一步为后续的 Bean 加载和初始化打下基础，也决定了容器的一些默认行为，例如资源加载方式、事件广播机制、国际化支持等。 | 设置 BeanFactory 基础组件 | `BeanClassLoaderAware`：获取类加载器<br>`ResourceLoaderAware`：加载资源文件（如 XML、Properties）<br>`ApplicationEventPublisherAware`：发布自定义事件<br>`MessageSourceAware`：支持国际化消息处理 |
| 4 | `postProcessBeanFactory(ConfigurableListableBeanFactory)` | 子类可以重写此方法，在 BeanFactory 初始化后进行扩展和定制化处理。例如 Web 容器中可以添加特定于 Web 的 Scope 支持，或者注册额外的处理器。这个方法提供了一个灵活的扩展点，让不同类型的 ApplicationContext 可以根据需要定制 BeanFactory。 | 子类可扩展点 | —— | —— |
| 5 | `invokeBeanFactoryPostProcessors(ConfigurableListableBeanFactory)` | 调用所有注册的 `BeanFactoryPostProcessor` 和 `BeanDefinitionRegistryPostProcessor`，这些处理器可以在 Bean 实例化之前修改 BeanDefinition，甚至动态注册新 Bean。最典型的是 `ConfigurationClassPostProcessor`，它负责解析 `@ComponentScan`、`@Import`、`@PropertySource` 等注解，完成自动装配和配置类的加载。 | 处理配置类并生成 BeanDefinition | `BeanFactoryAware`：访问 BeanFactory<br>`EnvironmentAware`：读取环境变量<br>`ResourceLoaderAware`：加载外部资源 |
| 6 | `registerBeanPostProcessors(ConfigurableListableBeanFactory)` | 注册所有的 `BeanPostProcessor`，这些处理器在 Bean 初始化前后执行逻辑（如注入、代理）。例如 `AutowiredAnnotationBeanPostProcessor` 解析 `@Autowired`、`CommonAnnotationBeanPostProcessor` 解析 `@Resource`、`AspectJAwareAdvisorAutoProxyCreator` 创建 AOP 代理。这些处理器是 Spring 功能强大的关键所在。 | 注册 Bean 后处理器 | `BeanFactoryAware`：访问容器<br>`ResourceLoaderAware`：加载资源<br>`ApplicationEventPublisherAware`：发布事件 |
| 7 | `initMessageSource()` | 初始化 `MessageSource`，用于支持国际化（i18n）功能，例如根据语言环境返回不同语言的消息。默认使用 `DelegatingMessageSource`，支持消息查找链。通过这个机制，Spring 可以根据不同用户的 Locale 返回对应语言的提示信息，常用于多语言系统开发。 | 初始化国际化消息源 | `MessageSourceAware`：允许 Bean 获取 `MessageSource`，实现多语言支持。 |
| 8 | `initApplicationEventMulticaster()` | 初始化 `ApplicationEventMulticaster`，用于发布和广播应用事件。默认使用 `SimpleApplicationEventMulticaster`，支持同步/异步事件传播机制。通过事件机制，Spring 内部模块之间可以松耦合通信，例如监听 `ContextRefreshedEvent` 来执行初始化逻辑。 | 初始化事件发布机制 | `ApplicationEventPublisherAware`：允许 Bean 发布事件，与其他组件通信。 |
| 9 | `onRefresh()` | 留给子类实现特定的刷新逻辑，例如 Web 容器初始化时可以启动内嵌的 Tomcat 或 Jetty。这是一个模板方法，不同的 `ApplicationContext` 实现类可以在这里加入自己的初始化逻辑，比如启动 Web 服务器、初始化线程池等。 | 子类可扩展点 | `ApplicationEventPublisherAware`：发布事件<br>`MessageSourceAware`：获取国际化消息 |
| 10 | `registerListeners()` | 注册所有实现了 `ApplicationListener` 的监听器，监听容器中发布的事件。这些监听器可以响应如 `ContextRefreshedEvent`、`ApplicationReadyEvent` 等。通过监听机制，开发者可以在某些关键节点插入自定义逻辑，如日志记录、权限控制等。 | 注册事件监听器 | `ApplicationEventPublisherAware`：发布事件<br>`MessageSourceAware`：获取国际化消息 |
| 11 | `finishBeanFactoryInitialization(ConfigurableListableBeanFactory)` | 初始化所有单例 Bean，调用各种 `Aware` 接口回调（如 `BeanNameAware`, `ApplicationContextAware`），完成依赖注入和 AOP 代理创建。这是整个容器中最关键的一步，大多数业务 Bean 在这里被创建并初始化，AOP 拦截逻辑也是在此阶段完成。 | 初始化单例 Bean | `BeanNameAware`：获取 Bean 名称<br>`ApplicationContextAware`：获取上下文<br>`BeanFactoryAware`：获取工厂<br>`ResourceLoaderAware`：加载资源<br>`ApplicationEventPublisherAware`：发布事件<br>`MessageSourceAware`：获取国际化消息<br>`EnvironmentAware`：获取环境信息<br>`EmbeddedValueResolverAware`：解析占位符（如 `${}`） |
| 12 | `finishRefresh()` | 刷新完成后，发布 `ContextRefreshedEvent` 事件，通知所有监听器上下文已刷新。此外还会初始化生命周期管理器（LifecycleProcessor），用于管理 SmartLifecycle 类型的组件。此时整个容器已经就绪，可以对外提供服务。 | 发布刷新完成事件 | `ApplicationEventPublisherAware`：允许 Bean 发布事件 |
| 13 | `resetCommonCaches()` | 清除 Java 反射相关缓存（如 `ConcurrentReferenceHashMap`），防止内存泄漏，尤其是在热部署场景下。虽然不是核心流程，但对性能和稳定性有重要影响。 | 清理反射缓存 | —— | —— |

