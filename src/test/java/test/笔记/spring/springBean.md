# Bean 的生命周期
### 简单流程图
![img_3.png](img/img_3.png)
### 详细流程图
![img_4.png](img/img_4.png)

1. 处理BeanDefinition：BeanDefinition的解析，注册，合并
2. Bean实例化（Instantiation）：还没有生成bean，即没有调用构造函数，生成对象
3. Bean属性赋值：查找被@Autowired和@Value标注的方法或属性，注入需要的值
4. Bean初始化（Initialization）：已经生成bean，进行属性赋值
5. Bean销毁：并没有gc
### 重点
搞懂了Spring内置的BeanPostProcessor的功能，基本上就把Spring Bean的生命周期搞懂了。

https://www.cnblogs.com/imok520/p/16411309.html
# 三级缓存 主要解决循环依赖
现在，2.6.0 这个版本已经默认禁止 Bean 之间的循环引用, 则基于上面的代码，会报错。
> // 一级缓存Map 存放完整的Bean（流程跑完的）
> private final Map<String, Object> singletonObjects = new ConcurrentHashMap(256);
> 
> // 二级缓存Map 存放不完整的Bean（只实例化完，还没属性赋值、初始化）
> private final Map<String, Object> earlySingletonObjects = new ConcurrentHashMap(16);
> 
> // 三级缓存Map 存放一个Bean的lambda表达式（也是刚实例化完）
> private final Map<String, ObjectFactory<?>> singletonFactories = new HashMap(16);

## 创建bean 总体流程图 
![img_1.png](img/img_1.png)

单个bean创建总结
![img_2.png](img/img_2.png)
## 三级缓存解决循环依赖 
### 流程图
![img.png](img/img.png)
### 问题
1. 发生依赖找缓存顺序
    > 一级缓存->二级缓存->三级缓存（存的是lambda表达式，包含了 name和原实例）
2. 为什么三级缓存中存的是lambda表达式
    > 1. 当A和B循环依赖时，B判断发生了循环依赖，创建A的代理对象给B。并在二级缓存生成半成品A的Bean，并在三级缓存清除A的lambda表达式（持有）。
    > 2. 在A（注入B） B（注入A C） C（注入A）循环依赖时，C从二级缓存中直接引用A的半成品Bean（A的三级缓存在B依赖注入时被清空了而注入到二级缓存中，
这样设计是为了防止C再创建A的代理对象而产生两个代理对象）。
    > 3. 由B将A提前AOP后放入二级缓存，A检查已经提前发生AOP后直接从二级缓存中获取A。

https://blog.csdn.net/weixin_44102992/article/details/128106055