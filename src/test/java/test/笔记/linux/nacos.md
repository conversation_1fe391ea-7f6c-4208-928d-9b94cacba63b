在 CentOS 上使用 Docker 安装 Nacos 需要以下步骤：

1. **安装 Docker**：
   首先，确保你的 CentOS 系统已经安装了 Docker。如果还没有安装，可以按照以下步骤进行安装：

   ```bash
   # 更新系统包
   sudo yum update -y

   # 安装必要的依赖
   sudo yum install -y yum-utils

   # 添加 Docker 仓库
   sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

   # 安装 Docker
   sudo yum install -y docker-ce docker-ce-cli containerd.io

   # 启动 Docker 服务
   sudo systemctl start docker

   # 设置 Docker 开机自启
   sudo systemctl enable docker
   ```

2. **拉取 Nacos 镜像**：
   使用 Docker 拉取 Nacos 镜像。Nacos 官方提供了 Docker 镜像，你可以直接拉取：

   ```bash
   docker pull nacos/nacos-server:latest
   ```
sudo mkdir -p /etc/docker
sudo tee /etc/docker/daemon.json <<-'EOF'
{
"registry-mirrors": ["https://k2lhp58k.mirror.aliyuncs.com"]
}
EOF
sudo systemctl daemon-reload
sudo systemctl restart docker
/
3. **运行 Nacos 容器**：
   使用以下命令运行 Nacos 容器。这里假设你将 Nacos 配置文件和数据存储目录映射到宿主机，以便于配置和持久化数据：

   ```bash
   docker run -d \
     --name nacos \
     -p 8848:8848 \
     -p 9848:9848 \
     -p 9849:9849 \
     -e MODE=standalone \
     -e SPRING_DATASOURCE_PLATFORM=mysql \
     -e MYSQL_SERVICE_HOST=localhost \
     -e MYSQL_SERVICE_PORT=3306 \
     -e MYSQL_SERVICE_DB_NAME=nacos_config \
     -e MYSQL_SERVICE_USER=root \
     -e MYSQL_SERVICE_PASSWORD=123456 \
     -v /usr/dockerData/nacos/conf:/home/<USER>/conf \
     -v /usr/dockerData/nacos/data:/home/<USER>/data \
     -v /usr/dockerData/nacos/logs:/home/<USER>/logs \
     -d nacos/nacos-server:1.4
   ```
   ```bash
   docker run -d --name nacos  -p 8848:8848  \
   -e MODE=standalone  \
   -e SPRING_DATASOURCE_PLATFORM=mysql  \
   -e MYSQL_SERVICE_HOST=localhost  \
   -e MYSQL_SERVICE_PORT=3306  \
   -e MYSQL_SERVICE_DB_NAME=nacos_config  \
   -e MYSQL_SERVICE_USER=root  \
   -e MYSQL_SERVICE_PASSWORD=123456  \
   -v E:\study\environment\dockerData\Container\nacos\conf:/home/<USER>/conf  \
   -v E:\study\environment\dockerData\Container\nacos/data:/home/<USER>/data  \
   -v E:\study\environment\dockerData\Container\nacos:/home/<USER>/logs  nacos/nacos-server:1.4.6
   ```
```bash
   docker run -d --name nacos  -p 8848:8848 --network my-network -e MODE=standalone -e SPRING_DATASOURCE_PLATFORM=mysql -e MYSQL_SERVICE_HOST=localhost -e MYSQL_SERVICE_PORT=3306 -e MYSQL_SERVICE_DB_NAME=nacos_config -e MYSQL_SERVICE_USER=root -e MYSQL_SERVICE_PASSWORD=123456 -v E:\study\environment\dockerData\Container\nacos\conf:/home/<USER>/conf -v E:\study\environment\dockerData\Container\nacos/data:/home/<USER>/data -v E:\study\environment\dockerData\Container\nacos:/home/<USER>/logs  nacos/nacos-server:v1.4.6
```
  
   请将上述命令中的以下部分替换为你的实际配置：
    - `your-mysql-host`：你的 MySQL 主机地址。
    - `your-mysql-user`：你的 MySQL 用户名。
    - `your-mysql-password`：你的 MySQL 密码。
    - `/path/to/nacos/conf`：Nacos 配置文件在宿主机上的路径。
    - `/path/to/nacos/data`：Nacos 数据存储目录在宿主机上的路径。
    - `/path/to/nacos/logs`：Nacos 日志目录在宿主机上的路径。

4. **访问 Nacos**：
   Nacos 默认运行在 `http://localhost:8848/nacos`。在浏览器中打开这个地址，你应该能够看到 Nacos 的登录页面。默认的用户名和密码都是 `nacos`。

以上步骤完成后，Nacos 应该已经在你的 CentOS 系统上成功安装并运行。你可以通过浏览器访问 Nacos 的管理界面，并进行相应的配置和管理操作。


## 常用配置
```nginx configuration
user nginx;  # 指定Nginx运行时使用的系统用户，一般是nginx或者www-data
worker_processes auto;  # 设置工作进程数量，auto表示自动根据CPU核心数设置

error_log /var/log/nginx/error.log error; # 【修改】日志等级改成error，只记录严重错误
pid /run/nginx.pid;  # 定义存放Nginx主进程PID的文件路径
include /usr/share/nginx/modules/*.conf;  # 引入额外模块的配置文件（动态加载模块用）

events {
    worker_connections 10240; # 【修改】连接数从1024提升到10240
}

http {
    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
                      # 定义名为main的访问日志格式，记录访问的详细信息

    access_log  /var/log/nginx/access.log  main;  # 设置访问日志的存储路径，并指定使用main格式

    sendfile            on;  # 开启sendfile，提高文件传输效率（减少拷贝操作）
    tcp_nopush          on;  # 优化TCP发送性能，配合sendfile一起使用，减少网络延迟
    tcp_nodelay         on;  # 禁用Nagle算法，及时发送小数据包，减少延迟

    keepalive_timeout   15; # 【修改】超时从65秒降低到15秒，释放资源更快
    keepalive_requests  100; # 【新增】增加keepalive最大请求数限制

    types_hash_max_size 4096; # 设置MIME类型哈希表的最大大小，提高类型查询效率

    include             /etc/nginx/mime.types; # 引入MIME类型定义文件，用来识别不同文件扩展名

    default_type        application/octet-stream; # 默认的Content-Type，当无法识别文件类型时使用

    # Gzip压缩设置 【新增】
    gzip on;
    gzip_min_length 1024;
    gzip_comp_level 5;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 上传体积限制 【新增】
    client_max_body_size 10M;

    # 隐藏Nginx版本信息 【新增】
    server_tokens off;

    # 缓冲区优化 【新增】
    client_body_buffer_size 16k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;

    # 限制IP最大连接数 【新增】（防简单DDoS）
    limit_conn_zone $binary_remote_addr zone=addr:10m;

    include /etc/nginx/conf.d/*.conf;  # 引入conf.d目录下所有以.conf结尾的配置文件，通常用于虚拟主机配置

    server {
        listen       80;  # 监听IPv4的80端口（HTTP默认端口）
        listen       [::]:80;  # 监听IPv6的80端口
        server_name  _; # 通配服务器名（下划线），匹配未指定域名的请求

        root         /usr/share/nginx/html;  # 站点根目录，默认存放网页文件的位置
        include /etc/nginx/default.d/*.conf;  # 引入default.d目录下的所有额外配置（比如安全设置或自定义location）

        error_page 404 /404.html;   # 指定404错误时返回的页面路径
        location = /404.html {
        } # 定义访问404页面的处理逻辑，这里是静态返回，没有特别配置

        error_page 500 502 503 504 /50x.html;  # 指定服务器500/502/503/504错误时返回的页面路径
        location = /50x.html {
        }# 定义访问50x错误页面的处理逻辑，这里同样是静态返回

        # 限制单IP最大连接数 【新增】
        limit_conn addr 10;
    }
}
```


## 常用配置
```nginx configuration
user nginx;  # 指定Nginx运行时使用的系统用户，一般是nginx或者www-data
worker_processes auto;  # 设置工作进程数量，auto表示自动根据CPU核心数设置

error_log /var/log/nginx/error.log error; # 【修改】日志等级改成error，只记录严重错误
pid /run/nginx.pid;  # 定义存放Nginx主进程PID的文件路径
include /usr/share/nginx/modules/*.conf;  # 引入额外模块的配置文件（动态加载模块用）

events {
    worker_connections 10240; # 【修改】连接数从1024提升到10240
}

http {
    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
                      # 定义名为main的访问日志格式，记录访问的详细信息

    access_log  /var/log/nginx/access.log  main;  # 设置访问日志的存储路径，并指定使用main格式

    sendfile            on;  # 开启sendfile，提高文件传输效率（减少拷贝操作）
    tcp_nopush          on;  # 优化TCP发送性能，配合sendfile一起使用，减少网络延迟
    tcp_nodelay         on;  # 禁用Nagle算法，及时发送小数据包，减少延迟

    keepalive_timeout   15; # 【修改】超时从65秒降低到15秒，释放资源更快
    keepalive_requests  100; # 【新增】增加keepalive最大请求数限制

    types_hash_max_size 4096; # 设置MIME类型哈希表的最大大小，提高类型查询效率

    include             /etc/nginx/mime.types; # 引入MIME类型定义文件，用来识别不同文件扩展名

    default_type        application/octet-stream; # 默认的Content-Type，当无法识别文件类型时使用

    # Gzip压缩设置 【新增】
    gzip on;
    gzip_min_length 1024;
    gzip_comp_level 5;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 上传体积限制 【新增】
    client_max_body_size 10M;

    # 隐藏Nginx版本信息 【新增】
    server_tokens off;

    # 缓冲区优化 【新增】
    client_body_buffer_size 16k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;

    # 限制IP最大连接数 【新增】（防简单DDoS）
    limit_conn_zone $binary_remote_addr zone=addr:10m;

    include /etc/nginx/conf.d/*.conf;  # 引入conf.d目录下所有以.conf结尾的配置文件，通常用于虚拟主机配置

    server {
        listen       80;  # 监听IPv4的80端口（HTTP默认端口）
        listen       [::]:80;  # 监听IPv6的80端口
        server_name  _; # 通配服务器名（下划线），匹配未指定域名的请求

        root         /usr/share/nginx/html;  # 站点根目录，默认存放网页文件的位置
        include /etc/nginx/default.d/*.conf;  # 引入default.d目录下的所有额外配置（比如安全设置或自定义location）

        error_page 404 /404.html;   # 指定404错误时返回的页面路径
        location = /404.html {
        } # 定义访问404页面的处理逻辑，这里是静态返回，没有特别配置

        error_page 500 502 503 504 /50x.html;  # 指定服务器500/502/503/504错误时返回的页面路径
        location = /50x.html {
        }# 定义访问50x错误页面的处理逻辑，这里同样是静态返回

        # 限制单IP最大连接数 【新增】
        limit_conn addr 10;
    }
}
```