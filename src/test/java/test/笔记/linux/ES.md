```bash
docker run -d \
    --name es \
    -e "ES_JAVA_OPTS=-Xms512m -Xmx512m" \
    -e "discovery.type=single-node" \
    -v es-data:/usr/share/elasticsearch/data \
    -v es-plugins:/usr/share/elasticsearch/plugins \
    --privileged \
    --network es-net \
    -p 9200:9200 \
    -p 9300:9300 \
elasticsearch:7.17.5
```
```bash
    docker run -d --name es -p 9200:9200 -p 9300:9300 --network es-net  --privileged -e "ES_JAVA_OPTS=-Xms512m -Xmx512m" -e "discovery.type=single-node" -v E:/study/environment/dockerData/Container/ES/conf:/usr/share/elasticsearch/config -v E:/study/environment/dockerData/Container/ES/data:/usr/share/elasticsearch/data -v E:/study/environment/dockerData/Container/ES/plugins:/usr/share/elasticsearch/plugins -v E:/study/environment/dockerData/Container/ES/logs:/usr/share/elasticsearch/logs elasticsearch:8.17.3
```
# 遇到问题
```
received plaintext http traffic on an https channel, closing connection Netty4HttpChannel{localAddress=/127.0.0.1:9200, remoteAddress=/127.0.0.1:52077}
```
解决 ：在config中改.yaml 以下两项为false
```
xpack.security.enabled: false
xpack.security.enrollment.enabled: true
xpack.security.http.ssl:
  enabled: false
```
