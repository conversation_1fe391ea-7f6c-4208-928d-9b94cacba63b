
# 开发环境数据库容器部署

## PostgreSQL 15
```bash
# 创建并运行PostgreSQL容器
docker run --name postgres \
  -e POSTGRES_USER=root \
  -e POSTGRES_PASSWORD=123456 \
  -e POSTGRES_DB=postgres \
  -p 5432:5432 \
  -d postgres:15

# 连接数据库
docker exec -it postgres psql -U root -d postgres

# 停止/启动容器
docker stop postgres
docker start postgres
```

**连接信息：**
- 主机: localhost
- 端口: 5432
- 数据库: postgres
- 用户名: root
- 密码: 123456
- JDBC URL: `*****************************************`

## MongoDB 7.0
```bash
# 创建并运行MongoDB容器
docker run --name mongodb \
  -e MONGO_INITDB_ROOT_USERNAME=root \
  -e MONGO_INITDB_ROOT_PASSWORD=123456 \
  -p 27017:27017 \
  -d mongo:7.0

# 连接数据库
docker exec -it mongodb mongosh --username root --password 123456

# 停止/启动容器
docker stop mongodb
docker start mongodb
```

**连接信息：**
- 主机: localhost
- 端口: 27017
- 用户名: root
- 密码: 123456
- 连接字符串: `*******************************************`

## Elasticsearch 8.11.0 (HTTP模式)
```bash
# 创建并运行Elasticsearch容器 (禁用SSL)
docker run --name elasticsearch \
  -e "discovery.type=single-node" \
  -e "ELASTIC_PASSWORD=123456" \
  -e "xpack.security.enabled=true" \
  -e "xpack.security.http.ssl.enabled=false" \
  -e "xpack.security.transport.ssl.enabled=false" \
  -p 9200:9200 \
  -p 9300:9300 \
  -d elasticsearch:8.11.0

# 测试连接
docker exec -it elasticsearch curl -u elastic:123456 http://localhost:9200

# 停止/启动容器
docker stop elasticsearch
docker start elasticsearch
```

**连接信息：**
- 主机: localhost
- 端口: 9200 (HTTP), 9300 (Transport)
- 用户名: elastic
- 密码: 123456
- 访问地址: `http://localhost:9200`
- JDBC URL: `jdbc:elasticsearch://localhost:9200?user=elastic&password=123456&ssl=false`

## 批量操作命令

### 查看所有运行的容器
```bash
docker ps
```

### 查看特定服务容器
```bash
# 查看PostgreSQL
docker ps | findstr postgres

# 查看MongoDB
docker ps | findstr mongodb

# 查看Elasticsearch
docker ps | findstr elasticsearch
```

### 批量停止容器
```bash
# 停止所有新创建的数据库容器
docker stop postgres mongodb elasticsearch
```

### 批量启动容器
```bash
# 启动所有数据库容器
docker start postgres mongodb elasticsearch
```

### 批量删除容器
```bash
# 删除所有数据库容器 (注意：会丢失数据)
docker rm postgres mongodb elasticsearch
```

## Spring Boot 配置示例

### application.yml 配置
```yaml
spring:
  # PostgreSQL配置
  datasource:
    url: *****************************************
    username: root
    password: 123456
    driver-class-name: org.postgresql.Driver

  # MongoDB配置
  data:
    mongodb:
      uri: *******************************************

  # Elasticsearch配置
  elasticsearch:
    uris: http://localhost:9200
    username: elastic
    password: 123456
```

### Maven依赖
```xml
<!-- PostgreSQL -->
<dependency>
    <groupId>org.postgresql</groupId>
    <artifactId>postgresql</artifactId>
</dependency>

<!-- MongoDB -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-mongodb</artifactId>
</dependency>

<!-- Elasticsearch -->
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
</dependency>
```

## 注意事项

1. **统一密码**: 所有数据库都使用 `root/123456` 作为账号密码（Elasticsearch使用 `elastic/123456`）
2. **端口映射**: 确保本地端口未被占用
3. **数据持久化**: 当前配置未挂载数据卷，容器删除后数据会丢失
4. **开发环境**: 这些配置仅适用于开发环境，生产环境需要额外的安全配置
5. **SSL配置**: Elasticsearch已禁用SSL以简化开发环境配置

## 完整的开发环境服务列表

运行 `docker ps` 后可以看到以下服务：
- ✅ PostgreSQL 15 (端口5432)
- ✅ MongoDB 7.0 (端口27017)
- ✅ Elasticsearch 8.11.0 (端口9200)
- ✅ MySQL 8.0 (端口3306) - 之前已存在
- ✅ Redis 7.2 (端口6379) - 之前已存在
- ✅ RabbitMQ 3.12 (端口5672/15672) - 之前已存在
- ✅ Nacos 2.0.4 (端口8848) - 之前已存在