在使用Docker运行MySQL容器时，你可以通过`-v`选项将配置文件和数据目录挂载到主机上。以下是具体步骤：

1. **创建数据目录和配置文件目录**（如果尚未创建）：
   ```bash
   sudo mkdir -p /usr/dockerData/mysql/log
   sudo mkdir -p /usr/dockerData/mysql/data
   sudo mkdir -p /usr/dockerData/mysql/conf

   docker pull mysql:8.0.29
   ```
2. **运行MySQL容器并挂载目录**：
   ```bash
    docker run -p 3306:3306 --name mysql --restart=always --privileged=true \
    -v /usr/dockerData/mysql/log:/var/log/mysql \
    -v /usr/dockerData/mysql/data:/var/lib/mysql \
    -v /usr/dockerData/mysql/conf/my.cnf:/etc/mysql/my.cnf \
    -e MYSQL_ROOT_PASSWORD=123456 -e TZ=Asia/Shanghai  -d mysql:8.0.29
   ```
   
# 集群 
1. 创建网络
   ```bash
   docker network create --driver bridge --subnet **********/24 --gateway ********** my-network
   ```
2. 创建容器
   ```bash
   docker cp mysql:/etc/my.cnf /usr/dockerData/mysql/conf/
   docker run -itd -p 3306:3306      
   --name mysql-master      
   --net mynet --ip ***********      
   -v /opt/mydata/docker/mysql/mysql-master/conf/my.cnf:/etc/mysql/my.cnf      
   -v /opt/mydata/docker/mysql/mysql-master/data:/var/lib/mysql      
   --privileged=true      
   --restart=always      
   -e MYSQL_ROOT_PASSWORD=123456     
   -e TZ=Asia/Shanghai      
   mysql:8.0.29
   ```

   ```
   docker run -itd -p 3306:3306      
   --name mysql-master      
   --net mynet --ip ***********      
   -v /opt/mydata/docker/mysql/mysql-master/conf/my.cnf:/etc/mysql/my.cnf      
   -v /opt/mydata/docker/mysql/mysql-master/data:/var/lib/mysql      
   --privileged=true      
   --restart=always      
   -e MYSQL_ROOT_PASSWORD=123456     
   -e TZ=Asia/Shanghai      
   mysql:8.0.27
   
   docker cp mysql-demo:/etc/mysql/my.cnf /opt/mydata/docker/mysql/mysql-master/conf/
   
   
   redis mq es
   
   sudo docker run -itd -p 3301:3306 \
   --name mysql-master \
   --net mynet --ip *********** \
   -v /opt/dockerData/mysql/master/conf/my.cnf:/etc/mysql/my.cnf \
   -v /opt/dockerData/mysql/master/data:/var/lib/mysql \
   --privileged=true \
   --restart=always \
   -e MYSQL_ROOT_PASSWORD=123456 \
   -e TZ=Asia/Shanghai \
   mysql:8.0.27

   ```
# 创建数据库前判断
   ```sql
   CREATE DATABASE IF NOT EXISTS your_database_name CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
use your_database_name;
   ```