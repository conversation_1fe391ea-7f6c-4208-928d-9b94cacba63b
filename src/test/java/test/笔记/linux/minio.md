```bash
   docker run -p 9000:9000 -p 9090:9090 --network=my-network --name minio -d --restart=always -e MINIO_ACCESS_KEY=minioadmin -e MINIO_SECRET_KEY=minioadmin -v E:\study\environment\dockerData\Container\minio\data:/data  -v E:\study\environment\dockerData\Container\minio\conf:/root/.minio -d minio/minio server /data/data1 /data/data2 --console-address ":9090" -address ":9000"
```

```bash
docker run  --name xxl-job  -p 18088:8080 --network my-network --restart=always --privileged=true  -e PARAMS="--spring.datasource.username=root --spring.datasource.username=123456 --spring.datasource.url=*****************************************************************************************************************************"  -v E:\study\environment\dockerData\Container\xxl-job\logs:/data/applogs   -d vulhub/xxl-job:2.2.0-admin
```
