## concurrentHashMao 1.7 
### 1结构图
![concurrentHashMap1.7结构图](imgs/concurrentHashMap1.7.png)
- 1 Segment（继承于extends ReentrantLock)
基于锁分段，每个 分段锁 维护一个HashEntry数组，每个HashEntry节点存放的是一个链表结构的单向链表（hashMap1.7结构）。
- 2 锁为ReentrantLock锁 tryLock()尝试获取锁返回turn/false
- 3 使用UNSAFE查询主内存最新的数据
- 4.解决并发核心为scanAndLockForPut()方法中主要使用cas做修改 </br>
  自旋缓存当前对应冲突链表。</br>
  每次检查当前缓存链表头结点是否有发生变化。</br>
  如果发生变化的情况下，修改为最新的。</br>
  自旋次数最大是为64</br>
  参数解读 entryForHash方法用于获取给定哈希值的第一个条目。
  tryLock 方法尝试获取当前段的锁。
  如果 e 为 null，表示没有找到键对应的条目，那么会创建一个新的节点 node。
  如果 retries 大于 MAX_SCAN_RETRIES（数值为64），则使用 lock 方法强制加锁。
  (retries & 1) == 0 是一个检查，用于在每次重试的偶数次时重新检查第一个条目是否发生了变化。

### Jdk1.8ConcurrentHashMap
- 1.去除Segment分段锁
- 2.synchronized+cas保证node节点线程安全问题
- HashMap1,8与jdk1,8 ConcurrentHashMap数据结构模型是一样ConcurrentHashMap数组+链表+红黑树
- 3 ConcurrentHashMap加锁对应table数组index下标对应的node（链表）节点上锁 1.7是Segment（数组）节点
  ConcurrentHashMap的竞争：
- 4 多个线程同时put key的时候如果多个key都落入到同一个index node节点的时候需要做锁的竞争：
  如果没有落到一个index node节点的情况下不需要做锁的竞争：只需要计算一次index值 1.7需要计算两次
```java

final V putVal(K key, V value, boolean onlyIfAbsent) {
      if (key == null || value == null) throw new NullPointerException();
      //扰动计算，和HashMap一样
      int hash = spread(key.hashCode());
      int binCount = 0;
      //进入循环
      for (Node<K,V>[] tab = table;;) {
            Node<K,V> f; int n, i, fh;
  
            if (tab == null || (n = tab.length) == 0){
                  //也是懒加载，如果table为null或长度为0，则进行初始化
                  tab = initTable();
           }
  
            else if ((f = tabAt(tab, i = (n - 1) & hash)) == null) {
                  //当前索引位置没有元素，则通过CAS操作尝试插入新节点
                  if (casTabAt(tab, i, null,
                                     new Node<K,V>(hash, key, value, null)))
                        break;                            // no lock when adding to empty bin
           }
            else if ((fh = f.hash) == MOVED){
                  //说明当前索引位置有元素，且hash值为MOVED，表示正在进行扩容。
                  //帮助迁移数据
                  tab = helpTransfer(tab, f);
           } else {
                  //ps：到这步则进行链表/红黑树的节点遍历和插入操作
                  V oldVal = null;
                  //加锁，确保只有一个线程操作改桶位置的链表或红黑树
                  synchronized (f) {
                        if (tabAt(tab, i) == f) {
                              if (fh >= 0) {
                                    //遍历链表，找到相同的key的节点，更新值或插入新节点
                                    binCount = 1;
                                    for (Node<K,V> e = f;; ++binCount) {
                                          K ek;
                                          if (e.hash == hash &&
                                               ((ek = e.key) == key ||
                                                 (ek != null && key.equals(ek)))) {
                                                oldVal = e.val;
                                                if (!onlyIfAbsent)
                                                      e.val = value;
                                                break;
                                         }
                                          Node<K,V> pred = e;
                                          if ((e = e.next) == null) {
                                                //将新节点插入到链表末尾（尾插法）
                                                pred.next = new Node<K,V>(hash, key,
                                                                                       value, null);
                                                break;
                                         }
                                   }
                             }
                              //遍历红黑树，找到相同的key的节点，更新至或插入新节点
                              else if (f instanceof TreeBin) {
                                    Node<K,V> p;
                                    binCount = 2;
                                    if ((p = ((TreeBin<K,V>)f).putTreeVal(hash, key,
                                                                                  value)) != null) {
                                          oldVal = p.val;
                                          if (!onlyIfAbsent)
                                                p.val = value;
                                   }
                             }
                       }
                 }
                  if (binCount != 0) {
                        //插入或更新值成功，判断是否需要进行树化操作
                        if (binCount >= TREEIFY_THRESHOLD)
                              treeifyBin(tab, i);
                        if (oldVal != null)
                              return oldVal;
                        break;
                 }
           }
     }
      //累加元素个数，传入添加的数量1，以及链表节点个数，用于控制是否执行扩容操作
      addCount(1L, binCount);
      return null;
}
```

### 锁在什么时候使用
- CAS锁：
  1. put index没有发生冲突（获取链表为null）
  2. put时 初始化table(initTable()方法中) 懒加载table
  3. addCount()方法中，当counterCells为null时，对counterCells进行初始化时，以及对SizeCtl修改+1时（表示这个线程也参加扩容）随后调用扩容方法。
  4. 扩容时（transfer()方法中）扩容前划分每个线程从哪里处理迁移以及记录完成的线程数，
  transferIndex(确保多个线程可以协同工作而不会重复迁移同一个桶)修改，以及对sizeCtl的减少(数组需要扩容时的大小，这里表示一个线程已经完成了迁移任务)。
- synchronized：
  1. index已经发生冲突，<span style="color:red;">使用synchronized对头node节点上锁 </span>。
  2. 扩容时(transfer()方法中) <span style="color:red;">真正扩容迁移时 </span>
详细以下内容
### 扩容
- ConcurrentHashMap通过ForwardingNode来记录当前桶是否被迁移，如果old Table[i] instanceOf ForwardingNode则说明处于i节点的桶已经被移动到newTable中了。它里面有一个nextTable变量，指向的是下一次扩容后的table。
- transferIndex记录了当前扩容的桶索引，最开始为oldTable.length，它给下一个线程指定了要扩容的节点。
#### 大致流程
1. 通过CPU核数为每个线程计算划分任务，每个线程最少的任务是迁移16个桶。
2. 将当前桶扩容的索引transferIndex赋值给当前线程，如果索引<=0，则说明扩容完毕，结束流程，否则
3. 再将当前线程扩容后的索引赋值给transferIndex，比如如果transferIndex原来是32，那么赋值之后transferIndex应该变为16，这样下一个线程就可以从16开始扩容。通过CAS进行设置，不会有并发问题。
4. 之后就可以对真正的扩容流程进行加锁操作了。

以下为AI生成内容
1. 任务划分：每个线程需要迁移的桶数量由 CPU 核心数决定，最少 16 个桶。
2. 获取索引：使用 CAS 操作将 transferIndex 分配给当前线程，确定迁移范围。
3. 迁移数据：
   - 如果桶为空，标记为 ForwardingNode。
   - 如果桶已被迁移，跳过。
   - 如果桶有数据，加锁迁移数据到新表。
4. 更新状态：迁移完成后，更新 sizeCtl，完成扩容。
   这样，ConcurrentHashMap 可以高效地利用多线程进行扩容，同时保持线程安全。

代码解读
```java
private final void transfer(Node<K,V>[] tab, Node<K,V>[] nextTab) {
    //n:数组长度  stride：每个处理多少任务
    int n = tab.length, stride;
    //多喝处理n>>>3/核心数个任务，最少处理16个任务
    if ((stride = (NCPU > 1) ? (n >>> 3) / NCPU : n) < MIN_TRANSFER_STRIDE)
        stride = MIN_TRANSFER_STRIDE; // subdivide range
 
    if (nextTab == null) {            // initiating
        //ps：表示第一个线程进此方法
        try {
            //扩容为原数组1倍
            @SuppressWarnings("unchecked")
            Node<K,V>[] nt = (Node<K,V>[])new Node<?,?>[n << 1];
            nextTab = nt;
        } catch (Throwable ex) {      // try to cope with OOME
            sizeCtl = Integer.MAX_VALUE;
            return;
        }
        nextTable = nextTab;
        transferIndex = n;
    }
    int nextn = nextTab.length;
    //创建一个fwd节点，用于控制并发。当一个节点为空或已被转移后，接设置fwd节点，表示处于move状态
    ForwardingNode<K,V> fwd = new ForwardingNode<K,V>(nextTab);
    //是否继续向前查找
    boolean advance = true;
    //在完成之前重新再扫描一遍数组，看看有没完成的没
    boolean finishing = false; // to ensure sweep before committing nextTab
    for (int i = 0, bound = 0;;) {
        Node<K,V> f; int fh;
        // 执行数据迁移
        // 且重新非陪transferIndex的值，用于不停向前推进更新迁移数据
        while (advance) {
            int nextIndex, nextBound;
            if (--i >= bound || finishing)
                advance = false;
            else if ((nextIndex = transferIndex) <= 0) {
                i = -1;
                advance = false;
            }
            else if (U.compareAndSwapInt
                     (this, TRANSFERINDEX, nextIndex,
                      nextBound = (nextIndex > stride ?
                                   nextIndex - stride : 0))) {
                bound = nextBound;
                i = nextIndex - 1;
                advance = false;
            }
        }
        //数据迁移完成的后置处理。包括重新检查一遍迁移数据以及归还线程。
        if (i < 0 || i >= n || i + n >= nextn) {
            int sc;
            if (finishing) {
                nextTable = null;
                table = nextTab;
                sizeCtl = (n << 1) - (n >>> 1);
                return;
            }
            if (U.compareAndSwapInt(this, SIZECTL, sc = sizeCtl, sc - 1)) {
                if ((sc - 2) != resizeStamp(n) << RESIZE_STAMP_SHIFT)
                    return;
                finishing = advance = true;
                i = n; // recheck before commit
            }
        }
 
        else if ((f = tabAt(tab, i)) == null)
            //把数组中null的元素的hash值置为MOVED，让循环体处理下一个节点，后续辅助线程发现节点为MOVE则会直接跳过
            advance = casTabAt(tab, i, null, fwd);
        else if ((fh = f.hash) == MOVED)
            //表示已有线程再处理，让循环体处理下一个节点
            advance = true; // already processed
        else {
            //锁住节点，开始真正的扩容流程
            synchronized (f) {
                if (tabAt(tab, i) == f) {
                    Node<K,V> ln, hn;
                    if (fh >= 0) {
                        //ps：说明是node节点
                        int runBit = fh & n;
                        Node<K,V> lastRun = f;
                        for (Node<K,V> p = f.next; p != null; p = p.next) {
                            int b = p.hash & n;
                            if (b != runBit) {
                                runBit = b;
                                lastRun = p;
                            }
                        }
                        if (runBit == 0) {
                            ln = lastRun;
                            hn = null;
                        }
                        else {
                            hn = lastRun;
                            ln = null;
                        }
                        //前面的节点不确定高低位，所以遍历f~LastRun范围的所有节点
                        //分别逆序存入Ln或hn链表中
                        for (Node<K,V> p = f; p != lastRun; p = p.next) {
                            int ph = p.hash; K pk = p.key; V pv = p.val;
                            if ((ph & n) == 0)
                                ln = new Node<K,V>(ph, pk, pv, ln);
                            else
                                hn = new Node<K,V>(ph, pk, pv, hn);
                        }
                        //存入之前的位置
                        setTabAt(nextTab, i, ln);
                        //存入改变后的位置
                        setTabAt(nextTab, i + n, hn);
                        //设置fwd，这样其它线程执行的时候，会跳过去
                        setTabAt(tab, i, fwd);
                        advance = true;
                    }
                    else if (f instanceof TreeBin) {
                        //ps：红黑树的处理
                       //...省略
                    }
                }
            }
        }
    }
}
```
## concurrentHashMap1.8  面试
1. 为什么ConcurrentHashMap1.8需要去除Segments分段锁？
   - ConcurrentHashMap1.8的取出取消segment分段设计（锁了整个table），采用对CAS+synchronized(hash计算后位置元素如果为空用CAS 
   否则synchronized)保证并发线程安全问题，将锁的粒度拆分到每个index 下标位置（锁链表node），实现的效率与ConcurrentHashMap1.7相同。
   - ConcurrentHashMap1.8的锁粒度更小，并发性更高。
   - 1.7put 需要计算两次hash 1.8只需要一次。效率提高。因为两层数组

2. 为什么 ConcurrentHashMap1.8使用synchronized而不使用lock锁？
 ConcurrentHashMap1.7在jdk1.5的时候出现 而synchronized在1.6之后才做优化
(锁升级 无锁->偏向锁(markWord中存线程id)->轻量级锁（CAS）
->重量级锁（cas10次失败后升级 阻塞线程到队列中等待 头为在执行线程）)
3. ConcurrentHashMap1.8如何基于Node节点实现锁机制
   
1.8时通过CounterCell[]数组实现
   1. 尝试通过Unsafe的CAS修改baseCount(基本的计数 主要用途非并发场景)
   2. 如果失败则尝试则通过当前线程的随机数计算应在CounterCell[]数组哪个下标 如果该下标不为null则再次通过cas修改
   3. 如果还失败则通过fullAddCount()（太复杂了 简单描述 第一阶段，计算当前线程的探针哈希值；
      第二阶段，在无限循环中，利用CounterCell进行计数。） cas修改
   4. 最后在sumCount()方法中通过累加baseCount和CounterCell数组的counterCell值获取到Map的总大小Size，但是这个size只是一个约数，如果获取size的同时纯在插入或者删除操作，size和实际的数量可能有所不同。

4. ConcurrentHashMa即多线程size++效率最高？
   
5. 多个线程同时执行CAS效率真的非常高吗？
  在竞争不激烈的情况下，CAS可以快速完成更新，因为不需要像传统锁那样引起线程的阻塞和上下文切换。
然而，如果多个线程频繁地竞争同一个变量， 
CAS可能会在多线程环境中，CAS（Compare-And-Swap）操作提供了一种无锁的原子操作方式，它在处理并发时不需要传统的锁机制，
因此在某些情况下可以提高效率。然而，CAS的效率并不总是非常高，这主要取决于线程间的竞争程度；
- 6.ConcurrentHashMap1.8与ConcurrentHashMap1.7区别
  结构变化：
  1.7版本：采用分段锁Segment，每个Segment是一个独立的ReentrantLock。
  1.8版本：取消了Segment结构，改为直接对节点进行加锁，提高了并发性能。
  锁的优化：
  1.7版本：使用多个Segment锁来减少锁竞争。
  1.8版本：引入红黑树优化长链表，使用更细粒度的锁，如synchronized和CAS操作。
  性能提升：
  1.8版本：在高并发环境下，通过减少锁的粒度和优化数据结构，提高了查询和更新操作的效率。