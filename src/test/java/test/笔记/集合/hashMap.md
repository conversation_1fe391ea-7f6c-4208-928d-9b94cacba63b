## 1. hashMap1.7 
#### 1.1 死循环问题
- 发生在扩容时且多线程情况下
- 当线程1 2都拿到当前节点 并且1线程先完成了链表的迁移（头插法 a->b->c => c->b->a）线程2 如果线程2当前节点为b 改变指针next时就会导致回路（b->a    ->b->a）在调用get的时候 查询ab以外的key（hash计算index刚好命中该链表且这个链表不存在该key）遍历该链表时就会死循环 
- （问题代码）（e当前节点）e.next= newTable[i]（数组）;

### 1.2 总结
   1. HashMap创建时，假如没有给定长度，初始默认长度为16，默认加载因子0.75f；
   2. HashMap在创建时，并不会初始化数组，而是在第一次添加元素时初始化数组；
   3. 当HashMap需要扩容时，扩容长度为原来的2倍；
   4. HashMap存储数据结构是数据+链表实现的；
   5. HashMap存入数据采用头插法，头插法在并发编程中会出现循环链表，在get方法时会出现死锁；
   6. HashMap存入时key值允许为空值，但是key值唯一；
   7. HashMap初始值为2的N次幂的好处有方便&运算，&运算比取模运算效率高，扩容之后的迁移只需要判断二进制位即可算出新下标
   ————————————————

版权声明：本文为博主原创文章，遵循 CC 4.0 BY-SA 版权协议，转载请附上原文出处链接和本声明。

原文链接：https://blog.csdn.net/qq_42764468/article/details/129234373

## 2. HashMap1.8
### 2.1 Put流程图
![HashMap1.8Put流程图.png](imgs/HashMap1.8Put.png)
### 2.2 扩容

- 计算新的容量大小： 根据threshold（扩容阈值 - 负载因子（load factor）乘以当前容量）数组达到阈值时就开始扩容（resize()方法），
- 负载因子是指HashMap允许的最大填充比例，一般情况下默认为0.75。HashMap会重新计算一个更大的容量大小，通常是当前容量的两倍（oldThr << 1 ）并更新扩容阈值。
- 创建新的数组：先拷贝到局部变量并将旧数组对链表的引用设置为null，遍历旧数组的每个数据，重新计算每个元素在新数组中的存储位置。
使用节点的hash值与旧数组长度进行位与运算（e.hash & oldCap（原来的容量 这个值必为偶数 偶数与运算必然是0或者是这个偶数 所以可以平均分割成了两个链表）），如果运算结果为0，表示元素在新数组中的位置不变；
否则，则在新数组中的位置下标=原位置+原数组长度，也就是平均分割了。
- 更新引用：将旧数组上的每个数据使用尾插法逐个转移到新数组中。

### 2.3 HashMap 链表转换成红黑树
- 当链表长度达到8个时，会触发 treeifyBin()  方法检测数组长度有没有大于64 大于则将链表节点（Node）
转红黑树节点（TreeNode，间接继承Node）， 转成红黑树节点后，其实链表的结构还存在， 通过next属性维持，
红黑树节点在进行操作时都会维护链表的结构， 并不是转为红黑树节点后，链表结构就不存在了。
当数组中某个位置的节点在移除后达到6个时， 并且该索引位置的节点为红黑树节点，会触发 untreeify() 将红黑树节点转化成链表节点。
HashMap 在进行插入和删除时有可能会触发红黑树的插入平衡调整（balanceInsertion方法）或删除平衡调整（balanceDeletion ）方法，
调整的方式主要有以下手段：
左旋转（rotateLeft方法）、右旋转（rotateRight方法）、改变节点颜色（x.red = false、x.red = true），
进行调整的原因是为了维持红黑树的数据结构。<br>

- 当链表长过长时会转换成红黑树，那能不能使用AVL树替代呢？
AVL树是完全平衡二叉树，要求每个结点的左右子树的高度之差的绝对值最多为1，
而红黑树通过适当的放低该条件（红黑树限制从根到叶子的最长的可能路径不多于最短的可能路径的两倍长，结果是这个树大致上是平衡的），
以此来减少插入/删除时的平衡调整耗时，从而获取更好的性能，虽然会导致红黑树的查询会比AVL稍慢，但相比插入/删除时获取的时间，
这个付出在大多数情况下显然是值得的。


版权声明：本文为博主原创文章，遵循 CC 4.0 BY-SA 版权协议，转载请附上原文出处链接和本声明。
原文链接：https://blog.csdn.net/m0_52012606/article/details/125984243


## 2.4HashMap 在 JDK1.7 和 JDK1.8 有哪些区别?
① 数据结构：在 JDK1.7 及之前的版本，HashMap 的数据结构可以看成“数组+链表”，在 JDK1.8 及之后的版本，数据结构可以看成"数组+链表+红黑树"，当链表的长度超过8时，链表就会转换成红黑树，从而降低时间复杂度（由O(n) 变成了 O(logN)），提高了效率

② 对数据重哈希：JDK1.8 及之后的版本，对 hash() 方法进行了优化，重新计算 hash 值时，让 hashCode 的高16位参与异或运算，目的是在 table 的 length较小的时候，在进行计算元素存储位置时，也让高位也参与运算。

③ 在 JDK1.7 及之前的版本，在添加元素的时候，采用头插法，所以在扩容的时候，会导致之前元素相对位置倒置了，在多线程环境下扩容可能造成环形链表而导致死循环的问题。JDK1.8之后使用的是尾插法，扩容是不会改变元素的相对位置

④ 扩容时重新计算元素的存储位置的方式：JDK1.7 及之前的版本重新计算存储位置是直接使用 hash & (table.length-1)；JDK1.8 使用节点的hash值与旧数组长度进行位与运算，如果运算结果为0，表示元素在新数组中的位置不变；否则，则在新数组中的位置下标=原位置+原数组长度。

⑤ JDK1.7 是先扩容后插入，这就导致无论这次插入是否发生hash冲突都需要进行扩容，但如果这次插入并没有发生Hash冲突的话，那么就会造成一次无效扩容；JDK1.8是先插入再扩容的，优点是减少这一次无效的扩容，原因就是如果这次插入没有发生Hash冲突的话，那么其实就不会造成扩容。

## 2.5 并发环境下数据丢失的具体原因
- 竞态条件和覆盖：

- 假设两个线程同时尝试插入不同的键值对到HashMap中的相同位置。
- 由于没有同步机制，两个线程可能几乎同时读到某个桶是空的，并各自计算其键值对应存储的位置。
- 如果两者都尝试写入同一个位置，其中一个线程的写入操作可能会覆盖另一个线程的写入，导致其中一个键值对丢失。
- 扩容操作中的数据丢失：
- 
- 当HashMap中的元素数量超过负载因子与当前容量的乘积时，HashMap会进行扩容，即创建一个容量更大的新数组，并将旧数组中的所有元素重新映射到新数组中。
- 在并发环境下，如果有多个线程同时进行插入操作，而另一线程正在进行扩容操作，没有适当的同步可能会导致一些元素在重新映射过程中丢失，或者两个线程可能在不同的数组实例上操作，导致数据不一致。
- 链表转红黑树的过程中数据丢失：
- 
- 在HashMap的某个桶的链表长度超过阈值（如8）时，链表会转换为红黑树，以改善未来的查找和插入性能。
- 如果在转换过程中，多个线程同时对这个桶进行操作，而转换操作没有被适当同步，可能会导致一些元素在链表和红黑树之间的转换过程中丢失。