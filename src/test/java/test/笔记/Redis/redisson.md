# 原理
## 前言
在单服务系统我们常用JVM锁——Synchronized、ReentrantLock等。然而在多台服务系统的情况下，JVM锁就无法在多个服务器之间生效了，这时候我们就需要用分布式锁来解决线程安全的问题。

分布式锁的实现方式有很多，主流的就是基于数据库、zookeeper以及redis，当然使用redis的居多，由于篇幅原因，本节就详细介绍一下使用redis实现分布式锁的几种方式。

## 整体类图
![img_13.png](img_13.png)

标黄的两个类就是我们今天的重点，看门狗续期的实现逻辑在RedissonBaseLock类中，加锁逻辑在RedissonLock类中。

## 执行流程
![img_14.png](img_14.png)


## 原文[Redis第14讲——Redis实现分布式锁（Redisson源码解析）_redisson分布式锁实现-CSDN博客.html](html/Redis%E7%AC%AC14%E8%AE%B2%E2%80%94%E2%80%94Redis%E5%AE%9E%E7%8E%B0%E5%88%86%E5%B8%83%E5%BC%8F%E9%94%81%EF%BC%88Redission%E6%BA%90%E7%A0%81%E8%A7%A3%E6%9E%90%EF%BC%89_redission%E5%88%86%E5%B8%83%E5%BC%8F%E9%94%81%E5%AE%9E%E7%8E%B0-CSDN%E5%8D%9A%E5%AE%A2.html)

## Redis分布式锁的SETNX实现和Redisson的区别
### 1. 概念
Redis分布式锁的SETNX实现和Redisson的区别主要在于实现方式和提供的功能。

SETNX实现方式主要依赖于Redis的SETNX命令，这是一种原子操作，只有当指定的key不存在时，才能设置这个key的值。</br>
这种方式实现分布式锁的基本原理是，当一个进程尝试获取锁时，它会使用SETNX命令设置一个key，如果设置成功，</br>
那么它就获得了锁；如果设置失败，那么它就尝试等待一段时间后再次尝试获取锁。<span style="color:red;">这种方式实现简单，但是它没有考虑锁的自动释放和锁的续期等问题。 </span>

Redisson是Redis的Java客户端，它提供了一套完整的分布式锁的实现。Redisson的分布式锁不仅实现了基本的获取和释放锁的功能，还提供了锁的自动释放和锁的续期等功能。</br>
Redisson的分布式锁的实现是基于Redis的SET命令，它使用SET命令的NX参数来实现锁的获取，使用EX参数来实现锁的自动释放，使用PX参数来实现锁的续期。</br>
Redisson的分布式锁的实现还考虑了锁的重入性，即一个进程可以多次获取同一个锁，Redisson会记录这个进程获取锁的次数，只有当这个进程释放锁的次数等于获取锁的次数时，锁才会被真正释放。</br>

总的来说，SETNX实现方式是一种基本的分布式锁的实现，而Redisson提供了更完整和更强大的分布式锁的实现。
### 2. 带来的问题
1. 如果没有设置过期时间
   如果没有设置过期时间，那么锁将一直存在，直到手动释放。如果进程在获取锁后由于某些原因（如崩溃或网络问题）无法释放锁，那么其他进程将无法获取锁，导致死锁。
2. 如果设置了过期时间
   如果设置了过期时间，那么锁将在一段时间后自动释放。但是，如果进程在获取锁后由于某些原因（如网络延迟）无法及时续期，那么锁可能会在一段时间后自动释放，<span style="color:red;"> 导致其他进程可以获取锁，同一块代码会执行两次，从而导致数据的不一致</span>。
3. 死锁
   如果多个进程同时尝试获取锁，并且没有设置过期时间，那么可能会出现死锁的情况。例如，两个进程A和B同时尝试获取锁，进程A获取锁后由于某些原因无法释放锁，进程B也尝试获取锁，但是由于进程A没有释放锁，进程B将无法获取锁，导致死锁。 
#### tip 重入锁的
锁的重入性（Reentrant Lock）的主要作用是：

1. **避免死锁**：在同一个进程或线程中多次获取同一个锁时，不会导致死锁。

2. **实现锁的递归调用**：在递归函数中可以多次获取同一个锁，而不会导致死锁。

3. **提高分布式锁的可靠性和安全性**：避免由于锁的多次获取导致的死锁问题，从而提高分布式锁的可靠性和安全性。

## 小结
### 什么时候会进行锁续期
加锁时，如果没有指定过期时间，则默认过期时间为30s且每隔10s进行锁续期操作。
## redisson提供了几种锁
Redisson提供了多种类型的分布式锁，以满足不同的业务需求。以下是Redisson支持的主要分布式锁类型：

1. **可重入锁（Reentrant Lock）**
   - 类型：`RLock`
   - 特点：支持同一个线程多次获取锁，并且需要释放相同次数的锁才能真正释放。
2. **红锁（RedLock）**
   - 类型：`RedLock`
   - 特点：在多个Redis实例上实现分布式锁，提高锁的可用性和容错性。
3. **公平锁（Fair Lock）**
   - 类型：`RFairLock`
   - 特点：按照请求锁的顺序获取锁，确保线程公平地获取锁。
4. **联锁（MultiLock）**
   - 类型：`MultiLock`
   - 特点：同时获取多个锁，只有当所有锁都被成功获取时，整个锁才算获取成功。
5. **读写锁（Read-Write Lock）**
   - 类型：`RReadWriteLock`
   - 特点：提供读锁和写锁，允许多个线程同时读取数据，但写操作是独占的。
-- 以下不常用
6. **信号量（Semaphore）**
   - 类型：`RSemaphore`
   - 特点：允许多个线程同时访问，但可以通过信号量限制访问的数量。

7. **许可（Permit）**
   - 类型：`RPermitExpirableSemaphore`
   - 特点：类似于信号量，但每个许可都有一个过期时间。

8. **锁与Map结合（Map Cache）**
   - 类型：`RMapCache`
   - 特点：提供带有过期时间的Map，可以用于实现更复杂的分布式锁场景。

这些锁类型覆盖了多种常见的分布式锁需求，开发者可以根据具体的应用场景选择合适的锁类型。
### 什么情况会停止续期
1. 锁被释放。
2. 续期时发生异常。
3. 执行锁续期LUA脚本失败。
4. Redisson的续期时Netty时间轮（TimerTask、TimeOut、Timer）的，并且操作都是基于JVM，所以当应用宕机、下线或重启后，续期任务也没有了。

四、Redisson实现分布式锁存在的问题
Redisson使用看门狗续期的方案在大多数场景下是挺不错的，但在极端情况下还是会存在问题，比如：

1. 线程1首先获取锁成功，将键值对写入redis的master节点。
2. 在redis将master数据同步到slave节点之前，master故障了。
3. 此时会触发故障转移，将其中一个slave升级为master。
4. 但新的master并没有线程1写入的键值对，因此如果此时来个线程2，也同样可以获取到锁，这就违背了锁的初衷。

这个场景就是我们常说的集群脑裂（网络分区）问题。

那么比较主流的解决方案就是Redis作者提出的Redlock和Zookeeper实现的分布式锁，这个我们下节再讲。
文章：https://blog.csdn.net/weixin_45433817/article/details/138251421
## 这里我就概括redLock

### 1。 为什么要使用RedLock
在上篇文章也提到过，不管是用原生的setnx命令还是用Reidssion实现的分布式锁，它们都无法解决Reids在主从复制、哨兵集群下的多节点问题：
   - 线程A在Matser上获取锁。
   - 在Key的写入被传输到Slave之前，Matser崩溃。
   - Slave被提升为主节点。
   - 线程B 获取了与线程A已经持有锁相同的锁。
RedLock就是为了解决这种问题，它是一种分布式锁的算法，可以在多个Redis节点上实现分布式锁，提高锁的可用性和容错性。

### 2.  RedLock加锁原理
RedLock通过使用多个Redis节点，来提供一个更加健壮的分布式锁解决方案，能够在某些Redis节点故障的情况下，仍然能够保证分布式锁的可用性。
![img_15.png](img_15.png)
### 3. 存在的问题
![img_16.png](img_16.png)
还有一个性能问题：setnx和Redission实现的分布式锁只需要在一个节点写成功就行了，而RedLock需要写多个节点才算加锁成功。

### 4 如何使用
![img_17.png](img_17.png)