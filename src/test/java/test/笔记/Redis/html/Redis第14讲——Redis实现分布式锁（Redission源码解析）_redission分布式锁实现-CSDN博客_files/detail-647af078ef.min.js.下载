function setCopyBtn(){$(".CopyToClipboard").each(function(){var t=new ZeroClipboard.Client;t.setHandCursor(!0),t.addEventListener("load",function(t){}),t.addEventListener("mouseOver",function(t){var e=t.movie.parentNode.parentNode.parentNode.parentNode.parentNode.nextSibling.innerHTML;e=e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&"),t.setText(e)}),t.addEventListener("complete",function(t,e){alert("代码已经复制到你的剪贴板。")}),t.glue(this,this.parentNode)})}function computePos(){"object"==("undefined"==typeof toolBar?"undefined":_typeof(toolBar))&&(toolBar.setPosX(),toolBar.computePositon())}function getRecommendListUrl(){return null!==getRecommendListUrlArr?getRecommendListUrlArr:(getRecommendListUrlArr=[[],[]],$(".recommend-box div.recommend-item-box").each(function(t,e){if($(e).data("url")){var o=$(e).data("url").toLowerCase().split("://"),n=$(e).data("url").toLowerCase().split("article/details/");getRecommendListUrlArr[0].push(2==o.length?o[1]:o[0]),getRecommendListUrlArr[1].push(2==n.length?"article/details/"+n[1]:n[0])}}),getRecommendListUrlArr)}function baidudatatemp(t,e,o,n){var i="blog",a='<span class="type"><img src="'+blogStaticHost+'dist/components/img/blogType.png" alt=""><span class="tip">博客</span></span>';t.linkUrl.indexOf("download.csdn.net")>-1?(i="download",a='<span class="type"><img src="'+blogStaticHost+'dist/components/img/downloadType.png" alt=""><span class="tip">下载</span></span>'):t.linkUrl.indexOf("edu.csdn.net")>-1&&(i="edu",a='<span class="type"><img src="'+blogStaticHost+'dist/components/img/eduType.png" alt=""><span class="tip">课程</span></span>');var s=highlight.map(function(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")});10==setBaiduJsCount&&(a=""),s="("+s.join("|")+")";var c=new RegExp(s,"gi"),r=/<[^>]*>/g,l=t["abstract"].replace(r,"").replace(c,"<em>$1</em>"),d=t.dispTime.replace(/^(\d*)-/,""),m=t.linkUrl,p=t.title.replace(r,"").replace(c,"<em>$1</em>"),u=m.split("?")[0].split("/").splice(-1)[0],h='"extra":"{\\"utm_medium\\":\\"distribute.'+baiduSearchChannel+".none-task-"+i+"-2~default~baidujs_"+baiduSearchType+"~default-"+e+"-"+u+"-blog-"+articleId+baiduSearchIdentification+'\\",\\"dist_request_id\\":\\"'+distRequestId+'\\",\\"parent_index\\":\\"'+o+'\\"}",',f='<div class="recommend-item-box baiduSearch clearfix" data-url="'+m+'" data-type="'+i+'" data-report-view=\'{"mod":"popu_387",'+h+'"spm":"1001.2101.3001.4242.'+n+'","dest":"'+m+'","strategy":"2~default~baidujs_'+baiduSearchType+'~default","ab":"new","index":"'+e+'"}\'>\t                <div class="content-box">\t\t                <div class="content-blog display-flex">\t\t\t                  <div class="title-box">'+a+'\t\t\t\t                  <a class="tit" href="'+m+'" target="_blank" data-report-click=\'{"mod":"popu_387",'+h+'"spm":"1001.2101.3001.4242.'+n+'","dest":"'+m+'","strategy":"2~default~baidujs_'+baiduSearchType+'~default","ab":"new","index":"'+e+'"}\' data-report-query="utm_medium=distribute.'+baiduSearchChannel+".none-task-"+i+"-2~default~baidujs_"+baiduSearchType+"~default-"+e+"-"+u+"-blog-"+articleId+baiduSearchIdentification+"&spm=1001.2101.3001.4242."+n+'">\t\t\t\t\t                <div class="left ellipsis-online ellipsis-online-1">'+p+'</div>\t\t\t\t                  </a>\t\t\t                  </div>                    <div class="info-box display-flex">                      <div class="info display-flex">                        <span class="info-block">'+d+'</span>                      </div>                    </div>                  </div>                  <div class="desc-box">                    <a href="'+m+'" target="_blank" data-report-click=\'{"mod":"popu_387",'+h+'"spm":"1001.2101.3001.4242.'+n+'","dest":"'+m+'","strategy":"2~default~baidujs_'+baiduSearchType+'~default","ab":"new","index":"'+e+'"}\' data-report-query="utm_medium=distribute.'+baiduSearchChannel+".none-task-"+i+"-2~default~baidujs_"+baiduSearchType+"~default-"+e+"-"+u+"-blog-"+articleId+baiduSearchIdentification+"&spm=1001.2101.3001.4242."+n+'">                      <div class="desc ellipsis-online ellipsis-online-1">'+l+"</div>                    </a>                  </div>                </div>              </div>";return f}function showDownRecommend(t){if(isGitCodeBlog)return!1;var e=highlight.map(function(t){return t.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")});e="("+e.join("|")+")";var o=new RegExp(e,"gi"),n=/<[^>]*>/g,i=t.linkUrl,a=t.title.replace(n,"").replace(o,"<em>$1</em>"),s='<div class="recommend_down">  相关资源：<a class="recommend_down_link" href="'+i+'?spm=1001.2101.3001.5697" target="_blank" data-report-view=\'{"mod":"1612247418_001","spm":"1001.2101.3001.5697","dest":"'+i+'","extend1":"pc"}\' data-report-click=\'{"mod":"1612247418_001","spm":"1001.2101.3001.5697","dest":"'+i+'","extend1":"pc"}\'>'+a+"</a>  </div>";$("#recommendDown").html(s)}function showResult(t,e){var o=[],n=recommendRegularDomainArr||["blog.csdn.net/.+/article/details/","download.csdn.net/download/","edu.csdn.net/course/detail/"],i=new RegExp(n.join("|"));if(!t||t.length<=0)return!1;if(t&&t.length>0){for(var a=0;a<t.length;a++)t[a].noRepeatTit=t[a].title.split("_")[0].replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、<em></em>&quot;]/g,"").replace(/\s/g,"").toLowerCase();for(var a=0;a<t.length;a++)for(var s=a+1;s<t.length;s++)t[a].noRepeatTit==t[s].noRepeatTit&&(t.splice(s,1),s--)}if(t&&t.length>0)for(var c=[],a=0;a<t.length;a++)if(t[a].linkUrl.split("?")[0].toLowerCase().indexOf(curentUrl.split("://")[2==curentUrl.split("://").length?1:0].toLowerCase())===-1&&i.test(t[a].linkUrl)&&""!==t[a].title){var r=t[a].linkUrl.split("?")[0].split("//"),l=2==r.length?r[1]:r[0],d="";l=l.replace(/\/$/,"");var m=t[a].linkUrl.split("?")[0].replace("/",""),p=t[a].title.split("_")[0].replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、<em></em>&quot;]/g,"").replace(/\s/g,"").toLowerCase();if(2==l.split("article/details/").length){var u=l.split("article/details/")[1];u=u.replace("/",""),d=u,u&&c.indexOf(u)===-1&&(c.push(u),m="article/details/"+u)}$.inArray(l.toLowerCase(),getRecommendListUrl()[0])==-1&&$.inArray(m.toLowerCase(),getRecommendListUrl()[1])==-1&&$.inArray(d,getRecommendListUrl()[1])==-1&&a<10&&p!==articleTitleContent&&articleId!=d&&e!==t[a].linkUrl&&o.push(t[a])}var h=$(".insert-baidu-box").children().not("script").not("dl"),f="",g=0,v=0,w=1;o=o.slice(0,setBaiduJsCount),(!baiduCount||baiduCount<=0)&&(baiduCount=2);for(var a=isBaiduPre?0:baiduCount;a<h.length;a+=baiduCount){for(var s=a;s<a+baiduCount;s++)if(isBaiduPre?o[s]:o[s-baiduCount]){var b=isBaiduPre?2*baiduCount*v-1>0?2*baiduCount*v:0:2*baiduCount*v+baiduCount,x=$(".insert-baidu-box .recommend-item-box").eq(b),y=0;$(x).children().length&&$(x).find("a.tit").attr("data-report-click")&&(y=JSON.parse($(x).find("a.tit").attr("data-report-click")).index),f+=baidudatatemp(isBaiduPre?o[s]:o[s-baiduCount],isBaiduPre?a+s:s+v*baiduCount,y,w),w+=1}$(h[a]).length&&($(h[a]).after(f),v+=1),f="",g=v*baiduCount}if(g<o.length){var k=(isBaiduPre?2*v-1:2*v)*baiduCount+(h.length-1)%baiduCount;if(h.length&&$(h[h.length-1]).find("a.tit")&&$(h[h.length-1]).find("a.tit").attr("data-report-click"))var y=JSON.parse($(h[h.length-1]).find("a.tit").attr("data-report-click")).index;else var y=0;for(var a=g;a<o.length;a++)$(".insert-baidu-box").append(baidudatatemp(o[a],k,y,w)),w+=1,k+=1,g+=1}}function getQueryIdx(){var t=$("div[class^='recommend-item-box type_'],div.recommend-item-box.baiduSearch");$(t).each(function(t,e){var o=$(e).find("a"),n=o.attr("data-report-query");n+="&utm_relevant_index="+(t+1),o.attr("data-report-query",n)})}function reportTop10(){var t=Array.from(document.querySelectorAll(".recommend-box .recommend-item-box.clearfix")).slice(0,10),e=t.map(function(t){return t.dataset.url}),o={spm:"1018.2226.3001.8548",extra:{urlList:e}};window.csdn&&window.csdn.report&&window.csdn.report.reportView(o)}var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ZeroClipboard={version:"1.0.7",clients:{},moviePath:"https://csdnimg.cn/public/highlighter/ZeroClipboard.swf",nextId:1,$:function(t){return"string"==typeof t&&(t=document.getElementById(t)),t.hide=function(){this.style.display="none"},t.show=function(){this.style.display="block"},t.addClass=function(t){this.removeClass(t),this.className+=" "+t},t.removeClass=function(t){for(var e=this.className.split(/\s+/),o=-1,n=0;n<e.length;n++)e[n]==t&&(o=n,n=e.length);return o>-1&&(e.splice(o,1),this.className=e.join(" ")),this},t.hasClass=function(t){return!!this.className.match(new RegExp("\\s*"+t+"\\s*"))},t},setMoviePath:function(t){this.moviePath=t},dispatch:function(t,e,o){var n=this.clients[t];n&&n.receiveEvent(e,o)},register:function(t,e){this.clients[t]=e},getDOMObjectPosition:function(t,e){for(var o={left:0,top:0,width:t.width?t.width:t.offsetWidth,height:t.height?t.height:t.offsetHeight};t&&t!=e;)o.left+=t.offsetLeft,o.top+=t.offsetTop,t=t.offsetParent;return o},Client:function(t){this.handlers={},this.id=ZeroClipboard.nextId++,this.movieId="ZeroClipboardMovie_"+this.id,ZeroClipboard.register(this.id,this),t&&this.glue(t)}};ZeroClipboard.Client.prototype={id:0,ready:!1,movie:null,clipText:"",handCursorEnabled:!0,cssEffects:!0,handlers:null,glue:function(t,e,o){this.domElement=ZeroClipboard.$(t);var n=99;this.domElement.style.zIndex&&(n=parseInt(this.domElement.style.zIndex,10)+1),"string"==typeof e?e=ZeroClipboard.$(e):"undefined"==typeof e&&(e=document.getElementsByTagName("body")[0]);var i=ZeroClipboard.getDOMObjectPosition(this.domElement,e);this.div=document.createElement("div");var a=this.div.style;if(a.position="absolute",a.left=""+i.left+"px",a.top=""+i.top+"px",a.width=""+i.width+"px",a.height=""+i.height+"px",a.zIndex=n,"object"==("undefined"==typeof o?"undefined":_typeof(o)))for(addedStyle in o)a[addedStyle]=o[addedStyle];e.appendChild(this.div),this.div.innerHTML=this.getHTML(i.width,i.height)},getHTML:function(t,e){var o="",n="id="+this.id+"&width="+t+"&height="+e;if(navigator.userAgent.match(/MSIE/)){var i=location.href.match(/^https/i)?"https://":"http://";o+='<object classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000" codebase="'+i+'download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=9,0,0,0" width="'+t+'" height="'+e+'" id="'+this.movieId+'" align="middle"><param name="allowScriptAccess" value="always" /><param name="allowFullScreen" value="false" /><param name="movie" value="'+ZeroClipboard.moviePath+'" /><param name="loop" value="false" /><param name="menu" value="false" /><param name="quality" value="best" /><param name="bgcolor" value="#ffffff" /><param name="flashvars" value="'+n+'"/><param name="wmode" value="transparent"/></object>'}else o+='<embed id="'+this.movieId+'" src="'+ZeroClipboard.moviePath+'" loop="false" menu="false" quality="best" bgcolor="#ffffff" width="'+t+'" height="'+e+'" name="'+this.movieId+'" align="middle" allowScriptAccess="always" allowFullScreen="false" type="application/x-shockwave-flash" pluginspage="http://www.macromedia.com/go/getflashplayer" flashvars="'+n+'" wmode="transparent" />';return o},hide:function(){this.div&&(this.div.style.left="-2000px")},show:function(){this.reposition()},destroy:function(){if(this.domElement&&this.div){this.hide(),this.div.innerHTML="";var t=document.getElementsByTagName("body")[0];try{t.removeChild(this.div)}catch(e){}this.domElement=null,this.div=null}},reposition:function(t){if(t&&(this.domElement=ZeroClipboard.$(t),this.domElement||this.hide()),this.domElement&&this.div){var e=ZeroClipboard.getDOMObjectPosition(this.domElement),o=this.div.style;o.left=""+e.left+"px",o.top=""+e.top+"px"}},setText:function(t){this.clipText=t,this.ready&&this.movie.setText(t)},addEventListener:function(t,e){t=t.toString().toLowerCase().replace(/^on/,""),this.handlers[t]||(this.handlers[t]=[]),this.handlers[t].push(e)},setHandCursor:function(t){this.handCursorEnabled=t,this.ready&&this.movie.setHandCursor(t)},setCSSEffects:function(t){this.cssEffects=!!t},receiveEvent:function(t,e){switch(t=t.toString().toLowerCase().replace(/^on/,"")){case"load":if(this.movie=document.getElementById(this.movieId),!this.movie){var o=this;return void setTimeout(function(){o.receiveEvent("load",null)},1)}if(!this.ready&&navigator.userAgent.match(/Firefox/)&&navigator.userAgent.match(/Windows/)){var o=this;return setTimeout(function(){o.receiveEvent("load",null)},100),void(this.ready=!0)}this.ready=!0,this.movie.setText(this.clipText),this.movie.setHandCursor(this.handCursorEnabled);break;case"mouseover":this.domElement&&this.cssEffects&&(this.domElement.addClass("hover"),this.recoverActive&&this.domElement.addClass("active"));break;case"mouseout":this.domElement&&this.cssEffects&&(this.recoverActive=!1,this.domElement.hasClass("active")&&(this.domElement.removeClass("active"),this.recoverActive=!0),this.domElement.removeClass("hover"));break;case"mousedown":this.domElement&&this.cssEffects&&this.domElement.addClass("active");break;case"mouseup":this.domElement&&this.cssEffects&&(this.domElement.removeClass("active"),this.recoverActive=!1)}if(this.handlers[t])for(var n=0,i=this.handlers[t].length;n<i;n++){var a=this.handlers[t][n];"function"==typeof a?a(this,e):"object"==("undefined"==typeof a?"undefined":_typeof(a))&&2==a.length?a[0][a[1]](this,e):"string"==typeof a&&window[a](this,e)}}},$(document).ready(function(){$(".article_content pre").each(function(){var t=$(this);if(void 0!=t.attr("class")){if(t.attr("class").indexOf("brush:")!=-1){var e=t.attr("class").split(";")[0].split(":")[1];t.attr("name","code"),t.attr("class",e)}t.attr("class")&&t.attr("name","code")}}),$(".article_content textarea[name=code]").each(function(){var t=$(this);t.attr("class").indexOf(":")!=-1&&t.attr("class",t.attr("class").split(":")[0])}),window.clipboardData||setTimeout("setCopyBtn()",500)}),function(t){function e(t,e){var o=e-t+1;return Math.floor(Math.random()*o+t)}t.fn.extend({selection:function(){var e="",o=this[0];if(document.selection){var n=document.selection.createRange();e=n.text}else if("number"==typeof o.selectionStart){var i=o.selectionStart,a=o.selectionEnd;i!=a&&(e=o.value.substring(i,a))}return t.trim(e)},parseHtml:function(e){var o=this[0],n=t(this).val();if(document.selection){var i=document.selection.createRange();i.text?i.text=e:t(this).val(n+e)}else if("number"==typeof o.selectionStart){var a=o.selectionStart,s=o.selectionEnd,c=n.substring(0,a),r=n.substring(s);t(this).val(c+e+r)}else t(this).val(n+e);o.selectionStart=o.selectionEnd=t(this).val().length,o.focus()}});var o=t("div.pulllog-box");o.find("button.btn-close").click(function(){o.remove()}),o.find(".pulllog-login").click(function(t){getCookie("UserName")?window.location.reload():(window.csdn.loginBox.show(),t.preventDefault())});var n=function(e){function o(){i.indexOf("windows ")>-1?t("body").css({overflow:"auto","margin-left":"0"}):t("body").css({overflow:"auto"}),t(".imgViewDom").fadeOut(300)}e=e?e:"#content_views";var n=t(e+" img:not(.contentImg-no-view)"),i=navigator.userAgent.toLowerCase();if(0===n.length)return!1;for(var a="",s=0;s<n.length;s++){var c=n[s];a+='<div class="swiper-slide"><img src="'+c.src+'"/></div>'}if(0===t(".imgViewDom").length){t("body").append('<div class="imgViewDom">        <div class="swiper">          <a class= "close-btn">            <img src="'+blogStaticHost+'dist/pc/img/quoteClose1White.png">          </a>          <div class="swiper-wrapper">'+a+'</div>          <div class="swiper-button-prev"></div>          <div class="swiper-button-next"></div>        </div>      </div>');var r=new Swiper(".imgViewDom .swiper",{autoplay:!1,loop:!1,mousewheel:!0,keyboard:{enabled:!0,pageUpDown:!1},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}});t(".swiper-wrapper").on("click",function(){o()}),t(".imgViewDom a.close-btn").on("click",function(){o()}),t(document).keyup(function(e){if("none"!==t(".imgViewDom").css("display")){var n=e||event,i=n.which||n.keyCode;27==i&&o()}}),t(".imgViewDom").click(function(e){0===t(".imgViewDom>.swiper").has(e.target).length&&o()})}return r};window.CSDNviewImg=n,window.csdn=window.csdn||{},window.csdn.random_num=e}(jQuery),$(function(){function t(t){if(l&&c){var e=$(window).height(),o=$(document).scrollTop(),i=t.offset().top,a=t.height();i<o+e&&i+a>o&&(n(t),c=!1)}}function e(t){return'<a target="_blank" href="'+t.clickUrl+'">                    <div class="ad-top">                        <div class="ad-top-tit">                            <span class="ad-top-topic">'+t.title+'</span>                            <span class="ad-top-type">'+t.titleRight+'</span>                            <span class="ad-top-num">'+t.topCenter+'</span>                        </div>                        <div class="ad-top-tag">                            <span class="ad-top-count">'+t.topRightNumber+'</span>                            <span class="ad-top-text">'+t.topRight+'</span>                        </div>                    </div>                    <div class="ad-con">                        <div class="ad-con-tit">'+t.introTitle+'</div>                        <div class="ad-con-box">                            <span class="ad-con-txt">'+t.intro+'</span>                            <span class="ad-con-go">'+t.buttonText+"</span>                        </div>                    </div>                </a>"}function o(o){if(o)var n={authorUserName:username,contentKey:articleTitle,pageSpm:JSON.parse(articleReport).spm,positions:542};else var n={authorUserName:username,contentKey:articleTitle,pageSpm:JSON.parse(articleReport).spm,positions:544};$.ajax({type:"GET",url:"https://kunpeng.csdn.net/ad/json/list",dataType:"json",data:n,xhrFields:{withCredentials:!0},success:function(n){200==n.code&&n.data.length?(l="articleContentAd",o?$("#content_views").append("<div id="+l+">"+e(n.data[0])+"</div>"):$(i[Math.floor(a/2)]).after("<div id="+l+">"+e(n.data[0])+"</div>"),r=n.data[0].exposureUrl,t($("#"+l))):l=""},error:function(t){l=""}})}function n(t){t.append('<img src="'+r+'" style="display:none;width:0px;height:0px" alt="">')}var i=$("#content_views").children(),a=i.length,s=$("#content_views").height(),c=!0,r="",l="";if(4===articleSource&&a>8&&s>300){var d=(null!=(_ref1=/(; )?(uuid_tt_dd|_javaeye_cookie_id_)=([^;]+)/.exec(window.document.cookie))?_ref1[3]:void 0)||"",m=d?d.substring(d.length-6)%16:0;o(m<=7)}$(document).on("scroll",function(){t($("#"+l))}),a>contentViewsCount&&s>contentViewsHeight}),$(function(){function t(t,e,o){clearTimeout(d),document.selection?l=document.selection.createRange().text:window.getSelection()&&(l=window.getSelection()),l=l.toString().replace(/\n/g,"").trim();var a=!0;if(""!=l){(l.length<=2||$.isNumeric(l)||null!==n(l)||null!==i(l))&&(a=!1);var s="//so.csdn.net/so/search?spm=1001.2101.3001.6607&from=pc_blog_select&q="+encodeURIComponent(l.toString()),c='<div id="articleSearchTip" class="article-search-tip">          '+(a?'<a class="article-href article-search" href="'+s+'" target="_blank" ><img src="'+blogStaticHost+'dist/pc/img/newarticleSearchWhite.png"><span class="article-text">搜索</span></a>':"")+'          <a class="article-href article-aichat"><img src="'+blogStaticHost+'dist/pc/img/newarticleChatWhite.png"><span class="article-text">AI提问</span></a>          <a data-report-click=\'{"spm":"3001.6773"}\' class="article-href article-comment" href="javascript:;" data-type="comment"><img src="'+blogStaticHost+'dist/pc/img/newarticleComment1White.png"><span class="article-text">评论</span></a>          <a class="article-href cnote" href="javascript:;" data-type="cnote"><img src="'+blogStaticHost+'dist/pc/img/newcNoteWhite.png"><span class="article-text">笔记</span></a>          </div > ';$("body").append(c),$(document).off("click","#articleSearchTip .article-search"),$(document).on("click","#articleSearchTip .article-search",function(){window.csdn&&window.csdn.report&&window.csdn.report.reportClick({spm:"3001.6607",extra:JSON.stringify({searchword:l.toString()})})}),window.csdn&&window.csdn.report&&window.csdn.report.reportView({spm:"3001.10112",extra:JSON.stringify({searchword:l.toString()})}),$(document).off("click","#articleSearchTip .article-aichat"),$(document).on("click","#articleSearchTip .article-aichat",function(){window.csdn&&window.csdn.report&&window.csdn.report.reportClick({spm:"3001.10112",extra:JSON.stringify({searchword:l.toString()})}),setTimeout(function(){window.chatModelObj.setExtraData({utm_source:"vip_chatgpt_common_blog_pc_detail"}),window.chatModelObj.openChat(!0),l.toString().length>0&&window.chatModelObj.sendMessage(l.toString())},200)}),window.csdn.report&&"function"==typeof window.csdn.report.reportView&&(window.csdn.report.reportView({spm:"3001.6607",extra:JSON.stringify({searchword:l.toString()})}),window.csdn.report.reportView({spm:"3001.6773"})),$("#articleSearchTip").css({top:o.pageY-e-8,left:t})}}function e(t){var e='<div class="git-hub-box" data-report-view=\'{"spm":"3001.9232"}\' id="git-hub-box-bt">        <div class="git-hub-top">          <div class="git-hub-info">            <p class="info"> <img src="https://gitcode.net/uploads/-/system/appearance/favicon/1/icon.png" alt=""> <span class="text" title="'+t.format_path+'">'+t.format_path+'</span></p>            <p class="next">已加入 <span class="tag">GitHub加速计划</span> </p>          </div>          <div class="git-hub-btn">            <p id="git-hub-link" data-report-click=\'{"spm":"3001.9234"}\'>查看项目</p>          </div>        </div>        <div class="git-hub-bottom">          <div class="git-hub-desc">            '+t.description+'          </div>          <div class="git-hub-newnps">            <span>'+t.language+'</span>            <p><img src="https://img-home.csdnimg.cn/images/20230307102412.png" alt=""><span>'+t.star_count+'</span></p>            <p><img src="https://img-home.csdnimg.cn/images/20230307102356.png" alt=""><span>'+t.fork_count+"</span></p>            <span>"+t.updated_at_format+"更新</span>          </div>        </div>      </div>";return e}function o(t,o,n){$.ajax({url:"https://web-api.gitcode.com/api/v1/search/nauth/project/card",type:"get",data:{url:t},xhrFields:{crossDomain:!0,withCredentials:!0},success:function(i){200==i.status&&i.data&&!$.isEmptyObject(i.data)?($("body").append(e(i.data)),"none"===$(".git-hub-box").css("display")&&$(".git-hub-box").show(),m=t,$(".git-hub-box").css({top:o+"px",left:n+"px"}),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportView({spm:"3001.9232"}),$(".git-hub-box").click(function(t){window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"3001.9232"});var e="https://link.csdn.net?from_id="+articleId+"&target="+encodeURIComponent(i.data.link);window.open(e)}),$("#git-hub-download").click(function(t){window.open(i.data.download_url)}),$(".git-hub-box").mouseleave(function(){$(".git-hub-box").hide(),u=!1}),$(".git-hub-box").mouseenter(function(){u=!0})):m=""},error:function(t){m=""}})}function n(t){var e="(https?|http|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]";return s=t.match(e),s}function i(t){var e=/[Γ∫∞​∀∈∋N∏∑ϕ⎡⎤⇒⇏⟹⇑⇓⇕⨿%†‡…⋯◯△▽⪇≦≰≨≯⋍≂≅≆∮]/g;return s=t.match(e),s}function a(t){var e=$("#commentQuote"),o=$("#pcCommentSideBox"),n='<span class="comment-quote-bar"></span>'+t+'<span data-report-click=\'{"spm":"3001.6774"}\' class="comment-quote-close"><img src="'+blogStaticHost+"dist/pc/img/quoteClose1"+skinStatus+'.png"></span>';e.length?e.html(n):(o.prepend('<div id="commentQuote" class="comment-quote">'+n+"</div>"),$("#commentSideBoxshadow").fadeIn()),$("input.bt-comment-show").trigger("click")}var c=0,r=0,l="",d=null;$("#content_views").mouseup(function(e){$(".article-search-tip").remove();var o=e.pageY-r,n=Math.min(e.pageX,c);return Math.abs(n)<=5&&Math.abs(o)<=5?(clearTimeout(d),!1):(o=o>0?o:0,clearTimeout(d),void(d=setTimeout(function(){t(n,o,e)},1e3)))}).mousedown(function(t){c=t.pageX,r=t.pageY,l="",$(".git-hub-box").hide(),$("#articleSearchTip").remove()});var m="",p=0,u=!1,h=null;$(document).on("mouseenter mouseleave","#article_content a",function(t){if("mouseenter"==t.type){var e=$(this).offset().top+20,n=$(this).offset().left;$(this).offset().top-p>246&&(e=$(this).offset().top-226),clearTimeout(h),$(this).attr("href").indexOf("github.com")==-1&&$(this).attr("href").indexOf("gitcode.com")==-1||(m!==$(this).attr("href")?($(".git-hub-box").length>=1&&$(".git-hub-box").remove(),o($(this).attr("href"),e,n)):($(".git-hub-box").show(),$(".git-hub-box").css({top:e+"px",left:n+"px"})))}else"mouseleave"==t.type&&(h=setTimeout(function(){u||$(".git-hub-box").hide()},300))}),$(window).scroll(function(){p=$(window).scrollTop()}),$(document).on("click",".article-href",function(t){var e=$(this).data("type");if(void 0!==e)switch(e){case"comment":if(getCookie("UserName"))if(l.toString().length){var o=$("#csdn-toolbar").height(),n=$(document).scrollTop();n<52&&(o=2*o),a(l.toString().replace(/</g,"&lt;").replace(/>/g,"&gt;")),t.stopPropagation?t.stopPropagation():t.cancelBubble=!0}else $("#articleSearchTip").remove(),showToast({text:"选中内容为空，请重新选中",bottom:"10%",zindex:9e3,speed:500,time:1500});else window.csdn.loginBox.show();break;case"cnote":if(getCookie("UserName"))if(l.toString().length){l=l.toString().replace(/</g,"&lt;").replace(/>/g,"&gt;");var i=new Date,s=i.getMonth()+1,c=i.getDate(),r=i.getFullYear()+"年"+s+"月"+c+"日",d={noteTabName:"博客笔记",topicTitle:"博客摘录「 "+articleTitle+"」 "+r,content:articleDetailUrl?"文章来源：<a href='"+articleDetailUrl+"'>"+articleDetailUrl+"</a><br/>":"",source:{hostname:"blog.csdn.net",url:articleDetailUrl,title:articleTitle,selection:l}};$.ajax({url:"https://note-ccloud.csdn.net/v1/community/content/sendNote",type:"post",contentType:"application/json",dataType:"json",data:JSON.stringify(d),xhrFields:{withCredentials:!0},success:function(t){},error:function(t){}});var m=articleTitle;articleTitle&&articleTitle.length>=80&&(m=articleTitle.slice(0,77)+"..."),blogApiAxios({article_id:"",title:"博客摘录「 "+m+"」"+r,description:"",content:"<p>"+l+"</p>\n",type:"original",status:2,read_type:"public",authorized_status:!1,check_original:!1,source:"pc_postedit",not_auto_saved:1,cover_type:1,vote_id:0,scheduled_time:0,is_new:1,tags:"笔记"}),t.stopPropagation?t.stopPropagation():t.cancelBubble=!0}else $("#articleSearchTip").remove(),showToast({text:"选中内容为空，请重新选中",bottom:"10%",zindex:9e3,speed:500,time:1500});else window.csdn.loginBox.show()}}),$(document).on("click",".comment-quote-close",function(t){var e=$("#commentQuote");e.animate({height:0},300,function(){e.remove()})})}),window.apiOpenEditor=function(t){showToast({text:"请稍后再试",bottom:"10%",zindex:9e3,speed:500,time:2e3})},window.chatModelObj=new ChatModel({model:"chatdoc",title:"AI学习助手",keycode:"blog",darkMode:"Black"===skinStatus,simpleMode:!0,width:"65%",features:{sug:!1,roles:!1,tools:!1,collapseBtn:!1,skillTree:!1,modelControl:!1}}),function(){function t(t){o.hasClass("no-login")||($("div.article_content").removeAttr("style"),0==$(".column-mask").length&&$(".hide-article-box").hide(),o.hasClass("fans_read_more")&&($("#btnAttent").hasClass("attented")||t.originalEvent&&($(".tool-attend").trigger("click"),window.csdn.report.reportClick({mod:"popu_376",spm:"1001.2101.3001.4248",extend1:"粉丝登录阅读更多"}))))}function e(e,o){var n=($(window).height(),$("div.article_content")),i=n.height();$("#btn-readmore").attr("height",o),i>2e3?(n.css({height:"2000px",overflow:"hidden"}),e.click(t)):e.parent().hide()}0!=$(".btn-readmore").length&&$("#content_views").find("pre").each(function(t,e){e=$(e),e.addClass("set-code-show")});var o=$(".btn-readmore");$(".vipmaskclassname").length<=0&&(o.length>0||$(".vip-mask").length>0?currentUserName?e(o,3):e(o,2):0==$(".column-mask").length&&$(".hide-article-box").addClass("hide-article-style"));var n=window.location.hash,i=RegExp(/\#/);n.match(i)&&($(".btn-readmore").parent().hide(),$("div.article_content").removeAttr("style")),window.csdn=window.csdn?window.csdn:{},window.csdn.clearReadMoreBtn=t}(),$(function(){function t(){var t=$(".recommend-ask-box").find("a.btn-link-toask").data("href");t=t?t:$(".recommend-ask-box").find(".btn-gochat").data("href");var e=$(".recommend-ask-box").find("#txtChat").val();e=e&&e.length?e.trim():$(".recommend-ask-box").find("a.btn-link-toask").data("defaultvalue"),e=e?e:$(".recommend-ask-box").find("#txtChat").data("defaultvalue");var o=document.createElement("a");o.style.display="none",o.href=t+"&query="+encodeURIComponent(e),o.target="_blank",document.body.appendChild(o),o.click(),document.body.removeChild(o)}$(".recommend-ask-box").find("#txtChat").on("keyup",function(e){var o=e.which||e.keyCode;13===o&&t()}),$(".recommend-ask-box").find(".btn-gochat").on("click",function(){t()}),$(".recommend-ask-box").find(".btn-link-toask").on("click",function(){t()})}),$(function(){function t(t){t.list&&t.list.length&&($.each(t.list,function(t,e){var o="",n="";articleId==e.articleId&&(o="active"),e.preView&&(n='<span class="try-read">试读</span>'),i.columnlistStr+='<div class="columnlist-list-item" title="'+e.title+'"><a href="'+e.url+'" class="columnlist-list-href" data-report-view=\'{"spm":"3001.6326"}\' data-report-click=\'{"spm":"3001.6326"}\'><span class="text '+o+'">'+e.title+"</span>"+n+"</a></div>"}),t.total-t.page*t.size>0?(i.columnPage+=1,i.columnlistStr+='<p class="look-more-article active">查看更多<img class="look-more-img" src="'+blogStaticHost+"dist/pc/img/newLookMore1"+skinStatus+'.png" title="查看更多"></p>'):i.columnlistStr+='<p class="look-more-article">暂无更多内容</p>',$(document).find(i.columnlistHeight).append(i.columnlistStr),i.columnlistStr="")}function e(t,e,o,n){$.ajax({type:"GET",url:blogUrl+t,dataType:"json",xhrFields:{withCredentials:!0},data:e,success:function(t){200==t.code&&o(t.data)},error:function(t){}})}function o(t,e){if(getCookie("UserName")){var o=blogUrl+"phoenix/web/v1/subscribe/subscribe";$.ajax({url:o,type:"post",dataType:"json",data:t,xhrFields:{withCredentials:!0},success:function(t){200===t.code&&(t.data.status&&showToast({text:t.data.msg,bottom:"10%",zindex:9003,speed:500,time:1500}),e.removeClass("articleColumnFreeBt").html("已订阅"))}})}else window.csdn.loginBox.show({spm:"1001.2101.3001.8607"})}function n(t,e,o){if($(document).find(t).length){var n=$(document).find(t).outerHeight(!0),i=$(document).find(e).outerHeight(!0),a=$(document).find(o).outerHeight(!0);$(document).find(o).css({height:n-i+a})}}var i={columnlistBox:"#columnlistBox",columnlistClose:".columnlist-close",columnlistContent:".columnlist-content-box",columnlistHeight:".columnlist-list-box",columnlistShow:$(".bt-columnlist-show"),columnlistShadow:$(".directory-boxshadow"),columnlistItem:$(".column-group"),columnlistUrl:"phoenix/web/v1/column/article-list",columnId:"",columnPage:1,columnlistStr:""};i.columnlistItem.on("click",function(t){$(this).children().length>1&&!$(t.target).is(".item-target")&&($(this).hasClass("open")?($(this).removeClass("open"),$(this).find(".dec").show()):($(this).addClass("open"),$(this).find(".dec").hide()))});var a="";studyLearnWord&&(a='<span class="column_studyvip_free-active">'+studyLearnWord+"</span>"),i.columnlistShow.on("click",function(o){i.columnId=$(this).data("id");var s="",c="",r="",l="",d="",m="";isOwner||($(this).data("free")?s=$(this).data("subscribe")?'<a class="columnlist-con-btn article-column-subscribe">已订阅</a>':'<a class="columnlist-con-btn columnlist-con-subscribe articleColumnFreeBt" data-id="'+$(this).data("id")+'">订阅专栏</a>':($(this).data("join")?$(this).data("studyvip")?$(this).data("subscribe")?s='<a class="columnlist-con-btn article-column-subscribe">已订阅</a>            <a class="column-studyvip-discount column-studyvip-pass" data-report-view=\'{"spm":"3001.6331"}\' data-report-click=\'{"spm":"3001.6331"}\'>8折续费 '+a+"</a>":(s='<a data-report-view=\'{"spm":"3001.6328","extend1":"会员已订阅"}\' data-report-click=\'{"spm":"3001.6328","extend1":"会员已订阅"}\' style="'+($(this).data("studysubscribe")?"display:inline-block":"display:none")+'" data-type="true" class="columnlist-con-btn article-column-subscribe" data-id="'+$(this).data("id")+'">              会员已订阅            </a>',
s+='<a data-report-view=\'{"spm":"3001.6328","extend1":"会员免费订"}\' data-report-click=\'{"spm":"3001.6328","extend1":"会员免费订"}\' style="'+($(this).data("studysubscribe")?"display:none":"display:inline-block")+'" data-type="false" class="column-studyvip-free studyvip-unsubscribe column-studyvip-ajax" data-id="'+$(this).data("id")+'">              <img class="column-studyvip-icon" src="'+blogStaticHost+'dist/components/img/studyVipIcon.png">会员免费订            </a>',s+=$(this).data("studysubscribe")?'<a class="column-studyvip-discount column-studyvip-pass" data-report-view=\'{"spm":"3001.6330"}\' data-report-click=\'{"spm":"3001.6330"}\'>8折续费 '+a+"</a>":'<a class="column-studyvip-discount column-studyvip-pass" data-report-view=\'{"spm":"3001.6329"}\' data-report-click=\'{"spm":"3001.6329"}\'>8折续费 '+a+"</a>"):s=$(this).data("subscribe")?'<a class="columnlist-con-btn article-column-subscribe">已订阅</a>            <a class="column-studyvip-free column-studyvip-pass" data-report-view=\'{"spm":"3001.6327"}\' data-report-click=\'{"spm":"3001.6327"}\'>              <img class="column-studyvip-icon" src="'+blogStaticHost+'dist/components/img/studyVipIcon.png">超级会员免费看              '+a+"            </a>":'<a class="columnlist-con-btn columnlist-con-subscribe articleColumnBt" data-id="'+$(this).data("id")+'" data-price="'+$(this).data("price")+'" data-oldprice="'+$(this).data("oldprice")+'" data-report-view=\'{"spm":"3001.6324"}\' data-report-click=\'{"spm":"3001.6324"}\'>订阅专栏</a>            <a class="column-studyvip-free column-studyvip-pass" data-report-view=\'{"spm":"3001.6325"}\' data-report-click=\'{"spm":"3001.6325"}\'>              <img class="column-studyvip-icon" src="'+blogStaticHost+'dist/components/img/studyVipIcon.png">超级会员免费看              '+a+"            </a>":s=$(this).data("subscribe")?'<a class="columnlist-con-btn article-column-subscribe">已订阅</a>':'<a class="columnlist-con-btn columnlist-con-subscribe articleColumnBt" data-id="'+$(this).data("id")+'" data-sum="'+$(this).data("sum")+'" data-people="'+$(this).data("people")+'" data-hotrank="'+$(this).data("hotrank")+'" data-price="'+$(this).data("price")+'" data-oldprice="'+$(this).data("oldprice")+'" data-report-view=\'{"spm":"3001.6324"}\' data-report-click=\'{"spm":"3001.6324"}\'>订阅专栏</a>',c='<div class="columnlist-con-price">¥'+$(this).data("price")+"</div>")),$(this).data("free")||$(this).data("status")||$(this).data("subscribe")||(s='<p class="article-column-off-shelf">已下架不支持订阅</p>',c=""),l='<div class="columnlist-head">                  <div class="columnlist-head-l">                      <a href="'+$(this).data("url")+'" target="_blank"><img class="columnlist-head-img" src="'+$(this).data("img")+'" alt=""></a>                  </div>                  <div class="columnlist-head-r">                      <div class="columnlist-head-tit"><a href="'+$(this).data("url")+'" target="_blank">'+$(this).data("title")+'</a></div>                      <div class="columnlist-head-con">                          <span>'+$(this).data("sum")+" 篇文章</span>                          <span>"+$(this).data("people")+" 订阅</span>                      </div>                  </div>                </div>",d='<div class="columnlist-con">                  <div class="columnlist-con-autor">作者：'+nickName+"</div>"+c+'<div class="columnlist-con-bt">                      <a class="columnlist-con-btn columnlist-con-look" target="_blank" href="'+$(this).data("url")+'" data-report-view=\'{"spm":"3001.6323"}\' data-report-click=\'{"spm":"3001.6323"}\'>查看详情</a>'+s+"</div>                </div>",m='<div class="columnlist-list">                <div class="columnlist-list-tit">专栏目录</div>                <div class="columnlist-list-box"></div></div>',r='<div class="columnlist-box" id="columnlistBox">                <div class="columnlist-content-box">                <div class="columnlist-content">'+l+d+m+'</div>                <div class="columnlist-close">                    <img src="'+blogStaticHost+"dist/pc/img/nerClose"+skinStatus+'.png" alt="">                </div>                </div>              </div>',$("body").append(r),i.columnlistShadow.fadeIn(function(){$("html,body").addClass("forbidden-htmlbody-scroll"),$(document).find(i.columnlistBox).addClass("open"),n(i.columnlistBox,i.columnlistContent,i.columnlistHeight)}),e(i.columnlistUrl,{columnId:i.columnId,page:i.columnPage},t,i.columnlistBox);var p=o||window.event;p.stopPropagation?p.stopPropagation():p.cancelBubble=!0}),i.columnlistShadow.click(function(){$(i.columnlistClose).trigger("click")}),$(document).on("click",i.columnlistClose,function(){$(document).find(i.columnlistBox).removeClass("open"),i.columnlistShadow.fadeOut(function(){$("html,body").removeClass("forbidden-htmlbody-scroll"),$(document).find(i.columnlistBox).remove(),i.columnPage=1,i.columnId="",i.columnlistStr=""})}),$(document).on("click","#columnlistBox .look-more-article",function(o){if($(this).hasClass("active")){$(this).fadeOut(function(){$(this).remove()}),e(i.columnlistUrl,{columnId:i.columnId,page:i.columnPage},t,i.columnlistBox);var n=o||window.event;n.stopPropagation?n.stopPropagation():n.cancelBubble=!0}}),$(document).on("click",".articleColumnFreeBt",function(t){var e=$(this).data("id");o({columnId:e},$(this));var n=t||window.event;n.stopPropagation?n.stopPropagation():n.cancelBubble=!0}),$(window).resize(function(){n(i.columnlistBox,i.columnlistContent,i.columnlistHeight)})}),$(function(){function t(){var t="",e="",o="",n="",i="",a="",r="";isOwner||(s.columnInfoDom.data("free")?t=s.columnInfoDom.data("subscribe")?'<a class="columnlist-con-btn article-column-subscribe">已订阅</a>':'<a class="columnlist-con-btn columnlist-con-subscribe articleColumnFreeBt" data-id="'+s.columnInfoDom.data("id")+'">订阅专栏</a>':s.columnInfoDom.data("join")?s.columnInfoDom.data("studyvip")?s.columnInfoDom.data("subscribe")?t='<a class="columnlist-con-btn article-column-subscribe">已订阅</a>            <a class="column-studyvip-discount column-studyvip-pass" data-report-view=\'{"spm":"1001.2101.3001.6331"}\' data-report-click=\'{"spm":"1001.2101.3001.6331"}\'>8折续费 '+c+"</a>":(t='<a data-report-view=\'{"spm":"1001.2101.3001.6328","extend1":"会员已订阅"}\' data-report-click=\'{"spm":"1001.2101.3001.6328","extend1":"会员已订阅"}\' style="'+(s.columnInfoDom.data("studysubscribe")?"display:inline-block":"display:none")+'" data-type="true" class="columnlist-con-btn article-column-subscribe" data-id="'+s.columnInfoDom.data("id")+'">              会员已订阅            </a>',t+='<a data-report-view=\'{"spm":"1001.2101.3001.6328","extend1":"会员免费订"}\' data-report-click=\'{"spm":"1001.2101.3001.6328","extend1":"会员免费订"}\' style="'+(s.columnInfoDom.data("studysubscribe")?"display:none":"display:inline-block")+'" data-type="false" class="column-studyvip-free studyvip-unsubscribe column-studyvip-ajax" data-id="'+s.columnInfoDom.data("id")+'">              <img class="column-studyvip-icon" src="'+blogStaticHost+'dist/components/img/studyVipIcon.png">会员免费订            </a>',t+=s.columnInfoDom.data("studysubscribe")?'<a class="column-studyvip-discount column-studyvip-pass" data-report-view=\'{"spm":"1001.2101.3001.6330"}\' data-report-click=\'{"spm":"1001.2101.3001.6330"}\'>8折续费 '+c+"</a>":'<a class="column-studyvip-discount column-studyvip-pass" data-report-view=\'{"spm":"1001.2101.3001.6329"}\' data-report-click=\'{"spm":"1001.2101.3001.6329"}\'>8折续费 '+c+"</a>"):t=s.columnInfoDom.data("subscribe")?'<a class="columnlist-con-btn article-column-subscribe">已订阅</a>            <a class="column-studyvip-free column-studyvip-pass" data-report-view=\'{"spm":"1001.2101.3001.6327"}\' data-report-click=\'{"spm":"1001.2101.3001.6327"}\'>              <img class="column-studyvip-icon" src="'+blogStaticHost+'dist/components/img/studyVipIcon.png">超级会员免费看              '+c+"            </a>":'<a class="columnlist-con-btn columnlist-con-subscribe articleColumnBt" data-id="'+s.columnInfoDom.data("id")+'" data-price="'+s.columnInfoDom.data("price")+'" data-oldprice="'+s.columnInfoDom.data("oldprice")+'" data-report-view=\'{"spm":"1001.2101.3001.6324"}\' data-report-click=\'{"spm":"1001.2101.3001.6324"}\'>订阅专栏</a>            <a class="column-studyvip-free column-studyvip-pass" data-report-view=\'{"spm":"1001.2101.3001.6325"}\' data-report-click=\'{"spm":"1001.2101.3001.6325"}\'>              <img class="column-studyvip-icon" src="'+blogStaticHost+'dist/components/img/studyVipIcon.png">超级会员免费看              '+c+"            </a>":t=s.columnInfoDom.data("subscribe")?'<a class="columnlist-con-btn article-column-subscribe">已订阅</a>':'<a class="columnlist-con-btn columnlist-con-subscribe articleColumnBt" data-id="'+s.columnInfoDom.data("id")+'" data-sum="'+s.columnInfoDom.data("sum")+'" data-people="'+s.columnInfoDom.data("people")+'" data-hotrank="'+s.columnInfoDom.data("hotrank")+'" data-price="'+s.columnInfoDom.data("price")+'" data-oldprice="'+s.columnInfoDom.data("oldprice")+'" data-report-view=\'{"spm":"1001.2101.3001.6324"}\' data-report-click=\'{"spm":"1001.2101.3001.6324"}\'>订阅专栏</a>');var l=s.columnInfoDom.data("hotrank");l&&0!==l&&(e='<span class="rank"><img src="'+blogStaticHost+'dist/pc/img/columnHotIcon2.png" alt="">该专栏为热销专栏榜&nbsp;第'+l+"名</span>");var d=s.columnInfoDom.data("description").toString();d&&d.length>0&&(o='<div class="columnlist-con-desc">'+d+"</div>"),s.columnInfoDom.data("free")||s.columnInfoDom.data("status")||s.columnInfoDom.data("subscribe")||(t='<p class="article-column-off-shelf">已下架不支持订阅</p>'),i='<div class="columnlist-head">                  <div class="columnlist-head-l">                      <a href="'+s.columnInfoDom.data("url")+'" target="_blank"><img class="columnlist-head-img" src="'+s.columnInfoDom.data("img")+'" alt=""></a>                  </div>                  <div class="columnlist-head-r">                      <div class="columnlist-head-tit"><a href="'+s.columnInfoDom.data("url")+'" target="_blank">'+s.columnInfoDom.data("title")+'</a></div>                      <div class="columnlist-head-con">                          <span>'+s.columnInfoDom.data("sum")+" 篇文章</span>                          <span>"+s.columnInfoDom.data("people")+" 订阅</span>                      </div>                  </div>                </div>",a='<div class="columnlist-con">'+e+o+'<div class="columnlist-con-bt">'+t+"</div>                </div>",r='<div class="columnlist-list">                <div class="columnlist-list-tit">专栏目录</div>                <div class="columnlist-list-box"></div></div>',n='<div class="columnlist-box" id="columnlistRightBox">                <div class="columnlist-content-box">                <div class="columnlist-content">'+i+a+r+"</div>                </div>              </div>",s.asideColumnInfoDom.append(n)}function e(t){t.list&&t.list.length&&($.each(t.list,function(t,e){var o="",n="";articleId==e.articleId&&(o="active"),e.preView&&(n='<span class="try-read">试读</span>'),s.columnlistStr+='<div class="columnlist-list-item" title="'+e.title+'"><a href="'+e.url+'" class="columnlist-list-href" data-report-query="spm=3001.10138.'+t+'" data-report-view=\'{"spm":"1001.2101.3001.10138.'+t+'"}\' data-report-click=\'{"spm":"1001.2101.3001.10138.'+t+'"}\' ><span class="text '+o+'">'+e.title+"</span>"+n+"</a></div>"}),t.total-t.page*t.size>0?(s.columnPage+=1,s.columnlistStr+='<p class="look-more-article active">查看更多<img class="look-more-img" src="'+blogStaticHost+"dist/pc/img/newLookMore1"+skinStatus+'.png" title="查看更多"></p>'):s.columnlistStr+='<p class="look-more-article">暂无更多内容</p>',s.asideColumnInfoDom.find(s.columnlistHeight).append(s.columnlistStr),s.columnlistStr="")}function o(){t(),n(s.columnlistUrl,{columnId:s.columnInfoDom.data("id"),page:s.columnPage},e),$(document).on("click",".column-info-container .look-more-article",function(t){if($(this).hasClass("active")){$(this).fadeOut(function(){$(this).remove()}),n(s.columnlistUrl,{columnId:s.columnInfoDom.data("id"),page:s.columnPage},e);var o=t||window.event;o.stopPropagation?o.stopPropagation():o.cancelBubble=!0}}),$("#recommend-right").prepend(s.asideColumnInfoDom),i(s.asideColumnInfoDom,s.columnlistContent,s.columnlistHeight)}function n(t,e,o){$.ajax({type:"GET",url:blogUrl+t,dataType:"json",xhrFields:{withCredentials:!0},data:e,success:function(t){200==t.code&&o(t.data)},error:function(t){}})}function i(t,e,o){if(t.length){var n=t.outerHeight(!0),i=$(t).find(e).outerHeight(!0),a=$(t).find(o).outerHeight(!0);$(t).find(o).css({height:n-i+a})}}function a(t){for(var e=document.cookie.split("; "),o=0;o<e.length;o++){var n=e[o].split("=");if(n[0]==t&&"UD"==t)return decodeURIComponent(n[1]);if(n[0]==t)return decodeURI(n[1])}}var s={asideColumnInfoDom:$('<div class="aside-box column-info-container d-flex flex-column"></div>'),columnlistHeight:".columnlist-list-box",columnlistContent:".columnlist-content-box",columnlistUrl:"phoenix/web/v1/column/article-list",columnInfoDom:$(".bt-columnlist-show"),columnPage:1,columnlistStr:""},c="";studyLearnWord&&(c='<span class="column_studyvip_free-active">'+studyLearnWord+"</span>"),window.csdn?window.csdn:window.csdn={},window.csdn.loadRightColumn=function(){$.ajax({type:"GET",url:blogUrl+"/phoenix/web/v1/ab",dataType:"json",xhrFields:{withCredentials:!0},data:{id:380,key:a("uuid_tt_dd")},success:function(t){if(200==t.code&&"control"!==t.data){var e=$("#recommend-right").children();e.each(function(t,e){$(e).hasClass("kind_person")||"asideArchive"!==!$(e).attr("id")||$(e).remove()}),o()}else window.csdn.report&&"function"==typeof window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.10139"})},error:function(t){}})}}),$(function(){function t(){this.rewardSettingJson=null,this.rewardInfo={name:"",sendAmount:"",money:""},this.minSendAmount=10,this.minMoney=5,this.availableAmount=0,this.presetTitle=[],this.currentRedEnvolope=null,this.hasShowGuideBottom=getCookie("hasShowRRGuideBottom"),this.hasShowGuideSide=getCookie("hasShowRRGuideSide"),this.redEnvolope=$("#redEnvolope"),this.formRewardBox=$(".comment-reward-box"),this.dialogRewardModal=$(".comment-rewarddialog-box"),this.commentRewardForm=this.dialogRewardModal.find("#commentRewardForm"),this.txtName=this.commentRewardForm.find("#txtName"),this.txtSendAmount=this.commentRewardForm.find("#txtSendAmount"),this.txtMoney=this.commentRewardForm.find("#txtMoney"),this.getSetting()}function e(){this.commentForm=$("#commentform"),this.txtComment=$("#comment_content"),this.commentCountObj=this.commentForm.find("em"),this.commtCode=$("#commentCode"),this.domLi="",this.btnShowMore=$("#btnMoreComment"),this.commentBox=$("div.comment-list-box"),this.commentBoxDefault=$("div.has-comment-con"),this.commentPagination=$("#commentPage"),this.commentLineBox="",this.commentTxt=$("#comment_replyId"),this.cancelBtn=this.commentForm.find(".btn-cancel"),this.articleId=$("article_id"),this.curH=0,this.pageCount=0,this.pageIndex=1,this.curFloor=1,this.commentCount=0,this.commentPageObj=null,this.commentFontLimit=1e3,this.firstLoad=!0,this.pageSize=10,this.showAll=!1,this.commentFold={pageSize:10,pageIndex:1,pageCount:0,total:0,fold:"fold"},this.lookBadComment=$("#lookBadComment"),this.lookUnFlodComment=$("#lookUnFlodComment"),this.commentUnFold={pageSize:10,pageIndex:1,pageCount:0,total:0,fold:"unfold"},this.lookFlodComment=$("#lookFlodComment"),this.lookGoodComment=$("#lookGoodComment"),this.isCommentFold=!1,this.initHotComment=!0,this.bindBtnFoldMore(),this.commentFoldTextareaStr(),this.initTxt(),this.init(),this.bindBtn(),this.bindTxt(),this.commentCode(),this.comment(),this.lookMoreComment(),this.lookMoreFlodComment(),this.gosideComment(),this.commentSideTitClose(),this.cancelBtnBindEvent(),this.getPagination=function(t){var e=t,o=function(o,n){o&&(o.list&&0==o.list.length&&t-1>=1&&(e=t-1,n.commentPageObj.go(e),1==e&&$("#commentPage").hide()),n.pageCount=o.pageCount,n.commentCount=o.floorCount,n.commentPageObj.render({count:o.floorCount,current:e}),n.renderData(o.list))};this.getData(e,o)},this.bindLikedClick(),this.bindRedRewardClick()}function o(t){username===t.userName?window.isBloger='<span class="is_bloger comment_status_tip">作者</span>':window.isBloger=""}function n(){$("pre.code2").each(function(t,e){hljs.highlightBlock(e),hljs.getLines(e.innerHTML).length>1?hljs.lineNumbersBlock(e):hljs.lineNumbersBlock(e,{singleLine:!0}),$(e).removeClass("code2")})}function i(){$(document).on("click",".comment-reward-item",function(t){if(!getCookie("UserName"))return window.csdn.loginBox.show(),!1;var e=$(this).data("id");w.showRedEnvolope(e)})}function a(){$(document).on("click",".comment-like",function(t){if(!getCookie("UserName"))return window.csdn.loginBox.show(),!1;var e=$(this).hasClass("liked")?"undigg":"digg",o=$(this).data("commentid"),n=this,i={articleId:articleId,commentId:o};$.ajax({url:blogUrl+"phoenix/web/v1/comment/"+e,type:"post",dataType:"json",data:i,xhrFields:{withCredentials:!0},success:function(t){var e=$(n).find("span").text()?parseInt($(n).find("span").text()):0;if($(n).hasClass("liked")){var o=e-1;o=o>0?o:"",$(n).removeClass("liked"),$(n).find(".unclickImg").css("display","inline-block"),$(n).find(".clickedImg").css("display","none"),$(n).find("span").text(o).css("color","#999AAA"),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.6253",extend1:'{"praise":0}'})}else $(n).addClass("liked"),$(n).find(".unclickImg").css("display","none"),$(n).find(".clickedImg").css("display","inline-block"),$(n).find("span").text(e+1).css("color","#FC5531"),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.6253",extend1:'{"praise":1}'});$(n).parents(".right-box").children(".comment-like").html($(n).html()),$(".comment-like-img-hover").css("display","none")}})})}function s(t){var e="";return 0==t.length?"":(e=t.replace(/</g,"&lt;"),e=e.replace(/>/g,"&gt;"))}function c(){for(var t="",e=1;e<=72;e++){var o="";o=e<10?"00"+e:e>=10&&e<100?"0"+e:e,t+='<img class="emoticon-monkey-img" data-emoticon="[face]emoji:'+o+'.png[/face]" src="https://g.csdnimg.cn/static/face/emoji/'+o+'.png">'}return t}function r(t){var e=window.location.href.split(t)[1];return null!=e?e:""}var l="https://liveapi.csdn.net/";t.prototype.bindInit=function(){var t=this;this.rewardInfo.money=this.rewardSettingJson["default"].variables.DEFAULT_AMOUNT,this.rewardInfo.sendAmount=this.rewardSettingJson["default"].variables.DEFAULT_NUM;var e=$(".guide-rr-first");e.remove();var o=$(".rr-guide-box");o.remove();var n=function(e,o){var n=e.parents(".ipt-box"),i=n.find(".notice"),a=e.val().trim();if(0===a.length)"sendAmount"===o?i.text("请输入红包数量"):"money"===o&&i.text("红包金额最低"+t.minMoney+"元"),n.addClass("error");else{var s=!0;"sendAmount"===o?parseInt(a)<t.minSendAmount&&(i.text("红包个数最小为"+t.minSendAmount+"个"),s=!1):"money"===o&&(parseFloat(a)<t.minMoney?(i.text("红包金额最低"+t.minMoney+"元"),t.commentRewardForm.find(".balance-info-box").removeClass("error"),t.commentRewardForm.find(".link-charge").text("前往充值 >"),s=!1):parseFloat(a)>parseFloat(t.availableAmount)?(t.commentRewardForm.find(".balance-info-box").addClass("error"),t.commentRewardForm.find(".link-charge").text("余额不足请充值")):(t.commentRewardForm.find(".balance-info-box").removeClass("error"),t.commentRewardForm.find(".link-charge").text("前往充值 >"))),s?n.removeClass("error"):n.addClass("error");var c=t.txtSendAmount.parents(".ipt-box"),r=t.txtSendAmount.val(),l=t.txtMoney.val();if(r&&parseFloat(l)>=5&&parseFloat(.1*r).toFixed(2)>parseFloat(l)){var d=Math.floor(parseFloat(l)/.1);c.find(".notice").text("红包个数范围为"+t.minSendAmount+"-"+d+"个"),c.addClass("error")}else parseInt(r)>10&&c.removeClass("error")}},i=function(t,e,o){var n=t.indexOf(e),i="";return n===-1?t:"-"===e&&0!==n?t.slice(0,n):("."===e&&t.match(/^(\.|-\.)/)&&(i=n?"-0":"0"),i+t.slice(0,n+1)+t.substr(n+1,2).replace(o,""))},a=function(t,e){t=e?i(t,".",/\./g):t.split(".")[0];var o=e?/[^-0-9.]/g:/[^-0-9]/g;return t.replace(o,"")},s=function(){var e=t.txtName.val(),o=t.txtSendAmount.val(),n=t.txtMoney.val();""!==e&&""!==o&&""!==n&&0===t.commentRewardForm.find(".ipt-box.error,.balance-info-box.error").length?(t.commentRewardForm.find(".btn-submit").removeAttr("disabled"),t.commentRewardForm.find(".pay-info").find(".price").text(parseFloat(n).toFixed(2)),t.commentRewardForm.find(".pay-info").show()):(t.commentRewardForm.find(".btn-submit").attr("disabled",!0),t.commentRewardForm.find(".pay-info").hide())};this.txtName.val(this.rewardInfo.name),this.commentRewardForm.find(".btn-random").click(function(){t.randomTitle()}),this.dialogRewardModal.find("a.btn-form-close").click(function(){t.hideRewardForm()}),this.commentRewardForm.find("input:text").keyup(function(){var t=$(this).attr("name");"sendAmount"===t?$(this).val(a($(this).val(),!1)):"money"===t&&$(this).val(a($(this).val(),!0)),n($(this),t),s()}),this.commentRewardForm.find("input:text").blur(function(){var t=$(this).attr("name");n($(this),t),s()}),this.formRewardBox.find(".btn-remove-reward").click(function(){t.formRewardBox.hide(),t.rewardInfo={name:"",sendAmount:"",money:""},t.txtName.val(t.rewardSettingJson["default"].defaultTitle),t.txtSendAmount.val(""),t.txtMoney.val(""),t.commentRewardForm.find(".error").removeClass("error")}),this.commentRewardForm.find(".btn-submit").click(function(){t.rewardInfo.name=t.txtName.val(),t.rewardInfo.sendAmount=t.txtSendAmount.val(),t.rewardInfo.money=t.txtMoney.val(),t.formRewardBox.find(".info").text(t.rewardInfo.name);var e=new Number(t.rewardInfo.money).toFixed(2);t.formRewardBox.find(".price").text(e+"元"),t.formRewardBox.show(),t.dialogRewardModal.hide()}),this.commentRewardForm.find(".btn-cancel").click(function(){t.hideRewardForm()})},t.prototype.getSetting=function(){var t=this;$.get("https://img-home.csdnimg.cn/data_json/jsconfig/redPacket.json",function(e){t.rewardSettingJson=e,t.rewardInfo.name=e["default"].defaultTitle,t.presetTitle=e["default"].presetTitle,t.bindInit(),t.formRewardBox.css(e["default"].redCardSty),t.redEnvolope.find(".top").css(e["default"].preOpenSty),t.redEnvolope.find(".red-openbtn").css(e["default"].preOpenBtnSty),t.redEnvolope.find("footer").css(e["default"].preFooterSty),t.redEnvolope.find("a.rule").attr("href",e.blog.ruleUrl)})},t.prototype.getRedPacketVerifyBatch=function(){var t=$(".comment-reward-item[data-uncheck='1']");if(t.length>0){for(var e=[],o=0;o<t.length;o++)e.push($(t[o]).data("id"));$.ajax({type:"GET",url:l+"mp/live-gift/wrapper/v1/redpacket/verify/batch",dataType:"json",data:{orderNoList:e.join(","),username:currentUserName},xhrFields:{withCredentials:!0},success:function(t){if(200==t.code){var e=t.data.records;if(e&&e.length>0)for(var o=0;o<e.length;o++){var n=e[o],i=$('.comment-reward-item[data-id="'+n.orderNo+'"]');i.removeAttr("data-uncheck"),i.attr("data-status",n.status)}}},error:function(){showToast({text:"用户信息获取失败，请重试。",bottom:"10%",zindex:9e3,speed:500,time:2e3})}})}},t.prototype.getUserAmount=function(t){var e=this;$.ajax({type:"GET",url:l+"mp/live-gift/wrapper/v1/wallet/balance",xhrFields:{crossDomain:!0,withCredentials:!0},success:function(o){200==o.code?(e.availableAmount=o.data.availableAmount,e.commentRewardForm.find(".balance").text(o.data.availableAmount),""!==e.rewardInfo.sendAmount&&""===e.txtSendAmount.val()&&(e.txtSendAmount.val(e.rewardInfo.sendAmount).attr("placeholder","请填写红包数量(最小"+e.minSendAmount+"个)"),e.txtSendAmount.trigger("blur")),""!==e.rewardInfo.money&&""===e.txtMoney.val()&&(e.txtMoney.val(e.rewardInfo.money).attr("placeholder","请填写总金额(最低"+e.minMoney+"元)"),e.txtMoney.trigger("blur")),t()):showToast({text:"用户信息获取失败，请重试。",bottom:"10%",zindex:9e3,speed:500,time:2e3})},error:function(){showToast({text:"用户信息获取失败，请重试。",bottom:"10%",zindex:9e3,speed:500,time:2e3})}})},t.prototype.showRewardForm=function(){var t=this;this.getUserAmount(function(){t.dialogRewardModal.show()})},t.prototype.hideRewardForm=function(){var t=this;t.txtName.val(t.rewardInfo.name),t.txtSendAmount.val(t.rewardInfo.sendAmount),t.txtMoney.val(t.rewardInfo.money),t.dialogRewardModal.hide()},t.prototype.randomTitle=function(){var t=this,e=t.presetTitle.filter(function(e){return e!==t.rewardInfo.name}),o=e[Math.floor(Math.random()*e.length)];return t.txtName.val(o),t.rewardInfo.name=o,t.txtName.trigger("blur"),o},t.prototype.getReceiveList=function(t,e){var o=this;$.ajax({type:"GET",url:l+"mp/live-gift/wrapper/v1/redpacket/receivedInfos",data:{orderNo:t},dataType:"json",xhrFields:{withCredentials:!0},success:function(n){if(200==n.code){o.currentRedEnvolope=n.data;var i=$('.comment-reward-item[data-id="'+t+'"]');i.attr("data-status",n.data.redPacketStatus),o.buildRedEnvolopeHtml(t,n.data,e)}else showToast({text:n.message,bottom:"10%",zindex:9e3,speed:500,time:2e3})},error:function(){showToast({text:"红包信息获取失败，请重试。",bottom:"10%",zindex:9e3,speed:500,time:2e3})}})},t.prototype.buildRedEnvolopeHtml=function(t,e,o){var n=this,i=e.redPacketInfo,a=this.redEnvolope.find("#preOpen");a.find(".clearTpaErr").attr("src",i.creatorAvatar),a.find(".author").text(i.title),a.find(".red-openbtn").off("click"),a.find(".red-openbtn").on("click",function(){n.receiveRedPacket(t)});var s=this.redEnvolope.find("#opened");s.find(".creatorUrl").attr("href","https://blog.csdn.net/"+i.creatorUserName),s.find(".clearTpaErr").attr("src",i.creatorAvatar);var c=i.creatorNickName?i.creatorNickName:i.creatorUserName;s.find(".author > .tt").text(c),s.find(".author > .tt").data("creatorUserName",i.creatorUserName);var r='<a href="'+this.rewardSettingJson.blog.ruleUrl+'" class="rule" target="_blank">红包规则</a>',l="";switch(e.redPacketStatus){case"received":l='<div class="my-receive">'+e.currentUserReceivedInfo.receivedMoney+'</div>          <div div class="wallet-msg">已放入你的CSDN钱包，<a href="https://i.csdn.net/#/wallet/index" target="_blank">            点击查看            </a>          </div>          <div class="receive-msg">已领取'+i.receivedAmount+"/"+i.totalAmount+"个，共"+i.receivedMoney+"/"+i.totalMoney+"元</div>"+r;break;case"completed":l='<div class="receive-msg">          <span>'+i.totalAmount+"个红包，"+i.completeTimeInterval+"被抢光</span>        </div>"+r;break;case"expired":l='<div class="receive-msg">          <span>该红包已过期，</span>已领取'+i.receivedAmount+"/"+i.totalAmount+"个，共"+i.receivedMoney+"/"+i.totalMoney+"元</div>"+r}s.find(".receive-box header").html(l);var d=function(t){return t=t<10?"0"+t:t},m=function(t){var e=new Date(t);return d(e.getMonth()+1)+"-"+d(e.getDate())+" "+d(e.getHours())+":"+d(e.getMinutes())};if(e.receivedInfos&&e.receivedInfos.length>0){for(var p="",u=0;u<e.receivedInfos.length;u++){var h=e.receivedInfos[u],f="completed"!==e.redPacketStatus&&"expired"!==e.redPacketStatus||!h.lucky?"":'<img class="best" src="'+blogStaticHost+'dist/pc/img/best.png" title="手气最佳">';p+='<div class="user-item">          <div class="user-info">            <a href="https://blog.csdn.net/'+h.receiverUserName+'" target="_blank">              <img src="'+h.receiverAvatar+'" alt="" />            </a>            <div class="u-info">              <div class="name">'+h.receiverNickName+'</div>              <div class="time">'+m(h.receiveTime)+'</div>            </div>          </div>          <div class="money">            <span>'+h.receivedMoney+"元</span>"+f+"</div>        </div>"}s.find(".receive-box .receive-list").html(p)}o()},t.prototype.receiveRedPacket=function(t){var e=this.redEnvolope.find("#preOpen"),o=this.redEnvolope.find("#opened"),n=this;e.find(".red-openbtn").addClass("rotate-start"),$.ajax({type:"POST",url:l+"mp/live-gift/v1.0/redPacket/receiveRedPacket",contentType:"application/json; charset=utf-8",headers:{requestID:(new Date).getTime(),xappid:"pc_blog",xdeviceid:"pc_blog"},data:JSON.stringify({orderNo:t,username:currentUserName}),xhrFields:{withCredentials:!0},success:function(i){if(500===i.code)showToast({text:i.message,bottom:"10%",zindex:9e3,speed:500,time:2e3}),e.find(".red-openbtn").removeClass("rotate-start");else{200!==i.code&&i.message&&(400108103===i.code?showToast({html:i.message+"<a style='color: #4ea1db;text-decoration: underline;' href='https://bbs.csdn.net/?type=4&header=0&utm_source=empty_dialog' target='_blank'>更多红包</a>",bottom:"10%",zindex:9e3,speed:500,time:2e3}):showToast({text:i.message,bottom:"10%",zindex:9e3,speed:500,time:2e3}));var a=o.find(".author > .tt").data("creatorUserName");username!==currentUserName&&a!==username&&($("#btnAttent").hasClass("attented")||(window.followByRR=!0,$("#btnAttent").trigger("click"))),n.getReceiveList(t,function(){setTimeout(function(){e.find(".red-openbtn").addClass("red-hidebtn"),e.find(".top").addClass("slide-top"),e.find("footer").addClass("slide-bottom"),setTimeout(function(){e.css("display","none"),o.css("display","flex"),e.find(".top").removeClass("slide-top"),e.find("footer").removeClass("slide-bottom"),e.find(".red-openbtn").removeClass("rotate-start").removeClass("red-hidebtn")},800)},800)})}},error:function(){showToast({text:"请求失败，请重试",bottom:"10%",zindex:9e3,speed:500,time:2e3})}})},t.prototype.showRedEnvolope=function(t){var e=this;this.getReceiveList(t,function(){"received"!==e.currentRedEnvolope.redPacketStatus&&"expired"!==e.currentRedEnvolope.redPacketStatus&&"completed"!==e.currentRedEnvolope.redPacketStatus||(e.redEnvolope.find("#preOpen").hide(),e.redEnvolope.find("#opened").css("display","flex")),e.redEnvolope.show(),e.redEnvolope.find(".close-btn").one("click",function(){e.redEnvolope.hide(),e.redEnvolope.find("#preOpen").removeAttr("style"),e.redEnvolope.find("#opened").removeAttr("style")})})};'<ul class="comment-list">                <li class="comment-line-box">                <div style="display: flex;width: 100%;">                <a target="_blank" href="'+blogUrl+username+'">                <img src="'+avatar+'" username="'+username+'" alt="'+username+'" class="avatar">                </a>                <div class="right-box "><div class="new-info-box clearfix">                <a target="_blank" href="'+blogUrl+username+'"><span class="name ">'+nickName+'<img class="is_bloger" src="'+blogStaticHost+'dist/components/img/<EMAIL>"></span></a><span class="colon">:</span><span class="floor-num"></span><span class="new-comment">这篇文章对你有帮助吗？作为一名程序工程师，在评论区留下你的困惑或你的见解，大家一起来交流吧！</span>                </div></div></div></li></ul>','<img class="read-reply-img" src="'+blogStaticHost+'dist/pc/img/replyComment.png">';e.prototype.lookMoreComment=function(){var t=this;$(document).on("click","#lookGoodComment",function(e){t.getPagination(t.commentUnFold.pageIndex)})},e.prototype.lookMoreFlodComment=function(){var t=this;$(document).on("click","#lookBadComment",function(e){t.getFoldList(t.commentFold)})},e.prototype.gosideComment=function(){$(document).on("click",".go-side-comment",function(){getCookie("UserName")?($("#commentSideBoxshadow").fadeIn(),$(this).hasClass("focus")&&$("#comment_content").focus()):window.csdn.loginBox.show()})},e.prototype.commentSideTitClose=function(){var t=this;$(document).on("click",".comment-side-tit-close",function(e){e.target.className.indexOf("comment-side-tit-close")>-1&&($("#commentSideBoxshadow").fadeOut(function(){t.isCommentFold&&(t.isCommentFold=!1,t.commentUnFold.pageIndex=1,t.getPagination(t.commentUnFold.pageIndex),$("#pcFlodCommentSideBox").hide(),$("#pcCommentSideBox").show(),$("#pcFlodCommentSideBox .comment-fold-content").html(""))}),$("#commentQuote").remove())}),$(document).keyup(function(t){var e=t.which,o=$("#commentSideBoxshadow");27==e&&"block"==o.css("display")&&($(t.target).is("#comment_content")||o.trigger("click"))})},e.prototype.getData=function(t,e){if(!getCookie("UserName"))return!1;var o=this;o.pageIndex=void 0!==t?t:o.pageIndex;var n=null,i="&commentId="+u;o.pageIndex>1&&(i=""),$.ajax({url:blogUrl+"phoenix/web/v1/comment/list/"+articleId+"?page="+o.pageIndex+"&size="+o.pageSize+"&fold=unfold"+i,type:"post",xhrFields:{withCredentials:!0},success:function(t){200===t.code&&(n=t.data,n&&(o.curFloor=n.floorCount-(o.pageIndex-1)*o.pageSize,o.commentUnFold.pageIndex+=1,o.commentUnFold.pageCount=n.pageCount,o.commentUnFold.total=n.floorCount,
o.commentFold.total=n.foldCount,o.commentUnFold.pageIndex>o.commentUnFold.pageCount?(o.lookGoodComment.hide(),o.commentFold.total>0?o.lookFlodComment.show():o.lookFlodComment.hide()):(o.lookGoodComment.show(),o.lookFlodComment.hide()),$(".tool-item-comment .count").html(n.count),$(".comment-side-tit-count .count").html(n.count),$(".has-comment-tit .count").html(n.count),$(".has-comment-bt-left .count").html(n.count),$(".look-flod-comment .count").html(o.commentFold.total),$(".comment-fold-tit .count").html(o.commentFold.total),o.commentFold.total>0||o.lookFlodComment.hide(),n.count>0?(getCookie("UserName")||$("#pcCommentBox .unlogin-comment-tit").text(n.count+" 条评论"),this.firstLoad&&($("#pcCommentBox").removeClass("comment-box-nostyle"),$("#pcCommentBox .has-comment").css({display:"block"}))):getCookie("UserName")?($("#pcCommentBox").addClass("comment-box-nostyle"),$("#pcCommentBox .has-comment").css({display:"none"})):($("#pcCommentBox .unlogin-comment-tit").text("参与评论"),$("#pcCommentBox").removeClass("comment-box-nostyle"),$("#pcCommentBox .has-comment").css({display:"none"}))))},complete:function(){e&&e(n,o)}})},e.prototype.init=function(){window.localStorage.getItem("AM_comment_data")&&window.localStorage.getItem("AM_comment_id")==articleId&&(this.txtComment[0].focus(),this.txtComment.val(window.localStorage.getItem("AM_comment_data")),this.commentTxt.val(window.localStorage.getItem("AM_comment_replyId")),$(window).scrollTop(this.txtComment.offset().top));var t=function(t,e){if(null!==t){var o="";if(getCookie("UserName")||(o="登录 "),e.pageCount=t.pageCount,e.curFloor=t.floorCount,e.commentCount=t.floorCount,e.btnShowMore.html("<span>"+o+"查看 "+t.count+' 条热评</span><img class="look-more-comment" src="'+blogStaticHost+'dist/pc/img/arrowDownComment.png">'),e.renderData(t.list),getCookie("UserName")?(e.btnShowMore.parent("div.opt-box").remove(),e.showAll=!0,t.floorCount<=e.pageSize?e.commentPagination.addClass("d-none"):0===$("#btnMoreComment").length&&e.commentPagination.removeClass("d-none")):t.floorCount<=3?(e.btnShowMore.parent("div.opt-box").remove(),e.showAll=!0):t.floorCount<=e.pageSize?e.commentPagination.addClass("d-none"):0===$("#btnMoreComment").length&&e.commentPagination.removeClass("d-none"),null===e.commentPageObj?t.pageCount>0&&(e.commentPageObj=new Paging,e.commentPageObj.init({target:e.commentPagination,pagesize:e.pageSize,count:e.commentCount,current:1,firstTpl:"",lastTpl:"",callback:function(t,o,n){e.getPagination(t)}})):e.commentPageObj.render({count:e.commentCount}),""!==u){var n=$("div.has-comment-con ul.comment-list:first-child").find("li.comment-line-box");$(n).each(function(t,e){if(u==$(e).data("commentid")){var o=$(document).scrollTop(),i=$("#csdn-toolbar").height(),a=16;o<52&&(i=2*i);var s=$(n)[t].offsetTop-i-a;return setTimeout(function(){$("html,body").animate({scrollTop:s},200)},850),!1}})}}else e.btnShowMore.parent("div.opt-box").remove(),e.showAll=!0};this.firstLoad?this.getData(1,t):blogMoveHomeArticle&&this.btnShowMore.parent("div.opt-box").remove()},e.prototype.initTxt=function(){var t=this;$("#comment_content").length&&$("#comment_content").focus(function(){t.addId(t);var e=t.commentFontLimit-parseInt($(this).val().length);t.commentCountObj.text(e>=0?e:0),t.cancelBtnToggle(!1),t.commentLineBox.length>0&&($("#commentEditBox").siblings(".comment-list-item").find("a.btn-reply").html("回复"),t.commentLineBox.find(".comment-edit-box").remove())}),getCookie("UserName")&&($(document).click(function(e){if(!$(e.target).is("div.comment-edit-box *")&&!$(e.target).is(".right_recommend_comment_button *")&&!$(e.target).is(".reply")&&($(".comment-operate-isshow").hide(),t.txtComment.length>0)){var o=t.commentFontLimit-t.txtComment.val().length;t.commentCountObj.text(o>=0?o:0)}}),$(document).click(function(t){$(t.target).is(".comment-line-box *")||($("#commentEditBox").siblings(".comment-list-item").find("a.btn-reply").html("回复"),$("#commentEditBox").remove())})),$("input.btn-comment-defualt").click(function(){$(".tool-item-comment").trigger("click")}),$(".tool-item-comment").click(function(){if(3===commentAuth){var t='\n          <div class="pos-box">\n            <div class="modal-box">\n              <p class="titile">评论提示</p>\n              <div class="content-box">\n                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">\n                  <path d="M7.60001 0.599976C3.70626 0.599976 0.600006 3.70623 0.600006 7.59998C0.600006 11.4937 3.70626 14.6 7.60001 14.6C11.4938 14.6 14.6 11.4937 14.6 7.59998C14.6 3.70623 11.4938 0.599976 7.60001 0.599976ZM6.92626 3.33873C7.11001 3.15498 7.26751 3.03248 7.56501 3.03248C7.89751 3.00623 8.20376 3.16373 8.32626 3.39998C8.46626 3.56623 8.54501 3.93373 8.51001 4.15248C8.51001 4.21373 8.46626 4.62498 8.44876 4.73873L8.20376 7.84498C8.20376 8.15123 8.14251 8.45748 8.02001 8.69373C7.95876 8.87748 7.77501 8.99998 7.53001 8.99998C7.34626 8.99998 7.16251 8.87748 7.10126 8.69373C6.97876 8.38748 6.91751 8.14248 6.91751 7.84498L6.75126 4.79998C6.69001 4.24873 6.69001 4.43248 6.69001 4.12623C6.69001 3.82873 6.75126 3.58373 6.92626 3.33873ZM8.27376 11.9225C8.09001 12.1062 7.84501 12.1675 7.66126 12.1675C7.41626 12.1675 7.17126 12.1062 6.98751 11.9225C6.80376 11.7387 6.68126 11.4937 6.68126 11.1962C6.68126 10.9512 6.74251 10.7062 6.92626 10.5225C7.11001 10.3387 7.35501 10.2162 7.60001 10.2162C7.84501 10.2162 8.09001 10.3387 8.27376 10.5225C8.45751 10.7062 8.51876 10.9512 8.51876 11.1962C8.51001 11.4937 8.44876 11.7387 8.27376 11.9225Z" fill="#F78E00"/>\n                </svg>\n                该文章评论区已关闭\n              </div>\n              <div class="opt-box">\n                <button class="btn-modal-close">\n                  知道了\n                </button>\n              </div>\n            </div>\n          </div>\n        ',e=document.createElement("div");return e.id="commentAuthAlertModal",e.classList.add("mask-dark"),e.innerHTML=t,document.body.appendChild(e),e.querySelector(".btn-modal-close").onclick=function(){e.remove()},!1}setTimeout(function(){$("#pcCommentBox").show(),$("#comment_content").focus()},0)})},e.prototype.bindBtn=function(){function t(t){return"svg"===this.nodeName||getCookie("UserName")?(e.commentCount>e.pageSize&&e.commentPagination.removeClass("d-none"),$(this).parent("div.opt-box").remove(),$(this).parent().parent("div.opt-box").remove(),e.showAll=!0,e.curH=0,void(window.event?window.event.cancelBubble=!0:t.stopPropagation())):(csdn.loginBox.show(),!1)}var e=this;$(document).on("click",".comment-operate-item",function(t){var o=$(t.target).data("type");if(void 0!==o)switch(o){case"report":var n=$(t.target).parents("li.comment-line-box"),i=n.data("commentid"),a=n.data("replyname");window.csdn.loginBox.key({biz:"blog",subBiz:"other_service",cb:function(){window.csdn.feedback({type:"blog",rtype:"comment",rid:i,reportedName:a,submitOptions:{contentUrl:articleDetailUrl},callback:function(){showToast({text:"感谢您的举报，我们会尽快审核！",bottom:"10%",zindex:9e3,speed:500,time:1500})}})}});break;case"reply":var s=$(t.target)[0].innerText;if($(this).find("a.btn-reply").text("回复"),$(t.target)[0].innerText="回复"==s?"收起":"回复",e.isCommentFold){if(!getCookie("UserName"))return window.csdn.loginBox.show(),!1;$("#commentEditBox").remove();var c=e.commentFoldTextareaStr("commentEditBox","New");if(!$("#commentEditBox").length&&"回复"==s){$(t.target).parents(".comment-list-item").after(c);var r=$(t.target).parents(".comment-line-box");e.addId(e,"new"),e.comment(),e.bindTxt(),curComment={},curComment.Id=r.data("commentid"),curComment.User=r.data("replyname"),curComment.text=r.find(".name").text()+(r.find(".nick-name").length?" 回复 "+r.find(".nick-name").text():"")+r.find(".comment").text(),e.cancelBtnToggle(!0),e.replayComment(curComment),e.txtComment.trigger("focus"),e.commentCode()}}else"回复"==s?"":$("#commentEditBox").remove();break;case"delete":var l=$(t.target).data("nickname"),d=$(t.target).parents("li.comment-line-box"),i=d.data("commentid");showConfirm({tit:"删除评论",text:'确认将 "'+l+'" 的评论删除吗？',isBlack:"Black"==skinStatus,rightText:"确定",zindex:9e3,success:function(){e.commentIsDelete(i)},cancel:function(){}});break;case"istop":var l=$(t.target).data("nickname"),m="false"!=$(t.target).attr("data-status"),p=$(t.target).parents("li.comment-line-box"),i=p.data("commentid");showConfirm({tit:m?"取消置顶":"置顶评论",text:m?'确认将 "'+l+'" 的评论取消置顶吗？':'确认将 "'+l+'" 的评论置顶吗？',isBlack:"Black"==skinStatus,rightText:"确定",zindex:9e3,success:function(){e.commentIsTop(i,m)},cancel:function(){}});break;case"blacklist":var m="false"!=$(t.target).attr("data-status"),u=$(t.target).data("username"),l=$(t.target).data("nickname");showConfirm({tit:m?"取消屏蔽":"屏蔽用户",text:isCurrentUserVip?m?'确认将 "'+l+'" 取消屏蔽吗？取消后对方即可在您的内容下进行评论':'确认将 "'+l+'" 屏蔽吗？屏蔽后对方将无法在您的内容下进行评论':"屏蔽用户为会员权益，您可加入会员行使该权益",isBlack:"Black"==skinStatus,rightText:isCurrentUserVip?"确定":"开通会员",zindex:9e3,success:function(){var t="";isCurrentUserVip?(t=m?"1001.2101.3001.6915":"1001.2101.3001.6913",e.commentIsBlack(username,u,m)):(window.open("https://www.csdn.net/vip?utm_source=commentblocking","_blank"),t="1001.2101.3001.6917"),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:t})},cancel:function(){}})}}),this.btnShowMore.click(t),this.btnShowMore.find("svg").click(t)},e.prototype.bindTxt=function(){var t=this;this.txtComment.keyup(function(){var e=t.commentCountObj,o=t.commentFontLimit-$(this).val().length;o<0?e.text(0):e.text(o),$(this).val().length||$(this).data("replayUser")?t.cancelBtnToggle(!0):t.cancelBtnToggle(!1)})},e.prototype.renderData=function(t){var e=this,o=e.commentFoldTextareaStr("commentEditBox","New");e.buildListDom(t,!1,this,!0),$(".comment-list li.comment-line-box").on("click",function(t){if($(t.target).data("flag")&&(e.commentLineBox&&e.commentLineBox[0]!=$(this)[0]&&e.commentLineBox.find(".comment-edit-box").remove(),0==$(this).find(".comment-edit-box").length)){if(getCookie("UserName")){getCookie("UserName")&&($(this).append(o),$("#closeNew").on("click",function(t){$(this).parents(".comment-edit-box").remove()}),e.addId(e,"new"),e.comment(),e.bindTxt(),curComment={},curComment.Id=$(this).data("commentid"),curComment.User=$(this).data("replyname"),curComment.text=$(this).find(".name").text()+($(this).find(".nick-name").length?" 回复 "+$(this).find(".nick-name").text():"")+$(this).find(".comment").text(),e.cancelBtnToggle(!0),e.replayComment(curComment),e.txtComment.trigger("focus"),e.commentCode());var n=$(document).scrollTop(),i=$("#csdn-toolbar").height();n<52&&(i=2*i)}else csdn.loginBox.show();e.commentLineBox=$(this)}}),n()},e.prototype.commentFoldTextareaStr=function(t,e){if($("#isShowCommentAuth").length&&1==$("#isShowCommentAuth").data("type"))var o='<span class="tip">&nbsp;|&nbsp;博主筛选后可见</span>';else var o="";var n='<div class="comment-edit-box d-flex" id="'+t+'">            <form id="commentform'+e+'">                <textarea class="comment-content" name="comment_content" id="comment_content'+e+'" placeholder="请发表有价值的评论， 博客评论不欢迎灌水，良好的社区氛围需大家一起维护。" maxlength="1000"></textarea>                <div class="comment-operate-box">                    <div class="comment-operate-l">                        <span id="tip_comment" class="tip">还能输入<em>1000</em>个字符</span>'+o+'                    </div>                    <div class="comment-operate-r">                        <div class="comment-operate-item comment-emoticon">                            <img class="comment-operate-img" data-url="" src="'+blogStaticHost+'dist/pc/img/commentEmotionIcon.png" alt="表情包">                            <span class="comment-operate-tip">插入表情</span>                            <div class="comment-operate-isshow comment-emoticon-box comment-emoticon'+e+'" style="display: none;">                                <div class="comment-emoticon-img-box"></div>                            </div>                        </div>                        <div class="comment-operate-item comment-code comment-code'+e+'">                            <img class="comment-operate-img" data-url="" src="'+blogStaticHost+'dist/pc/img/commentCodeIcon.png" alt="代码片">                            <span class="comment-operate-tip">代码片</span>                            <div class="comment-operate-isshow comment-code-box">                                <ul id="commentCode'+e+'">                                    <li><a data-code="html">HTML/XML</a></li>                                    <li><a data-code="objc">objective-c</a></li>                                    <li><a data-code="ruby">Ruby</a></li>                                    <li><a data-code="php">PHP</a></li>                                    <li><a data-code="csharp">C</a></li>                                    <li><a data-code="cpp">C++</a></li>                                    <li><a data-code="javascript">JavaScript</a></li>                                    <li><a data-code="python">Python</a></li>                                    <li><a data-code="java">Java</a></li>                                    <li><a data-code="css">CSS</a></li>                                    <li><a data-code="sql">SQL</a></li>                                    <li><a data-code="plain">其它</a></li>                                </ul>                            </div>                        </div>                        <div class="comment-operate-item">                            <input type="hidden" id="comment_replyId'+e+'" name="comment_replyId'+e+'">                            <input type="hidden" id="article_id'+e+'" name="article_id'+e+'" value="'+$("#article_id").val()+'" >                            <input type="hidden" id="comment_userId'+e+'" name="comment_userId'+e+'" value="">                            <input type="hidden" id="commentId'+e+'" name="commentId'+e+'" value="">                            <a data-report-click=\'{"spm":"1001.2101.3001.4227"}\'><input type="submit" class="btn-comment btn-comment-input" value="评论"></a>                        </div>                    </div>                </div>            </form>        </div>';return n},e.prototype.buildListDom=function(t,e,o,n){for(var i="",a="",s="",c=0;c<t.length;c++){var r=t[c].info,l=t[c].sub,d=void 0!==l?l.length:0;if(i='<ul class="comment-list">',i+=o.buildHtml(r,d,!0,r.userName?r.userName:"",n),d>0){i+='<li class="replay-box" style="display:block">',i+='<ul class="comment-list">';for(var m=0;m<d;m++){var p=l[m];null!==p&&(i+=this.buildHtml(p,0,!1,p.parentUserName?p.parentUserName:"",n))}d>1&&(i+='<div class="second-look-more">查看全部 '+d+' 条回复<img src="'+blogStaticHost+"dist/pc/img/commentArrowDown"+skinStatus+'.png"></div>'),i+="</ul>",i+="</li>"}if(i+="</ul>",e)a+=i;else{if(o.commentBox.append($(i)),2==o.commentUnFold.pageIndex&&0==c&&o.initHotComment&&t.length){var u=t[0].info,h="";u.content=this.replaceQuote(u.content);var f=this.getQuoteInfo(u.content);f.length>1?(quotename=f[1],quotecontent=this.getCodeInfo(f[2]),quotecontent=this.replaceNewUBB(quotecontent),s=this.getCodeInfo(f[3]),s=this.replaceNewUBB(s),s='<span class="quote">引用“<font color="black">'+quotename+"</font>”的评论：</span><blockquote>"+quotecontent+"</blockquote>"+s):(s=this.replaceNewUBB(f[0]),s=this.getCodeInfo(s));var g,v=null!==u.redEnvelopeInfo,b=v?JSON.parse(u.redEnvelopeInfo):null;v&&(g=JSON.stringify({spm:"3001.9224",extend1:{orderNo:u.orderNo}}),b.money=parseFloat(parseInt(b.money)/100).toFixed(2)),h='<div class="hot-comment-box" data-commentid="'+u.commentId+'"><a class="hot-comment-href" target="_blank" href="'+blogUrl+u.userName+'"><img src="'+u.avatar+'">'+(u.gptInfo?'<img class="hot-comment-gpt-icon" src="'+u.gptInfo.icon+'">':"")+u.nickName+'</a><span class="hot-comment-tag">热评</span>'+(v?'<div class="comment-reward-item" data-report-click=\''+g+'\' data-uncheck="1" data-commentid="'+u.commentId+'" data-id="'+u.orderNo+'">        <span span class="amount">'+b.money+"元</span>        </div > ":"")+'<div class="hot-comment-con">'+s+"</div></div>",o.commentBoxDefault.html($(h)),o.initHotComment=!1,$("#pcCommentBox").show()}o.curFloor--}}return!t.length&&o.commentUnFold.total<=0&&o.commentBoxDefault.html(""),e?a:void w.getRedPacketVerifyBatch()},e.prototype.addId=function(t,e){"new"==e?(t.commentForm=$("#commentformNew"),t.txtComment=$("#comment_contentNew"),t.commentCountObj=t.commentForm.find("em"),t.commtCode=$("#commentCodeNew"),t.commentTxt=$("#comment_replyIdNew"),t.articleId=$("#article_idNew")):(t.commentForm=$("#commentform"),t.txtComment=$("#comment_content"),t.commentCountObj=t.commentForm.find("em"),t.commtCode=$("#commentCode"),t.commentTxt=$("#comment_replyId"),t.articleId=$("#article_id"))},e.prototype.bindBtnFoldMore=function(){var t=this;$(document).on("click","#lookFlodComment",function(){t.isCommentFold=!0,t.commentFold.pageIndex=1,t.getFoldList(t.commentFold),$("#pcFlodCommentSideBox").show(),$("#pcCommentSideBox").hide(),$("#pcCommentSideBox .comment-list-box").html("")}),$(document).on("click","#lookUnFlodComment",function(){t.isCommentFold=!1,t.commentUnFold.pageIndex=1,t.getPagination(t.commentUnFold.pageIndex),$("#pcFlodCommentSideBox").hide(),$("#pcCommentSideBox").show(),$("#pcFlodCommentSideBox .comment-fold-content").html("")}),e.prototype.getFoldList=function(e){$.ajax({url:blogUrl+"phoenix/web/v1/comment/list/"+articleId+"?page="+t.commentFold.pageIndex+"&size="+t.commentFold.pageSize+"&fold="+t.commentFold.fold,type:"post",xhrFields:{withCredentials:!0},success:function(e){if(200===e.code){result=e.data;result.foldCount;if($(".tool-item-comment .count").html(result.count),$(".comment-side-tit-count .count").html(result.count),$(".has-comment-tit .count").html(result.count),$(".has-comment-bt-left .count").html(result.count),$(".look-flod-comment .count").html(result.foldCount),$(".comment-fold-tit .count").html(result.foldCount),result.foldCount>0?t.lookFlodComment.show():t.lookFlodComment.hide(),result.list.length){var o=t.buildListDom(result.list,!0,t,!1),n=document.createElement("div");n.classList.add("comment-fold-con","comment-list-box","comment-operate-item"),n.innerHTML=o,$("#pcFlodCommentSideBox .comment-fold-content").append(n),t.commentFold.pageIndex+=1,t.commentFold.pageCount=result.pageCount,t.commentFold.pageIndex>t.commentFold.pageCount?t.lookBadComment.hide():t.lookBadComment.show()}}}})}};var d="";if(e.prototype.buildHtml=function(t,e,n,i,a){var s="",c="",r="",l="",m=!1,p="",u="";if(t.content=this.replaceUrl(t.content),n){d=i,p=this.getQuote(t.content),t.content=this.replaceQuote(t.content);var h=this.getQuoteInfo(t.content);h.length>1?(r=h[1],l=this.getCodeInfo(h[2]),l=this.replaceNewUBB(l),s=this.getCodeInfo(h[3]),s=this.replaceNewUBB(s),s='<span class="quote">引用“<font color="black">'+r+"</font>”的评论：</span><blockquote>"+l+"</blockquote>"+s):(s=this.replaceNewUBB(h[0]),s=this.getCodeInfo(s)),username===getCookie("UserName")&&(m=!0)}else{var h=this.getReplyInfo(t.content);c=h[1];var f=h.length>2?this.getQuoteInfo(h[2]):this.getQuoteInfo(h[0]);f.length>1?(r=f[1],l=this.getCodeInfo(f[2]),l=this.replaceNewUBB(l),s=this.getCodeInfo(f[3]),s=this.replaceNewUBB(s),s='<span class="quote">引用“<font color="black">'+r+"</font>”的评论：</span><blockquote>"+l+"</blockquote>"+s):(s=this.replaceNewUBB(f[0]),s=this.getCodeInfo(s))}if(o(t),n&&p&&""!=p&&(u='<div class="comment-quote-item">'+p+"</div>"),n)var g="";else var g="comment-line-box-hide";var v,w=n&&null!==t.redEnvelopeInfo,b=w?JSON.parse(t.redEnvelopeInfo):null;w&&(v=JSON.stringify({spm:"3001.9224",extend1:{orderNo:t.orderNo}}),b.money=parseFloat(parseInt(b.money)/100).toFixed(2));var x="匿名用户"!=t.userName?blogUrl+t.userName:"javascript:void(0);",$='<li class="comment-line-box '+g+'" data-commentid="'+t.commentId+'" data-replyname="'+t.userName+'">            <div class="comment-list-item">                <a class="comment-list-href" target="_blank" href="'+x+'"><img src="'+t.avatar+'" username="'+t.userName+'" alt="'+t.userName+'" class="avatar">'+(t.gptInfo?'<img class="tag" src="'+t.gptInfo.icon+'" alt="">':"")+'</a>                <div class="right-box '+(c?"reply-box":"")+'" >                    <div class="new-info-box clearfix"><div class="comment-top"><div class="user-box"><a class="name-href" target = "_blank" href = "'+x+'" > <span class="name '+(n?"":"mr-8")+'">'+(n?t.nickName+"</span></span>"+isBloger+"</a> "+(t.isTop&&a?' <span class="is_top comment_status_tip"> 置顶</span> ':""):t.nickName+"</span>"+isBloger+'</a> <span class="text">回复</span> <span class="nick-name"> '+(t.parentNickName?t.parentNickName:"")+"</span>")+'<span class="date"  title="'+t.postTime+'">'+t.dateFormat+'</span><div class="opt-comment"><a class="btn-bt  btn-report"><img class="btn-report-img" src="'+blogStaticHost+'dist/pc/img/commentLookMore.png" title=""><div class="hide-box">'+(m&&a?' <span class="hide-item hide-top" data-type="istop" data-nickname="'+t.nickName+'" data-status="'+!!t.isTop+'" data-report-click=\'{"spm": '+(t.isTop?'"1001.2101.3001.6901"':'"1001.2101.3001.6511"')+',"extend1":'+(t.isTop?'"取消置顶"':'"置顶评论"')+"}'>"+(t.isTop?"取消置顶":"置顶评论")+"</span>":"")+(a&&getCookie("UserName")===username&&t.userName!==getCookie("UserName")?' <span class="hide-item hide-blacklist" data-type="blacklist" data-username="'+t.userName+'" data-nickname="'+t.nickName+'" data-status="'+!!t.isBlack+'" data-report-click=\'{"spm": '+(t.isBlack?'"1001.2101.3001.6914"':'"1001.2101.3001.6912"')+',"extend1":'+(t.isTop?'"取消屏蔽"':'"屏蔽用户"')+"}'> "+(t.isBlack?"取消屏蔽":"屏蔽用户")+"</span> ":"")+(isOwner||getCookie("UserName")===t.userName?'<span class="hide-item hide-delete" data-type="delete" data-nickname="'+t.nickName+'">删除</span>':"")+'<span data-type="report" class="hide-item hide-report"> 举报</span></div></a>'+(3!==commentAuth&&4!==commentAuth?'<img class="comment_img_replay" src="'+blogStaticHost+"dist/pc/img/newCommentReply"+skinStatus+'.png">':"")+(3!==commentAuth&&4!==commentAuth?'<a class="btn-bt btn-reply" data-type="reply" data-flag="true">回复</a>':"")+"</div></div >"+(w?'<div class="comment-reward-item" data-report-click=\''+v+'\' data-uncheck="1" data-commentid="'+t.commentId+'" data-id="'+t.orderNo+'">        <span span class="amount">'+b.money+"元</span>        </div > ":"")+'<div class="comment-like '+(t.diggArr.indexOf(getCookie("UserName"))!=-1?"liked":"")+'" data-commentid='+t.commentId+" >"+(0!==parseInt(t.digg)?"<span>"+t.digg+"</span>":"<span></span>")+'<img class="comment-like-img unclickImg" src="'+blogStaticHost+"dist/pc/img/commentLike"+skinStatus+'.png" title="点赞"><img class="comment-like-img comment-like-img-hover" style="display:none" src="'+blogStaticHost+'dist/pc/img/commentLikeHover.png" title="点赞"><img class="comment-like-img clickedImg" src="'+blogStaticHost+'dist/pc/img/commentLikeActive.png" title="取消点赞"></div></div><div class="comment-center">'+u+'<div class="new-comment">'+this.setNewLine(s)+"</div>"+(t.gptInfo?'<div class="gpt-comment">'+t.gptInfo.text+"</div>":"")+"</div></div></div></div></li>";return $},e.prototype.setNewLine=function(t){var e=/\<pre name="code2" ([\s\S]*?)\>([\s\S]*?)\<\/pre\>/gi,o=[];if(t.replace(e,function(t){o.push(t)}),o.length)for(var n=t.replace(e,function(t){return"[csdnCommentPreCode]"}),i=n.replace(/\n/g,"</br>"),a=0;a<o.length;a++)i=i.replace("[csdnCommentPreCode]",o[a]);else var i=t.replace(/\n/g,"<br/>");return i},e.prototype.getReplyInfo=function(t){var e=t.split(/\[reply]([\s\S]*?)\[\/reply\][\r\n]{0,1}/gi);return e},e.prototype.getQuoteInfo=function(t){var e=t.split(/\[quote=([\w#\.]+)\]([\s\S]*?)\[\/quote\][\r\n]{0,2}/gi);return e},e.prototype.getCodeInfo=function(t){var e=t.replace(/\[code=([\w#\.]+)\]([\s\S]*?)\[\/code\]/gi,function(t,e,o){return""==$.trim(o)?"":'<pre name="code2" class="code2 '+e+'"><code>'+s(o.trim())+"</code></pre>"});return e},e.prototype.replaceNewUBB=function(t){return t=t.replace(/\[face\]([^\]]+):([^\]]+)\[\/face\]/gi,'<img src="//g.csdnimg.cn/static/face/$1/$2" alt="表情包"/>')},e.prototype.replaceUrl=function(t){var e=/(https?|http|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g,o=/\[code=([\w#\.]+)\]([\s\S]*?)\[\/code\]/gi,n=[];if(t.replace(o,function(t){n.push(t)}),n.length)for(var i=t.replace(o,function(t){return"[csdnCommentCode]"}),a=i.replace(e,function(t){return'<a class="comment-match-url" target="_blank" href="'+t+'"><img src="https://img-home.csdnimg.cn/images/20230310025252.png" alt=""> 网页链接</a>'}),s=0;s<n.length;s++)a=a.replace("[csdnCommentCode]",n[s]);else var a=t.replace(e,function(t){return'<a class="comment-match-url" target="_blank" href="'+t+'"><img src="https://img-home.csdnimg.cn/images/20230310025252.png" alt=""> 网页链接</a>'});return a},e.prototype.replaceQuote=function(t){return t=t.replace(/\[quote\]([\s\S]*?)\[\/quote\]/gi,"")},e.prototype.getQuote=function(t){var e="";return t.replace(/\[quote\]([\s\S]*?)\[\/quote\]/gi,function(t){e=t}),e=e.replace("[quote]","").replace("[/quote]","")},e.prototype.replayComment=function(t){var e=t.Id,o=t.User;this.txtComment.attr("placeholder","回复："+t.text).data("replayUser",o),this.commentTxt.val(e)},e.prototype.commentCode=function(){var t=this;t.commtCode.find("a").click(function(e){var o="[code="+$(this).data("code")+"]\n\n[/code]",n=t.txtComment.val();(o+n).length<=t.commentFontLimit?(t.txtComment.val(n+o),"comment_content"==t.txtComment[0].id&&$("#comment-content").val(n+o),t.commtCode.parents(".comment-code-box").fadeOut()):showToast({text:"您输入的字数已经超过限定了",bottom:"10%",zindex:9e3,speed:500,time:1500});var i=e||window.event;i.stopPropagation?i.stopPropagation():i.cancelBubble=!0})},e.prototype.comment=function(){function t(t){if(!getCookie("UserName"))return e.setStorage();window.localStorage.removeItem("AM_comment_data");var o=$(t).find(":submit"),n={commentId:e.commentTxt.val(),content:e.txtComment.val(),articleId:articleId};if("block"===w.formRewardBox.css("display")&&Object.assign(n,w.rewardInfo),n.replyId&&$.trim(n.content)&&(n.content="[reply]"+e.txtComment.data("replayUser")+"[/reply]"+n.content),$(t).find("#comment_content").length&&$("#commentQuote").length&&""!==$("#commentQuote").text()&&(n.content="[quote]"+$("#commentQuote").text().substr(0,50)+"[/quote]"+n.content),""===$.trim(n.content)||""===e.txtComment.val())showToast({text:"请填写评论内容",bottom:"10%",zindex:9e3,speed:500,time:1500});else{var i=blogUrl+"phoenix/web/v1/comment/submit";o.prop("disabled",!0).val("提交中"),$.ajax({url:i,type:"post",dataType:"json",data:n,xhrFields:{withCredentials:!0},success:function(t){if(200===t.code){if(e.isCommentFold)e.commentFold.pageIndex=1,$("#pcFlodCommentSideBox .comment-fold-content").html(""),e.getFoldList(e.commentFold);else{e.commentForm.trigger("reset"),e.commentTxt.val(""),e.txtComment.data("replayUser","").attr("placeholder","想对作者说点什么"),e.cancelBtnToggle(!1),u=t.data,e.init(),e.removeStorage();var o=$("#commentQuote");o.length&&o.animate({height:0},300,function(){o.remove()}),e.commentUnFold.pageIndex=1,$("#pcCommentSideBox .comment-list-box").html("")}"block"===w.formRewardBox.css("display")&&w.formRewardBox.find(".btn-remove-reward").trigger("click"),showToast({text:"评论成功,审核后显示",bottom:"10%",zindex:9e3,speed:500,time:2e3})}else showToast({text:t.message,bottom:"10%",zindex:9e3,speed:500,time:2e3})},complete:function(){o.prop("disabled",!1).val("评论")}})}return!1}var e=this;e.commentForm.submit(function(e){e.preventDefault();var o=this;try{window.csdn.loginBox.key({biz:"blog",subBiz:"other_service",cb:function(){t(o)}})}catch(n){t(o)}})},e.prototype.insertRightCommnetPrompt=function(){if(window.localStorage.getItem("AM_comment_Prompt_show"))return!1;window.localStorage.setItem("AM_comment_Prompt_show",!0);var t=$(".right_recommend_comment"),e=$(".right_recommend_comment_Prompt");e.length>0?e.fadeIn(300):t.prepend('<div class="right_recommend_comment_Prompt"><div class="text">新的评论在这里</div><div class="arrow"></div></div>'),setTimeout(function(){$(".right_recommend_comment_Prompt").fadeOut(500)},4e3)},e.prototype.setStorage=function(){return window.localStorage.setItem("AM_comment_data",this.txtComment.val()),window.localStorage.setItem("AM_comment_replyId",this.commentTxt.val()),window.localStorage.setItem("AM_comment_id",articleId),!!getCookie("UserName")||(window.csdn.loginBox.show(),!1)},e.prototype.removeStorage=function(){window.localStorage.removeItem("AM_comment_data"),window.localStorage.removeItem("AM_comment_replyId"),window.localStorage.removeItem("AM_comment_id")},e.prototype.bindLikedClick=a,e.prototype.bindRedRewardClick=i,e.prototype.cancelBtnToggle=function(t){t?this.cancelBtn.removeClass("d-none"):this.cancelBtn.addClass("d-none")},e.prototype.cancelBtnBindEvent=function(){var t=this;this.cancelBtn.on("click",function(){t.txtComment.attr("placeholder","想对作者说点什么").data("replayUser","").val(""),t.commentTxt.val(""),t.cancelBtnToggle(!1)})},e.prototype.commentIsBlack=function(t,e,o){var n=this,i="https://mp-action.csdn.net/interact/wrapper/pc/black/"+(o?"cancel":"save"),a=o?{username:t,usernameList:[e],bizNo:"blog"}:{username:t,blackUsername:e,bizNo:"blog"};$.ajax({url:i,type:"post",data:JSON.stringify(a),contentType:"application/json",dataType:"json",xhrFields:{withCredentials:!0},success:function(t){200===t.code?(setTimeout(function(){n.isCommentFold?(n.commentFold.pageIndex=1,$("#pcFlodCommentSideBox .comment-fold-content").html(""),n.getFoldList(n.commentFold)):(n.commentUnFold.pageIndex=1,$("#pcCommentSideBox .comment-list-box").html(""),n.getPagination(n.commentUnFold.pageIndex))},100),showToast({text:o?"取消屏蔽成功！":"屏蔽用户成功！",bottom:"10%",zindex:9e3,speed:500,time:1500})):showToast({text:o?"取消屏蔽失败，请刷新页面重新操作！":"屏蔽用户失败，请刷新页面重新操作！",bottom:"10%",zindex:9e3,speed:500,time:1500})}})},e.prototype.commentIsTop=function(t,e){var o=this,n=blogUrl+"phoenix/web/v1/comment/top",i={commentId:t,articleId:articleId,action:e?0:1};$.ajax({url:n,type:"post",dataType:"json",data:i,xhrFields:{withCredentials:!0},success:function(t){200===t.code?(setTimeout(function(){o.isCommentFold?(o.commentFold.pageIndex=1,$("#pcFlodCommentSideBox .comment-fold-content").html(""),o.getFoldList(o.commentFold)):(o.commentUnFold.pageIndex=1,$("#pcCommentSideBox .comment-list-box").html(""),o.getPagination(o.commentUnFold.pageIndex))},100),showToast({text:e?"取消置顶成功！":"置顶评论成功！",bottom:"10%",zindex:9e3,speed:500,time:1500})):showToast({text:e?"取消置顶失败，请重新操作！":"置顶评论失败，请重新操作！",bottom:"10%",zindex:9e3,speed:500,time:1500})}})},e.prototype.commentIsDelete=function(t){var e=this,o=$("#article_id").val(),n=blogUrl+"phoenix/web/v1/comment/delete",i={commentId:t,articleId:o};$.ajax({url:n,type:"post",dataType:"json",data:i,xhrFields:{withCredentials:!0},success:function(o){200===o.code?(setTimeout(function(){e.isCommentFold?(e.commentFold.pageIndex=1,$("#pcFlodCommentSideBox .comment-fold-content").html(""),e.getFoldList(e.commentFold)):(e.commentUnFold.pageIndex=1,$("#pcCommentSideBox .comment-list-box").html(""),e.getPagination(e.commentUnFold.pageIndex));var o=$("#pcCommentBox").find(".hot-comment-box");o.length>0&&o.data("commentid")===t&&(o.html(""),o.data("commentid",""),$("#pcCommentBox").hide())},100),showToast({text:"删除成功!",bottom:"10%",zindex:9e3,speed:500,time:1500})):showToast({text:"删除失败，请重新操作!",bottom:"10%",zindex:9e3,speed:500,time:1500})}})},$(document).on("keydown",".comment-content",function(t){var e=t||event;console.log(t),(e.ctrlKey&&13==e.which||13==e.which&&e.metaKey)&&("comment_content"==$(this).attr("id")?$("#commentform .btn-comment").trigger("click"):$("#commentformNew .btn-comment").trigger("click"))}),$(document).on("click",".second-look-more",function(t){$(this).hide(),$(this).siblings(".comment-line-box-hide").show()}),$(document).on("click",".comment-operate-item",function(t){if(getCookie("UserName")){if($(".comment-operate-isshow").fadeOut(),$(this).hasClass("comment-emoticon")){""==$(".comment-emoticon-img-box").html()&&$(".comment-emoticon-img-box").html(c()),$(this).find(".comment-emoticonNew").length&&$(this).find(".comment-operate-isshow").html($("#commentform .comment-emoticon-box").html());var e=$(this).find(".comment-emoticon-box");
"block"===e.css("display")?e.fadeOut():($("#comment_content").one("focus",function(){e.fadeOut()}),e.fadeIn())}else if($(this).hasClass("comment-code")){var o=$(this).find(".comment-code-box");"block"===o.css("display")?o.fadeOut():($("#comment_content").one("focus",function(){o.fadeOut()}),o.fadeIn()),!$(this).hasClass("comment-codeNew")}else $(this).hasClass("comment-reward")&&w.showRewardForm();var n=t||window.event;n.stopPropagation?n.stopPropagation():n.cancelBubble=!0}else csdn.loginBox.show()}),$(document).on("click",".emoticon-monkey-img",function(t){var e=$(this).data("emoticon");if($(this).parents(".comment-emoticon-box").is(".comment-emoticonNew")){var o=$("#comment_contentNew").val()+e;o.length<=1e3?textareaPointLocation.insertAtCaret($("#comment_contentNew")[0],e):showToast({text:"您输入的字数已经超过限定了",bottom:"10%",zindex:9e3,speed:500,time:1500})}else{var o=$("#comment_content").val()+e;o.length<=1e3?textareaPointLocation.insertAtCaret($("#comment_content")[0],e):showToast({text:"您输入的字数已经超过限定了",bottom:"10%",zindex:9e3,speed:500,time:1500})}$(this).parents(".comment-emoticon-box").fadeOut();var n=t||window.event;n.stopPropagation?n.stopPropagation():n.cancelBubble=!0}),$(document).on("mouseenter",".comment-like",function(){$(this).hasClass("liked")?($(this).find(".clickedImg").css("display","none"),$(this).find(".comment-like-img-hover").css("display","block")):($(this).find(".unclickImg").css("display","none"),$(this).find(".comment-like-img-hover").css("display","block"))}),$(document).on("mouseleave",".comment-like",function(){$(this).hasClass("liked")?($(this).find(".clickedImg").css("display","block"),$(this).find(".comment-like-img-hover").css("display","none")):($(this).find(".unclickImg").css("display","block"),$(this).find(".comment-like-img-hover").css("display","none"))}),$(document).on("click",".unlogin-comment-box-new .unlogin-comment-bt",function(){window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.6916"}),getCookie("UserName")||window.csdn.loginBox.show({spm:"1001.2101.3001.8611"})}),$(".blog-postTime").length>0){var m=$(".blog-postTime").data("time"),p="于 "+m+" 发布";$(".blog-postTime").html(p)}var u=r("#comments_");if(u&&!getCookie("UserName")){$("#pcCommentBox").show();var h=$(document).scrollTop(),f=$("#csdn-toolbar").height(),g=2*$("#pcCommentBox").height();h<52&&(f=2*f);var v=$("#pcCommentBox").offset().top-f-g;return setTimeout(function(){$("html,body").animate({scrollTop:v},200)},850),!1}var w=new t;window.csdn.comments?window.csdn.comments:{},window.csdn.Comments=e,window.csdn.comments=new window.csdn.Comments}),$(function(){if(1===articleType){var t=$("main .blog-content-box")[0],e=document.querySelector(".creativecommons span a");e=e?e.innerText:"";var o="";e?o="\r\n————————————————\r\n版权声明：本文为CSDN博主「"+nickName+"」的原创文章，遵循"+e+"版权协议，转载请附上原文出处链接及本声明。\r\n原文链接："+curentUrl:(e=document.querySelector(".creativecommons"),e=e?e.innerText:"",e?o="\r\n————————————————\r\n"+e+"\r\n原文链接："+curentUrl:(e="CC 4.0 BY-SA",o="\r\n————————————————\r\n版权声明：本文为CSDN博主「"+nickName+"」的原创文章，遵循"+e+"版权协议，转载请附上原文出处链接及本声明。\r\n原文链接："+curentUrl)),csdn.copyright.init(t,o)}if(copyPopSwitch&&!getCookie("UserName")){var n=function(){window.csdn.loginBox.show({spm:"1001.2101.3001.9440"})};$("#content_views").unbind("keydown").bind("keydown",function(t){if(t.ctrlKey&&67==t.keyCode)return n(),!1}),$("#content_views").unbind("copy").bind("copy",function(t){return n(),!1})}}),$(function(){function t(t,o){if(!(o.length<2)){var n="";n+="<ol>";for(var i=0,a=0,s=0,c=0,r=0,l=0,d=0;d<o.length;d++){var m=parseInt(o[d].tagName.substr(1),10);if(i||(i=m),m>i?(n+='<li class="sub-box"><ol>',a++):m<i&&a>0&&(n+="</ol></li>",a--),m==p)for(;a>0;)n+="</ol>",a--;i=m;var u=o.eq(d).text();if(u=/^[\s]+$/.test(u)?u:u.replace(/^[\s]+/g,""),u=e(u),u.length>0)switch(m){case 1:++s,c=0,r=0,l=0;var h="";1===s&&(h="active"),n+="<li class="+h+'><a href="#t'+d+'">'+u+"</a></li>";break;case 2:++c,r=0,l=0,n+='<li><a href="#t'+d+'">'+u+"</a></li>";break;case 3:++r,l=0,n+='<li><a href="#t'+d+'">'+u+"</a></li>";break;case 4:++l,n+='<li><a href="#t'+d+'">'+u+"</a></li>"}}for(;a>0;)n+="</ol>",a--;return n}}function e(t){return t.replace(/[<>&"]/g,function(t){return{"<":"&lt;",">":"&gt;","&":"&amp;",'"':"&quot;"}[t]})}function o(t){function e(t){return $(t).each(function(t,n){n.children&&e(n.children),++o}),o}var o=0;return e(t)}function n(t,e,o){var n=$("div.groupfile"),i=n.find("div.toc-box"),a=n.find("div.opt-box");if(e<=2&&($("#liTocBox").remove(),!isShowSideModel&&payColumn&&window.csdn.loadRightColumn()),e>2&&"0"==d&&$("#liTocBox").css("display","block"),e<=2&&"1"==d?($(".groupfile").remove(),$("#rightAsideConcision").remove()):isShowConcision?(isCookieConcision&&($(".recommend-right1").addClass("show-directory"),$("#mainBox").removeAttr("style")),isHasDirectoryModel=!0):$("#rightAsideConcision").remove(),e<=2&&($("#blog_artical_directory").hide(),$(".left_menu .menu_con").hide()),!$(".first_li")[0]){i.html(o(t,m)),s(e,a,i);var c=!1;$("#liTocBox").hover(function(){$("#liTocBox .toc-container").finish().fadeIn(500),$(this).find("button.btn-toc").addClass("active"),c=!1},function(t){c=!0;var e=$(this);setTimeout(function(){c&&($("#liTocBox .toc-container").finish().fadeOut(500),e.find("button.btn-toc").removeClass("active"))},300)})}}function i(){var t=document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop;t+=10;for(var e=0;e<f.length-1;e++){if(t<=f[0]){var o=$(h[0]).find("a")[0].name;o=o.replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/\{/g,"%7b").replace(/\}/g,"%7d");var n="#"+o,i=$("#groupfile").find('a[href="'+n+'"]')[0],a=$("#directory").find('a[href="'+n+'"]')[0],s=$("#groupfileConcision").find('a[href="'+n+'"]')[0];$(i).parent().addClass("active"),$(a).parent().addClass("active"),$(s).parent().addClass("active")}if(f[e]<=t&&t<=f[e+1]){var n="#"+$(h[e]).find("a")[0].name,i=$("#groupfile").find('a[href="'+n+'"]')[0],a=$("#directory").find('a[href="'+n+'"]')[0],s=$("#groupfileConcision").find('a[href="'+n+'"]')[0];$(i).parent().addClass("active"),$(a).parent().addClass("active"),$(s).parent().addClass("active"),$(i).length&&$("#groupfile .align-items-stretch").animate({scrollTop:$(i)[0].offsetTop+"px"},0),$(a).length&&$("#directory .align-items-stretch").animate({scrollTop:$(a)[0].offsetTop+"px"},0)}}}function a(){var t=$("#csdn-toolbar").height()+8;b+$(document).scrollTop()-($("aside.recommend-right_aside").height()+$("#csdn-toolbar").height()+8)>=180?($("#recommend-right").css({position:"fixed",top:t}),$("#recommend-right-concision").css({position:"fixed",top:t}),$(".rightside-fixed-hide").hide()):($("#recommend-right").removeAttr("style"),$("#recommend-right-concision").removeAttr("style"),$(".rightside-fixed-hide").show()),0==$(document).scrollTop()&&($(".rightside-fixed-hide").show(),$("#recommend-right").removeAttr("style"),$("#recommend-right-concision").removeAttr("style"))}function s(t,e,o){if(t>9){var n=25*$("div.toc-box").find("li:not(.sub-box)").length,i=225,a=n-i,s=0;e.find("button.btn-opt").click(function(){$(this).hasClass("nomore")||($(this).hasClass("prev")?(s-=25,o.scrollTop(s),s<=0&&$(this).addClass("nomore")):(s+=25,s>=25*(t-17)&&(s=25*(t-17)),o.scrollTop(s),s>=a&&$(this).addClass("nomore")),$(this).siblings().removeClass("nomore"))})}else e.remove()}function c(){function t(e){$(".openvippay")[0]||window.csdn.clearReadMoreBtn(),o.off("click",t)}function e(){var t=window.location.href,e=document.title;setTimeout(function(){history.pushState(null,e,t)},0)}var o=$(".toc-container .toc-box ol li a");return 0!==o.length&&(o.click(t),void o.click(e))}function r(t){var e="<ol>";return $(t).each(function(t,o){e+=l(o,t)}),e+"</ol>"}function l(t,e){return t.children.length?"<li><a>"+t.title+'</a></li><li class="sub-box">'+r(t.children)+"</li>":"<li><a>"+t.title+"</a></li>"}if(!window.hasOwnProperty("isGitCodeBlog")||!isGitCodeBlog){for(var d=1,m=$("#article_content").find("h1,h2,h3,h4"),p=$("#article_content").find("h1").length>0?1:2,u=0;u<m.length;u++)m.eq(u).html('<a name="t'+u+'"></a>'+m.eq(u).html());$(".vip_article")[0]?($.ajax({type:"GET",url:blogUrl+"/phoenix/web/v1/get-article-catalog?articleId="+articleId,dataType:"json",xhrFields:{withCredentials:!0},success:function(t){200==t.code&&(n(t.data,o(t.data),r),c(),$("#groupfile .toc-box ol li a").each(function(t,e){e.href="#t"+t}),$("#directory .toc-box ol li a").each(function(t,e){e.href="#t"+t}),$("#rightAsideConcision .toc-box ol li a").each(function(t,e){e.href="#t"+t}),""===String($(".groupfile .toc-box").text()).replace(/(^\s*)|(\s*$)/g,"")?($("#asidedirectory").remove(),$("#groupfile").remove(),$("#rightAsideConcision").remove(),isHasDirectoryModel=!1):$("#groupfile").show())}}),$("#downloadEduApp").click(function(){getCookie("UserName")?window.open("https://vip.csdn.net/studyvip?utm_source=zhuanlan"):window.csdn.loginBox.show()})):(n("",m.length,t),c(),""===String($(".groupfile .toc-box").text()).replace(/(^\s*)|(\s*$)/g,"")?($("#asidedirectory").remove(),$("#groupfile").remove(),$("#rightAsideConcision").remove(),isHasDirectoryModel=!1):$("#groupfile").show()),$(document).on("toolbarHeightChange",function(t){var e=$("#csdn-toolbar").height()+8;$("#recommend-right").css("position")&&$("#recommend-right").css({top:e})});var h=$("#content_views").find("h1,h2,h3,h4"),f=[],g=$("#content_views").offset().top,v=$("#content_views").height(),w=$("#csdn-toolbar").height();$(h).each(function(t,e){f.push($(e)[0].offsetTop+g-2*w)}),f.push(v+g),$(window).resize(function(){f=[],g=$("#content_views").offset().top,v=$("#content_views").height(),w=$("#csdn-toolbar").height(),$(h).each(function(t,e){f.push($(e)[0].offsetTop+g-2*w)}),f.push(v+g)}),$(window).scroll(function(){$(".groupfile .toc-box li").removeClass("active"),i()}),i(),$(document).on("click",".groupfile .toc-box li a",function(t){$(".groupfile .toc-box li").removeClass("active"),$(this).parent().addClass("active")});var b=document.body.clientHeight||document.documentElement.clientHeight;if($("#groupfile").css("max-height",b/2+"px"),$(".groupfile-div").css("max-height",b/2+"px"),$(".groupfile-div1").css("max-height",b-48+"px"),$(document).scroll(function(){a()}),a(),$(document).on("click",".csdn-side-toolbar .option-box",function(t){isShowConcision&&setTimeout(function(){a()},100)}),$(".hide-article-box").length&&"none"!==$(".hide-article-box").css("display")){for(var x=$(".hide-article-box").offset().top,y=0,k=0;k<f.length-1;k++)f[k]<=x&&x<=f[k+1]&&(y=k+1);var C=h.slice(y,h.length);$(".vip_article")[0]&&$(document).click(".groupfile .toc-box ol a",function(t){if($(t.target)[0].hash&&$(t.target)[0].hash.split("#t")&&parseInt($(t.target)[0].hash.split("#t")[1])>y){var e=$("#getVipUrl").attr("href");e.indexOf("vip")>-1?window.open(e+"?utm_source=brv&sale_source=BgsN7mSaYF"):window.open(e)}}),$(".btn-readmore")[0]&&C.each(function(t,e){$(".groupfile .toc-box ol").find('a[href="#'+$(C[t]).find("a")[0].name+'"]').click(function(){$(".btn-readmore").trigger("click"),$(".btn-readmore").hasClass("no-login")?window.open("https://passport.csdn.net/account/login"):$("#btnAttent").trigger("click")})})}$(document).ready(function(){var t=$(".htmledit_views #main-toc").prevAll("h1,h2,h3,h4"),e=t.length,o=$('.htmledit_views p[id*="-toc"]').slice(1).filter(function(t,e){return $(e).find("a").length||$(e).css({margin:"0"}),$(e).find("a").length}),n=0;o.each(function(t,o){$(o).attr("id","");var i=$(o).css("margin-left").replace("px","")/40,a=0==i?0:48*i;if($(o).css({"padding-left":"24px",margin:"0","margin-left":a+"px","margin-bottom":"2px"}),e>0)$(o).find("a").attr("href","#t"+e),e++;else{var s=$(o).find("a").attr("href").replace("#","");s=s.replace(/%28/g,"(").replace(/%29/g,")").replace(/%7b/g,"{").replace(/%7d/g,"}"),$('[id="'+s+'"]').is("h1,h2,h3,h4")?$(o).find("a").attr("href","#t"+(t-n)):n++}});var i=$(".markdown_views .toc a");i.length&&i.each(function(t,e){var o=$(e).attr("href");o&&$(e).attr("href",o.replace(/%/g,"_"))});var a=$(".markdown_views").find("h1 a,h2 a,h3 a,h4 a");a.length&&a.each(function(t,e){var o=$(e).attr("id");o&&$(e).attr("id",o.replace(/%/g,"_"))}),$(document).on("click","#content_views a[href*=#],div.toc-box a[href*=#],li.tool-item-comment a[href*=#]",function(){var t=$("#csdn-toolbar").height(),e=$(document).scrollTop();if(e<52&&(t=2*t),location.pathname.replace(/^\//,"")==this.pathname.replace(/^\//,"")&&location.hostname==this.hostname){var o=$('[id="'+this.hash.slice(1)+'"]');if(o=o.length&&o||$('[name="'+this.hash.slice(1)+'"]'),o.length){var n=o.offset().top;return $("html,body").animate({scrollTop:n-t},500),!1}}}),setTimeout(function(){if(window.location.href.indexOf("#comments")>-1){var t=$("#csdn-toolbar").height();$("html,body").animate({scrollTop:$("#commentBox").offset().top-t-48},500)}},1e3)})}}),$(function(){function t(t){var e=$(".article-resource-info-box");$.ajax({type:"GET",url:"https://download-console-api.csdn.net/plugin/blog/source/get",dataType:"json",data:{sid:t},xhrFields:{withCredentials:!0},success:function(t){if(200==t.code&&t.data&&t.data.hasOwnProperty("sid")){var o=t.data,n='\n            <a href="'+o.url+'" target="_blank" class="img-box">\n              <img src="'+o.fileTypeIcon+'" alt="'+o.title+'" class="file-icon">\n            </a>\n            <div class="info-box">\n              <a data-report-view=\'{"spm":"3001.9500"}\' data-report-click=\'{"spm":"3001.9500"}\' data-report-query=\'spm=3001.9500\' href="'+o.url+'" target="_blank">\n                <p class="title" title="'+o.title+'">'+o.title+'</p>\n                <p class="desc" title="'+o.description+'">'+o.description+'</p>\n              </a>\n            </div>\n            <div class="opt-box">\n              <a data-report-view=\'{"spm":"3001.9499"}\' data-report-click=\'{"spm":"3001.9499"}\' data-report-query=\'spm=3001.9499\' href="'+o.url+'" class="btn-resource-link" target="_blank">立即下载</a>\n            </div>\n          ';e.append(n),e.css({display:"flex"}),window.csdn.report.viewCheck()}else e.remove()},error:function(){e.remove()}})}resourceId&&t(resourceId)}),$(function(){function t(){return!!window.navigator.userAgent.toLowerCase().match(/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone)/i)}function e(){return!!window.navigator.userAgent.toLowerCase().match(/(csdn)/i)}function o(){try{var t=$(".abstract-content")[0],e=document.createRange();e.setStart(t,0),e.setEnd(t,t.childNodes.length);var o=(e.getBoundingClientRect().width,e.getBoundingClientRect().height),n=window.getComputedStyle(t,null),i=Number.parseInt(n.paddingTop,10)||0,a=Number.parseInt(n.paddingBottom,10)||0,s=i+a;if(o+s>t.clientHeight){if($(".abstract-ab").length<=0){var c="https://img-home.csdnimg.cn/images/20240708095038.png";"Black"==skinStatus&&(c="https://img-home.csdnimg.cn/images/20240708025423.png"),$(".ai-abstract-box").append('<P class="abstract-ab">展开  <img class="lock-img" src="'+c+'" alt=""></P>')}$(".abstract-ab").show()}else $(".abstract-ab").hide()}catch(r){}}function n(){var t="https://trae.com.cn?utm_source=community&utm_medium=csdn&utm_campaign=daima";$(".hljs-button").each(function(e){var o={spm:"3001.10436",dest:t,extra:{index:e,runIdx:-1}};$(this).addClass("add_def");var n=$('<button class="btn-code-notes '+i+'">AI写代码</button>');if($(this).hasClass("def")||$(this).hasClass("mddef")){var a=$(this).parents("pre").find(".code-edithtml"),s=a.find(".code-edithtml-box").data("reportClick");o.extra.runIdx=s?s.extra.index:-2,n.addClass("btn_def"),a.before(n)}else $(this).parents("pre").append(n);n.attr("data-report-view-hover",JSON.stringify(o)).attr("data-report-click",JSON.stringify(o))}),$(document).on("click","#content_views .btn-code-notes",function(){var e=document.createElement("a");e.style.display="none",e.href=t,e.target="_blank",document.body.appendChild(e),e.click(),document.body.removeChild(e)}),$(document).on("mouseenter","#content_views pre",function(){var t=$(this).find(".btn-code-notes");t&&t.length>0&&t.attr("data-report-view-hover")&&(window.csdn.report.reportView(JSON.parse(t.attr("data-report-view-hover"))),t.removeAttr("data-report-view-hover"))}),window.csdn.report.viewCheck()}var i="",a=("CSDN_"+articleId+"_CS",document.querySelectorAll("pre code"));document.querySelectorAll("div.htmledit_views").length>0?i="ckeditor":a.length>0&&(i="mdeditor");var s="gwzcw.8483411.8483411.8483411";"https://cloudstudio.net/auth/realms/cloudstudio/protocol/openid-connect/auth?client_id=open-csdn&scope=r:user:id%20c:code_segment&response_type=code&redirect_uri=https%3A%2F%2Fpassport.csdn.net%2Fcloudstudiologin?article_id="+articleId+"&state=CSDN&fromSource="+s+"&kc_idp_hint=&channel=CSDN-Run";t()||e()||window.addEventListener("DOMContentLoaded",function(){$("code.hljs").length<=0&&$("pre code").length<=0||n()});o(),window.addEventListener("resize",o),$(".ai-abstract").mouseleave(function(){$(this).removeClass("active")}),$(".abstract-ab").mouseenter(function(){$(".ai-abstract").addClass("active")})}),$(function(){var t=$('#article_content a[href^="#"]');t.each(function(t){$(this).attr("target","_self")})}),$(function(){function t(t,e){var o=e?window.csdn.report.getFullSpm(e):"",n="https://link.csdn.net?from_id="+articleId+"&target="+encodeURIComponent(t+"?utm_source=artical_gitcode"+(o?"&spm="+o:""));return n}function e(t){var e=[{unit:"K",base:1e3},{unit:"M",base:1e6}],o=2;try{var n=e;return n.reduce(function(e,n){var i=n.base,a=n.unit;if(t>=i){var s=Math.pow(10,o);e=""+parseFloat((Math.round(t*s/i)/s).toFixed(o))+a}return e},""+t)}catch(i){}return""+t}function o(o){var n=$("#gitAsideProjectInfo"),i=$("#gitProjectInfo"),a=(e(o.star_count),e(o.forks_count),t(o.web_url,"3001.10288")),s=t(o.web_url,"3001.10290"),c=function(t){return JSON.stringify({spm:"3001.10288",dest:a,extra:{type:t}})};if(n){var r=c("title"),l=c("fork"),d=c("star"),m=c("clone"),p=c("source"),u=c("more");n.find(".title a").attr("href",a).attr("data-report-click",r).text(o.name).attr("title",o.name),n.find("a.link-more").attr("href",a).attr("data-report-click",u),n.find("a.btn-star").attr("href",a).attr("data-report-click",d).find("span.count").text(e(o.star_count)),n.find("a.btn-fork").attr("href",a).attr("data-report-click",l).find("span.count").text(e(o.forks_count)),n.find("a.btn-clone").attr("href",a).attr("data-report-click",m),n.find("a.btn-source").attr("href",a).attr("data-report-click",p),n.show()}if(i){var h=JSON.stringify({spm:"3001.10290",dest:s});i.find("a").attr("href",s).attr("data-report-click",h).attr("data-report-view",h),i.find(".title").text("⚡️Github加速计划 / "+o.name).attr("title",o.name),i.find(".desc").text(o.description?o.description:"").attr("title",o.description?o.description:""),i.find(".icon-start").text(e(o.star_count)),i.find(".icon-fork").text(e(o.forks_count)),i.show(),window.csdn.report.viewCheck()}}function n(o){var n=$("#gitRelatedProjects");if(n){var i=n.find(".gitcode-related-box"),a="",s=n.find("a.link-more");s&&s.attr("href",t("https://gitcode.com"));for(var c=o.length>5?5:o.length,r=0;r<c;r++){var l=o[r],d=t(l.web_url,"3001.10289"),m=JSON.stringify({spm:"3001.10289",dest:d,extra:{index:r}}),p=e(l.star_count);a+='\n        <div class="gitcode-item-box">\n          <a href="'+d+'" target="_blank" data-report-view=\''+m+"' data-report-click= '"+m+'\'>\n            <div class="title-box">\n              <p class="title" title="'+l.name+'">'+l.name+'</p>\n              <span class="start">'+p+"</span>\n            </div>\n            "+(l.description?'<p class="desc" title="'+l.description+'">'+l.description+"</p>":"")+"\n          </a>\n        </div>\n      "}""!==a&&(i.html(a),window.csdn.report.viewCheck()),n.show()}}function i(){$.ajax({url:"https://datalinkapi.gitcode.com/api/v2/projects/byCArticleId",type:"get",data:{articleId:articleId},xhrFields:{crossDomain:!0,withCredentials:!0},success:function(t){if(t&&t.name){try{a(t.name)}catch(e){$("#gitRelatedProjects").remove()}try{o(t)}catch(e){$("#gitAsideProjectInfo").remove()}}else{$("#gitAsideProjectInfo").remove();try{a(articleTitle)}catch(e){$("#gitRelatedProjects").remove()}}},error:function(){$("#gitAsideProjectInfo").remove();try{a(articleTitle)}catch(t){$("#gitRelatedProjects").remove()}}})}function a(t){$.ajax({url:"https://web-api.gitcode.com/api/v1/search/nauth/query",type:"get",data:{q:t,type:"repo",p:1,pp:10,o:"desc"},xhrFields:{crossDomain:!0,withCredentials:!0},success:function(t){if(t&&t.content&&t.content.length>0)try{n(t.content)}catch(e){$("#gitRelatedProjects").remove()}else $("#gitRelatedProjects").remove()},error:function(){$("#gitRelatedProjects").remove()}})}window.hasOwnProperty("isGitCodeBlog")&&isGitCodeBlog&&i()}),function(){function t(t){var e="",o="",a="";i&&(e="active"),t.type?(o='<span class="curriculum-box">作者：'+t.author+'</span>            <span class="curriculum-box">'+actionNumberFormat(t.learnNum)+'人已学</span>            <a class="item-target article-column-bt join-huawei-community">课程详情</a>',a=" curriculum-active"):o='<a class="item-target article-column-bt join-huawei-community">加入社区</a>';var s='<div class="column-group '+e+'" data-id="'+t.id+'" data-url="'+t.nsurl+'" data-report-click=\'{"spm":"1001.2101.3001.8542"}\' data-report-view=\'{"spm":"1001.2101.3001.8542"}\'>                    <div class="column-group-item '+a+'">                      <div class="item-l">                        <a class="item-target" href="javascript:;" title="'+t.title+'">                          <img class="item-target" src="'+t.logo+'" alt="">                          <span class="title item-target">                              <span>                              <span class="tit">'+t.title+'</span>                              <span class="dec">'+t.desc+'</span>                              </span>                          </span>                        </a>                      </div>                      <div class="item-r">'+o+"</div>                    </div>                  </div>";n.innerHTML=s}function e(){$.ajax({url:"https://devpress-api.csdn.net/api/internal/blog/nsInfo/blog/"+articleId,type:"GET",xhrFields:{withCredentials:!0},success:function(e){200==e.code&&e.data?t(e.data):n.remove()}})}function o(t,e){$.ajax({type:"POST",url:"https://devpress-api.csdn.net/v1/user/follow",contentType:"application/json; charset=utf-8",data:JSON.stringify({followTarget:t,type:1}),xhrFields:{withCredentials:!0},success:function(t){window.open(e)},error:function(t){window.open(e)}})}var n=document.getElementById("blogHuaweiyunAdvert"),i=document.getElementById("blogColumnPayAdvert");n&&(e(),$(document).on("click","#blogHuaweiyunAdvert .column-group",function(){if(getCookie("UserName")){var t=$(this).data("id"),e=$(this).data("url");o(t,e)}else window.csdn.loginBox.show()}))}(),$(".comment-box textarea.comment-content").on("select",function(){textareaPointLocation.setCaret(this)}).on("click",function(){textareaPointLocation.setCaret(this)}).on("keyup",function(){textareaPointLocation.setCaret(this)});var textareaPointLocation={setCaret:function(t){t.createTextRange&&(t.caretPos=document.selection.createRange().duplicate())},insertAtCaret:function(t,e){if(document.all)if(t.createTextRange&&t.caretPos){var o=t.caretPos;o.text=" "==o.text.charAt(o.text.length-1)?e+" ":e}else t.value=e;else if(t.setSelectionRange){var n=t.selectionStart,i=t.selectionEnd,a=t.value.substring(0,n),s=t.value.substring(i);t.value=a+e+s}else showToast({text:"This version of Mozilla based browser does not support setSelectionRange",bottom:"10%",zindex:9e3,speed:500,time:1500})}};$(function(){function t(){function t(){$("#publicPrompt").remove(),$(document).off("click",".publicPrompt-close",t),$(document).off("click",".publicPrompt-mask",t)}var e={success:'<svg class="icon success" aria-hidden="true"><use xlink:href="#csdnc-check"></use></svg>'};this.data={},this.data.status="success",this.data.titleStr="收藏成功",this.data.textStr='已收藏至 <a href="https://i.csdn.net/#/uc/favorite-list" target="_blank">个人中心</a>',this.data.imgUrl="",this.init=function(t){return $.extend(this.data,t),this.addStyle(),this.bindDom(),this},this.show=function(t){$.extend(this.data,t),this.insetStructure(this.data)},this.bindDom=function(){$(document).on("click",".publicPrompt-close",t),$(document).on("click",".publicPrompt-mask",t)},this.insetStructure=function(t){var o='<div id="publicPrompt"><div class="publicPrompt-mask"></div><div class="publicPrompt-content"><div class="publicPrompt-title-box"><h3 class="publicPrompt-title">'+e[t.status]+t.titleStr+'</h3><span class="publicPrompt-close"><svg class="icon" aria-hidden="true"><use xlink:href="#csdnc-times"></use></svg></span><div class="publicPrompt-text">'+t.textStr+'</div><img class="publicPrompt-qr" src="'+t.imgUrl+'" alt=""><div class="publicPrompt-footer-text">下载APP随时查看</div></div></div></div>';$("body").append(o)},this.addStyle=function(){var t=document.createElement("style");t.type="text/css",t.innerHTML=["#publicPrompt{","position: fixed;z-index: 99999;top: 0;left: 0;width: 100%;height: 100%;","}","#publicPrompt .publicPrompt-mask{","position: absolute;top: 0;left: 0;width: 100%;height: 100%;","background-color: rgba(0,0,0,0.5);","}","#publicPrompt .publicPrompt-content{","position: absolute;z-index: 2;top: 30%;left: 50%;margin-left: -175px;","background-color: #fff;width: 350px;padding: 16px;","}","#publicPrompt .publicPrompt-content .publicPrompt-title{","font-size: 16px;color: #3D3D3D;","}","#publicPrompt .publicPrompt-title svg{margin-right:4px;}","#publicPrompt .publicPrompt-title .success{fill:#89cb62;width: 20px;height: 20px;vertical-align: sub;}","#publicPrompt .publicPrompt-close{","position: absolute;top: 16px;right: 16px;cursor: pointer;","}","#publicPrompt .publicPrompt-text{","text-align: center;margin-top: 16px;margin-bottom: 16px;font-size: 14px;","}","#publicPrompt .publicPrompt-text a{","color: #3399EA;text-decoration: underline;","}","#publicPrompt .publicPrompt-qr{","display: block;width: 116px;height: 116px;padding: 8px;margin: 0 auto;border: 1px solid #EBEBEB;","}",".publicPrompt-footer-text{","font-size: 14px;color: #4D4D4D;margin-top:8px;text-align: center;","}"].join(""),document.getElementsByTagName("head")[0].appendChild(t)}}window.csdn?window.csdn:{},window.csdn.publicPrompt=t}),$(function(){function t(t,e){l.hide(),m=t,m?c.text("您举报的评论来自文章："):c.text("举报内容："),r.css({"z-index":9999}).show(),s.css({"z-index":1e4}).show()}function e(){s.find("#frmReport").trigger("reset"),r.css({"z-index":150}).hide(),s.hide(),$(".ipt-textarea").val(""),$(".box-botoom ul li").removeClass("box-active"),$(".box-content-bottom ul li").removeClass("box-active"),$(".box-content").eq(0).show().siblings().hide(),$("#cllcont").hide(),$(".content-input").val(""),flag=!1,i="",a=""}function o(t){showToast({text:t,bottom:"10%",zindex:9999,speed:500,time:1500})}var n=' <div class="report-box">  <div class="pos-boxer">      <div class="pos-content">          <div class="box-title">              <p>举报</p>              <img class="icon btn-close" src="'+blogStaticHost+'dist/pc/img/closeBlack.png">          </div>          <div class="box-header">              <div class="box-top"><span>选择你想要举报的内容（必选）</span></div>              <div class="box-botoom">                  <ul>                      <li data="1"  type="nei">内容涉黄</li>                      <li data="2" type="nei">政治相关</li>                      <li data="3" type="nei">内容抄袭</li>                      <li data="4" type="nei">涉嫌广告</li>                      <li data="5" type="nei">内容侵权</li>                      <li data="6" type="nei">侮辱谩骂</li>                      <li data="8" type="nei">样式问题</li>                      <li data="7" type="nei">其他</li>                  </ul>              </div>          </div>          <div>          <div class="box-content" >          </div>          <div class="box-content" >          </div>                    <div class="box-content" style="display:none;">                  <div class="box-content-top">                          <span>原文链接（必填）</span>                      </div>                      <div class="box-content-bottom" style="padding-bottom: 16px;">                        <div class="box-input" style="height: 32px;line-height: 32px;">                        <input class="content-input" type="text" id="originalurl" name="originalurl" placeholder="请输入被侵权原文链接">                        </div>                      </div>          </div>          <div class="box-content" >          </div>          <div class="box-content" style="display:none;">                  <div class="box-content-top">                          <span>请选择具体原因（必选）</span>                      </div>                  <div class="box-content-bottom">                          <ul>                              <li sub_type="1">包含不实信息</li>                              <li sub_type="2">涉及个人隐私</li>                          </ul>                      </div>          </div>          <div class="box-content" style="display:none;">                  <div class="box-content-top">                          <span>请选择具体原因（必选）</span>                      </div>                  <div class="box-content-bottom">                          <ul>                              <li sub_type="1">侮辱谩骂</li>                              <li sub_type="2">诽谤</li>                          </ul>                  </div>          </div>          <div class="box-content" style="display:none;">                <div class="box-content-top">                        <span>请选择具体原因（必选）</span>                    </div>                <div class="box-content-bottom">                        <ul>                            <li sub_type="1">搬家样式</li>                            <li sub_type="2">博文样式</li>                        </ul>                </div>          </div>          <div class="box-content" style="display:none;">          </div>          </div>            <div id="cllcont" style="display:none;">            <div class="box-content-top">              <span class="box-content-span">补充说明（选填）</span>            </div>                <div class="box-content-bottom">                  <div class="box-input" >                    <textarea class="ipt ipt-textarea" style="padding:0;"  name="description"  placeholder="请详细描述您的举报内容"></textarea>                  </div>                </div>            </div>            </div>      <div class="pos-footer">          <p class="btn-close">取消</p>          <p class="box-active">确定</p>      </div>  </div></div>';$("body").append(n);var i="",a="";$(".box-botoom ul li").on("click",function(){flag=!1,$(this).addClass("box-active").siblings().removeClass("box-active"),i=$(this).attr("data"),$(".content-input").val(""),$(".box-content").eq($(this).index()).show().siblings().hide(),"6"==$(this).attr("data")?$("#cllcont").hide():$("#cllcont").show(),"8"==$(this).attr("data")?$("#cllcont").hide():$("#cllcont").show(),$(".ipt-textarea")[0].value="",$(".box-content-bottom ul li").removeClass("box-active"),"7"==$(this).attr("data")?$(".box-content-span").html("补充说明（必填）"):$(".box-content-span").html("补充说明（选填）"),a=""}),$(".box-content-bottom ul li").on("click",function(){$(this).addClass("box-active").siblings().removeClass("box-active"),a=$(this).attr("sub_type"),flag=!0});var s=$("div.report-box"),c=s.find("#reptTit"),r=$("div.mask-dark"),l=$("#rptOriginalurl"),d=s.find('textarea[name="description"]'),m=!1,p=0;s.find(".btn-close").click(e),r.click(e);var u="";$(".box-active").on("click",function(){if(!i)return o("请选择你想要举报的内容！"),!1;switch(i){case"3":if(u=$("#originalurl").val(),""==u||"http://"==u)return o("举报抄袭必须提供原创文章地址！"),$("#originalurl").focus(),!1;break;case"7":if(u=d.val(),!u)return o("请填写补充说明！"),d.focus(),!1;break;case"5":if(!flag)return o("请选择具体原因"),!1;break;case"6":if(!flag)return o("请选择具体原因"),!1;break;case"8":if(!flag)return o("请选择具体原因"),!1}var t={articleId:articleId,commentId:m?p:"",subType:a,type:i,originalUrl:u,
description:d.val()},n=m?"report-comment":"report-article";$.ajax({url:blogUrl+"/phoenix/web/v1/"+n,type:"post",dataType:"json",data:t,xhrFields:{withCredentials:!0},success:function(t){200==t.code?(e(),sessionStorage.removeItem("usename"),sessionStorage.removeItem("articleId"),o("感谢您的举报，我们会尽快审核！"),$(".ipt-textarea").val(""),$(".box-botoom ul li").removeClass("box-active"),$(".box-content-bottom ul li").removeClass("box-active"),$(".box-content").eq(0).show().siblings().hide(),$("#cllcont").hide(),$(".content-input").val(""),flag=!1,i="",a="",u="",e()):o("举报失败")}})}),$("div.comment-box").on("click","a.btn-report",function(){p=$(this).parents("li.comment-line-box").data("commentid")}),window.showReport=t}),$(function(){}),$(function(){function t(){$("code.has-numbering").each(function(t,e){$(e).css("position","absolute"),$(e).parent("pre.prettyprint").css({position:"relative",height:$(e).outerHeight()+20+"px"})})}window.csdn=window.csdn?window.csdn:{},window.csdn.setSafariCodestyle=t}),$(function(){0==$(".btn-readmore").length&&($("#content_views").find("pre").each(function(t,e){e=$(e),e.find("code").height()>340?(e.addClass("set-code-hide"),e.append('<div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view=\'{"spm":"1001.2101.3001.7365"}\'><img class="look-more-preCode contentImg-no-view" src="'+blogStaticHost+"dist/pc/img/newCodeMore"+skinStatus+'.png" alt="" title=""></span></div>')):e.addClass("set-code-show")}),$(window).resize().scroll(),$(document).on("click",".hide-preCode-bt",function(){return $(this).parents("pre").removeClass("set-code-hide").addClass("set-code-show"),$(this).parents(".hide-preCode-box").hide().remove(),$(window).resize().scroll(),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.7365"}),!1}))}),$(function(){}),window.csdn.insertcallbackBlock=computePos,$(function(){function t(){var t="Black"===skinStatus?"-dark":"",o=blogStaticHost+"dist/pc/img/sideSearch.png";o="exp"!==sideToolbarResult?o:blogStaticHost+"dist/pc/img/toolbar/search"+t+".png";var n=document.referrer;if(n&&n.indexOf("so.csdn.net")>-1){var i='<a class="option-box search" data-type="search">        <img src="'+o+'" alt="" srcset="">        <span class="show-txt">搜索</span>      </a>';$(".csdn-side-toolbar").prepend(i);var a='<div class="side-search-box"><div class="side-search-content">          <span class="search-txt">'+c+'</span><span class="search-bt no" data-type="no">否</span><span class="search-bt yes" data-type="yes">是</span>        </div><div>';if($(".csdn-side-toolbar .search").append(a),!getCookie("referrer_search")){var s=0;timer=setInterval(function(){s++,3==s&&$(".side-search-box").fadeIn(1500),7==s&&($(".side-search-box").fadeOut(1500),clearInterval(timer))},1e3),e("referrer_search",(new Date).getTime(),2)}$(".csdn-side-toolbar .search").on("click",function(t){getCookie("referrer_search")||clearInterval(timer),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"3001.5543",dest:articleDetailUrl,extend1:c}),$(".side-search-box").fadeIn(500),setTimeout(function(){$(".side-search-box").fadeOut(1500)},1e4)}),$(".side-search-box .search-bt").on("click",function(t){getCookie("referrer_search")||clearInterval(timer);var e="";e="yes"===$(t.target).data("type")?"yes":"no",window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&(window.csdn.report.reportClick({spm:"3001.5543",dest:articleDetailUrl,extend1:c,ab:e}),t.stopPropagation()),$(".side-search-box .side-search-content").html("感谢您的反馈!").css({width:"92px"}),setTimeout(function(){$(".csdn-side-toolbar .search").fadeOut(800,function(){$(this).remove()})},1e3)})}}function e(t,e,o){var n=new Date;n.setTime(n.getTime()+36e5*o),document.cookie=t+"="+escape(e)+";expires="+n.toGMTString()+";domain=.csdn.net;path=/"}function o(t,e){r&&t>.5*e&&$(".csdn-side-toolbar .side-question-box").length>0&&(getCookie("blog_ask_question")||($(".csdn-side-toolbar .side-question-box").fadeIn(),d&&(window.csdn.report&&"function"==typeof window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.6041"}),d=!1)))}function n(){if(isShowConcision){var t='<a class="option-box sidecolumn sidecolumn-show" data-type="show" style="display:'+(isShowSideModel?"flex":"none")+'" data-report-view=\'{"spm":"3001.7788"}\' data-report-click=\'{"spm":"3001.7788"}\'>        <img src="'+blogStaticHost+'dist/pc/img/iconShowSide.png" alt="" srcset=""><img style="display:block" class="icon-option-beta" src="'+blogStaticHost+'dist/pc/img/iconSideBeta.png" alt="" srcset="">        <span class="show-txt">显示</br>侧栏</span>      </a>',e='<a  class="option-box sidecolumn sidecolumn-hide" data-type="hide" style="display:'+(isShowSideModel?"none":"flex")+'" data-report-view=\'{"spm":"3001.7789"}\' data-report-click=\'{"spm":"3001.7789"}\'>        <img src="'+blogStaticHost+'dist/pc/img/iconHideSide.png" alt="" srcset=""><img style="display:block" class="icon-option-beta" src="'+blogStaticHost+'dist/pc/img/iconSideBeta.png" alt="" srcset="">        <span class="show-txt">隐藏</br>侧栏</span>      </a>';$(".csdn-side-toolbar").prepend(t+e)}}function i(){var t="Black"===skinStatus?"-dark":"";if(isShowConcision){var e='<a class="option-box sidecolumn sidecolumn-show" data-type="show" style="display:'+(isShowSideModel?"flex":"none")+'" data-report-click=\'{"spm":"3001.10370","extra":{"type":"unfull"}}\'>        <img src="'+blogStaticHost+"dist/pc/img/toolbar/unfull"+t+'.png" alt="" srcset="">        <span class="show-txt">显示侧栏</span>      </a>',o='<a  class="option-box sidecolumn sidecolumn-hide" data-type="hide" style="display:'+(isShowSideModel?"none":"flex")+'" data-report-click=\'{"spm":"3001.10370","extra":{"type":"full"}}\'>        <img src="'+blogStaticHost+"dist/pc/img/toolbar/full"+t+'.png" alt="" srcset="">        <span class="show-txt">隐藏侧栏</span>      </a>';$(".csdn-side-toolbar").prepend(e+o)}}function a(){var t="Black"===skinStatus?"-dark":"",e='<a href="https://so.csdn.net/chat?c_tab=chat&c_model=ds&utm_source=vip_chatgpt_common_blog_detail&spm=1001.2101.3001.10583" id="sidecolumn-deepseek" class="option-box sidecolumn sidecolumn-deepseek" data-type="show" data-report-view=\'{"spm":"3001.10583"}\'  data-report-click=\'{"spm":"3001.10583"}\'>        <img src="'+blogStaticHost+"dist/pc/img/toolbar/Group"+t+'.png" alt="" srcset="">        <span class="show-txt">点击体验</br>DeepSeekR1满血版</span>      </a>';$(".csdn-side-toolbar").prepend(e),window.csdn.report.viewCheck()}function s(){p=setInterval(function(){if(window.csdn&&window.csdn.toolbarData&&window.csdn.toolbarData.topicData&&(clearInterval(p),window.csdn.toolbarData.topicData.length>0)){var t=blogStaticHost+"dist/pc/img/"+("Black"!==skinStatus?"btnGuideSide1":"btnGuideSide2")+".gif",e='href="https://mp.csdn.net/mp_blog/manage/creative"',o="3001.9732",n='<div class="sidetool-writeguide-box">\n            <a class="btn-sidetool-writeguide" data-report-view=\'{"spm":"'+o+'"}\' data-report-query="spm='+o+'" '+e+' target="_blank" data-report-click=\'{"spm":"'+o+'","extra": '+JSON.stringify({type:"monkey"})+"}'>\n              <img src=\""+t+'" alt="创作活动">\n            </a>\n            <div class="tip-box">\n              <a class="to-activity-list" data-report-click=\'{"spm":"'+o+'","extra": '+JSON.stringify({type:"tip"})+"}' data-report-query=\"spm="+o+'" '+e+' target="_blank">点我去创作中心查看更多活动~</a>\n            </div>\n          </div>';$(".csdn-side-toolbar").prepend(n),window.csdn.report.viewCheck(),setTimeout(function(){$(".sidetool-writeguide-box .tip-box").remove()},5e3)}},500)}window.getSideToolbarTime=setInterval(function(){$(".csdn-side-toolbar").length>0&&(clearInterval(getSideToolbarTime),t(),"exp"!==sideToolbarResult?(s(),n()):i(),"exp1"===sideToolbarDeepseek&&a())},1e3);var c="此内容解决你搜索的问题？";"control"===showSearchText&&(c="此内容是您要找的内容？"),"secondText"===showSearchText&&(c="此内容解决你搜索的问题？");var r=!0,l=!0,d=!0,m=document.documentElement.clientHeight;window.onscroll=function(){var t=document.documentElement.scrollTop||document.body.scrollTop;o(t,m)},$(document).on("click",".csdn-side-toolbar .fade-question-box",function(){$(".csdn-side-toolbar .side-question-box").fadeOut(),e("blog_ask_question",(new Date).getTime(),72),r=!1}),$(document).on("mouseenter",".csdn-side-toolbar .question",function(t){$(".csdn-side-toolbar .side-question-box").fadeIn(),$(t.target).hasClass("ask_question_img")&&l&&"block"==$(".csdn-side-toolbar .side-question-box").css("display")&&(window.csdn.report&&"function"==typeof window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.6041"}),l=!1)}),$(document).on("mouseleave",".csdn-side-toolbar .question",function(t){l=!0}),$(document).on("click",".csdn-side-toolbar .question",function(t){window.open("https://ask.csdn.net/new?spm=1001.2101.3001.6040")}),$(document).on("click",".csdn-side-toolbar .side-question-box",function(t){t.stopPropagation()}),$(document).on("click",".csdn-side-toolbar .sidecolumn",function(t){var e=$(this).data("type");"show"==e?(isShowDirectoryModel||($("#rightAsideConcision").removeClass("show-directory"),$(".directory.directory-hide").hide(),$(".directory.directory-show").show()),$("#mainBox").removeAttr("style"),isShowSideModel=!1,$(".main_father").removeClass("mainfather-concision"),$(".main_father .container").removeClass("container-concision"),$(".sidecolumn.sidecolumn-show").hide(),$(".sidecolumn.sidecolumn-hide").show(),setCookieBaseHour("blog_details_concision",(new Date).getTime(),24)):(isShowDirectoryModel?$("#mainBox").css("margin-right","0"):($("#rightAsideConcision").addClass("show-directory"),$(".directory.directory-hide").show(),$(".directory.directory-show").hide(),$("#mainBox").removeAttr("style")),$(".main_father").addClass("mainfather-concision"),$(".main_father .container").addClass("container-concision"),isShowSideModel=!0,$(".sidecolumn.sidecolumn-hide").hide(),$(".sidecolumn.sidecolumn-show").show(),setCookieBaseHour("blog_details_concision",0,24)),window.csdn.fixedSidebar.stopListener=!$(".main_father").hasClass("mainfather-concision")}),$(document).on("click",".csdn-side-toolbar .directory",function(t){var e=$(this).data("type");"show"==e?(isShowSideModel||($("#rightAsideConcision").addClass("show-directory"),$(".main_father").addClass("mainfather-concision"),$(".main_father .container").addClass("container-concision"),$(".sidecolumn.sidecolumn-hide").hide(),$(".sidecolumn.sidecolumn-show").show()),$("#mainBox").removeAttr("style"),isShowDirectoryModel=!1,$(".directory.directory-show").hide(),$(".directory.directory-hide").show(),$("#rightAsideConcision").addClass("show-directory")):(isShowDirectoryModel=!0,$("#mainBox").css("margin-right","0"),$(".directory.directory-hide").hide(),$(".directory.directory-show").show(),$("#rightAsideConcision").removeClass("show-directory")),window.csdn.fixedSidebar.stopListener=!$(".main_father").hasClass("mainfather-concision"),setCookieBaseHour("blog_details_concision",0,24)});var p;if($(".swiper-remuneration-container").length>0){var u=function(){$(".swiper-remuneration-container .swiper-slide-active a").attr("data-report-swiper")&&(window.csdn.report.reportView(JSON.parse($(".swiper-remuneration-container .swiper-slide-active a").attr("data-report-swiper"))),$(".swiper-remuneration-container .swiper-slide-active a").removeAttr("data-report-swiper"))},h=new Swiper(".swiper-remuneration-container",{autoplay:{delay:3e3,disableOnInteraction:!1},loop:!0,mousewheel:!0,pagination:{el:".swiper-remuneration-pagination",clickable:!0},navigation:{nextEl:".swiper-remuneration-button-next",prevEl:".swiper-remuneration-button-prev"}}),f=document.querySelector(".swiper-remuneration-container"),g=h.slides.length;f.addEventListener("mouseenter",function(){h.autoplay.stop()}),f.addEventListener("mouseleave",function(){g>1&&h.autoplay.start()}),g<=1&&h.autoplay.stop();var v=g-2,w=!0;setTimeout(function(){u()},500),h.on("slideChange",function(){w&&setTimeout(function(){u()},500),h.realIndex+1==v&&(w=!1)})}});var getRecommendListUrlArr=null;$("#recommend-item-box-tow").children().length<=0&&$("#recommend-item-box-tow").remove(),baiduKey?csdn.afterCasInit=function(t,e){if(!t)return!1;var o="";4==articleSource&&$("#recommendDown .recommend_down").length<=0?csdn.baiduSearch(t,function(e){if(needInsertBaidu&&isRecommendModule)if(e&&e.length)showDownRecommend(e[0]);else{var n=$(".recommend-box .type_download")[0];if(n&&$(n).length){var i=$(n).find("a")[0],a={linkUrl:$(n).data("url"),title:$(i).text()};showDownRecommend(a)}}csdn.baiduSearch(t,function(t){needInsertBaidu&&isRecommendModule?(showResult(t,o),getQueryIdx(),reportTop10()):(getQueryIdx(),reportTop10())})},"download.csdn.net"):csdn.baiduSearch(t,function(t){needInsertBaidu&&isRecommendModule?(showResult(t,o),getQueryIdx(),reportTop10()):(getQueryIdx(),reportTop10())})}:(getQueryIdx(),reportTop10());var articleTitleContent=$("#articleContentId").text().replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、<em></em>&quot;]/g,"").replace(/\s/g,"").toLowerCase();$(function(){function t(){$.ajax({type:"POST",url:blogUrl+"phoenix/web/v1/vip-article-read",contentType:"application/json; charset=utf-8",xhrFields:{withCredentials:!0},data:JSON.stringify({articleId:articleId}),success:function(t){200==t.code&&console.log("vip文章阅读完成，成功上报")},error:function(t){console.log("上报失败")}})}function e(e){var o=new MutationObserver(function(e){var n=!1;e.forEach(function(e){n||"attributes"!==e.type||"data-report-view"!==e.attributeName||e.target.hasAttribute("data-report-view")||(n=!0,o.disconnect(),t())})});o.observe(e,{attributes:!0,attributeFilter:["data-report-view"]})}function o(){var t=$("#toolBarBox").offset().top,e=$("#toolBarBox").offset().left,o=$(".left-toolbox").height(),n=$("#toolBarBox").width(),i=window.innerHeight,a=document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop;t+o-i-a>0?($("#toolBarBox .left-toolbox").css({position:"fixed","z-index":"999",left:e+"px",bottom:"0",width:n+"px"}),$("#toolBarBox").addClass("more-toolbox-active")):($("#toolBarBox .left-toolbox").css({position:"relative","z-index":"999",left:"0px",bottom:"0",width:n+"px"}),$("#toolBarBox").removeClass("more-toolbox-active"))}function n(){var t=$("#toolBarBox").offset().top,e=$("#toolBarBox").offset().left,o=$(".left-toolbox").height(),n=$("#toolBarBox").width()-50,i=window.innerHeight,a=document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop;t+o-i-a>0?($("#toolBarBox .gitcode-guide-box").css({position:"fixed","z-index":"999",left:e+"px",bottom:"64px",width:n+"px"}),$("#toolBarBox").addClass("more-toolbox-active")):($("#toolBarBox .gitcode-guide-box").css({position:"relative","z-index":"999",left:"0px",bottom:"0",width:n+"px"}),$("#toolBarBox").removeClass("more-toolbox-active"))}function i(t,e){if(F.skinBoxshadow.html(""),t){var o='<div class="reward-success reward-tip">    <svg t="1513153231313" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4182" id="mx_n_1513153231314" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m244.8 392L467.2 691.2c-8 9.6-24 12.8-36.8 12.8-12.8 0-27.2-3.2-36.8-12.8L267.2 560c-16-16-16-43.2 0-59.2s41.6-16 57.6 0l105.6 110.4 267.2-278.4c16-16 41.6-16 57.6 0s16 43.2 1.6 59.2z" p-id="4183" fill=""></path></svg>    <span>'+e+"</span>    </div>";F.skinBoxshadow.append(o)}else{var n='<div class="reward-error reward-tip">    <svg t="1513590447537" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9504" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m0 832c-35.2 0-64-28.8-64-64s28.8-64 64-64 64 28.8 64 64-28.8 64-64 64z m64-320c0 35.2-28.8 64-64 64s-64-28.8-64-64V256c0-35.2 28.8-64 64-64s64 28.8 64 64v256z" p-id="9505" fill=""></path></svg>    <span>'+e+"</span>    </div>";F.skinBoxshadow.append(n)}F.skinBoxshadow.fadeIn(200),setTimeout(function(){F.skinBoxshadow.fadeOut(200),F.skinBoxshadow.html("")},1500)}function a(t,e){e?t.addClass("active"):t.removeClass("active")}function s(t){getCookie("UserName")?$.ajax({url:blogUrl+"/phoenix/web/v1/article/like",type:"post",dataType:"json",data:{articleId:articleId},xhrFields:{withCredentials:!0},success:function(e){if(200==e.code){if(e.data.status)$("#is-like-img").hide(),$("#is-like-img-new").hide(),$("#is-like-imgactive").show(),$("#is-like-imgactive-new").show(),isLikeStatus=!0,isUnLikeStatus&&t&&c(!1),a($("#spanCount"),!0),$("#spanCount")[0].style="color:#fc5531 !important;",window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"1582594662_001",spm:"1001.2101.3001.4241",dest:"",extend1:'{"praise":1}'});else{$("#is-like-imgactive").hide(),$("#is-like-imgactive-new").hide(),$("#is-like-img").show(),$("#is-like-img-new").show(),isLikeStatus=!1,t&&c(!1),a($("#spanCount"),!1);var o="color:#999999 !important;";"Black"===skinStatus&&(o="color:#ccccd8 !important;"),$("#spanCount")[0].style=o,window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"1582594662_001",spm:"1001.2101.3001.4241",dest:"",extend1:'{"praise":0}'})}$("#spanCount").text(e.data.like_num>0?e.data.like_num:""),$("#blog-digg-num").text(e.data.like_num>0?"点赞数"+actionNumberFormat(e.data.like_num):"点赞数0")}else i(!1,e.message);S()}}):window.csdn.loginBox.show({spm:"1001.2101.3001.8604"})}function c(t){getCookie("UserName")?$.ajax({url:blogUrl+"/phoenix/web/v1/article/bury",type:"post",dataType:"json",data:{articleId:articleId},xhrFields:{withCredentials:!0},success:function(e){200==e.code?e.data.status?($("#is-unlike-img").hide(),$("#is-unlike-imgactive").show(),isUnLikeStatus=!0,isLikeStatus&&t&&s(!1),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.6669",extend1:'{"praise":1}'})):($("#is-unlike-imgactive").hide(),$("#is-unlike-img").show(),isUnLikeStatus=!1,t&&s(!1),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.6669",extend1:'{"praise":0}'})):i(!1,e.message)}}):window.csdn.loginBox.show({spm:"1001.2101.3001.8605"})}function r(){window.csdn.loginBox.key({biz:"blog",subBiz:"other_service",cb:function(){$.ajax({url:blogUrl+"/phoenix/web/v1/collect",dataType:"json",type:"POST",xhrFields:{withCredentials:!0},data:{articleId:articleId},success:function(t){200===t.code&&l(t.data.status)},error:function(){}})}})}function l(t,e){var o=1*$("#get-collection").attr("data-num");if(t){if($("#is-collection-imgactive").show(),$("#is-collection-img").hide(),a($("#get-collection"),!0),$("#blog_detail_zk_collection .un-collect-status").hide(),$("#blog_detail_zk_collection .collect-status").show(),N){if(N=!1,e)return;o?($(".get-collection").text(actionNumberFormat(o+1)),$("#get-collection").text(o+1),$("#get-collection").attr("data-num",o+1)):$(".get-collection").text("1")}}else{if($("#is-collection-imgactive").hide(),$("#is-collection-img").show(),a($("#get-collection"),!1),"White"==skinStatus?$(".get-collection").css({color:"#999aaa"}):$(".get-collection").css({color:"#999999"}),$("#blog_detail_zk_collection .un-collect-status").show(),$("#blog_detail_zk_collection .collect-status").hide(),N=!0,e)return;o?1*o-1<=0?($(".get-collection").text(""),$("#get-collection").attr("data-num",0)):($(".get-collection").text(actionNumberFormat(o-1)),$("#get-collection").text(o-1),$("#get-collection").attr("data-num",o-1)):$(".get-collection").text("")}S()}function d(){getCookie("UserName")&&$.ajax({url:blogUrl+"phoenix/web/v1/isCollect",type:"GET",xhrFields:{withCredentials:!0},data:{articleId:articleId},success:function(t){200===t.code&&l(t.data.status,!0),S()}})}function m(t,e,o){o.length&&$.ajax({type:"GET",url:blogUrl+t,dataType:"json",xhrFields:{withCredentials:!0},data:{articleId:articleId},success:function(t){200==t.code&&e(t.data,o)},error:function(t){}})}function p(t,e){var o="";if(t.list&&t.list.length){if(o='<div class="reward-left">',$.each(t.list,function(t,e){t<10&&(o+='<a class="reward-href" target="_blank" href="'+blogUrl+e.username+'"><img src="'+e.avatarUrl+'" alt=""></a>')}),o+="</div>",o+='<div class="reward-right">'+(t.total>10?"等":"")+'<span class="count">'+t.total+"</span>人已打赏</div>",e.html(o),F.rewardContent.show(),F.initHeighgt=50,h())var n=422;else var n=480;$(".tool-item-reward .count").html(t.total),F.rewardNew.css({"max-height":n+F.initHeighgt+"px"})}else F.rewardContent.hide(),F.initHeighgt=0}function u(t,e){Number(t)-Number(e)<0?F.sureBoxBlance.addClass("active"):F.sureBoxBlance.removeClass("active")}function h(){var t=0;return F.payType.each(function(e,o){if($(o).hasClass("active"))return void(t=e)}),"blance"===F.payType.eq(t).data("type")}function f(t){F.sureBoxBlance.find(".tip").removeAttr("style",""),t.value||(t.value=2),F.moneyNum=t.value,u(F.blance,F.moneyNum),F.codeNum.html("¥"+F.moneyNum),0<Number(F.moneyNum)<=500?h()||g():F.customizeTip.fadeIn(1e3,function(){F.customizeTip.fadeOut(1e3)})}function g(){if(initRewardObject&&""!=initRewardObject.sign){var t={product_id:72029,goods_id:77957,num:Number(F.moneyNum),flag:39,request_type:4,is_use_balance:0,ext:initRewardObject,stringExt:JSON.stringify(initRewardObject),success_function:y,error_function:w,timeout_function:b,get_pay_success_callback:k,payment_function:v};F.codeImgBox.html(F.payrun),cart.qrPay(t)}else showToast({text:"打赏信息失效，请刷新页面重试",bottom:"10%",zindex:9002,speed:500,time:1500})}function v(){imgsrc="https://csdnimg.cn/release/download/images/pay_error.png",imgtext="已扫码<br>请在手机端操作",F.codeImgBox.html('<div class="renovate"><img src="'+imgsrc+'"><span>'+imgtext+"</span></div>")}function w(t){showToast({text:t.errorMessage,bottom:"10%",zindex:9002,speed:500,time:1500})}function b(){F.codeImgBox.html(F.repeatAgain)}function x(t,e){var o=qrcode(6,"M");o.addData(t),o.make(),e.html(o.createImgTag(3,3))}function y(t,e){x(e.pay_url,F.codeImgBox)}function k(){F.rewardNew.fadeOut(),F.skinBoxshadow.html("");var t='<img style="position:fixed;margin:auto;left:0px;top:0px;right:0px;bottom:0px" src="'+blogStaticHost+'dist/pc/img/newRewardSuccess.gif" alt="打赏" title="打赏">';F.skinBoxshadow.append(t),setTimeout(function(){m(F.ajaxRewardUrl,p,F.rewardContent),F.skinBoxshadow.fadeOut(200),F.skinBoxshadow.html("")},4e3)}function C(t){if(getCookie("UserName")){var e=$(".tool-attend");e.is(".tool-unbt-attend")?(e.removeClass("tool-unbt-attend").addClass("tool-bt-attend").text("关注"),U&&(window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"1592215036_002",spm:"1001.2101.3001.4132",extend1:"已关注"}),U=!1)):(e.removeClass("tool-bt-attend").addClass("tool-unbt-attend").text("已关注"),U&&(window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"1592215036_002",spm:"1001.2101.3001.4132",extend1:"关注"}),U=!1))}else window.csdn.loginBox.show({spm:"1001.2101.3001.8606"});S()}function S(){if("block"===$("#is-like-imgactive").css("display")&&"block"===$("#is-collection-imgactive").css("display")&&$(".tool-unbt-attend").length>0){if($("#health-companies").length){$("#health-companies").removeClass("active");var t=$("#health-companies").attr("src");t.indexOf("Default")>-1?$("#health-companies").attr("src",t):$("#health-companies").attr("src",t.replace("Active","Default"))}}else if($("#health-companies").length){$("#health-companies").addClass("active");var t=$("#health-companies").attr("src");t.indexOf("Default")>-1?$("#health-companies").attr("src",t.replace("Default","Active")):$("#health-companies").attr("src",t)}}$(document).on("mouseover",".article-bar-top .time",function(){if($(".article-info-box .up-time").length){var t=$(this).offset().left,e=($(this).width(),$(this).parents().find(".article-bar-top").offset().left);$(".article-info-box .up-time").css({left:t-e-20}).show()}}),$(document).on("mouseout",".article-bar-top .time",function(){$(".article-info-box .up-time").length&&$(".article-info-box .up-time").hide()});var _=!0;$(document).on("click",".article-info-box .slide-toggle",function(){_?$(this).text("收起"):$(this).text("版权"),$(this).parents(".article-info-box").find(".slide-content-box").slideToggle(),_=!_});var B='{"mod":"1585297308_001","spm":"1001.2101.3001.6548","dest":"'+articleDetailUrl+'","extend1":"pc","ab":"new"}';if(canRead){var I=$("<div data-report-view="+B+"><div>");$(".hide-article-box").length?$(document).on("click",".hide-article-box .btn-readmore",function(){$("#content_views").after(I),isVipArticle&&e(I[0])}):($("#content_views").after(I),isVipArticle&&e(I[0]))}$("#blog_detail_zk_collection").click(function(){getCookie("UserName")?window.csdn.collectionBox.show(window.csdn.collectionBox.params):window.csdn.loginBox.show({spm:"1001.2101.3001.4130"})}),$("#tool-reward").on("click",function(){$("#reward").show()});var T=$("#tool-downloadpdf");T.length>0&&T.on("click",function(){setTimeout(function(){$(".blog-content-box").print()},500)}),$(".is-like").on("click",function(){isUnLikeStatus?c(!0):s(!1)}),$("#is-unlike").on("click",function(){isLikeStatus?s(!0):c(!1)}),$("#getVipUrl").on("click",function(){if($(this).hasClass("unlogin"))window.csdn.loginBox.show({spm:"1001.2101.3001.4249"});else if(vipArticleAbStyle&&("control"===vipArticleAbStyle||"newStyle2"===vipArticleAbStyle)&&window.csdn&&window.csdn.userOrderTip)window.csdn.userOrderTip.show({product_id:1151,goods_id:2076,flag:6,learnNum:viewCountFormat,tags:["6"],report_ext:{spm:"1001.2101.3001.4249"}});else{var t=document.createElement("a");t.style.display="none",t.href="https://mall.csdn.net/vip?spm=1001.2101.3001.4249",t.target="_blank",document.body.appendChild(t),t.click(),document.body.removeChild(t)}}),$("#btn-readmore-zk").on("click",function(){$("#btn-readmore").show()}),$("#btn-no-login").on("click",function(){window.csdn.loginBox.show({spm:"1001.2101.3001.9442"})}),$("#btn-readmore").on("click",function(){var t=$(window).height();t*$(this).attr("height");$(".hide-article-box").show(),$("div.article_content")[0].style="height:2000px; overflow: hidden;",o(),$("#btn-readmore").hide(),$(".btn-readmore-gz")&&$(".btn-readmore-gz span").text("阅读全文")}),$(document).on("click",".is-collection > a.tool-item-href",function(){getCookie("UserName")?window.csdn.collectionBox.show(window.csdn.collectionBox.params):window.csdn.loginBox.show({spm:"1001.2101.3001.4130"})}),$(document).on("click",".btn-change-collect",function(){getCookie("UserName")?window.csdn.collectionBox.show(window.csdn.collectionBox.params):window.csdn.loginBox.show({spm:"1001.2101.3001.4130"})}),$(document).on("click",".tool-more .article-report",function(){getCookie("UserName")?showReportNew(!1,articleTitles):window.csdn.loginBox.show()}),o(),$(window).scroll(function(){o()}),$(window).resize(function(){o()}),window.hasOwnProperty("isGitCodeBlog")&&isGitCodeBlog&&(n(),$(window).scroll(function(){n()}),$(window).resize(function(){n()})),$(document).on("click",".csdn-side-toolbar .option-box",function(t){isShowConcision&&setTimeout(function(){o()},100)}),$(".btn-readmore").click(o);var N=!0;window.csdn.collectionBox.params={url:curentUrl,title:articleTitle,description:articleDesc,author:username,source_id:articleId,source:"blog",collectionCallBack:function(t){l(t)}},d();var F={blance:"",rewardBtNew:$("#rewardBtNew,#rewardBtNewHide"),rewardNew:$("#rewardNew"),rewardContent:$(".reward-box-new .reward-content"),skinBoxshadow:$(".skin-boxshadow"),rewardClose:$(".reward-popupbox-new .reward-close"),sureBoxBlance:$(".reward-popupbox-new .sure-box-blance"),moneyNum:1,payType:$(".reward-popupbox-new .pay-type"),domBlance:$(".reward-popupbox-new .pay-type-num"),rewardBt:$(".reward-popupbox-new .reward-sure"),chooseMoney:$(".reward-popupbox-new .choose-money"),customizeMoney:$(".reward-popupbox-new .customize-money"),customizeTip:$(".reward-popupbox-new .customize-tip"),isShowCode:$(".reward-popupbox-new .sure-box-money"),codeNum:$(".reward-popupbox-new .code-num"),codeImgBox:$(".reward-popupbox-new .code-img-box"),payrun:'<div class="renovate"><img src="'+blogStaticHost+'dist/pc/img/pay-time-out.png"><span>获取中</span></div>',repeatAgain:'<div class="renovate"><img src="'+blogStaticHost+'dist/pc/img/pay-time-out.png"><span>点击重新获取</span></div>',ajaxRewardUrl:"phoenix/web/v1/reward/article-users",initHeighgt:0};F.rewardBtNew.on({click:function(){getCookie("UserName")?getCookie("UserName")!==username?(m(F.ajaxRewardUrl,p,F.rewardContent),F.codeNum.html("¥"+F.moneyNum),F.sureBoxBlance.find(".tip").html(""),F.isShowCode.slideDown(500),F.sureBoxBlance.slideUp(500),F.rewardNew.animate({"max-height":480+F.initHeighgt+"px"},500),g(),F.skinBoxshadow.fadeIn(),F.rewardNew.fadeIn()):showToast({text:"自己不能打赏自己",bottom:"10%",zindex:9002,speed:500,time:1500}):window.csdn.loginBox.show({spm:"1001.2101.3001.4237"})}}),F.rewardClose.on("click",function(t){F.rewardNew.fadeOut(),F.skinBoxshadow.fadeOut();var e=t||window.e;e&&e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}),F.payType.click(function(){F.payType.removeClass("active"),$(this).addClass("active"),h()?(u(F.blance,F.moneyNum),F.isShowCode.slideUp(500),F.sureBoxBlance.slideDown(500,function(){F.sureBoxBlance.find(".tip").html('您的余额不足，请更换扫码支付或<a target="_blank" data-report-click=\'{"mod":"1597646289_003","spm":"1001.2101.3001.4302"}\' href="https://i.csdn.net/#/wallet/balance/recharge?utm_source=RewardVip" class="go-invest">充值</a>')}),F.rewardNew.animate({"max-height":422+F.initHeighgt+"px"},500)):(F.codeNum.html("¥"+F.moneyNum),F.sureBoxBlance.find(".tip").html(""),F.isShowCode.slideDown(500),F.sureBoxBlance.slideUp(500),F.rewardNew.animate({"max-height":480+F.initHeighgt+"px"},500),g())}),F.chooseMoney.click(function(){F.chooseMoney.removeClass("choosed"),F.customizeMoney.removeClass("active"),F.customizeMoney.attr("placeholder","自定义"),F.customizeMoney.val(""),$(this).addClass("choosed"),F.moneyNum=$(this).data("id"),F.codeNum.html("¥"+F.moneyNum),h()?u(F.blance,F.moneyNum):g()}),F.customizeMoney.on({focus:function(){F.chooseMoney.removeClass("choosed"),$(this).addClass("active"),$(this).attr("placeholder","1-500"),h()&&(F.sureBoxBlance.addClass("active"),F.sureBoxBlance.find(".tip").css({display:"none"}))},input:function(){$(this)[0].value=$(this)[0].value.replace(/^(0+)|[^\d]+/g,""),$(this)[0].value?($(this)[0].value>500&&($(this)[0].value=2,F.customizeTip.fadeIn(1e3,function(){F.customizeTip.fadeOut(1e3)})),u(F.blance,$(this)[0].value),F.sureBoxBlance.find(".tip").removeAttr("style","")):h()&&(F.sureBoxBlance.addClass("active"),F.sureBoxBlance.find(".tip").css({display:"none"}))},blur:function(){f($(this)[0])}}),F.customizeMoney.on("keydown",function(t){var e=t||event;13==e.which&&f($(this)[0])}),F.rewardBt.on("click",function(t){if(getCookie("UserName"))if(initRewardObject&&""!=initRewardObject.sign){if(!F.sureBoxBlance.hasClass("active")&&"none"!==F.sureBoxBlance.css("display")&&h&&0<Number(F.moneyNum)<500){var e={product_id:72029,goods_id:77957,num:Number(F.moneyNum),flag:39,request_type:4,is_use_balance:2,ext:initRewardObject,stringExt:JSON.stringify(initRewardObject)};$.ajax({url:"https://mall.csdn.net/mp/mallorder/order/quickBuy",type:"POST",dataType:"json",contentType:"application/json",data:JSON.stringify(e),xhrFields:{withCredentials:!0},success:function(t){t.code?k():400103012===res.code&&showToast({text:"余额不足，请选择其他支付方式",bottom:"10%",zindex:9002,speed:500,time:1200})}})}}else showToast({text:"打赏信息失效，请刷新页面重试",bottom:"10%",zindex:9002,speed:500,time:1500});else window.csdn.loginBox.show({spm:"1001.2101.3001.4237"})}),F.codeImgBox.on("click",".renovate",function(){g()});var U=!1;
$("#btnAttent").on("click",function(){C()}),$(".tool-attend").on("click",function(t){t.originalEvent&&(U=!0),$("#btnAttent").trigger("click")}),$("#health-companies").on("click",function(){getCookie("UserName")?$(this).hasClass("active")&&(window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.4429"}),"block"==$("#is-like-img").css("display")&&(isUnLikeStatus?c(!0):s(!1)),"block"==$("#is-collection-img").css("display")&&r(),$(".tool-bt-attend").length>0&&($(".tool-attend").trigger("click"),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"1592215036_002",spm:"1001.2101.3001.4132",extend1:"关注"})),$("#is-like-imgactive-animation-like").show().addClass("active-animation"),$("#is-collection-img-collection").show().addClass("active-animation"),$(".tool-item-follow").show().addClass("active-animation"),setTimeout(function(){$("#is-like-imgactive-animation-like").hide().removeClass("active-animation"),$("#is-collection-img-collection").hide().removeClass("active-animation"),$(".tool-item-follow").hide().removeClass("active-animation")},800)):window.csdn.loginBox.show()});var A=!0;if($(".toolbox-list a.tool-item-href").on({mouseover:function(){if(A){var t=$(this).find("img.isdefault"),e=$(this).find("span.count");t.attr("src");"block"==t.css("display")?"White"==skinStatus?e.css({color:"#999999"}):e.css({color:"#999aaa"}):e.css({color:"#fc5531"}),A=!A}},mouseout:function(){if(!A){var t=$(this).find("img.isdefault"),e=$(this).find("span.count");t.attr("src");"block"==t.css("display")?"White"==skinStatus?e.css({color:"#999aaa"}):e.css({color:"#999999"}):e.css({color:"#fc5531"}),A=!A}}}),!getCookie("UserName")){var P=$("div.article_content"),R=P.offset().top,j=P.height(),E=document.body.clientHeight||document.documentElement.clientHeight,M=$(document).scrollTop(),D=!0;$(document).scroll(function(){M=$(document).scrollTop(),(M+E-R>j/2||M+E-R>2*E)&&D&&$(".tool-active-list").show()}),$("#tool-active-list-collection").on("click",function(){window.csdn.loginBox.show({spm:"1001.2101.3001.9443"})}),$("#tool-active-list-close").on("click",function(){$(".tool-active-list").hide(),D=!1})}if($(".annotation-boxshadow").length>0){window.addEventListener("message",function(t){t.data&&"blog-code"==t.data.type&&t.data.success&&($("#annotatio-num").text(t.data.unUseCount),t.data.unUseCount<=0&&($(".code-annotation").html('<div class="code-annotation-mask" data-title="今日免费次数已用完"></div>'),$(".code-annotation").addClass("active")),$(".i-ai-code-bottom").show(),window.csdn.report&&"function"==typeof window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.10008"}))});var O=!1;$(".annotation-content").hover(function(){O=!0},function(){O=!1}),$(".annotation-boxshadow").on("click",function(t){O||($(this).hide(),$("#annotation-iframe")[0].contentWindow.postMessage({type:"blog-code-close"},"*"))}),$(document).on("click",".annotation-close",function(){$(".annotation-boxshadow").hide(),$("#annotation-iframe")[0].contentWindow.postMessage({type:"blog-code-close"},"*")})}var L="",H=$(".annotation-content").attr("data-num")||"0";$(document).on("click",".annotation-btn",function(){var t="3001.10008";H!=$(".annotation-content").attr("data-num")&&(L=""),H=$(".annotation-content").attr("data-num")||"0",$(this).find(".annotation-active").show(),$(this).find(".annotation-def").hide(),$(".annotation-btn").removeClass("active"),$(this).addClass("active"),"cai"==$(this).data("type")?(t="3001.10009",$(".annotation-zan .annotation-def").show(),$(".annotation-zan .annotation-active").hide()):($(".annotation-cai .annotation-def").show(),$(".annotation-cai .annotation-active").hide()),$(this).data("type")!=L&&window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:t,extra:JSON.stringify({index:H})}),L=$(this).data("type")})}),$(function(){function t(){var t=new Date;t.setHours(23,59,59,999);var e=t.getTime();localStorage.setItem("trafficBubble_"+articleId,e)}window.csdn.report&&"function"==typeof window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.10095"});var e=null,o=document.getElementById("is-traffic");if(o){var n=(new Date).getTime(),i=localStorage.getItem("trafficBubble_"+articleId);if(null===i||i&&parseInt(i)<n){var a=o.querySelector("div.is-traffic-bubblebox");a.style.display="block",window.csdn.report&&"function"==typeof window.csdn.report.reportView&&(window.csdn.report.reportView({spm:"3001.10096"}),window.csdn.report.reportView({spm:"3001.10097"})),clearTimeout(e),e=setTimeout(function(){a.style.display="none"},1e4),a.querySelector("img.traffic-bubble-closeimg").onclick=function(o){clearTimeout(e),t(),a.style.display="none"}}}}),$(function(){function t(t,e){var o=6,n=new Date;n.setTime(n.getTime()+36e5*o),document.cookie=t+"="+escape(e)+";expires="+n.toGMTString()+";domain=.csdn.net;path=/"}function e(t){var e,o=new RegExp("(^| )"+t+"=([^;]*)(;|$)");return(e=document.cookie.match(o))?unescape(e[2]):null}if(!currentUserName){var o=$("div.article_content"),n=o.offset().top,i=o.height(),a=document.body.clientHeight||document.documentElement.clientHeight,s=$(document).scrollTop();$(document).scroll(function(){s=$(document).scrollTop(),e("loginbox_strategy")||e("unlogin_scroll_step")||e("UserName")||(s+a-n>i/2||s+a-n>2*a)&&(window.csdn.loginBox.show({spm:"3001.9454"}),t("unlogin_scroll_step",(new Date).getTime()))})}}),"undefined"!=typeof document.addEventListener&&document.addEventListener("DOMContentLoaded",function(){return"undefined"==typeof Chart?void("undefined"!=typeof console&&console.log("ERROR: You must include chart.min.js on this page in order to use Chart.js")):void[].forEach.call(document.querySelectorAll("div.chartjs"),function(t){var e,o;e="undefined"!=typeof chartjs_colors?chartjs_colors:"undefined"!=typeof chartjs_colors_json?JSON.parse(chartjs_colors_json):{fillColor:"rgba(151,187,205,0.5)",strokeColor:"rgba(151,187,205,0.8)",highlightFill:"rgba(151,187,205,0.75)",highlightStroke:"rgba(151,187,205,1)",data:["#B33131","#B66F2D","#B6B330","#71B232","#33B22D","#31B272","#2DB5B5","#3172B6","#3232B6","#6E31B2","#B434AF","#B53071"]},o="undefined"!=typeof chartjs_config?chartjs_config:"undefined"!=typeof chartjs_config_json?JSON.parse(chartjs_config_json):{Bar:{animation:!1},Doughnut:{animateRotate:!1},Line:{animation:!1},Pie:{animateRotate:!1},PolarArea:{animateRotate:!1}};var n=t.getAttribute("data-chart"),i=JSON.parse(t.getAttribute("data-chart-value"));if(i&&i.length&&n){t.innerHTML="";var a=document.createElement("canvas");a.height=t.getAttribute("data-chart-height"),t.appendChild(a);var s=document.createElement("div");s.setAttribute("class","chartjs-legend"),t.appendChild(s);var c,r=a.getContext("2d"),l=new Chart(r);if("bar"!=n)for(c=0;c<i.length;c++)i[c].color=e.data[c],i[c].highlight=e.data[c];if("bar"==n||"line"==n){var d={datasets:[{label:"",fillColor:e.fillColor,strokeColor:e.strokeColor,highlightFill:e.highlightFill,highlightStroke:e.highlightStroke,data:[]}],labels:[]};for(c=0;c<i.length;c++)i[c].value&&(d.labels.push(i[c].label),d.datasets[0].data.push(i[c].value));s.innerHTML=""}"bar"==n?l.Bar(d,o.Bar):"line"==n?l.Line(d,o.Line):"polar"==n?s.innerHTML=l.PolarArea(i,o.PolarArea).generateLegend():"pie"==n?s.innerHTML=l.Pie(i,o.Pie).generateLegend():s.innerHTML=l.Doughnut(i,o.Doughnut).generateLegend()}})}),$(function(){var t=setInterval(function(){if(window.Swiper){clearInterval(t);var e=new CSDNviewImg("#content_views");window.t=e,$("#content_views").on("click","img",function(t){if(t.currentTarget.src&&t.currentTarget.className.indexOf("contentImg-no-view")<0){var o=$(this).index("#content_views img:not(.contentImg-no-view)");$(".imgViewDom").fadeIn(300,function(){e.update(),e.slideTo(o,0)}),$("body").css({overflow:"hidden"})}})}},1e3);middleJump?middleJump({el:"#content_views",url:"https://link.csdn.net"}):$("#content_views").find("a").click(function(t){if(this.href&&"_self"!==this.target){t.preventDefault();var e=window.open(this.href,"_blank");e.focus()}})}),$(function(){function t(t,e,n){$.get(blog_address+"/phoenix/article/privacy?articleId="+t+"&index="+e+"&reason="+n,function(t){var e=t;e.result?(alert("文章已私密！"),location.reload()):e.content?o(e.content):alert("无法私密，请到后台私密！")})}function o(t){$(".super-private").hide(),$(".private-error").height(126).show().children(".private-content").text(t)}function n(){$(".private-form").removeClass("active").addClass("no-active")}var i={markdown_line:function(){$(".markdown_views pre").addClass("prettyprint"),$("pre.prettyprint code").each(function(){var t=$(this).text().split("\n").length+($(this).hasClass("hljs")?1:0),e=$("<ul/>").addClass("pre-numbering").hide();$(this).addClass("has-numbering").parent().append(e);for(var o=1;o<t;o++)e.append($("<li/>").text(o));e.fadeIn(1700)}),$(".pre-numbering li").css("color","#999"),setTimeout(function(){$(".math").each(function(t,e){$(this).find("span").last().css("color","#fff")})}),setTimeout(function(){$(".toc a[target='_blank']").attr("target",""),$("a.reversefootnote,a.footnote").attr("target","")},500)},html_line:function(){function t(){$(".CopyToClipboard").each(function(){var t=new ZeroClipboard.Client;t.setHandCursor(!0),t.addEventListener("load",function(t){}),t.addEventListener("mouseOver",function(t){var e=t.movie.parentNode.parentNode.parentNode.parentNode.nextSibling.innerHTML;e=e.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&"),t.setText(e)}),t.addEventListener("complete",function(t,e){alert("代码已经复制到你的剪贴板。")}),t.glue(this,this.parentNode)})}$(".article_content pre").each(function(){var t=$(this);try{if(t.attr("class").indexOf("brush:")!=-1){var e=t.attr("class").split(";")[0].split(":")[1];t.attr("name","code"),t.attr("class",e)}t.attr("class")&&t.attr("name","code")}catch(o){}}),$(".article_content textarea[name=code]").each(function(){var t=$(this);t.attr("class").indexOf(":")!=-1&&t.attr("class",t.attr("class").split(":")[0])}),$(".highlighter").each(function(t,e){hljs.highlightBlock(e),hljs.lineNumbersBlock(e)}),window.clipboardData||setTimeout(t,1e3)}},a=$(".markdown_views")[0];a?(i.markdown_line(),/Safari/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&window.csdn.setSafariCodestyle()):i.html_line(),$(document).on("click",".input-mod",function(){$(".select-option").show(),$(".select-button").removeClass("rotate0").addClass("rotate180")}),$(document).on("input porpertychange",".private-input",function(){n(),$(this).val().length>120&&$(this).val($(this).val().substr(0,120)),$(".textarea-box .number").text(120-$(this).val().length),$(this).val().length>0&&$(".private-form").removeClass("no-active").addClass("active")}),$(document).on("click",".select-option li",function(){$(".super-private").height(126),$(".private-content .other").hide(),n(),$(".select-active").text($(this).text()).data("index",$(this).data("index")),$(this).data("isinput")?($(".super-private").height(240),$(".private-content .other").show()):$(".private-form").removeClass("no-active").addClass("active"),$(".select-button").addClass("rotate0").removeClass("rotate180"),$(".select-option").fadeOut()}),$(document).on("click",".select-button",function(){"block"===$(".select-option").css("display")?($(".select-option").fadeOut(),$(this).removeClass("rotate180").addClass("rotate0")):($(this).removeClass("rotate0").addClass("rotate180"),$(".select-option").fadeIn()),e.stopPropagation(),e.cancelBubble=!0}),$(document).on("click",".private-close, .close-active",function(){$(".super-private").hide(),$(".mask-dark").hide()}),$(document).on("click",".private-footer .active",function(){var e=$(".select-active").data("index"),o=$(".reason").val();e||alert("请选择原因"),text="text",t(articleId,e,o)})}),$(function(){$("article").find("table").map(function(){$(this).wrap('<div class="table-box" />')})}),$(function(){window.addEventListener("message",function(t){"https://inscode.csdn.net"===t.origin&&"needLogin"===t.data&&window.csdn.loginBox.show()})}),!function(){function t(){var t=navigator.userAgent.toLowerCase();return window.ActiveXObject||"ActiveXObject"in window?"ie":t.indexOf("firefox")>=0?"firefox":t.indexOf("chrome")>=0?"chrome":t.indexOf("opera")>=0?"opera":t.indexOf("safari")>=0?"safari":void 0}var e=function(t,e,o,n,i,a){function s(o,n,i){var r=(o+n)/2;if(i<=0||n-o<a)return r;var l="("+t+":"+r+e+")";return c(l).matches?s(r,n,i-1):s(o,r,i-1)}var c,r,l,d;window.matchMedia?c=window.matchMedia:(r=document.getElementsByTagName("head")[0],l=document.createElement("style"),r.appendChild(l),d=document.createElement("div"),d.className="mediaQueryBinarySearch",d.style.display="none",document.body.appendChild(d),c=function(t){l.sheet.insertRule("@media "+t+"{.mediaQueryBinarySearch {text-decoration: underline} }",0);var e="underline"==getComputedStyle(d,null).textDecoration;return l.sheet.deleteRule(0),{matches:e}});var m=s(o,n,i);return d&&(r.removeChild(l),document.body.removeChild(d)),m},o={};o.ie=function(){return window.screen.deviceXDPI/window.screen.logicalXDPI},o.firefox=function(){return window.devicePixelRatio?window.devicePixelRatio:e("min--moz-device-pixel-ratio","",0,10,20,1e-4)},o.opera=function(){return window.outerWidth/window.innerWidth},o.chrome=function(){if(window.devicePixelRatio)return window.devicePixelRatio;var t=document.createElement("div");t.innerHTML="1",t.setAttribute("style","font:100px/1em sans-serif;-webkit-text-size-adjust:none;position: absolute;top:-100%;"),document.body.appendChild(t);var e=1e3/t.clientHeight;return e=Math.round(100*e)/100,document.body.removeChild(t),e},o.safari=function(){return window.outerWidth/window.innerWidth},window.detectZoom=function(){return o[t()]()}}(void 0),$(function(){function t(){var t=navigator.userAgent.toLowerCase();return t.indexOf("win")>=0?"win":t.indexOf("mac")>=0?"mac":"win"}function e(t,e){return Math.round(t*Math.pow(10,e))/Math.pow(10,e)}function o(t){return"win"==t.system&&1!=t.zoom||("mac"==t.system&&t.zoom%1!=0&&t.zoom%2!=0||void 0)}function n(n){var i={win:{add:187,minus:189,origin:48},mac:{add:187,minus:189,origin:48}},a=i[t()];if(n.ctrlKey||n.metaKey)if(n.keyCode==a.add||n.keyCode==a.minus){var s={zoom:e(detectZoom(),2),system:t()};l(o(s),r,u)}else n.keyCode!=a.add&&n.keyCode!=a.origin||d.animate(m,2e3)}function i(t){setTimeout(n,300,t)}function a(t,e){return localStorage.setItem(t,e)}function s(t){return localStorage.getItem(t)}function c(t){var e=document.createElement("style");e.type="text/css",e.innerHTML=[".leftPop{width:330px;position: fixed;font-size: 12px;","box-shadow: 0 4px 8px 0 rgba(0,0,0,0.10);padding:16px 40px 16px 16px;z-index: 100;","}",".leftPop .leftPop-close{position: absolute;right: 20px;"+t,"cursor: pointer;","}"].join(""),document.getElementsByTagName("head")[0].appendChild(e)}function r(t){var e=$(".leftPop");if(e.length>0)return e.stop(!0,!1).animate(t.animate,t.animateTime),!1;var o='<svg t="1536830466687" class="icon leftPop-close" viewBox="0 0 1024 1024" version="1.1" ><title>不再显示</title><path d="M512 438.378667L806.506667 143.893333a52.032 52.032 0 1 1 73.6 73.621334L585.621333 512l294.485334 294.485333a52.074667 52.074667 0 0 1-73.6 73.642667L512 585.621333 217.514667 880.128a52.053333 52.053333 0 1 1-73.621334-73.642667L438.378667 512 143.893333 217.514667a52.053333 52.053333 0 1 1 73.621334-73.621334L512 438.378667z" fill="" p-id="15859"></path></svg>';e=$('<div class="leftPop">'+o+"</div>"),c(t.closeColor),e.append(t.template).css(t.style),e.appendTo($("body")).delay(2e3).animate(t.animate,t.animateTime),d=e,$(".leftPop-close").on("click",function(){e.stop(!0,!1).animate(t.closeAnimate,t.animateTime),a("leftPop",0)})}function l(t,e,o){var n=s("leftPop");t&&null==n?e(o):d.stop(!0,!1).animate(o.closeAnimate,o.animateTime)}var d=({zoom:e(detectZoom(),2),system:t()},$("leftPop")),m={right:"-100%"},p={win:{even:"keyup",fun:n},mac:{even:"keydown",fun:i}};$(window).on(p[t()].even,p[t()].fun);var u={template:"<span>你的浏览器目前处于缩放状态，页面可能会出现错位现象，建议100%大小显示。</span>",style:{right:"-100%","background-color":"#EBF5FD",top:"100px","border-left":"4px solid #70B8F0"},closeColor:"fill:#70B8F0;",animate:{right:0},closeAnimate:m,animateTime:2e3}});