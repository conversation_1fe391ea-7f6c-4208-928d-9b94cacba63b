(function(sttc){'use strict';var aa,ba=Object.defineProperty,ca=globalThis,da=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ea={},fa={};function ha(a,b,c){if(!c||a!=null){c=fa[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}} 
function ia(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in ea?f=ea:f=ca;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=da&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?ba(ea,d,{configurable:!0,writable:!0,value:b}):b!==c&&(fa[d]===void 0&&(a=Math.random()*1E9>>>0,fa[d]=da?ca.Symbol(d):"$jscp$"+a+"$"+d),ba(f,fa[d],{configurable:!0,writable:!0,value:b})))}}var ja=Object.create,ka=Object.setPrototypeOf; 
function la(a,b){a.prototype=ja(b.prototype);a.prototype.constructor=a;ka(a,b);a.Nk=b.prototype}ia("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")},"es_next"); 
ia("String.prototype.replaceAll",function(a){return a?a:function(b,c){if(b instanceof RegExp&&!b.global)throw new TypeError("String.prototype.replaceAll called with a non-global RegExp argument.");return b instanceof RegExp?this.replace(b,c):this.replace(new RegExp(String(b).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"g"),c)}},"es_2021"); 
ia("AggregateError",function(a){function b(c,d){d=Error(d);"stack"in d&&(this.stack=d.stack);this.errors=c;this.message=d.message}if(a)return a;la(b,Error);b.prototype.name="AggregateError";return b},"es_2021"); 
ia("Promise.any",function(a){return a?a:function(b){b=b instanceof Array?b:Array.from(b);return Promise.all(b.map(function(c){return Promise.resolve(c).then(function(d){throw d;},function(d){return d})})).then(function(c){throw new ea.AggregateError(c,"All promises were rejected");},function(c){return c})}},"es_2021");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var q=this||self;function ma(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"}function na(a){var b=ma(a);return b=="array"||b=="object"&&typeof a.length=="number"}function oa(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function qa(a){return Object.prototype.hasOwnProperty.call(a,sa)&&a[sa]||(a[sa]=++ua)}var sa="closure_uid_"+(Math.random()*1E9>>>0),ua=0;function va(a,b,c){return a.call.apply(a.bind,arguments)} 
function wa(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function xa(a,b,c){xa=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?va:wa;return xa.apply(null,arguments)} 
function ya(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function za(a,b,c){a=a.split(".");c=c||q;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b}function Aa(a){return a} 
function Ba(a,b){function c(){}c.prototype=b.prototype;a.Nk=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Ko=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ca={Nn:0,Mn:1,Ln:2};var Da;function Ea(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(let c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1}function Ga(a,b){const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)}function Ka(a,b){var c=a.length;const d=typeof a==="string"?a.split(""):a;for(--c;c>=0;--c)c in d&&b.call(void 0,d[c],c,a)} 
function La(a,b){const c=a.length,d=[];let e=0;const f=typeof a==="string"?a.split(""):a;for(let g=0;g<c;g++)if(g in f){const h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d}function Ma(a,b){const c=a.length,d=Array(c),e=typeof a==="string"?a.split(""):a;for(let f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d}function Na(a,b){let c=1;Ga(a,function(d,e){c=b.call(void 0,c,d,e,a)});return c} 
function Pa(a,b){const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1}function Qa(a,b){return Ea(a,b)>=0}function Ra(a,b){b=Ea(a,b);let c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c}function Sa(a,b){let c=0;Ka(a,function(d,e){b.call(void 0,d,e,a)&&Array.prototype.splice.call(a,e,1).length==1&&c++})}function Ua(a){return Array.prototype.concat.apply([],arguments)} 
function Va(a){const b=a.length;if(b>0){const c=Array(b);for(let d=0;d<b;d++)c[d]=a[d];return c}return[]}function Wa(a,b){for(let c=1;c<arguments.length;c++){const d=arguments[c];if(na(d)){const e=a.length||0,f=d.length||0;a.length=e+f;for(let g=0;g<f;g++)a[e+g]=d[g]}else a.push(d)}}function Xa(a,b,c){return arguments.length<=2?Array.prototype.slice.call(a,b):Array.prototype.slice.call(a,b,c)} 
function Za(a,b,c){c=c||ab;let d=0,e=a.length,f;for(;d<e;){const g=d+(e-d>>>1);let h;h=c(b,a[g]);h>0?d=g+1:(e=g,f=!h)}return f?d:-d-1}function ab(a,b){return a>b?1:a<b?-1:0}function bb(a){const b=[];for(let c=0;c<arguments.length;c++){const d=arguments[c];if(Array.isArray(d))for(let e=0;e<d.length;e+=8192){const f=bb.apply(null,Xa(d,e,e+8192));for(let g=0;g<f.length;g++)b.push(f[g])}else b.push(d)}return b} 
function cb(a,b){b=b||Math.random;for(let c=a.length-1;c>0;c--){const d=Math.floor(b()*(c+1)),e=a[c];a[c]=a[d];a[d]=e}};var db={gl:"google_adtest",ll:"google_ad_client",Al:"google_ad_intent_query",zl:"google_ad_intent_qetid",yl:"google_ad_intent_eids",xl:"google_ad_intents_format",ml:"google_ad_format",rl:"google_ad_height",Ml:"google_ad_width",Bl:"google_ad_layout",Cl:"google_ad_layout_key",El:"google_ad_output",Fl:"google_ad_region",Il:"google_ad_slot",Kl:"google_ad_type",Ll:"google_ad_url",tm:"google_gl",Bm:"google_enable_ose",Lm:"google_full_width_responsive",Qn:"google_rl_filtering",Pn:"google_rl_mode",Rn:"google_rt", 
On:"google_rl_dest_url",wn:"google_max_radlink_len",Bn:"google_num_radlinks",Cn:"google_num_radlinks_per_unit",kl:"google_ad_channel",vn:"google_max_num_ads",xn:"google_max_responsive_height",gm:"google_color_border",Am:"google_enable_content_recommendations",qm:"google_content_recommendation_ui_type",pm:"google_source_type",om:"google_content_recommendation_rows_num",nm:"google_content_recommendation_columns_num",mm:"google_content_recommendation_ad_positions",rm:"google_content_recommendation_use_square_imgs", 
im:"google_color_link",hm:"google_color_line",km:"google_color_url",il:"google_ad_block",Hl:"google_ad_section",jl:"google_ad_callback",dm:"google_captcha_token",jm:"google_color_text",Yl:"google_alternate_ad_url",wl:"google_ad_host_tier_id",em:"google_city",ul:"google_ad_host",vl:"google_ad_host_channel",Zl:"google_alternate_color",fm:"google_color_bg",Cm:"google_encoding",Jm:"google_font_face",Nm:"google_hints",en:"google_image_size",yn:"google_mtl",qo:"google_cpm",lm:"google_contents",zn:"google_native_settings_key", 
sm:"google_country",oo:"google_targeting",Km:"google_font_size",ym:"google_disable_video_autoplay",Eo:"google_video_product_type",Do:"google_video_doc_id",Co:"google_cust_gender",ho:"google_cust_lh",fo:"google_cust_l",po:"google_tfs",mn:"google_kw",lo:"google_tag_for_child_directed_treatment",mo:"google_tag_for_under_age_of_consent",Tn:"google_region",vm:"google_cust_criteria",Gl:"google_safe",um:"google_ctr_threshold",Vn:"google_resizing_allowed",Xn:"google_resizing_width",Wn:"google_resizing_height", 
Bo:"google_cust_age",qn:"google_language",nn:"google_kw_type",In:"google_pucrd",Gn:"google_page_url",no:"google_tag_partner",bo:"google_restrict_data_processing",bl:"google_adbreak_test",ql:"google_ad_frequency_hint",el:"google_admob_interstitial_slot",fl:"google_admob_rewarded_slot",dl:"google_admob_ads_only",Jl:"google_ad_start_delay_hint",un:"google_max_ad_content_rating",Kn:"google_ad_public_floor",Jn:"google_ad_private_floor",zo:"google_traffic_source",En:"google_overlays",Hn:"google_privacy_treatments", 
jo:"google_special_category_data",Fo:"google_wrap_fullscreen_ad",Dl:"google_ad_loaded_callback"};function eb(){return!1}function fb(){return!0}function gb(a){const b=arguments,c=b.length;return function(){for(let d=0;d<c;d++)if(!b[d].apply(this,arguments))return!1;return!0}}function hb(a){return function(){return!a.apply(this,arguments)}}function jb(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}function kb(a){let b=a;return function(){if(b){const c=b;b=null;c()}}} 
function lb(a,b){let c=0;return function(d){q.clearTimeout(c);const e=arguments;c=q.setTimeout(function(){a.apply(b,e)},63)}}function mb(a,b){function c(){e=q.setTimeout(d,63);let h=g;g=[];a.apply(b,h)}function d(){e=0;f&&(f=!1,c())}let e=0,f=!1,g=[];return function(h){g=arguments;e?f=!0:c()}};var nb={passive:!0},ob=jb(function(){let a=!1;try{const b=Object.defineProperty({},"passive",{get:function(){a=!0}});q.addEventListener("test",null,b)}catch(b){}return a});function pb(a){return a?a.passive&&ob()?a:a.capture||!1:!1}function qb(a,b,c,d){return a.addEventListener?(a.addEventListener(b,c,pb(d)),!0):!1}function rb(a,b,c,d){return a.removeEventListener?(a.removeEventListener(b,c,pb(d)),!0):!1};var sb,tb;a:{for(var ub=["CLOSURE_FLAGS"],vb=q,xb=0;xb<ub.length;xb++)if(vb=vb[ub[xb]],vb==null){tb=null;break a}tb=vb}var yb=tb&&tb[610401301];sb=yb!=null?yb:!1;function zb(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]}function Ab(a,b){return a.toLowerCase().indexOf(b.toLowerCase())!=-1};function Bb(){var a=q.navigator;return a&&(a=a.userAgent)?a:""}var Cb;const Db=q.navigator;Cb=Db?Db.userAgentData||null:null;function Eb(a){if(!sb||!Cb)return!1;for(let b=0;b<Cb.brands.length;b++){const {brand:c}=Cb.brands[b];if(c&&c.indexOf(a)!=-1)return!0}return!1}function Fb(a){return Bb().indexOf(a)!=-1};function Gb(){return sb?!!Cb&&Cb.brands.length>0:!1}function Hb(){return Gb()?!1:Fb("Opera")}function Ib(){return Fb("Firefox")||Fb("FxiOS")}function Jb(){return Fb("Safari")&&!(Kb()||(Gb()?0:Fb("Coast"))||Hb()||(Gb()?0:Fb("Edge"))||(Gb()?Eb("Microsoft Edge"):Fb("Edg/"))||(Gb()?Eb("Opera"):Fb("OPR"))||Ib()||Fb("Silk")||Fb("Android"))}function Kb(){return Gb()?Eb("Chromium"):(Fb("Chrome")||Fb("CriOS"))&&!(Gb()?0:Fb("Edge"))||Fb("Silk")};function Lb(){return sb&&Cb?Cb.mobile:!Mb()&&(Fb("iPod")||Fb("iPhone")||Fb("Android")||Fb("IEMobile"))}function Mb(){return sb&&Cb?!Cb.mobile&&(Fb("iPad")||Fb("Android")||Fb("Silk")):Fb("iPad")||Fb("Android")&&!Fb("Mobile")||Fb("Silk")};function Nb(a,b,c){return Math.min(Math.max(a,b),c)}function Ob(a){return Array.prototype.reduce.call(arguments,function(b,c){return b+c},0)}function Qb(a){return Ob.apply(null,arguments)/arguments.length};function Rb(a,b){const c={};for(const d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c}function Sb(a){var b=Tb;a:{for(const c in b)if(b[c]==a){a=!0;break a}a=!1}return a}function Ub(a){const b=[];let c=0;for(const d in a)b[c++]=a[d];return b}function Vb(a){const b={};for(const c in a)b[c]=a[c];return b}const Wb="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" "); 
function Xb(a,b){let c,d;for(let e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(let f=0;f<Wb.length;f++)c=Wb[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function $b(a){$b[" "](a);return a}$b[" "]=function(){};function ac(a,b){try{return $b(a[b]),!0}catch(c){}return!1};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
let bc=globalThis.trustedTypes,cc;function dc(){let a=null;if(!bc)return a;try{const b=c=>c;a=bc.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a}function ec(){cc===void 0&&(cc=dc());return cc};var fc=class{constructor(a){this.g=a}toString(){return this.g+""}};function gc(a){const b=ec();return new fc(b?b.createScriptURL(a):a)}function hc(a){if(a instanceof fc)return a.g;throw Error("");};var ic=class{constructor(a){this.g=a}toString(){return this.g}},jc=new ic("about:invalid#zClosurez");function kc(a){return a instanceof ic}function lc(a){if(kc(a))return a.g;throw Error("");};class mc{constructor(a){this.Sj=a}}function nc(a){return new mc(b=>b.substr(0,a.length+1).toLowerCase()===a+":")}const oc=[nc("data"),nc("http"),nc("https"),nc("mailto"),nc("ftp"),new mc(a=>/^[^:]*([/?#]|$)/.test(a))];function pc(a,b=oc){if(kc(a))return a;for(let c=0;c<b.length;++c){const d=b[c];if(d instanceof mc&&d.Sj(a))return new ic(a)}}var tc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function uc(a){if(tc.test(a))return a};function vc(a){var b=uc("#");b!==void 0&&(a.href=b)};function wc(a,b=`unexpected value ${a}!`){throw Error(b);};var xc=class{constructor(a){this.g=a}toString(){return this.g+""}};function yc(a){const b=ec();return new xc(b?b.createHTML(a):a)}function zc(a){if(a instanceof xc)return a.g;throw Error("");};function Ac(a=document){a=a.querySelector?.("script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};function Bc(a,b){a.src=hc(b);(b=Ac(a.ownerDocument))&&a.setAttribute("nonce",b)};var Cc=class{constructor(a){this.g=a}toString(){return this.g}};function Dc(a,b){if(a.nodeType===1&&/^(script|style)$/i.test(a.tagName))throw Error("");a.innerHTML=zc(b)}function Ec(a,b,c){var d=[Fc`width`,Fc`height`];if(d.length===0)throw Error("");d=d.map(f=>{if(f instanceof Cc)f=f.g;else throw Error("");return f});const e=b.toLowerCase();if(d.every(f=>e.indexOf(f)!==0))throw Error(`Attribute "${b}" does not match any of the allowed prefixes.`);a.setAttribute(b,c)};var Gc=class{constructor(a){this.g=a}toString(){return this.g}};function Hc(a){if(a instanceof Gc)return a.g;throw Error("");};function Ic(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};function Jc(a,b){const c={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"'};let d;d=b?b.createElement("div"):q.document.createElement("div");return a.replace(Kc,function(e,f){let g=c[e];if(g)return g;f.charAt(0)=="#"&&(f=Number("0"+f.slice(1)),isNaN(f)||(g=String.fromCharCode(f)));g||(Dc(d,yc(e+" ")),g=d.firstChild.nodeValue.slice(0,-1));return c[e]=g})}var Kc=/&([^;\s<&]+);?/g;function Lc(a){let b=0;for(let c=0;c<a.length;++c)b=31*b+a.charCodeAt(c)>>>0;return b} 
function Mc(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})}function Nc(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};var Oc=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");var Pc=Gb()?!1:Fb("Trident")||Fb("MSIE"),Qc=Fb("Edge")||Pc,Vc=Fb("Gecko")&&!(Ab(Bb(),"WebKit")&&!Fb("Edge"))&&!(Fb("Trident")||Fb("MSIE"))&&!Fb("Edge"),Wc=Ab(Bb(),"WebKit")&&!Fb("Edge");function Fc(a){return new Cc(a[0].toLowerCase())};function Xc(a){return new Gc(a[0])};function Yc(a){return a instanceof xc?a:yc(Zc(String(a)))}function Zc(a){return a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}function $c(a){const b=Yc("");return yc(a.map(c=>zc(Yc(c))).join(zc(b).toString()))}const ad=/^[a-z][a-z\d-]*$/i,bd="APPLET BASE EMBED IFRAME LINK MATH META OBJECT SCRIPT STYLE SVG TEMPLATE".split(" ");var cd="AREA BR COL COMMAND HR IMG INPUT KEYGEN PARAM SOURCE TRACK WBR".split(" "); 
const dd=["action","formaction","href"];function ed(a){if(!ad.test(a))throw Error("");if(bd.indexOf(a.toUpperCase())!==-1)throw Error("");}function fd(a,b,c){ed(a);let d=`<${a}`;b&&(d+=gd(b));Array.isArray(c)||(c=c===void 0?[]:[c]);cd.indexOf(a.toUpperCase())!==-1?d+=">":(b=$c(c.map(e=>e instanceof xc?e:Yc(String(e)))),d+=">"+b.toString()+"</"+a+">");return yc(d)} 
function gd(a){var b="";const c=Object.keys(a);for(let f=0;f<c.length;f++){var d=c[f],e=a[d];if(!ad.test(d))throw Error("");if(e!==void 0&&e!==null){if(/^on./i.test(d))throw Error("");dd.indexOf(d.toLowerCase())!==-1&&(e=kc(e)?e.toString():uc(String(e))||"about:invalid#zClosurez");e=`${d}="${Yc(String(e))}"`;b+=" "+e}}return b};function hd(a,...b){if(b.length===0)return gc(a[0]);let c=a[0];for(let d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return gc(c)}function id(a,b){a=hc(a).toString();const c=a.split(/[?#]/),d=/[?]/.test(a)?"?"+c[1]:"";return jd(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)} 
function jd(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(k=>e(k,h)):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}let f=b.length?"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(g=>e(g[1],g[0])):d.forEach(e);return gc(a+b+c)};function kd(a){try{return!!a&&a.location.href!=null&&ac(a,"foo")}catch{return!1}}function ld(a,b=q){b=md(b);let c=0;for(;b&&c++<40&&!a(b);)b=md(b)}function md(a){try{const b=a.parent;if(b&&b!=a)return b}catch{}return null}function qd(a){return kd(a.top)?a.top:null}function rd(a,b){const c=sd("SCRIPT",a);Bc(c,b);(a=a.getElementsByTagName("script")[0])&&a.parentNode&&a.parentNode.insertBefore(c,a)}function td(a,b){return b.getComputedStyle?b.getComputedStyle(a,null):a.currentStyle} 
function ud(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function vd(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function wd(a){const b=[];vd(a,function(c){b.push(c)});return b}function xd(a){const b=a.length;if(b==0)return 0;let c=305419896;for(let d=0;d<b;d++)c^=(c<<5)+(c>>2)+a.charCodeAt(d)&4294967295;return c>0?c:4294967296+c} 
var zd=jb(()=>Pa(["Google Web Preview","Mediapartners-Google","Google-Read-Aloud","Google-Adwords"],yd)||Math.random()<1E-4);const yd=a=>Bb().indexOf(a)!=-1;var Ad=/^([0-9.]+)px$/,Bd=/^(-?[0-9.]{1,30})$/;function Cd(a){if(!Bd.test(a))return null;a=Number(a);return isNaN(a)?null:a}function Dd(a){return(a=Ad.exec(a))?+a[1]:null} 
var Ed={Nl:"allow-forms",Ol:"allow-modals",Pl:"allow-orientation-lock",Ql:"allow-pointer-lock",Rl:"allow-popups",Sl:"allow-popups-to-escape-sandbox",Tl:"allow-presentation",Ul:"allow-same-origin",Vl:"allow-scripts",Wl:"allow-top-navigation",Xl:"allow-top-navigation-by-user-activation"};const Fd=jb(()=>wd(Ed));function Gd(){var a=["allow-top-navigation","allow-modals","allow-orientation-lock","allow-presentation","allow-pointer-lock"];const b=Fd();return a.length?La(b,c=>!Qa(a,c)):b} 
function Hd(){const a=sd("IFRAME"),b={};Ga(Fd(),c=>{a.sandbox&&a.sandbox.supports&&a.sandbox.supports(c)&&(b[c]=!0)});return b} 
var Ld=(a,b)=>{try{return!(!a.frames||!a.frames[b])}catch{return!1}},Md=(a,b)=>{for(let c=0;c<50;++c){if(Ld(a,b))return a;if(!(a=md(a)))break}return null},Nd=jb(()=>Lb()?2:Mb()?1:0),r=(a,b)=>{vd(b,(c,d)=>{a.style.setProperty(d,c,"important")})},Pd=(a,b)=>{if("length"in a.style){a=a.style;const c=a.length;for(let d=0;d<c;d++){const e=a[d];b(a[e],e,a)}}else a=Od(a.style.cssText),vd(a,b)},Od=a=>{const b={};if(a){const c=/\s*:\s*/;Ga((a||"").split(/\s*;\s*/),d=>{if(d){var e=d.split(c);d=e[0];e=e[1];d&& 
e&&(b[d.toLowerCase()]=e)}})}return b},Qd=a=>{const b=/!\s*important/i;Pd(a,(c,d)=>{b.test(c)?b.test(c):a.style.setProperty(d,c,"important")})};const Rd={["http://googleads.g.doubleclick.net"]:!0,["http://pagead2.googlesyndication.com"]:!0,["https://googleads.g.doubleclick.net"]:!0,["https://pagead2.googlesyndication.com"]:!0},Sd=/\.proxy\.(googleprod|googlers)\.com(:\d+)?$/,Td=/.*domain\.test$/,Ud=/\.prod\.google\.com(:\d+)?$/;var Vd=a=>Rd[a]||Sd.test(a)||Td.test(a)||Ud.test(a);let Wd=[]; 
const Xd=()=>{const a=Wd;Wd=[];for(const b of a)try{b()}catch{}}; 
var Yd=()=>{var a=Math.random;return Math.floor(a()*2**52)},Zd=(a,b)=>{if(typeof a.goog_pvsid!=="number")try{Object.defineProperty(a,"goog_pvsid",{value:Yd(),configurable:!1})}catch(c){b&&b.ma(784,c)}a=Number(a.goog_pvsid);b&&(!a||a<=0)&&b.ma(784,Error(`Invalid correlator, ${a}`));return a||-1},$d=(a,b)=>{a.document.readyState==="complete"?(Wd.push(b),Wd.length==1&&(window.Promise?Promise.resolve().then(Xd):window.setImmediate?setImmediate(Xd):setTimeout(Xd,0))):a.addEventListener("load",b)},ae=(a, 
b)=>new Promise(c=>{setTimeout(()=>void c(b),a)});function sd(a,b=document){return b.createElement(String(a).toLowerCase())}var be=a=>{let b=a;for(;a&&a!=a.parent;)a=a.parent,kd(a)&&(b=a);return b},ce=a=>{var b=qd(a);if(!b)return 1;a=Nd()===0;const c=!!b.document.querySelector('meta[name=viewport][content*="width=device-width"]'),d=b.innerWidth;b=b.outerWidth;if(d===0)return 1;const e=Math.round((b/d+Number.EPSILON)*100)/100;return e===1?1:a||c?e:Math.round((b/d/.4+Number.EPSILON)*100)/100};let de;function ee(a){return(de||(de=new TextEncoder)).encode(a)};function fe(a){q.setTimeout(()=>{throw a;},0)};var ge={},he=null;function ie(a){var b=3;b===void 0&&(b=0);je();b=ge[b];const c=Array(Math.floor(a.length/3)),d=b[64]||"";let e=0,f=0;for(;e<a.length-2;e+=3){var g=a[e],h=a[e+1],k=a[e+2],l=b[g>>2];g=b[(g&3)<<4|h>>4];h=b[(h&15)<<2|k>>6];k=b[k&63];c[f++]=l+g+h+k}l=0;k=d;switch(a.length-e){case 2:l=a[e+1],k=b[(l&15)<<2]||d;case 1:a=a[e],c[f]=b[a>>2]+b[(a&3)<<4|l>>4]+k+d}return c.join("")} 
function ke(a){const b=[];let c=0;for(let d=0;d<a.length;d++){let e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}return ie(b)}function me(a){const b=[];ne(a,function(c){b.push(c)});return b} 
function ne(a,b){function c(e){for(;d<a.length;){const f=a.charAt(d++),g=he[f];if(g!=null)return g;if(!/^[\s\xa0]*$/.test(f))throw Error("Unknown base64 encoding at char: "+f);}return e}je();let d=0;for(;;){const e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}} 
function je(){if(!he){he={};var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"];for(let c=0;c<5;c++){const d=a.concat(b[c].split(""));ge[c]=d;for(let e=0;e<d.length;e++){const f=d[e];he[f]===void 0&&(he[f]=e)}}}};const oe=/[-_.]/g,pe={"-":"+",_:"/",".":"="};function qe(a){return pe[a]||""}var re={},se=typeof structuredClone!="undefined";function te(){return ue||(ue=new ve(null,re))}var ve=class{isEmpty(){return this.g==null}constructor(a,b){we(b);this.g=a;if(a!=null&&a.length===0)throw Error("ByteString should be constructed with non-empty values");}};let ue;function we(a){if(a!==re)throw Error("illegal external caller");};let xe=void 0,ye;function ze(a){if(ye)throw Error("");ye=b=>{q.setTimeout(()=>{a(b)},0)}}function Ae(a){if(ye)try{ye(a)}catch(b){throw b.cause=a,b;}}function Be(){const a=Error();Ic(a,"incident");ye?Ae(a):fe(a)}function Ce(a){a=Error(a);Ic(a,"warning");Ae(a);return a}function De(a,b){if(a!=null){var c=xe??(xe={});var d=c[a]||0;d>=b||(c[a]=d+1,Be())}};function Ee(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var Fe=Ee(),Ge=Ee(),He=Ee(),Ie=Ee(),Je=Ee("m_m",!0),Ke=Ee();const u=Ee("jas",!0);var Le;const Me=[];Me[u]=55;Le=Object.freeze(Me);function Ne(a,b){a[u]|=b}function Oe(a,b){a[u]&=~b}function Pe(a){if(4&a)return 2048&a?2048:4096&a?4096:0}function Qe(a){Ne(a,34);return a}function Re(a){Ne(a,32);return a};const Se=typeof Je==="symbol";var Te={};function Ve(a){a=a[Je];const b=a===Te;Se&&a&&!b&&De(Ke,3);return b}function We(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object}function Xe(a,b){if(a!=null)if(typeof a==="string")a=a?new ve(a,re):te();else if(a.constructor!==ve)if(a!=null&&a instanceof Uint8Array)a=a.length?new ve(new Uint8Array(a),re):te();else{if(!b)throw Error();a=void 0}return a}function Ye(a){if(a&2)throw Error();} 
class Ze{constructor(a,b,c){this.g=a;this.i=b;this.j=c}next(){const a=this.g.next();a.done||(a.value=this.i.call(this.j,a.value));return a}[Symbol.iterator](){return this}}var $e=Object.freeze({});function af(a,b,c){const d=b&512?0:-1,e=a.length;b=b&64?b&256:!!e&&We(a[e-1]);const f=e+(b?-1:0);for(let g=0;g<f;g++)c(g-d,a[g]);if(b){a=a[e-1];for(const g in a)Object.prototype.hasOwnProperty.call(a,g)&&!isNaN(g)&&c(+g,a[g])}};function bf(a,b){const c=cf;if(!b(a))throw b=(typeof c==="function"?c():c)?.concat("\n")??"",Error(b+String(a));}function df(a){a.Qo=!0;return a}let cf=void 0;const ef=df(a=>a!==null&&a!==void 0);var ff=df(a=>typeof a==="number"),gf=df(a=>typeof a==="string"),hf=df(a=>a===void 0);function jf(){var a=kf;return df(b=>{for(const c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}var lf=df(a=>!!a&&(typeof a==="object"||typeof a==="function"));function mf(){return nf(df((a,b)=>a===void 0?!0:gf(a,b)))}function nf(a){a.Pj=!0;return a}var of=df(a=>Array.isArray(a));function pf(){return df(a=>of(a)?a.every(b=>ff(b)):!1)};function qf(a){if(gf(a)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(a))throw Error(String(a));}else if(ff(a)&&!Number.isSafeInteger(a))throw Error(String(a));return BigInt(a)}var tf=df(a=>a>=rf&&a<=sf);const rf=BigInt(Number.MIN_SAFE_INTEGER),sf=BigInt(Number.MAX_SAFE_INTEGER);let uf=0,vf=0,wf;function xf(a){const b=a>>>0;uf=b;vf=(a-b)/4294967296>>>0}function yf(a){if(a<0){xf(-a);a=uf;var b=vf;b=~b;a?a=~a+1:b+=1;const [c,d]=[a,b];uf=c>>>0;vf=d>>>0}else xf(a)}function zf(a,b){const c=b*4294967296+(a>>>0);return Number.isSafeInteger(c)?c:Af(a,b)}function Af(a,b){b>>>=0;a>>>=0;var c;b<=2097151?c=""+(4294967296*b+a):c=""+(BigInt(b)<<BigInt(32)|BigInt(a));return c}function Bf(){var a=uf,b=vf,c;b&2147483648?c=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):c=Af(a,b);return c} 
function Cf(a){a.length<16?yf(Number(a)):(a=BigInt(a),uf=Number(a&BigInt(4294967295))>>>0,vf=Number(a>>BigInt(32)&BigInt(4294967295)))};const Df=typeof BigInt==="function"?BigInt.asIntN:void 0,Ef=typeof BigInt==="function"?BigInt.asUintN:void 0,Ff=Number.isSafeInteger,Gf=Number.isFinite,Hf=Math.trunc;function If(a){if(a!=null&&typeof a!=="number")throw Error(`Value of float/double field must be a number, found ${typeof a}: ${a}`);return a}function Jf(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)} 
function Kf(a){if(typeof a!=="boolean")throw Error(`Expected boolean but got ${ma(a)}: ${a}`);return a}function Lf(a){if(a==null||typeof a==="boolean")return a;if(typeof a==="number")return!!a}const Mf=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function Nf(a){switch(typeof a){case "bigint":return!0;case "number":return Gf(a);case "string":return Mf.test(a);default:return!1}}function Of(a){if(!Gf(a))throw Ce("enum");return a|0}function Pf(a){return a==null?a:Gf(a)?a|0:void 0} 
function Qf(a){if(typeof a!=="number")throw Ce("int32");if(!Gf(a))throw Ce("int32");return a|0}function Rf(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Gf(a)?a|0:void 0}function Sf(a){if(typeof a!=="number")throw Ce("uint32");if(!Gf(a))throw Ce("uint32");return a>>>0}function Tf(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Gf(a)?a>>>0:void 0} 
function Uf(a,b=0){if(!Nf(a))throw Ce("int64");const c=typeof a;switch(b){case 2048:switch(c){case "string":return Vf(a);case "bigint":return String(Df(64,a));default:return $f(a)}case 4096:switch(c){case "string":return ag(a);case "bigint":return qf(Df(64,a));default:return bg(a)}case 0:switch(c){case "string":return Vf(a);case "bigint":return qf(Df(64,a));default:return cg(a)}default:return wc(b,"Unknown format requested type for int64")}}function dg(a){return a==null?a:Uf(a,0)} 
function eg(a){if(a[0]==="-")return!1;const b=a.length;return b<20?!0:b===20&&Number(a.substring(0,6))<184467}function fg(a){const b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function gg(a){if(a<0){yf(a);var b=Af(uf,vf);a=Number(b);return Ff(a)?a:b}b=String(a);if(eg(b))return b;yf(a);return zf(uf,vf)} 
function cg(a){a=Hf(a);if(!Ff(a)){yf(a);var b=uf,c=vf;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);b=zf(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function hg(a){a=Hf(a);return a>=0&&Ff(a)?a:gg(a)}function $f(a){a=Hf(a);if(Ff(a))a=String(a);else{{const b=String(a);fg(b)?a=b:(yf(a),a=Bf())}}return a}function ig(a){a=Hf(a);if(a>=0&&Ff(a))a=String(a);else{{const b=String(a);eg(b)?a=b:(yf(a),a=Af(uf,vf))}}return a} 
function Vf(a){var b=Hf(Number(a));if(Ff(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));fg(a)||(Cf(a),a=Bf());return a}function ag(a){var b=Hf(Number(a));if(Ff(b))return qf(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return qf(Df(64,BigInt(a)))}function bg(a){return Ff(a)?qf(cg(a)):qf($f(a))}function jg(a){var b=Hf(Number(a));if(Ff(b)&&b>=0)return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));eg(a)||(Cf(a),a=Af(uf,vf));return a} 
function kg(a){if(a==null)return a;if(typeof a==="bigint")return tf(a)?a=Number(a):(a=Df(64,a),a=tf(a)?Number(a):String(a)),a;if(Nf(a))return typeof a==="number"?cg(a):Vf(a)}function lg(a){const b=typeof a;if(a==null)return a;if(b==="bigint")return qf(Df(64,a));if(Nf(a))return b==="string"?ag(a):bg(a)} 
function mg(a,b=0){if(!Nf(a))throw Ce("uint64");const c=typeof a;switch(b){case 2048:switch(c){case "string":return jg(a);case "bigint":return String(Ef(64,a));default:return ig(a)}case 4096:switch(c){case "string":return b=Hf(Number(a)),Ff(b)&&b>=0?a=qf(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=qf(Ef(64,BigInt(a)))),a;case "bigint":return qf(Ef(64,a));default:return Ff(a)?qf(hg(a)):qf(ig(a))}case 0:switch(c){case "string":return jg(a);case "bigint":return qf(Ef(64,a));default:return hg(a)}default:return wc(b, 
"Unknown format requested type for int64")}}function ng(a){return a==null?a:mg(a,0)}function og(a){const b=typeof a;if(a==null)return a;if(b==="bigint")return String(Ef(64,a));if(Nf(a))return b==="string"?jg(a):ig(a)}function pg(a){if(a==null)return a;const b=typeof a;if(b==="bigint")return String(Df(64,a));if(Nf(a)){if(b==="string")return Vf(a);if(b==="number")return cg(a)}} 
function qg(a){if(a==null)return a;const b=typeof a;if(b==="bigint")return String(Ef(64,a));if(Nf(a)){if(b==="string")return jg(a);if(b==="number")return hg(a)}}function rg(a){if(typeof a!=="string")throw Error();return a}function sg(a){if(a!=null&&typeof a!=="string")throw Error();return a}function tg(a){return a==null||typeof a==="string"?a:void 0} 
function ug(a,b,c,d){if(a!=null&&typeof a==="object"&&Ve(a))return a;if(!Array.isArray(a))return c?d&2?((a=b[Fe])||(a=new b,Qe(a.T),a=b[Fe]=a),b=a):b=new b:b=void 0,b;let e=c=a[u]|0;e===0&&(e|=d&32);e|=d&2;e!==c&&(a[u]=e);return new b(a)}function vg(a,b,c){return b?rg(a):tg(a)??(c?"":void 0)};function wg(a){return a};const xg={},yg=(()=>class extends Map{constructor(){super()}})();function zg(a){return a}function Ag(a){if(a.Bc&2)throw Error("Cannot mutate an immutable Map");} 
var Eg=class extends yg{constructor(a,b,c=zg,d=zg){super();let e=a[u]|0;e|=64;this.Bc=a[u]=e;this.Nb=b;this.Yd=c;this.Zh=this.Nb?Bg:d;for(let f=0;f<a.length;f++){const g=a[f],h=c(g[0],!1,!0);let k=g[1];b?k===void 0&&(k=null):k=d(g[1],!1,!0,void 0,void 0,e);super.set(h,k)}}Tk(){var a=Cg;if(this.size!==0)return Array.from(super.entries(),a)}Wh(){return Array.from(super.entries())}clear(){Ag(this);super.clear()}delete(a){Ag(this);return super.delete(this.Yd(a,!0,!1))}entries(){if(this.Nb){var a=super.keys(); 
a=new Ze(a,Dg,this)}else a=super.entries();return a}values(){if(this.Nb){var a=super.keys();a=new Ze(a,Eg.prototype.get,this)}else a=super.values();return a}forEach(a,b){this.Nb?super.forEach((c,d,e)=>{a.call(b,e.get(d),d,e)}):super.forEach(a,b)}set(a,b){Ag(this);a=this.Yd(a,!0,!1);return a==null?this:b==null?(super.delete(a),this):super.set(a,this.Zh(b,!0,!0,this.Nb,!1,this.Bc))}has(a){return super.has(this.Yd(a,!1,!1))}get(a){a=this.Yd(a,!1,!1);const b=super.get(a);if(b!==void 0){var c=this.Nb; 
return c?(c=this.Zh(b,!1,!0,c,this.Mi,this.Bc),c!==b&&super.set(a,c),c):b}}[Symbol.iterator](){return this.entries()}};Eg.prototype.toJSON=void 0;function Bg(a,b,c,d,e,f){a=ug(a,d,c,f);e&&(a=Fg(a));return a}function Dg(a){return[a,this.get(a)]}let Gg;function Hg(){return Gg||(Gg=new Eg(Qe([]),void 0,void 0,void 0,xg))};function Ig(a,b,c,d,e){d=d?!!(b&32):void 0;const f=[];var g=a.length;let h,k,l,m=!1;b&64?(b&256?(g--,h=a[g],k=g):(k=4294967295,h=void 0),e||b&512||(m=!0,l=(Jg??wg)(h?k- -1:b>>15&1023||536870912,-1,a,h),k=l+-1)):(k=4294967295,b&1||(h=g&&a[g-1],We(h)?(g--,k=g,l=0):h=void 0));let n=void 0;for(let p=0;p<g;p++){let t=a[p];t!=null&&(t=c(t,d))!=null&&(p>=k?(n??(n={}))[p- -1]=t:f[p]=t)}if(h)for(let p in h)Object.prototype.hasOwnProperty.call(h,p)&&(a=h[p],a!=null&&(a=c(a,d))!=null&&(g=+p,g<l?f[g+-1]=a:(n?? 
(n={}))[p]=a));n&&(m?f.push(n):f[k]=n);e&&(f[u]=b&33522241|(n!=null?290:34));return f}function Cg(a){a[0]=Kg(a[0]);a[1]=Kg(a[1]);return a} 
function Kg(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return tf(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[u]|0;return a.length===0&&b&1?void 0:Ig(a,b,Kg,!1,!1)}if(Ve(a))return Lg(a);if(a instanceof ve){b=a.g;if(b==null)a="";else if(typeof b==="string")a=b;else{let c="",d=0;const e=b.length-10240;for(;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));c+=String.fromCharCode.apply(null,d?b.subarray(d): 
b);a=a.g=btoa(c)}return a}if(a instanceof Eg)return a.Tk();return}return a}var Mg=se?structuredClone:a=>Ig(a,0,Kg,void 0,!1);let Jg;function Lg(a){a=a.T;return Ig(a,a[u]|0,Kg,void 0,!1)};let Ng,Og;function Pg(a){switch(typeof a){case "boolean":return Ng||(Ng=[0,void 0,!0]);case "number":return a>0?void 0:a===0?Og||(Og=[0,void 0]):[-a,void 0];case "string":return[0,a];case "object":return a}}function Qg(a,b,c){a=Rg(a,b[0],b[1],c?1:2);b!==Ng&&c&&Ne(a,8192);return a} 
function Rg(a,b,c,d){if(a==null){var e=96;c?(a=[c],e|=512):a=[];b&&(e=e&-33521665|(b&1023)<<15)}else{if(!Array.isArray(a))throw Error("narr");e=a[u]|0;8192&e||!(64&e)||2&e||Sg();if(e&1024)throw Error("farr");if(e&64)return d!==3||e&16384||(a[u]=e|16384),a;d===1||d===2||(e|=64);if(c&&(e|=512,c!==a[0]))throw Error("mid");a:{c=a;var f=c.length;if(f){var g=f-1;const k=c[g];if(We(k)){e|=256;b=e&512?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var h in k)if(Object.prototype.hasOwnProperty.call(k,h))if(f= 
+h,f<g)c[f+b]=k[h],delete k[h];else break;e=e&-33521665|(g&1023)<<15;break a}}if(b){h=Math.max(b,f-(e&512?0:-1));if(h>1024)throw Error("spvt");e=e&-33521665|(h&1023)<<15}}}d===3&&(e|=16384);a[u]=e;return a}function Sg(){De(Ie,5)};function Tg(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[u]|0;return a.length===0&&c&1?void 0:c&2?a:b&&(c===0||c&32&&!(c&64)&&c&16)?(Ne(a,34),c&4&&Object.freeze(a),a):Ig(a,c,Tg,b!==void 0,!0)}if(Ve(a))return Ug(a);if(a instanceof Eg){b=a.Bc;if(b&2)return a;if(!a.size)return;c=Qe(a.Wh());if(a.Nb)for(a=0;a<c.length;a++){const d=c[a];let e=d[1];if(e==null||typeof e!=="object")e=void 0;else if(Ve(e))e=Ug(e);else if(Array.isArray(e)){const f=e[u]|0;b&32&&(f===0||f&32&&!(f&64)&&f&16)? 
Ne(e,34):e=Ig(e,f,Tg,!0,!0)}else e=void 0;d[1]=e}return c}if(a instanceof ve)return a}function Ug(a){const b=a.T,c=b[u]|0;return c&2?a:Ig(b,c,Tg,!0,!0)}function Vg(a){const b=a.T;a=new a.constructor(Ig(b,b[u]|0,Tg,!0,!0));Oe(a.T,2);return a}function Fg(a){const b=a.T;if(!((b[u]|0)&2))return a;a=new a.constructor(Ig(b,b[u]|0,Tg,!0,!0));Oe(a.T,2);return a}function Wg(a){const b=a.T,c=b[u]|0;return c&2?a:new a.constructor(Ig(b,c,Tg,!0,!0))};const Xg=qf(0);function Yg(a,b){a=a.T;return Zg(a,a[u]|0,b)}function Zg(a,b,c,d){if(c===-1)return null;const e=c+(b&512?0:-1),f=a.length-1;let g;if(e>=f&&b&256)b=a[f][c],g=!0;else if(e<=f)b=a[e];else return;if(d&&b!=null){d=d(b);if(d==null)return d;if(d!==b)return g?a[f][c]=d:a[e]=d,d}return b}function $g(a,b,c){const d=a.T;let e=d[u]|0;Ye(e);ah(d,e,b,c);return a} 
function ah(a,b,c,d){const e=b&512?0:-1,f=c+e;var g=a.length-1;if(f>=g&&b&256)return a[g][c]=d,b;if(f<=g)return a[f]=d,b;d!==void 0&&(g=b>>15&1023||536870912,c>=g?d!=null&&(a[g+e]={[c]:d},b|=256,a[u]=b):a[f]=d);return b}function bh(a,b,c){return ch(a,b,c)!==void 0}function dh(a,b){a=a.T;return Zg(a,a[u]|0,b,Jf)}function eh(a){return a===$e?2:4} 
function fh(a,b,c,d,e,f){const g=a.T;let h=g[u]|0;const k=2&h?1:d;e=!!e;d=gh(g,h,b);var l=d[u]|0;var m=l;4&m?f==null?a=!1:(!e&&f===0&&(2048&m||4096&m)&&(a.constructor[He]=(a.constructor[He]|0)+1)<5&&Be(),a=f===0?!1:!(f&m)):a=!0;if(a){4&l&&(d=[...d],l=hh(l,h),h=ah(g,h,b,d));for(m=a=0;a<d.length;a++){const n=c(d[a]);n!=null&&(d[m++]=n)}m<a&&(d.length=m);l=ih(l,h);l=(l|20)&-2049;l&=-4097;f&&(l|=f);d[u]=l;2&l&&Object.freeze(d)}k===1||k===4&&32&l?jh(l)||(e=l,l|=2,l!==e&&(d[u]=l),Object.freeze(d)):(k=== 
2&&jh(l)&&(d=[...d],l=hh(l,h),l=kh(l,h,e),d[u]=l,h=ah(g,h,b,d)),jh(l)||(b=l,l=kh(l,h,e),l!==b&&(d[u]=l)));return d}function gh(a,b,c){a=Zg(a,b,c);return Array.isArray(a)?a:Le}function ih(a,b){a===0&&(a=hh(a,b),a|=16);return a|1}function jh(a){return!!(2&a)&&!!(4&a)||!!(1024&a)}function lh(a){return Xe(a,!0)} 
function mh(a){var b=nh,c=a.T;const d=c[u]|0;var e=Zg(c,d,14);a=d&2;a:{var f=e,g=d&2;e=!1;if(f==null){if(g){c=Hg();break a}f=[]}else if(f.constructor===Eg){if((f.Bc&2)==0||g){c=f;break a}f=f.Wh()}else Array.isArray(f)?e=!!((f[u]|0)&2):f=[];if(g){if(!f.length){c=Hg();break a}e||(e=!0,Qe(f))}else if(e){e=!1;g=[...f];for(f=0;f<g.length;f++){const h=g[f]=[...g[f]];Array.isArray(h[1])&&(h[1]=Qe(h[1]))}f=g}e||((f[u]|0)&64?Oe(f,32):32&d&&Re(f));e=new Eg(f,b,vg,void 0);ah(c,d,14,e);c=e}!a&&b&&(c.Mi=!0);return c} 
function oh(a,b,c,d){const e=a.T;let f=e[u]|0;Ye(f);if(c==null)return ah(e,f,b),a;let g=c[u]|0,h=g;var k=jh(g);let l=k||Object.isFrozen(c);k||(g=0);l||(c=[...c],h=0,g=hh(g,f),g=kh(g,f,!0),l=!1);g|=21;k=Pe(g)??0;for(let m=0;m<c.length;m++){const n=c[m],p=d(n,k);Object.is(n,p)||(l&&(c=[...c],h=0,g=hh(g,f),g=kh(g,f,!0),l=!1),c[m]=p)}g!==h&&(l&&(c=[...c],g=hh(g,f),g=kh(g,f,!0)),c[u]=g);ah(e,f,b,c);return a} 
function ph(a,b,c,d){const e=a.T;let f=e[u]|0;Ye(f);ah(e,f,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}function qh(a,b,c,d){const e=a.T;var f=e[u]|0;Ye(f);if(d==null){var g=rh(e);if(sh(g,e,f,c)===b)g.set(c,0);else return a}else{g=rh(e);const h=sh(g,e,f,c);h!==b&&(h&&(f=ah(e,f,h)),g.set(c,b))}ah(e,f,b,d);return a}function th(a,b,c){return uh(a,b)===c?c:-1}function uh(a,b){a=a.T;return sh(rh(a),a,a[u]|0,b)}function rh(a){return a[Ge]??(a[Ge]=new Map)} 
function sh(a,b,c,d){let e=a.get(d);if(e!=null)return e;e=0;for(let f=0;f<d.length;f++){const g=d[f];Zg(b,c,g)!=null&&(e!==0&&(c=ah(b,c,e)),e=g)}a.set(d,e);return e}function vh(a){var b=wh;a=a.T;let c=a[u]|0;Ye(c);const d=Zg(a,c,26);b=Fg(ug(d,b,!0,c));d!==b&&ah(a,c,26,b);return b}function ch(a,b,c){a=a.T;let d=a[u]|0;const e=Zg(a,d,c);b=ug(e,b,!1,d);b!==e&&b!=null&&ah(a,d,c,b);return b}function xh(a,b,c){(a=ch(a,b,c))||(a=b[Fe])||(a=new b,Qe(a.T),a=b[Fe]=a);return a} 
function v(a,b,c){b=ch(a,b,c);if(b==null)return b;a=a.T;let d=a[u]|0;if(!(d&2)){const e=Fg(b);e!==b&&(b=e,ah(a,d,c,b))}return b} 
function yh(a,b,c,d,e,f,g){a=a.T;var h=!!(2&b);const k=h?1:e;f=!!f;g&&(g=!h);e=gh(a,b,d);var l=e[u]|0;h=!!(4&l);if(!h){l=ih(l,b);var m=e,n=b;const p=!!(2&l);p&&(n|=2);let t=!p,z=!0,w=0,B=0;for(;w<m.length;w++){const I=ug(m[w],c,!1,n);if(I instanceof c){if(!p){const T=!!((I.T[u]|0)&2);t&&(t=!T);z&&(z=T)}m[B++]=I}}B<w&&(m.length=B);l|=4;l=z?l|16:l&-17;l=t?l|8:l&-9;m[u]=l;p&&Object.freeze(m)}if(g&&!(8&l||!e.length&&(k===1||k===4&&32&l))){jh(l)&&(e=[...e],l=hh(l,b),b=ah(a,b,d,e));c=e;g=l;for(m=0;m<c.length;m++)l= 
c[m],n=Fg(l),l!==n&&(c[m]=n);g|=8;g=c.length?g&-17:g|16;l=c[u]=g}k===1||k===4&&32&l?jh(l)||(b=l,l|=!e.length||16&l&&(!h||32&l)?2:1024,l!==b&&(e[u]=l),Object.freeze(e)):(k===2&&jh(l)&&(e=[...e],l=hh(l,b),l=kh(l,b,f),e[u]=l,b=ah(a,b,d,e)),jh(l)||(d=l,l=kh(l,b,f),l!==d&&(e[u]=l)));return e}function zh(a,b,c,d){const e=a.T[u]|0;return yh(a,e,b,c,d,!1,!(2&e))}function x(a,b,c){c==null&&(c=void 0);return $g(a,b,c)}function Ah(a,b,c,d){d==null&&(d=void 0);return qh(a,b,c,d)} 
function Kh(a,b,c){const d=a.T;let e=d[u]|0;Ye(e);if(c==null)return ah(d,e,b),a;let f=c[u]|0,g=f;const h=jh(f),k=h||Object.isFrozen(c);let l=!0,m=!0;for(let p=0;p<c.length;p++){var n=c[p];h||(n=!!((n.T[u]|0)&2),l&&(l=!n),m&&(m=n))}h||(f=l?13:5,f=m?f|16:f&-17);k&&f===g||(c=[...c],g=0,f=hh(f,e),f=kh(f,e,!0));f!==g&&(c[u]=f);ah(d,e,b,c);return a}function hh(a,b){2&a&&(a|=16);a=(2&b?a|2:a&-3)|32;return a&=-1025}function kh(a,b,c){32&b&&c||(a&=-33);return a} 
function Lh(a,b,c,d,e,f,g){Ye(a.T[u]|0);b=fh(a,b,e,2,!0);e=Pe(b[u]|0)??0;if(g)if(Array.isArray(d))for(f=d.length,g=0;g<f;g++)b.push(c(d[g],e));else for(const h of d)b.push(c(h,e));else{if(f)throw Error();b.push(c(d,e))}return a}function Mh(a,b,c,d){var e=a.T[u]|0;Ye(e);b=yh(a,e,c,b,2,!0);d=d!=null?d:new c;b.push(d);e=c=b[u]|0;(d.T[u]|0)&2?(c&=-9,b.length===1&&(c|=16)):c&=-17;c!==e&&(b[u]=c);return a}function Nh(a,b){return Lf(Yg(a,b))}function Oh(a,b){return Rf(Yg(a,b))} 
function y(a,b){return tg(Yg(a,b))}function Ph(a,b){return Pf(Yg(a,b))}function A(a,b){return Nh(a,b)??!1}function Qh(a,b){return Oh(a,b)??0}function Rh(a,b){return lg(Yg(a,b))??Xg}function Sh(a,b,c=0){return dh(a,b)??c}function C(a,b){return y(a,b)??""}function D(a,b){return Ph(a,b)??0}function Th(a,b){return fh(a,b,Rf,eh())}function Uh(a,b){return fh(a,b,tg,eh())}function Vh(a,b){return fh(a,b,Pf,eh())}function Wh(a,b,c,d){return v(a,b,th(a,d,c))}function Xh(a,b){return Nh(a,b)??void 0} 
function Yh(a,b){return Oh(a,b)??void 0}function Zh(a,b){return Ph(a,b)??void 0}function $h(a,b,c){return $g(a,b,c==null?c:Kf(c))}function E(a,b,c){return ph(a,b,c==null?c:Kf(c),!1)}function ai(a,b,c){return $g(a,b,c==null?c:Qf(c))}function bi(a,b,c){return ph(a,b,c==null?c:Qf(c),0)}function ci(a,b,c){return $g(a,b,dg(c))}function di(a,b,c){return ph(a,b,dg(c),"0")}function ei(a,b,c){return $g(a,b,sg(c))}function fi(a,b,c){return ph(a,b,sg(c),"")} 
function gi(a,b,c){return $g(a,b,c==null?c:Of(c))}function F(a,b,c){return ph(a,b,c==null?c:Of(c),0)}function hi(a,b){return Nh(a,b)!=null};function ii(a){return JSON.stringify(Lg(a))}var G=class{constructor(a){this.T=Rg(a,void 0,void 0,3)}toJSON(){return Lg(this)}};G.prototype[Je]=Te;function ji(a,b){if(b==null)return new a;if(!Array.isArray(b))throw Error("must be an array");if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error("arrays passed to jspb constructors must be mutable");Ne(b,128);return new a(Re(b))};function ki(a){a=BigInt.asUintN(64,a);return new li(Number(a&BigInt(4294967295)),Number(a>>BigInt(32)))}function mi(a){if(!a)return ni||(ni=new li(0,0));if(!/^\d+$/.test(a))return null;Cf(a);return new li(uf,vf)}var li=class{constructor(a,b){this.i=a>>>0;this.g=b>>>0}};let ni;function oi(a){a=BigInt.asUintN(64,a);return new pi(Number(a&BigInt(4294967295)),Number(a>>BigInt(32)))}function qi(a){if(!a)return ri||(ri=new pi(0,0));if(!/^-?\d+$/.test(a))return null;Cf(a);return new pi(uf,vf)} 
var pi=class{constructor(a,b){this.i=a>>>0;this.g=b>>>0}};let ri;function si(a,b,c){for(;c>0||b>127;)a.g.push(b&127|128),b=(b>>>7|c<<25)>>>0,c>>>=7;a.g.push(b)}function ti(a,b){for(;b>127;)a.g.push(b&127|128),b>>>=7;a.g.push(b)}function ui(a,b){if(b>=0)ti(a,b);else{for(let c=0;c<9;c++)a.g.push(b&127|128),b>>=7;a.g.push(1)}}function vi(a,b){a.g.push(b>>>0&255);a.g.push(b>>>8&255);a.g.push(b>>>16&255);a.g.push(b>>>24&255)}var wi=class{constructor(){this.g=[]}length(){return this.g.length}end(){const a=this.g;this.g=[];return a}};function xi(a,b){b.length!==0&&(a.j.push(b),a.i+=b.length)}function yi(a,b){xi(a,a.g.end());xi(a,b)}function zi(a,b,c){ti(a.g,b*8+c)}function Ai(a,b){zi(a,b,2);b=a.g.end();xi(a,b);b.push(a.i);return b}function Bi(a,b){var c=b.pop();for(c=a.i+a.g.length()-c;c>127;)b.push(c&127|128),c>>>=7,a.i++;b.push(c);a.i++}function Ci(a){xi(a,a.g.end());const b=new Uint8Array(a.i),c=a.j,d=c.length;let e=0;for(let f=0;f<d;f++){const g=c[f];b.set(g,e);e+=g.length}a.j=[b];return b} 
function Di(a,b,c,d){c!=null&&(b=Ai(a,b),d(c,a),Bi(a,b))}function Ei(a,b,c){var d=Fi;if(c!=null)for(let e=0;e<c.length;e++){const f=Ai(a,b);d(c[e],a);Bi(a,f)}}var Gi=class{constructor(){this.j=[];this.i=0;this.g=new wi}};function Hi(){const a=class{constructor(){throw Error();}};Object.setPrototypeOf(a,a.prototype);return a}var Ii=Hi(),Ji=Hi(),Ki=Hi(),Li=Hi(),Mi=Hi(),Ni=Hi(),Oi=Hi(),Pi=Hi();var Qi=class{constructor(a,b){this.g=a;a=Aa(Ii);this.i=!!a&&b===a||!1}};function Ri(a,b,c,d,e){Di(a,c,Si(b,d),e)}const Ti=new Qi(Ri,Ii),Ui=new Qi(Ri,Ii);var Vi=Symbol(),Wi=Symbol();let Xi,Yi; 
function Zi(a){var b=$i,c=aj,d=a[Vi];if(d)return d;d={};d.Lo=a;d.Rf=Pg(a[0]);var e=a[1];let f=1;e&&e.constructor===Object&&(d.oj=e,e=a[++f],typeof e==="function"&&(d.Oj=!0,Xi??(Xi=e),Yi??(Yi=a[f+1]),e=a[f+=2]));const g={};for(;e&&Array.isArray(e)&&e.length&&typeof e[0]==="number"&&e[0]>0;){for(var h=0;h<e.length;h++)g[e[h]]=e;e=a[++f]}for(h=1;e!==void 0;){typeof e==="number"&&(h+=e,e=a[++f]);let m;var k=void 0;e instanceof Qi?m=e:(m=Ti,f--);if(m?.i){e=a[++f];k=a;var l=f;typeof e==="function"&&(e= 
e(),k[l]=e);k=e}e=a[++f];l=h+1;typeof e==="number"&&e<0&&(l-=e,e=a[++f]);for(;h<l;h++){const n=g[h];k?c(d,h,m,k,n):b(d,h,m,n)}}return a[Vi]=d}function Si(a,b){if(a instanceof G)return a.T;if(Array.isArray(a))return Qg(a,b,!1)};function $i(a,b,c){a[b]=c.g}function aj(a,b,c,d){let e,f;const g=c.g;a[b]=(h,k,l)=>g(h,k,l,f||(f=Zi(d).Rf),e||(e=bj(d)))}function bj(a){let b=a[Wi];if(!b){const c=Zi(a);b=(d,e)=>cj(d,e,c);a[Wi]=b}return b}function cj(a,b,c){af(a,a[u]|0|(c.Rf[1]?512:0),(d,e)=>{if(e!=null){var f=dj(c,d);f&&f(b,e,d)}})} 
function dj(a,b){var c=a[b];if(c)return c;if(c=a.oj)if(c=c[b]){c=Array.isArray(c)?c[0]instanceof Qi?c:[Ui,c]:[c,void 0];var d=c[0].g;if(c=c[1]){const e=bj(c),f=Zi(c).Rf;c=a.Oj?Yi(f,e):(g,h,k)=>d(g,h,k,f,e)}else c=d;return a[b]=c}};function ej(a,b,c){if(Array.isArray(b)){var d=b[u]|0;if(d&4)return b;for(var e=0,f=0;e<b.length;e++){const g=a(b[e]);g!=null&&(b[f++]=g)}f<e&&(b.length=f);c&&(b[u]=(d|21)&-6145,d&2&&Object.freeze(b));return b}}function fj(a,b){return new Qi(a,b)}function gj(a,b){return new Qi(a,b)}var hj=new Qi(function(a,b,c,d,e){if(b instanceof Eg)b.forEach((f,g)=>{Di(a,c,Qg([g,f],d,!1),e)});else if(Array.isArray(b))for(let f=0;f<b.length;f++){const g=b[f];Array.isArray(g)&&Di(a,c,Qg(g,d,!1),e)}},Ii); 
function ij(a,b,c){b=Jf(b);b!=null&&(zi(a,c,1),a=a.g,c=wf||(wf=new DataView(new ArrayBuffer(8))),c.setFloat64(0,+b,!0),uf=c.getUint32(0,!0),vf=c.getUint32(4,!0),vi(a,uf),vi(a,vf))}function jj(a,b,c){b=pg(b);if(b!=null){switch(typeof b){case "string":qi(b)}if(b!=null)switch(zi(a,c,0),typeof b){case "number":a=a.g;yf(b);si(a,uf,vf);break;case "bigint":c=oi(b);si(a.g,c.i,c.g);break;default:c=qi(b),si(a.g,c.i,c.g)}}} 
function kj(a,b,c){b=ej(pg,b,!1);if(b!=null&&b.length){c=Ai(a,c);for(let e=0;e<b.length;e++){const f=b[e];switch(typeof f){case "number":var d=a.g;yf(f);si(d,uf,vf);break;case "bigint":d=oi(f);si(a.g,d.i,d.g);break;default:d=qi(f),si(a.g,d.i,d.g)}}Bi(a,c)}}function lj(a,b,c){b=qg(b);if(b!=null){switch(typeof b){case "string":mi(b)}if(b!=null)switch(zi(a,c,0),typeof b){case "number":a=a.g;yf(b);si(a,uf,vf);break;case "bigint":c=ki(b);si(a.g,c.i,c.g);break;default:c=mi(b),si(a.g,c.i,c.g)}}} 
function mj(a,b,c){b=Rf(b);b!=null&&b!=null&&(zi(a,c,0),ui(a.g,b))}function nj(a,b,c){b=Lf(b);b!=null&&(zi(a,c,0),a.g.g.push(b?1:0))}function oj(a,b,c){b=tg(b);b!=null&&(b=ee(b),zi(a,c,2),ti(a.g,b.length),yi(a,b))}function pj(a,b,c,d,e){Di(a,c,Si(b,d),e)}function qj(a,b,c){b=Rf(b);b!=null&&(b=parseInt(b,10),zi(a,c,0),ui(a.g,b))} 
var rj=fj(ij,Oi),sj=fj(ij,Oi),tj=fj(function(a,b,c){b=Jf(b);b!=null&&(zi(a,c,5),a=a.g,c=wf||(wf=new DataView(new ArrayBuffer(8))),c.setFloat32(0,+b,!0),vf=0,uf=c.getUint32(0,!0),vi(a,uf))},Hi()),uj=gj(kj,Mi),vj=fj(jj,Mi),wj=gj(kj,Mi),xj=fj(jj,Mi),yj=fj(jj,Mi),zj=fj(lj,Ni),Aj=gj(function(a,b,c){b=ej(qg,b,!1);if(b!=null&&b.length){c=Ai(a,c);for(let f=0;f<b.length;f++){var d=b[f];switch(typeof d){case "number":var e=a.g;yf(d);si(e,uf,vf);break;case "bigint":e=Number(d);Number.isSafeInteger(e)?(d=a.g, 
yf(e),si(d,uf,vf)):(d=ki(d),si(a.g,d.i,d.g));break;default:d=mi(d),si(a.g,d.i,d.g)}}Bi(a,c)}},Ni),Bj=fj(lj,Ni),Cj=fj(mj,Li),Dj=gj(function(a,b,c){b=ej(Rf,b,!0);if(b!=null&&b.length){c=Ai(a,c);for(let d=0;d<b.length;d++)ui(a.g,b[d]);Bi(a,c)}},Li),Ej=fj(mj,Li),Fj=fj(function(a,b,c){b=Tf(b);b!=null&&(zi(a,c,5),vi(a.g,b))},Hi()),Gj=fj(nj,Ji),Hj=fj(nj,Ji),Ij=fj(nj,Ji),Jj=fj(oj,Ki),Kj=gj(function(a,b,c){b=ej(tg,b,!0);if(b!=null)for(let g=0;g<b.length;g++){var d=a,e=c,f=b[g];f!=null&&(f=ee(f),zi(d,e,2), 
ti(d.g,f.length),yi(d,f))}},Ki),Lj=fj(oj,Ki),Mj=fj(oj,Ki),Nj=function(a,b,c=Ii){return new Qi(b,c)}(function(a,b,c,d,e){if(a.g()!==2)return!1;var f=a.i;d=Qg(void 0,d,!0);var g=b[u]|0;Ye(g);var h=!!(64&g)||!(8192&g);let k=gh(b,g,c);var l=k!==Le;if(h||!l){l=h=k===Le?55:k[u]|0;if(2&l||jh(l)||4&l&&!(32&l))k=[...k],h=0,l=hh(l,g),g=ah(b,g,c,k);l=ih(l,g)&-13;l=kh(l,g,!0);l!==h&&(k[u]=l)}k.push(d);f.call(a,d,e);return!0},function(a,b,c,d,e){if(Array.isArray(b))for(let f=0;f<b.length;f++)pj(a,b[f],c,d,e)}), 
H=new Qi(pj,Ii),Oj=fj(function(a,b,c){b=Tf(b);b!=null&&b!=null&&(zi(a,c,0),ti(a.g,b))},Hi()),Pj=fj(qj,Pi),Qj=gj(function(a,b,c){b=ej(Rf,b,!0);if(b!=null&&b.length){c=Ai(a,c);for(let d=0;d<b.length;d++)ui(a.g,b[d]);Bi(a,c)}},Pi),Rj=fj(qj,Pi);function Sj(a){return()=>{var b;(b=a[Fe])||(b=new a,Qe(b.T),b=a[Fe]=b);return b}}function Tj(a){return b=>{const c=new Gi;cj(b.T,c,Zi(a));return Ci(c)}}function Uj(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b=new a(Re(b))}return b}};hd`https://www.google.com/recaptcha/api2/aframe`;function Vj(a){var b=window;new Promise((c,d)=>{function e(){f.onload=null;f.onerror=null;f.parentElement?.removeChild(f)}const f=b.document.createElement("script");f.onload=()=>{e();c()};f.onerror=()=>{e();d(void 0)};f.type="text/javascript";Bc(f,a);b.document.readyState!=="complete"?qb(b,"load",()=>{b.document.body.appendChild(f)}):b.document.body.appendChild(f)})};async function Wj(a){var b=`${a.Za?"https://ep1.adtrafficquality.google/getconfig/sodar":"https://pagead2.googlesyndication.com/getconfig/sodar"}?sv=${200}&tid=${a.g}`+`&tv=${a.i}&st=`+`${a.yc}`;let c=void 0;try{c=await Xj(b)}catch(g){}if(c){b=a.dd||c.sodar_query_id;var d=c.rc_enable!==void 0&&a.j?c.rc_enable:"n",e=c.bg_snapshot_delay_ms===void 0?"0":c.bg_snapshot_delay_ms,f=c.is_gen_204===void 0?"1":c.is_gen_204;if(b&&c.bg_hash_basename&&c.bg_binary)return{context:a.l,Hi:c.bg_hash_basename,Gi:c.bg_binary, 
Tj:a.g+"_"+a.i,dd:b,yc:a.yc,Xd:d,De:e,Vd:f,Za:a.Za}}}let Xj=a=>new Promise((b,c)=>{const d=new XMLHttpRequest;d.onreadystatechange=()=>{d.readyState===d.DONE&&(d.status>=200&&d.status<300?b(JSON.parse(d.responseText)):c())};d.open("GET",a,!0);d.send()}); 
async function Yj(a){if(a=await Wj(a)){var b=window,c=b.GoogleGcLKhOms;c&&typeof c.push==="function"||(c=b.GoogleGcLKhOms=[]);c.push({_ctx_:a.context,_bgv_:a.Hi,_bgp_:a.Gi,_li_:a.Tj,_jk_:a.dd,_st_:a.yc,_rc_:a.Xd,_dl_:a.De,_g2_:a.Vd,_atqg_:a.Za===void 0?"0":a.Za?"1":"0"});if(c=b.GoogleDX5YKUSk)b.GoogleDX5YKUSk=void 0,c[1]();a=a.Za?hd`https://ep2.adtrafficquality.google/sodar/${"sodar2"}.js`:hd`https://tpc.googlesyndication.com/sodar/${"sodar2"}.js`;Vj(a)}};function Zj(a,b){return fi(a,1,b)}var ak=class extends G{g(){return C(this,1)}};function bk(a,b){return x(a,5,b)}function ck(a,b){return fi(a,3,b)}function dk(a,b){return E(a,6,b)}var ek=class extends G{};function fk(a){switch(a){case 1:return"gda";case 2:return"gpt";case 3:return"ima";case 4:return"pal";case 5:return"xfad";case 6:return"dv3n";case 7:return"spa";default:return"unk"}}var gk=class{constructor(a){this.g=a.i;this.i=a.j;this.l=a.l;this.dd=a.dd;this.A=a.da();this.yc=a.yc;this.Xd=a.Xd;this.De=a.De;this.Vd=a.Vd;this.j=a.g;this.Za=a.Za}},Tk=class{constructor(a,b,c){this.i=a;this.j=b;this.l=c;this.A=window;this.yc="env";this.Xd="n";this.De="0";this.Vd="1";this.g=!0;this.Za=!1}da(){return this.A}build(){return new gk(this)}};function Uk(a){var b=new Vk;return ei(b,1,a)}var Vk=class extends G{getValue(){return C(this,1)}getVersion(){return D(this,5)}};var Wk=class extends G{};var Xk=class extends G{};function Yk(a,b,c=null,d=!1,e=!1){Zk(a,b,c,d,e)}function Zk(a,b,c,d,e=!1){a.google_image_requests||(a.google_image_requests=[]);const f=sd("IMG",a.document);if(c||d){const g=h=>{c&&c(h);d&&Ra(a.google_image_requests,f);rb(f,"load",g);rb(f,"error",g)};qb(f,"load",g);qb(f,"error",g)}e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)} 
function $k(a,b){let c=`https://${"pagead2.googlesyndication.com"}/pagead/gen_204?id=${b}`;vd(a,(d,e)=>{if(d||d===0)c+=`&${e}=${encodeURIComponent(String(d))}`});al(c)}function al(a){var b=window;b.fetch?b.fetch(a,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"}):Yk(b,a,void 0,!1,!1)};let bl=null;var cl=window;var dl=class extends G{};var el=class extends G{getCorrelator(){return Rh(this,1)}setCorrelator(a){return di(this,1,a)}};var fl=class extends G{};let gl=null,hl=null;function il(){if(gl!=null)return gl;gl=!1;try{const a=qd(q);a&&a.location.hash.indexOf("google_logging")!==-1&&(gl=!0)}catch(a){}return gl}function jl(){if(hl!=null)return hl;hl=!1;try{const a=qd(q);a&&a.location.hash.indexOf("auto_ads_logging")!==-1&&(hl=!0)}catch(a){}return hl}var kl=(a,b=[])=>{let c=!1;q.google_logging_queue||(c=!0,q.google_logging_queue=[]);q.google_logging_queue.push([a,b]);c&&il()&&rd(q.document,hd`https://pagead2.googlesyndication.com/pagead/js/logging_library.js`)};function ll(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0}ll.prototype.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};ll.prototype.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};ll.prototype.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};ll.prototype.scale=function(a,b){this.x*=a;this.y*=typeof b==="number"?b:a;return this};function ml(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d}aa=ml.prototype;aa.getWidth=function(){return this.right-this.left};aa.getHeight=function(){return this.bottom-this.top};function nl(a){return new ml(a.top,a.right,a.bottom,a.left)}aa.contains=function(a){return this&&a?a instanceof ml?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1}; 
function ol(a,b){return a.left<=b.right&&b.left<=a.right&&a.top<=b.bottom&&b.top<=a.bottom}aa.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};aa.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this}; 
aa.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};aa.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};function pl(a,b){this.width=a;this.height=b}function ql(a,b){return a==b?!0:a&&b?a.width==b.width&&a.height==b.height:!1}aa=pl.prototype;aa.aspectRatio=function(){return this.width/this.height};aa.isEmpty=function(){return!(this.width*this.height)};aa.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};aa.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this}; 
aa.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};aa.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};function rl(a,b,c,d){this.left=a;this.top=b;this.width=c;this.height=d}function sl(a,b){const c=Math.max(a.left,b.left),d=Math.min(a.left+a.width,b.left+b.width);if(c<=d){const e=Math.max(a.top,b.top);a=Math.min(a.top+a.height,b.top+b.height);if(e<=a)return new rl(c,e,d-c,a-e)}return null} 
function tl(a,b){var c=sl(a,b);if(!c||!c.height||!c.width)return[new rl(a.left,a.top,a.width,a.height)];c=[];let d=a.top,e=a.height;const f=a.left+a.width,g=a.top+a.height,h=b.left+b.width,k=b.top+b.height;b.top>a.top&&(c.push(new rl(a.left,a.top,a.width,b.top-a.top)),d=b.top,e-=b.top-a.top);k<g&&(c.push(new rl(a.left,k,a.width,g-k)),e=k-d);b.left>a.left&&c.push(new rl(a.left,d,b.left-a.left,e));h<f&&c.push(new rl(h,d,f-h,e));return c}aa=rl.prototype; 
aa.contains=function(a){return a instanceof ll?a.x>=this.left&&a.x<=this.left+this.width&&a.y>=this.top&&a.y<=this.top+this.height:this.left<=a.left&&this.left+this.width>=a.left+a.width&&this.top<=a.top&&this.top+this.height>=a.top+a.height};aa.ceil=function(){this.left=Math.ceil(this.left);this.top=Math.ceil(this.top);this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this}; 
aa.floor=function(){this.left=Math.floor(this.left);this.top=Math.floor(this.top);this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};aa.round=function(){this.left=Math.round(this.left);this.top=Math.round(this.top);this.width=Math.round(this.width);this.height=Math.round(this.height);return this};aa.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.width*=a;this.top*=b;this.height*=b;return this};const ul={"AMP-CAROUSEL":"ac","AMP-FX-FLYING-CARPET":"fc","AMP-LIGHTBOX":"lb","AMP-STICKY-AD":"sa"};function vl(a=q){let b=a.context||a.AMP_CONTEXT_DATA;if(!b)try{b=a.parent.context||a.parent.AMP_CONTEXT_DATA}catch{}return b?.pageViewId&&b?.canonicalUrl?b:null}function wl(a=vl()){return a&&a.mode?+a.mode.version||null:null}function xl(a=vl()){if(a&&a.container){a=a.container.split(",");const b=[];for(let c=0;c<a.length;c++)b.push(ul[a[c]]||"x");return b.join()}return null} 
function yl(){var a=vl();return a&&a.initialIntersection}function zl(){const a=yl();return a&&oa(a.rootBounds)?new pl(a.rootBounds.width,a.rootBounds.height):null}function Al(a=vl()){return a?kd(a.master)?a.master:null:null} 
function Bl(a,b){const c=a.ampInaboxIframes=a.ampInaboxIframes||[];let d=()=>{},e=()=>{};b&&(c.push(b),e=()=>{a.AMP&&a.AMP.inaboxUnregisterIframe&&a.AMP.inaboxUnregisterIframe(b);Ra(c,b);d()});if(a.ampInaboxInitialized)return e;a.ampInaboxPendingMessages=a.ampInaboxPendingMessages||[];const f=g=>{if(a.ampInaboxInitialized)g=!0;else{var h,k=g.data==="amp-ini-load";a.ampInaboxPendingMessages&&!k&&(h=/^amp-(\d{15,20})?/.exec(g.data))&&(a.ampInaboxPendingMessages.push(g),g=h[1],a.ampInaboxInitialized|| 
g&&!/^\d{15,20}$/.test(g)||a.document.querySelector('script[src$="amp4ads-host-v0.js"]')||rd(a.document,g?hd`https://cdn.ampproject.org/rtv/${g}/amp4ads-host-v0.js`:hd`https://cdn.ampproject.org/amp4ads-host-v0.js`));g=!1}g&&d()};c.google_amp_listener_added||(c.google_amp_listener_added=!0,qb(a,"message",f),d=()=>{rb(a,"message",f)});return e};function Cl(a){return a?new Dl(El(a)):Da||(Da=new Dl)}function Fl(a){a=a.document;a=a.compatMode=="CSS1Compat"?a.documentElement:a.body;return new pl(a.clientWidth,a.clientHeight)}function Gl(a){const b=a.scrollingElement?a.scrollingElement:Wc||a.compatMode!="CSS1Compat"?a.body||a.documentElement:a.documentElement;a=a.defaultView;return new ll(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)} 
function Hl(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)}function Il(a){a&&a.parentNode&&a.parentNode.removeChild(a)}function El(a){return a.nodeType==9?a:a.ownerDocument||a.document}var Jl={SCRIPT:1,STYLE:1,HEAD:1,IFRAME:1,OBJECT:1},Kl={IMG:" ",BR:"\n"}; 
function Ll(a){const b=[];Ml(a,b,!0);a=b.join("");a=a.replace(/ \xAD /g," ").replace(/\xAD/g,"");a=a.replace(/\u200B/g,"");a=a.replace(/ +/g," ");a!=" "&&(a=a.replace(/^\s*/,""));return a}function Ml(a,b,c){if(!(a.nodeName in Jl))if(a.nodeType==3)c?b.push(String(a.nodeValue).replace(/(\r\n|\r|\n)/g,"")):b.push(a.nodeValue);else if(a.nodeName in Kl)b.push(Kl[a.nodeName]);else for(a=a.firstChild;a;)Ml(a,b,c),a=a.nextSibling} 
function Nl(a,b,c){if(!b&&!c)return null;const d=b?String(b).toUpperCase():null;return Ol(a,function(e){return(!d||e.nodeName==d)&&(!c||typeof e.className==="string"&&Qa(e.className.split(/\s+/),c))})}function Ol(a,b){let c=0;for(;a;){if(b(a))return a;a=a.parentNode;c++}return null}function Dl(a){this.g=a||q.document||document}aa=Dl.prototype;aa.gi=function(a){var b=this.g;return typeof a==="string"?b.getElementById(a):a};aa.al=Dl.prototype.gi;function Pl(a,b){return Hl(a.g,b)} 
function Ql(a,b){var c=a.g;a=Hl(c,"DIV");Dc(a,b);if(a.childNodes.length==1)b=a.removeChild(a.firstChild);else for(b=c.createDocumentFragment();a.firstChild;)b.appendChild(a.firstChild);return b}aa.da=function(){return this.g.defaultView};aa.contains=function(a,b){return a&&b?a==b||a.contains(b):!1}; 
aa.qj=function(a){let b;const c=arguments.length;if(!c)return null;if(c==1)return arguments[0];const d=[];let e=Infinity;for(b=0;b<c;b++){for(var f=[],g=arguments[b];g;)f.unshift(g),g=g.parentNode;d.push(f);e=Math.min(e,f.length)}f=null;for(b=0;b<e;b++){g=d[0][b];for(let h=1;h<c;h++)if(g!=d[h][b])return f;f=g}return f};function Rl(a,b,c){if(typeof b==="string")(b=Sl(a,b))&&(a.style[b]=c);else for(const e in b){c=a;var d=b[e];const f=Sl(c,e);f&&(c.style[f]=d)}}var Tl={};function Sl(a,b){let c=Tl[b];if(!c){var d=Mc(b);c=d;a.style[d]===void 0&&(d=(Wc?"Webkit":Vc?"Moz":null)+Nc(d),a.style[d]!==void 0&&(c=d));Tl[b]=c}return c}function Ul(a,b){const c=a.style[Mc(b)];return typeof c!=="undefined"?c:a.style[Sl(a,b)]||""} 
function Vl(a,b){const c=El(a);return c.defaultView&&c.defaultView.getComputedStyle&&(a=c.defaultView.getComputedStyle(a,null))?a[b]||a.getPropertyValue(b)||"":""}function Wl(a,b){return Vl(a,b)||(a.currentStyle?a.currentStyle[b]:null)||a.style&&a.style[b]}function Xl(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}} 
function Yl(a){var b=El(a);const c=new ll(0,0);if(a==(b?El(b):document).documentElement)return c;a=Xl(a);b=Gl(Cl(b).g);c.x=a.left+b.x;c.y=a.top+b.y;return c}function Zl(a){var b=$l;if(Wl(a,"display")!="none")return b(a);const c=a.style,d=c.display,e=c.visibility,f=c.position;c.visibility="hidden";c.position="absolute";c.display="inline";a=b(a);c.display=d;c.position=f;c.visibility=e;return a} 
function $l(a){const b=a.offsetWidth,c=a.offsetHeight,d=Wc&&!b&&!c;return(b===void 0||d)&&a.getBoundingClientRect?(a=Xl(a),new pl(a.right-a.left,a.bottom-a.top)):new pl(b,c)};var am=a=>typeof a==="number"&&a>0,cm=(a,b)=>{a=bm(a);if(!a)return b;const c=b.slice(-1);return b+(c==="?"||c==="#"?"":"&")+a},bm=a=>Object.entries(dm(a)).map(([b,c])=>`${b}=${encodeURIComponent(String(c))}`).join("&"),dm=a=>{const b={};vd(a,(c,d)=>{if(c||c===0||c===!1)typeof c==="boolean"&&(c=c?1:0),b[d]=c});return b},em=a=>{a=Al(vl(a))||a;a.google_unique_id=(a.google_unique_id||0)+1},fm=a=>{a=a.google_unique_id;return typeof a==="number"?a:0},gm=a=>{let b;b=a.nodeType!==9&&a.id;a:{if(a&&a.nodeName&& 
a.parentElement){var c=a.nodeName.toString().toLowerCase();const d=a.parentElement.childNodes;let e=0;for(let f=0;f<d.length;++f){const g=d[f];if(g.nodeName&&g.nodeName.toString().toLowerCase()===c){if(a===g){c="."+e;break a}++e}}}c=""}return(a.nodeName&&a.nodeName.toString().toLowerCase())+(b?"/"+b:"")+c},hm=a=>(a=a.google_ad_format)?a.indexOf("_0ads")>0:!1,im=a=>{let b=Number(a.google_ad_width),c=Number(a.google_ad_height);if(!(b>0&&c>0)){a:{try{const e=String(a.google_ad_format);if(e&&e.match){const f= 
e.match(/(\d+)x(\d+)/i);if(f){const g=parseInt(f[1],10),h=parseInt(f[2],10);if(g>0&&h>0){var d={width:g,height:h};break a}}}}catch(e){}d=null}a=d;if(!a)return null;b=b>0?b:a.width;c=c>0?c:a.height}return{width:b,height:c}};var jm=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function km(a){return new jm(a,{message:lm(a)})}function lm(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);a.stack&&(b=mm(a.stack,b));return b}function mm(a,b){try{a.indexOf(b)==-1&&(a=b+"\n"+a);let c;for(;a!=c;)c=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");return a.replace(RegExp("\n *","g"),"\n")}catch(c){return b}};const nm=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var om=class{constructor(a,b){this.g=a;this.i=b}},pm=class{constructor(a,b,c){this.url=a;this.A=b;this.g=!!c;this.depth=null}};let qm=null;function rm(){var a=window;if(qm===null){qm="";try{let b="";try{b=a.top.location.hash}catch(c){b=a.location.hash}if(b){const c=b.match(/\bdeid=([\d,]+)/);qm=c?c[1]:""}}catch(b){}}return qm};function sm(){const a=q.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function tm(){const a=q.performance;return a&&a.now?a.now():null};var um=class{constructor(a,b){var c=tm()||sm();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const vm=q.performance,wm=!!(vm&&vm.mark&&vm.measure&&vm.clearMarks),xm=jb(()=>{var a;if(a=wm)a=rm(),a=!!a.indexOf&&a.indexOf("1337")>=0;return a});function ym(a){a&&vm&&xm()&&(vm.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),vm.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))}function zm(a){a.g=!1;a.i!==a.j.google_js_reporting_queue&&(xm()&&Ga(a.i,ym),a.i.length=0)}function Am(a,b){if(!a.g)return b();const c=a.start("491",3);let d;try{d=b()}catch(e){throw ym(c),e;}a.end(c);return d} 
var Bm=class{constructor(a){this.i=[];this.j=a||q;let b=null;a&&(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.i=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.g=xm()||(b!=null?b:Math.random()<1)}start(a,b){if(!this.g)return null;a=new um(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;vm&&xm()&&vm.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(tm()||sm())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;vm&&xm()&&vm.mark(b);!this.g||this.i.length> 
2048||this.i.push(a)}}};function Cm(a,b){const c={};c[a]=b;return[c]}function Dm(a,b,c,d,e){const f=[];vd(a,(g,h)=>{(g=Em(g,b,c,d,e))&&f.push(`${h}=${g}`)});return f.join(b)} 
function Em(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push(Em(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Dm(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function Fm(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.j.length-1} 
function Gm(a,b,c,d){b=b+"//"+c+d;let e=Fm(a)-d.length;if(e<0)return"";a.g.sort((f,g)=>f-g);d=null;c="";for(let f=0;f<a.g.length;f++){const g=a.g[f],h=a.i[g];for(let k=0;k<h.length;k++){if(!e){d=d==null?g:d;break}let l=Dm(h[k],a.j,",$");if(l){l=c+l;if(e>=l.length){e-=l.length;b+=l;c=a.j;break}d=d==null?g:d}}}a="";d!=null&&(a=`${c}${"trn"}=${d}`);return b+a}var Hm=class{constructor(){this.j="&";this.i={};this.l=0;this.g=[]}};var Km=class{constructor(a=null){this.I=Im;this.i=a;this.g=null;this.l=!1;this.B=this.ma}j(a){this.g=a}C(a){this.l=a}mb(a,b,c){let d,e;try{this.i&&this.i.g?(e=this.i.start(a.toString(),3),d=b(),this.i.end(e)):d=b()}catch(f){b=!0;try{ym(e),b=this.B(a,km(f),void 0,c)}catch(g){this.ma(217,g)}if(b)window.console?.error?.(f);else throw f;}return d}nb(a,b,c,d){return(...e)=>this.mb(a,()=>b.apply(c,e),d)}ma(a,b,c,d,e){e=e||"jserror";let f=void 0;try{const U=new Hm;var g=U;g.g.push(1);g.i[1]=Cm("context", 
a);b.error&&b.meta&&b.id||(b=km(b));g=b;if(g.msg){b=U;var h=g.msg.substring(0,512);b.g.push(2);b.i[2]=Cm("msg",h)}var k=g.meta||{};h=k;if(this.g)try{this.g(h)}catch(ta){}if(d)try{d(h)}catch(ta){}d=U;k=[k];d.g.push(3);d.i[3]=k;var l;if(!(l=p)){d=q;k=[];h=null;do{var m=d;if(kd(m)){var n=m.location.href;h=m.document&&m.document.referrer||null}else n=h,h=null;k.push(new pm(n||"",m));try{d=m.parent}catch(ta){d=null}}while(d&&m!==d);for(let ta=0,Fa=k.length-1;ta<=Fa;++ta)k[ta].depth=Fa-ta;m=q;if(m.location&& 
m.location.ancestorOrigins&&m.location.ancestorOrigins.length===k.length-1)for(n=1;n<k.length;++n){const ta=k[n];ta.url||(ta.url=m.location.ancestorOrigins[n-1]||"",ta.g=!0)}l=k}var p=l;let X=new pm(q.location.href,q,!1);l=null;const $a=p.length-1;for(m=$a;m>=0;--m){var t=p[m];!l&&nm.test(t.url)&&(l=t);if(t.url&&!t.g){X=t;break}}t=null;const Pb=p.length&&p[$a].url;X.depth!==0&&Pb&&(t=p[$a]);f=new om(X,t);if(f.i){p=U;var z=f.i.url||"";p.g.push(4);p.i[4]=Cm("top",z)}var w={url:f.g.url||""};if(f.g.url){const ta= 
f.g.url.match(Oc);var B=ta[1],I=ta[3],T=ta[4];z="";B&&(z+=B+":");I&&(z+="//",z+=I,T&&(z+=":"+T));var R=z}else R="";B=U;w=[w,{url:R}];B.g.push(5);B.i[5]=w;Jm(this.I,e,U,this.l,c)}catch(U){try{Jm(this.I,e,{context:"ecmserr",rctx:a,msg:lm(U),url:f?.g.url??""},this.l,c)}catch(X){}}return!0}Pa(a,b,c){b.catch(d=>{d=d?d:"unknown rejection";this.ma(a,d instanceof Error?d:Error(d),void 0,c||this.g||void 0)})}};var Lm=class extends G{};var Mm=Tj([0,Rj,Lj]);function Nm(a,b){try{const c=d=>[{[d.Ya]:d.ie}];return JSON.stringify([a.filter(d=>d.Oa).map(c),Lg(b),a.filter(d=>!d.Oa).map(c)])}catch(c){return Om(c,b),""}}function Pm(a,b){const c=new Gi;try{const d=a.filter(f=>f.Oa).map(Qm);Ei(c,1,d);Di(c,2,Mm(b),Fi);const e=a.filter(f=>!f.Oa).map(Qm);Ei(c,3,e)}catch(d){Om(d,b)}return Ci(c)}function Om(a,b){try{$k({m:lm(a instanceof Error?a:Error(String(a))),b:D(b,1)||null,v:C(b,2)||null},"rcs_internal")}catch(c){}} 
function Qm(a){const b=new Gi;Di(b,a.Ya,a.Be,Fi);return Ci(b)}function Fi(a,b){yi(b,a.subarray(0,a.length))}var Rm=class{constructor(a,b){var c=new Lm;a=F(c,1,a);b=fi(a,2,b);this.i=Wg(b)}};function Sm(a){return Math.round(a)};function Tm(a,b){return qh(a,1,Um,sg(b))}function Vm(a,b){return qh(a,2,Um,dg(b))}function Wm(a,b){return qh(a,3,Um,b==null?b:Kf(b))}var J=class extends G{},Um=[1,2,3];function Xm(a,b){return qh(a,2,Ym,dg(b))}function Zm(a,b){return qh(a,4,Ym,If(b))}var $m=class extends G{},Ym=[2,4];function an(a){var b=new bn;return fi(b,1,a)}function cn(a,b){return x(a,3,b)}function K(a,b){return Mh(a,4,J,b)}var bn=class extends G{};var dn=Tj([0,Lj,1,[0,Ym,1,yj,1,sj],Nj,[0,Um,Mj,yj,Ij]]);var en=class extends G{g(){return C(this,2)}getWidth(){return C(this,3)}getHeight(){return C(this,4)}};var fn=class extends G{};var gn=class extends G{};var hn=class extends G{};var jn=class extends G{},kn=[5,6];var ln=[0,vj,-1];var mn=[0,Nj,[0,Qj,[0,Cj,-3]],ln,-1];var nn=class extends G{getValue(){return D(this,1)}};function on(a){var b=new pn;return gi(b,1,a)}var pn=class extends G{getValue(){return D(this,1)}};var qn=class extends G{getValue(){return D(this,1)}};var rn=class extends G{getHeight(){return Qh(this,2)}};function sn(a,b){return ai(a,1,b)}function tn(a,b){return Kh(a,2,b)}var un=class extends G{};var vn=class extends G{};var wn=class extends G{};var yn=class extends G{setError(a){return Ah(this,3,xn,a)}},xn=[2,3];function zn(a,b){return di(a,1,b)}function An(a,b){return di(a,2,b)}function Bn(a,b){return di(a,3,b)}function Cn(a,b){return di(a,4,b)}function Dn(a,b){return di(a,5,b)}function En(a,b){return ph(a,8,If(b),0)}function Fn(a,b){return ph(a,9,If(b),0)}var Gn=class extends G{};function Hn(a,b){return di(a,1,b)}function In(a,b){return di(a,2,b)}var Jn=class extends G{};function Kn(a,b){Mh(a,1,Jn,b)}var nh=class extends G{};var Ln=class extends G{};function Mn(a,b){return oh(a,1,b,rg)}function Nn(a,b){return oh(a,12,b,mg)}function On(){var a=new Pn;return Lh(a,2,rg,"irr",tg)}function Qn(a,b){return E(a,3,b)}function Rn(a,b){return E(a,4,b)}function Sn(a,b){return E(a,5,b)}function Tn(a,b){return E(a,7,b)}function Un(a,b){return E(a,8,b)}function Vn(a,b){return di(a,9,b)}function Wn(a,b){return Kh(a,10,b)}function Xn(a,b){return oh(a,11,b,Uf)}var Pn=class extends G{};function Yn(a){var b=Zn();x(a,1,b)}function $n(a,b){return di(a,2,b)}function ao(a,b){return Kh(a,3,b)}function bo(a,b){return Kh(a,4,b)}function co(a,b){return Mh(a,4,pn,b)}function eo(a,b){return Kh(a,5,b)}function fo(a,b){return oh(a,6,b,rg)}function go(a,b){return di(a,7,b)}function ho(a,b){return di(a,8,b)}function io(a,b){x(a,9,b)}function jo(a,b){return E(a,10,b)}function ko(a,b){return E(a,11,b)}function lo(a,b){return E(a,12,b)}var mo=class extends G{};var no=class extends G{};var oo=class extends G{};function po(a){var b=new qo;return F(b,1,a)}var qo=class extends G{};var ro=class extends G{};var so=class extends G{};var to=class extends G{};var uo=class extends G{},vo=[1,2];var wo=class extends G{};var xo=class extends G{},yo=[1];function zo(a){var b=new Ao;return F(b,1,a)}var Ao=class extends G{};var Bo=class extends G{};var Co=class extends G{};var Do=class extends G{};var Eo=class extends G{};var Fo=class extends G{};var Go=class extends G{getContentUrl(){return C(this,1)}};var Ho=class extends G{};function Io(a){var b=new Jo;return oh(b,1,a,Of)}var Jo=class extends G{};var Ko=class extends G{};function Lo(){var a=new Mo,b=new Ko;return Ah(a,1,No,b)}function Oo(){var a=new Mo,b=new Ko;return Ah(a,9,No,b)}function Po(){var a=new Mo,b=new Ko;return Ah(a,13,No,b)}function Qo(a,b){return Ah(a,14,No,b)}var Mo=class extends G{},No=[1,2,3,5,6,7,8,9,10,11,12,13,14];var Ro=class extends G{};var So=class extends G{};var To=class extends G{};var Uo=class extends G{};function Vo(a,b){return ph(a,10,ng(b),"0")}function Wo(a,b){return F(a,1,b)}var Xo=class extends G{};var Yo=class extends G{};var Zo=class extends G{};var ap=class extends G{i(){return Wh(this,Yo,4,$o)}g(){return ch(this,Yo,th(this,$o,4))!==void 0}},$o=[4,5];function bp(a){var b=new cp;return fi(b,4,a)}function dp(a,b){return $g(a,6,ng(b))}var cp=class extends G{};var ep=class extends G{};var fp=class extends G{i(){return v(this,Yo,1)}g(){return bh(this,Yo,1)}};var gp=class extends G{};var hp=class extends G{};var ip=class extends G{};var jp=class extends G{};function kp(a,b){return di(a,1,b)}function lp(a){var b=new ip;return Ah(a,3,mp,b)}var np=class extends G{},mp=[2,3];var op=class extends G{},pp=[3,4,5,6,7,8,9,11,12,13,14,16,17];function Pp(a,b){return di(a,3,b)}var Qp=class extends G{},Rp=[4,5,6,8,9,10,11,12,13,14,15,16,17];var Sp=[0];var Tp=[0,Hj,Nj,[0,Rj,Lj,-2,Ej,-2,[0,Lj,2,Ej,-1,Lj,[0,Ej,-2,tj],-1],Nj,[0,Lj,Kj],Bj,Jj,Cj],xj,[0,Hj,xj,Hj,-2]];var Up=[0,vj,-1];var Vp=Tj([0,Rp,xj,Lj,xj,H,[0,ln,-1,[0,Pj],Lj,Hj],H,[0,[0,Kj,-1,Hj,-5,xj,Nj,[0,Lj,xj,Hj,-1],wj,Aj,Bj],xj,Nj,[0,Pj],Nj,[0,Pj],Nj,[0,Pj],Kj,xj,-1,[0,xj,-4,2,rj,-1],Hj,-2,1,hj,[!0,Jj,[0,Nj,[0,xj,-1]]],Nj,[0,Lj,-2],[0,xn,1,H,[0,Cj,-2,Gj,Nj,[0,Cj,Nj,[0,Cj,-1]]],H,[0,Pj,-1]]],H,[0,pp,xj,-1,H,[0,$o,[0,Lj,-1,Hj,vj],[0,Kj,Lj,-1],xj,H,Tp,H,[0,Nj,[0,No,H,Sp,-2,1,H,Sp,-1,H,[0,xj],H,Sp,-5,H,[0,Qj]]],[0,Cj,-1,Oj,-2,Cj]],H,[0,xj,Bj],H,[0,xj],H,[0,vj,zj,Jj,Lj,Hj,Cj],H,[0,xj],H,[0,vj,-2,Lj,Gj,zj,vj],H,[0,mp,xj,H, 
[0],H,[0]],[0,xj,Ej,wj],H,[0,Rj],H,[0,xj,Lj,Bj],H,[0],H,[0,Tp,xj],Gj,H,[0,Bj],H,[0,xj]],xj,H,[0,Lj,[0,Ej,-1,[0,rj,-5,Hj]],xj,mn],H,[0,Rj,Dj],H,[0,Rj,-1,Lj,-1],H,[0,yo,H,[0,Gj,-1]],H,[0,Rj,Hj,-9],H,[0,vo,H,[0,[0,Rj,Lj,-1]],H,[0,Ej,-1,Lj,[0,Ej,-1],-1,Hj,Qj,Ej,-1]],H,[0,[1,2,3,4],H,[0,[0,vj,-1],Up,Gj,Jj],H,[0],H,[0,Up],H,[0]],H,[0,[3,4,5,6,7,8],vj,[0,uj],H,[0],H,[0],H,[0],H,[0],H,[0],H,[0,[1,2,3,4,5],H,[0],-4]],H,[0,kn,vj,-2,[0,Jj,-2,Gj,[0,Jj,-3]],H,[0],H,[0]],H,mn]);function Wp(a,b){return di(a,1,b)}function Xp(a,b){return di(a,2,b)}var Yp=class extends G{getTagSessionCorrelator(){return Rh(this,1)}};var Zp=Tj([0,xj,-1,Rj]);var $p=class extends G{};function aq(){var a=Fg(bq());return fi(a,1,cq())}var dq=class extends G{};var eq=[0,[0,vj,Fj,-1],Lj];var fq=class extends G{};var gq=class extends G{getTagSessionCorrelator(){return Rh(this,1)}};var hq=class extends G{},iq=[1,7],jq=[4,6,8];var kq=Tj([0,iq,jq,H,[0,Rj,Lj,-1,Kj,-1,eq],[0,xj,Dj,Lj],1,H,[0,Lj,Ej,Kj],xj,H,[0,Lj,-1,Jj,[0,Dj],1,Rj,Lj,-1],H,[0,Lj,-1,Kj,-1,eq],H,[0,[1],H,[0,[0,Lj,-2,Rj,Lj]],[0,xj,-1]]]);class lq{constructor(a){this.I=a;this.Fe=new mq(this.I)}}class mq{constructor(a){this.I=a;this.Bd=new nq(this.I)}}class nq{constructor(a){this.I=a;this.g=new oq(this.I);this.Rh=new pq(this.I)}}class oq{constructor(a){this.I=a;this.i=new qq(this.I);this.g=new rq(this.I)}}class qq{constructor(a){this.I=a}od(a){this.I.g(cn(K(an("xR0Czf"),Tm(new J,a.status)),Zm(new $m,a.ud)))}}class rq{constructor(a){this.I=a}od(a){this.I.g(cn(K(an("jM4CPd"),Vm(new J,Sm(a.Sk))),Zm(new $m,a.ud)))}} 
class pq{constructor(a){this.I=a;this.ri=new sq(this.I);this.si=new tq(this.I);this.We=new uq(this.I);this.ti=new vq(this.I);this.ui=new wq(this.I);this.vi=new xq(this.I);this.wi=new yq(this.I);this.Ye=new zq(this.I);this.Oi=new Aq(this.I);this.aj=new Bq(this.I);this.bj=new Cq(this.I);this.sj=new Dq(this.I);this.uk=new Eq(this.I)}}class sq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(an("VEDP7d"),Tm(new J,a.language)),Vm(new J,a.xa)),Xm(new $m,Sm(a.ga))))}} 
class tq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(an("igjuhc"),Tm(new J,a.language)),Vm(new J,a.xa)),Xm(new $m,Sm(a.ga))))}}class uq{constructor(a){this.I=a}od(a){this.I.g(cn(K(K(K(K(K(an("i3zJEd"),Tm(new J,a.language)),Vm(new J,a.xa)),Vm(new J,a.outcome)),Wm(new J,a.cd)),Wm(new J,a.zf)),Zm(new $m,a.ud)))}} 
class vq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(K(K(K(an("JN0hVd"),Tm(new J,a.language)),Vm(new J,a.xa)),Vm(new J,a.outcome)),Wm(new J,a.cd)),Wm(new J,a.zf)),Xm(new $m,Sm(a.ga))))}}class wq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(K(an("rmHfOd"),Tm(new J,a.language)),Vm(new J,a.xa)),Vm(new J,a.reason)),Xm(new $m,Sm(a.ga))))}}class xq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(K(an("VEyQic"),Tm(new J,a.language)),Vm(new J,a.xa)),Vm(new J,a.format)),Xm(new $m,Sm(a.ga))))}} 
class yq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(K(an("QFcNxc"),Tm(new J,a.language)),Vm(new J,a.xa)),Vm(new J,a.format)),Xm(new $m,Sm(a.ga))))}}class zq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(K(K(an("SIhp4"),Tm(new J,a.language)),Vm(new J,a.xa)),Vm(new J,a.format)),Wm(new J,a.cd)),Xm(new $m,Sm(a.ga))))}}class Aq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(K(an("Eeiun"),Tm(new J,a.language)),Vm(new J,a.xa)),Vm(new J,a.format)),Xm(new $m,Sm(a.ga))))}} 
class Bq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(an("zGH6sc"),Tm(new J,a.language)),Vm(new J,a.xa)),Xm(new $m,Sm(a.ga))))}}class Cq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(K(an("SmbJl"),Tm(new J,a.language)),Vm(new J,a.xa)),Vm(new J,a.type)),Xm(new $m,Sm(a.ga))))}}class Dq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(K(an("qleBg"),Tm(new J,a.language)),Vm(new J,a.xa)),Vm(new J,a.format)),Xm(new $m,Sm(a.ga))))}} 
class Eq{constructor(a){this.I=a}Ea(a){this.I.g(cn(K(K(K(an("pYLGPe"),Tm(new J,a.language)),Vm(new J,a.xa)),Vm(new J,a.type)),Xm(new $m,Sm(a.ga))))}}class Fq extends Rm{constructor(){super(...arguments);this.je=new lq(this)}} 
var Gq=class extends Fq{Ph(...a){this.C(...a.map(b=>({Oa:!0,Ya:3,ie:Lg(b)})))}Jb(...a){this.C(...a.map(b=>({Oa:!0,Ya:7,ie:Lg(b)})))}F(...a){this.C(...a.map(b=>({Oa:!0,Ya:16,ie:Lg(b)})))}g(...a){this.C(...a.map(b=>({Oa:!1,Ya:1,ie:Lg(b)})))}},Iq=class extends Fq{Ph(...a){Hq(this,...a.map(b=>({Oa:!0,Ya:3,Be:kq(b)})))}Jb(...a){Hq(this,...a.map(b=>({Oa:!0,Ya:7,Be:Vp(b)})))}F(...a){Hq(this,...a.map(b=>({Oa:!0,Ya:16,Be:Zp(b)})))}g(...a){Hq(this,...a.map(b=>({Oa:!1,Ya:1,Be:dn(b)})))}};var Jq=(a,b)=>{globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(()=>{})},Kq=class extends Gq{constructor(a){super(2,a);this.j=Jq}C(...a){try{const b=Nm(a,this.i);this.j("https://pagead2.googlesyndication.com/pagead/ping?e=1",b)}catch(b){Om(b,this.i)}}},Lq=class extends Kq{};function Mq(a){a.l!==null&&(clearTimeout(a.l),a.l=null);if(a.j.length){var b=Nm(a.j,a.i);a.G("https://pagead2.googlesyndication.com/pagead/ping?e=1",b);a.j=[]}} 
var Oq=class extends Gq{constructor(a,b,c,d,e){super(2,a);this.G=Jq;this.X=b;this.K=c;this.O=d;this.B=e;this.j=[];this.l=null;this.H=!1}C(...a){try{this.O&&Nm(this.j.concat(a),this.i).length>=65536&&Mq(this),this.B&&!this.H&&(this.H=!0,Nq(this.B,()=>{Mq(this)})),this.j.push(...a),this.j.length>=this.K&&Mq(this),this.j.length&&this.l===null&&(this.l=setTimeout(()=>{Mq(this)},this.X))}catch(b){Om(b,this.i)}}},Pq=class extends Oq{constructor(a,b=1E3,c=100,d=!1,e){super(a,b,c,d&&!0,e)}};var L=a=>{var b="If";if(a.If&&a.hasOwnProperty(b))return a.If;b=new a;return a.If=b};function Qq(a,b,c){return b[a]||c};function Rq(a,b){a.i=(c,d)=>Qq(2,b,()=>[])(c,1,d);a.g=()=>Qq(3,b,()=>[])(1)}class Sq{i(){return[]}g(){return[]}}function Tq(a,b){return L(Sq).i(a,b)};function Jm(a,b,c,d=!1,e){if((d?a.g:Math.random())<(e||.01))try{let f;c instanceof Hm?f=c:(f=new Hm,vd(c,(h,k)=>{var l=f;const m=l.l++;h=Cm(k,h);l.g.push(m);l.i[m]=h}));const g=Gm(f,a.protocol,a.domain,a.path+b+"&");g&&Yk(q,g)}catch(f){}}function Uq(a,b){b>=0&&b<=1&&(a.g=b)}var Vq=class{constructor(){this.domain="pagead2.googlesyndication.com";this.path="/pagead/gen_204?id=";this.protocol="https:";this.g=Math.random()}};let Im,Wq;const Xq=new Bm(window);(function(a){Im=a??new Vq;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());Uq(Im,window.google_srt);Wq=new Km(Xq);Wq.j(()=>{});Wq.C(!0);window.document.readyState==="complete"?window.google_measure_js_timing||zm(Xq):Xq.g&&qb(window,"load",()=>{window.google_measure_js_timing||zm(Xq)})})();let Yq=(new Date).getTime();var Zq={Ym:0,Xm:1,Um:2,Pm:3,Vm:4,Qm:5,Wm:6,Sm:7,Tm:8,Om:9,Rm:10,Zm:11};var $q={cn:0,dn:1,bn:2};function ar(a,b){return a.left<b.right&&b.left<a.right&&a.top<b.bottom&&b.top<a.bottom}function br(a){a=a.map(b=>new ml(b.top,b.right,b.bottom,b.left));a=cr(a);return{top:a.top,right:a.right,bottom:a.bottom,left:a.left}}function cr(a){if(!a.length)throw Error("pso:box:m:nb");return a.slice(1).reduce((b,c)=>{b.left=Math.min(b.left,c.left);b.top=Math.min(b.top,c.top);b.right=Math.max(b.right,c.right);b.bottom=Math.max(b.bottom,c.bottom);return b},nl(a[0]))};var Tb={Sn:0,Dm:1,Gm:2,Em:3,Fm:4,Mm:8,co:9,rn:10,sn:11,ao:16,xm:17,wm:24,on:25,bm:26,am:27,ii:30,gn:32,ln:40,ko:41,eo:42};var dr={overlays:1,interstitials:2,vignettes:2,inserts:3,immersives:4,list_view:5,full_page:6,side_rails:7},er={[1]:1,[2]:1,[3]:7,[4]:7,[8]:2,[27]:3,[9]:4,[30]:5};var fr=728*1.38;function gr(a,b=-1){if(a!==a.top){if(b<0)a=!1;else{var c=hr(a,!0,!0),d=ir(a,!0);a=c>0&&d>0&&Math.abs(1-a.screen.width/c)<=b&&Math.abs(1-a.screen.height/d)<=b}a=a?0:512}else a=0;return a}function jr(a,b=420,c=!1,d=!1){return(a=hr(a,c,d))?a>b?32768:a<320?65536:0:16384}function kr(a){return Math.max(0,lr(a,!0)-ir(a))}function mr(a){a=a.document;let b={};a&&(b=a.compatMode=="CSS1Compat"?a.documentElement:a.body);return b||{}} 
function ir(a,b=!1){const c=mr(a).clientHeight;return b?c*(Kb()&&Lb()?ce(a):1):c}function hr(a,b=!1,c=!1){c=mr(a).clientWidth??(c?a.innerWidth:void 0);return b?c*(Kb()&&Lb()?ce(a):1):c}function lr(a,b){const c=mr(a);return b?(a=ir(a),c.scrollHeight===a?c.offsetHeight:c.scrollHeight):c.offsetHeight}function nr(a,b){var c=M(or);return pr(b)||b===10||!a.adCount?!1:c||b!==1&&b!==2?(a=a.adCount[b])?a>=1:!1:!(!a.adCount[1]&&!a.adCount[2])} 
function qr(a,b){return a&&a.source?a.source===b||a.source.parent===b:!1}function rr(a){return a.pageYOffset===void 0?(a.document.documentElement||a.document.body.parentNode||a.document.body).scrollTop:a.pageYOffset}function sr(a){return a.pageXOffset===void 0?(a.document.documentElement||a.document.body.parentNode||a.document.body).scrollLeft:a.pageXOffset} 
function tr(a){const b={};let c;Array.isArray(a)?c=a:a&&a.key_value&&(c=a.key_value);if(c)for(a=0;a<c.length;a++){const d=c[a];if("key"in d&&"value"in d){const e=d.value;b[d.key]=e==null?null:String(e)}}return b}function ur(a,b,c,d){Jm(c,b,{c:d.data.substring(0,500),u:a.location.href.substring(0,500)},!0,.1);return!0} 
function vr(a){const b={bottom:"auto",clear:"none",display:"inline","float":"none",height:"auto",left:"auto",margin:0,"margin-bottom":0,"margin-left":0,"margin-right":"0","margin-top":0,"max-height":"none","max-width":"none",opacity:1,overflow:"visible",padding:0,"padding-bottom":0,"padding-left":0,"padding-right":0,"padding-top":0,position:"static",right:"auto",top:"auto","vertical-align":"baseline",visibility:"visible",width:"auto","z-index":"auto"};Ga(Object.keys(b),c=>{Ul(a,c)||Rl(a,c,b[c])}); 
Qd(a)}function pr(a){return a===26||a===27||a===40||a===41};function wr(a,b){xr(a).forEach(b,void 0)}function xr(a){const b=[],c=a.length;for(let d=0;d<c;d++)b.push(a[d]);return b};function yr(a,b){return a.g[zr(b)]!==void 0}function Ar(a){const b=[];for(const c in a.g)a.g[c]!==void 0&&a.g.hasOwnProperty(c)&&b.push(a.i[c]);return b}function Br(a){const b=[];for(const c in a.g)a.g[c]!==void 0&&a.g.hasOwnProperty(c)&&b.push(a.g[c]);return b}var Cr=class{constructor(){this.g={};this.i={}}set(a,b){const c=zr(a);this.g[c]=b;this.i[c]=a}get(a,b){a=zr(a);return this.g[a]!==void 0?this.g[a]:b}Yc(){return Ar(this).length}clear(){this.g={};this.i={}}}; 
function zr(a){return a instanceof Object?String(qa(a)):a+""};var Dr=class{constructor(a){this.g=new Cr;if(a)for(let b=0;b<a.length;++b)this.add(a[b])}add(a){this.g.set(a,!0)}contains(a){return yr(this.g,a)}};const Er=new Dr("IMG AMP-IMG IFRAME AMP-IFRAME HR EMBED OBJECT VIDEO AMP-VIDEO INPUT BUTTON SVG".split(" "));function Fr(a,{rb:b,kb:c,Qb:d}){return d&&c(b)?b:(b=b.parentElement)?Gr(a,{rb:b,kb:c,Qb:!0}):null}function Gr(a,{rb:b,kb:c,Qb:d=!1}){const e=Hr({rb:b,kb:c,Qb:d}),f=a.g.get(e);if(f)return f.element;b=Fr(a,{rb:b,kb:c,Qb:d});a.g.set(e,{element:b});return b}var Ir=class{constructor(){this.g=new Map}};function Hr({rb:a,kb:b,Qb:c}){a=qa(a);b=qa(b);return`${a}:${b}:${c}`};function Jr(a){$b(a.document.body.offsetHeight)};function Kr(a){a&&typeof a.dispose=="function"&&a.dispose()};function N(){this.C=this.C;this.H=this.H}N.prototype.C=!1;N.prototype.dispose=function(){this.C||(this.C=!0,this.i())};N.prototype[ha(Symbol,"dispose")]=function(){this.dispose()};function Lr(a,b){Mr(a,ya(Kr,b))}function Mr(a,b){a.C?b():(a.H||(a.H=[]),a.H.push(b))}N.prototype.i=function(){if(this.H)for(;this.H.length;)this.H.shift()()};function Nr(a){a.g.forEach((b,c)=>{if(b.overrides.delete(a)){b=Array.from(b.overrides.values()).pop()||b.originalValue;var d=a.element;b?d.style.setProperty(c,b.value,b.priority):d.style.removeProperty(c)}})} 
function Or(a,b,c){c={value:c,priority:"important"};var d=a.g.get(b);if(!d){d=a.element;var e=d.style.getPropertyValue(b);d={originalValue:e?{value:e,priority:d.style.getPropertyPriority(b)}:null,overrides:new Map};a.g.set(b,d)}d.overrides.delete(a);d.overrides.set(a,c);a=a.element;c?a.style.setProperty(b,c.value,c.priority):a.style.removeProperty(b)} 
var Pr=class extends N{constructor(a,b){super();this.element=b;a=a.googTempStyleOverrideInfo=a.googTempStyleOverrideInfo||new Map;var c=a.get(b);c?b=c:(c=new Map,a.set(b,c),b=c);this.g=b}i(){Nr(this);super.i()}};function Qr(a){const b=new O(a.getValue());a.listen(c=>b.g(c));return b}function Rr(a,b){const c=new O({first:a.R,second:b.R});a.listen(()=>c.g({first:a.R,second:b.R}));b.listen(()=>c.g({first:a.R,second:b.R}));return c}function Sr(...a){const b=[...a],c=()=>b.every(f=>f.R),d=new O(c()),e=()=>{d.g(c())};b.forEach(f=>f.listen(e));return Tr(d)}function Ur(...a){const b=[...a],c=()=>b.findIndex(f=>f.R)!==-1,d=new O(c()),e=()=>{d.g(c())};b.forEach(f=>f.listen(e));return Tr(d)} 
function Tr(a,b=Vr){var c=a.R;const d=new O(a.R);a.listen(e=>{b(e,c)||(c=e,d.g(e))});return d}function Wr(a,b,c){return a.i(d=>{d===b&&c()})}function Xr(a,b,c){if(a.R===b)return c(),()=>{};const d={Cc:null};d.Cc=Wr(a,b,()=>{d.Cc&&(d.Cc(),d.Cc=null);c()});return d.Cc}function Yr(a,b,c){Tr(a).listen(d=>{d===b&&c()})}function Zr(a,b){a.l&&a.l();a.l=b.listen(c=>a.g(c),!0)} 
function $r(a,b,c,d){const e=new O(!1);var f=null;a=a.map(d);Wr(a,!0,()=>{f===null&&(f=b.setTimeout(()=>{e.g(!0)},c))});Wr(a,!1,()=>{e.g(!1);f!==null&&(b.clearTimeout(f),f=null)});return Tr(e)}function as(a){return{listen:b=>a.listen(b),getValue:()=>a.R}} 
var O=class{constructor(a){this.R=a;this.j=new Map;this.B=1;this.l=null}listen(a,b=!1){const c=this.B++;this.j.set(c,a);b&&a(this.R);return()=>{this.j.delete(c)}}i(a){return this.listen(a,!0)}C(){return this.R}g(a){this.R=a;this.j.forEach(b=>{b(this.R)})}map(a){const b=new O(a(this.R));this.listen(c=>b.g(a(c)));return b}};function Vr(a,b){return a==b};function bs(a){return new cs(a)}function ds(a,b){Ga(a.g,c=>{c(b)})}var es=class{constructor(){this.g=[]}};class cs{constructor(a){this.g=a}listen(a){this.g.g.push(a)}map(a){const b=new es;this.listen(c=>ds(b,a(c)));return bs(b)}delay(a,b){const c=new es;this.listen(d=>{a.setTimeout(()=>{ds(c,d)},b)});return bs(c)}}function fs(...a){const b=new es;a.forEach(c=>{c.listen(d=>{ds(b,d)})});return bs(b)};function gs(a){return Tr(Rr(a.g,a.j).map(b=>{var c=b.first;b=b.second;return c==null||b==null?null:hs(c,b)}))}var js=class{constructor(a){this.i=a;this.g=new O(null);this.j=new O(null);this.l=new es;this.H=b=>{this.g.R==null&&b.touches.length==1&&this.g.g(b.touches[0])};this.C=b=>{const c=this.g.R;c!=null&&(b=is(c,b.changedTouches),b!=null&&(this.g.g(null),this.j.g(null),ds(this.l,hs(c,b))))};this.B=b=>{var c=this.g.R;c!=null&&(c=is(c,b.changedTouches),c!=null&&(this.j.g(c),b.preventDefault()))}}}; 
function hs(a,b){return{ci:b.pageX-a.pageX,di:b.pageY-a.pageY}}function is(a,b){if(b==null)return null;for(let c=0;c<b.length;++c)if(b[c].identifier==a.identifier)return b[c];return null};function ks(a){return Tr(Rr(a.g,a.i).map(b=>{var c=b.first;b=b.second;return c==null||b==null?null:ls(c,b)}))}var ms=class{constructor(a,b){this.l=a;this.C=b;this.g=new O(null);this.i=new O(null);this.j=new es;this.F=c=>{this.g.g(c)};this.B=c=>{const d=this.g.R;d!=null&&(this.g.g(null),this.i.g(null),ds(this.j,ls(d,c)))};this.H=c=>{this.g.R!=null&&(this.i.g(c),c.preventDefault())}}};function ls(a,b){return{ci:b.screenX-a.screenX,di:b.screenY-a.screenY}};var ps=(a,b,c)=>{const d=new ns(a,b,c);return()=>os(d)};function os(a){if(a.g)return!1;if(a.i==null)return qs(a),!0;const b=a.i+a.C-(new Date).getTime();if(b<1)return qs(a),!0;rs(a,b);return!0}function qs(a){a.i=(new Date).getTime();a.l()}function rs(a,b){a.g=!0;a.j.setTimeout(()=>{a.g=!1;qs(a)},b)}class ns{constructor(a,b,c){this.j=a;this.C=b;this.l=c;this.i=null;this.g=!1}};function ss(a){return ts(ks(a.g),gs(a.i))}function us(a){return fs(bs(a.g.j),bs(a.i.l))}var vs=class{constructor(a,b){this.g=a;this.i=b}};function ts(a,b){return Rr(a,b).map(({first:c,second:d})=>c||d||null)};var ws=class{constructor(){this.cache=new Map}getBoundingClientRect(a){var b=this.cache.get(a);if(b)return b;b=a.getBoundingClientRect();this.cache.set(a,b);return b}};function xs(a){a.B==null&&(a.B=new O(a.F.getBoundingClientRect()));return a.B}var ys=class extends N{constructor(a,b){super();this.j=a;this.F=b;this.G=!1;this.B=null;this.l=()=>{xs(this).g(this.F.getBoundingClientRect())}}g(){this.G||(this.G=!0,this.j.addEventListener("resize",this.l),this.j.addEventListener("scroll",this.l));return xs(this)}i(){this.j.removeEventListener("resize",this.l);this.j.removeEventListener("scroll",this.l);super.i()}};function zs(a,b){return new As(a,b)}function Bs(a){a.A.requestAnimationFrame(()=>{a.C||a.j.g(new pl(a.element.offsetWidth,a.element.offsetHeight))})}function Cs(a){a.g||(a.g=!0,a.l.observe(a.element));return Tr(a.j,ql)}var As=class extends N{constructor(a,b){super();this.A=a;this.element=b;this.g=!1;this.j=new O(new pl(this.element.offsetWidth,this.element.offsetHeight));this.l=new ResizeObserver(()=>{Bs(this)})}i(){this.l.disconnect();super.i()}};function Ds(a,b){return{top:a.g-b,right:a.j+a.i,bottom:a.g+b,left:a.j}}var Es=class{constructor(a,b,c){this.j=a;this.g=b;this.i=c}};function Fs(a,b){a=a.getBoundingClientRect();return new Gs(a.top+rr(b),a.bottom-a.top)}function Hs(a){return new Gs(Math.round(a.g),Math.round(a.i))}var Gs=class{constructor(a,b){this.g=a;this.i=b}getHeight(){return this.i}};var Js=(a,b)=>{const c=a.google_pso_loaded_fonts||(a.google_pso_loaded_fonts=[]),d=new Dr(c);b=b.filter(e=>!d.contains(e));b.length&&(Is(a,b),Wa(c,b))};function Is(a,b){for(const d of b){const e=sd("LINK",a.document);e.type="text/css";b=e;var c=hd`//fonts.googleapis.com/css?family=${d}`;b.href=hc(c).toString();b.rel="stylesheet";a.document.head.appendChild(e)}};function Ks(a,b){a.G?b(a.l):a.j.push(b)}function Ls(a,b){a.G=!0;a.l=b;a.j.forEach(c=>{c(a.l)});a.j=[]} 
var Ms=class extends N{constructor(a){super();this.g=a;this.j=[];this.G=!1;this.F=this.l=null;this.K=ps(a,1E3,()=>{if(this.F!=null){var b=lr(this.g,!0)-this.F;b>1E3&&Ls(this,b)}});this.B=null}M(a,b){a==null?(this.F=a=lr(this.g,!0),this.g.addEventListener("scroll",this.K),b!=null&&b(a)):this.B=this.g.setTimeout(()=>{this.M(void 0,b)},a)}i(){this.B!=null&&this.g.clearTimeout(this.B);this.g.removeEventListener("scroll",this.K);this.j=[];this.l=null;super.i()}};var Ns=(a,b)=>a.reduce((c,d)=>c.concat(b(d)),[]);var Os=class{constructor(a=1){this.g=a}next(){const a=48271*this.g%2147483647;this.g=a*2147483647<0?a+2147483647:a;return this.g/2147483647}};function Ps(a,b,c){const d=[];for(const e of a.g)b(e)?d.push(e):c(e);return new Qs(d)}function Rs(a){return a.g.slice(0)}function Ss(a,b=1){a=Rs(a);const c=new Os(b);cb(a,()=>c.next());return new Qs(a)}var Qs=class{constructor(a){this.g=a.slice(0)}forEach(a){this.g.forEach((b,c)=>void a(b,c,this))}filter(a){return new Qs(La(this.g,a))}apply(a){return new Qs(a(Rs(this)))}sort(a){return new Qs(Rs(this).sort(a))}get(a){return this.g[a]}add(a){const b=Rs(this);b.push(a);return new Qs(b)}};var Ts=class{constructor(a){this.g=new Dr(a)}contains(a){return this.g.contains(a)}};function Us(a){return new Vs({value:a},null)}function Ws(a){return new Vs(null,a)}function Xs(a){try{return Us(a())}catch(b){return Ws(b)}}function Ys(a){return a.i!=null}function Zs(a){return Ys(a)?a.getValue():null}function $s(a,b){Ys(a)&&b(a.getValue());return a}function at(a,b){return Ys(a)?a:Ws(b(a.g))}function bt(a,b){return at(a,c=>Error(`${b}${c.message}`))}function ct(a,b){Ys(a)||b(a.g);return a} 
var Vs=class{constructor(a,b){this.i=a;this.g=b}getValue(){return this.i.value}map(a){return Ys(this)?(a=a(this.getValue()),a instanceof Vs?a:Us(a)):this}};var dt=class{constructor(){this.g=new Cr}set(a,b){let c=this.g.get(a);c||(c=new Dr,this.g.set(a,c));c.add(b)}};function et(a){return!a}function ft(a){return b=>{for(const c of a)c(b)}};function gt(a){return a!==null};var ht=class extends G{getId(){return y(this,3)}};var it=class{constructor(a,{Jg:b,ni:c,Hj:d,Lh:e}){this.C=a;this.j=c;this.l=new Qs(b||[]);this.i=e;this.g=d}};var jt=a=>{var b=a.split("~").filter(c=>c.length>0);a=new Cr;for(const c of b)b=c.indexOf("."),b==-1?a.set(c,""):a.set(c.substring(0,b),c.substring(b+1));return a},lt=a=>{var b=kt(a);a=[];for(let c of b)b=String(c.Hc),a.push(c.Lb+"."+(b.length<=20?b:b.slice(0,19)+"_"));return a.join("~")}; 
const kt=a=>{const b=[],c=a.l;c&&c.g.length&&b.push({Lb:"a",Hc:mt(c)});a.j!=null&&b.push({Lb:"as",Hc:a.j});a.g!=null&&b.push({Lb:"i",Hc:String(a.g)});a.i!=null&&b.push({Lb:"rp",Hc:String(a.i)});b.sort(function(d,e){return d.Lb.localeCompare(e.Lb)});b.unshift({Lb:"t",Hc:nt(a.C)});return b},nt=a=>{switch(a){case 0:return"aa";case 1:return"ma";default:throw Error("Invalid slot type"+a);}},mt=a=>{a=Rs(a).map(ot);a=JSON.stringify(a);return xd(a)},ot=a=>{const b={};y(a,7)!=null&&(b.q=y(a,7));Oh(a,2)!=null&& 
(b.o=Oh(a,2));Oh(a,5)!=null&&(b.p=Oh(a,5));return b};function pt(){var a=new qt;return gi(a,2,1)}var qt=class extends G{setLocation(a){return gi(this,1,a)}g(){return Zh(this,1)}};function rt(a){const b=[].slice.call(arguments).filter(hb(e=>e===null));if(!b.length)return null;let c=[],d={};b.forEach(e=>{c=c.concat(e.Og||[]);d=Object.assign(d,e.Zc())});return new st(c,d)}function tt(a){switch(a){case 1:return new st(null,{google_ad_semantic_area:"mc"});case 2:return new st(null,{google_ad_semantic_area:"h"});case 3:return new st(null,{google_ad_semantic_area:"f"});case 4:return new st(null,{google_ad_semantic_area:"s"});default:return null}} 
function ut(a){return a==null?null:new st(null,{google_ml_rank:a})}function vt(a){return a==null?null:new st(null,{google_placement_id:lt(a)})}function wt({Ui:a,nj:b=null}){if(a==null)return null;a={google_daaos_ts:a};b!=null&&(a.google_erank=b+1);return new st(null,a)}var st=class{constructor(a,b){this.Og=a;this.g=b}Zc(){return this.g}};var xt=class extends G{};var yt=class extends G{};var zt=class extends G{i(){return y(this,2)}g(){return y(this,5)}j(){return zh(this,yt,3,eh())}l(){return Yh(this,4)}C(){return dh(this,6)??void 0}B(){return bh(this,xt,7)}};var At=class extends G{};var Bt=class extends G{j(){return A(this,12)}i(){return lg(Yg(this,13))??void 0}g(){return Xh(this,23)}};var Ct=class extends G{};var Dt=class extends G{g(){return Ph(this,3)}i(){return Nh(this,6)}};var Et=class extends G{};var Ft=class extends G{};var Gt=class extends G{ja(){return v(this,ht,1)}g(){return Ph(this,2)}};var Ht=class extends G{};var It=class extends G{};var Jt=class extends G{getName(){return y(this,4)}},Kt=[1,2,3];var Lt=class extends G{g(){return v(this,Dt,10)}};var Mt=class extends G{g(){return Nh(this,2)}i(){return Nh(this,3)}};var Nt=class extends G{g(){return kg(Yg(this,1))}};var Ot=class extends G{g(){return Rh(this,1)}};var Pt=class extends G{g(){return C(this,1)}i(){return C(this,2)}};var Qt=class extends G{j(){return A(this,1)}l(){return A(this,3)}C(){return A(this,7)}g(){return A(this,4)}i(){return A(this,5)}};var Rt=class extends G{g(){return v(this,Ot,6)}i(){return v(this,Qt,12)}};var St=class extends G{};var Tt=class extends G{};var Ut=class extends G{};var Vt=class extends G{g(){return zh(this,Ut,1,eh())}};var Wt=class extends G{setProperty(a){return ei(this,1,a)}getValue(){return y(this,2)}};var Xt=class extends G{};var Yt=class extends G{};var Zt=class extends G{ja(){return v(this,ht,1)}g(){return Ph(this,2)}};var $t=class extends G{};var au=class extends G{};var bu=class extends G{g(){return Uh(this,6)}};var cu=class extends G{Af(){return bh(this,au,2)}};var du=class extends G{g(){return kg(Yg(this,1))??0}};var eu=class extends G{};var gu=class extends G{g(){return Wh(this,eu,2,fu)}},fu=[1,2];var hu=class extends G{g(){return v(this,gu,3)}};var iu=class extends G{};var ju=class extends G{g(){return zh(this,iu,1,eh())}};var ku=class extends G{g(){return Uh(this,1)}i(){return v(this,hu,3)}};var lu=Uj(class extends G{g(){return v(this,Bt,15)}});var mu=class extends G{},nu=Uj(mu);function ou(a){try{const b=a.localStorage.getItem("google_ama_settings");return b?nu(b):null}catch(b){return null}}function pu(a,b){if(a.nf!==void 0){var c=ou(b);c||(c=new mu);a.nf!==void 0&&$h(c,2,a.nf);a=Date.now()+864E5;Number.isFinite(a)&&ci(c,1,Math.round(a));c=ii(c);try{b.localStorage.setItem("google_ama_settings",c)}catch(d){}}else if((c=ou(b))&&lg(Yg(c,1))<Date.now())try{b.localStorage.removeItem("google_ama_settings")}catch(d){}};var qu={dc:"ama_success",ob:.1,Bb:!0,ic:!0},ru={dc:"ama_failure",ob:.1,Bb:!0,ic:!0},su={dc:"ama_coverage",ob:.1,Bb:!0,ic:!0},tu={dc:"ama_opt",ob:.1,Bb:!0,ic:!1},uu={dc:"ama_auto_rs",ob:1,Bb:!0,ic:!1},vu={dc:"ama_constraints",ob:0,Bb:!0,ic:!0};function wu(a){if(a!=null)return xu(a)}function xu(a){return tf(a)?Number(a):String(a)};function yu(a,b){zu(a.i,uu,{...b,evt:"place",vh:ir(a.A),eid:wu(a.g.g()?.g())||0,hl:v(a.g,Pt,5)?.g()||""})}function Au(a,b,c){b={sts:b};c&&(b.excp_n=c.name,b.excp_m=c.message&&c.message.substring(0,512),b.excp_s=c.stack&&mm(c.stack,"")||"");yu(a,b)}var Bu=class{constructor(a,b,c){this.A=a;this.i=b;this.g=c}};const Cu=["-webkit-text-fill-color"];function Du(a,b){if(Qc){{const d=td(a.document.body,a);if(d){a={};var c=d.length;for(let e=0;e<c;++e)a[d[e]]="initial";a=Eu(a)}else a=Fu()}}else a=Fu();r(b,a)}function Fu(){const a={all:"initial"};Ga(Cu,b=>{a[b]="unset"});return a}function Eu(a){Ga(Cu,b=>{delete a[b]});return a};var Gu=class{constructor(a){this.g=a}Xc(a){const b=a.document.createElement("div");Du(a,b);r(b,{width:"100%","max-width":"1000px",margin:"auto"});b.appendChild(this.g);const c=a.document.createElement("div");Du(a,c);r(c,{width:"100%","text-align":"center",display:"block",padding:"5px 5px 2px","box-sizing":"border-box","background-color":"#FFF"});c.appendChild(b);return c}};function Hu(a){if(a.nodeType!=1)var b=!1;else if(b=a.tagName=="INS")a:{b=["adsbygoogle-placeholder"];var c=a.className?a.className.split(/\s+/):[];a={};for(let d=0;d<c.length;++d)a[c[d]]=!0;for(c=0;c<b.length;++c)if(!a[b[c]]){b=!1;break a}b=!0}return b}function Iu(a){return xr(a.querySelectorAll("ins.adsbygoogle-ablated-ad-slot"))};function Ju(a,b){a=Pl(new Dl(a),"DIV");const c=a.style;c.width="100%";c.height="auto";c.clear=b?"both":"none";return a}function Ku(a,b,c){switch(c){case 0:b.parentNode&&b.parentNode.insertBefore(a,b);break;case 3:if(c=b.parentNode){var d=b.nextSibling;if(d&&d.parentNode!=c)for(;d&&d.nodeType==8;)d=d.nextSibling;c.insertBefore(a,d)}break;case 1:b.insertBefore(a,b.firstChild);break;case 2:b.appendChild(a)}Hu(b)&&(b.setAttribute("data-init-display",b.style.display),b.style.display="block")} 
function Lu(a){if(a&&a.parentNode){const b=a.parentNode;b.removeChild(a);Hu(b)&&(b.style.display=b.getAttribute("data-init-display")||"none")}};var P=class{constructor(a,b=!1){this.g=a;this.defaultValue=b}},Q=class{constructor(a,b=0){this.g=a;this.defaultValue=b}},Mu=class{constructor(a,b=""){this.g=a;this.defaultValue=b}},Nu=class{constructor(a,b=[]){this.g=a;this.defaultValue=b}},Ou=class{constructor(a,b=[]){this.g=a;this.defaultValue=b}};var Pu=new Q(619278254,10),Qu=new P(687716473),Ru=new P(1377),Su=new P(676894296,!0),Tu=new P(682658313,!0),Uu=new Q(1130,100),Vu=new Q(1339,.3),Wu=new Q(1032,200),Xu=new P(736254284),Yu=new Mu(14),Zu=new Q(1224,.01),$u=new Q(1346,6),av=new Q(1347,3),bv=new P(1260),cv=new P(316),dv=new P(1290),ev=new P(334),fv=new P(1383),gv=new Q(1263,-1),hv=new Q(54),iv=new Q(1323,-1),jv=new Q(1265,-1),kv=new Q(1264,-1),lv=new P(1291),mv=new P(1267,!0),nv=new P(1266),ov=new P(313),pv=new Q(66,-1),qv=new Q(65,-1), 
rv=new P(1256),sv=new P(369),tv=new P(1241,!0),uv=new P(368),vv=new P(1300,!0),wv=new Nu(1273,["en","de","fr","es","ja"]),xv=new Nu(1261,["44786015","44786016"]),yv=new P(1361),zv=new P(1372,!0),Av=new P(290),Bv=new P(1382),Cv=new P(1222),Dv=new P(1354),Ev=new P(1341),Fv=new P(1350),Gv=new P(1356),Hv=new P(626390500),Iv=new Q(717888910,.5),Jv=new Ou(627094447,"1 2 3 4 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 24 29 30 34".split(" ")),Kv=new Q(717888911),Lv=new Ou(641845510,["33","38"]),Mv=new P(566279275), 
Nv=new P(622128248),Ov=new P(566279276),Pv=new Mu(589752731,"#FFFFFF"),Qv=new Mu(589752730,"#1A73E8"),Rv=new Ou(635821288,["29_18","30_19"]),Sv=new Mu(716722045),Tv=new Ou(631402549),Uv=new P(728618449),Vv=new P(732113159),Wv=new Ou(636146137,["29_5","30_6"]),Xv=new P(636570127,!0),Yv=new Q(717888912,.6),Zv=new Ou(627094446,"1 2 4 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 24 29 30 34".split(" ")),$v=new Q(652486359,3),aw=new Q(626062006,670),bw=new Q(688905693,2),cw=new Q(666400580,22),dw=new Nu(712458671, 
" ar bn en es fr hi id ja ko mr pt ru sr te th tr uk vi zh".split(" ")),ew=new Q(687270738,500),fw=new P(720093567),gw=new Ou(683929765),hw=new P(713244099),iw=new P(683614711),jw=new P(736171003),kw=new P(45683445),lw=new P(506914611),mw=new P(655991266,!0),nw=new P(725532016,!0),ow=new Ou(630330362),pw=new Q(643258048,.15),qw=new Q(643258049,.33938),rw=new Q(618163195,15E3),sw=new Q(624950166,15E3),tw=new Q(623405755,300),uw=new Q(508040914,500),vw=new Q(547455356,49),ww=new Q(727864505,3),xw=new Q(650548030, 
2),yw=new Q(650548032,300),zw=new Q(650548031,1),Aw=new Q(655966487,300),Bw=new Q(655966486,250),Cw=new Q(469675170,6E4),or=new P(737425145),Dw=new P(562896595),Ew=new P(675298507),Fw=new P(644381219),Gw=new P(644381220),Hw=new P(676460084),Iw=new P(710737579),Jw=new P(45650663),Kw=new Q(684147713,-1),Lw=new P(678806782,!0),Mw=new P(570863962,!0),Nw=new Mu(570879859,"control_1\\.\\d"),Ow=new Q(570863961,50),Pw=new P(570879858,!0),Qw=new Q(63,30),Rw=new P(1134),Sw=new P(562874197),Tw=new P(562874196), 
Uw=new P(555237685,!0),Vw=new P(45460956),Ww=new P(45414947,!0),Xw=new Q(472785970,500),Yw=new Q(550718588,250),Zw=new Q(624290870,50),$w=new P(506738118),ax=new P(77),bx=new P(78),cx=new P(83),dx=new P(80),ex=new P(76),fx=new P(84),gx=new P(1973),hx=new P(188),ix=new P(485990406);var jx=class{constructor(){const a={};this.j=(b,c)=>a[b]!=null?a[b]:c;this.C=(b,c)=>a[b]!=null?a[b]:c;this.B=(b,c)=>a[b]!=null?a[b]:c;this.g=(b,c)=>a[b]!=null?a[b]:c;this.l=(b,c)=>a[b]!=null?c.concat(a[b]):c;this.i=()=>{}}};function M(a){return L(jx).j(a.g,a.defaultValue)}function V(a){return L(jx).C(a.g,a.defaultValue)}function kx(a){return L(jx).B(a.g,a.defaultValue)}function lx(a){return L(jx).l(a.g,a.defaultValue)};var nx=(a,b,c,d=0)=>{var e=mx(b,c,d);if(e.M){for(c=b=e.M;c=e.Sd(c);)b=c;e={anchor:b,position:e.se}}else e={anchor:b,position:c};a["google-ama-order-assurance"]=d;Ku(a,e.anchor,e.position)},ox=(a,b,c,d=0)=>{M(ov)?nx(a,b,c,d):Ku(a,b,c)}; 
function mx(a,b,c){const d=f=>{f=px(f);return f==null?!1:c<f},e=f=>{f=px(f);return f==null?!1:c>f};switch(b){case 0:return{M:qx(a.previousSibling,d),Sd:f=>qx(f.previousSibling,d),se:0};case 2:return{M:qx(a.lastChild,d),Sd:f=>qx(f.previousSibling,d),se:0};case 3:return{M:qx(a.nextSibling,e),Sd:f=>qx(f.nextSibling,e),se:3};case 1:return{M:qx(a.firstChild,e),Sd:f=>qx(f.nextSibling,e),se:3}}throw Error("Un-handled RelativePosition: "+b);} 
function px(a){return a.hasOwnProperty("google-ama-order-assurance")?a["google-ama-order-assurance"]:null}function qx(a,b){return a&&b(a)?a:null};function rx(a,b){try{const c=b.document.documentElement.getBoundingClientRect(),d=a.getBoundingClientRect();return{x:d.left-c.left,y:d.top-c.top}}catch(c){return null}}function sx(a,b){const c=a.google_reactive_ad_format===40,d=a.google_reactive_ad_format===16;return!!a.google_ad_resizable&&(!a.google_reactive_ad_format||c)&&!d&&!!b.navigator&&/iPhone|iPod|iPad|Android|BlackBerry/.test(b.navigator.userAgent)&&b===b.top}function tx(a,b,c){a=a.style;b==="rtl"?a.marginRight=c:a.marginLeft=c} 
function ux(a,b,c){a=rx(b,a);return c==="rtl"?-a.x:a.x}function vx(a,b){b=b.parentElement;return b?(a=td(b,a))?a.direction:"":""}function wx(a,b,c){if(ux(a,b,c)!==0){tx(b,c,"0px");var d=ux(a,b,c);tx(b,c,`${-1*d}px`);a=ux(a,b,c);a!==0&&a!==d&&tx(b,c,`${d/(a-d)*d}px`)}};const xx=RegExp("(^| )adsbygoogle($| )");function yx(a,b){for(let c=0;c<b.length;c++){const d=b[c],e=Mc(d.property);a[e]=d.value}}function zx(a,b,c,d,e,f){a=Ax(a,e);a.ua.setAttribute("data-ad-format",d?d:"auto");Bx(a,b,c,f);return a}function Cx(a,b,c=null){a=Ax(a,{});Bx(a,b,null,c);return a}function Bx(a,b,c,d){var e=[];if(d=d&&d.Og)a.yb.className=d.join(" ");a=a.ua;a.className="adsbygoogle";a.setAttribute("data-ad-client",b);c&&a.setAttribute("data-ad-slot",c);e.length&&a.setAttribute("data-ad-channel",e.join("+"))} 
function Ax(a,b){const c=Ju(a,b.clearBoth||!1);var d=c.style;d.textAlign="center";b.re&&yx(d,b.re);a=Pl(new Dl(a),"INS");d=a.style;d.display="block";d.margin="auto";d.backgroundColor="transparent";b.yg&&(d.marginTop=b.yg);b.bf&&(d.marginBottom=b.bf);b.Ac&&yx(d,b.Ac);c.appendChild(a);return{yb:c,ua:a}} 
function Dx(a,b,c){b.dataset.adsbygoogleStatus="reserved";b.className+=" adsbygoogle-noablate";const d={element:b};c=c&&c.Zc();if(b.hasAttribute("data-pub-vars")){try{c=JSON.parse(b.getAttribute("data-pub-vars"))}catch(e){return}b.removeAttribute("data-pub-vars")}c&&(d.params=c);(a.adsbygoogle=a.adsbygoogle||[]).push(d)} 
function Ex(a){const b=Iu(a.document);Ga(b,function(c){const d=Fx(a,c);var e;if(e=d){e=(e=rx(c,a))?e.y:0;const f=ir(a);e=!(e<f)}e&&(c.setAttribute("data-pub-vars",JSON.stringify(d)),c.removeAttribute("height"),c.style.removeProperty("height"),c.removeAttribute("width"),c.style.removeProperty("width"),Dx(a,c))})}function Fx(a,b){b=b.getAttribute("google_element_uid");a=a.google_sv_map;if(!b||!a||!a[b])return null;a=a[b];b={};for(let c in db)a[db[c]]&&(b[db[c]]=a[db[c]]);return b};var Hx=(a,b,c)=>{if(!b||!c)return!1;var d=b.parentElement;const e=c.parentElement;if(!d||!e||d!=e)return!1;d=0;for(b=b.nextSibling;d<10&&b;){if(b==c)return!0;if(Gx(a,b))break;b=b.nextSibling;d++}return!1}; 
const Gx=(a,b)=>{if(b.nodeType==3)return b.nodeType==3?(b=b.data,a=b.indexOf("&")!=-1?Jc(b,a.document):b,a=/\S/.test(a)):a=!1,a;if(b.nodeType==1){var c=a.getComputedStyle(b);if(c.opacity=="0"||c.display=="none"||c.visibility=="hidden")return!1;if((c=b.tagName)&&Er.contains(c.toUpperCase()))return!0;b=b.childNodes;for(c=0;c<b.length;c++)if(Gx(a,b[c]))return!0}return!1}; 
var Ix=a=>{if(a>=460)return a=Math.min(a,1200),Math.ceil(a<800?a/4:200);a=Math.min(a,600);return a<=420?Math.ceil(a/1.2):Math.ceil(a/1.91)+130};var Jx=class{constructor(){this.g={clearBoth:!0}}i(a,b,c,d){return zx(d.document,a,null,null,this.g,b)}j(a){return Ix(Math.min(a.screen.width||0,a.screen.height||0))}};function Kx(a){const b=[];wr(a.getElementsByTagName("p"),function(c){Lx(c)>=100&&b.push(c)});return b}function Lx(a){if(a.nodeType==3)return a.length;if(a.nodeType!=1||a.tagName=="SCRIPT")return 0;let b=0;wr(a.childNodes,function(c){b+=Lx(c)});return b}function Mx(a){return a.length==0||isNaN(a[0])?a:"\\"+(30+parseInt(a[0],10))+" "+a.substring(1)} 
function Nx(a,b){if(a.g==null)return b;switch(a.g){case 1:return b.slice(1);case 2:return b.slice(0,b.length-1);case 3:return b.slice(1,b.length-1);case 0:return b;default:throw Error("Unknown ignore mode: "+a.g);}} 
function Ox(a,b){var c=[];try{c=b.querySelectorAll(a.l)}catch(d){}if(!c.length)return[];b=Va(c);b=Nx(a,b);typeof a.i==="number"&&(c=a.i,c<0&&(c+=b.length),b=c>=0&&c<b.length?[b[c]]:[]);if(typeof a.j==="number"){c=[];for(let d=0;d<b.length;d++){const e=Kx(b[d]);let f=a.j;f<0&&(f+=e.length);f>=0&&f<e.length&&c.push(e[f])}b=c}return b} 
var Px=class{constructor(a,b,c,d){this.l=a;this.i=b;this.j=c;this.g=d}toString(){return JSON.stringify({nativeQuery:this.l,occurrenceIndex:this.i,paragraphIndex:this.j,ignoreMode:this.g})}};var Qx=class{constructor(){this.g=hd`https://pagead2.googlesyndication.com/pagead/js/err_rep.js`}ma(a,b,c=.01,d="jserror"){if(Math.random()>c)return!1;b.error&&b.meta&&b.id||(b=new jm(b,{context:a,id:d}));q.google_js_errors=q.google_js_errors||[];q.google_js_errors.push(b);q.error_rep_loaded||(rd(q.document,this.g),q.error_rep_loaded=!0);return!1}mb(a,b){try{return b()}catch(c){if(!this.ma(a,c,.01,"jserror"))throw c;}}nb(a,b,c){return(...d)=>this.mb(a,()=>b.apply(c,d))}Pa(a,b){b.catch(c=>{c=c?c:"unknown rejection"; 
this.ma(a,c instanceof Error?c:Error(c),void 0)})}};function Rx(a,b){b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];b.length<2048&&b.push(a)} 
function Sx(a,b,c,d){const e=d||window,f=typeof queueMicrotask!=="undefined";return function(...g){f&&queueMicrotask(()=>{e.google_rum_task_id_counter=e.google_rum_task_id_counter||1;e.google_rum_task_id_counter+=1});const h=tm();let k,l=3;try{k=b.apply(this,g)}catch(m){l=13;if(!c)throw m;c(a,m)}finally{e.google_measure_js_timing&&h&&Rx({label:a.toString(),value:h,duration:(tm()||0)-h,type:l,...(f&&{taskId:e.google_rum_task_id_counter=e.google_rum_task_id_counter||1})},e)}return k}} 
function Tx(a,b){return Sx(754,a,(c,d)=>{(new Qx).ma(c,d)},b)};function Ux(a,b,c){return Sx(a,b,void 0,c).apply()}function Vx(a,b){return Tx(a,b).apply()}function Wx(a){if(!a)return null;var b=y(a,7);if(y(a,1)||a.getId()||Uh(a,4).length>0){var c=a.getId(),d=y(a,1),e=Uh(a,4);b=Oh(a,2);var f=Oh(a,5);a=Xx(Ph(a,6));let g="";d&&(g+=d);c&&(g+="#"+Mx(c));if(e)for(c=0;c<e.length;c++)g+="."+Mx(e[c]);b=(e=g)?new Px(e,b,f,a):null}else b=b?new Px(b,Oh(a,2),Oh(a,5),Xx(Ph(a,6))):null;return b}const Yx={1:1,2:2,3:3,0:0};function Xx(a){return a==null?a:Yx[a]} 
function Zx(a){const b=[];for(let c=0;c<a.length;c++){const d=y(a[c],1),e=a[c].getValue();d&&e!=null&&b.push({property:d,value:e})}return b}function $x(a,b){const c={};a&&(c.yg=y(a,1),c.bf=y(a,2),c.clearBoth=!!Nh(a,3));b&&(c.re=Zx(zh(b,Wt,3,eh()).map(d=>Fg(d))),c.Ac=Zx(zh(b,Wt,4,eh()).map(d=>Fg(d))));return c}const ay={1:0,2:1,3:2,4:3},by={0:1,1:2,2:3,3:4};var cy=class{constructor(a){this.g=a}i(a,b,c,d){return zx(d.document,a,null,null,this.g,b)}j(){return null}};var dy=class{constructor(a){this.i=a}g(a){a=Math.floor(a.i);const b=Ix(a);return new st(["ap_container"],{google_reactive_ad_format:27,google_responsive_auto_format:16,google_max_num_ads:1,google_ad_type:this.i,google_ad_format:a+"x"+b,google_ad_width:a,google_ad_height:b})}};var ey=class{constructor(a,b){this.l=a;this.j=b}i(){return this.l}g(){return this.j}};var fy=class{constructor(a){this.g=a}i(a,b,c,d){var e=zh(this.g,Xt,9,eh()).length>0?zh(this.g,Xt,9,eh())[0]:null,f=$x(xh(this.g,Yt,3),e);if(!e)return null;if(e=y(e,1)){d=d.document;var g=c.tagName;c=Pl(new Dl(d),g);c.style.clear=f.clearBoth?"both":"none";g=="A"&&(c.style.display="block");c.style.padding="0px";c.style.margin="0px";f.re&&yx(c.style,f.re);d=Pl(new Dl(d),"INS");f.Ac&&yx(d.style,f.Ac);c.appendChild(d);f={yb:c,ua:d};f.ua.setAttribute("data-ad-type","text");f.ua.setAttribute("data-native-settings-key", 
e);Bx(f,a,null,b);a=f}else a=null;return a}j(){var a=zh(this.g,Xt,9,eh()).length>0?zh(this.g,Xt,9,eh())[0]:null;if(!a)return null;a=zh(a,Wt,3,eh());for(let b=0;b<a.length;b++){const c=a[b];if(y(c,1)=="height"&&parseInt(c.getValue(),10)>0)return parseInt(c.getValue(),10)}return null}};var gy=class{constructor(a){this.g=a}i(a,b,c,d){if(!this.g)return null;const e=this.g.google_ad_format||null,f=this.g.google_ad_slot||null;if(c=c.style){var g=[];for(let h=0;h<c.length;h++){const k=c.item(h);k!=="width"&&k!=="height"&&g.push({property:k,value:c.getPropertyValue(k)})}c={Ac:g}}else c={};a=zx(d.document,a,f,e,c,b);a.ua.setAttribute("data-pub-vars",JSON.stringify(this.g));return a}j(){return this.g?parseInt(this.g.google_ad_height,10)||null:null}Zc(){return this.g}};var hy=class{constructor(a){this.i=a}g(){return new st([],{google_ad_type:this.i,google_reactive_ad_format:26,google_ad_format:"fluid"})}};var iy=class{constructor(a,b){this.l=a;this.j=b}g(){return this.j}i(a){a=Ox(this.l,a.document);return a.length>0?a[0]:null}};function jy(a,b,c){const d=[];for(let t=0;t<a.length;t++){var e=a[t];var f=t,g=b,h=c,k=e.ja();if(k){var l=Wx(k);if(l){var m=e.g();m=ay[m];var n=m===void 0?null:m;if(n===null)e=null;else{m=(m=v(e,Yt,3))?Nh(m,3):null;l=new iy(l,n);n=Vh(e,10).slice(0);Oh(k,5)!=null&&n.push(1);var p=h?h:{};h=Oh(e,12);k=bh(e,qt,4)?v(e,qt,4):null;Zh(e,8)==1?(p=p.Ei||null,e=new ky(l,new cy($x(v(e,Yt,3),null)),p,m,0,n,k,g,f,h,e)):e=Zh(e,8)==2?new ky(l,new fy(e),p.Ij||new hy("text"),m,1,n,k,g,f,h,e):null}}else e=null}else e= 
null;e!==null&&d.push(e)}return d}function ly(a){return a.l}function my(a){return a.Ha}function ny(a){return a.B instanceof gy?a.B.Zc():null}function oy(a,b,c){yr(a.O,b)||a.O.set(b,[]);a.O.get(b).push(c)}function py(a){return a.B.j(a.i)}function qy(a,b=null,c=null){return new ky(a.F,b||a.B,c||a.X,a.G,a.kc,a.ed,a.Ae,a.i,a.qa,a.H,a.j,a.C,a.aa)} 
var ky=class{constructor(a,b,c,d,e,f,g,h,k,l=null,m=null,n=null,p=null){this.F=a;this.B=b;this.X=c;this.G=d;this.kc=e;this.ed=f;this.Ae=g?g:new qt;this.i=h;this.qa=k;this.H=l;this.j=m;(a=!m)||(a=!(m.ja()&&Yh(m.ja(),5)!=null));this.Ha=!a;this.C=n;this.aa=p;this.K=[];this.l=!1;this.O=new Cr}da(){return this.i}g(){return this.F.g()}};function ry(a,b,c,d,e,f){const g=pt();return new ky(new ey(c,e),new Jx,new dy(a),!0,2,[],g,d,null,null,null,b,f)}function sy(a,b,c,d,e){const f=pt();return new ky(new ey(b,d),new cy({clearBoth:!0}),null,!0,2,[],f,c,null,null,null,a,e)};var ty=class{constructor(a,b,c){this.articleStructure=a;this.element=b;this.A=c}da(){return this.A}C(a){return ry(a,this.articleStructure,this.element,this.A,3,null)}j(){return sy(this.articleStructure,this.element,this.A,3,null)}};const uy={TABLE:{Nc:new Ts([1,2])},THEAD:{Nc:new Ts([0,3,1,2])},TBODY:{Nc:new Ts([0,3,1,2])},TR:{Nc:new Ts([0,3,1,2])},TD:{Nc:new Ts([0,3])}};function vy(a,b,c,d){const e=c.childNodes;c=c.querySelectorAll(b);b=[];for(const f of c)c=Ea(e,f),c<0||b.push(new wy(a,[f],c,f,3,Ll(f).trim(),d));return b} 
function xy(a,b,c){let d=[];const e=[],f=b.childNodes,g=f.length;let h=0,k="";for(let n=0;n<g;n++){var l=f[n];if(l.nodeType==1||l.nodeType==3){if(l.nodeType!=1)var m=null;else l.tagName=="BR"?m=l:(m=c.getComputedStyle(l).getPropertyValue("display"),m=m=="inline"||m=="inline-block"?null:l);m?(d.length&&k&&e.push(new wy(a,d,n-1,m,0,k,c)),d=[],h=n+1,k=""):(d.push(l),l=Ll(l).trim(),k+=l&&k?" "+l:l)}}d.length&&k&&e.push(new wy(a,d,h,b,2,k,c));return e}function yy(a,b){return a.g-b.g} 
var wy=class{constructor(a,b,c,d,e,f,g){this.l=a;this.Jd=b.slice(0);this.g=c;this.He=d;this.Ie=e;this.B=f;this.i=g}da(){return this.i}C(a){return ry(a,this.l,this.He,this.i,this.Ie,this.g)}j(){return sy(this.l,this.He,this.i,this.Ie,this.g)}};function zy(a){return Ua(a.B?xy(a.g,a.j,a.i):[],a.C?vy(a.g,a.C,a.j,a.i):[]).filter(b=>{var c=b.He.tagName;c?(c=uy[c.toUpperCase()],b=c!=null&&c.Nc.contains(b.Ie)):b=!1;return!b})}var Ay=class{constructor(a,b,c){this.j=a;this.C=b.Id;this.B=b.Zg;this.g=b.articleStructure;this.i=c;this.l=b.Ig}};function By(a,b){if(!b)return!1;const c=qa(b),d=a.g.get(c);if(d!=null)return d;if(b.nodeType==1&&(b.tagName=="UL"||b.tagName=="OL")&&a.i.getComputedStyle(b).getPropertyValue("list-style-type")!="none")return a.g.set(c,!0),!0;b=By(a,b.parentNode);a.g.set(c,b);return b}function Cy(a,b){return Pa(b.Jd,c=>By(a,c))}var Dy=class{constructor(a){this.g=new Cr;this.i=a}};var Ey=class{constructor(a,b){this.l=a;this.g=[];this.i=[];this.j=b}};var Gy=(a,{lh:b=!1,qg:c=!1,wh:d=c?2:3,pg:e=null}={})=>{a=zy(a);return Fy(a,{lh:b,qg:c,wh:d,pg:e})},Fy=(a,{lh:b=!1,qg:c=!1,wh:d=c?2:3,pg:e=null}={})=>{if(d<2)throw Error("minGroupSize should be at least 2, found "+d);var f=a.slice(0);f.sort(yy);a=[];b=new Ey(b,e);for(const g of f){e={te:g,Ud:g.B.length<51?!1:b.j!=null?!Cy(b.j,g):!0};if(b.l||e.Ud)b.g.length?(f=b.g[b.g.length-1].te,f=Hx(f.da(),f.Jd[f.Jd.length-1],e.te.Jd[0])):f=!0,f?(b.g.push(e),e.Ud&&b.i.push(e.te)):(b.g=[e],b.i=e.Ud?[e.te]:[]);if(b.i.length>= 
d){e=b;f=c?0:1;if(f<0||f>=e.i.length)e=null;else{for(f=e.i[f];e.g.length&&!e.g[0].Ud;)e.g.shift();e.g.shift();e.i.shift();e=f}e&&a.push(e)}}return a};var Iy=(a,b,c=!1)=>{a=Hy(a,b);const d=new Dy(b);return Ns(a,e=>Gy(e,{qg:c,pg:d}))},Jy=(a,b)=>{a=Hy(a,b);const c=new Dy(b);return Ns(a,d=>{if(d.l){var e=d.g;var f=d.i;d=d.j.querySelectorAll(d.l);var g=[];for(var h of d)g.push(new ty(e,h,f));e=g}else e=[];d=e.slice(0);if(d.length){e=[];f=d[0];for(g=1;g<d.length;g++){const m=d[g];h=f;b:{if(h.element.hasAttributes())for(l of h.element.attributes)if(l.name.toLowerCase()==="style"&&l.value.toLowerCase().includes("background-image")){var k=!0;break b}k= 
h.element.tagName;k=k==="IMG"||k==="SVG"}(k||h.element.textContent.length>1)&&!By(c,f.element)&&Hx(m.da(),f.element,m.element)&&e.push(f);f=m}var l=e}else l=[];return l})},Hy=(a,b)=>{const c=new Cr;a.forEach(d=>{var e=Wx(xh(d,ht,1));if(e){var f=e.toString();yr(c,f)||c.set(f,{articleStructure:d,zi:e,Id:null,Zg:!1,Ig:null});e=c.get(f);(f=(f=v(d,ht,2))?y(f,7):null)?e.Id=e.Id?e.Id+","+f:f:e.Zg=!0;d=v(d,ht,4);e.Ig=d?y(d,7):null}});return Br(c).map(d=>{const e=Ox(d.zi,b.document);return e.length?new Ay(e[0], 
d,b):null}).filter(d=>d!=null)};var Ky=a=>a?.google_ad_slot?Us(new it(1,{ni:a.google_ad_slot})):Ws(Error("Missing dimension when creating placement id")),My=a=>{switch(a.kc){case 0:case 1:var b=a.j;b==null?a=null:(a=b.ja(),a==null?a=null:(b=b.g(),a=b==null?null:new it(0,{Jg:[a],Lh:b})));return a!=null?Us(a):Ws(Error("Missing dimension when creating placement id"));case 2:return a=Ly(a),a!=null?Us(a):Ws(Error("Missing dimension when creating placement id"));default:return Ws(Error("Invalid type: "+a.kc))}}; 
const Ly=a=>{if(a==null||a.C==null)return null;const b=v(a.C,ht,1),c=v(a.C,ht,2);if(b==null||c==null)return null;const d=a.aa;if(d==null)return null;a=a.g();return a==null?null:new it(0,{Jg:[b,c],Hj:d,Lh:by[a]})};function Ny(a){const b=ny(a.ha);return(b?Ky(b):My(a.ha)).map(c=>lt(c))}function EA(a){a.g=a.g||Ny(a);return a.g}function FA(a,b){if(a.ha.l)throw Error("AMA:AP:AP");ox(b,a.ja(),a.ha.g());a=a.ha;a.l=!0;b!=null&&a.K.push(b)}const GA=class{constructor(a,b,c){this.ha=a;this.i=b;this.pa=c;this.g=null}ja(){return this.i}fill(a,b){var c=this.ha;(a=c.B.i(a,b,this.i,c.i))&&FA(this,a.yb);return a}};function HA(a,b){return Vx(()=>{const c=[],d=[];try{var e=[];for(var f=0;f<a.length;f++){var g=a[f],h=g.F.i(g.i);h&&e.push({Gh:g,anchorElement:h})}for(g=0;g<e.length;g++){f=d;var k=f.push;{var l=e[g];const B=l.anchorElement,I=l.Gh;var m=I.G,n=I.i.document.createElement("div");n.className="google-auto-placed";var p=n.style;p.textAlign="center";p.width="100%";p.height="0px";p.clear=m?"both":"none";h=n;try{ox(h,B,I.g());var t=h}catch(T){throw Lu(h),T;}}k.call(f,t)}const z=rr(b),w=sr(b);for(k=0;k<d.length;k++){const B= 
d[k].getBoundingClientRect(),I=e[k];c.push(new GA(I.Gh,I.anchorElement,new Es(B.left+w,B.top+z,B.right-B.left)))}}finally{for(e=0;e<d.length;e++)Lu(d[e])}return c},b)};const IA={1:"0.5vp",2:"300px"},JA={1:700,2:1200},KA={[1]:{Uh:"3vp",tg:"1vp",Th:"0.3vp"},[2]:{Uh:"900px",tg:"300px",Th:"90px"}}; 
function LA(a,b,c){var d=MA(a),e=ir(a)||JA[d],f=void 0;c&&(f=(c=(c=NA(zh(c,zt,2,eh()),d))?v(c,xt,7):void 0)?OA(c,e):void 0);c=f;f=MA(a);a=ir(a)||JA[f];const g=PA(KA[f].tg,a);a=g===null?QA(f,a):new RA(g,g,SA(g,8),8,.3,c);c=PA(KA[d].Uh,e);f=PA(KA[d].tg,e);d=PA(KA[d].Th,e);e=a.j;c&&d&&f&&b!==void 0&&(e=b<=.5?f+(1-2*b)*(c-f):d+(2-2*b)*(f-d));return new RA(e,e,SA(e,a.i),a.i,a.l,a.g)}function TA(a,b){const c=kg(Yg(a,4));a=dh(a,5);return c==null||a==null?b:new RA(a,0,[],c,1)} 
function UA(a,b){const c=MA(a);a=ir(a)||JA[c];if(!b)return QA(c,a);if(b=NA(zh(b,zt,2,eh()),c))if(b=VA(b,a))return b;return QA(c,a)}function WA(a){const b=MA(a);a=ir(a)||JA[b];return QA(b,a)}function XA(){return M(dv)?new RA(0,null,[],8,.3):new RA(0,null,[],3,null)}function YA(a,b){let c={ld:a.j,Fb:a.B};for(let d of a.C)d.adCount<=b&&(c=d.wd);return c} 
function ZA(a,b,c){var d=Nh(b,2);b=v(b,zt,1);var e=MA(c);var f=ir(c)||JA[e];c=PA(b?.i(),f)??a.j;e=PA(b?.g(),f)??a.B;d=d?[]:$A(b?.j(),f)??a.C;const g=b?.l()??a.i,h=b?.C()??a.l;a=(b?.B()?OA(v(b,xt,7),f):null)??a.g;return new RA(c,e,d,g,h,a)} 
function aB(a,b){var c=MA(b);const d=new At,e=new zt;let f=!1;var g=V(gv);g>=0&&(ai(e,4,g),f=!0);g=null;c===1?(c=V(kv),c>=0&&(g=c+"vp")):(c=V(jv),c>=0&&(g=c+"px"));c=V(iv);c>=0&&(g=c+"px");g!==null&&(ei(e,2,g),f=!0);c=M(mv)?"0px":null;c!==null&&(ei(e,5,c),f=!0);if(M(nv))$h(d,2,!0),f=!0;else if(c!==null||g!==null){const m=[];for(const n of a.C){var h=m,k=h.push;var l=new yt;l=ai(l,1,n.adCount);l=ei(l,3,c??n.wd.Fb+"px");l=ei(l,2,g??n.wd.ld+"px");k.call(h,l)}Kh(e,3,m)}return f?(x(d,1,e),ZA(a,d,b)):a} 
var RA=class{constructor(a,b,c,d,e,f){this.j=a;this.B=b;this.C=c.sort((g,h)=>g.adCount-h.adCount);this.i=d;this.l=e;this.g=f}};function NA(a,b){for(let c of a)if(Zh(c,1)==b)return c;return null}function $A(a,b){if(a===void 0)return null;const c=[];for(let d of a){a=Oh(d,1);const e=PA(y(d,2),b),f=PA(y(d,3),b);if(typeof a!=="number"||e===null)return null;c.push({adCount:a,wd:{ld:e,Fb:f}})}return c} 
function VA(a,b){const c=PA(a.i(),b),d=PA(a.g(),b);if(c===null)return null;const e=Oh(a,4);if(e==null)return null;var f=a.j();f=$A(f,b);if(f===null)return null;const g=v(a,xt,7);b=g?OA(g,b):void 0;return new RA(c,d,f,e,dh(a,6),b)}function QA(a,b){a=PA(IA[a],b);return M(dv)?new RA(a===null?Infinity:a,null,[],8,.3):new RA(a===null?Infinity:a,null,[],3,null)}function PA(a,b){if(!a)return null;const c=parseFloat(a);return isNaN(c)?null:a.endsWith("px")?c:a.endsWith("vp")?c*b:null} 
function MA(a){a=hr(a)>=900;return Lb()&&!a?1:2}function SA(a,b){if(b<4)return[];const c=Math.ceil(b/2);return[{adCount:c,wd:{ld:a*2,Fb:a*2}},{adCount:c+Math.ceil((b-c)/2),wd:{ld:a*3,Fb:a*3}}]}function OA(a,b){const c=PA(y(a,2),b)||0,d=Oh(a,3)||1;a=PA(y(a,1),b)||0;return{xh:c,uh:d,Fc:a}};function bB(a,b,c){return ar({top:a.g.top-(c+1),right:a.g.right+(c+1),bottom:a.g.bottom+(c+1),left:a.g.left-(c+1)},b.g)}function cB(a){if(!a.length)return null;const b=br(a.map(c=>c.g));a=a.reduce((c,d)=>c+d.i,0);return new dB(b,a)}var dB=class{constructor(a,b){this.g=a;this.i=b}};function cq(){return"m202503250101"};var eB=Sj($p);var bq=Sj(dq);function fB(a,b){return b(a)?a:void 0} 
function gB(a,b,c,d,e){c=c instanceof jm?c.error:c;var f=new hq;const g=new gq;try{var h=Zd(window);di(g,1,h)}catch(p){}try{var k=L(Sq).g();oh(g,2,k,Qf)}catch(p){}try{fi(g,3,window.document.URL)}catch(p){}h=x(f,2,g);k=new fq;b=F(k,1,b);try{var l=gf(c?.name)?c.name:"Unknown error";fi(b,2,l)}catch(p){}try{var m=gf(c?.message)?c.message:`Caught ${c}`;fi(b,3,m)}catch(p){}try{var n=gf(c?.stack)?c.stack:Error().stack;n&&oh(b,4,n.split(/\n\s*/),rg)}catch(p){}l=Ah(h,1,iq,b);if(e){m=0;switch(e.errSrc){case "LCC":m= 
1;break;case "PVC":m=2}n=aq();b=fB(e.shv,gf);n=fi(n,2,b);m=F(n,6,m);n=Fg(eB());b=fB(e.es,pf());n=oh(n,1,b,Qf);n=Wg(n);m=x(m,4,n);n=fB(e.client,gf);m=ei(m,3,n);n=fB(e.slotname,gf);m=fi(m,7,n);e=fB(e.tag_origin,gf);e=fi(m,8,e);e=Wg(e)}else e=Wg(aq());e=Ah(l,6,jq,e);d=di(e,5,d??1);a.Ph(d)};let hB,iB=64;function jB(){try{return hB??(hB=new Uint32Array(64)),iB>=64&&(crypto.getRandomValues(hB),iB=0),hB[iB++]}catch(a){return Math.floor(Math.random()*2**32)}};var lB=class{constructor(){this.g=kB}};function kB(){return{fk:jB()+(jB()&2**21-1)*2**32,Wi:Number.MAX_SAFE_INTEGER}};var oB=class{constructor(a=!1){var b=mB;this.I=nB;this.i=a;this.B=b;this.g=null;this.l=this.ma}j(a){this.g=a}C(){}mb(a,b,c){let d;try{d=b()}catch(e){b=this.i;try{b=this.l(a,km(e),void 0,c)}catch(f){this.ma(217,f)}if(b)window.console?.error?.(e);else throw e;}return d}nb(a,b,c,d){return(...e)=>this.mb(a,()=>b.apply(c,e),d)}Pa(a,b,c){b.catch(d=>{d=d?d:"unknown rejection";this.ma(a,d instanceof Error?d:Error(d),void 0,c)})}ma(a,b,c,d){try{const g=c===void 0?1/this.B:c===0?0:1/c;var e=(new lB).g();if(g> 
0&&e.fk*g<=e.Wi){var f=this.I;c={};if(this.g)try{this.g(c)}catch(h){}if(d)try{d(c)}catch(h){}gB(f,a,b,g,c)}}catch(g){}return this.i}};var pB=class extends Error{constructor(a=""){super();this.name="TagError";this.message=a?"adsbygoogle.push() error: "+a:"";Error.captureStackTrace?Error.captureStackTrace(this,pB):this.stack=Error().stack||""}};let nB,qB,rB,sB,mB;const tB=new Bm(q);(function(a,b,c=!0){({vk:mB,vj:rB}=uB());qB=a||new Vq;Uq(qB,rB);nB=b||new Pq(cq(),1E3);sB=new oB(c);q.document.readyState==="complete"?q.google_measure_js_timing||zm(tB):tB.g&&qb(q,"load",()=>{q.google_measure_js_timing||zm(tB)})})();function vB(a,b,c){return sB.mb(a,b,c)}function wB(a,b){return sB.nb(a,b)}function xB(a,b,c){sB.Pa(a,b,c)}function yB(a,b,c=.01){const d=L(Sq).g();!b.eid&&d.length&&(b.eid=d.toString());Jm(qB,a,b,!0,c)} 
function zB(a,b,c=mB,d){return sB.ma(a,b,c,d,void 0)}function uB(){let a,b;typeof q.google_srt==="number"?(b=q.google_srt,a=q.google_srt===0?1:.01):(b=Math.random(),a=.01);return{vk:a,vj:b}};function AB(a=null){({googletag:a}=a??window);return a?.apiReady?a:void 0};function BB(a,b){var c=CB(b,".google-auto-placed");const d=DB(b),e=EB(b),f=FB(b),g=GB(b),h=HB(b),k=CB(b,"div.googlepublisherpluginad"),l=CB(b,"html > ins.adsbygoogle");let m=[].concat(...CB(b,"iframe[id^=aswift_],iframe[id^=google_ads_frame]"),...CB(b,"body ins.adsbygoogle"));b=[];for(const [n,p]of[[a.Td,c],[a.hc,d],[a.Fj,e],[a.Ff,f],[a.Gf,g],[a.Dj,h],[a.Ej,k],[a.Gj,l]])n===!1?b=b.concat(p):m=m.concat(p);a=IB(m);c=IB(b);a=a.slice(0);for(const n of c)for(c=0;c<a.length;c++)(n.contains(a[c])||a[c].contains(n))&& 
a.splice(c,1);return a}function JB(a){return!!a.className&&a.className.indexOf("google-auto-placed")!=-1}function KB(a){const b=AB(a);return b?La(Ma(b.pubads().getSlots(),c=>a.document.getElementById(c.getSlotElementId())),c=>c!=null):null}function CB(a,b){return Va(a.document.querySelectorAll(b))}function DB(a){return CB(a,"ins.adsbygoogle[data-anchor-status]")}function EB(a){return CB(a,"ins.adsbygoogle[data-ad-format=autorelaxed]")} 
function FB(a){return(KB(a)||CB(a,"div[id^=div-gpt-ad]")).concat(CB(a,"iframe[id^=google_ads_iframe]"))} 
function GB(a){return CB(a,"div.trc_related_container,div.OUTBRAIN,div[id^=rcjsload],div[id^=ligatusframe],div[id^=crt-],iframe[id^=cto_iframe],div[id^=yandex_], div[id^=Ya_sync],iframe[src*=adnxs],div.advertisement--appnexus,div[id^=apn-ad],div[id^=amzn-native-ad],iframe[src*=amazon-adsystem],iframe[id^=ox_],iframe[src*=openx],img[src*=openx],div[class*=adtech],div[id^=adtech],iframe[src*=adtech],div[data-content-ad-placement=true],div.wpcnt div[id^=atatags-]")} 
function HB(a){return CB(a,"ins.adsbygoogle-ablated-ad-slot")}function IB(a){const b=[];for(const c of a){a=!0;for(let d=0;d<b.length;d++){const e=b[d];if(e.contains(c)){a=!1;break}if(c.contains(e)){a=!1;b[d]=c;break}}a&&b.push(c)}return b};var LB=wB(453,BB),MB=wB(454,function(a,b){const c=CB(b,".google-auto-placed"),d=DB(b),e=EB(b),f=FB(b),g=GB(b),h=HB(b),k=CB(b,"div.googlepublisherpluginad");b=CB(b,"html > ins.adsbygoogle");return IB([...(a.Td===!0?c:[]),...(a.hc===!0?d:[]),...(a.Fj===!0?e:[]),...(a.Ff===!0?f:[]),...(a.Gf===!0?g:[]),...(a.Dj===!0?h:[]),...(a.Ej===!0?k:[]),...(a.Gj===!0?b:[])])});function NB(a,b,c){const d=OB(a);b=PB(d,b,c);return new QB(a,d,b)}function RB(a){return(a.bottom-a.top)*(a.right-a.left)>1}function SB(a){return a.g.map(b=>b.box)}function TB(a){return a.g.reduce((b,c)=>b+c.box.bottom-c.box.top,0)}var QB=class{constructor(a,b,c){this.j=a;this.g=b.slice(0);this.l=c.slice(0);this.i=null}}; 
function OB(a){const b=LB({hc:!1},a),c=sr(a),d=rr(a);return b.map(e=>{const f=e.getBoundingClientRect();return(e=JB(e))||RB(f)?{box:{top:f.top+d,right:f.right+c,bottom:f.bottom+d,left:f.left+c},Go:e?1:0}:null}).filter(hb(e=>e===null))}function PB(a,b,c){return b!=void 0&&a.length<=(c!=void 0?c:8)?UB(a,b):Ma(a,d=>new dB(d.box,1))} 
function UB(a,b){a=Ma(a,d=>new dB(d.box,1));const c=[];for(;a.length>0;){let d=a.pop(),e=!0;for(;e;){e=!1;for(let f=0;f<a.length;f++)if(bB(d,a[f],b)){d=cB([d,a[f]]);Array.prototype.splice.call(a,f,1);e=!0;break}}c.push(d)}return c};function VB(a,b,c){const d=Ds(c,b);return!Pa(a,e=>ar(e,d))}function WB(a,b,c,d,e){e=e.pa;const f=Ds(e,b),g=Ds(e,c),h=Ds(e,d);return!Pa(a,k=>ar(k,g)||ar(k,f)&&!ar(k,h))}function XB(a,b,c,d){const e=SB(a);if(VB(e,b,d.pa))return!0;if(!WB(e,b,c.xh,c.Fc,d))return!1;const f=new dB(Ds(d.pa,0),1);a=La(a.l,g=>bB(g,f,c.Fc));b=Na(a,(g,h)=>g+h.i);return a.length===0||b>c.uh?!1:!0};var YB=(a,b)=>{const c=[];let d=a;for(a=()=>{c.push({anchor:d.anchor,position:d.position});return d.anchor==b.anchor&&d.position==b.position};d;){switch(d.position){case 1:if(a())return c;d.position=2;case 2:if(a())return c;if(d.anchor.firstChild){d={anchor:d.anchor.firstChild,position:1};continue}else d.position=3;case 3:if(a())return c;d.position=4;case 4:if(a())return c}for(;d&&!d.anchor.nextSibling&&d.anchor.parentNode!=d.anchor.ownerDocument.body;){d={anchor:d.anchor.parentNode,position:3};if(a())return c; 
d.position=4;if(a())return c}d&&d.anchor.nextSibling?d={anchor:d.anchor.nextSibling,position:1}:d=null}return c};function ZB(a,b){const c=new dt,d=new Dr;b.forEach(e=>{if(Wh(e,Ht,1,Kt)){e=Wh(e,Ht,1,Kt);if(v(e,Gt,1)&&v(e,Gt,1).ja()&&v(e,Gt,2)&&v(e,Gt,2).ja()){const g=$B(a,v(e,Gt,1).ja()),h=$B(a,v(e,Gt,2).ja());if(g&&h)for(var f of YB({anchor:g,position:v(e,Gt,1).g()},{anchor:h,position:v(e,Gt,2).g()}))c.set(qa(f.anchor),f.position)}v(e,Gt,3)&&v(e,Gt,3).ja()&&(f=$B(a,v(e,Gt,3).ja()))&&c.set(qa(f),v(e,Gt,3).g())}else Wh(e,It,2,Kt)?aC(a,Wh(e,It,2,Kt),c):Wh(e,Ft,3,Kt)&&bC(a,Wh(e,Ft,3,Kt),d)});return new cC(c,d)} 
var cC=class{constructor(a,b){this.i=a;this.g=b}};const aC=(a,b,c)=>{v(b,Gt,2)?(b=v(b,Gt,2),(a=$B(a,b.ja()))&&c.set(qa(a),b.g())):v(b,ht,1)&&(a=dC(a,v(b,ht,1)))&&a.forEach(d=>{d=qa(d);c.set(d,1);c.set(d,4);c.set(d,2);c.set(d,3)})},bC=(a,b,c)=>{v(b,ht,1)&&(a=dC(a,v(b,ht,1)))&&a.forEach(d=>{c.add(qa(d))})},$B=(a,b)=>(a=dC(a,b))&&a.length>0?a[0]:null,dC=(a,b)=>(b=Wx(b))?Ox(b,a):null;var eC=class{constructor(){this.g=Yd();this.i=0}};function fC(a,b,c){switch(c){case 2:case 3:break;case 1:case 4:b=b.parentElement;break;default:throw Error("Unknown RelativePosition: "+c);}for(c=[];b;){if(gC(b))return!0;if(a.g.has(b))break;c.push(b);b=b.parentElement}c.forEach(d=>a.g.add(d));return!1}function hC(a){a=iC(a);return a.has("all")||a.has("after")}function jC(a){a=iC(a);return a.has("all")||a.has("before")}function iC(a){return(a=a&&a.getAttribute("data-no-auto-ads"))?new Set(a.split("|")):new Set} 
function gC(a){const b=iC(a);return a&&(a.tagName==="AUTO-ADS-EXCLUSION-AREA"||b.has("inside")||b.has("all"))}var kC=class{constructor(){this.g=new Set;this.i=new eC}};function lC(a){return function(b){return HA(b,a)}}function mC(a){const b=ir(a);return b?ya(nC,b+rr(a)):eb}function oC(a,b,c){if(a<0)throw Error("ama::ead:nd");if(a===Infinity)return eb;const d=SB(c||NB(b));return e=>VB(d,a,e.pa)}function pC(a,b,c,d){if(a<0||b.xh<0||b.uh<0||b.Fc<0)throw Error("ama::ead:nd");return a===Infinity?eb:e=>XB(d||NB(c,b.Fc),a,b,e)}function qC(a){if(!a.length)return eb;const b=new Ts(a);return c=>b.contains(c.kc)} 
function rC(a){return function(b){for(let c of b.ed)if(a.indexOf(c)>-1)return!1;return!0}}function sC(a){return a.length?function(b){const c=b.ed;return a.some(d=>c.indexOf(d)>-1)}:fb}function tC(a,b){if(a<=0)return fb;const c=mr(b).scrollHeight-a;return function(d){return d.pa.g<=c}}function uC(a){const b={};a&&a.forEach(c=>{b[c]=!0});return function(c){return!b[Ph(c.Ae,2)||0]}}function vC(a){return a.length?b=>a.includes(Ph(b.Ae,1)||0):fb} 
function wC(a,b){const c=ZB(a,b);return function(d){var e=d.ja();d=by[d.ha.g()];var f=c.i,g=qa(e);f=f.g.get(g);if(!(f=f?f.contains(d):!1))a:{if(c.g.contains(qa(e)))switch(d){case 2:case 3:f=!0;break a;default:f=!1;break a}for(e=e.parentElement;e;){if(c.g.contains(qa(e))){f=!0;break a}e=e.parentElement}f=!1}return!f}} 
function xC(){const a=new kC;return function(b){var c=b.ja(),d=by[b.ha.g()];a:switch(d){case 1:b=hC(c.previousElementSibling)||jC(c);break a;case 4:b=hC(c)||jC(c.nextElementSibling);break a;case 2:b=jC(c.firstElementChild);break a;case 3:b=hC(c.lastElementChild);break a;default:throw Error("Unknown RelativePosition: "+d);}c=fC(a,c,d);d=a.i;yB("ama_exclusion_zone",{typ:b?c?"siuex":"siex":c?"suex":"noex",cor:d.g,num:d.i++,dvc:Nd()},.1);return!(b||c)}} 
const nC=(a,b)=>b.pa.g>=a,yC=(a,b,c)=>{c=c.pa.i;return a<=c&&c<=b};function zC(a,b,c,d,e){var f=AC(BC(a,b),a);if(f.length===0){var g=!!v(b,Vt,6)?.g()?.length;f=v(b,Rt,28)?.i()?.i()&&g?AC(CC(a,b),a):f}if(f.length===0)return Au(d,"pfno"),[];b=f;a=e.Md?DC(a,b,c):{ub:b,Nd:null};const {ub:h,Nd:k}=a;f=h;return f.length===0&&k?(Au(d,k),[]):[f[e.Wk?0:e.Uk?Math.floor(f.length/4):Math.floor(f.length/2)]]} 
function DC(a,b,c){c=c?zh(c,Jt,5,eh()):[];const d=wC(a.document,c),e=xC();b=b.filter(f=>d(f));if(b.length===0)return{ub:[],Nd:"pfaz"};b=b.filter(f=>e(f));return b.length===0?{ub:[],Nd:"pfet"}:{ub:b,Nd:null}}function EC(a,b){return a.pa.g-b.pa.g}function BC(a,b){const c=v(b,Vt,6);if(!c)return[];b=v(b,Rt,28)?.i();return(b?.g()?Jy(c.g(),a):Iy(c.g(),a,!!b?.j())).map(d=>d.j())}function CC(a,b){b=zh(b,Zt,1,eh())||[];return jy(b,a,{}).filter(c=>!c.ed.includes(6))} 
function AC(a,b){a=HA(a,b);const c=mC(b);a=a.filter(d=>c(d));return a.sort(EC)};var FC={},GC={},HC={},IC={},JC={};function KC(){throw Error("Do not instantiate directly");}KC.prototype.Qg=null;KC.prototype.Xc=function(){return this.content};KC.prototype.toString=function(){return this.content};function LC(a){if(a.Rg!==FC)throw Error("Sanitized content was not of kind HTML.");return yc(a.toString())}function MC(){KC.call(this)}Ba(MC,KC);MC.prototype.Rg=FC;function NC(a){if(a!=null)switch(a.Qg){case 1:return 1;case -1:return-1;case 0:return 0}return null}function OC(a){return PC(a,FC)?a:a instanceof xc?QC(zc(a).toString()):QC(String(String(a)).replace(RC,SC),NC(a))}var QC=function(a){function b(c){this.content=c}b.prototype=a.prototype;return function(c,d){c=new b(String(c));d!==void 0&&(c.Qg=d);return c}}(MC);function TC(a){return UC(String(a),()=>"").replace(VC,"&lt;")} 
const WC=RegExp.prototype.hasOwnProperty("sticky"),XC=new RegExp((WC?"":"^")+"(?:!|/?([a-zA-Z][a-zA-Z0-9:-]*))",WC?"gy":"g"); 
function UC(a,b){const c=[],d=a.length;let e=0,f=[],g,h,k=0;for(;k<d;){switch(e){case 0:var l=a.indexOf("<",k);if(l<0){if(c.length===0)return a;c.push(a.substring(k));k=d}else c.push(a.substring(k,l)),h=l,k=l+1,WC?(XC.lastIndex=k,l=XC.exec(a)):(XC.lastIndex=0,l=XC.exec(a.substring(k))),l?(f=["<",l[0]],g=l[1],e=1,k+=l[0].length):c.push("<");break;case 1:l=a.charAt(k++);switch(l){case "'":case '"':let m=a.indexOf(l,k);m<0?k=d:(f.push(l,a.substring(k,m+1)),k=m+1);break;case ">":f.push(l);c.push(b(f.join(""), 
g));e=0;f=[];h=g=null;break;default:f.push(l)}break;default:throw Error();}e===1&&k>=d&&(k=h+1,c.push("<"),e=0,f=[],h=g=null)}return c.join("")}function YC(a,b){a=a.replace(/<\//g,"<\\/").replace(/\]\]>/g,"]]\\>");return b?a.replace(/{/g," \\{").replace(/}/g," \\}").replace(/\/\*/g,"/ *").replace(/\\$/,"\\ "):a}function Y(a){return PC(a,FC)?String(TC(a.Xc())).replace(ZC,SC):String(a).replace(RC,SC)} 
function $C(a){a=String(a);const b=(d,e,f)=>{const g=Math.min(e.length-f,d.length);for(let k=0;k<g;k++){var h=e[f+k];if(d[k]!==("A"<=h&&h<="Z"?h.toLowerCase():h))return!1}return!0};for(var c=0;(c=a.indexOf("<",c))!=-1;){if(b("\x3c/script",a,c)||b("\x3c!--",a,c))return"zSoyz";c+=1}return a}function aD(a){if(a==null)return" null ";if(PC(a,GC))return a.Xc();switch(typeof a){case "boolean":case "number":return" "+a+" ";default:return"'"+bD(String(a))+"'"}}const cD=/['()]/g; 
function dD(a){return"%"+a.charCodeAt(0).toString(16)}function eD(a){a=encodeURIComponent(String(a));cD.lastIndex=0;return cD.test(a)?a.replace(cD,dD):a}function fD(a){PC(a,HC)||PC(a,IC)?a=gD(a):kc(a)?a=gD(lc(a)):a instanceof fc?a=gD(hc(a).toString()):(a=String(a),a=hD.test(a)?a.replace(iD,jD):"about:invalid#zSoyz");return a}function Z(a){return PC(a,JC)?YC(a.Xc(),!1):a==null?"":a instanceof Gc?YC(Hc(a),!1):YC(String(a),!0)}function PC(a,b){return a!=null&&a.Rg===b} 
const kD={"\x00":"&#0;","\t":"&#9;","\n":"&#10;","\v":"&#11;","\f":"&#12;","\r":"&#13;"," ":"&#32;",'"':"&quot;","&":"&amp;","'":"&#39;","-":"&#45;","/":"&#47;","<":"&lt;","=":"&#61;",">":"&gt;","`":"&#96;","\u0085":"&#133;","\u00a0":"&#160;","\u2028":"&#8232;","\u2029":"&#8233;"};function SC(a){return kD[a]} 
const lD={"\x00":"\\x00","\b":"\\x08","\t":"\\t","\n":"\\n","\v":"\\x0b","\f":"\\f","\r":"\\r",'"':"\\x22",$:"\\x24","&":"\\x26","'":"\\x27","(":"\\x28",")":"\\x29","*":"\\x2a","+":"\\x2b",",":"\\x2c","-":"\\x2d",".":"\\x2e","/":"\\/",":":"\\x3a","<":"\\x3c","=":"\\x3d",">":"\\x3e","?":"\\x3f","[":"\\x5b","\\":"\\\\","]":"\\x5d","^":"\\x5e","{":"\\x7b","|":"\\x7c","}":"\\x7d","\u0085":"\\x85","\u2028":"\\u2028","\u2029":"\\u2029"};function mD(a){return lD[a]} 
const nD={"\x00":"%00","\u0001":"%01","\u0002":"%02","\u0003":"%03","\u0004":"%04","\u0005":"%05","\u0006":"%06","\u0007":"%07","\b":"%08","\t":"%09","\n":"%0A","\v":"%0B","\f":"%0C","\r":"%0D","\u000e":"%0E","\u000f":"%0F","\u0010":"%10","\u0011":"%11","\u0012":"%12","\u0013":"%13","\u0014":"%14","\u0015":"%15","\u0016":"%16","\u0017":"%17","\u0018":"%18","\u0019":"%19","\u001a":"%1A","\u001b":"%1B","\u001c":"%1C","\u001d":"%1D","\u001e":"%1E","\u001f":"%1F"," ":"%20",'"':"%22","'":"%27","(":"%28", 
")":"%29","<":"%3C",">":"%3E","\\":"%5C","{":"%7B","}":"%7D","\u007f":"%7F","\u0085":"%C2%85","\u00a0":"%C2%A0","\u2028":"%E2%80%A8","\u2029":"%E2%80%A9","\uff01":"%EF%BC%81","\uff03":"%EF%BC%83","\uff04":"%EF%BC%84","\uff06":"%EF%BC%86","\uff07":"%EF%BC%87","\uff08":"%EF%BC%88","\uff09":"%EF%BC%89","\uff0a":"%EF%BC%8A","\uff0b":"%EF%BC%8B","\uff0c":"%EF%BC%8C","\uff0f":"%EF%BC%8F","\uff1a":"%EF%BC%9A","\uff1b":"%EF%BC%9B","\uff1d":"%EF%BC%9D","\uff1f":"%EF%BC%9F","\uff20":"%EF%BC%A0","\uff3b":"%EF%BC%BB", 
"\uff3d":"%EF%BC%BD"};function jD(a){return nD[a]} 
const RC=/[\x00\x22\x26\x27\x3c\x3e]/g,ZC=/[\x00\x22\x27\x3c\x3e]/g,oD=/[\x00\x08-\x0d\x22\x26\x27\/\x3c-\x3e\x5b-\x5d\x7b\x7d\x85\u2028\u2029]/g,iD=/[\x00- \x22\x27-\x29\x3c\x3e\\\x7b\x7d\x7f\x85\xa0\u2028\u2029\uff01\uff03\uff04\uff06-\uff0c\uff0f\uff1a\uff1b\uff1d\uff1f\uff20\uff3b\uff3d]/g,hD=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i,pD=/^[^&:\/?#]*(?:[\/?#]|$)|^https?:|^ftp:|^data:image\/[a-z0-9+-]+;base64,[a-z0-9+\/]+=*$|^blob:/i,qD=/^[a-zA-Z0-9+\/_-]+={0,2}$/; 
function bD(a){return String(a).replace(oD,mD)}function gD(a){return String(a).replace(iD,jD)}function rD(a){a=String(a);return qD.test(a)?a:"zSoyz"}const VC=/</g;/* 
 
 
 Copyright Mathias Bynens <http://mathiasbynens.be/> 
 
 Permission is hereby granted, free of charge, to any person obtaining 
 a copy of this software and associated documentation files (the 
 "Software"), to deal in the Software without restriction, including 
 without limitation the rights to use, copy, modify, merge, publish, 
 distribute, sublicense, and/or sell copies of the Software, and to 
 permit persons to whom the Software is furnished to do so, subject to 
 the following conditions: 
 
 The above copyright notice and this permission notice shall be 
 included in all copies or substantial portions of the Software. 
 
 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, 
 EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF 
 MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND 
 NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE 
 LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION 
 OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION 
 WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE. 
*/ 
const sD=Math.floor;var tD=/^xn--/,uD=/[\x2E\u3002\uFF0E\uFF61]/g;const vD={Dn:"Overflow: input needs wider integers to process",An:"Illegal input >= 0x80 (not a basic code point)",kn:"Invalid input"};function wD(a){throw RangeError(vD[a]);}function xD(a,b){const c=a.split("@");let d="";c.length>1&&(d=c[0]+"@",a=c[1]);a=a.replace(uD,".");a=a.split(".").map(b).join(".");return d+a} 
function yD(a){return xD(a,b=>{if(tD.test(b)&&b.length>4){b=b.slice(4).toLowerCase();const h=[],k=b.length;let l=0,m=128;var c=72,d=b.lastIndexOf("-");d<0&&(d=0);for(var e=0;e<d;++e)b.charCodeAt(e)>=128&&wD("Illegal input >= 0x80 (not a basic code point)"),h.push(b.charCodeAt(e));for(d=d>0?d+1:0;d<k;){e=l;for(let n=1,p=36;;p+=36){d>=k&&wD("Invalid input");var f=b.charCodeAt(d++);f=f-48<10?f-22:f-65<26?f-65:f-97<26?f-97:36;(f>=36||f>sD((2147483647-l)/n))&&wD("Overflow: input needs wider integers to process"); 
l+=f*n;var g=p<=c?1:p>=c+26?26:p-c;if(f<g)break;f=36-g;n>sD(2147483647/f)&&wD("Overflow: input needs wider integers to process");n*=f}f=h.length+1;c=l-e;g=0;c=e==0?sD(c/700):c>>1;for(c+=sD(c/f);c>455;g+=36)c=sD(c/35);c=sD(g+36*c/(c+38));sD(l/f)>2147483647-m&&wD("Overflow: input needs wider integers to process");m+=sD(l/f);l%=f;h.splice(l++,0,m)}b=String.fromCodePoint.apply(null,h)}return b})};function zD(a,b,c){var d=a.Ba.contentWindow;a.Va?(b={action:"search",searchTerm:b,rsToken:c},b.experimentId=a.Ta,a.postMessage(d,b)):(d=d.google.search.cse.element.getElement(a.Vb),c={rsToken:c,hostName:a.host},a.Ua||typeof a.Ta!=="number"||(c.afsExperimentId=a.Ta),d.execute(b,void 0,c))} 
var AD=class{constructor(a){this.Ba=a.Ba;this.La=a.La;this.Vb=a.Vb;this.Wa=a.Wa;this.xd=a.xd;this.host=a.location.host;this.origin=a.location.origin;this.language=a.language;this.jc=a.jc;this.Ta=a.Ta;this.md=a.md||!1;this.Va=a.Va;this.Hb=a.Hb;this.g=a.rg||!1;this.Ua=a.Ua||!1;this.sg=!!a.sg}postMessage(a,b){a?.postMessage(b,"https://www.gstatic.com")}M(){this.Ba.setAttribute("id","prose-iframe");this.Ba.setAttribute("width","100%");this.Ba.setAttribute("height","100%");this.Ba.style.cssText="box-sizing:border-box;border:unset;"; 
var a="https://www.google.com/s2/favicons?sz=64&domain_url="+encodeURIComponent(this.host);var b=pc(a,oc)||jc;var c=yD(this.host.startsWith("www.")?this.host.slice(4):this.host),d={};a=this.Vb;var e=this.Wa,f=this.xd;const g=this.host;c=this.jc.replace("${website}",c);var h=this.md;const k=this.g,l=this.sg,m=d&&d.Wb,n=d&&d.Ug;d=QC;h="<style"+(m?' nonce="'+Y(rD(m))+'"':"")+">.cse-favicon {display: block; float: left; height: 16px; position: absolute; left: 15px; width: 16px;}.cse-header {font-size: 16px; font-family: Arial; margin-left: 35px; margin-top: 6px; margin-bottom: unset; line-height: 16px;}.gsc-search-box {max-width: 520px !important;}.gsc-input {padding-right: 0 !important;}.gsc-input-box {border-radius: 16px 0 0 16px !important;}.gsc-search-button-v2 {border-left: 0 !important; border-radius: 0 16px 16px 0 !important; min-height: 30px !important; margin-left: 0 !important;}.gsc-cursor-page, .gsc-cursor-next-page, .gsc-cursor-numbered-page {color: #1a73e8 !important;}.gsc-cursor-chevron {fill: #1a73e8 !important;}.gsc-cursor-box {text-align: center !important;}.gsc-cursor-current-page {color: #000 !important;}.gcsc-find-more-on-google-root, .gcsc-find-more-on-google {display: none !important;}.prose-container {max-width: 652px;}#prose-empty-serp-container {display: flex; flex-direction: column; align-items: center; padding: 0; gap: 52px; position: relative; width: 248px; height: 259px; margin: auto; top: 100px;}#prose-empty-serp-icon-image {display: flex; flex-direction: row; justify-content: center; align-items: center; padding: 30px; gap: 10px; width: 124px; height: 124px; border-radius: 62px; flex: none; order: 1; flex-grow: 0; position: absolute; top: 0;}#prose-empty-serp-text-container {display: flex; flex-direction: column; align-items: center; padding: 0; gap: 19px; width: 248px; height: 83px; flex: none; order: 2; align-self: stretch; flex-grow: 0; position: absolute; top: 208px;}#prose-empty-serp-text-div {display: flex; flex-direction: column; align-items: flex-start; padding: 0; gap: 11px; width: 248px; height: 83px; flex: none; order: 0; align-self: stretch; flex-grow: 0;}#prose-empty-serp-supporting-text {width: 248px; height: 40px; font-family: 'Arial'; font-style: normal; font-weight: 400; font-size: 14px; line-height: 20px; text-align: center; letter-spacing: 0.2px; color: #202124; flex: none; order: 1; align-self: stretch; flex-grow: 0;}</style>"+ 
(h?"<script"+(n?' nonce="'+Y(rD(n))+'"':"")+'>window.__gcse={initializationCallback:function(){top.postMessage({action:"init",adChannel:"'+bD(f)+'"},top.location.origin);}};\x3c/script>':"")+'<div class="prose-container"><img class="cse-favicon" src="';PC(b,HC)||PC(b,IC)?b=gD(b):kc(b)?b=gD(lc(b)):b instanceof fc?b=gD(hc(b).toString()):(b=String(b),b=pD.test(b)?b.replace(iD,jD):"about:invalid#zSoyz");a=d(h+Y(b)+'" alt="'+Y(g)+' icon"><p class="cse-header"><strong>'+OC(c)+"</strong></p>"+(l?'<div class="gcse-searchresults-only" data-gname="'+ 
Y(a)+'" data-adclient="'+Y(e)+'" data-adchannel="'+Y(f)+'" data-as_sitesearch="'+Y(g)+'" data-personalizedAds="false" data-disableAds="true"></div>':'<div class="gcse-search" data-gname="'+Y(a)+'" data-adclient="'+Y(e)+'" data-adchannel="'+Y(f)+'" data-as_sitesearch="'+Y(g)+'" data-personalizedAds="false"></div>')+"</div>"+(k?"<div id=\"prose-empty-serp-container\"><img id='prose-empty-serp-icon-image' src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI0IiBoZWlnaHQ9IjEyNCIgdmlld0JveD0iMCAwIDEyNCAxMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjQiIGhlaWdodD0iMTI0IiByeD0iNjIiIGZpbGw9IiNGMUYzRjQiLz4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik02OS4zNiA2NS4zODY3TDg0LjY0IDgwLjY2NjdMODAuNjY2NyA4NC42NEw2NS4zODY3IDY5LjM2QzYyLjUzMzMgNzEuNDEzMyA1OS4wOTMzIDcyLjY2NjcgNTUuMzMzMyA3Mi42NjY3QzQ1Ljc2IDcyLjY2NjcgMzggNjQuOTA2NyAzOCA1NS4zMzMzQzM4IDQ1Ljc2IDQ1Ljc2IDM4IDU1LjMzMzMgMzhDNjQuOTA2NyAzOCA3Mi42NjY3IDQ1Ljc2IDcyLjY2NjcgNTUuMzMzM0M3Mi42NjY3IDU5LjA5MzMgNzEuNDEzMyA2Mi41MzMzIDY5LjM2IDY1LjM4NjdaTTU1LjMzMzMgNDMuMzMzM0M0OC42OTMzIDQzLjMzMzMgNDMuMzMzMyA0OC42OTMzIDQzLjMzMzMgNTUuMzMzM0M0My4zMzMzIDYxLjk3MzMgNDguNjkzMyA2Ny4zMzMzIDU1LjMzMzMgNjcuMzMzM0M2MS45NzMzIDY3LjMzMzMgNjcuMzMzMyA2MS45NzMzIDY3LjMzMzMgNTUuMzMzM0M2Ny4zMzMzIDQ4LjY5MzMgNjEuOTczMyA0My4zMzMzIDU1LjMzMzMgNDMuMzMzM1oiIGZpbGw9IiM5QUEwQTYiLz4KPC9zdmc+Cg==' alt=''><div id='prose-empty-serp-text-container'><div id='prose-empty-serp-text-div'><div id='prose-empty-serp-supporting-text'>Search this website by entering a keyword.</div></div></div></div>": 
""));a=LC(a);this.Va?(a=this.Ba,e=hd`https://www.gstatic.com/prose/protected/${this.Hb||"558153351"}/iframe.html?cx=${this.La}&host=${this.host}&hl=${this.language}&lrh=${this.jc}&client=${this.Wa}&origin=${this.origin}`,a.src=hc(e).toString()):(e=new Map([["cx",this.La],["language",this.language]]),this.Ua&&(f=Array.isArray(this.Ta)?this.Ta:[this.Ta],f.length&&e.set("fexp",f.join())),e=id(hd`https://cse.google.com/cse.js`,e),e=hc(e).toString(),e=yc(`<script src="${Zc(e)}"`+">\x3c/script>"),a=fd("body", 
{style:"margin:0;"},[a,e]),this.Ba.srcdoc=zc(a))}};function BD(a){a.google_reactive_ads_global_state?(a.google_reactive_ads_global_state.sideRailProcessedFixedElements==null&&(a.google_reactive_ads_global_state.sideRailProcessedFixedElements=new Set),a.google_reactive_ads_global_state.sideRailAvailableSpace==null&&(a.google_reactive_ads_global_state.sideRailAvailableSpace=new Map),a.google_reactive_ads_global_state.sideRailPlasParam==null&&(a.google_reactive_ads_global_state.sideRailPlasParam=new Map),a.google_reactive_ads_global_state.sideRailMutationCallbacks== 
null&&(a.google_reactive_ads_global_state.sideRailMutationCallbacks=[])):a.google_reactive_ads_global_state=new CD;return a.google_reactive_ads_global_state} 
var CD=class{constructor(){this.wasPlaTagProcessed=!1;this.wasReactiveAdConfigReceived={};this.adCount={};this.wasReactiveAdVisible={};this.stateForType={};this.reactiveTypeEnabledInAsfe={};this.wasReactiveTagRequestSent=!1;this.reactiveTypeDisabledByPublisher={};this.tagSpecificState={};this.messageValidationEnabled=!1;this.floatingAdsStacking=new DD;this.sideRailProcessedFixedElements=new Set;this.sideRailAvailableSpace=new Map;this.sideRailPlasParam=new Map;this.sideRailMutationCallbacks=[];this.g= 
null;this.clickTriggeredInterstitialMayBeDisplayed=!1}},DD=class{constructor(){this.maxZIndexRestrictions={};this.nextRestrictionId=0;this.maxZIndexListeners=[]}};function ED(a,b){return new FD(a,b)}function GD(a){const b=HD(a);Ga(a.floatingAdsStacking.maxZIndexListeners,c=>c(b))}function HD(a){a=wd(a.floatingAdsStacking.maxZIndexRestrictions);return a.length?Math.min.apply(null,a):null}function ID(a,b){Sa(a.floatingAdsStacking.maxZIndexListeners,c=>c===b)}var JD=class{constructor(a){this.floatingAdsStacking=BD(a).floatingAdsStacking}}; 
function KD(a){if(a.g==null){var b=a.controller,c=a.Db;const d=b.floatingAdsStacking.nextRestrictionId++;b.floatingAdsStacking.maxZIndexRestrictions[d]=c;GD(b);a.g=d}}function LD(a){if(a.g!=null){var b=a.controller;delete b.floatingAdsStacking.maxZIndexRestrictions[a.g];GD(b);a.g=null}}var FD=class{constructor(a,b){this.controller=a;this.Db=b;this.g=null}};function MD(a){a=a.activeElement;const b=a?.shadowRoot;return b?MD(b)||a:a}function ND(a,b){return OD(b,a.document.body).flatMap(c=>PD(c))}function OD(a,b){var c=a;for(a=[];c&&c!==b;){a.push(c);let e;var d;(d=c.parentElement)||(c=c.getRootNode(),d=((e=c.mode&&c.host?c:null)==null?void 0:e.host)||null);c=d}return c!==b?[]:a}function PD(a){const b=a.parentElement;return b?Array.from(b.children).filter(c=>c!==a):[]};function QD(a){a.g!==null&&(a.g.ij.forEach(b=>{b.inert=!1}),a.g.tk?.focus(),a.g=null)}function RD(a,b){QD(a);const c=MD(a.A.document);b=ND(a.A,b).filter(d=>!d.inert);b.forEach(d=>{d.inert=!0});a.g={tk:c,ij:b}}var SD=class{constructor(a){this.A=a;this.g=null}Ge(){QD(this)}};function TD(a){return new UD(a,new Pr(a,a.document.body),new Pr(a,a.document.documentElement),new Pr(a,a.document.documentElement))} 
function VD(a){Or(a.j,"scroll-behavior","auto");var b=WD(a.A);b.activePageScrollPreventers.add(a);b.previousWindowScroll===null&&(b.previousWindowScroll=a.A.scrollY);Or(a.g,"position","fixed");Or(a.g,"top",`${-b.previousWindowScroll}px`);Or(a.g,"width","100%");Or(a.g,"overflow-x","hidden");Or(a.g,"overflow-y","hidden");a.dontOverrideDocumentOverflowUnlessNeeded()?(b=getComputedStyle(a.A.document.documentElement),XD(b.overflowX)&&Or(a.i,"overflow-x","unset"),XD(b.overflowY)&&Or(a.i,"overflow-y","unset")): 
(Or(a.i,"overflow-x","hidden"),Or(a.i,"overflow-y","hidden"))}function XD(a){return a==="scroll"||a==="auto"}function YD(a){Nr(a.g);Nr(a.i);const b=WD(a.A);b.activePageScrollPreventers.delete(a);b.activePageScrollPreventers.size===0&&(a.A.scrollTo(0,b.previousWindowScroll||0),b.previousWindowScroll=null);Nr(a.j)}var UD=class{constructor(a,b,c,d){this.A=a;this.g=b;this.i=c;this.j=d}dontOverrideDocumentOverflowUnlessNeeded(){return WD(this.A).dontOverrideDocumentOverflowUnlessNeeded}}; 
function WD(a){return a.googPageScrollPreventerInfo=a.googPageScrollPreventerInfo||{previousWindowScroll:null,activePageScrollPreventers:new Set,dontOverrideDocumentOverflowUnlessNeeded:!1}}function ZD(a){return a.googPageScrollPreventerInfo&&a.googPageScrollPreventerInfo.activePageScrollPreventers.size>0?!0:!1};function $D(a,b){return aE(`#${a}`,b)}function bE(a,b){return aE(`.${a}`,b)}function aE(a,b){b=b.querySelector(a);if(!b)throw Error(`Element (${a}) does not exist`);return b};function cE(a,b){const c=a.document.createElement("div");Du(a,c);a=c.attachShadow({mode:"open"});b&&c.classList.add(b);return{pb:c,shadowRoot:a}};function dE(a,b){b=cE(a,b);a.document.body.appendChild(b.pb);return b}function eE(a,b){const c=new O(b.R);Yr(b,!0,()=>void c.g(!0));Yr(b,!1,()=>{a.setTimeout(()=>{b.R||c.g(!1)},700)});return Tr(c)};function fE(a){var b={},c=a.Vc,d=a.lg,e=a.Tc;const f=a.Ec,g=a.Lg,h=a.zIndex,k=a.Ve;a=a.Ma;b=b&&b.Wb;c="<style"+(b?' nonce="'+Y(rD(b))+'"':"")+">#hd-drawer-container {position: fixed; left: 0; top: 0; width: 100vw; height: 100%; overflow: hidden; z-index: "+Z(h)+"; pointer-events: none;}#hd-drawer-container.hd-revealed {pointer-events: auto;}#hd-modal-background {position: absolute; left: 0; bottom: 0; background-color: black; transition: opacity .5s ease-in-out; width: 100%; height: 100%; opacity: 0;}.hd-revealed > #hd-modal-background {opacity: 0.5;}#hd-drawer {position: absolute; top: 0; height: 100%; width: "+ 
Z(c)+"; background-color: white; display: flex; flex-direction: column; box-sizing: border-box; padding-bottom: ";d=d?a?20:16:0;c+=Z(d)+"px; transition: transform "+Z(k)+"s ease-in-out;"+(e?"left: 0; border-top-right-radius: "+Z(d)+"px; border-bottom-right-radius: "+Z(d)+"px; transform: translateX(-100%);":"right: 0; border-top-left-radius: "+Z(d)+"px; border-bottom-left-radius: "+Z(d)+"px; transform: translateX(100%);")+"}.hd-revealed > #hd-drawer {transform: translateY(0);}#hd-control-bar {"+(a? 
"height: 24px;":"padding: 5px;")+"}.hd-control-button {border: none; background: none; cursor: pointer;"+(a?"":"padding: 5px;")+"}#hd-back-arrow-button {"+(e?"float: right;":"float: left;")+"}#hd-close-button {"+(e?"float: left;":"float: right;")+'}#hd-content-container {flex-grow: 1; overflow: auto;}#hd-content-container::-webkit-scrollbar * {background: transparent;}.hd-hidden {visibility: hidden;}</style><div id="hd-drawer-container" class="hd-hidden" aria-modal="true" role="dialog" tabindex="0"><div id="hd-modal-background"></div><div id="hd-drawer"><div id="hd-control-bar"><button id="hd-back-arrow-button" class="hd-control-button hd-hidden" aria-label="'+ 
Y(g)+'">';e=a?"#5F6368":"#444746";c+='<svg xmlns="http://www.w3.org/2000/svg" height="24" width="24" fill="'+Y(e)+'"><path d="m12 20-8-8 8-8 1.425 1.4-5.6 5.6H20v2H7.825l5.6 5.6Z"/></svg></button><button id="hd-close-button" class="hd-control-button" aria-label="'+Y(f)+'"><svg xmlns="http://www.w3.org/2000/svg" height="24" width="24" fill="'+Y(e)+'"><path d="M6.4 19 5 17.6 10.6 12 5 6.4 6.4 5 12 10.6 17.6 5 19 6.4 13.4 12 19 17.6 17.6 19 12 13.4Z"/></svg></button></div><div id="hd-content-container"></div></div></div>'; 
return QC(c)};function gE(a){a=a.top;if(!a)return null;try{var b=a.history}catch(c){b=null}b=b&&b.pushState&&typeof b.pushState==="function"?b:null;if(!b)return null;if(a.googNavStack)return a.googNavStack;b=new hE(a,b);b.M();return b?a.googNavStack=b:null}function iE(a,b){return b?b.googNavStackId===a.g?b:null:null}function jE(a,b){for(let c=b.length-1;c>=0;--c){const d=c===0;a.L.requestAnimationFrame(()=>void b[c].Fk({isFinal:d}))}} 
function kE(a,b){b=Za(a.stack,b,(c,d)=>c-d.ih.googNavStackStateId);if(b>=0)return a.stack.splice(b,a.stack.length-b);b=-b-1;return a.stack.splice(b,a.stack.length-b)} 
class hE extends N{constructor(a,b){super();this.L=a;this.history=b;this.stack=[];this.g=Math.random()*1E9>>>0;this.l=0;this.j=c=>{(c=iE(this,c.state))?jE(this,kE(this,c.googNavStackStateId+.5)):jE(this,this.stack.splice(0,this.stack.length))}}pushEvent(){const a={googNavStackId:this.g,googNavStackStateId:this.l++},b=new Promise(c=>{this.stack.push({Fk:c,ih:a})});this.history.pushState(a,"");return{navigatedBack:b,triggerNavigateBack:()=>{const c=kE(this,a.googNavStackStateId);var d;if(d=c.length> 
0){d=c[0].ih;const e=iE(this,this.history.state);d=e&&e.googNavStackId===d.googNavStackId&&e.googNavStackStateId===d.googNavStackStateId}d&&this.history.go(-c.length);jE(this,c)}}}M(){this.L.addEventListener("popstate",this.j)}i(){this.L.removeEventListener("popstate",this.j);super.i()}};function lE(a){return(a=gE(a))?new mE(a):null}function nE(a){if(!a.g){var {navigatedBack:b,triggerNavigateBack:c}=a.l.pushEvent();a.g=c;b.then(()=>{a.g&&!a.C&&(a.g=null,ds(a.j))})}}var mE=class extends N{constructor(a){super();this.l=a;this.j=new es;this.g=null}};function oE(a,b,c){var d=c.Jc?null:new SD(a);const e=ED(new JD(a),c.zIndex-1);b=pE(a,b,c);d=new qE(a,b,d,c.Uc,TD(a),e);d.M();(c.uf||c.uf===void 0)&&rE(d);c.Ab&&((a=lE(a))?sE(d,a,c.Wf):c.Wf?.(Error("Unable to create closeNavigator")));return d}function rE(a){a.B=b=>{b.key==="Escape"&&a.g.R&&a.collapse()};a.A.document.body.addEventListener("keydown",a.B)} 
function sE(a,b,c){Yr(a.g,!0,()=>{try{nE(b)}catch(d){c?.(d)}});Yr(a.g,!1,()=>{try{b.g&&(b.g(),b.g=null)}catch(d){c?.(d)}});bs(b.j).listen(()=>void a.collapse());Lr(a,b)}function tE(a){if(a.C)throw Error("Accessing domItems after disposal");return a.F}function uE(a){a.A.setTimeout(()=>{a.g.R&&tE(a).Na.focus()},500)}function vE(a){const {Uf:b,Pi:c}=tE(a);b.addEventListener("click",()=>void a.collapse());c.addEventListener("click",()=>void a.collapse())} 
function wE(a){Yr(a.j,!1,()=>{tE(a).Na.classList.add("hd-hidden")})} 
var qE=class extends N{constructor(a,b,c,d=!0,e,f){super();this.A=a;this.F=b;this.l=c;this.Uc=d;this.g=new O(!1);this.j=eE(a,this.g);Yr(this.j,!0,()=>{VD(e);KD(f)});Yr(this.j,!1,()=>{YD(e);LD(f)})}show({tf:a=!1}={}){if(this.C)throw Error("Cannot show drawer after disposal");tE(this).Na.classList.remove("hd-hidden");Jr(this.A);tE(this).Na.classList.add("hd-revealed");this.g.g(!0);this.l&&(RD(this.l,tE(this).qb.pb),this.Uc&&uE(this));a&&Yr(this.j,!1,()=>{this.dispose()})}collapse(){tE(this).Na.classList.remove("hd-revealed"); 
this.g.g(!1);this.l?.Ge()}isVisible(){return this.j}M(){vE(this);wE(this)}i(){this.B&&this.A.document.body.removeEventListener("keydown",this.B);const a=this.F.qb.pb,b=a.parentNode;b&&b.removeChild(a);this.l?.Ge();super.i()}}; 
function pE(a,b,c){const d=dE(a,c.Kc),e=d.shadowRoot;e.appendChild(Ql(new Dl(a.document),LC(fE({Vc:c.Vc,lg:c.lg??!0,Tc:c.Tc||!1,Ec:c.Ec,Lg:c.Lg||"",zIndex:c.zIndex,Ve:.5,Ma:c.Ma||!1}))));const f=$D("hd-drawer-container",e);c.Oc?.i(g=>{f.setAttribute("aria-label",g)});c=$D("hd-content-container",e);c.appendChild(b);Jr(a);return{Na:f,Uf:$D("hd-modal-background",e),hf:c,Pi:$D("hd-close-button",e),Jo:$D("hd-back-arrow-button",e),qb:d}};function xE(a){var b={};const c=a.nk,d=a.uj;var e=a.zIndex,f=a.Ve;a=a.Ma;b=b&&b.Wb;e="<style"+(b?' nonce="'+Y(rD(b))+'"':"")+">#ved-drawer-container {position:  fixed; left: 0; top: 0; width: 100vw; height: 100%; overflow: hidden; z-index: "+Z(e)+"; pointer-events: none;}#ved-drawer-container.ved-revealed {pointer-events: auto;}#ved-modal-background {position: absolute; left: 0; bottom: 0; background-color: black; transition: opacity .5s ease-in-out; width: 100%; height: 100%; opacity: 0;}.ved-revealed > #ved-modal-background {opacity: 0.5;}#ved-ui-revealer {position: absolute; left: 0; bottom: 0; width: 100%; height: "+ 
Z(d)+"%; transition: transform "+Z(f)+"s ease-in-out; transform: translateY(100%);}#ved-ui-revealer.ved-no-animation {transition-property: none;}.ved-revealed > #ved-ui-revealer {transform: translateY(0);}#ved-scroller-container {position: absolute; left: 0; bottom: 0; width: 100%; height: 100%; clip-path: inset(0 0 -50px 0 round ";f=a?20:28;e+=Z(f)+"px);}#ved-scroller {position: relative; width: 100%; height: 100%; overflow-y: scroll; -ms-overflow-style: none; scrollbar-width: none; overflow-y: scroll; overscroll-behavior: none; scroll-snap-type: y mandatory;}#ved-scroller.ved-scrolling-paused {overflow: hidden;}#ved-scroller.ved-no-snap {scroll-snap-type: none;}#ved-scroller::-webkit-scrollbar {display: none;}#ved-scrolled-stack {width: 100%; height: 100%; overflow: visible;}#ved-scrolled-stack.ved-with-background {background-color: white;}.ved-snap-point-top {scroll-snap-align: start;}.ved-snap-point-bottom {scroll-snap-align: end;}#ved-fully-closed-anchor {height: "+ 
Z(c/d*100)+"%;}.ved-with-background #ved-fully-closed-anchor {background-color: white;}#ved-partially-extended-anchor {height: "+Z((d-c)/d*100)+"%;}.ved-with-background #ved-partially-extended-anchor {background-color: white;}#ved-moving-handle-holder {scroll-snap-stop: always;}.ved-with-background #ved-moving-handle-holder {background-color: white;}#ved-fixed-handle-holder {position: absolute; left: 0; top: 0; width: 100%;}#ved-visible-scrolled-items {display: flex; flex-direction: column; min-height: "+ 
Z(c/d*100)+"%;}#ved-content-background {width: 100%; flex-grow: 1; padding-top: 1px; margin-top: -1px; background-color: white;}#ved-content-sizer {overflow: hidden; width: 100%; height: 100%;}#ved-content-container {width: 100%;}#ved-over-scroll-block {display: flex; flex-direction: column; position: absolute; bottom: 0; left: 0; width: 100%; height: "+Z(c/d*100)+"%; pointer-events: none;}#ved-over-scroll-handle-spacer {height: "+Z(80)+"px;}#ved-over-scroll-background {flex-grow: 1; background-color: white;}.ved-handle {align-items: flex-end; border-radius: "+ 
Z(f)+"px "+Z(f)+"px 0 0; background: white; display: flex; height: "+Z(30)+"px; justify-content: center; cursor: grab;}.ved-handle-icon {"+(a?"background: #dadce0; width: 50px;":"background: #747775; opacity: 0.4; width: 32px;")+'border-radius: 2px; height: 4px; margin-bottom: 8px;}.ved-hidden {visibility: hidden;}</style><div id="ved-drawer-container" class="ved-hidden" aria-modal="true" role="dialog" tabindex="0"><div id="ved-modal-background"></div><div id="ved-ui-revealer"><div id="ved-over-scroll-block" class="ved-hidden"><div id=\'ved-over-scroll-handle-spacer\'></div><div id=\'ved-over-scroll-background\'></div></div><div id="ved-scroller-container"><div id="ved-scroller"><div id="ved-scrolled-stack"><div id="ved-fully-closed-anchor" class="ved-snap-point-top"></div><div id="ved-partially-extended-anchor" class="ved-snap-point-top"></div><div id="ved-visible-scrolled-items"><div id="ved-moving-handle-holder" class="ved-snap-point-top">'+ 
yE("ved-moving-handle")+'</div><div id="ved-content-background"><div id="ved-content-sizer" class="ved-snap-point-bottom"><div id="ved-content-container"></div></div></div></div></div></div></div><div id="ved-fixed-handle-holder" class="ved-hidden">'+yE("ved-fixed-handle")+"</div></div></div>";return QC(e)}function yE(a){return QC('<div class="ved-handle" id="'+Y(a)+'"><div class="ved-handle-icon"></div></div>')};function zE(a){return ss(a.g).map(b=>b?AE(a,b):0)}function AE(a,b){switch(a.direction){case 0:return BE(-b.di);case 1:return BE(-b.ci);default:throw Error(`Unhandled direction: ${a.direction}`);}}function CE(a){return us(a.g).map(b=>AE(a,b))}var DE=class{constructor(a){this.g=a;this.direction=0}};function BE(a){return a===0?0:a};function EE(a){if(a.C)throw Error("Accessing domItems after disposal");return a.F}function FE(a){a.A.setTimeout(()=>{a.g.R&&EE(a).Na.focus()},500)}function GE(a){EE(a).Na.classList.remove("ved-hidden");Jr(a.A);const {ra:b,lb:c}=EE(a);c.getBoundingClientRect().top<=b.getBoundingClientRect().top||HE(a);EE(a).Na.classList.add("ved-revealed");a.g.g(!0);a.j&&(RD(a.j,EE(a).qb.pb),a.Uc&&FE(a))}function IE(a){return eE(a.A,a.g)} 
function JE(a,b){const c=new O(b());bs(a.K).listen(()=>void c.g(b()));return Tr(c)}function KE(a){const {ra:b,qe:c}=EE(a);return JE(a,()=>c.getBoundingClientRect().top<=b.getBoundingClientRect().top)}function LE(a){const {ra:b,qe:c}=EE(a);return JE(a,()=>c.getBoundingClientRect().top<=b.getBoundingClientRect().top-1)}function ME(a){const {ra:b}=EE(a);return JE(a,()=>b.scrollTop===b.scrollHeight-b.clientHeight)}function NE(a){return Ur(KE(a),ME(a))} 
function OE(a){const {ra:b,lb:c}=EE(a);return JE(a,()=>c.getBoundingClientRect().top<b.getBoundingClientRect().top-1)}function HE(a){EE(a).lb.classList.add("ved-snap-point-top");var b=PE(a,EE(a).lb);EE(a).ra.scrollTop=b;QE(a)}function RE(a){Wr(KE(a),!0,()=>{const {bh:b,vd:c}=EE(a);b.classList.remove("ved-hidden");c.classList.add("ved-with-background")});Wr(KE(a),!1,()=>{const {bh:b,vd:c}=EE(a);b.classList.add("ved-hidden");c.classList.remove("ved-with-background")})} 
function SE(a){const b=zs(a.A,EE(a).hf);Cs(b).i(()=>void TE(a));Lr(a,b)}function UE(a){Wr(VE(a),!0,()=>{EE(a).Dh.classList.remove("ved-hidden")});Wr(VE(a),!1,()=>{EE(a).Dh.classList.add("ved-hidden")})}function WE(a){const b=()=>void ds(a.G),{Uf:c,lb:d,tj:e}=EE(a);c.addEventListener("click",b);d.addEventListener("click",b);e.addEventListener("click",b);Yr(XE(a),!0,b)}function YE(a){Yr(IE(a),!1,()=>{HE(a);EE(a).Na.classList.add("ved-hidden")})}function QE(a){Xr(a.l,!1,()=>void ds(a.K))} 
function TE(a){if(!a.l.R){var {Sg:b,hf:c}=EE(a),d=c.getBoundingClientRect().height;d=Math.max(ZE(a),d);a.l.g(!0);var e=$E(a);b.style.setProperty("height",`${d}px`);e();a.A.requestAnimationFrame(()=>{a.A.requestAnimationFrame(()=>{a.l.g(!1)})})}}function VE(a){const {ra:b,lb:c}=EE(a);return JE(a,()=>c.getBoundingClientRect().top<=b.getBoundingClientRect().top)}function XE(a){return Sr(a.B.map(et),aF(a))}function aF(a){return JE(a,()=>EE(a).ra.scrollTop===0)} 
function PE(a,b){({vd:a}=EE(a));a=a.getBoundingClientRect().top;return b.getBoundingClientRect().top-a}function bF(a,b){a.B.g(!0);const {vd:c,ra:d}=EE(a);d.scrollTop=0;d.classList.add("ved-scrolling-paused");c.style.setProperty("margin-top",`-${b}px`);return()=>void cF(a,b)}function cF(a,b){const {vd:c,ra:d}=EE(a);c.style.removeProperty("margin-top");d.classList.remove("ved-scrolling-paused");EE(a).ra.scrollTop=b;QE(a);a.B.g(!1)} 
function $E(a){const b=EE(a).ra.scrollTop;bF(a,b);return()=>void cF(a,b)}function ZE(a){const {ra:b,qe:c,Sg:d,lb:e}=EE(a);a=b.getBoundingClientRect();const f=c.getBoundingClientRect();var g=d.getBoundingClientRect();const h=e.getBoundingClientRect();g=g.top-f.top;return Math.max(a.height-h.height-g,Math.min(a.height,a.bottom-f.top)-g)} 
var dF=class extends N{constructor(a,b,c,d,e=!0){super();this.A=a;this.F=b;this.O=c;this.j=d;this.Uc=e;this.G=new es;this.K=new es;this.g=new O(!1);this.B=new O(!1);this.l=new O(!1)}M(){HE(this);RE(this);SE(this);UE(this);WE(this);YE(this);EE(this).ra.addEventListener("scroll",()=>void QE(this))}i(){const a=this.F.qb.pb,b=a.parentNode;b&&b.removeChild(a);this.j?.Ge();super.i()}}; 
function eF(a,b,c){const d=dE(a,c.Kc),e=d.shadowRoot;e.appendChild(Ql(new Dl(a.document),LC(xE({nk:c.dg*100,uj:c.yf*100,zIndex:c.zIndex,Ve:.5,Ma:c.Ma||!1}))));const f=$D("ved-drawer-container",e);c.Oc?.i(g=>{f.setAttribute("aria-label",g)});c=$D("ved-content-container",e);c.appendChild(b);Jr(a);return{Na:f,Uf:$D("ved-modal-background",e),Xh:$D("ved-ui-revealer",e),ra:$D("ved-scroller",e),vd:$D("ved-scrolled-stack",e),tj:$D("ved-fully-closed-anchor",e),lb:$D("ved-partially-extended-anchor",e),Sg:$D("ved-content-sizer", 
e),hf:c,Ro:$D("ved-moving-handle",e),qe:$D("ved-moving-handle-holder",e),rj:$D("ved-fixed-handle",e),bh:$D("ved-fixed-handle-holder",e),Dh:$D("ved-over-scroll-block",e),qb:d}};function fF(a,b,c){var d=ED(new JD(a),c.zIndex-1);b=eF(a,b,c);const e=c.Jc?null:new SD(a);var f=b.rj;f=new vs(new ms(a,f),new js(f));var g=f.g;g.C.addEventListener("mousedown",g.F);g.l.addEventListener("mouseup",g.B);g.l.addEventListener("mousemove",g.H,{passive:!1});g=f.i;g.i.addEventListener("touchstart",g.H);g.i.addEventListener("touchend",g.C);g.i.addEventListener("touchmove",g.B,{passive:!1});b=new dF(a,b,new DE(f),e,c.Uc);b.M();d=new gF(a,b,TD(a),d);Lr(d,b);d.M();c.Ab&&((a=lE(a))?hF(d,a,c.Wf): 
c.Wf?.(Error("Unable to create closeNavigator")));return d}function hF(a,b,c){Yr(a.g.g,!0,()=>{try{nE(b)}catch(d){c?.(d)}});Yr(a.g.g,!1,()=>{try{b.g&&(b.g(),b.g=null)}catch(d){c?.(d)}});bs(b.j).listen(()=>void a.collapse());Lr(a,b)} 
function iF(a){Yr(Sr(NE(a.g),OE(a.g)),!0,()=>{EE(a.g).lb.classList.remove("ved-snap-point-top")});Wr(LE(a.g),!0,()=>{EE(a.g).ra.classList.add("ved-no-snap")});Wr(LE(a.g),!1,()=>{EE(a.g).ra.classList.remove("ved-no-snap")});Yr(LE(a.g),!1,()=>{var b=a.g;var c=EE(b).qe;c=bF(b,PE(b,c));b.A.setTimeout(c,100)})} 
function jF(a){const b=a.g.O;zE(b).listen(c=>{c=-c;if(c>0){const {Xh:d}=EE(a.g);d.classList.add("ved-no-animation");d.style.setProperty("transform",`translateY(${c}px)`)}else({Xh:c}=EE(a.g)),c.classList.remove("ved-no-animation"),c.style.removeProperty("transform")});CE(b).listen(c=>{-c>30&&a.collapse()})} 
var gF=class extends N{constructor(a,b,c,d){super();this.A=a;this.g=b;Yr(IE(b),!0,()=>{VD(c);KD(d)});Yr(IE(b),!1,()=>{YD(c);LD(d)})}show({tf:a=!1}={}){if(this.C)throw Error("Cannot show drawer after disposal");GE(this.g);a&&Yr(IE(this.g),!1,()=>{this.dispose()})}collapse(){var a=this.g;EE(a).Na.classList.remove("ved-revealed");a.g.g(!1);a.j?.Ge()}isVisible(){return IE(this.g)}M(){bs(this.g.G).listen(()=>{this.collapse()});iF(this);jF(this);Jr(this.A)}};function kF(a,b){return Nd()===2?fF(a.A,b,{dg:.95,yf:.95,zIndex:2147483645,Ab:!0,Ma:!0}):oE(a.A,b,{Vc:"min(65vw, 768px)",Ec:"",Tc:!1,zIndex:2147483645,Ab:!0,lg:!1,Ma:!0})} 
function lF(a){((d,e)=>{d[e]=d[e]||function(){(d[e].q=d[e].q||[]).push(arguments)};d[e].t=(new Date).getTime()})(a.A,"_googCsa");const b=a.K.map(d=>({container:d,relatedSearches:5})),c={pubId:a.Wa,styleId:"5134551505",hl:a.language,fexp:a.l,channel:"AutoRsVariant",resultsPageBaseUrl:"http://google.com",resultsPageQueryParam:"q",relatedSearchTargeting:"content",relatedSearchResultClickedCallback:a.bb.bind(a),relatedSearchUseResultCallback:!0,cx:a.La};a.X&&(c.adLoadedCallback=a.aa.bind(a));a.Ua&&a.j instanceof 
Array&&(c.fexp=a.j.join(","));a.A._googCsa("relatedsearch",c,b)}function mF(a){a.A.addEventListener("message",b=>{b.origin==="https://www.gstatic.com"&&b.data.action==="resize"&&(a.g.style.height=`${Math.ceil(b.data.height)+1}px`)})} 
var nF=class extends N{constructor(a,b,c,d,e,f,g,h,k=()=>{}){super();this.A=a;this.K=b;this.G=e;this.l=f;this.Va=!0;this.Hb=h;this.Ha=k;this.language=d?.g()||"en";this.qa=d?.i()||"Search results from ${website}";this.X=M(tv);this.Wa=c.replace("ca","partner");this.F=new Dl(a.document);this.g=Pl(this.F,"IFRAME");this.La=g.g?g.La:"9d449ff4a772956c6";this.j=(this.Ua=!0,L(Sq).g().concat(this.l));this.B=new AD({Ba:this.g,La:this.La,Vb:"auto-rs-prose",Wa:this.Wa,xd:"AutoRsVariant",location:a.location,language:this.language, 
jc:this.qa,Ta:this.j,Va:this.Va,Hb:this.Hb,md:!1,rg:!1,Ua:this.Ua});this.O=kF(this,this.g);Lr(this,this.O)}M(){this.K.length!==0&&(this.X||Ux(1075,()=>{this.B.M()},this.A),Ux(1076,()=>{const a=Pl(this.F,"SCRIPT");Bc(a,hd`https://www.google.com/adsense/search/async-ads.js`);this.A.document.head.appendChild(a)},this.A),lF(this),yu(this.G,{sts:"ok"}),this.Va&&mF(this))}aa(a,b){b?Ux(1075,()=>{this.B.M()},this.A):(this.Ha(),Au(this.G,"pfns"))}bb(a,b){zD(this.B,a,b);(()=>{if(!this.Va){const c=new ResizeObserver(async e=> 
{this.g.height="0";await new Promise(f=>{this.A.requestAnimationFrame(f)});this.g.height=e[0].target.scrollHeight.toString()}),d=()=>{const e=this.g.contentDocument?.documentElement;e?c.observe(e):(console.warn("iframe body missing"),setTimeout(d,1E3))};d()}this.O.show()})()}};var oF=class{constructor(a,b){this.g=a;this.La=b}};var pF=class{constructor(a,b,c){this.C=a;this.i=b;this.B=c;this.l="autors-widget";this.g=null;this.j=new O(null)}M(){var a=this.i.ha;a=Ju(a.i.document,a.G||!1);const b=this.B.Xc(this.C);a.appendChild(b);this.l&&(a.className=this.l);this.g=a;FA(this.i,this.g);this.j.g(b)}};async function qF(a){await new Promise(b=>{setTimeout(()=>{a.run();b()})})} 
function rF(a){if((!a.Md||!sF(a.config,a.ca,a.i))&&tF(v(a.g,Pt,5),a.i)){var b=a.g.i();b=zC(a.A,a.config,a.ca,a.i,{Wk:!!b?.l(),Md:a.Md,So:!!b?.g(),Uk:!!b?.C()});b=uF(b,a.A);var c=Object.keys(b),d=Object.values(b),e=wu(a.g.g()?.g())||0,f=vF(a.g),g=String(C(a.g,13));if(!v(a.config,Mt,25)?.g()){var h=()=>{d.forEach(k=>{k.g&&k.g.parentNode&&k.g.parentNode.removeChild(k.g);k.g=null;k.j.g(null)})};Ux(1074,()=>{(new nF(a.A,c,a.Sa,v(a.g,Pt,5),a.i,e,f,g,h)).M()},a.A)}}} 
var wF=class{constructor(a,b,c,d,e){this.A=a;this.config=c;this.Sa=d;this.ca=e;this.Md=!0;this.g=v(this.config,Rt,28);this.i=new Bu(a,b,this.g)}run(){try{rF(this)}catch(a){Au(this.i,"pfere",a)}}};function sF(a,b,c){a=wu(v(a,Rt,28)?.g()?.g())||0;const d=L(jx).g(xv.g,xv.defaultValue);return d&&d.includes(a.toString())?!1:(b?Vh(b,2):[]).length===0?(Au(c,"pfeu"),!0):!1} 
function tF(a,b){const c=L(jx).g(wv.g,wv.defaultValue);a=a?.g()||"";return c&&c.length!==0&&!c.includes(a.toString())?(Au(b,"pflna"),!1):!0}function uF(a,b){const c={};for(let e=0;e<a.length;e++){var d=a[e];const f="autors-container-"+e.toString(),g=b.document.createElement("div");g.setAttribute("id",f);d=new pF(b,d,new Gu(g));d.M();c[f]=d}return c}function vF(a){const b=A(a,11)||!1;a=C(a,8)||"";return new oF(b,a)};var xF=(a,b)=>{const c=[];v(a,$t,18)&&c.push(2);b.ca&&c.push(0);v(a,Rt,28)&&D(v(a,Rt,28),1)==1&&c.push(1);v(a,cu,34)&&A(v(a,cu,34),3)&&c.push(7);return c};var yF=a=>a.googlefc=a.googlefc||{},zF=a=>{a=a.googlefc=a.googlefc||{};return a.__fcusi=a.__fcusi||{}},AF=a=>{a=a.googlefc=a.googlefc||{};if(!a.getFloatingToolbarTranslatedMessages)return null;if(a=a.getFloatingToolbarTranslatedMessages()){var b=new St;b=ei(b,1,a.defaultFloatingToolbarToggleExpansionText);b=ei(b,2,a.defaultFloatingToolbarTogglePrivacySettings);a=ei(b,3,a.defaultFloatingToolbarDismissPrivacySettings);a=Wg(a)}else a=null;return a};function BF(a,b){b=b.filter(c=>v(c,qt,4)?.g()===5&&Zh(c,8)===1);b=jy(b,a);a=HA(b,a);a.sort((c,d)=>d.pa.g-c.pa.g);return a[0]||null};function CF({ug:a,vf:b,Xf:c,vg:d,wf:e,Yf:f}){const g=[];for(let n=0;n<f;n++)for(let p=0;p<c;p++){var h=p,k=c-1,l=n,m=f-1;g.push({x:a+(k===0?0:h/k)*(b-a),y:d+(m===0?0:l/m)*(e-d)})}return g}function DF(a,b){a.hasOwnProperty("_goog_efp_called_")||(a._goog_efp_called_=a.elementFromPoint(b.x,b.y));return a.elementFromPoint(b.x,b.y)};function EF(a,b){var c=CF({ug:b.left,vf:b.right,Xf:10,vg:b.top,wf:b.bottom,Yf:10});b=new Set;for(const d of c)(c=FF(a,d))&&b.add(c);return b}function GF(a,b,c=!1){for(const d of b)if((b=FF(a,d))&&!b.hasAttribute("google-allow-overlap")){if(c){const e=b.getBoundingClientRect();if(e.width>=a.L.innerWidth&&e.height>=a.L.innerHeight)continue}return b}return null}function HF(a,b,c=!1){return GF(a,b,c)!=null} 
function IF(a,b,c){if(Wl(b,"position")!=="fixed")return null;var d=b.getAttribute("class")==="GoogleActiveViewInnerContainer"||Zl(b).width<=1&&Zl(b).height<=1||a.g.gj&&!a.g.gj(b)?!0:!1;a.g.ah&&a.g.ah(b,c,d);return d?null:b}function FF(a,b){var c=DF(a.L.document,b);if(c){var d;if(!(d=IF(a,c,b)))a:{d=a.L.document;for(c=c.offsetParent;c&&c!==d.body;c=c.offsetParent){const e=IF(a,c,b);if(e){d=e;break a}}d=null}a=d||null}else a=null;return a}var JF=class{constructor(a,b={}){this.L=a;this.g=b}};var KF=class{constructor(a,b,c){this.position=a;this.Pb=b;this.Bf=c}};function LF(a,b){this.start=a<b?a:b;this.end=a<b?b:a};function MF(a,b,c){var d=ir(a);d=new KF(b.xc.yh(b.wb),b.Pb+2*b.wb,Math.min(d,b.ee)-b.xc.Rd()+2*b.wb);d=d.position.Tg(a,d.Pb,d.Bf);var e=hr(a),f=ir(a);c=NF(a,new ml(Nb(d.top,0,f-1),Nb(d.right,0,e-1),Nb(d.bottom,0,f-1),Nb(d.left,0,e-1)),c);f=OF(c);let g=d.top;e=[];for(let h=0;h<f.length;h++)f[h].start>g&&e.push(new LF(g,f[h].start)),g=f[h].end;g<d.bottom&&e.push(new LF(g,d.bottom));a=ir(a);d=[];for(f=e.length-1;f>=0;f--)d.push(new LF(a-e[f].end,a-e[f].start));a:{for(const h of d)if(a=h.start+b.wb,a> 
b.xc.Rd()+b.Nf?a=null:(d=Math.min(h.end-b.wb,b.ee)-a,a=d<b.Tf?null:{position:b.xc.bi(a),jd:d}),a){b=a;break a}b=null}return{af:b,Io:c}}function NF(a,b,c){const d=EF(new JF(a),b);c.forEach(e=>void d.delete(e));return d}function OF(a){return[...a].map(PF).sort((b,c)=>b.start-c.start)}function PF(a){a=a.getBoundingClientRect();return new LF(a.top,a.bottom)};function QF({ia:a,Ca:b}){return new RF(a,b)}var RF=class{constructor(a,b){this.ia=a;this.Ca=b}yh(a){return new RF(this.ia-a,this.Ca-a)}Tg(a,b,c){a=ir(a)-this.ia-c;return new ml(a,this.Ca+b,a+c,this.Ca)}Kg(a){a.bottom=`${this.ia}px`;a.left=`${this.Ca}px`;a.right=""}eh(){return 0}Rd(){return this.ia}bi(a){return new RF(a,this.Ca)}};function SF({ia:a,Qa:b}){return new TF(a,b)} 
var TF=class{constructor(a,b){this.ia=a;this.Qa=b}yh(a){return new TF(this.ia-a,this.Qa-a)}Tg(a,b,c){var d=hr(a);a=ir(a)-this.ia-c;d=d-this.Qa-b;return new ml(a,d+b,a+c,d)}Kg(a){a.bottom=`${this.ia}px`;a.right=`${this.Qa}px`;a.left=""}eh(){return 1}Rd(){return this.ia}bi(a){return new TF(a,this.Qa)}};function UF(a){var b={};const c=a.mj,d=a.Ri,e=a.Ji,f=a.Jk,g=a.Ki;a=a.Ii;b=b&&b.Wb;return QC('<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Symbols:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"'+(b?' nonce="'+Y(rD(b))+'"':"")+'/><link href="https://fonts.googleapis.com/css?family=Google+Sans+Text_old:400,500,700" rel="stylesheet"'+(b?' nonce="'+Y(rD(b))+'"':"")+"><style"+(b?' nonce="'+Y(rD(b))+'"':"")+">.ft-styless-button {border: none; background: none; user-select: none; cursor: pointer; border-radius: "+ 
Z(16)+"px;}.ft-container {position: fixed;}.ft-menu {position: absolute; bottom: 0; display: flex; flex-direction: column; justify-content: center; align-items: center; box-shadow: 0 4px 8px 3px rgba(60, 64, 67, 0.15), 0 1px 3px rgba(60, 64, 67, 0.3); min-height: "+Z(e)+"px;}.ft-menu:not(.ft-multiple-buttons *) {transition: padding 0.25s 0.25s, margin 0.25s 0.25s, border-radius 0.25s 0.25s, background-color 0s 0.5s; padding: 0; margin: "+Z(a)+"px; border-radius: "+Z(16)+"px; background-color: rgba(255, 255, 255, 0);}.ft-multiple-buttons .ft-menu {transition: margin 0.25s, padding 0.25s, border-radius 0.25s 0.25s, background-color 0s; padding: "+ 
Z(a)+"px; margin: 0; border-radius: "+Z(16+a)+"px; background-color: rgba(255, 255, 255, 1);}.ft-left-pos .ft-menu {left: 0;}.ft-right-pos .ft-menu {right: 0;}.ft-container.ft-hidden {transition: opacity 0.25s, visibility 0.5s 0s; opacity: 0; visibility: hidden;}.ft-container:not(.ft-hidden) {transition: opacity 0.25s, bottom 0.5s ease; opacity: 1;}.google-symbols {font-size: 26px; color: #3c4043;}.ft-button-holder {display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 0;}.ft-flip-vertically {transform: scaleY(-1);}.ft-expand-toggle {width: "+ 
Z(e)+"px; height: "+Z(e)+"px;}.ft-collapsed .ft-expand-icon {transition: transform 0.25s; transform: rotate(180deg);}.ft-expand-icon:not(.ft-collapsed *) {transition: transform 0.25s; transform: rotate(0deg);}.ft-button {position: relative; height: "+Z(e)+"px; margin-bottom: "+Z(g)+"px; transform: margin 0.25s 0.25s;}.ft-button.ft-last-button {margin-bottom: 0;}.ft-button > button {position: relative; height: "+Z(e)+"px; width: "+Z(e)+"px; margin: 0; padding: 0; border: none;}.ft-button > button > * {position: relative;}.ft-button .ft-highlighter {position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%); height: "+ 
Z(e-6)+"px; width: "+Z(e-6)+"px; border-radius: "+Z(e/2)+"px; background-color: #d2e3fc; opacity: 0; transition: opacity 0.25s;}.ft-button.ft-highlighted .ft-highlighter {opacity: 1;}.ft-button-corner-info {display: none;}.ft-button.ft-show-corner-info .ft-button-corner-info {position: absolute; left: -5px; top: 4px; background: #b3261e; border: 1.5px solid #ffffff; box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15); border-radius: 100px; color: ffffff; font-family: 'Google Sans Text'; font-style: normal; font-weight: 700; font-size: 11px; line-height: 14px; min-width: 16px; height: 16px; display: flex; flex-direction: row; justify-content: center; align-items: center;}.ft-separator {display: block; width: 100%; height: "+ 
Z(f)+"px;}.ft-separator > span {display: block; width: 28px; margin: 0 auto 10px auto; height: 0; border-bottom: 1px solid #dadce0;}.ft-expand-toggle-container {height: "+Z(e)+"px;}.ft-hidden {transition: opacity 0.25s, visibility 0.5s 0s; opacity: 0; visibility: hidden;}:not(.ft-hidden) {transition: opacity 0.25s; opacity: 1;}.ft-collapsed .ft-collapsible, .ft-collapsible.ft-collapsed, .ft-expand-toggle-container.ft-collapsed {transition: opacity 0.25s, margin 0.25s 0.25s, height 0.25s 0.25s, overflow 0.25s 0s, visibility 1s 0s; height: 0; opacity: 0; overflow: hidden; visibility: hidden; margin: 0;}.ft-collapsible:not(.ft-collapsed *):not(.ft-collapsed), .ft-expand-toggle-container:not(.ft-collapsed) {transition: margin 0.25s, height 0.25s, opacity 0.25s 0.25s; opacity: 1;}.ft-symbol-font-load-test {position: fixed; left: -1000px; top: -1000px; font-size: 26px; visibility: hidden;}.ft-reg-bubble {position: absolute; bottom: 0; padding: 10px 10px 0 10px; background: #fff; box-shadow: 0 4px 8px 3px rgba(60, 64, 67, 0.15), 0 1px 3px rgba(60, 64, 67, 0.3); border-radius: "+ 
Z(16)+"px; max-width: calc(90vw - "+Z(e*2)+"px); width: 300px; height: 200px;}.ft-left-pos .ft-reg-bubble {left: "+Z(e+10+a)+"px;}.ft-right-pos .ft-reg-bubble {right: "+Z(e+10+a)+"px;}.ft-collapsed .ft-reg-bubble, .ft-reg-bubble.ft-collapsed {transition: width 0.25s ease-in 0.25s, height 0.25s ease-in 0.25s, opacity 0.05s linear 0.45s, overflow 0s 0.25s, visibility 0s 0.5s; width: 0; overflow: hidden; opacity: 0; visibility: hidden;}.ft-collapsed .ft-reg-bubble, .ft-reg-bubble.ft-no-messages {height: 0 !important;}.ft-reg-bubble:not(.ft-collapsed *):not(.ft-collapsed) {transition: width 0.25s ease-out, height 0.25s ease-out, opacity 0.05s linear;}.ft-reg-bubble-content {display: flex; flex-direction: row; max-width: calc(90vw - "+ 
Z(e*2)+'px); width: 300px;}.ft-collapsed .ft-reg-bubble-content {transition: opacity 0.25s; opacity: 0;}.ft-reg-bubble-content:not(.ft-collapsed *) {transition: opacity 0.25s 0.25s; opacity: 1;}.ft-reg-message-holder {flex-grow: 1; display: flex; flex-direction: column; height: auto;}.ft-reg-controls {flex-grow: 0; padding-left: 5px;}.ft-reg-bubble-close-icon {font-size: 16px;}.ft-reg-message {font-family: \'Google Sans Text\'; font-style: normal; font-weight: 400; font-size: 12px; line-height: 14px; padding-bottom: 5px; margin-bottom: 5px; border-bottom: 1px solid #dadce0;}.ft-reg-message:last-of-type {border-bottom: none;}.ft-reg-message-button {border: none; background: none; font-family: \'Google Sans Text\'; color: #0b57d0; font-weight: 500; font-size: 14px; line-height: 22px; cursor: pointer; margin: 0; padding: 0; text-align: start;}.ft-display-none {display: none;}</style><toolbar id="ft-floating-toolbar" class="ft-container ft-hidden"><div class="ft-menu"><div class="ft-button-holder"></div><div class="ft-separator ft-collapsible ft-collapsed"><span></span></div><div class="ft-bottom-button-holder"></div><div class="ft-expand-toggle-container"><button class="ft-expand-toggle ft-styless-button" aria-controls="ft-floating-toolbar" aria-label="'+ 
Y(c)+'"><span class="google-symbols ft-expand-icon" aria-hidden="true">expand_more</span></button></div></div><div id="ft-reg-bubble" class="ft-reg-bubble ft-collapsed ft-no-messages"><div class="ft-reg-bubble-content"><div class="ft-reg-message-holder"></div><div class="ft-reg-controls"><button class="ft-reg-bubble-close ft-styless-button" aria-controls="ft-reg-bubble" aria-label="'+Y(d)+'"><span class="google-symbols ft-reg-bubble-close-icon" aria-hidden="true">close</span></button></div></div></div></toolbar><span inert class="ft-symbol-font-load-test"><span class="ft-symbol-reference google-symbols" aria-hidden="true">keyboard_double_arrow_right</span><span class="ft-text-reference" aria-hidden="true">keyboard_double_arrow_right</span></span>')} 
function VF(a){const b=a.googleIconName,c=a.backgroundColorCss,d=a.iconColorCss;return QC('<div class="ft-button ft-collapsible ft-collapsed ft-last-button"><button class="ft-styless-button" aria-label="'+Y(a.ariaLabel)+'" style="background-color: '+Y(Z(c))+'"><span class="ft-highlighter"></span><span class="google-symbols" style="color: '+Y(Z(d))+'" aria-hidden="true">'+OC(b)+'</span></button><span class="ft-button-corner-info"></span></div>')};const WF=["Google Symbols:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200","Google Sans Text:400,500,700"];function XF(a,b){a=new YF(a,b,ZF(a,b));a.M();return a}function $F(){({Gc:a}={Gc:2});var a;return a>1?50:120}function aG(a,b,c){bG(a)===0&&b.classList.remove("ft-collapsed");cG(b,c);Jr(a.A);b.classList.remove("ft-collapsed");dG(a);return()=>void eG(a,b,c)} 
function fG(a){gG(a.g.ka.kd).length===0?(a.l.R?.yk(),a.l.g(null),a.g.ka.Af.g(!1),a.g.ka.oh.g(!1),a.g.ka.Jf.g(!1)):(a.g.ka.Af.g(!0),hG(a))}function iG(a,{oi:b=0,Ho:c=0}){b=Math.max(gG(a.g.Tb).length+b,0);c=Math.max(gG(a.g.vb).length+c,0);const d=b+c;let e=d*50;b>0&&c>0&&(e+=11);e+=Math.max(0,d-1)*10;d>=a.j.Gc&&(e+=60);d>1&&(e+=10);return e}function bG(a){const b=a.g.vb;return gG(a.g.Tb).length+gG(b).length} 
function dG(a){const b=a.g.vb,c=a.g.separator;gG(a.g.Tb).length>0&&gG(b).length>0?c.classList.remove("ft-collapsed"):c.classList.add("ft-collapsed");bG(a)>=a.j.Gc?a.g.mh.g(!0):a.g.mh.g(!1);bG(a)>1?a.g.hh.g(!0):a.g.hh.g(!1);bG(a)>0?a.g.isVisible.g(!0):a.g.isVisible.g(!1);jG(a);kG(a)}function eG(a,b,c){b.classList.contains("ft-removing")||(b.classList.add("ft-removing"),b.classList.add("ft-collapsed"),dG(a),a.A.setTimeout(()=>{c.removeChild(b)},750))} 
function jG(a){const b=gG(a.g.Tb).concat(gG(a.g.vb));b.forEach(c=>{c.classList.remove("ft-last-button")});bG(a)>=a.j.Gc||b[b.length-1]?.classList.add("ft-last-button")}function kG(a){const b=gG(a.g.Tb).concat(gG(a.g.vb)).filter(c=>!c.classList.contains("ft-reg-button"));a.G.g(b.length>0)}function lG(a){wr(a.g.ka.kd.children,b=>{const c=a.g.ka.pd;eG(a,b,a.g.ka.kd);const d=c.get(b);c.delete(b);d?.isDismissed.g(!0)});fG(a)} 
function hG(a){if(!a.l.R){var b=mG(a.A,{googleIconName:"verified_user",ariaLabel:C(a.j.Xa,2),orderingIndex:0,onClick:()=>{a.g.ka.oh.g(!a.g.ka.isVisible.R);for(const [,c]of a.g.ka.pd)c.rh=!0;a.g.ka.Jf.g(!1)},backgroundColorCss:"#fff"});b.Fd.classList.add("ft-reg-button");aG(a,b.Fd,a.g.vb);Zr(b.Nj,a.g.ka.isVisible);a.l.g({Mo:b,yk:()=>void eG(a,b.Fd,a.g.vb)})}}function nG(a){var b=a.g.ka.Jf,c=b.g;a:{for([,d]of a.g.ka.pd)if(a=d,a.showUnlessUserInControl&&!a.rh){var d=!0;break a}d=!1}c.call(b,d)} 
function oG(a){a.g.ka.Qi.listen(()=>{lG(a)})} 
var YF=class extends N{constructor(a,b,c){super();this.A=a;this.j=b;this.g=c;this.l=new O(null);this.G=new O(!1)}addButton(a){a=mG(this.A,a);return aG(this,a.Fd,this.g.Tb)}addRegulatoryMessage(a){const b=this.g.ka.kd,c=pG(this.A,a);cG(c.Qf,b);this.g.ka.pd.set(c.Qf,c);fG(this);return{showUnlessUserInControl:()=>{c.showUnlessUserInControl=!0;nG(this)},hideUnlessUserInControl:()=>{c.showUnlessUserInControl=!1;nG(this)},isDismissed:as(c.isDismissed),removeCallback:()=>{var d=c.Qf;const e=this.g.ka.kd; 
d.parentNode===e&&e.removeChild(d);this.g.ka.pd.delete(d);fG(this)}}}K(){return Tr(this.l.map(a=>a!=null))}F(){return Tr(this.G)}B(){return[this.g.container]}i(){const a=this.g.qb.pb;a.parentNode?.removeChild(a);super.i()}M(){Js(this.A,WF);Zr(this.g.Zk,this.j.Db);this.A.document.body.appendChild(this.g.qb.pb);oG(this)}}; 
function ZF(a,b){const c=cE(a),d=c.shadowRoot;d.appendChild(Ql(new Dl(a.document),LC(UF({mj:C(b.Xa,1),Ri:C(b.Xa,3),Ji:50,Jk:11,Ki:10,Ii:5}))));const e=bE("ft-container",d),f=bE("ft-expand-toggle",d),g=bE("ft-expand-toggle-container",d),h=new O(null);h.i(p=>{e.style.zIndex=String(p??2147483647)});const k=new O(!0);Wr(k,!0,()=>{e.classList.remove("ft-collapsed");f.setAttribute("aria-expanded","true")});Wr(k,!1,()=>{e.classList.add("ft-collapsed");f.setAttribute("aria-expanded","false")});f.addEventListener("click", 
()=>{k.g(!k.R)});const l=new O(!1);Wr(l,!0,()=>{g.classList.remove("ft-collapsed");e.classList.add("ft-toolbar-collapsible")});Wr(l,!1,()=>{g.classList.add("ft-collapsed");e.classList.remove("ft-toolbar-collapsible");k.g(!0)});const m=new O(!1);Wr(m,!0,()=>{e.classList.add("ft-multiple-buttons")});Wr(m,!1,()=>{e.classList.remove("ft-multiple-buttons")});b.position.i(p=>{if(p){p.Kg(e.style);p=p.eh();switch(p){case 0:e.classList.add("ft-left-pos");e.classList.remove("ft-right-pos");break;case 1:e.classList.add("ft-right-pos"); 
e.classList.remove("ft-left-pos");break;default:throw Error(`Unknown HorizontalAnchoring: ${p}`);}Jr(a)}});const n=new O(!1);b=Sr(qG(a,d),n,b.position.map(p=>p!==null));Wr(b,!0,()=>{e.classList.remove("ft-hidden")});Wr(b,!1,()=>{e.classList.add("ft-hidden")});b=rG(a,bE("ft-reg-bubble",d));return{container:e,Tb:bE("ft-button-holder",d),vb:bE("ft-bottom-button-holder",d),separator:bE("ft-separator",d),qb:c,Zk:h,Po:k,mh:l,hh:m,isVisible:n,ka:b}} 
function rG(a,b){const c=new O(!1),d=new O(!1),e=Ur(c,d);Wr(e,!0,()=>{b.classList.remove("ft-collapsed")});Wr(e,!1,()=>{b.classList.add("ft-collapsed")});const f=new O(!1);Wr(f,!0,()=>{b.classList.remove("ft-no-messages")});Wr(f,!1,()=>{b.classList.add("ft-no-messages")});const g=bE("ft-reg-bubble-close",b),h=new es;g.addEventListener("click",()=>{ds(h)});const k=bE("ft-reg-message-holder",b);Cs(zs(a,k)).i(()=>{b.style.height=`${k.offsetHeight}px`});return{kd:k,oh:c,Jf:d,isVisible:e,Af:f,pd:new Map, 
Qi:bs(h)}} 
function mG(a,b){const c=Ql(new Dl(a.document),LC(VF({googleIconName:b.googleIconName,ariaLabel:b.ariaLabel,backgroundColorCss:b.backgroundColorCss||"#e2eaf6",iconColorCss:b.iconColorCss||"#3c4043"})));b.buttonExtension?.styleSheet&&c.appendChild(b.buttonExtension.styleSheet);if(b.cornerNumber!==void 0){const d=Nb(Math.round(b.cornerNumber),0,99);bE("ft-button-corner-info",c).appendChild(a.document.createTextNode(String(d)));c.classList.add("ft-show-corner-info")}c.orderingIndex=b.orderingIndex;b.onClick&& 
aE("BUTTON",c).addEventListener("click",b.onClick);a=new O(!1);Wr(a,!0,()=>{c.classList.add("ft-highlighted")});Wr(a,!1,()=>{c.classList.remove("ft-highlighted")});return{Fd:c,Nj:a}} 
function pG(a,b){a=new Dl(a.document);var c=QC('<div class="ft-reg-message"><button class="ft-reg-message-button"></button><div class="ft-reg-message-info"></div></div>');a=Ql(a,LC(c));c=bE("ft-reg-message-button",a);b.regulatoryMessage.actionButton?(c.appendChild(b.regulatoryMessage.actionButton.buttonText),c.addEventListener("click",b.regulatoryMessage.actionButton.onClick)):c.classList.add("ft-display-none");c=bE("ft-reg-message-info",a);b.regulatoryMessage.informationText?c.appendChild(b.regulatoryMessage.informationText): 
c.classList.add("ft-display-none");a.orderingIndex=b.orderingIndex;return{Qf:a,showUnlessUserInControl:!1,rh:!1,isDismissed:new O(!1)}}function cG(a,b){a:{var c=Array.from(b.children);for(let d=0;d<c.length;++d)if(c[d].orderingIndex>=a.orderingIndex){c=d;break a}c=c.length}b.insertBefore(a,b.childNodes[c]||null)}function gG(a){return Array.from(a.children).filter(b=>!b.classList.contains("ft-removing"))} 
function qG(a,b){const c=new O(!1),d=bE("ft-symbol-font-load-test",b);b=bE("ft-symbol-reference",d);const e=bE("ft-text-reference",d),f=zs(a,b);Xr(Cs(f).map(g=>g.width>0&&g.width<e.offsetWidth/2),!0,()=>{c.g(!0);d.parentNode?.removeChild(d);f.dispose()});return c};function sG(a){const b=new es,c=ps(a,2500,()=>void ds(b));return new tG(a,()=>void uG(a,()=>void c()),bs(b))}function vG(a){const b=new MutationObserver(()=>{a.g()});b.observe(a.A.document.documentElement,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["class","style"]});Mr(a,()=>void b.disconnect())}function wG(a){a.A.addEventListener("resize",a.g);Mr(a,()=>void a.A.removeEventListener("resize",a.g))}var tG=class extends N{constructor(a,b,c){super();this.A=a;this.g=b;this.l=c;this.j=!1}}; 
function uG(a,b){b();a.setTimeout(b,1500)};function xG(a){return a.g[a.g.length-1]}var zG=class{constructor(){this.j=yG;this.g=[];this.i=new Set}add(a){if(this.i.has(a))return!1;const b=Za(this.g,a,this.j);this.g.splice(b>=0?b:-b-1,0,a);this.i.add(a);return!0}first(){return this.g[0]}has(a){return this.i.has(a)}delete(a){Sa(this.g,b=>b===a);return this.i.delete(a)}clear(){this.i.clear();return this.g.splice(0,this.g.length)}size(){return this.g.length}};function AG(a){var b=a.jd.R;let c;for(;a.j.Ti()>b&&(c=a.i.first());){var d=a,e=c;BG(d,e);d.g.add(e)}for(;(d=xG(a.g))&&a.j.Aj()<=b;)CG(a,d);for(;(d=xG(a.g))&&(c=a.i.first())&&d.priority>c.priority;)b=a,e=c,BG(b,e),b.g.add(e),CG(a,d)}function CG(a,b){a.g.delete(b);a.i.add(b)&&(b.zg=a.j.addButton(b.buttonSpec));b.isInToolbar.g(!0)}function BG(a,b){b.zg&&b.zg();b.zg=void 0;a.i.delete(b);b.isInToolbar.g(!1)} 
var DG=class{constructor(a,b){this.jd=a;this.j=b;this.g=new zG;this.i=new zG;this.l=0;this.jd.listen(()=>void AG(this))}addButton(a){const b={buttonSpec:a.buttonSpec,priority:a.priority,Bg:this.l++,isInToolbar:new O(!1)};this.g.add(b);AG(this);return{isInToolbar:as(Tr(b.isInToolbar)),removeCallback:()=>{BG(this,b);this.g.delete(b);AG(this)}}}};function yG(a,b){return a.priority===b.priority?b.Bg-a.Bg:a.priority-b.priority};function EG(a){if(!ZD(a.A)){if(a.j.R){const b=rr(a.A);if(b>a.g+100||b<a.g-100)a.j.g(!1),a.g=kr(a.A)}a.l&&a.A.clearTimeout(a.l);a.l=a.A.setTimeout(()=>void FG(a),200)}}function FG(a){if(!ZD(a.A)){var b=kr(a.A);a.g&&a.g>b&&(a.g=b);b=rr(a.A);b>=a.g-100&&(a.g=Math.max(a.g,b),a.j.g(!0))}} 
var GG=class extends N{constructor(a){super();this.A=a;this.j=new O(!1);this.g=0;this.l=null;this.B=()=>void EG(this)}M(){this.A.addEventListener("scroll",this.B);this.g=kr(this.A);FG(this)}i(){this.A.removeEventListener("scroll",this.B);this.j.g(!1);super.i()}};function HG(a){if(!a.g){var b=new GG(a.A);b.M();a.g=Tr(b.j);Lr(a,b)}return a.g}function IG(a,b,c){const d=a.j.addRegulatoryMessage(b.messageSpec);b.messageSpec.regulatoryMessage.disableFloatingToolbarAutoShow||JG(a,d,c);Xr(c,!0,()=>{d.removeCallback()})}function JG(a,b,c){a=HG(a);const d=Wr(a,!0,()=>void b.showUnlessUserInControl()),e=Wr(a,!1,()=>void b.hideUnlessUserInControl());Wr(Qr(b.isDismissed),!0,()=>{d();e()});Xr(c,!0,()=>{d();e()})} 
var KG=class extends N{constructor(a,b){super();this.A=a;this.j=b;this.g=null}addRegulatoryMessage(a){const b=new O(!1),c=Xr(HG(this),!0,()=>{IG(this,a,b)});return{removeCallback:()=>{b.g(!0);c()}}}};function LG(a,b){a.googFloatingToolbarManager||(a.googFloatingToolbarManager=new MG(a,b));return a.googFloatingToolbarManager}function NG(a){a.g||(a.g=OG(a.A,a.j,a.Db),Lr(a,a.g.Zb),Lr(a,a.g.Kh),PG(a),QG(a,a.g.Zb));return a.g}function RG(a){a.Db.R===null&&a.g?.position.g(SG(a))}function TG(a){a.A.requestAnimationFrame(()=>void RG(a))} 
function SG(a){var b=[];a.g?.Zb?.F().C()?(b.push(()=>UG(a)),b.push(()=>VG(a))):(b.push(()=>VG(a)),b.push(()=>UG(a)));a.g?.Zb?.K()?.C()&&b.push(()=>{const c=ir(a.A);return{position:QF({ia:Math.floor(c/3),Ca:10}),jd:0}});for(const c of b)if(b=c())return b;return null}function PG(a){a.A.googFloatingToolbarManagerAsyncPositionUpdate?TG(a):RG(a)} 
function QG(a,b){const c=sG(a.A);c.j||(vG(c),wG(c),c.j=!0);c.l.listen(()=>void PG(a));Lr(a,c);b.K().listen(()=>void PG(a));b.F().listen(()=>void PG(a));a.Db.listen(()=>void PG(a))}function UG(a){var b=a.A;const c=ir(a.A);return MF(b,{xc:SF({ia:50,Qa:10}),Nf:Math.floor(c/3),Pb:60,Tf:$F(),ee:Math.floor(c/2),wb:20},[...(a.g?.Zb.B()??[]),a.A.document.body]).af} 
function VG(a){var b=a.A;const c=ir(a.A);return MF(b,{xc:QF({ia:50,Ca:10}),Nf:Math.floor(c/3),Pb:60,Tf:$F(),ee:Math.floor(c/2),wb:40},[...(a.g?.Zb.B()??[]),a.A.document.body]).af}class MG extends N{constructor(a,b){super();this.A=a;this.j=b;this.g=null;this.Db=WG(this.A,this)}addButton(a){return NG(this).ek.addButton(a)}addRegulatoryMessage(a){return NG(this).Kh.addRegulatoryMessage(a)}} 
function OG(a,b,c){const d=new O(null),e=XF(a,{Gc:2,position:d.map(f=>f?.position??null),Xa:b,Db:c});b=new DG(d.map(f=>f?.jd||0),{addButton:f=>e.addButton(f),Ti:()=>iG(e,{}),Aj:()=>iG(e,{oi:1})});a=new KG(a,{addRegulatoryMessage:f=>e.addRegulatoryMessage(f)});return{Zb:e,position:d,ek:b,Kh:a}}function WG(a,b){const c=new JD(a),d=new O(null),e=f=>void d.g(f);Mr(b,()=>{ID(c,e)});c.floatingAdsStacking.maxZIndexListeners.push(e);d.g(HD(c));return d};const XG=["Google Symbols:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200","Google Sans Text:400,500"]; 
function YG(a,b,c,d,e){a=new ZG(a,b,c,d,e);if(a.l){Js(a.A,XG);var f=a.A;b=a.message;c=cE(f);e=c.shadowRoot;d=e.appendChild;f=new Dl(f.document);var g=(g={},g.Wb);g=QC('<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Google+Symbols:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200"'+(g?' nonce="'+Y(rD(g))+'"':"")+'/><link href="https://fonts.googleapis.com/css?family=Google+Sans+Text_old:400,500" rel="stylesheet"'+(g?' nonce="'+Y(rD(g))+'"':"")+"><style"+(g?' nonce="'+Y(rD(g))+ 
'"':"")+'>.ipr-container {font-family: \'Google Sans Text\'; font-style: normal; font-weight: 400; font-size: 12px; line-height: 14px; color: #000; border-top: 2px solid rgb(236, 237, 237); border-bottom: 2px solid rgb(236, 237, 237); background-color: #fff; padding: 5px; margin: 5px 0; text-align: center;}.ipr-button {border: none; background: none; font-family: \'Google Sans Text\'; color: #0b57d0; font-weight: 500; font-size: 14px; line-height: 22px; cursor: pointer; margin: 0; padding: 0;}.ipr-display-none {display: none;}</style><div class="ipr-container"><button class="ipr-button"></button><div class="ipr-info"></div></div>'); 
d.call(e,Ql(f,LC(g)));d=bE("ipr-container",e);e=bE("ipr-button",d);b.actionButton?(e.appendChild(b.actionButton.buttonText),e.addEventListener("click",b.actionButton.onClick)):e.classList.add("ipr-display-none");d=bE("ipr-info",d);b.informationText?d.appendChild(b.informationText):d.classList.add("ipr-display-none");a.g=c.pb;FA(a.l,a.g);a.j&&a.j(zo(1));$G(a)}else aH(a);return a}function $G(a){const b=new Ms(a.A);b.M(2E3);Lr(a,b);Ks(b,()=>{bH(a);aH(a);b.dispose()})} 
function aH(a){const b=LG(a.A,a.B).addRegulatoryMessage({messageSpec:{regulatoryMessage:a.message,orderingIndex:0}});Mr(a,()=>void b.removeCallback());a.j&&a.j(zo(2))}function bH(a){a.g&&(a.g.parentNode?.removeChild(a.g),a.g=null)}var ZG=class extends N{constructor(a,b,c,d,e){super();this.A=a;this.l=b;this.message=c;this.B=d;this.j=e;this.g=null}i(){bH(this);super.i()}};var dH=(a,b,c,d)=>cH(a,b,c,d);function cH(a,b,c,d){const e=YG(a,BF(a,d),{actionButton:{buttonText:a.document.createTextNode(b),onClick:c}},eH(a));return()=>e.dispose()}function eH(a){if(a=AF(a))return a;zB(1234,Error("No messages"));return Wg(new St)};function fH(a,b){b&&(a.g=dH(a.i,b.localizedDnsText,()=>gH(a,b),a.l))}function hH(a){const b=yF(a.i);b.callbackQueue=b.callbackQueue||[];zF(a.i).overrideDnsLink=!0;b.callbackQueue.push({INITIAL_US_STATES_DATA_READY:c=>fH(a,c)})}function gH(a,b){KD(a.j);b.openConfirmationDialog(c=>{c&&a.g&&(a.g(),a.g=null);LD(a.j)})}var iH=class{constructor(a,b,c){this.i=a;this.j=ED(b,2147483643);this.l=c;this.g=null}};function jH(a){kH(a.j,b=>{var c=a.i,d=b.revocationText,e=b.attestationText,f=b.showRevocationMessage;b=BF(c,a.l);d={actionButton:{buttonText:c.document.createTextNode(d),onClick:f},informationText:c.document.createTextNode(e)};e=AF(c);e||(zB(1233,Error("No messages")),e=Wg(new St));YG(c,b,d,e)},()=>{LD(a.g);lH(a)})}function mH(a){KD(a.g);jH(a)} 
function lH(a){a.i.__tcfapi?a.i.__tcfapi("addEventListener",2,(b,c)=>{c&&b.eventStatus=="cmpuishown"?KD(a.g):LD(a.g)}):zB(1250,Error("No TCF API function"))}var nH=class{constructor(a,b,c,d){this.i=a;this.g=ED(b,2147483643);this.l=c;this.j=d}};var oH=a=>{if(!a||Ph(a,1)==null)return!1;a=D(a,1);switch(a){case 1:return!0;case 2:return!1;default:throw Error("Unhandled AutoConsentUiStatus: "+a);}},pH=a=>{if(!a||Ph(a,3)==null)return!1;a=D(a,3);switch(a){case 1:return!0;case 2:return!1;default:throw Error("Unhandled AutoCcpaUiStatus: "+a);}},qH=a=>a?A(a,5)===!0:!1;function rH(a){let b=a.location.href;if(a===a.top)return{url:b,Lf:!0};let c=!1;const d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));(a=a.location.ancestorOrigins)&&(a=a[a.length-1])&&b.indexOf(a)===-1&&(c=!1,b=a);return{url:b,Lf:c}};function sH(a,b){vd(a,(c,d)=>{b[d]=c})}function tH(a){if(a===a.top)return 0;for(let b=a;b&&b!==b.top&&kd(b);b=b.parent){if(a.sf_)return 2;if(a.$sf)return 3;if(a.inGptIF)return 4;if(a.inDapIF)return 5}return 1};function uH(){if(vH)return vH;const a=Al()||window,b=a.google_persistent_state_async;return b!=null&&typeof b=="object"&&b.S!=null&&typeof b.S=="object"?vH=b:a.google_persistent_state_async=vH=new wH}function xH(a,b,c){b=yH[b]||`google_ps_${b}`;a=a.S;const d=a[b];return d===void 0?(a[b]=c(),a[b]):d}function zH(a,b,c){return xH(a,b,()=>c)}function AH(a,b,c){return a.S[yH[b]||`google_ps_${b}`]=c}function BH(a,b){return AH(a,b,zH(a,b,0)+1)}function CH(){var a=uH();return zH(a,20,{})} 
function DH(){var a=uH();const b=zH(a,41,!1);b||AH(a,41,!0);return!b}function EH(){var a=uH();return zH(a,26)}function FH(){var a=uH();return zH(a,28,[])}function GH(){var a=uH();return xH(a,39,HH)}var wH=class{constructor(){this.S={}}},vH=null;const yH={[8]:"google_prev_ad_formats_by_region",[9]:"google_prev_ad_slotnames_by_region"};function IH(a){return a.google_ad_modifications=a.google_ad_modifications||{}}function JH(a,b){a=IH(a);a.processed_sra_frame_pingbacks=a.processed_sra_frame_pingbacks||{};const c=!a.processed_sra_frame_pingbacks[b];a.processed_sra_frame_pingbacks[b]=!0;return c};function Nq(a,b){a.g.size>0||KH(a);const c=a.g.get(0);c?c.push(b):a.g.set(0,[b])}function LH(a,b,c,d){qb(b,c,d);Mr(a,()=>rb(b,c,d))}function MH(a,b){a.j!==1&&(a.j=1,a.g.size>0&&NH(a,b))}function KH(a){a.A.document.visibilityState?LH(a,a.A.document,"visibilitychange",b=>{a.A.document.visibilityState==="hidden"&&MH(a,b);a.A.document.visibilityState==="visible"&&(a.j=0)}):"onpagehide"in a.A?(LH(a,a.A,"pagehide",b=>{MH(a,b)}),LH(a,a.A,"pageshow",()=>{a.j=0})):LH(a,a.A,"beforeunload",b=>{MH(a,b)})} 
function NH(a,b){for(let c=9;c>=0;c--)a.g.get(c)?.forEach(d=>{d(b)})}var OH=class extends N{constructor(a){super();this.A=a;this.j=0;this.g=new Map}};async function PH(a,b){var c=10;return c<=0?Promise.reject(Error(`wfc bad input ${c} ${200}`)):b()?Promise.resolve():new Promise((d,e)=>{const f=a.setInterval(()=>{--c?b()&&(a.clearInterval(f),d()):(a.clearInterval(f),e(Error(`wfc timed out ${c}`)))},200)})};function QH(a){const b=a.g.pc;return b!==null&&b!==0?b:a.g.pc=Zd(a.A)}function RH(a){var b=a.g.wpc;if(b===null||b==="")b=a.g,a=a.A,a=a.google_ad_client?String(a.google_ad_client):IH(a).head_tag_slot_vars?.google_ad_client??a.document.querySelector(".adsbygoogle[data-ad-client]")?.getAttribute("data-ad-client")??"",b=b.wpc=a;return b}function SH(a,b){var c=new Qp;var d=QH(a);c=di(c,1,d);d=RH(a);c=fi(c,2,d);c=Pp(c,a.g.sd);return di(c,7,Math.round(b||a.A.performance.now()))} 
function TH(a,b,c){b(a.I.je.Fe.Bd).Ea(c)}function UH(a,b,c){b(a.I.je.Fe.Bd).od(c)}async function VH(a){await PH(a.A,()=>!(!QH(a)||!RH(a)))}function WH(){var a=L(XH);a.i&&(a.g.tar+=1)}function YH(a){var b=L(XH);if(b.i){var c=b.l;a(c);b.g.cc=Lg(c)}}async function ZH(a,b,c){if(a.i&&c.length&&!a.g.lgdp.includes(Number(b))){a.g.lgdp.push(Number(b));var d=a.A.performance.now();await VH(a);var e=a.I,f=e.Jb;a=SH(a,d);d=new no;b=F(d,1,b);c=oh(b,2,c,Qf);c=Ah(a,9,Rp,c);f.call(e,c)}} 
async function $H(a,b){await VH(a);var c=SH(a);b=Ah(c,5,Rp,b);a.i&&!a.g.le.includes(2)&&(a.g.le.push(2),a.I.Jb(b))}async function aI(a,b,c){await VH(a);var d=a.I,e=d.Jb;a=Pp(SH(a,c),1);b=Ah(a,6,Rp,b);e.call(d,b)}async function bI(a,b,c){await VH(a);TH(a,d=>b(d.Rh),c)}async function cI(a,b,c){await VH(a);UH(a,d=>b(d.Rh),c)}async function dI(a,b){await VH(a);var c=a.I,d=c.Jb;a=Pp(SH(a),1);b=Ah(a,13,Rp,b);d.call(c,b)} 
async function eI(a,b){if(a.i){await VH(a);var c=a.I,d=c.Jb;a=SH(a);b=Ah(a,11,Rp,b);d.call(c,b)}}async function fI(a,b){await VH(a);var c=a.I,d=c.Jb;a=Pp(SH(a),1);b=Ah(a,16,Rp,b);d.call(c,b)} 
var XH=class{constructor(a,b){this.A=Al()||window;this.j=b??new OH(this.A);this.I=a??new Pq(cq(),100,100,!0,this.j);this.g=xH(uH(),33,()=>{const c=V(Uu);return{sd:c,ssp:c>0&&ud()<1/c,pc:null,wpc:null,cu:null,le:[],lgdp:[],psi:null,tar:0,cc:null}})}get i(){return this.g.ssp}get ib(){return this.g.cu}set ib(a){this.g.cu=a}get l(){return vB(1227,()=>ji(oo,Mg(this.g.cc||[])))||new oo}};var gI=class{constructor(a,b,c,d,e){this.i=a;this.j=b;this.g=c;this.C=d;this.l=e||null}run(){if(this.i.adsbygoogle_ama_fc_has_run!==!0){var a=oH(this.g),b=pH(this.g),c=!1;a&&(mH(new nH(this.i,this.C,this.l||zh(this.g,Zt,4,eh()),this.j)),c=!0);b&&(hH(new iH(this.i,this.C,this.l||zh(this.g,Zt,4,eh()))),c=!0);YH(d=>{d=E(d,9,!0);d=E(d,10,a);E(d,11,b)});qH(this.g)&&(c=!0);c&&(this.j.start(!0),this.i.adsbygoogle_ama_fc_has_run=!0)}}};function hI(a,b,c,d,e,f){try{const g=a.g,h=sd("SCRIPT",g);h.async=!0;Bc(h,b);g.head.appendChild(h);h.addEventListener("load",()=>{e();d&&g.head.removeChild(h)});h.addEventListener("error",()=>{c>0?hI(a,b,c-1,d,e,f):(d&&g.head.removeChild(h),f())})}catch(g){f()}}function iI(a,b,c=()=>{},d=()=>{}){hI(Cl(a),b,0,!1,c,d)};function jI(a=null){a=a||q;return a.googlefc||(a.googlefc={})};Ub(Zq).map(a=>Number(a));Ub($q).map(a=>Number(a));const kI=q.URL;function lI(a){const b=c=>encodeURIComponent(c).replace(/[!()~']|(%20)/g,d=>({"!":"%21","(":"%28",")":"%29","%20":"+","'":"%27","~":"%7E"})[d]);return Array.from(a,c=>b(c[0])+"="+b(c[1])).join("&")};function mI(a){var b=(new kI(a.location.href)).searchParams;a=b.get("fcconsent");b=b.get("fc");return b==="alwaysshow"?b:a==="alwaysshow"?a:null}function nI(a){const b=["ab","gdpr","consent","ccpa","monetization"];return(a=(new kI(a.location.href)).searchParams.get("fctype"))&&b.indexOf(a)!==-1?a:null} 
function oI(a){var b=new kI(a),c={search:"",hash:""};a={};b&&(a.protocol=b.protocol,a.username=b.username,a.password=b.password,a.hostname=b.hostname,a.port=b.port,a.pathname=b.pathname,a.search=b.search,a.hash=b.hash);Object.assign(a,c);if(a.port&&a.port[0]===":")throw Error("port should not start with ':'");a.hash&&a.hash[0]!="#"&&(a.hash="#"+a.hash);c.search?c.search[0]!="?"&&(a.search="?"+c.search):c.searchParams&&(a.search="?"+lI(c.searchParams),a.searchParams=void 0);b="";a.protocol&&(b+=a.protocol+ 
"//");c=a.username;var d=a.password;b=b+(c&&d?c+":"+d+"@":c?c+"@":d?":"+d+"@":"")+(a.hostname||"");a.port&&(b+=":"+a.port);b+=a.pathname||"";b+=a.search||"";b+=a.hash||"";a=(new kI(b)).toString();a.charAt(a.length-1)==="/"&&(a=a.substring(0,a.length-1));return a.toString().length<=1E3?a:null};function pI(a,b){const c=a.document,d=()=>{if(!a.frames[b])if(c.body){const e=sd("IFRAME",c);e.style.display="none";e.style.width="0px";e.style.height="0px";e.style.border="none";e.style.zIndex="-1000";e.style.left="-1000px";e.style.top="-1000px";e.name=b;c.body.appendChild(e)}else a.setTimeout(d,5)};d()};var qI=Uj(class extends G{});function rI(a){if(a.g)return a.g;a.O&&a.O(a.j)?a.g=a.j:a.g=Md(a.j,a.X);return a.g??null}function sI(a){a.l||(a.l=b=>{try{var c=a.K?a.K(b):void 0;if(c){var d=c.eg,e=a.G.get(d);e&&(e.rk||a.G.delete(d),e.sc?.(e.Xi,c.payload))}}catch(f){}},qb(a.j,"message",a.l))}function tI(a,b,c){if(rI(a))if(a.g===a.j)(b=a.F.get(b))&&b(a.g,c);else{var d=a.B.get(b);if(d&&d.hd){sI(a);var e=++a.aa;a.G.set(e,{sc:d.sc,Xi:d.ae(c),rk:b==="addEventListener"});a.g.postMessage(d.hd(c,e),"*")}}} 
var uI=class extends N{constructor(a,b,c,d){super();this.X=b;this.O=c;this.K=d;this.F=new Map;this.aa=0;this.B=new Map;this.G=new Map;this.l=void 0;this.j=a}i(){delete this.g;this.F.clear();this.B.clear();this.G.clear();this.l&&(rb(this.j,"message",this.l),delete this.l);delete this.j;delete this.K;super.i()}};const vI=(a,b)=>{const c={cb:d=>{d=qI(d);b.hb({Ic:d})}};b.spsp&&(c.spsp=b.spsp);a=a.googlefc||(a.googlefc={});a.__fci=a.__fci||[];a.__fci.push(b.command,c)},wI={ae:a=>a.hb,hd:(a,b)=>({__fciCall:{callId:b,command:a.command,spsp:a.spsp||void 0}}),sc:(a,b)=>{a({Ic:b})}};function xI(a){a=qI(a.data.__fciReturn);return{payload:a,eg:kg(Yg(a,1))??0}}function yI(a,b=!1){if(b)return!1;a.j||(a.g=!!rI(a.caller),a.j=!0);return a.g} 
function zI(a){return new Promise(b=>{yI(a)&&tI(a.caller,"getDataWithCallback",{command:"loaded",hb:c=>{b(c.Ic)}})})}function AI(a,b){yI(a)&&tI(a.caller,"getDataWithCallback",{command:"prov",spsp:ii(b),hb:()=>{}})}var BI=class extends N{constructor(a){super();this.g=this.j=!1;this.caller=new uI(a,"googlefcPresent",void 0,xI);this.caller.F.set("getDataWithCallback",vI);this.caller.B.set("getDataWithCallback",wI)}i(){this.caller.dispose();super.i()}};function CI(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3} 
function DI(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=CI(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?($k({e:String(a.internalErrorState)},"tcfe"),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0} 
function EI(a,b={}){return DI(a)?a.gdprApplies===!1?!0:a.tcString==="tcunavailable"?!b.idpcApplies:(b.idpcApplies||a.gdprApplies!==void 0||b.No)&&(b.idpcApplies||typeof a.tcString==="string"&&a.tcString.length)?FI(a,"1",0):!0:!1} 
function FI(a,b,c){a:{if(a.publisher&&a.publisher.restrictions){var d=a.publisher.restrictions[b];if(d!==void 0){d=d["755"];break a}}d=void 0}if(d===0)return!1;let e=c;c===2?(e=0,d===2&&(e=1)):c===3&&(e=1,d===1&&(e=0));if(e===0)a=a.purpose&&a.vendor?(c=GI(a.vendor.consents,"755"))&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:c&&GI(a.purpose.consents,b):!0;else{var f;e===1?f=a.purpose&&a.vendor?GI(a.purpose.legitimateInterests,b)&&GI(a.vendor.legitimateInterests,"755"):!0:f=!0;a=f}return a} 
function GI(a,b){return!(!a||!a[b])}function HI(a,b,c){return a.gdprApplies===!1?!0:b.every(d=>FI(a,d,c))}function II(a){if(a.g)return a.g;a.g=Md(a.j,"__tcfapiLocator");return a.g}function JI(a){return typeof a.j.__tcfapi==="function"||II(a)!=null}function KI(a,b,c,d){c||(c=()=>{});var e=a.j;typeof e.__tcfapi==="function"?(a=e.__tcfapi,a(b,2,c,d)):II(a)?(LI(a),e=++a.K,a.F[e]=c,a.g&&a.g.postMessage({__tcfapiCall:{command:b,version:2,callId:e,parameter:d}},"*")):c({},!1)} 
function MI(a,b){let c={internalErrorState:0,internalBlockOnErrors:a.B};const d=kb(()=>b(c));let e=0;a.G!==-1&&(e=setTimeout(()=>{e=0;c.tcString="tcunavailable";c.internalErrorState=1;d()},a.G));KI(a,"addEventListener",f=>{f&&(c=f,c.internalErrorState=CI(c),c.internalBlockOnErrors=a.B,DI(c)?(c.internalErrorState!==0&&(c.tcString="tcunavailable"),KI(a,"removeEventListener",null,c.listenerId),(f=e)&&clearTimeout(f),d()):(c.cmpStatus==="error"||c.internalErrorState!==0)&&(f=e)&&clearTimeout(f))})} 
function LI(a){if(!a.l){var b=c=>{try{var d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.F[d.callId](d.returnValue,d.success)}catch(e){}};a.l=b;qb(a.j,"message",b)}} 
var NI=class extends N{constructor(a,b={}){super();this.g=null;this.F={};this.K=0;this.l=null;this.j=a;this.G=b.timeoutMs??500;this.B=b.Fi??!1}i(){this.F={};this.l&&(rb(this.j,"message",this.l),delete this.l);delete this.F;delete this.j;delete this.g;super.i()}addEventListener(a){let b={internalBlockOnErrors:this.B};const c=kb(()=>a(b));let d=0;this.G!==-1&&(d=setTimeout(()=>{b.tcString="tcunavailable";b.internalErrorState=1;c()},this.G));const e=(f,g)=>{clearTimeout(d);f?(b=f,b.internalErrorState= 
CI(b),b.internalBlockOnErrors=this.B,g&&b.internalErrorState===0||(b.tcString="tcunavailable",g||(b.internalErrorState=3))):(b.tcString="tcunavailable",b.internalErrorState=3);a(b)};try{KI(this,"addEventListener",e)}catch(f){b.tcString="tcunavailable",b.internalErrorState=3,d&&(clearTimeout(d),d=0),c()}}removeEventListener(a){a&&a.listenerId&&KI(this,"removeEventListener",null,a.listenerId)}};function kH(a,b,c){const d=jI(a.A);d.callbackQueue=d.callbackQueue||[];d.callbackQueue.push({CONSENT_DATA_READY:()=>{const e=jI(a.A),f=new NI(a.A);JI(f)&&MI(f,g=>{g.cmpId===300&&g.tcString&&g.tcString!=="tcunavailable"&&g.gdprApplies&&b({revocationText:(0,e.getDefaultConsentRevocationText)(),closeText:(0,e.getDefaultConsentRevocationCloseText)(),attestationText:(0,e.getDefaultConsentRevocationAttestationText)(),showRevocationMessage:()=>{(0,e.showRevocationMessage)()}})});c()}})} 
function OI(a,b=!1,c){const d={};try{const f=mI(a.A),g=nI(a.A);d.fc=f;d.fctype=g}catch(f){}let e;try{e=oI(a.A.location.href)}catch(f){}b&&e&&(d.href=e);b=PI(a.g,d);iI(a.A,b,()=>{},()=>{});c&&AI(new BI(a.A),c)}var QI=class{constructor(a,b){this.A=a;this.g=b}start(a=!1,b){if(this.A===this.A.top)try{pI(this.A,"googlefcPresent"),OI(this,a,b)}catch(c){}}};function PI(a,b){a=hd`https://fundingchoicesmessages.google.com/i/${a}`;return id(a,{...b,ers:2})};const RI=new Set(["ARTICLE","SECTION"]);var SI=class{constructor(a){this.g=a}};function TI(a,b){return Array.from(b.classList).map(c=>`${a}=${c}`)}function UI(a){return a.classList.length>0}function VI(a){return RI.has(a.tagName)};var WI=class{constructor(a,b){this.g=a;this.i=b}};function XI(a){return oa(a)&&a.nodeType==1&&a.tagName=="FIGURE"?a:(a=a.parentNode)?XI(a):null};var YI=a=>{var b=a.src;const c=a.getAttribute("data-src")||a.getAttribute("data-lazy-src");(b&&b.startsWith("data:")&&c?c:b||c)?(a.getAttribute("srcset"),b=(b=XI(a))?(b=b.getElementsByTagName("figcaption")[0])?b.textContent:null:null,a=new WI(a,b||a.getAttribute("alt")||null)):a=null;return a};var ZI=class{constructor(){this.map=new Map}clear(){this.map.clear()}delete(a,b){const c=this.map.get(a);return c?(b=c.delete(b),c.size===0&&this.map.delete(a),b):!1}get(a){return[...(this.map.get(a)??[])]}keys(){return this.map.keys()}add(a,b){let c=this.map.get(a);c||this.map.set(a,c=new Set);c.add(b)}get size(){let a=0;for(const b of this.map.values())a+=b.size;return a}values(){const a=this.map;return function(){return function*(){for(const b of a.values())yield*b}()}()}[Symbol.iterator](){const a= 
this.map;return function(){return function*(){for(const [b,c]of a){const d=b,e=c;for(const f of e)yield[d,f]}}()}()}};function $I(a){return[a[0],[...a[1]]]};function aJ(a){return Array.from(bJ(a).map.values()).filter(b=>b.size>=3).map(b=>[...b])}function cJ(a,b){return b.every(c=>{var d=a.g.getBoundingClientRect(c.g);if(d=d.height>=50&&d.width>=a.C){var e=a.g.getBoundingClientRect(c.g);d=a.l;e=new LF(e.left,e.right);d=Math.max(d.start,e.start)<=Math.min(d.end,e.end)}return d&&Gr(a.j,{rb:c.g,kb:dJ,Qb:!0})===null})}function eJ(a){return aJ(a).sort(fJ).find(b=>cJ(a,b))||[]} 
function bJ(a){return gJ(Array.from(a.A.document.getElementsByTagName("IMG")).map(YI).filter(gt),b=>{var c=[...TI("CLASS_NAME",b.g)],d=b.g.parentElement;d=[...(d?TI("PARENT_CLASS_NAME",d):[])];var e=b.g.parentElement?.parentElement;e=[...(e?TI("GRANDPARENT_CLASS_NAME",e):[])];var f=(f=Gr(a.i.g,{rb:b.g,kb:UI}))?TI("NEAREST_ANCESTOR_CLASS_NAME",f):[];return[...c,...d,...e,...f,...(b.i?["HAS_CAPTION=true"]:[]),...(Gr(a.i.g,{rb:b.g,kb:VI})?["ARTICLE_LIKE_ANCESTOR=true"]:[])]})} 
var hJ=class{constructor(a,b,c,d,e){var f=new ws;this.A=a;this.l=b;this.C=c;this.g=f;this.j=d;this.i=e}};function gJ(a,b){const c=new ZI;for(const d of a)for(const e of b(d))c.add(e,d);return c}function dJ(a){return a.tagName==="A"&&a.hasAttribute("href")}function fJ(a,b){return b.length-a.length};function iJ(a){const b=a.l.parentNode;if(!b)throw Error("Image not in the DOM");const c=jJ(a.j),d=kJ(a.j);c.appendChild(d);b.insertBefore(c,a.l.nextSibling);a.B.g().i(e=>{var f=a.j;const g=d.getBoundingClientRect(),h=g.top-e.top,k=g.left-e.left,l=g.width-e.width;e=g.height-e.height;Math.abs(h)<1&&Math.abs(k)<1&&Math.abs(l)<1&&Math.abs(e)<1||(f=f.getComputedStyle(d),r(d,{top:parseInt(f.top,10)-h+"px",left:parseInt(f.left,10)-k+"px",width:parseInt(f.width,10)-l+"px",height:parseInt(f.height,10)-e+"px"}))}); 
return d}function lJ(a){a.g||(a.g=iJ(a));return a.g}var mJ=class extends N{constructor(a,b,c){super();this.j=a;this.l=b;this.B=c;this.g=null}i(){if(this.g){var a=this.g;const b=a.parentNode;b&&b.removeChild(a);this.g=null}super.i()}};function kJ(a){const b=a.document.createElement("div");Du(a,b);r(b,{position:"absolute",left:"0",top:"0",width:"0",height:"0","pointer-events":"none"});return b} 
function jJ(a){const b=a.document.createElement("div");Du(a,b);r(b,{position:"relative",width:"0",height:"0"});return b};function nJ(a){const b=new O(a.dataset.adStatus||null);(new MutationObserver(()=>{b.g(a.dataset.adStatus||null)})).observe(a,{attributes:!0});return Tr(b)};const oJ=["Google Material Icons","Roboto"];function pJ({A:a,wa:b,Cj:c,Sa:d,Xa:e,U:f}){const g=new ys(a,c);c=new mJ(a,c,g);Lr(c,g);a=new qJ(a,d,e,b,c,f);Lr(a,c);a.M()} 
var qJ=class extends N{constructor(a,b,c,d,e,f){super();this.A=a;this.Sa=b;this.Xa=c;this.wa=d;this.j=e;this.U=f;this.g=new O(!1)}M(){const a=rJ(this.A,this.Sa,this.Xa);lJ(this.j).appendChild(a.hj);Dx(this.A,a.ua);nJ(a.ua).i(b=>{if(b!==null){switch(b){case "unfilled":this.dispose();break;case "filled":this.g.g(!0);break;default:this.U?.reportError("Unhandled AdStatus: "+String(b)),this.dispose()}this.U?.Bk(this.wa,b)}});Xr(this.g,!0,()=>void a.Kj.g(!0));a.dj.listen(()=>void this.dispose());a.cj.listen(()=> 
void this.U?.zk(this.wa))}}; 
function rJ(a,b,c){const d=new O(!1),e=a.document.createElement("div");Du(a,e);r(e,{position:"absolute",top:"50%",left:"0",transform:"translateY(-50%)",width:"100%",height:"100%",overflow:"hidden","background-color":"rgba(0, 0, 0, 0.75)",opacity:"0",transition:"opacity 0.25s ease-in-out","box-sizing":"border-box",padding:"40px 5px 5px 5px"});Wr(d,!0,()=>void r(e,{opacity:"1"}));Wr(d,!1,()=>void r(e,{opacity:"0"}));const f=a.document.createElement("div");Du(a,f);r(f,{display:"block",width:"100%",height:"100%"}); 
e.appendChild(f);const {ta:g,Jj:h}=sJ(a,b);f.appendChild(g);e.appendChild(tJ(a,C(c,1)));b=uJ(a,C(c,2));e.appendChild(b.Li);b.gf.listen(()=>void d.g(!1));return{Kj:d,hj:e,ua:h,cj:b.gf,dj:b.gf.delay(a,450)}}function tJ(a,b){const c=a.document.createElement("div");Du(a,c);r(c,{position:"absolute",top:"10px",width:"100%",color:"white","font-family":"Roboto","font-size":"12px","line-height":"16px","text-align":"center"});c.appendChild(a.document.createTextNode(b));return c} 
function uJ(a,b){const c=a.document.createElement("button");c.setAttribute("aria-label",b);Du(a,c);r(c,{position:"absolute",top:"10px",right:"10px",display:"block",cursor:"pointer",width:"24px",height:"24px","font-size":"24px","user-select":"none",color:"white"});b=a.document.createElement("gm-icon");b.className="google-material-icons";b.appendChild(a.document.createTextNode("close"));c.appendChild(b);const d=new es;c.addEventListener("click",()=>void ds(d));return{Li:c,gf:bs(d)}} 
function sJ(a,b){a=zx(a.document,b,null,null,{});return{ta:a.yb,Jj:a.ua}};function vJ({target:a,threshold:b=0}){const c=new wJ;c.M(a,b);return c}var wJ=class extends N{constructor(){super();this.g=new O(!1)}M(a,b){const c=new IntersectionObserver(d=>{for(const e of d)if(e.target===a){this.g.g(e.isIntersecting);break}},{threshold:b});c.observe(a);Mr(this,()=>void c.disconnect())}};function xJ(a){const b=yJ(a.A,Yh(a.g,2)??250,Yh(a.g,3)??300);let c=1;return eJ(a.l).map(d=>({wa:c++,image:d,sb:b(d)}))}function zJ(a,b){const c=vJ({target:b.image.g,threshold:dh(a.g,4)??void 0??.8});a.j.push(c);Xr($r(c.g,a.A,Yh(a.g,5)??3E3,d=>d),!0,()=>{if(a.i<(Yh(a.g,1)??1)){pJ({A:a.A,wa:b.wa,Cj:b.image.g,Sa:a.Sa,Xa:a.Xa,U:a.U});a.i++;if(!(a.i<(Yh(a.g,1)??1)))for(;a.j.length;)a.j.pop()?.dispose();a.U?.Ak(b.wa)}})} 
var BJ=class{constructor(a,b,c,d,e,f){this.A=a;this.Sa=b;this.g=c;this.Xa=d;this.l=e;this.U=f;this.j=[];this.i=0}run(){const a=xJ(this);a.filter(AJ).forEach(b=>void zJ(this,b));this.U?.Ck(a.map(b=>({wa:b.wa,sb:b.sb})))}};function AJ(a){return a.sb.rejectionReasons.length===0}function yJ(a,b,c){const d=ir(a);return e=>{e=e.g.getBoundingClientRect();const f=[];e.width<b&&f.push(1);e.height<c&&f.push(2);e.top<=d&&f.push(3);return{Pb:e.width,Bf:e.height,ej:e.top-d,rejectionReasons:f}}};function CJ(a,b){a.wa=b;return a}var DJ=class{constructor(a,b,c,d,e){this.C=a;this.Sa=b;this.hostname=c;this.j=d;this.l=e;this.errorMessage=this.i=this.wa=this.g=null}};function EJ(a,b){return new DJ(b,a.Sa,a.hostname,a.i,a.l)} 
function FJ(a,b,c){var d=a.j++;a.g===null?(a.g=sm(),a=0):a=sm()-a.g;var e=b.C,f=b.Sa,g=b.hostname,h=b.j,k=b.l.map(encodeURIComponent).join(",");if(b.g){var l={imcnt:b.g.length};var m=Math.min(b.g.length,10);for(let n=0;n<m;n++){const p=`im${n}`;l[`${p}_id`]=b.g[n].wa;l[`${p}_s_w`]=b.g[n].sb.Pb;l[`${p}_s_h`]=b.g[n].sb.Bf;l[`${p}_s_dbf`]=b.g[n].sb.ej;b.g[n].sb.rejectionReasons.length>0&&(l[`${p}_s_rej`]=b.g[n].sb.rejectionReasons.join(","))}}else l=null;yB("abg::imovad",{typ:e,wpc:f,hst:g,pvsid:h,peid:k, 
rate:c,num:d,tim:a,...(b.wa===null?{}:{imid:b.wa}),...(b.i===null?{}:{astat:b.i}),...(b.errorMessage===null?{}:{errm:b.errorMessage}),...l},c)} 
var GJ=class{constructor(a,b,c,d){this.Sa=a;this.hostname=b;this.i=c;this.l=d;this.j=0;this.g=null}Ck(a){var b=EJ(this,"fndi");b.g=a;FJ(this,b,a.length>0?1:.1)}Ak(a){a=CJ(EJ(this,"adpl"),a);FJ(this,a,1)}Bk(a,b){a=CJ(EJ(this,"adst"),a);a.i=b;FJ(this,a,1)}zk(a){a=CJ(EJ(this,"adis"),a);FJ(this,a,1)}reportError(a){var b=EJ(this,"err");b.errorMessage=a;FJ(this,b,.1)}};function HJ(a,b,c){return(a=a.g())&&Nh(a,11)?c.map(d=>d.j()):c.map(d=>d.C(b))};const IJ=new Set([7,1]);var JJ=class{constructor(){this.j=new ZI;this.l=[]}g(a,b){IJ.has(b)||ct($s(EA(a),c=>void this.j.add(c,b)),c=>void this.l.push(c))}i(a,b){for(const c of a)this.g(c,b)}};function KJ(a){return new st(["pedestal_container"],{google_reactive_ad_format:30,google_phwr:2.189,google_ad_width:Math.floor(a),google_ad_format:"autorelaxed",google_full_width_responsive:!0,google_enable_content_recommendations:!0,google_content_recommendation_ui_type:"pedestal"})}var LJ=class{g(a){return KJ(Math.floor(a.i))}};var MJ=class extends G{};function NJ(a,b){var c=b.adClient;if(typeof c!=="string"||!c)return!1;a.Je=c;a.j=!!b.adTest;c=b.pubVars;oa(c)&&(a.D=c);if(Array.isArray(b.fillMessage)&&b.fillMessage.length>0){a.B={};for(const d of b.fillMessage)a.B[d.key]=d.value}a.l=b.adWidth;a.i=b.adHeight;am(a.l)&&am(a.i)||yB("rctnosize",b);return!0}var OJ=class{constructor(){this.B=this.D=this.j=this.Je=null;this.i=this.l=0}H(){return!0}};function PJ(a){try{a.setItem("__storage_test__","__storage_test__");const b=a.getItem("__storage_test__");a.removeItem("__storage_test__");return b==="__storage_test__"}catch(b){return!1}}function QJ(a,b=[]){const c=Date.now();return La(b,d=>c-d<a*1E3)}function RJ(a,b,c){try{const d=a.getItem(c);if(!d)return[];let e;try{e=JSON.parse(d)}catch(f){}if(!Array.isArray(e)||Pa(e,f=>!Number.isInteger(f)))return a.removeItem(c),[];e=QJ(b,e);e.length||a?.removeItem(c);return e}catch(d){return null}} 
function SJ(a,b,c){return b<=0||a==null||!PJ(a)?null:RJ(a,b,c)};function TJ(a,b,c){let d=0;try{var e=d|=gr(a);const h=hr(a),k=a.innerWidth;var f=h&&k?h/k:0;d=e|(f?f>1.05?262144:f<.95?524288:0:131072);d|=jr(a);d|=a.innerHeight>=a.innerWidth?0:8;d|=a.navigator&&/Android 2/.test(a.navigator.userAgent)?1048576:0;var g;if(g=b)g=SJ(c,3600,"__lsv__")?.length!==0;g&&(d|=134217728)}catch(h){d|=32}return d};var UJ=class extends OJ{constructor(){super(...arguments);this.C=!1;this.g=null}H(a){this.C=!!a.enableAma;if(a=a.amaConfig)try{var b=lu(a)}catch(c){b=null}else b=null;this.g=b;return!0}};var VJ={};function WJ(a,b,c){let d=XJ(a,c,b);if(!d)return!0;const e=c.H.i;for(;d.uc&&d.uc.length;){const f=d.uc.shift(),g=py(f.ha);if(g&&!(g<=d.Ed))c.B?.g(f,18);else if(YJ(c,f,{be:d.Ed})){if(d.zd.g.length+1>=e)return c.B?.i(d.uc,19),!0;d=XJ(a,c,b);if(!d)return!0}}return c.j} 
const XJ=(a,b,c)=>{var d=b.H.i,e=b.H.l,f=b.H;f=NB(b.da(),f.g?f.g.Fc:void 0,d);if(f.g.length>=d)return b.B?.i(ZJ(b,f,{types:a},c),19),null;e?(d=f.i||(f.i=mr(f.j).scrollHeight||null),e=!d||d<0?-1:f.i*e-TB(f)):e=void 0;const g=(d=e==null||e>=50)?ZJ(b,f,{types:a},c):null;d||b.B?.i(ZJ(b,f,{types:a},c),18);return{zd:f,Ed:e,uc:g}}; 
VJ[2]=ya(function(a,b){a=ZJ(b,NB(b.da()),{types:a,tb:WA(b.da())},2);if(a.length==0)return!0;for(let c=0;c<a.length;c++)if(YJ(b,a[c]))return!0;return b.j?(b.l.push(11),!0):!1},[0]);VJ[5]=ya(WJ,[0],5);VJ[10]=ya(function(a,b){a=[];const c=b.yd;c.includes(3)&&a.push(2);c.includes(1)&&a.push(0);c.includes(2)&&a.push(1);return WJ(a,10,b)},10); 
VJ[3]=function(a){if(!a.j)return!1;const b=ZJ(a,NB(a.da()),{types:[0],tb:WA(a.da())},3);if(b.length==0)return!0;for(let c=b.length-1;c>=0;c--)if(YJ(a,b[c]))return!0;a.l.push(11);return!0};const aK=a=>{const b=a.da().document.body.getBoundingClientRect().width;$J(a,KJ(b))},cK=(a,b)=>{var c={types:[0],tb:XA(),Ek:[5]};c=ZJ(a,NB(a.da()),c,8);bK(a,c.reverse(),b)},bK=(a,b,c)=>{for(const d of b)if(b=c.g(d.pa),YJ(a,d,{Ke:b}))return!0;return!1}; 
VJ[8]=function(a){var b=a.da().document;if(b.readyState!="complete")return b.addEventListener("readystatechange",()=>VJ[8](a),{once:!0}),!0;if(!a.j)return!1;if(!a.Wd())return!0;b={types:[0],tb:XA(),jg:[2,4,5]};b=ZJ(a,NB(a.da()),b,8);const c=new LJ;if(bK(a,b,c))return!0;if(a.C.Xg)switch(a.C.Fh||0){case 1:cK(a,c);break;default:aK(a)}return!0};VJ[6]=ya(WJ,[2],6);VJ[7]=ya(WJ,[1],7); 
VJ[9]=function(a){const b=XJ([0,2],a,9);if(!b||!b.uc)return a.l.push(17),a.j;for(var c of b.uc){var d=a.C.xf||null;d==null?d=null:(d=qy(c.ha,new dK,new eK(d,a.da())),d=new GA(d,c.ja(),c.pa));if(d&&!(py(d.ha)>b.Ed)&&YJ(a,d,{be:b.Ed,df:!0}))return a=d.ha.K,c=c.ha,a=a.length>0?a[0]:null,c.l=!0,a!=null&&c.K.push(a),!0}a.l.push(17);return a.j};var dK=class{i(a,b,c,d){return Cx(d.document,a,b)}j(a){return ir(a)||0}};var fK=class{constructor(a,b,c){this.i=a;this.g=b;this.zd=c}Aa(a){return this.g?pC(this.i,this.g,a,this.zd):oC(this.i,a,this.zd)}va(){return this.g?16:9}};var gK=class{constructor(a){this.Le=a}Aa(a){return wC(a.document,this.Le)}va(){return 11}};var hK=class{constructor(a){this.Fb=a}Aa(a){return tC(this.Fb,a)}va(){return 13}};var iK=class{Aa(a){return mC(a)}va(){return 12}};var jK=class{constructor(a){this.Sc=a}Aa(){return rC(this.Sc)}va(){return 2}};var kK=class{constructor(a){this.g=a}Aa(){return uC(this.g)}va(){return 3}};var lK=class{Aa(){return xC()}va(){return 17}};var mK=class{constructor(a){this.g=a}Aa(){return qC(this.g)}va(){return 1}};var nK=class{Aa(){return hb(ly)}va(){return 7}};var oK=class{constructor(a){this.jg=a}Aa(){return sC(this.jg)}va(){return 6}};var pK=class{constructor(a){this.g=a}Aa(){return vC(this.g)}va(){return 5}};var qK=class{constructor(a,b){this.minWidth=a;this.maxWidth=b}Aa(){return ya(yC,this.minWidth,this.maxWidth)}va(){return 10}};var rK=class{constructor(a){this.l=a.i.slice(0);this.i=a.g.slice(0);this.j=a.j;this.C=a.l;this.g=a.C}};function sK(a){var b=new tK;b.C=a;b.i.push(new mK(a));return b}function uK(a,b){a.i.push(new oK(b));return a}function vK(a,b){a.i.push(new jK(b));return a}function wK(a,b){a.i.push(new pK(b));return a}function xK(a,b){a.i.push(new kK(b));return a}function yK(a){a.i.push(new nK);return a}function zK(a){a.g.push(new iK);return a}function AK(a,b=0,c,d){a.g.push(new fK(b,c,d));return a} 
function BK(a,b=0,c=Infinity){a.g.push(new qK(b,c));return a}function CK(a){a.g.push(new lK);return a}function DK(a,b=0){a.g.push(new hK(b));return a}function EK(a,b){a.j=b;return a}var tK=class{constructor(){this.j=0;this.l=!1;this.i=[].slice(0);this.g=[].slice(0)}build(){return new rK(this)}};var eK=class{constructor(a,b){this.i=a;this.j=b}g(){var a=this.i,b=this.j;const c=a.D||{};c.google_ad_client=a.Je;c.google_ad_height=ir(b)||0;c.google_ad_width=hr(b)||0;c.google_reactive_ad_format=9;b=new MJ;b=$h(b,1,a.C);a.g&&x(b,2,a.g);c.google_rasc=ii(b);a.j&&(c.google_adtest="on");return new st(["fsi_container"],c)}};var FK=lt(new it(0,{})),GK=lt(new it(1,{})),HK=a=>a===FK||a===GK;function IK(a,b,c){yr(a.g,b)||a.g.set(b,[]);a.g.get(b).push(c)}var JK=class{constructor(){this.g=new Cr}};function KK(a,b){a.H.wpc=b;return a}function LK(a,b){for(let c=0;c<a.C.length;c++)if(a.C[c]==b)return a;a.C.push(b);return a}function MK(a,b){for(let c=0;c<b.length;c++)LK(a,b[c]);return a}function NK(a,b){a.j=a.j?a.j:b;return a} 
var OK=class{constructor(a){this.H={};this.H.c=a;this.C=[];this.j=null;this.B=[];this.F=0}l(a){const b=Vb(this.H);this.F>0&&(b.t=this.F);b.err=this.C.join();b.warn=this.B.join();this.j&&(b.excp_n=this.j.name,b.excp_m=this.j.message&&this.j.message.substring(0,512),b.excp_s=this.j.stack&&mm(this.j.stack,""));b.w=0<a.innerWidth?a.innerWidth:null;b.h=0<a.innerHeight?a.innerHeight:null;return b}};function PK(a,b){if(b&&(a.g.apv=y(b,4),bh(b,Nt,23))){var c=a.g;b=v(b,Nt,23);b=lg(Yg(b,1));c.sat=""+b}return a}function QK(a,b){a.g.afm=b.join(",");return a}var RK=class extends OK{constructor(a){super(a);this.g={}}l(a){try{this.g.su=a.location.hostname}catch(b){this.g.su="_ex"}a=super.l(a);Xb(a,this.g);return a}};function SK(a){return a==null?null:Number.isInteger(a)?a.toString():a.toFixed(3)};function TK(a,b,c,d=30){c.length<=d?a[b]=UK(c):(a[b]=UK(c.slice(0,d)),a[b+"_c"]=c.length.toString())}function UK(a){const b=a.length>0&&typeof a[0]==="string";a=a.map(c=>c?.toString()??"null");b&&(a=a.map(c=>ha(c,"replaceAll").call(c,"~","")));return a.join("~")}function VK(a){return a==null?"null":typeof a==="string"?a:typeof a==="boolean"?a?"1":"0":Number.isInteger(a)?a.toString():a.toFixed(3)};function WK(a,b){a.i.op=VK(b)}function XK(a,b,c){TK(a.i,"fap",b);a.i.fad=VK(c)}function YK(a,b,c){TK(a.i,"fmp",b);a.i.fmd=VK(c)}function ZK(a,b,c){TK(a.i,"vap",b);a.i.vad=VK(c)}function $K(a,b,c){TK(a.i,"vmp",b);a.i.vmd=VK(c)}function aL(a,b,c){TK(a.i,"pap",b);a.i.pad=VK(c)}function bL(a,b,c){TK(a.i,"pmp",b);a.i.pmd=VK(c)}function cL(a,b){TK(a.i,"psq",b)} 
var dL=class extends RK{constructor(a){super(0);Object.assign(this,a);this.i={};this.errors=[]}l(a){a=super.l(a);Object.assign(a,this.i);this.errors.length>0&&(a.e=UK(this.errors));return a}};function eL(a,b,c){const d=b.ha;yr(a.g,d)||a.g.set(d,new fL(Zs(EA(b))??""));c(a.g.get(d))}function gL(a,b){eL(a,b,c=>{c.g=!0})}function hL(a,b){eL(a,b,c=>{c.i=!0})}function iL(a,b){eL(a,b,c=>{c.j=!0});a.O.push(b.ha)}function jL(a,b,c){eL(a,b,d=>{d.lc=c})}function kL(a,b,c){const d=[];let e=0;for(const f of c.filter(b))HK(f.lc??"")?++e:(b=a.i.get(f.lc??"",null),d.push(b));return{list:d.sort((f,g)=>(f??-1)-(g??-1)),mc:e}} 
function lL(a,b){WK(b,a.i.Yc());var c=Br(a.g).filter(f=>(f.Kb.startsWith(FK)?0:1)===0),d=Br(a.g).filter(f=>(f.Kb.startsWith(FK)?0:1)===1),e=kL(a,f=>f.g,c);XK(b,e.list,e.mc);e=kL(a,f=>f.g,d);YK(b,e.list,e.mc);e=kL(a,f=>f.i,c);ZK(b,e.list,e.mc);e=kL(a,f=>f.i,d);$K(b,e.list,e.mc);c=kL(a,f=>f.j,c);aL(b,c.list,c.mc);d=kL(a,f=>f.j,d);bL(b,d.list,d.mc);cL(b,a.O.map(f=>a.g.get(f)?.lc).map(f=>a.i.get(f)??null))} 
function Zn(){var a=L(mL);if(!a.C)return On();const b=Xn(Wn(Vn(Un(Tn(Sn(Rn(Qn(Nn(Mn(new Pn,a.C??[]),a.K??[]),a.B),a.F),a.G),a.X),a.aa),a.H??0),Br(a.g).map(c=>{var d=new Ln;d=fi(d,1,c.Kb);var e=a.i.get(c.lc??"",-1);d=di(d,2,e);d=E(d,3,c.g);return E(d,4,c.i)})),a.O.map(c=>a.g.get(c)?.lc).map(c=>a.i.get(c)??-1));a.j!=null&&E(b,6,a.j);a.l!=null&&ph(b,13,ng(a.l),"0");return b} 
var mL=class{constructor(){this.l=this.K=this.C=null;this.G=this.F=!1;this.j=null;this.aa=this.B=this.X=!1;this.H=null;this.i=new Cr;this.g=new Cr;this.O=[]}};class fL{constructor(a){this.j=this.i=this.g=!1;this.lc=null;this.Kb=a}};var nL=class{constructor(a){this.i=a;this.g=-1}};function oL(a){let b=0;for(;a;)(!b||a.previousElementSibling||a.nextElementSibling)&&b++,a=a.parentElement;return b};function pL(a,b){const c=a.K.filter(d=>Ar(d.Kd).every(e=>d.Kd.get(e)===b.get(e)));return c.length===0?(a.i.push(19),null):c.reduce((d,e)=>d.Kd.Yc()>e.Kd.Yc()?d:e,c[0])}function qL(a,b){b=EA(b);if(!Ys(b))return a.i.push(18),null;b=b.getValue();if(yr(a.j,b))return a.j.get(b);var c=jt(b);c=pL(a,c);a.j.set(b,c);return c} 
var rL=class{constructor(a){this.g=a;this.j=new Cr;this.K=(v(a,ju,2)?.g()||[]).map(b=>{const c=jt(C(b,1)),d=kg(Yg(b,2))??0;return{Kd:c,Ih:d,Kb:C(b,1)}});this.i=[]}G(){const a=L(mL);var b=this.l();a.C=b;b=this.B();a.K=b;b=this.C();b!=null&&(a.l=b);b=!!this.g.i()?.g()?.g();a.G=b;b=new Cr;for(const c of v(this.g,ju,2)?.g()??[])b.set(C(c,1),kg(Yg(c,2))??0);a.i=b}H(){return[...this.i]}l(){return[...this.g.g()]}B(){return[...fh(this.g,4,kg,eh(),void 0,0)]}C(){return v(this.g,du,5)?.g()??null}F(a){const b= 
qL(this,a);b?.Kb!=null&&jL(L(mL),a,b.Kb)}O(a){return a.length==0?!0:.75<=(new Qs(a)).filter(b=>{b=qL(this,b)?.Kb||"";return b!=""&&!(b===FK||b===GK)}).g.length/a.length}};function sL(a,b){return b.g.length==0?b:b.sort((c,d)=>(qL(a.g,c)?.Ih??Number.MAX_VALUE)-(qL(a.g,d)?.Ih??Number.MAX_VALUE))}function tL(a,b){var c=b.pa.g,d=Math,e=d.min;const f=b.ja(),g=b.ha.g();c+=200*e.call(d,20,g==0||g==3?oL(f.parentElement):oL(f));a=a.i;a.g<0&&(a.g=mr(a.i).scrollHeight||0);a=a.g-b.pa.g;a=c+(a>1E3?0:2*(1E3-a));b.ja();return a}function uL(a,b){return b.g.length==0?b:b.sort((c,d)=>tL(a,c)-tL(a,d))} 
function vL(a,b){return b.sort((c,d)=>{const e=c.ha.H,f=d.ha.H;var g;e==null||f==null?g=e==null&&f==null?tL(a,c)-tL(a,d):e==null?1:-1:g=e-f;return g})}var wL=class{constructor(a,b=null){this.i=new nL(a);this.g=b&&new rL(b)}};function xL(a,b,c=0,d){var e=a.i;for(var f of b.l)e=Ps(e,f.Aa(a.j),yL(f.va(),c));f=e=e.apply(lC(a.j));for(const g of b.i)f=Ps(f,g.Aa(a.j),ft([zL(g.va(),c),h=>{d?.g(h,g.va())}]));switch(b.j){case 1:f=uL(a.g,f);break;case 2:f=vL(a.g,f);break;case 3:const g=L(mL);f=sL(a.g,f);e.forEach(h=>{gL(g,h);a.g.g?.F(h)});f.forEach(h=>hL(g,h))}b.C&&(f=Ss(f,Lc(a.j.location.href+a.j.localStorage.google_experiment_mod)));b.g?.length===1&&IK(a.l,b.g[0],{ub:e.g.length,Yh:f.g.length});return Rs(f)} 
var AL=class{constructor(a,b,c=null){this.i=new Qs(a);this.g=new wL(b,c);this.j=b;this.l=new JK}};const yL=(a,b)=>c=>oy(c,b,a),zL=(a,b)=>c=>oy(c.ha,b,a);function BL(a,b,c,d){a:{switch(b){case 0:a=CL(DL(c),a);break a;case 3:a=CL(c,a);break a;case 2:const e=c.lastChild;a=CL(e?e.nodeType==1?e:DL(e):null,a);break a}a=!1}if(d=!a&&!(!d&&b==2&&!EL(c)))b=b==1||b==2?c:c.parentNode,d=!(b&&!Hu(b)&&b.offsetWidth<=0);return d}function CL(a,b){if(!a)return!1;a=td(a,b);if(!a)return!1;a=a.cssFloat||a.styleFloat;return a=="left"||a=="right"}function DL(a){for(a=a.previousSibling;a&&a.nodeType!=1;)a=a.previousSibling;return a?a:null} 
function EL(a){return!!a.nextSibling||!!a.parentNode&&EL(a.parentNode)};const FL=!Pc&&!Jb();function GL(a){if(/-[a-z]/.test("adFormat"))return null;if(FL&&a.dataset){if(!(!Fb("Android")||Kb()||Ib()||Hb()||Fb("Silk")||"adFormat"in a.dataset))return null;a=a.dataset.adFormat;return a===void 0?null:a}return a.getAttribute("data-"+"adFormat".replace(/([A-Z])/g,"-$1").toLowerCase())};function HL(a,b,c){if(!b)return null;const d=Hl(document,"INS");d.id="google_pedestal_container";d.style.width="100%";d.style.zIndex="-1";if(c){var e=a.getComputedStyle(c),f="";if(e&&e.position!=="static"){var g=c.parentNode.lastElementChild;for(f=e.position;g&&g!==c;){if(a.getComputedStyle(g).display!=="none"){f=a.getComputedStyle(g).position;break}g=g.previousElementSibling}}if(c=f)d.style.position=c}b.appendChild(d);if(d){var h=a.document;f=h.createElement("div");f.style.width="100%";f.style.height= 
"2000px";c=ir(a);e=h.body.scrollHeight;a=a.innerHeight;g=h.body.getBoundingClientRect().bottom;d.appendChild(f);var k=f.getBoundingClientRect().top;h=h.body.getBoundingClientRect().top;d.removeChild(f);f=e;e<=a&&c>0&&g>0&&(f=g-h);a=k-h>=.8*f}else a=!1;return a?d:(b.removeChild(d),null)} 
function IL(a){const b=a.document.body;var c=HL(a,b,null);if(c)return c;if(a.document.body){c=Math.floor(a.document.body.getBoundingClientRect().width);for(var d=[{element:a.document.body,depth:0,height:0}],e=-1,f=null;d.length>0;){const h=d.pop(),k=h.element;var g=h.height;h.depth>0&&g>e&&(e=g,f=k);if(h.depth<5)for(g=0;g<k.children.length;g++){const l=k.children[g],m=l.getBoundingClientRect().width;(m==null||c==null?0:m>=c*.9&&m<=c*1.01)&&d.push({element:l,depth:h.depth+1,height:l.getBoundingClientRect().height})}}c= 
f}else c=null;return c?HL(a,c.parentNode||b,c):null}function JL(a){let b=0;try{b|=gr(a),Lb()||(b|=1048576),Math.floor(a.document.body.getBoundingClientRect().width)<=1200||(b|=32768),KL(a)&&(b|=33554432)}catch(c){b|=32}return b}function KL(a){a=a.document.getElementsByClassName("adsbygoogle");for(let b=0;b<a.length;b++)if(GL(a[b])==="autorelaxed")return!0;return!1};function LL(a){const b=lr(a,!0),c=mr(a).scrollWidth,d=mr(a).scrollHeight;let e="unknown";a&&a.document&&a.document.readyState&&(e=a.document.readyState);var f=rr(a);const g=[];var h=[];const k=[],l=[];var m=[],n=[],p=[];let t=0,z=0,w=Infinity,B=Infinity,I=null;var T=BB({hc:!1},a);for(var R of T){T=R.getBoundingClientRect();const $a=b-(T.bottom+f);var U=void 0,X=void 0;if(R.className&&R.className.indexOf("adsbygoogle-ablated-ad-slot")!=-1){U=R.getAttribute("google_element_uid");X=a.google_sv_map;if(!U|| 
!X||!X[U])continue;U=(X=im(X[U]))?X.height:0;X=X?X.width:0}else if(U=T.bottom-T.top,X=T.right-T.left,U<=1||X<=1)continue;g.push(U);k.push(X);l.push(U*X);JB(R)?(z+=1,R.className&&R.className.indexOf("pedestal_container")!=-1&&(I=U)):(w=Math.min(w,$a),n.push(T),t+=1,h.push(U),m.push(U*X));B=Math.min(B,$a);p.push(T)}w=w===Infinity?null:w;B=B===Infinity?null:B;f=ML(n);p=ML(p);h=NL(b,h);n=NL(b,g);m=NL(b*c,m);R=NL(b*c,l);return new OL(a,{fj:e,cg:b,mk:c,kk:d,Uj:t,Ai:z,Ci:PL(g),Di:PL(k),Bi:PL(l),ck:f,bk:p, 
ak:w,Zj:B,mf:h,lf:n,yi:m,xi:R,pk:I})} 
function QL(a,b,c,d){const e=Lb()&&!(hr(a.A)>=900);d=La(d,f=>Qa(a.i,f)).join(",");b={wpc:b,su:c,eid:d,doc:a.g.fj??null,pg_h:RL(a.g.cg),pg_w:RL(a.g.mk),pg_hs:RL(a.g.kk),c:RL(a.g.Uj),aa_c:RL(a.g.Ai),av_h:RL(a.g.Ci),av_w:RL(a.g.Di),av_a:RL(a.g.Bi),s:RL(a.g.ck),all_s:RL(a.g.bk),b:RL(a.g.ak),all_b:RL(a.g.Zj),d:RL(a.g.mf),all_d:RL(a.g.lf),ard:RL(a.g.yi),all_ard:RL(a.g.xi),pd_h:RL(a.g.pk),dt:e?"m":"d"};c={};for(const f of Object.keys(b))b[f]!==null&&(c[f]=b[f]);return c} 
var OL=class{constructor(a,b){this.i="633794002 633794005 21066126 21066127 21065713 21065714 21065715 21065716 42530887 42530888 42530889 42530890 42530891 42530892 42530893".split(" ");this.A=a;this.g=b}};function PL(a){return Qb.apply(null,La(a,b=>b>0))||null}function NL(a,b){return a<=0?null:Ob.apply(null,b)/a} 
function ML(a){let b=Infinity;for(let e=0;e<a.length-1;e++)for(let f=e+1;f<a.length;f++){var c=a[e],d=a[f];c=Math.max(Math.max(0,c.left-d.right,d.left-c.right),Math.max(0,c.top-d.bottom,d.top-c.bottom));c>0&&(b=Math.min(c,b))}return b!==Infinity?b:null}function RL(a){return a==null?null:Number.isInteger(a)?a.toString():a.toFixed(3)};function SL(a){var b=LB({hc:!1,Td:!1},a);a=(ir(a)||0)-rr(a);let c=0;for(let d=0;d<b.length;d++){const e=b[d].getBoundingClientRect();RB(e)&&e.top<=a&&(c+=1)}return c>0} 
function TL(a){const b={};var c=LB({hc:!1,Td:!1,Ff:!1,Gf:!1},a).map(d=>d.getBoundingClientRect()).filter(RB);b.Cg=c.length;c=MB({Ff:!0},a).map(d=>d.getBoundingClientRect()).filter(RB);b.Wg=c.length;c=MB({Gf:!0},a).map(d=>d.getBoundingClientRect()).filter(RB);b.zh=c.length;c=MB({Td:!0},a).map(d=>d.getBoundingClientRect()).filter(RB);b.Gg=c.length;c=(ir(a)||0)-rr(a);c=LB({hc:!1},a).map(d=>d.getBoundingClientRect()).filter(RB).filter(xa(UL,null,c));b.Dg=c.length;a=LL(a);c=a.g.mf!=null?a.g.mf:null;c!= 
null&&(b.th=c);a=a.g.lf!=null?a.g.lf:null;a!=null&&(b.Eg=a);return b}function YJ(a,b,{be:c,Ke:d,df:e}={}){return Ux(997,()=>VL(a,b,{be:c,Ke:d,df:e}),a.g)} 
function ZJ(a,b,c,d){var e=c.tb?c.tb:a.H;const f=YA(e,b.g.length);e=a.C.Fg?e.g:void 0;const g=CK(DK(zK(BK(AK(yK(wK(xK(uK(vK(sK(c.types),a.Ha),c.jg||[]),a.qa),c.Ek||[])),f.ld||void 0,e,b),c.minWidth,c.maxWidth)),f.Fb||void 0));a.aa&&g.g.push(new gK(a.aa));b=1;a.Cb()&&(b=3);EK(g,b);a.C.Sh&&(g.l=!0);return Ux(995,()=>xL(a.i,g.build(),d,a.B||void 0),a.g)}function $J(a,b){const c=IL(a.g);if(c){const d=rt(a.X,b),e=zx(a.g.document,a.G,null,null,{},d);e&&(ox(e.yb,c,2,256),Ux(996,()=>WL(a,e,d),a.g))}} 
function XL(a){return a.K?a.K:a.K=a.g.google_ama_state} 
function VL(a,b,{be:c,Ke:d,df:e}={}){const f=b.ha;if(f.l)return!1;var g=b.ja(),h=f.g();if(!BL(a.g,h,g,a.j))return!1;h=null;f.ed?.includes(6)?(h=Math.round(g.getBoundingClientRect().height),h=new st(null,{google_max_responsive_height:c==null?h:Math.min(c,h),google_full_width_responsive:"false"})):h=c==null?null:new st(null,{google_max_responsive_height:c});c=tt(Ph(f.Ae,2)||0);g=ut(f.H);const k=YL(a,f),l=ZL(a),m=rt(a.X,f.X?f.X.g(b.pa):null,h,d||null,c,g,k,l),n=b.fill(a.G,m);if(e&&!$L(a,n,m)||!Ux(996, 
()=>WL(a,n,m),a.g))return!1;kl(9,[f.H,f.kc]);a.Cb()&&iL(L(mL),b);return!0}function YL(a,b){return Zs(ct(My(b).map(vt),()=>{a.l.push(18)}))}function ZL(a){if(!a.Cb())return null;var b=a.i.g.g?.B();if(b==null)return null;b=b.join("~");a=a.i.g.g?.C()??null;return wt({Ui:b,nj:a})} 
function $L(a,b,c){if(!b)return!1;var d=b.ua,e=d.style.width;d.style.width="100%";var f=d.offsetWidth;d.style.width=e;d=a.g;e=b.ua;c=c&&c.Zc()||{};var g=V(Vu);if(d!==d.top)g=qd(d)?3:16;else if(hr(d)<488)if(d.innerHeight>=d.innerWidth){var h=hr(d);if(!h||(h-f)/h>g)g=6;else{if(g=c.google_full_width_responsive!=="true")b:{h=e.parentElement;for(g=hr(d);h;h=h.parentElement){const k=td(h,d);if(!k)continue;const l=Dd(k.width);if(l&&!(l>=g)&&k.overflow!=="visible"){g=!0;break b}}g=!1}g=g?7:!0}}else g=5;else g= 
4;if(g!==!0)f=g;else{if(!(c=c.google_full_width_responsive==="true"))a:{do if((c=td(e,d))&&c.position=="fixed"){c=!1;break a}while(e=e.parentElement);c=!0}c?(d=hr(d),f=d-f,f=d&&f>=0?!0:d?f<-10?11:f<0?14:12:10):f=9}if(f){a=a.g;b=b.ua;if(d=vx(a,b))f=b.style,f.border=f.borderStyle=f.outline=f.outlineStyle=f.transition="none",f.borderSpacing=f.padding="0",tx(b,d,"0px"),f.width=`${hr(a)}px`,wx(a,b,d),f.zIndex="30";return!0}Lu(b.yb);return!1} 
function WL(a,b,c){if(!b)return!1;try{Dx(a.g,b.ua,c)}catch(d){return Lu(b.yb),a.l.push(6),!1}return!0} 
var aM=class{constructor(a,b,c,d,e={},f=[],g=!1){this.i=a;this.G=b;this.g=c;this.H=d.tb;this.Ha=d.Sc||[];this.X=d.pj||null;this.qa=d.Zi||[];this.aa=d.Le||[];this.C=e;this.j=!1;this.F=[];this.l=[];this.O=this.K=void 0;this.yd=f;this.B=g?new JJ:null}da(){return this.g}Cb(){if((this.i.g.g?.l().length??0)==0)return!1;if(this.O===void 0){const a=EK(zK(yK(sK([0,1,2]))),1).build(),b=Ux(995,()=>xL(this.i,a),this.g);this.O=this.i.g.g?.O(b)||!1}return this.O}Kf(){return!!this.C.Mh}Wd(){return!KL(this.g)}bb(){return this.B}}; 
const UL=(a,b)=>b.top<=a;function bM(a,b,c,d,e,f=0,g=0){this.eb=a;this.ve=f;this.ue=g;this.errors=b;this.Ob=c;this.g=d;this.i=e};var cM=(a,{Wd:b=!1,Kf:c=!1,Gk:d=!1,Cb:e=!1}={})=>{const f=[];d&&f.push(9);if(e){a.includes(4)&&!c&&b&&f.push(8);a.includes(1)&&f.push(1);d=a.includes(3);e=a.includes(2);const g=a.includes(1);(d||e||g)&&f.push(10)}else a.includes(3)&&f.push(6),a.includes(4)&&!c&&b&&f.push(8),a.includes(1)&&f.push(1,5),a.includes(2)&&f.push(7);a.includes(4)&&c&&b&&f.push(8);return f};function dM(a,b,c){a=cM(a,{Wd:b.Wd(),Kf:b.Kf(),Gk:!!b.C.xf,Cb:b.Cb()});return new eM(a,b,c)}function fM(a,b){const c=VJ[b];return c?Ux(998,()=>c(a.g),a.C):(a.g.F.push(12),!0)}function gM(a,b){return new Promise(c=>{setTimeout(()=>{c(fM(a,b))})})}function hM(a){a.g.j=!0;return Promise.all(a.i.map(b=>gM(a,b))).then(b=>{b.includes(!1)&&a.g.F.push(5);a.i.splice(0,a.i.length)})}var eM=class{constructor(a,b,c){this.l=a.slice(0);this.i=a.slice(0);this.j=Ra(this.i,1);this.g=b;this.C=c}};var iM=class{constructor(a){this.g=a;this.exception=void 0}};function jM(a){return hM(a).then(()=>{var b=a.g.i.i.filter(ly).g.length;var c=a.g.F.slice(0);var d=a.g;d=[...d.l,...(d.i.g.g?.H()||[])];b=new bM(b,c,d,a.g.i.i.g.length,a.g.i.l.g,a.g.i.i.filter(ly).filter(my).g.length,a.g.i.i.filter(my).g.length);return new iM(b)})};var kM=class{g(){return new st([],{google_reactive_ad_format:40,google_tag_origin:"qs"})}};var lM=class{g(){return new st(["adsbygoogle-resurrected-ad-slot"],{})}};function mM(a){return Iu(a.g.document).map(b=>{const c=new ey(b,3);b=new gy(Fx(a.g,b));return new ky(c,b,a.i,!1,0,[],null,a.g,null)})}var nM=class{constructor(a){var b=new lM;this.g=a;this.i=b||null}};const oM={yg:"10px",bf:"10px"};function pM(a){return xr(a.g.document.querySelectorAll("INS.adsbygoogle-placeholder")).map(b=>new ky(new ey(b,1),new cy(oM),a.i,!1,0,[],null,a.g,null))}var qM=class{constructor(a,b){this.g=a;this.i=b||null}};function rM(a,b){const c=[];b.forEach((d,e)=>{c.push(ha(e,"replaceAll").call(e,"~","_")+"--"+d.map(f=>Number(f)).join("_"))});TK(a.g,"cnstr",c,80)}var sM=class extends OK{constructor(){super(-1);this.g={}}l(a){a=super.l(a);Object.assign(a,this.g);return a}};var tM=class extends Error{constructor(a,b,c){super(a);this.g=b;this.i=c}};function uM(a,b,c){return a==null?new tM(b+"ShouldNotBeNull",2,c):a==0?new tM(b+"ShouldNotBeZero",3,c):a<-1?new tM(b+"ShouldNotBeLessMinusOne",4,c):null}function vM(a,b,c){const d=uM(c.Wc,"gapsMeasurementWindow",1)||uM(c.ac,"gapsPerMeasurementWindow",2)||uM(c.oc,"maxGapsToReport",3);return d!=null?Ws(d):c.Ne||c.ac!=-1||c.oc!=-1?Us(new wM(a,b,c)):Ws(new tM("ShouldHaveLimits",1,0))} 
function xM(a){return XL(a.j)&&XL(a.j).placed||[]}function yM(a){return xM(a).map(b=>Hs(Fs(b.element,a.g)))}function zM(a){return xM(a).map(b=>b.index)}function AM(a,b){const c=b.ha;return!a.B&&c.j&&Ph(c.j,8)!=null&&Ph(c.j,8)==1?[]:c.l?(c.K||[]).map(d=>Hs(Fs(d,a.g))):[Hs(new Gs(b.pa.g,0))]}function BM(a){a.sort((e,f)=>e.g-f.g);const b=[];let c=0;for(let e=0;e<a.length;++e){var d=a[e];let f=d.g;d=d.g+d.i;f<=c?c=Math.max(c,d):(b.push(new Gs(c,f-c)),c=d)}return b} 
function CM(a,b){b=b.map(c=>{var d=new rn;d=ai(d,1,c.g);c=c.getHeight();return ai(d,2,c)});return tn(sn(new un,a),b)}function DM(a){const b=zh(a,rn,2,eh()).map(c=>`G${Qh(c,1)}~${c.getHeight()}`);return`W${Qh(a,1)}${b.join("")}`}function EM(a,b){const c=[];let d=0;for(const e of Ar(b)){const f=b.get(e);f.sort((g,h)=>h.getHeight()-g.getHeight());a.G||f.splice(a.C,f.length);!a.H&&d+f.length>a.i&&f.splice(a.i-d,f.length);c.push(CM(e,f));d+=f.length;if(!a.H&&d>=a.i)break}return c} 
function FM(a){const b=zh(a,un,5,eh()).map(c=>DM(c));return`M${Qh(a,1)}H${Qh(a,2)}C${Qh(a,3)}B${Number(!!A(a,4))}${b.join("")}`} 
function GM(a){var b=HA(Rs(a.j.i.i),a.g),c=yM(a),d=new Dr(zM(a));for(var e=0;e<b.length;++e)if(!d.contains(e)){var f=AM(a,b[e]);c.push(...f)}c.push(new Gs(0,0));c.push(Hs(new Gs(mr(a.g).scrollHeight,0)));b=BM(c);c=new Cr;for(d=0;d<b.length;++d)e=b[d],f=a.F?0:Math.floor(e.g/a.l),yr(c,f)||c.set(f,[]),c.get(f).push(e);b=EM(a,c);c=new vn;c=ai(c,1,a.i);c=ai(c,2,a.l);c=ai(c,3,a.C);a=$h(c,4,a.B);return Kh(a,5,b)}function HM(a){a=GM(a);return FM(a)} 
var wM=class{constructor(a,b,c){this.F=c.Wc==-1;this.l=c.Wc;this.G=c.ac==-1;this.C=c.ac;this.H=c.oc==-1;this.i=c.oc;this.B=c.Ef;this.j=b;this.g=a}};function zu(a,b,c){let d=b.ob;b.ic&&M(rv)&&(d=1,"r"in c&&(c.r+="F"));d<=0||(!b.Bb||"pvc"in c||(c.pvc=Zd(a.g)),yB(b.dc,c,d))}function IM(a,b,c){c=c.l(a.g);b.Bb&&(c.pvc=Zd(a.g));0<=b.ob&&(c.r=b.ob,zu(a,b,c))}var JM=class{constructor(a){this.g=a}};const KM={google_ad_channel:!0,google_ad_host:!0};function LM(a,b){a.location.href&&a.location.href.substring&&(b.url=a.location.href.substring(0,200));yB("ama",b,.01)}function MM(a){const b={};vd(KM,(c,d)=>{d in a&&(b[d]=a[d])});return b};function NM(a){const b=/[a-zA-Z0-9._~-]/,c=/%[89a-zA-Z]./;return a.replace(/(%[a-zA-Z0-9]{2})/g,d=>{if(!d.match(c)){const e=decodeURIComponent(d);if(e.match(b))return e}return d.toUpperCase()})}function OM(a){let b="";const c=/[/%?&=]/;for(let d=0;d<a.length;++d){const e=a[d];b=e.match(c)?b+e:b+encodeURIComponent(e)}return b};function PM(a,b){a=Vh(a,2);if(!a)return!1;for(let c=0;c<a.length;c++)if(a[c]==b)return!0;return!1}function QM(a,b){a=OM(NM(a.location.pathname)).replace(/(^\/)|(\/$)/g,"");const c=xd(a),d=RM(a);return b.find(e=>{const f=bh(e,Et,7)?Tf(Yg(v(e,Et,7),1)):Tf(Yg(e,1));e=bh(e,Et,7)?Ph(v(e,Et,7),2):2;if(typeof f!=="number")return!1;switch(e){case 1:return f==c;case 2:return d[f]||!1}return!1})||null}function RM(a){const b={};for(;;){b[xd(a)]=!0;if(!a)return b;a=a.substring(0,a.lastIndexOf("/"))}};function SM(a,b){try{b.removeItem("google_ama_config")}catch(c){LM(a,{lserr:1})}};var UM=(a,b,c,d,e=null,f=null)=>{TM(a,new JM(a),b,c,d,e,f)},TM=(a,b,c,d,e,f=null,g=null)=>{if(c)if(d){var h=xF(d,e);try{const k=new VM(a,b,c,d,e,h,f,g);Ux(990,()=>WM(k),a)}catch(k){jl()&&kl(15,[k]),IM(b,ru,NK(LK(KK(QK(PK(new RK(0),d),h),c),1),k)),$H(L(XH),co(new mo,on(1)))}}else IM(b,ru,LK(KK(new RK(0),c),8)),$H(L(XH),co(new mo,on(8)));else IM(b,ru,LK(new RK(0),9)),$H(L(XH),co(new mo,on(9)))}; 
function WM(a){a.K.forEach(b=>{switch(b){case 0:Ux(991,()=>XM(a),a.g);break;case 1:Ux(1073,()=>{qF(new wF(a.g,a.B,a.l,a.C,a.i.ca))},a.g);break;case 2:YM(a);break;case 7:Ux(1203,()=>{var c=v(a.l,cu,34);if(c){var d=a.g,e=a.C,f=Wg(c);c=d.location.hostname;var g=v(f,bu,1)?.g()??[];c=new GJ(e,c,Zd(q),g);if(g=v(f,bu,1))if(f=v(f,au,2)){Js(d,oJ);const l=new Ir;var h=d.innerWidth;var k=.375*h;h=new LF(k,h-k);k=d.innerWidth;k=hr(d)>=900?.2*k:.5*k;(new BJ(d,e,g,f,new hJ(d,h,k,l,new SI(l)),c)).run()}else c.reportError("No messages"); 
else c.reportError("No settings")}},a.g)}})} 
function XM(a){var b=M(dv)?void 0:a.i.sk;let c=null;c=M(dv)?WA(a.g):UA(a.g,b);if(a.i.ca&&bh(a.i.ca,Dt,10)){var d=dh(a.i.ca.g(),1);d!==null&&d!==void 0&&(c=LA(a.g,d,b));M(vv)&&a.i.ca.g()?.g()===2&&(c=TA(a.i.ca.g(),c))}bh(a.l,At,26)&&(c=ZA(c,v(a.l,At,26),a.g));c=aB(c,a.g);b=a.i.ca?Vh(a.i.ca,6):[];d=a.i.ca?zh(a.i.ca,Jt,5,eh()):[];const e=a.i.ca?Vh(a.i.ca,2):[],f=Ux(993,()=>{var g=a.l,h=zh(g,Zt,1,eh()),k=a.i.ca&&PM(a.i.ca,1)?"text_image":"text",l=new kM,m=jy(h,a.g,{Ei:l,Ij:new hy(k)});h.length!=m.length&& 
a.G.push(13);m=m.concat(pM(new qM(a.g,l)));h=M(sv);l=v(g,ku,24)?.i()?.g()?.g()||!1;if(h||l)h=mM(new nM(a.g)),l=L(mL),m=m.concat(h),l.X=!0,l.H=h.length,a.F==="n"&&(a.F=v(g,ku,24)?.g()?.length?"o":"p");h=M(vv)&&a.i.ca.g()?.g()===2&&a.i.ca.g()?.i();h=M(bv)||h;a:{if(l=v(g,Vt,6))for(n of l.g())if(bh(n,ht,4)){var n=!0;break a}n=!1}h&&n?(n=m.concat,h=a.g,(l=v(g,Vt,6))?(h=Jy(l.g(),h),k=HJ(g,k,h)):k=[],k=n.call(m,k)):(n=m.concat,h=a.g,(l=v(g,Vt,6))?(h=Iy(l.g(),h),k=HJ(g,k,h)):k=[],k=n.call(m,k));m=k;g=v(g, 
ku,24);return new AL(m,a.g,g)},a.g);a.j=new aM(f,a.C,a.g,{tb:c,pj:a.X,Sc:a.i.Sc,Zi:b,Le:d},ZM(a),e,M(rv));XL(a.j)?.optimization?.ablatingThisPageview&&!a.j.Cb()&&(Ex(a.g),L(mL).B=!0,a.F="f");a.H=dM(e,a.j,a.g);Ux(992,()=>jM(a.H),a.g).then(Ux(994,()=>a.qa.bind(a),a.g),a.aa.bind(a))}function YM(a){const b=v(a.l,$t,18);b&&(new gI(a.g,new QI(a.g,a.C),b,new JD(a.g),zh(a.l,Zt,1,eh()))).run()} 
function ZM(a){const b=M(uv);if(!a.l.g())return{Sh:b,Xg:!1,Mh:!1,qk:0,Fh:0,Fg:$M(a),xf:a.O};const c=a.l.g();return{Sh:b||A(c,14),Xg:A(c,5),Mh:A(c,6),qk:Sh(c,8),Fh:Ph(c,10),Fg:$M(a),xf:a.O}}function $M(a){return M(lv)||M(vv)&&a.i.ca?.g()?.g()===2?!1:a.i.ca&&bh(a.i.ca,Dt,10)?(dh(a.i.ca.g(),1)||0)>=.5:!0} 
function aN(a,b){var c=new RK(b.eb);c.g.pp=b.ue;c.g.ppp=b.ve;c.g.ppos=b.placementPositionDiffs;c.g.eatf=b.Lc;c.g.eatfAbg=b.Mc;c.g.reatf=b.ec;c.g.a=a.H.l.slice(0).join(",");c=QK(PK(c,a.l),a.K);var d=b.Ja;d&&(c.g.as_count=d.Cg,c.g.d_count=d.Wg,c.g.ng_count=d.zh,c.g.am_count=d.Gg,c.g.atf_count=d.Dg,c.g.mdns=SK(d.th),c.g.alldns=SK(d.Eg));d=b.tc;d!=null&&(c.g.allp=d);if(d=b.Od){var e=[];for(var f of Ar(d))if(d.get(f).length>0){var g=d.get(f)[0];e.push("("+[f,g.ub,g.Yh].join()+")")}c.g.fd=e.join(",")}f= 
b.cg;f!=null&&(c.g.pgh=f);c.g.abl=b.gh;c.g.rr=a.F;a=MK(MK(KK(c,a.C),b.errors),a.G);c=b.Ob;for(e=0;e<c.length;e++)a:{f=a;d=c[e];for(g=0;g<f.B.length;g++)if(f.B[g]==d)break a;f.B.push(d)}b.exception!==void 0&&LK(NK(a,b.exception),1);return a} 
function bN(a,b){var c=aN(a,b);IM(a.B,b.errors.length>0||a.G.length>0||b.exception!==void 0?ru:qu,c);if(v(a.l,ku,24)){a.j.i.g.g?.G();b=XL(a.j);const d=L(mL);d.j=!!b?.optimization?.ablationFromStorage;b?.optimization?.ablatingThisPageview&&(d.F=!0);d.aa=!!b?.optimization?.availableAbg;b=L(mL);c=new dL(c);b.C?(c.i.sl=UK(b.C??[]),c.i.daaos=UK(b.K??[]),c.i.ab=VK(b.F),c.i.rr=VK(b.X),c.i.oab=VK(b.G),b.j!=null&&(c.i.sab=VK(b.j)),b.B&&(c.i.fb=VK(b.B)),c.i.ls=VK(b.aa),WK(c,b.i.Yc()),b.H!=null&&(c.i.rp=VK(b.H)), 
b.l!=null&&(c.i.expl=VK(b.l)),lL(b,c)):c.errors.push("irr");IM(a.B,tu,c)}c=a.j?.bb();M(rv)&&c!=null&&(c=new Map([...c.j.map.entries()].map($I)),b=new sM,rM(b,c),IM(a.B,vu,b))}function cN(a,b){if(M(fv)&&a.j!=null){var c=vM(a.g,a.j,{Wc:V(qv),ac:V(pv),oc:V(hv),Ef:!0,Ne:!1});if(Ys(c))a=new yn,c=GM(c.getValue()),a=Ah(a,2,xn,c),x(b,16,a);else{var d=c.g;a=new yn;c=a.setError;var e=new wn;e=gi(e,2,d.i);d=gi(e,1,d.g);a=c.call(a,d);x(b,16,a)}}} 
function dN(a,b){const c=L(XH);if(c.i){var d=new mo,e=b.Ob.filter(g=>g!==null),f=a.G.concat(b.errors,b.exception?[1]:[]).filter(g=>g!==null);io(fo(lo(ko(jo(ho(go($n(bo(eo(ao(d,a.H.l.slice(0).map(g=>{var h=new nn;return gi(h,1,g)})),e.map(g=>{var h=new qn;return gi(h,1,g)})),f.map(g=>on(g))),v(a.l,Nt,23)?.g()),b.eb),b.tc),b.ec),b.Lc),b.Mc),a.K.map(g=>g.toString())),Fn(En(Dn(Cn(Bn(An(zn(new Gn,b.Ja?.Cg),b.Ja?.Wg),b.Ja?.zh),b.Ja?.Gg),b.Ja?.Dg),b.Ja?.th),b.Ja?.Eg));if(b.Od)for(let g of Ar(b.Od)){e=new nh; 
for(let h of b.Od.get(g))Kn(e,In(Hn(new Jn,h.ub),h.Yh));mh(d).set(g.toString(),e)}v(a.l,ku,24)&&Yn(d);cN(a,d);$H(c,d)}} 
function eN(a,b,c){{var d=XL(a.j),e=b.g;const f=e.g,g=e.ue;let h=e.eb,k=e.ve,l=e.errors.slice(),m=e.Ob.slice(),n=b.exception;const p=IH(a.g).had_ads_ablation??!1;d?(d.numAutoAdsPlaced?h+=d.numAutoAdsPlaced:a.H.j&&m.push(13),d.exception!==void 0&&(n=d.exception),d.numPostPlacementsPlaced&&(k+=d.numPostPlacementsPlaced),c={eb:h,ue:g,ve:k,tc:f,errors:e.errors.slice(),Ob:m,exception:n,ec:c,Lc:!!d.eatf,Mc:!!d.eatfAbg,gh:p}):(m.push(12),a.H.j&&m.push(13),c={eb:h,ue:g,ve:k,tc:f,errors:l,Ob:m,exception:n, 
ec:c,Lc:!1,Mc:!1,gh:p})}c.Ja=TL(a.j.g);if(b=b.g.i)c.Od=b;c.cg=mr(a.g).scrollHeight;if(jl()||v(a.l,Mt,25)?.i()){d=Rs(a.j.i.i);b=[];for(const f of d){d={};e=f.O;for(const g of Ar(e))d[g]=e.get(g);d={anchorElement:f.F.i(f.i),position:f.g(),clearBoth:f.G,locationType:f.kc,placed:f.l,placementProto:f.j?Lg(f.j):null,articleStructure:f.C?Lg(f.C):null,rejectionReasons:d};b.push(d)}kl(14,[{placementIdentifiers:b},a.j.G,c.Ja])}return c} 
function fN(a,b){var c=a.j.g;c=c.googleSimulationState=c.googleSimulationState||{};c.amaConfigPlacementCount=b.tc;c.numAutoAdsPlaced=b.eb;c.hasAtfAd=b.ec;b.exception!==void 0&&(c.exception=b.exception);if(a.j!=null)if(a=vM(a.g,a.j,{Wc:-1,ac:-1,oc:-1,Ef:!0,Ne:!0}),Ys(a))c.placementPositionDiffs=HM(a.getValue()),b=GM(a.getValue()),a=new yn,a=Ah(a,2,xn,b),c.placementPositionDiffsReport=ii(a);else{c.placementPositionDiffs="E"+a.g.message;var d=a.g;a=new yn;b=a.setError;var e=new wn;e=gi(e,2,d.i);d=gi(e, 
1,d.g);a=b.call(a,d);c.placementPositionDiffsReport=ii(a)}}function gN(a,b){bN(a,{eb:0,tc:void 0,errors:[],Ob:[],exception:b,ec:void 0,Lc:void 0,Mc:void 0,Ja:void 0});dN(a,{eb:0,tc:void 0,errors:[],Ob:[],exception:b,ec:void 0,Lc:void 0,Mc:void 0,Ja:void 0})} 
var VM=class{constructor(a,b,c,d,e,f,g,h){this.g=a;this.B=b;this.C=c;this.l=d;this.i=e;this.K=f;this.X=g||null;this.G=[];this.O=h;this.F="n"}qa(a){try{const b=SL(this.j.g)||void 0;pu({nf:b},this.g);const c=eN(this,a,SL(this.j.g));bh(this.l,Mt,25)&&Nh(v(this.l,Mt,25),1)&&fN(this,c);bN(this,c);dN(this,c);wB(753,()=>{if(M(ev)&&this.j!=null){var d=vM(this.g,this.j,{Wc:V(qv),ac:V(pv),oc:V(hv),Ef:!0,Ne:!1}),e=Vb(c);Ys(d)?(d=HM(d.getValue()),e.placementPositionDiffs=d):e.placementPositionDiffs="E"+d.g.message; 
e=aN(this,e);IM(this.B,su,e)}})()}catch(b){gN(this,b)}}aa(a){gN(this,a)}};var hN=class extends G{},iN=Uj(hN);function jN(a){try{var b=a.localStorage.getItem("google_auto_fc_cmp_setting")||null}catch(d){b=null}const c=b;return c?Xs(()=>iN(c)):Us(null)};function kN(a){this.g=a||{cookie:""}}aa=kN.prototype; 
aa.set=function(a,b,c){let d,e,f,g=!1,h;typeof c==="object"&&(h=c.Nh,g=c.ze||!1,f=c.domain||void 0,e=c.path||void 0,d=c.ce);if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');d===void 0&&(d=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(e?";path="+e:"")+(d<0?"":d==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+d*1E3)).toUTCString())+(g?";secure":"")+(h!=null?";samesite="+h:"")}; 
aa.get=function(a,b){const c=a+"=",d=(this.g.cookie||"").split(";");for(let e=0,f;e<d.length;e++){f=zb(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};function lN(a,b,c,d){a.get(b);a.set(b,"",{ce:0,path:c,domain:d})}aa.isEmpty=function(){return!this.g.cookie};aa.Yc=function(){return this.g.cookie?(this.g.cookie||"").split(";").length:0}; 
aa.clear=function(){var a=(this.g.cookie||"").split(";");const b=[],c=[];let d,e;for(let f=0;f<a.length;f++)e=zb(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));for(a=b.length-1;a>=0;a--)lN(this,b[a])};function mN(a,b=window){if(a.g())try{return b.localStorage}catch{}return null}let nN;function oN(a){return nN?nN:a.origin==="null"?nN=!1:nN=pN(a)}function pN(a){if(!a.navigator.cookieEnabled)return!1;const b=new kN(a.document);if(!b.isEmpty())return!0;b.set("TESTCOOKIESENABLED","1",{ce:60,Nh:a.isSecureContext?"none":void 0,ze:a.isSecureContext||void 0});if(b.get("TESTCOOKIESENABLED")!=="1")return!1;lN(b,"TESTCOOKIESENABLED");return!0} 
function qN(a,b){b=b.origin!=="null"?b.document.cookie:null;return b===null?null:(new kN({cookie:b})).get(a)||""}function rN(a,b,c,d){d.origin!=="null"&&(d.isSecureContext&&(c={...c,Nh:"none",ze:!0}),(new kN(d.document)).set(a,b,c))};function sN(a,b){return $h(a,5,b)}function tN(a,b){return $h(a,8,b)}function uN(a,b){return $h(a,12,b)}function vN(a,b){return $h(a,16,b)}var wN=class extends G{j(){return y(this,1)!=null}i(){return y(this,2)!=null}l(){return A(this,3)}g(){return A(this,5)}};var zN=({A:a,Fa:b,jh:c=!1,kh:d=!1})=>xN({A:a,Fa:b,jh:c,kh:d})?(b=zH(uH(),24))?yN(a,sN(new wN,EI(b))):Ws(Error("tcunav")):yN(a,sN(new wN,!0));function xN({A:a,Fa:b,jh:c,kh:d}){if(!(d=!d&&JI(new NI(a)))){if(c=!c){if(b){a=jN(a);if(Ys(a))if((a=a.getValue())&&Ph(a,1)!=null)b:switch(a=D(a,1),a){case 1:a=!0;break b;default:throw Error("Unhandled AutoGdprFeatureStatus: "+a);}else a=!1;else zB(806,a.g),a=!1;b=!a}c=b}d=c}return d?!0:!1}function yN(a,b){return(a=mN(b,a))?Us(a):Ws(Error("unav"))};var AN=class{constructor(a,b,c,d,e){this.g=a;this.C=b;this.l=c;this.i=!1;this.j=d;this.B=e}run(){const a=this.l;if(this.i){var b=this.g;if(this.j&&!oH(a)){var c=new hN;c=gi(c,1,1)}else c=null;if(c){c=ii(c);try{b.localStorage.setItem("google_auto_fc_cmp_setting",c)}catch(d){}}}b=oH(a)&&(this.j||this.B);a&&b&&(new gI(this.g,new QI(this.g,this.C),a,new JD(this.g))).run()}};var BN=class extends G{getName(){return D(this,1)}getVersion(){return C(this,3)}};var CN=[0,Pj,-1,Jj];var DN=class extends G{wj(){return D(this,3)}};const EN={"-":0,Y:2,N:1};var FN=class extends G{getVersion(){return Qh(this,2)}};function GN(a){return a.includes("~")?a.split("~").slice(1):[]};function HN(a){return me(a.length%4!==0?a+"A":a).map(b=>b.toString(2).padStart(8,"0")).join("")}function IN(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);return parseInt(a,2)}function JN(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);const b=[1,2,3,5];let c=0;for(let d=0;d<a.length-1;d++)b.length<=d&&b.push(b[d-1]+b[d-2]),c+=parseInt(a[d],2)*b[d];return c}function KN(a,b){a=HN(a);return a.length<b?a.padEnd(b,"0"):a};function LN(a){var b=HN(a),c=IN(b.slice(0,6));a=IN(b.slice(6,12));var d=new FN;c=bi(d,1,c);a=bi(c,2,a);b=b.slice(12);c=IN(b.slice(0,12));d=[];let e=b.slice(12).replace(/0+$/,"");for(let k=0;k<c;k++){if(e.length===0)throw Error(`Found ${k} of ${c} sections [${d}] but reached end of input [${b}]`);var f=IN(e[0])===0;e=e.slice(1);var g=MN(e,b),h=d.length===0?0:d[d.length-1];h=JN(g)+h;e=e.slice(g.length);if(f)d.push(h);else{f=MN(e,b);g=JN(f);for(let l=0;l<=g;l++)d.push(h+l);e=e.slice(f.length)}}if(e.length> 
0)throw Error(`Found ${c} sections [${d}] but has remaining input [${e}], entire input [${b}]`);return oh(a,3,d,Qf)}function MN(a,b){const c=a.indexOf("11");if(c===-1)throw Error(`Expected section bitstring but not found in [${a}] part of [${b}]`);return a.slice(0,c+2)};var NN=class extends G{g(){return D(this,1)}i(){return D(this,2)}};var ON=class extends G{};var PN=class extends G{getVersion(){return Qh(this,1)}};var QN=class extends G{};function RN(a){var b=new SN;return x(b,1,a)}var SN=class extends G{};const TN=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],UN=6+TN.reduce((a,b)=>a+b);var VN=class extends G{};var WN=class extends G{getVersion(){return Qh(this,1)}};var XN=class extends G{};function YN(a){var b=new ZN;return x(b,1,a)}var ZN=class extends G{};const $N=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],aO=6+$N.reduce((a,b)=>a+b);var bO=class extends G{g(){return D(this,1)}i(){return D(this,2)}j(){return D(this,3)}};var cO=class extends G{};var dO=class extends G{getVersion(){return Qh(this,1)}};var eO=class extends G{};function fO(a){var b=new gO;return x(b,1,a)}var gO=class extends G{};const hO=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],iO=6+hO.reduce((a,b)=>a+b);var jO=class extends G{g(){return D(this,1)}i(){return D(this,2)}j(){return D(this,3)}};var kO=class extends G{};var lO=class extends G{getVersion(){return Qh(this,1)}};const mO=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],nO=6+mO.reduce((a,b)=>a+b);var oO=class extends G{g(){return D(this,1)}i(){return D(this,2)}};var pO=class extends G{};var qO=class extends G{getVersion(){return Qh(this,1)}};var rO=class extends G{};function sO(a){var b=new tO;return x(b,1,a)}var tO=class extends G{};const uO=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],vO=6+uO.reduce((a,b)=>a+b);var wO=class extends G{};var xO=class extends G{getVersion(){return Qh(this,1)}};const yO=[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2],zO=6+yO.reduce((a,b)=>a+b);var AO=class extends G{};function BO(a,b){return oh(a,1,b,Of)}function CO(a,b){return oh(a,2,b,Of)}function DO(a,b){return oh(a,3,b,Qf)}function EO(a,b){oh(a,4,b,Qf)}var FO=class extends G{};function GO(a,b){return di(a,1,b)}function HO(a){var b=Number;{var c=Yg(a,1);const d=typeof c;c=c==null?c:d==="bigint"?String(Df(64,c)):Nf(c)?d==="string"?Vf(c):$f(c):void 0}b=b(c??"0");a=Qh(a,2);return new Date(b*1E3+a/1E6)}var IO=class extends G{};function JO(a,b){return bi(a,1,b)}function KO(a,b){return x(a,2,b)}function LO(a,b){return x(a,3,b)}function MO(a,b){return bi(a,4,b)}function NO(a,b){return bi(a,5,b)}function OO(a,b){return bi(a,6,b)}function PO(a,b){return fi(a,7,b)}function QO(a,b){return bi(a,8,b)}function RO(a,b){return bi(a,9,b)}function SO(a,b){return E(a,10,b)}function TO(a,b){return E(a,11,b)}function UO(a,b){return oh(a,12,b,Of)}function VO(a,b){return oh(a,13,b,Of)}function WO(a,b){return oh(a,14,b,Of)} 
function XO(a,b){return E(a,15,b)}function YO(a,b){return fi(a,16,b)}function ZO(a,b){return oh(a,17,b,Qf)}function $O(a,b){return oh(a,18,b,Qf)}function aP(a,b){return Kh(a,19,b)}var bP=class extends G{getVersion(){return Qh(this,1)}};var cP=class extends G{};var dP="a".charCodeAt(),eP=Ub(Zq),fP=Ub($q);function gP(a,b){if(a.g+b>a.i.length)throw Error("Requested length "+b+" is past end of string.");const c=a.i.substring(a.g,a.g+b);a.g+=b;return parseInt(c,2)}function hP(a){a=gP(a,36);var b=GO(new IO,Math.floor(a/10));return bi(b,2,a%10*1E8)}function iP(a){return String.fromCharCode(dP+gP(a,6))+String.fromCharCode(dP+gP(a,6))}function jP(a){let b=gP(a,12);const c=[];for(;b--;){var d=!!gP(a,1)===!0,e=gP(a,16);if(d)for(d=gP(a,16);e<=d;e++)c.push(e);else c.push(e)}c.sort((f,g)=>f-g);return c} 
function kP(a,b,c){const d=[];for(let e=0;e<b;e++)if(gP(a,1)){const f=e+1;if(c&&c.indexOf(f)===-1)throw Error(`ID: ${f} is outside of allowed values!`);d.push(f)}return d}function lP(a){const b=gP(a,16);return!!gP(a,1)===!0?(a=jP(a),a.forEach(c=>{if(c>b)throw Error(`ID ${c} is past MaxVendorId ${b}!`);}),a):kP(a,b)}function mP(a){const b=[];let c=gP(a,12);for(;c--;){const k=gP(a,6);var d=gP(a,2),e=jP(a),f=b,g=f.push;var h=new AO;h=F(h,1,k);d=F(h,2,d);e=oh(d,3,e,Qf);g.call(f,e)}return b} 
var nP=class{constructor(a){if(/[^01]/.test(a))throw Error(`Input bitstring ${a} is malformed!`);this.i=a;this.g=0}skip(a){this.g+=a}};var oP=a=>{try{const b=me(a).map(f=>f.toString(2).padStart(8,"0")).join(""),c=new nP(b);if(gP(c,3)!==3)return null;const d=CO(BO(new FO,kP(c,24,eP)),kP(c,24,eP)),e=gP(c,6);e!==0&&EO(DO(d,kP(c,e)),kP(c,e));return d}catch(b){return null}};var pP=a=>{try{const b=me(a).map(d=>d.toString(2).padStart(8,"0")).join(""),c=new nP(b);return aP($O(ZO(YO(XO(WO(VO(UO(TO(SO(RO(QO(PO(OO(NO(MO(LO(KO(JO(new bP,gP(c,6)),hP(c)),hP(c)),gP(c,12)),gP(c,12)),gP(c,6)),iP(c)),gP(c,12)),gP(c,6)),!!gP(c,1)),!!gP(c,1)),kP(c,12,fP)),kP(c,24,eP)),kP(c,24,eP)),!!gP(c,1)),iP(c)),lP(c)),lP(c)),mP(c))}catch(b){return null}};var rP=a=>{if(!a)return null;a=a.split(".");if(a.length>4)return null;var b=pP(a[0]);if(!b)return null;var c=new cP;b=x(c,1,b);a.shift();for(const d of a)switch(qP(d)){case 1:case 2:break;case 3:a=oP(d);if(!a)return null;x(b,2,a);break;default:return null}return b};const qP=a=>{try{const b=me(a).map(c=>c.toString(2).padStart(8,"0")).join("");return gP(new nP(b),3)}catch(b){return-1}};const sP=(a,b)=>{const c={};if(Array.isArray(b)&&b.length!==0)for(const d of b)c[d]=a.indexOf(d)!==-1;else for(const d of a)c[d]=!0;delete c[0];return c};var uP=(a,b)=>{try{var c=me(a.split(".")[0]).map(e=>e.toString(2).padStart(8,"0")).join("");const d=new nP(c);c={};c.tcString=a;c.gdprApplies=b;d.skip(78);c.cmpId=gP(d,12);c.cmpVersion=gP(d,12);d.skip(30);c.tcfPolicyVersion=gP(d,6);c.isServiceSpecific=!!gP(d,1);c.useNonStandardStacks=!!gP(d,1);c.specialFeatureOptins=tP(kP(d,12,fP),fP);c.purpose={consents:tP(kP(d,24,eP),eP),legitimateInterests:tP(kP(d,24,eP),eP)};c.purposeOneTreatment=!!gP(d,1);c.publisherCC=iP(d);c.vendor={consents:tP(lP(d),null), 
legitimateInterests:tP(lP(d),null)};return c}catch(d){return null}};const tP=(a,b)=>{const c={};if(Array.isArray(b)&&b.length!==0)for(const d of b)c[d]=a.indexOf(d)!==-1;else for(const d of a)c[d]=!0;delete c[0];return c};function Hq(a,...b){try{const c=encodeURIComponent(ie(Pm(b,a.i)));a.j(`${"https://pagead2.googlesyndication.com/pagead/ping"}?e=${4}&d=${c}`)}catch(c){Om(c,a.i)}}var vP=class extends Iq{constructor(a){super(7,cq());this.j=a}};var wP=class extends G{g(){return y(this,2)!=null}};var xP=class extends G{j(){return y(this,2)!=null}};var yP=class extends G{i(){return y(this,1)!=null}};var zP=Uj(class extends G{});function AP(a){a=BP(a);try{var b=a?zP(a):null}catch(c){b=null}return b?v(b,yP,4)||null:null}function BP(a){a=(new kN(a)).get("FCCDCF","");if(a)if(a.startsWith("%"))try{var b=decodeURIComponent(a)}catch(c){b=null}else b=a;else b=null;return b};function CP(a){a.__tcfapiPostMessageReady||DP(new EP(a))} 
function DP(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__tcfapiCall;e&&(e.command==="ping"||e.command==="addEventListener"||e.command==="removeEventListener")&&(0,a.A.__tcfapi)(e.command,e.version,(f,g)=>{const h={};h.__tcfapiReturn=e.command==="removeEventListener"?{success:f,callId:e.callId}:{returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f, 
b.origin);return f},e.parameter)};a.A.addEventListener("message",a.g);a.A.__tcfapiPostMessageReady=!0}var EP=class{constructor(a){this.A=a}};function FP(a){a.__uspapiPostMessageReady||GP(new HP(a))} 
function GP(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__uspapiCall;e&&e.command==="getUSPData"&&a.A.__uspapi(e.command,e.version,(f,g)=>{const h={};h.__uspapiReturn={returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f})};a.A.addEventListener("message",a.g);a.A.__uspapiPostMessageReady=!0} 
var HP=class{constructor(a){this.A=a;this.g=null}};var IP=class extends G{};var JP=Uj(class extends G{g(){return y(this,1)!=null}});function KP(a,b){function c(n){if(n.length<10)return null;var p=h(n.slice(0,4));p=k(p);n=h(n.slice(6,10));n=l(n);return"1"+p+n+"N"}function d(n){if(n.length<10)return null;var p=h(n.slice(0,6));p=k(p);n=h(n.slice(6,10));n=l(n);return"1"+p+n+"N"}function e(n){if(n.length<12)return null;var p=h(n.slice(0,6));p=k(p);n=h(n.slice(8,12));n=l(n);return"1"+p+n+"N"}function f(n){if(n.length<18)return null;var p=h(n.slice(0,8));p=k(p);n=h(n.slice(12,18));n=l(n);return"1"+p+n+"N"}function g(n){if(n.length<10)return null; 
var p=h(n.slice(0,6));p=k(p);n=h(n.slice(6,10));n=l(n);return"1"+p+n+"N"}function h(n){const p=[];let t=0;for(let z=0;z<n.length/2;z++)p.push(IN(n.slice(t,t+2))),t+=2;return p}function k(n){return n.every(p=>p===1)?"Y":"N"}function l(n){return n.some(p=>p===1)?"Y":"N"}if(a.length===0)return null;a=a.split(".");if(a.length>2)return null;a=HN(a[0]);const m=IN(a.slice(0,6));a=a.slice(6);if(m!==1)return null;switch(b){case 8:return c(a);case 10:case 12:case 9:return d(a);case 11:return e(a);case 7:return f(a); 
case 13:return g(a);default:return null}};function LP(a){!a.l||a.A.__uspapi||a.A.frames.__uspapiLocator||(a.A.__uspapiManager="fc",pI(a.A,"__uspapiLocator"),za("__uspapi",(b,c,d)=>{typeof d==="function"&&b==="getUSPData"&&(b=a.i&&!A(a.j,3),d({version:1,uspString:b?"1---":a.l},!0))},a.A),FP(a.A))} 
function MP(a){!a.tcString||a.A.__tcfapi||a.A.frames.__tcfapiLocator||(a.A.__tcfapiManager="fc",pI(a.A,"__tcfapiLocator"),a.A.__tcfapiEventListeners=a.A.__tcfapiEventListeners||[],za("__tcfapi",(b,c,d,e)=>{if(typeof d==="function")if(c&&(c>2.2||c<=1))d(null,!1);else{var f=a.A.__tcfapiEventListeners;c=a.i&&!A(a.j,1);switch(b){case "ping":d({gdprApplies:!c,cmpLoaded:!0,cmpStatus:"loaded",displayStatus:"disabled",apiVersion:"2.2",cmpVersion:2,cmpId:300});break;case "addEventListener":e=f.push(d);b=!c; 
--e;a.tcString?(b=uP(a.tcString,b),b.addtlConsent=a.g!=null?a.g:void 0,b.cmpStatus="loaded",b.eventStatus="tcloaded",e!=null&&(b.listenerId=e)):b=null;d(b,!0);break;case "removeEventListener":e!==void 0&&f[e]?(f[e]=null,d(!0)):d(!1);break;case "getInAppTCData":case "getVendorList":d(null,!1);break;case "getTCData":d(null,!1)}}},a.A),CP(a.A))} 
function NP(a){if(!a?.g()||C(a,1).length===0||zh(a,IP,2,eh()).length===0)return null;const b=C(a,1);let c;try{var d=LN(b.split("~")[0]);c=GN(b)}catch(e){return null}a=zh(a,IP,2,eh()).reduce((e,f)=>xu(Rh(OP(e),1))>xu(Rh(OP(f),1))?e:f);d=Th(d,3).indexOf(Qh(a,1));return d===-1||d>=c.length?null:{uspString:KP(c[d],Qh(a,1)),kf:HO(OP(a))}}function PP(a){a=a.find(b=>b&&D(b,1)===13);if(a?.g())try{return JP(C(a,2))}catch(b){}return null}function OP(a){return bh(a,IO,2)?v(a,IO,2):GO(new IO,0)} 
var QP=class{constructor(a,b,c){this.A=a;this.j=b;this.i=c;b=BP(this.A.document);try{var d=b?zP(b):null}catch(e){d=null}(b=d)?(d=v(b,xP,5)||null,b=zh(b,wP,7,eh()),b=PP(b??[]),d={Mg:d,fh:b}):d={Mg:null,fh:null};b=d;d=NP(b.fh);b=b.Mg;b?.j()&&C(b,2).length!==0?(c=bh(b,IO,1)?v(b,IO,1):GO(new IO,0),b={uspString:C(b,2),kf:HO(c)}):b=null;this.l=b&&d?d.kf>b.kf?d.uspString:b.uspString:b?b.uspString:d?d.uspString:null;this.tcString=(d=AP(a.document))&&d.i()?C(d,1):null;this.g=(a=AP(a.document))&&y(a,2)!=null? 
C(a,2):null}};function RP(){const a=Bb();return a?Pa("AmazonWebAppPlatform;Android TV;Apple TV;AppleTV;BRAVIA;BeyondTV;Freebox;GoogleTV;HbbTV;LongTV;MiBOX;MiTV;NetCast.TV;Netcast;Opera TV;PANASONIC;POV_TV;SMART-TV;SMART_TV;SWTV;Smart TV;SmartTV;TV Store;UnionTV;WebOS".split(";"),b=>Ab(a,b))||Ab(a,"OMI/")&&!Ab(a,"XiaoMi/")?!0:Ab(a,"Presto")&&Ab(a,"Linux")&&!Ab(a,"X11")&&!Ab(a,"Android")&&!Ab(a,"Mobi"):!1};function SP(a){const b=a[0]/255,c=a[1]/255;a=a[2]/255;return(b<=.03928?b/12.92:Math.pow((b+.055)/1.055,2.4))*.2126+(c<=.03928?c/12.92:Math.pow((c+.055)/1.055,2.4))*.7152+(a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4))*.0722}var TP=(a,b)=>{a=SP(a);b=SP(b);return(Math.max(a,b)+.05)/(Math.min(a,b)+.05)};function UP(a,b,c,d=null){const e=g=>{let h;try{h=JSON.parse(g.data)}catch(k){return}!h||h.googMsgType!==b||d&&/[:|%3A]javascript\(/i.test(g.data)&&!d(h,g)||c(h,g)};qb(a,"message",e);let f=!1;return()=>{let g=!1;f||(f=!0,g=rb(a,"message",e));return g}}function VP(a,b,c,d=null){const e=UP(a,b,gb(c,()=>e()),d);return e}function WP(a,b,c,d){c.googMsgType=b;a.postMessage(JSON.stringify(c),d)} 
function XP(a,b,c,d,e){if(!(e<=0)&&(WP(a,b,c,d),a=a.frames))for(let f=0;f<a.length;++f)e>1&&XP(a[f],b,c,d,--e)};function YP(a,b,c,d){return UP(a,"fullscreen",d.nb(952,(e,f)=>{if(f.source===b){if(!("eventType"in e))throw Error(`bad message ${JSON.stringify(e)}`);delete e.googMsgType;c(e)}}))};class ZP{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};async function $P(a){return a.B.promise}async function aQ(a){return a.j.promise}async function bQ(a){return a.l.promise}function cQ(a,b){b.type="err_st";b.slot=a.slotType;b.freq=.25;a.qem&&(b.qem=a.qem);b.tag_type=a.F.Ok;b.version=a.F.version;Jm(a.I,"fullscreen_tag",b,!1,.25)} 
class dQ extends N{constructor(a,b,c){var d=sB,e=qB,f={Ok:2,version:cq()};super();this.slotType=a;this.pubWin=b;this.jf=c;this.za=d;this.I=e;this.F=f;this.g=1;this.qem=null;this.B=new ZP;this.j=new ZP;this.l=new ZP}M(){const a=YP(this.pubWin,this.jf,b=>{if(b.eventType==="adError")this.l.resolve(),this.g=4;else if(b.eventType==="adReady"&&this.g===1)this.qem=b.qem,b.slotType!==this.slotType&&(cQ(this,{cur_st:this.g,evt:b.eventType,adp_tp:b.slotType}),this.g=4),this.B.resolve(),this.g=2;else if(b.eventType=== 
"adClosed"&&this.g===2)this.j.resolve(b.result),this.g=3;else if(b.eventType!=="adClosed"||this.g!==3)cQ(this,{cur_st:this.g,evt:b.eventType}),this.g=4},this.za);Mr(this,a)}};var eQ=Promise;class fQ{constructor(a){this.j=a}i(a,b,c){this.j.then(d=>{d.i(a,b,c)})}g(a,b){return this.j.then(c=>c.g(a,b))}};class gQ{constructor(a){this.data=a}};function hQ(a,b){iQ(a,b);return new jQ(a)}class jQ{constructor(a){this.j=a}i(a,b,c=[]){const d=new MessageChannel;iQ(d.port1,b);this.j.postMessage(a,[d.port2].concat(c))}g(a,b){return new eQ(c=>{this.i(a,c,b)})}}function iQ(a,b){b&&(a.onmessage=c=>{b(new gQ(c.data,hQ(c.ports[0])))})};var kQ=class{constructor(a){this.g=a}};const lQ=a=>{const b=Object.create(null);(typeof a==="string"?[a]:a).forEach(c=>{if(c==="null")throw Error("Receiving from null origin not allowed without token verification. Please use NullOriginConnector.");b[c]=!0});return c=>b[c]===!0};var nQ=({destination:a,Ba:b,origin:c,cf:d="ZNWN1d",onMessage:e,Ch:f})=>mQ({destination:a,xj:()=>b.contentWindow,gk:c instanceof kQ?c:typeof c==="function"?new kQ(c):new kQ(lQ(c)),cf:d,onMessage:e,Ch:f}); 
const mQ=({destination:a,xj:b,gk:c,Uo:d,cf:e,onMessage:f,Ch:g})=>new fQ(new eQ((h,k)=>{const l=m=>{m.source&&m.source===b()&&c.g(m.origin)&&(m.data.n||m.data)===e&&(a.removeEventListener("message",l,!1),d&&m.data.t!==d?k(Error(`Token mismatch while establishing channel "${e}". Expected ${d}, but received ${m.data.t}.`)):(h(hQ(m.ports[0],f)),g&&g(m)))};a.addEventListener("message",l,!1)}));var oQ=Sj(fn);var pQ=Sj(gn);var qQ=Sj(jn);var rQ=Sj(en);var sQ=Sj(hn);var tQ={cm:"google_ads_preview",zm:"google_mc_lab",Im:"google_anchor_debug",Hm:"google_bottom_anchor_debug",INTERSTITIAL:"google_ia_debug",fn:"google_scr_debug",jn:"google_ia_debug_allow_onclick",Fn:"googleads",ii:"google_pedestal_debug",Zn:"google_responsive_slot_preview",Yn:"google_responsive_dummy_ad"},uQ={google_bottom_anchor_debug:1,google_anchor_debug:2,google_ia_debug:8,google_scr_debug:9,googleads:2,google_pedestal_debug:30};var vQ={INTERSTITIAL:1,BOTTOM_ANCHOR:2,TOP_ANCHOR:3,1:"INTERSTITIAL",2:"BOTTOM_ANCHOR",3:"TOP_ANCHOR"};function wQ(a,b){if(!a)return!1;a=a.hash;if(!a||!a.indexOf)return!1;if(a.indexOf(b)!=-1)return!0;b=xQ(b);return b!="go"&&a.indexOf(b)!=-1?!0:!1}function xQ(a){let b="";vd(a.split("_"),c=>{b+=c.substr(0,2)});return b}function yQ(){var a=q.location;let b=!1;vd(tQ,c=>{wQ(a,c)&&(b=!0)});return b}function zQ(a,b){switch(a){case 1:return wQ(b,"google_ia_debug");case 2:return wQ(b,"google_bottom_anchor_debug");case 3:return wQ(b,"google_anchor_debug")||wQ(b,"googleads")}};function AQ(a){var b=window;return a.google_adtest==="on"||a.google_adbreak_test==="on"||b.location.host.endsWith("h5games.usercontent.goog")||b.location.host==="gamesnacks.com"?b.document.querySelector('meta[name="h5-games-eids"]')?.getAttribute("content")?.split(",").map(c=>Math.floor(Number(c))).filter(c=>!isNaN(c)&&c>0)||[]:[]};function BQ(a,b){b&&!a.g&&(b=CQ(b),a.g=b.id,a.j=b.creationTimeSeconds)} 
var DQ=class{constructor(){this.l=new Date(Date.now());this.j=this.g=null;this.i={[3]:{},[4]:{},[5]:{}};this.i[3]={[71]:(...a)=>{var b=this.g;var c=this.l,d=Number(a[0]);a=Number(a[1]);b=b!==null?xd(`${"w5uHecUBa2S"}:${d}:${b}`)%a===Math.floor(c.valueOf()/864E5)%a:void 0;return b}};this.i[4]={[15]:()=>{var a=Number(this.j||void 0);isNaN(a)?a=void 0:(a=new Date(a*1E3),a=a.getFullYear()*1E4+(a.getMonth()+1)*100+a.getDate());return a}}}},EQ;let FQ=void 0;function GQ(a){return IH(a)?.head_tag_slot_vars?.google_ad_host??a.document?.querySelector('meta[name="google-adsense-platform-account"]')?.getAttribute("content")??null};function HQ(a){try{const b=a.getItem("google_adsense_settings");if(!b)return{};const c=JSON.parse(b);return c!==Object(c)?{}:Rb(c,(d,e)=>Object.prototype.hasOwnProperty.call(c,e)&&typeof e==="string"&&Array.isArray(d))}catch(b){return{}}};function IQ(a=q){return a.ggeac||(a.ggeac={})};function JQ(a,b=document){return!!b.featurePolicy?.features().includes(a)}function KQ(a,b=document){return!!b.featurePolicy?.allowedFeatures().includes(a)}function LQ(a=navigator){try{return!!a.protectedAudience?.queryFeatureSupport?.("deprecatedRenderURLReplacements")}catch(b){return!1}}function MQ(a,b,c=b.document){return!!(a&&"sharedStorage"in b&&b.sharedStorage&&KQ("shared-storage",c))};function NQ(a=ud()){return b=>xd(`${b} + ${a}`)%1E3};function OQ(a,b){a.g=Qq(14,b,()=>{})}class PQ{constructor(){this.g=()=>{}}}function QQ(a){L(PQ).g(a)};function RQ(a=IQ()){Rq(L(Sq),a);SQ(a);OQ(L(PQ),a);L(jx).i()}function SQ(a){const b=L(jx);b.j=(c,d)=>Qq(5,a,()=>!1)(c,d,1);b.C=(c,d)=>Qq(6,a,()=>0)(c,d,1);b.B=(c,d)=>Qq(7,a,()=>"")(c,d,1);b.g=(c,d)=>Qq(8,a,()=>[])(c,d,1);b.l=(c,d)=>Qq(17,a,()=>[])(c,d,1);b.i=()=>{Qq(15,a,()=>{})(1)}};function CQ(a){var b=a.split(":");a=b.find(c=>c.indexOf("ID=")===0)||null;b=b.find(c=>c.indexOf("T=")===0)?.substring(2)||null;return{id:a,creationTimeSeconds:b}}function TQ(a,b,c){c?(a=a.A,b=c.g()?qN(b,a):null):b=null;return b}function UQ(a,b,c,d){if(d){var e=xu(Rh(c,2))-Date.now()/1E3;e={ce:Math.max(e,0),path:C(c,3),domain:C(c,4),ze:!1};c=c.getValue();a=a.A;d.g()&&rN(b,c,e,a)}} 
function VQ(a,b,c){var d;(d=!c)||(d=a.A,d=!(c.g()&&qN(b,d)));if(!d)for(const f of WQ(a.A.location.hostname)){d=b;var e=a.A;c.g()&&e.origin!=="null"&&lN(new kN(e.document),d,"/",f)}}var XQ=class{constructor(a){this.A=a}};function WQ(a){if(a==="localhost")return["localhost"];a=a.split(".");if(a.length<2)return[];const b=[];for(let c=0;c<a.length-1;++c)b.push(a.slice(c).join("."));return b};function YQ(a,b,c){var d={[0]:NQ(Zd(b).toString())};if(c){c=TQ(new XQ(b),"__gads",c)||"";EQ||(EQ=new DQ);b=EQ;BQ(b,c);QQ(b.i);const e=(new RegExp(/(?:^|:)(ID=[^\s:]+)/)).exec(c)?.[1];d[1]=f=>e?NQ(e)(f):void 0}d=Tq(a,d);Wq.Pa(1085,ZH(L(XH),a,d))}function ZQ(a,b){YQ(20,a,b);YQ(17,a,b)}function $Q(a){const b=L(Sq).g();a=AQ(a);return b.concat(a).join(",")}function aR(a){const b=rm();b&&(a.debug_experiment_id=b)};function bR(a,b){if(a&&!IH(a).ads_density_stats_processed&&!vl(a)&&(IH(a).ads_density_stats_processed=!0,M(Av)||ud()<.01)){const c=()=>{if(a){var d=QL(LL(a),b.google_ad_client,a.location.hostname,$Q(b).split(","));yB("ama_stats",d,1)}};$d(a,()=>{q.setTimeout(c,1E3)})}};function cR(a,b,c,d,e,f=null){if(e){if(M(cv))var g=null;else try{g=e.getItem("google_ama_config")}catch(l){g=null}try{var h=g?lu(g):null}catch(l){h=null}}else h=null;a:{if(d)try{var k=lu(d);break a}catch(l){LM(a,{cfg:1,inv:1})}k=null}if(d=k){if(e){k=new Ct;x(d,3,k);h=wu(d?.g()?.i())||1;h=Date.now()+864E5*h;Number.isFinite(h)&&ci(k,1,Math.round(h));k=Vg(d);d.g()&&(h=new Bt,g=d?.g()?.g(),h=$h(h,23,g),g=d?.g()?.j(),h=$h(h,12,g),x(k,15,h));h=zh(k,Zt,1,eh());for(g=0;g<h.length;g++)$g(h[g],11);$g(k,22); 
if(M(cv))SM(a,e);else try{e.setItem("google_ama_config",ii(k))}catch(l){LM(a,{lserr:1})}}e=QM(a,zh(d,Lt,7,eh()));k={};M(dv)||(k.sk=v(d,Tt,8)||new Tt);e&&(k.ca=e);e&&PM(e,3)&&(k.Sc=[1]);e=k;JH(a,2)&&(kl(5,[Lg(d)]),c=MM(c),k=(k=e.ca)&&y(k,4)||"",c.google_package=k,UM(a,b,d,e,new st(["google-auto-placed"],c),f));return!0}h&&(LM(a,{cfg:1,cl:1}),e!=null&&SM(a,e));return!1};function dR(a,b){b=b&&b[0];if(!b)return null;b=b.target;const c=b.getBoundingClientRect(),d=Fl(a.g.da()||window);if(c.bottom<=0||c.bottom>d.height||c.right<=0||c.left>=d.width)return null;var e=eR(a,b,c,a.g.g.elementsFromPoint(Nb(c.left+c.width/2,c.left,c.right-1),Nb(c.bottom-1-2,c.top,c.bottom-1)),1,[]),f=eR(a,b,c,a.g.g.elementsFromPoint(Nb(c.left+c.width/2,c.left,c.right-1),Nb(c.top+2,c.top,c.bottom-1)),2,e.zb),g=eR(a,b,c,a.g.g.elementsFromPoint(Nb(c.left+2,c.left,c.right-1),Nb(c.top+c.height/2, 
c.top,c.bottom-1)),3,[...e.zb,...f.zb]);const h=eR(a,b,c,a.g.g.elementsFromPoint(Nb(c.right-1-2,c.left,c.right-1),Nb(c.top+c.height/2,c.top,c.bottom-1)),4,[...e.zb,...f.zb,...g.zb]);var k=fR(a,b,c),l=n=>Qa(a.j,n.Gb)&&Qa(a.l,n.bg)&&Qa(a.i,n.Eh);e=La([...e.entries,...f.entries,...g.entries,...h.entries],l);l=La(k,l);k=[...e,...l];f=c.left<-2||c.right>d.width+2;f=k.length>0||f;g=Gl(a.g.g);const m=new rl(c.left,c.top,c.width,c.height);e=[...Ma(e,n=>new rl(n.Pc.left,n.Pc.top,n.Pc.width,n.Pc.height)),...bb(Ma(l, 
n=>tl(m,n.Pc))),...La(tl(m,new rl(0,0,d.width,d.height)),n=>n.top>=0&&n.top+n.height<=d.height)];return{entries:k,qh:f,Oh:{scrollX:g.x,scrollY:g.y},target:b,zc:c,ai:{width:d.width,height:d.height},hk:e.length<20?gR(m,e):hR(c,e)}} 
function iR(a,b){const c=a.g.da(),d=a.g.g;return new Promise((e,f)=>{const g=c.IntersectionObserver;if(g)if(d.elementsFromPoint)if(d.createNodeIterator)if(d.createRange)if(c.Range.prototype.getBoundingClientRect){var h=new g(k=>{const l=new Bm,m=Am(l,()=>dR(a,k));m&&(l.i.length&&(m.lj=Math.round(Number(l.i[0].duration))),h.disconnect(),e(m))},jR);h.observe(b)}else f(Error("5"));else f(Error("4"));else f(Error("3"));else f(Error("2"));else f(Error("1"))})} 
function eR(a,b,c,d,e,f){if(c.width===0||c.height===0)return{entries:[],zb:[]};const g=[],h=[];for(let m=0;m<d.length;m++){const n=d[m];if(n===b)continue;if(Qa(f,n))continue;h.push(n);const p=n.getBoundingClientRect();if(a.g.contains(n,b)){g.push(kR(a,c,n,p,1,e));continue}if(a.g.contains(b,n)){g.push(kR(a,c,n,p,2,e));continue}a:{var k=a;var l=b;const w=k.g.qj(l,n);if(!w){k=null;break a}const {Ga:B,Ub:I}=lR(k,l,w,n)||{},{Ga:T,Ub:R}=lR(k,n,w,l)||{};k=B&&I&&T&&R?I<=R?{Ga:B,Gb:mR[I]}:{Ga:T,Gb:nR[R]}: 
B&&I?{Ga:B,Gb:mR[I]}:T&&R?{Ga:T,Gb:nR[R]}:null}const {Ga:t,Gb:z}=k||{};t&&z?g.push(kR(a,c,n,p,z,e,t)):g.push(kR(a,c,n,p,9,e))}return{entries:g,zb:h}} 
function fR(a,b,c){const d=[];for(b=b.parentElement;b;b=b.parentElement){const f=b.getBoundingClientRect();if(f){var e=td(b,a.g.da());e&&e.overflow!=="visible"&&(e.overflowY!=="auto"&&e.overflowY!=="scroll"&&c.bottom>f.bottom+2?d.push(kR(a,c,b,f,5,1)):(e=e.overflowX==="auto"||e.overflowX==="scroll",!e&&c.left<f.left-2?d.push(kR(a,c,b,f,5,3)):!e&&c.right>f.right+2&&d.push(kR(a,c,b,f,5,4))))}}return d} 
function gR(a,b){if(a.width===0||a.height===0||b.length===0)return 0;let c=0;for(let d=1;d<1<<b.length;d++){let e=a,f=0;for(let g=0;g<b.length&&(!(d&1<<g)||(f++,e=sl(e,b[g]),e));g++);e&&(c=f%2===1?c+(e.width+1)*(e.height+1):c-(e.width+1)*(e.height+1))}return c/((a.width+1)*(a.height+1))} 
function hR(a,b){if(a.width===0||a.height===0||b.length===0)return 0;let c=0;for(let d=a.left;d<=a.right;d++)for(let e=a.top;e<=a.bottom;e++)for(let f=0;f<b.length;f++)if(d>=b[f].left&&d<=b[f].left+b[f].width&&e>=b[f].top&&e<=b[f].top+b[f].height){c++;break}return c/((a.width+1)*(a.height+1))} 
function kR(a,b,c,d,e,f,g){g={element:c,Pc:d,Gb:e,Eh:f,bg:null,Ga:g||null};if(Qa(a.j,e)&&Qa(a.i,f)){b=new ml(b.top,b.right-1,b.bottom-1,b.left);if((a=oR(a,c))&&ol(b,a))c=4;else{a=pR(c,d);e=Vl(c,"paddingLeft");f=Vl(c,"paddingRight");const h=Vl(c,"paddingTop"),k=Vl(c,"paddingBottom");e=new ml(parseFloat(h),parseFloat(f),parseFloat(k),parseFloat(e));ol(b,new ml(a.top+e.top,a.right-e.right,a.bottom-e.bottom,a.left+e.left))?c=3:(c=pR(c,d),c=ol(b,c)?2:1)}g.bg=c}return g} 
function lR(a,b,c,d){const e=[];for(var f=b;f&&f!==c;f=f.parentElement)e.unshift(f);c=a.g.da();for(f=0;f<e.length;f++){const h=e[f];var g=td(h,c);if(g){if(g.position==="fixed")return{Ga:h,Ub:1};if(g.position==="sticky"&&a.g.contains(h.parentElement,d))return{Ga:h,Ub:2};if(g.position==="absolute")return{Ga:h,Ub:3};if(g.cssFloat!=="none"){g=h===e[0];const k=qR(a,e.slice(0,f),h);if(g||k)return{Ga:h,Ub:4}}}}return(a=qR(a,e,b))?{Ga:a,Ub:5}:null} 
function qR(a,b,c){const d=c.getBoundingClientRect();if(!d)return null;for(let e=0;e<b.length;e++){const f=b[e];if(!a.g.contains(f,c))continue;const g=f.getBoundingClientRect();if(!g)continue;const h=td(f,a.g.da());if(h&&d.bottom>g.bottom+2&&h.overflowY==="visible")return f}return null} 
function oR(a,b){var c=a.g.g;a=c.createRange();if(!a)return null;c=c.createNodeIterator(b,NodeFilter.SHOW_TEXT,{acceptNode:d=>d.nodeValue===null||/^[\s\xa0]*$/.test(d.nodeValue)?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT});for(b=c.nextNode();c.nextNode(););c=c.previousNode();if(!b||!c)return null;a.setStartBefore(b);a.setEndAfter(c);a=a.getBoundingClientRect();return a.width===0||a.height===0?null:new ml(a.top,a.right,a.bottom,a.left)} 
function pR(a,b){var c=Vl(a,"borderLeftWidth");const d=Vl(a,"borderRightWidth"),e=Vl(a,"borderTopWidth");a=Vl(a,"borderBottomWidth");c=new ml(parseFloat(e),parseFloat(d),parseFloat(a),parseFloat(c));return new ml(b.top+c.top,b.right-1-c.right,b.bottom-1-c.bottom,b.left+c.left)}var sR=class{constructor(a){var b=rR;this.j=[5,8,9];this.l=[3,4];this.i=b;this.g=Cl(a)}}; 
const rR=[1,2,3,4],mR={[1]:3,[4]:10,[3]:12,[2]:4,[5]:5},nR={[1]:6,[4]:11,[3]:13,[2]:7,[5]:8},jR={threshold:[0,.25,.5,.75,.95,.96,.97,.98,.99,1]};function tR(a){a.g!=null||a.C||(a.g=new MutationObserver(b=>{for(const c of b)for(const d of c.addedNodes)oa(d)&&d.nodeType==1&&(b=a,d.matches('A[href]:not([href=""])')&&ds(b.j,d))}),a.g.observe(a.A.document.documentElement,{childList:!0,subtree:!0}))}var uR=class extends N{constructor(a){super();this.A=a;this.j=new es;this.g=null;Mr(this,()=>{this.g?.disconnect();this.g=null})}};function vR(a,b){b.addEventListener("click",()=>{var c=a.g;var d=b.getAttribute("href");c=d?d==="#"?Us(po(4)):d.startsWith("#")?Us(po(5)):wR(d,c):Ws(Error("Empty href"));if(Ys(c)){d=c.getValue();c=a.U;var e=new ro;d=x(e,1,d);c.call(a,d)}else a.i(c.g)})}var yR=class{constructor(a,b,c){var d=xR();this.A=a;this.g=b;this.U=c;this.i=d}M(){const a=new uR(this.A);Array.from(a.A.document.querySelectorAll('A[href]:not([href=""])')).forEach(b=>{vR(this,b)});tR(a);bs(a.j).listen(b=>{vR(this,b)})}}; 
function wR(a,b){return zR(a,b).map(c=>zR(b).map(d=>{if(c.protocol==="http:"||c.protocol==="https:"){var e=po(2);e=fi(e,2,`${c.host}${c.pathname}`);d=fi(e,3,`${d.host}${d.pathname}`)}else d=c.protocol==="javascript:"?po(3):po(1);return d}))}function zR(a,b){return at(Xs(()=>new URL(a,b)),()=>Error("Invalid URL"))};function AR(a){if(a<0||!Number.isInteger(a))return Ws(Error(`Not a non-negative integer: ${a}`));const b=[];do b.push("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".charAt(a%64)),a=Math.floor(a/64);while(a>0);return Us(b.reverse().join(""))};class BR{constructor(){this.hi=5E3}Si(){return 5E3}}function CR(a,b){return a.quantizer?Math.floor(b/5E3)*5E3/a.quantizer.hi:b}function DR(a,b){b=b.map(c=>CR(a,c));return ER(b,a.g===void 0?void 0:CR(a,a.g)).map(c=>{a:{var d=FR;const e=[];for(const f of c){c=d(f);if(!Ys(c)){d=Ws(c.g);break a}e.push(c.getValue())}d=Us(e)}return d}).map(c=>c.join(".")).map(c=>GR(c,a.quantizer?.Si()))}var HR=class{constructor(a,b){this.quantizer=a;this.g=b}}; 
function FR(a){const b=AR(a.value);if(!Ys(b))return b;const c=b.getValue();return a.ye===1?Us(`${c}`):a.ye===2?Us(`${c}${"~"}`):ct(AR(a.ye-2),d=>{throw d;}).map(d=>`${c}${"~"}${d}`)}function ER(a,b){const c=[];for(let d=0;d<a.length;d++){const e=a[d]??b;if(e===void 0)return Ws(Error("Sparse but no default"));c.length===0||e!==c[c.length-1].value?c.push({value:e,ye:1}):c[c.length-1].ye++}return Us(c)}function GR(a,b){return a===""?Us(""):IR(b).map(c=>`${c}${a}`)} 
function IR(a){return a===void 0||a===1?Us(""):bt(AR(a),"ComFactor: ").map(b=>`${"~"}${b}${"."}`)};var JR=class extends N{constructor(a){super();this.A=a;this.j=new O(!1);this.g=()=>{this.j.g(this.A.document.hasFocus())}}M(){this.A.addEventListener("focus",this.g);this.A.addEventListener("blur",this.g);Mr(this,()=>void this.A.removeEventListener("focus",this.g));Mr(this,()=>void this.A.removeEventListener("blur",this.g));this.j.g(this.A.document.hasFocus())}};function KR(a){a.j.g(a.A.document.visibilityState==="visible")}var LR=class extends N{constructor(a){super();this.A=a;this.j=new O(!1);this.g=()=>void KR(this)}M(){this.A.addEventListener("visibilitychange",this.g);Mr(this,()=>void this.A.removeEventListener("visibilitychange",this.g));KR(this)}};function MR(a){return a.g!==null?a.i+a.j()-a.g:a.i}var OR=class{constructor(a){this.A=a;this.i=0;this.g=null;this.j=NR(this.A)}start(){this.g===null&&(this.g=this.j())}};function NR(a){return a.performance&&a.performance.now?()=>a.performance.now():()=>Date.now()};function PR(a){a=new QR(a);a.M();return a}function RR(a){const b=ps(a.A,1E3,()=>void a.handleEvent());a.A.addEventListener("scroll",()=>void b())}function SR(a){const b=TR(a.A),c=()=>{const d=TR(a.A),e=Math.abs(d.height-b.height);if(Math.abs(d.width-b.width)>20||e>20)a.G=!0,a.A.removeEventListener("resize",c)};a.A.addEventListener("resize",c)}function UR(a){a.l=!a.g.R;Xr(a.g,!1,()=>{a.A.setTimeout(()=>{a.l=!0},100)})} 
function VR(a){Wr(a.g,!0,()=>void a.j.start());Wr(a.g,!1,()=>{var b=a.j;b.g!==null&&(b.i+=b.j()-b.g);b.g=null});a.F.start()} 
function WR(a){var b=a.A.scrollY;var c=ir(a.A);b={Ee:Math.floor(b/100),Ld:Math.floor((b+c)/100),Vh:a.A.performance.now()};if(b.Ee<0||b.Ld<0||b.Ee>1E3||b.Ld>1E3)a.H=!0,a.i=null;else{if(a.i){c=a.i;var d=new LF(c.Ee,c.Ld),e=new LF(b.Ee,b.Ld);var f=Math.max(d.start,e.start);d=Math.min(d.end,e.end);if(f=f<=d?new LF(f,d):null)for(c=b.Vh-c.Vh,d=f.start;d<=f.end;d++)a.B[d]=(a.B[d]??0)+c}a.i=a.C.R?b:null}} 
var QR=class{constructor(a){this.A=a;this.B=[];this.G=this.l=this.H=!1;this.i=null;a=this.A;var b=new JR(a);b.M();b=Tr(b.j);a=new LR(a);a.M();this.C=this.g=Sr(b,Tr(a.j));this.j=new OR(this.A);this.F=new OR(this.A);this.K=new HR((new HR(new BR)).quantizer,0)}M(){RR(this);SR(this);UR(this);VR(this);this.C.listen(()=>void WR(this));q.setInterval(()=>void this.handleEvent(),5E3);this.handleEvent()}handleEvent(){this.C.R&&WR(this)}};function TR(a){return new pl(hr(a),ir(a))};function YR(a,{Fa:b}){a=new ZR(a,b);if(!a.Fa&&M(Fv)){b=a.A;var c=$R(aS(a));(new yR(b,b.document.baseURI,c)).M()}bS(a)} 
function bS(a){if(M(Gv)){var b=PR(a.A);Nq(new OH(a.A),cS(()=>{var c=aS(a),d=new uo,e=DR(b.K,b.B);if(!Ys(e))throw bt(e,"PVDC: ").g;var f=new to;f=bi(f,2,5E3);f=bi(f,1,100);e=e.getValue();e=fi(f,3,e);f=TR(b.A);var g=new so;g=bi(g,1,f.width);f=bi(g,2,f.height);e=x(e,4,f);f=new so;f=bi(f,1,mr(b.A).scrollWidth);f=bi(f,2,mr(b.A).scrollHeight);e=x(e,5,f);e=E(e,6,b.l);f=Math.round(MR(b.F)/1E3);e=bi(e,8,f);f=Math.round(MR(b.j)/1E3);e=bi(e,9,f);b.H&&Lh(e,7,Of,1,Pf);b.G&&Lh(e,7,Of,2,Pf);d=Ah(d,2,vo,e);c(d)}))}} 
function aS(a){if(!a.U){const b=L(XH);a.U=c=>{dI(b,c)}}return a.U}var ZR=class{constructor(a,b){this.A=a;this.Fa=b;this.U=null}};function $R(a){return b=>{var c=new uo;b=Ah(c,1,vo,b);return void a(b)}}function xR(){return a=>{zB(1243,a,void 0,dS("LCC"))}}function cS(a){return()=>void vB(1243,a,dS("PVC"))}function dS(a){return b=>{b.errSrc=a}};var eS=class extends N{constructor(a,b){super();this.value=a;Mr(this,b)}};function fS(a,b){const c=gS(a.getBoundingClientRect()),d=new O(c),e=hS(a,b,f=>{d.g(gS(f.boundingClientRect))});return new eS(Tr(d),()=>void e.disconnect())}function hS(a,b,c){b=new IntersectionObserver(d=>{d.filter(e=>e.target===a).forEach(c)},{root:b});b.observe(a);return b}function gS(a){return a.height>0||a.width>0};var iS={hn:0,Ao:1,Un:2,0:"INITIAL_RENDER",1:"UNRENDER",2:"RENDER_BACK"};function jS(a,b,c){var d=[1,2];const e=fS(b,c),f=e.value,g=new es;Xr(f,!0,()=>void kS(a,f,g,d));return new eS(bs(g),()=>void e.dispose())}function kS(a,b,c,d){const e=new OR(a);let f=new OR(a);e.start();f.start();let g=0;const h=k=>{k={type:k,Bh:++g,Yj:MR(f),Xj:MR(e)};f=new OR(a);f.start();return k};d&&!d.includes(0)||ds(c,h(0));b.listen(k=>{k=k?2:1;d&&!d.includes(k)||ds(c,h(k))})};function lS(a,b){var c=L(XH);vB(1282,()=>void mS(a,b,c))}function mS(a,b,c){const d=nS(a);if(!d)throw Error("No adsbygoogle INS found");const e=jS(a.pubWin,b,d);e.value.listen(f=>{oS(f,d,c,()=>void e.dispose())})}function nS(a){return(a=a.ba.parentElement)&&xx.test(a.className)?a:null} 
function oS(a,b,c,d){if(a.Bh>5)d();else{var e=a.type===1;d=a.type===2;if(!e&&!d)throw Error(`Unsupported event type: ${iS[a.type]}`);var f=Fg(qQ());f=ci(f,1,a.Yj);f=ci(f,2,a.Xj);a=ci(f,3,a.Bh);f=b.dataset.vignetteLoaded;var g=Fg(oQ());g=ei(g,1,b.dataset.adStatus);g=ei(g,2,b.dataset.sideRailStatus);g=ei(g,3,b.dataset.anchorStatus);f=$h(g,4,f!==void 0?f==="true":void 0);b=getComputedStyle(b);g=rQ();g=Fg(g);g=ei(g,1,b.display);g=ei(g,2,b.position);g=ei(g,3,b.width);b=ei(g,4,b.height);b=Wg(b);b=x(f,5, 
b);b=Wg(b);a=x(a,4,b);e=e?sQ():void 0;e=Ah(a,5,kn,e);d=d?pQ():void 0;d=Ah(e,6,kn,d);d=Wg(Fg(Wg(d)));fI(c,d)}};var qS=a=>{const b=a.D;b.google_ad_output==null&&(b.google_ad_output="html");if(b.google_ad_client!=null){var c;(c=String(b.google_ad_client))?(c=c.toLowerCase(),c.substring(0,3)!="ca-"&&(c="ca-"+c)):c="";b.google_ad_client=c}b.google_ad_slot!=null&&(b.google_ad_slot=String(b.google_ad_slot));b.google_webgl_support=!!cl.WebGLRenderingContext;b.google_ad_section=b.google_ad_section||b.google_ad_region||"";b.google_country=b.google_country||b.google_gl||"";c=(new Date).getTime();Array.isArray(b.google_color_bg)&& 
(b.google_color_bg=pS(a,b.google_color_bg,c));Array.isArray(b.google_color_text)&&(b.google_color_text=pS(a,b.google_color_text,c));Array.isArray(b.google_color_link)&&(b.google_color_link=pS(a,b.google_color_link,c));Array.isArray(b.google_color_url)&&(b.google_color_url=pS(a,b.google_color_url,c));Array.isArray(b.google_color_border)&&(b.google_color_border=pS(a,b.google_color_border,c));Array.isArray(b.google_color_line)&&(b.google_color_line=pS(a,b.google_color_line,c))}; 
function pS(a,b,c){a.g|=2;return b[c%b.length]};const rS={google:1,googlegroups:1,gmail:1,googlemail:1,googleimages:1,googleprint:1};hd`https://securepubads.g.doubleclick.net/pagead/js/car.js`;hd`https://securepubads.g.doubleclick.net/pagead/js/cocar.js`;var sS=hd`https://ep3.adtrafficquality.google/ivt/worklet/caw.js`;function tS(a){const b=[];for(let c=0;c<8;++c){const d=new vP(f=>{b.push({url:f})}),e=F(Xp(Wp(new Yp,a),c),3,6);d.F(e)}return b} 
async function uS(a){const b=window;if(b.sharedStorage&&!b.clientAgeRequested)try{const d=await (await b.sharedStorage.createWorklet(sS.toString(),{dataOrigin:"script-origin"})).selectURL("ps_caus",tS(a),{resolveToConfig:!0,savedQuery:"ps_cac"});if(d){var c=b.document.body;const e=document.createElement("fencedframe");e.id="ps_caff";e.name="ps_caff";e.mode="opaque-ads";e.config=d;Rl(e,"display","none");c.appendChild(e)}b.clientAgeRequested=!0}catch(d){}};function vS(a,b){const c=MQ(a.isSecureContext,a,a.document),d=!!a.sharedStorage?.createWorklet;b&&c&&d&&!zH(uH(),34,!1)&&(AH(uH(),34,!0),a=uS(Zd(a)),xB(1279,a))};const wS=(a,b)=>{b=b.listener;(a=(0,a.__gpp)("addEventListener",b))&&b(a,!0)},xS=(a,b)=>{(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},yS={ae:a=>a.listener,hd:(a,b)=>({__gppCall:{callId:b,command:"addEventListener",version:"1.1"}}),sc:(a,b)=>{b=b.__gppReturn;a(b.returnValue,b.success)}},zS={ae:a=>a.listener,hd:(a,b)=>({__gppCall:{callId:b,command:"removeEventListener",version:"1.1",parameter:a.listenerId}}),sc:(a,b)=>{b=b.__gppReturn;const c=b.returnValue.data;a?.(c,b.success)}}; 
function AS(a){let b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,eg:b.__gppReturn.callId}} 
function BS(a,b,c){let d=!(b.includes(2)&&c?.idpcApplies),e=!1,f=!1,g=!1;if(a&&!a.startsWith("GPP_ERROR_STRING_")){const VV=LN(a.split("~")[0]),WV=GN(a),Oy=Th(VV,3);for(let hk=0;hk<Oy.length;++hk){const Py=Oy[hk];if(!b.includes(Py))continue;const Ta=WV[hk];switch(Py){case 2:if(c?.supportTcfeu){a:{const S=rP(Ta);if(!S||!Ta){var h=null;break a}const Ha=v(S,bP,1),ik=v(S,FO,2)||new FO;var k=Qh(Ha,9),l=Qh(Ha,4),m=Qh(Ha,5),n=A(Ha,10),p=A(Ha,11),t=C(Ha,16),z=A(Ha,15),w={consents:sP(Vh(Ha,13),eP),legitimateInterests:sP(Vh(Ha, 
14),eP)},B={consents:sP(Th(Ha,17)),legitimateInterests:sP(Th(Ha,18))},I=sP(Vh(Ha,12),fP),T=zh(Ha,AO,19,eh());const jk={};for(const qp of T){const rp=D(qp,1);jk[rp]=jk[rp]||{};for(const XV of Th(qp,3))jk[rp][XV]=D(qp,2)}h={tcString:Ta,tcfPolicyVersion:k,gdprApplies:!0,cmpId:l,cmpVersion:m,isServiceSpecific:n,useNonStandardStacks:p,publisherCC:t,purposeOneTreatment:z,purpose:w,vendor:B,specialFeatureOptins:I,publisher:{restrictions:jk,consents:sP(Vh(ik,1),eP),legitimateInterests:sP(Vh(ik,2),eP),customPurposes:{consents:sP(Th(ik, 
3)),legitimateInterests:sP(Th(ik,4))}}}}const W=h;if(!W)throw Error("Cannot decode TCF V2 section string.");d=EI(W);!HI(W,["3","4"],0)&&(e=!0);!HI(W,["2","7","9","10"],3)&&(f=!0)}break;case 7:if(Ta.length===0)throw Error("Cannot decode empty USNat section string.");const Bh=Ta.split(".");if(Bh.length>2)throw Error(`Expected at most 2 segments but got ${Bh.length} when decoding ${Ta}.`);var R=void 0,U=void 0,X=void 0,$a=void 0,Pb=void 0,ta=void 0,Fa=void 0,nd=void 0,Rc=void 0,Sc=void 0,le=void 0,pa= 
void 0,Id=void 0,Wf=void 0,kk=void 0,lk=void 0,mk=void 0,nk=void 0,ok=void 0,pk=void 0,qk=void 0,rk=void 0,sk=void 0,tk=void 0,uk=void 0,vk=void 0,wk=void 0,xk=void 0,yk=void 0,zk=void 0,Ak=Bh[0];if(Ak.length===0)throw Error("Cannot decode empty core segment string.");let Bk=KN(Ak,vO);const sp=IN(Bk.slice(0,6));Bk=Bk.slice(6);if(sp!==1)throw Error(`Unable to decode unsupported USNat Section specification version ${sp} - only version 1 is supported.`);let tp=0;const ra=[];for(let W=0;W<uO.length;W++){const S= 
uO[W];ra.push(IN(Bk.slice(tp,tp+S)));tp+=S}var Ch=new qO;zk=bi(Ch,1,sp);var up=ra.shift();yk=F(zk,2,up);var Ck=ra.shift();xk=F(yk,3,Ck);var vp=ra.shift();wk=F(xk,4,vp);var Dh=ra.shift();vk=F(wk,5,Dh);var wp=ra.shift();uk=F(vk,6,wp);var xp=ra.shift();tk=F(uk,7,xp);var yp=ra.shift();sk=F(tk,8,yp);var qc=ra.shift();rk=F(sk,9,qc);var zp=ra.shift();qk=F(rk,10,zp);var Ap=new pO,Bp=ra.shift();pk=F(Ap,1,Bp);var Dk=ra.shift();ok=F(pk,2,Dk);var Ek=ra.shift();nk=F(ok,3,Ek);var Fk=ra.shift();mk=F(nk,4,Fk);var Gk= 
ra.shift();lk=F(mk,5,Gk);var Tc=ra.shift();kk=F(lk,6,Tc);var Jd=ra.shift();Wf=F(kk,7,Jd);var rc=ra.shift();Id=F(Wf,8,rc);var Yb=ra.shift();pa=F(Id,9,Yb);var Cp=ra.shift();le=F(pa,10,Cp);var Xf=ra.shift();Sc=F(le,11,Xf);var Eh=ra.shift();Rc=F(Sc,12,Eh);nd=x(qk,11,Rc);var Uc=new oO,Zb=ra.shift();Fa=F(Uc,1,Zb);var od=ra.shift();ta=F(Fa,2,od);Pb=x(nd,12,ta);var Kd=ra.shift();$a=F(Pb,13,Kd);var wb=ra.shift();X=F($a,14,wb);var sc=ra.shift();U=F(X,15,sc);var Hk=ra.shift();const Qy=R=F(U,16,Hk);if(Bh.length=== 
1)var Ik=sO(Qy);else{var Jk=sO(Qy),pd=void 0,Fh=void 0,Yf=void 0,Zf=Bh[1];if(Zf.length===0)throw Error("Cannot decode empty GPC segment string.");const W=KN(Zf,3),S=IN(W.slice(0,2));if(S<0||S>1)throw Error(`Attempting to decode unknown GPC segment subsection type ${S}.`);Yf=S+1;const Ha=IN(W.charAt(2));var Ue=new rO;Fh=F(Ue,2,Yf);pd=E(Fh,1,!!Ha);Ik=x(Jk,2,pd)}const Ry=Ik,Kk=v(Ry,qO,1),YV=v(Kk,oO,12);D(Kk,8)!==1&&D(Kk,9)!==1&&D(Kk,10)!==1&&YV?.g()!==1||(e=!0);const Sy=v(v(Ry,qO,1),oO,12)?.i();Sy!== 
1&&Sy!==2||(g=!0);break;case 8:if(Ta.length===0)throw Error("Cannot decode empty USCA section string.");const Gh=Ta.split(".");if(Gh.length>2)throw Error(`Expected at most 1 sub-section but got ${Gh.length-1} when decoding ${Ta}.`);var Lk=void 0,Hh=void 0,Ty=void 0,Uy=void 0,Vy=void 0,Wy=void 0,Xy=void 0,Yy=void 0,Zy=void 0,$y=void 0,az=void 0,bz=void 0,cz=void 0,dz=void 0,ez=void 0,fz=void 0,gz=void 0,hz=void 0,iz=void 0,jz=void 0,kz=void 0,lz=void 0,mz=void 0,nz=Gh[0];if(nz.length===0)throw Error("Cannot decode empty core segment string."); 
let Mk=KN(nz,UN);const Dp=IN(Mk.slice(0,6));Mk=Mk.slice(6);if(Dp!==1)throw Error(`Unable to decode unsupported USCA Section specification version ${Dp} - only version 1 is supported.`);let Ep=0;const Ia=[];for(let W=0;W<TN.length;W++){const S=TN[W];Ia.push(IN(Mk.slice(Ep,Ep+S)));Ep+=S}var ZV=new PN;mz=bi(ZV,1,Dp);var $V=Ia.shift();lz=F(mz,2,$V);var aW=Ia.shift();kz=F(lz,3,aW);var bW=Ia.shift();jz=F(kz,4,bW);var cW=Ia.shift();iz=F(jz,5,cW);var dW=Ia.shift();hz=F(iz,6,dW);var eW=new ON,fW=Ia.shift(); 
gz=F(eW,1,fW);var gW=Ia.shift();fz=F(gz,2,gW);var hW=Ia.shift();ez=F(fz,3,hW);var iW=Ia.shift();dz=F(ez,4,iW);var jW=Ia.shift();cz=F(dz,5,jW);var kW=Ia.shift();bz=F(cz,6,kW);var lW=Ia.shift();az=F(bz,7,lW);var mW=Ia.shift();$y=F(az,8,mW);var nW=Ia.shift();Zy=F($y,9,nW);Yy=x(hz,7,Zy);var oW=new NN,pW=Ia.shift();Xy=F(oW,1,pW);var qW=Ia.shift();Wy=F(Xy,2,qW);Vy=x(Yy,8,Wy);var rW=Ia.shift();Uy=F(Vy,9,rW);var sW=Ia.shift();Ty=F(Uy,10,sW);var tW=Ia.shift();Hh=F(Ty,11,tW);var uW=Ia.shift();const oz=Lk=F(Hh, 
12,uW);if(Gh.length===1)var pz=RN(oz);else{var vW=RN(oz),qz=void 0,rz=void 0,sz=void 0,tz=Gh[1];if(tz.length===0)throw Error("Cannot decode empty GPC segment string.");const W=KN(tz,3),S=IN(W.slice(0,2));if(S<0||S>1)throw Error(`Attempting to decode unknown GPC segment subsection type ${S}.`);sz=S+1;const Ha=IN(W.charAt(2));var wW=new QN;rz=F(wW,2,sz);qz=E(rz,1,!!Ha);pz=x(vW,2,qz)}const uz=pz,vz=v(uz,PN,1);D(vz,5)!==1&&D(vz,6)!==1||(e=!0);const Nk=v(v(uz,PN,1),NN,8);Nk?.g()!==1&&Nk?.g()!==2&&Nk?.i()!== 
1&&Nk?.i()!==2||(g=!0);break;case 9:if(Ta.length===0)throw Error("Cannot decode empty USVA section string.");let Ok=KN(Ta,zO);const Fp=IN(Ok.slice(0,6));Ok=Ok.slice(6);if(Fp!==1)throw Error(`Unable to decode unsupported USVA Section specification version ${Fp} - only version 1 is supported.`);let Gp=0;const Ya=[];for(let W=0;W<yO.length;W++){const S=yO[W];Ya.push(IN(Ok.slice(Gp,Gp+S)));Gp+=S}var xW=Fp,yW=new xO,zW=bi(yW,1,xW),AW=Ya.shift(),BW=F(zW,2,AW),CW=Ya.shift(),DW=F(BW,3,CW),EW=Ya.shift(),FW= 
F(DW,4,EW),GW=Ya.shift(),HW=F(FW,5,GW),IW=Ya.shift();var JW=F(HW,6,IW);var KW=new wO,LW=Ya.shift(),MW=F(KW,1,LW),NW=Ya.shift(),OW=F(MW,2,NW),PW=Ya.shift(),QW=F(OW,3,PW),RW=Ya.shift(),SW=F(QW,4,RW),TW=Ya.shift(),UW=F(SW,5,TW),VW=Ya.shift(),WW=F(UW,6,VW),XW=Ya.shift(),YW=F(WW,7,XW),ZW=Ya.shift();var $W=F(YW,8,ZW);var aX=x(JW,7,$W),bX=Ya.shift(),cX=F(aX,8,bX),dX=Ya.shift(),eX=F(cX,9,dX),fX=Ya.shift(),gX=F(eX,10,fX),hX=Ya.shift();const Hp=F(gX,11,hX);D(Hp,5)!==1&&D(Hp,6)!==1||(e=!0);const wz=D(Hp,8); 
wz!==1&&wz!==2||(g=!0);break;case 10:if(Ta.length===0)throw Error("Cannot decode empty USCO section string.");const Ih=Ta.split(".");if(Ih.length>2)throw Error(`Expected at most 2 segments but got ${Ih.length} when decoding ${Ta}.`);var iX=void 0,xz=void 0,yz=void 0,zz=void 0,Az=void 0,Bz=void 0,Cz=void 0,Dz=void 0,Ez=void 0,Fz=void 0,Gz=void 0,Hz=void 0,Iz=void 0,Jz=void 0,Kz=void 0,Lz=void 0,Mz=void 0,Nz=void 0,Oz=Ih[0];if(Oz.length===0)throw Error("Cannot decode empty core segment string.");let Pk= 
KN(Oz,aO);const Ip=IN(Pk.slice(0,6));Pk=Pk.slice(6);if(Ip!==1)throw Error(`Unable to decode unsupported USCO Section specification version ${Ip} - only version 1 is supported.`);let Jp=0;const ib=[];for(let W=0;W<$N.length;W++){const S=$N[W];ib.push(IN(Pk.slice(Jp,Jp+S)));Jp+=S}var jX=new WN;Nz=bi(jX,1,Ip);var kX=ib.shift();Mz=F(Nz,2,kX);var lX=ib.shift();Lz=F(Mz,3,lX);var mX=ib.shift();Kz=F(Lz,4,mX);var nX=ib.shift();Jz=F(Kz,5,nX);var oX=ib.shift();Iz=F(Jz,6,oX);var pX=new VN,qX=ib.shift();Hz=F(pX, 
1,qX);var rX=ib.shift();Gz=F(Hz,2,rX);var sX=ib.shift();Fz=F(Gz,3,sX);var tX=ib.shift();Ez=F(Fz,4,tX);var uX=ib.shift();Dz=F(Ez,5,uX);var vX=ib.shift();Cz=F(Dz,6,vX);var wX=ib.shift();Bz=F(Cz,7,wX);Az=x(Iz,7,Bz);var xX=ib.shift();zz=F(Az,8,xX);var yX=ib.shift();yz=F(zz,9,yX);var zX=ib.shift();xz=F(yz,10,zX);var AX=ib.shift();const Pz=iX=F(xz,11,AX);if(Ih.length===1)var Qz=YN(Pz);else{var BX=YN(Pz),Rz=void 0,Sz=void 0,Tz=void 0,Uz=Ih[1];if(Uz.length===0)throw Error("Cannot decode empty GPC segment string."); 
const W=KN(Uz,3),S=IN(W.slice(0,2));if(S<0||S>1)throw Error(`Attempting to decode unknown GPC segment subsection type ${S}.`);Tz=S+1;const Ha=IN(W.charAt(2));var CX=new XN;Sz=F(CX,2,Tz);Rz=E(Sz,1,!!Ha);Qz=x(BX,2,Rz)}const Vz=Qz,Wz=v(Vz,WN,1);D(Wz,5)!==1&&D(Wz,6)!==1||(e=!0);const Xz=D(v(Vz,WN,1),8);Xz!==1&&Xz!==2||(g=!0);break;case 12:if(Ta.length===0)throw Error("Cannot decode empty usct section string.");const Jh=Ta.split(".");if(Jh.length>2)throw Error(`Expected at most 2 segments but got ${Jh.length} when decoding ${Ta}.`); 
var DX=void 0,Yz=void 0,Zz=void 0,$z=void 0,aA=void 0,bA=void 0,cA=void 0,dA=void 0,eA=void 0,fA=void 0,gA=void 0,hA=void 0,iA=void 0,jA=void 0,kA=void 0,lA=void 0,mA=void 0,nA=void 0,oA=void 0,pA=void 0,qA=void 0,rA=void 0,sA=Jh[0];if(sA.length===0)throw Error("Cannot decode empty core segment string.");let Qk=KN(sA,iO);const Kp=IN(Qk.slice(0,6));Qk=Qk.slice(6);if(Kp!==1)throw Error(`Unable to decode unsupported USCT Section specification version ${Kp} - only version 1 is supported.`);let Lp=0;const Oa= 
[];for(let W=0;W<hO.length;W++){const S=hO[W];Oa.push(IN(Qk.slice(Lp,Lp+S)));Lp+=S}var EX=new dO;rA=bi(EX,1,Kp);var FX=Oa.shift();qA=F(rA,2,FX);var GX=Oa.shift();pA=F(qA,3,GX);var HX=Oa.shift();oA=F(pA,4,HX);var IX=Oa.shift();nA=F(oA,5,IX);var JX=Oa.shift();mA=F(nA,6,JX);var KX=new cO,LX=Oa.shift();lA=F(KX,1,LX);var MX=Oa.shift();kA=F(lA,2,MX);var NX=Oa.shift();jA=F(kA,3,NX);var OX=Oa.shift();iA=F(jA,4,OX);var PX=Oa.shift();hA=F(iA,5,PX);var QX=Oa.shift();gA=F(hA,6,QX);var RX=Oa.shift();fA=F(gA,7, 
RX);var SX=Oa.shift();eA=F(fA,8,SX);dA=x(mA,7,eA);var TX=new bO,UX=Oa.shift();cA=F(TX,1,UX);var VX=Oa.shift();bA=F(cA,2,VX);var WX=Oa.shift();aA=F(bA,3,WX);$z=x(dA,8,aA);var XX=Oa.shift();Zz=F($z,9,XX);var YX=Oa.shift();Yz=F(Zz,10,YX);var ZX=Oa.shift();const tA=DX=F(Yz,11,ZX);if(Jh.length===1)var uA=fO(tA);else{var $X=fO(tA),vA=void 0,wA=void 0,xA=void 0,yA=Jh[1];if(yA.length===0)throw Error("Cannot decode empty GPC segment string.");const W=KN(yA,3),S=IN(W.slice(0,2));if(S<0||S>1)throw Error(`Attempting to decode unknown GPC segment subsection type ${S}.`); 
xA=S+1;const Ha=IN(W.charAt(2));var aY=new eO;wA=F(aY,2,xA);vA=E(wA,1,!!Ha);uA=x($X,2,vA)}const zA=uA,Mp=v(zA,dO,1),AA=v(Mp,bO,8);D(Mp,5)!==1&&D(Mp,6)!==1&&AA?.i()!==1&&AA?.j()!==1||(e=!0);const BA=v(v(zA,dO,1),bO,8);BA?.g()!==1&&BA?.g()!==2||(g=!0);break;case 13:if(Ta.length===0)throw Error("Cannot decode empty USFL section string.");let Rk=KN(Ta,nO);const Np=IN(Rk.slice(0,6));Rk=Rk.slice(6);if(Np!==1)throw Error(`Unable to decode unsupported USFL Section specification version ${Np} - only version 1 is supported.`); 
let Op=0;const Ja=[];for(let W=0;W<mO.length;W++){const S=mO[W];Ja.push(IN(Rk.slice(Op,Op+S)));Op+=S}var bY=Np,cY=new lO,dY=bi(cY,1,bY),eY=Ja.shift(),fY=F(dY,2,eY),gY=Ja.shift(),hY=F(fY,3,gY),iY=Ja.shift(),jY=F(hY,4,iY),kY=Ja.shift(),lY=F(jY,5,kY),mY=Ja.shift();var nY=F(lY,6,mY);var oY=new kO,pY=Ja.shift(),qY=F(oY,1,pY),rY=Ja.shift(),sY=F(qY,2,rY),tY=Ja.shift(),uY=F(sY,3,tY),vY=Ja.shift(),wY=F(uY,4,vY),xY=Ja.shift(),yY=F(wY,5,xY),zY=Ja.shift(),AY=F(yY,6,zY),BY=Ja.shift(),CY=F(AY,7,BY),DY=Ja.shift(); 
var EY=F(CY,8,DY);var FY=x(nY,7,EY);var GY=new jO,HY=Ja.shift(),IY=F(GY,1,HY),JY=Ja.shift(),KY=F(IY,2,JY),LY=Ja.shift();var MY=F(KY,3,LY);var NY=x(FY,8,MY),OY=Ja.shift(),PY=F(NY,9,OY),QY=Ja.shift(),RY=F(PY,10,QY),SY=Ja.shift(),TY=F(RY,11,SY),UY=Ja.shift();const Sk=F(TY,12,UY),CA=v(Sk,jO,8);D(Sk,5)!==1&&D(Sk,6)!==1&&CA?.i()!==1&&CA?.j()!==1||(e=!0);const DA=v(Sk,jO,8)?.g();DA!==1&&DA!==2||(g=!0)}}}return{Mk:d,Ah:e,Qk:f,Ng:g}} 
var FS=class extends N{constructor(a){({timeoutMs:b}={});var b;super();this.caller=new uI(a,"__gppLocator",c=>typeof c.__gpp==="function",AS);this.caller.F.set("addEventListener",wS);this.caller.B.set("addEventListener",yS);this.caller.F.set("removeEventListener",xS);this.caller.B.set("removeEventListener",zS);this.timeoutMs=b??500}i(){this.caller.dispose();super.i()}addEventListener(a){const b=kb(()=>{a(CS,!0)}),c=this.timeoutMs===-1?void 0:setTimeout(()=>{b()},this.timeoutMs);tI(this.caller,"addEventListener", 
{listener:(d,e)=>{clearTimeout(c);try{if(d.pingData?.gppVersion===void 0||d.pingData.gppVersion==="1"||d.pingData.gppVersion==="1.0"){this.removeEventListener(d.listenerId);var f={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",applicableSections:[-1]}}}else Array.isArray(d.pingData.applicableSections)?f=d:(this.removeEventListener(d.listenerId),f={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY", 
applicableSections:[-1]}});a(f,e)}catch{if(d?.listenerId)try{this.removeEventListener(d.listenerId)}catch{a(DS,!0);return}a(ES,!0)}}})}removeEventListener(a){tI(this.caller,"removeEventListener",{listener:()=>{},listenerId:a})}}; 
const ES={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},CS={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},DS={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1}; 
function GS(a){return!a||a.length===1&&a[0]===-1};function HS(a){a=new FS(a);if(!rI(a.caller))return Promise.resolve(null);const b=uH(),c=zH(b,35);if(c)return Promise.resolve(c);const d=new Promise(e=>{e={resolve:e};const f=zH(b,36,[]);f.push(e);AH(b,36,f)});c||c===null||(AH(b,35,null),a.addEventListener(e=>{if(e.pingData.signalStatus==="ready"||GS(e.pingData.applicableSections)){e=e.pingData;AH(b,35,e);for(const f of zH(b,36,[]))f.resolve(e);AH(b,36,[])}}));return d};function IS(a){a=new NI(a,{timeoutMs:-1,Fi:!0});if(!JI(a))return Promise.resolve(null);const b=uH(),c=zH(b,24);if(c)return Promise.resolve(c);const d=new Promise(e=>{e={resolve:e};const f=zH(b,25,[]);f.push(e);AH(b,25,f)});c||c===null||(AH(b,24,null),a.addEventListener(e=>{if(DI(e)){AH(b,24,e);for(const f of zH(b,25,[]))f.resolve(e);AH(b,25,[])}else AH(b,24,null)}));return d};const JS=(a,b)=>{(0,a.__uspapi)("getUSPData",1,(c,d)=>{b.hb({Ic:c??void 0,Yg:d?void 0:2})})},KS={ae:a=>a.hb,hd:(a,b)=>({__uspapiCall:{callId:b,command:"getUSPData",version:1}}),sc:(a,b)=>{b=b.__uspapiReturn;a({Ic:b.returnValue??void 0,Yg:b.success?void 0:2})}};function LS(a){let b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,eg:b.__uspapiReturn.callId}} 
function MS(a,b){let c={};if(rI(a.caller)){var d=kb(()=>{b(c)});tI(a.caller,"getDataWithCallback",{hb:e=>{e.Yg||(c=e.Ic);d()}});setTimeout(d,a.timeoutMs)}else b(c)}var NS=class extends N{constructor(a){super();this.timeoutMs={}.timeoutMs??500;this.caller=new uI(a,"__uspapiLocator",b=>typeof b.__uspapi==="function",LS);this.caller.F.set("getDataWithCallback",JS);this.caller.B.set("getDataWithCallback",KS)}i(){this.caller.dispose();super.i()}};function OS(a){const b=new NS(a);return new Promise(c=>{MS(b,d=>{d&&typeof d.uspString==="string"?c(d.uspString):c(null)})})} 
function PS(a,{Pk:b,Xk:c,zj:d}){var e=new wN;var f=M(Hw)?hi(d,5)?d.g():hi(b,5)?b.g():a.g():hi(b,5)?b.g():a.g();e=sN(e,f);f=M(Hw)?hi(d,8)?A(d,8):hi(b,8)?A(b,8):void 0:Xh(b,8);e=tN(e,f);a=Xh(a,14);a=$h(e,14,a);f=Xh(b,3);a=$h(a,3,f);f=y(b,2)??void 0;a=ei(a,2,f);f=y(b,4)??void 0;a=ei(a,4,f);f=Zh(b,7);a=gi(a,7,f);b=Xh(b,9);b=$h(a,9,b);a=y(c,1)??void 0;b=ei(b,1,a);c=Xh(c,13);c=$h(b,13,c);b=y(d,11)??void 0;c=ei(c,11,b);b=fh(d,10,kg,eh(),void 0,0);c=oh(c,10,b,Uf);uN(c,Xh(d,12));return e} 
async function QS(a,{Fa:b,Mj:c}){const [d,e,f]=await Promise.all([IS(a.pubWin),OS(a.pubWin),HS(a.pubWin)]);b=!!b&&(M(Iw)||!RP());var g=sN(new wN,!b);c=$h(g,14,c&&navigator.globalPrivacyControl);g=new wN;if(d){var h=sN(g,EI(d,{idpcApplies:b}));h=ei(h,2,d.tcString);h=ei(h,4,d.addtlConsent||"");h=gi(h,7,d.internalErrorState);var k=!HI(d,["3","4"],0);h=$h(h,9,k);tN(h,!HI(d,["2","7","9","10"],3));d.gdprApplies!=null&&$h(g,3,d.gdprApplies)}h=new wN;if(e){k=ei(h,1,e);var l=e.toUpperCase();if(l.length==4&& 
(l.indexOf("-")==-1||l.substring(1)==="---")&&l[0]>="1"&&l[0]<="9"&&EN.hasOwnProperty(l[1])&&EN.hasOwnProperty(l[2])&&EN.hasOwnProperty(l[3])){var m=new DN;m=bi(m,1,parseInt(l[0],10));m=F(m,2,EN[l[1]]);m=F(m,3,EN[l[2]]);l=F(m,4,EN[l[3]])}else l=null;l=l?.wj()===2;$h(k,13,l)}k=new wN;if(f)if(f.internalErrorState)ei(k,11,f.gppString);else if(GS(f.applicableSections))uN(oh(k,10,f.applicableSections,Uf),!1),M(Hw)&&sN(k,!0);else if(l=oh(k,10,f.applicableSections,Uf),ei(l,11,f.gppString),M(Hw))try{const n= 
BS(f.gppString,f.applicableSections,{idpcApplies:b,supportTcfeu:!0});tN(vN(uN(sN(k,n.Mk),n.Ah),n.Ng),n.Qk)}catch(n){zB(1182,n),tN(vN(uN(sN(k,!b),!1),!1),!1)}else try{const n=BS(f.gppString,f.applicableSections,{idpcApplies:b});vN(uN(k,n.Ah),n.Ng)}catch(n){zB(1182,n),vN(uN(k,!1),!1)}a.j=PS(c,{Pk:g,Xk:h,zj:k})};async function RS(a){const b=sm(),c=a.sa,d=a.pageState.g();YH(g=>{if(D(g,1)===0){var h=!(d.i()?!A(d,4):!A(c,6));g=E(g,2,h);h=!(hi(d,5)?!A(d,5):!A(c,20));g=E(g,6,h);F(g,1,1)}});SS(a.pubWin,v(d,wh,10)||vh(c));TS(a.D.google_ad_client);YH(g=>{D(g,1)===1&&F(g,1,2)});const e=new BI(a.pubWin);await US(e,C(d,8)||C(c,8));YH(g=>{D(g,1)===2&&(g=E(g,3,!0),F(g,1,3))});await QS(a,{Fa:d.i()?A(d,4):A(c,6),Mj:hi(d,7)?A(d,7):A(c,25)});const f=sm();YH(g=>{if(D(g,1)===3){g=E(g,3,f-b>500);var h=!!a.j?.l();g=E(g,4,h); 
h=!!a.j?.g();g=E(g,5,h);h=!!a.j?.i();g=E(g,7,h);h=!!a.j?.j();g=E(g,8,h);F(g,1,4)}})}function SS(a,b){var c=M(Jw);a!==a.top||a.__uspapi||a.frames.__uspapiLocator||(a=new QP(a,b,c),LP(a),MP(a))}function TS(a){var b=Ld(q.top,"googlefcPresent");q.googlefc&&!b&&yB("adsense_fc_has_namespace_but_no_iframes",{publisherId:a},1)}function US(a,b){return yI(a,b===".google.cn")?zI(a):Promise.resolve(null)};function VS(a,b=!1){try{return b?(new pl(a.innerWidth,a.innerHeight)).round():Fl(a||window).round()}catch(c){return new pl(-12245933,-12245933)}}function WS(a=q){a=a.devicePixelRatio;return typeof a==="number"?+a.toFixed(3):null}function XS(a,b=q){a=a.scrollingElement||(a.compatMode==="CSS1Compat"?a.documentElement:a.body);return new ll(b.pageXOffset||a.scrollLeft,b.pageYOffset||a.scrollTop)} 
function YS(a){try{return!(!a||!(a.offsetWidth||a.offsetHeight||a.getClientRects().length))}catch(b){return!1}};function ZS(a,b){var c=sB,d;var e;d=(e=(e=vl())&&(d=e.initialLayoutRect)&&typeof d.top==="number"&&typeof d.left==="number"&&typeof d.width==="number"&&typeof d.height==="number"?new rl(d.left,d.top,d.width,d.height):null)?new ll(e.left,e.top):(d=yl())&&oa(d.rootBounds)?new ll(d.rootBounds.left+d.boundingClientRect.left,d.rootBounds.top+d.boundingClientRect.top):null;if(d)return d;try{{const k=new ll(0,0);var f=El(b);let l=f?f.defaultView:window;if(ac(l,"parent")){do{if(l==a)var g=Yl(b);else{const m= 
Xl(b);g=new ll(m.left,m.top)}f=g;k.x+=f.x;k.y+=f.y}while(l&&l!=a&&l!=l.parent&&(b=l.frameElement)&&(l=l.parent))}var h=k}return h}catch(k){return c.ma(888,k),new ll(-12245933,-12245933)}}function $S(a,b,c,d=!1){a=ZS(a,c);c=zl()||VS(b.top);if(!a||a.y===-12245933||c.width===-12245933||c.height===-12245933||!c.height)return 0;let e=0;try{const f=b.top;e=XS(f.document,f).y}catch(f){return 0}b=e+c.height;return a.y<e?d?0:(e-a.y)/c.height:a.y>b?(a.y-b)/c.height:0};function aT(a,b,c){var d=TQ(a,"__gpi_opt_out",b);d&&(d=ei(ci(Uk(d),2,2147483647),3,"/"),c=ei(d,4,c),UQ(a,"__gpi_opt_out",c,b))}function bT(a,b,c,d){const e=UP(a,"gpi-uoo",(f,g)=>{if(g.source===c){g=ei(ci(Uk(f.userOptOut?"1":"0"),2,2147483647),3,"/");g=ei(g,4,a.location.hostname);var h=new XQ(a);UQ(h,"__gpi_opt_out",g,b);if(f.userOptOut||f.clearAdsData)VQ(h,"__gads",b),VQ(h,"__gpi",b)}});d.push(e)};function cT(a,b){const c=a.pubWin,d=a.D.google_ad_client,e=CH();let f=null;const g=UP(c,"pvt",(h,k)=>{typeof h.token==="string"&&k.source===b.contentWindow&&(f=h.token,g(),e[d]=e[d]||[],e[d].push(f),e[d].length>100&&e[d].shift())});a.i.push(g);return()=>{f&&Array.isArray(e[d])&&(Ra(e[d],f),e[d].length||delete e[d],f=null)}};function dT(a){return a.length?a.join("~"):void 0};function eT({L:a,dk:b,Vj:c,Ni:d,Wo:e,Xo:f,I:g,Bj:h}){let k=0;try{k|=gr(a,f);const n=Math.min(a.screen.width||0,a.screen.height||0);k|=n?n<320?8192:0:2048;k|=a.navigator&&fT(a.navigator.userAgent)?1048576:0;if(b){f=k;const p=a.innerHeight;var l=(Kb()&&Lb()?ce(a):1)*p>=b;var m=f|(l?0:1024)}else m=k|(a.innerHeight>=a.innerWidth?0:8);k=m;k|=jr(a,c,!0,e)}catch{k|=32}switch(d){case 2:gT(a,g,h)&&(k|=16777216);break;case 1:hT(a,g,h)&&(k|=16777216)}return k} 
function fT(a){return/Android 2/.test(a)||/iPhone OS [34]_/.test(a)||/Windows Phone (?:OS )?[67]/.test(a)||/MSIE.*Windows NT/.test(a)||/Windows NT.*Trident/.test(a)}function gT(a,b=null,c=!1){const d=CF({ug:0,vf:a.innerWidth,Xf:3,vg:0,wf:Math.min(Math.round(a.innerWidth/320*50),iT)+15,Yf:3});return HF(jT(a,b),d,c)} 
function hT(a,b=null,c=!1){const d=a.innerWidth,e=a.innerHeight,f=Math.min(Math.round(a.innerWidth/320*50),iT)+15,g=CF({ug:0,vf:d,Xf:3,vg:e-f,wf:e,Yf:3});f>25&&g.push({x:d-25,y:e-25});return HF(jT(a,b),g,c)}function jT(a,b=null){return new JF(a,{ah:kT(a,b)})}function kT(a,b=null){if(b)return(c,d,e)=>{Jm(b,"ach_evt",{tn:c.tagName,id:c.getAttribute("id")??"",cls:c.getAttribute("class")??"",ign:String(e),pw:a.innerWidth,ph:a.innerHeight,x:d.x,y:d.y},!0,1)}}const iT=90*1.38;function lT(a,b){return eT({L:a,Vj:3E3,dk:a.innerWidth>fr?650:0,I:qB,Ni:b,Bj:M(Xu)})};function mT(a){let b=0;try{b|=gr(a)}catch(c){b|=32}return b};function nT(a){let b=0;try{b|=gr(a),b|=jr(a,1E4)}catch(c){b|=32}return b};var wh=class extends G{};var oT=class extends G{g(){return C(this,3)}i(){return hi(this,4)}};var pT=class extends G{g(){return xh(this,oT,1)}},qT=Uj(pT);function rT(){var a=q.adsbygoogle;try{const b=a.pageState;bf(b,gf);return qT(b)}catch(b){return new pT}};function sT(){const a={};kx(Yu)&&(a.bust=kx(Yu));return a};function tT(){const {promise:a,resolve:b}=new ZP;return{promise:a,resolve:b}};function uT(a,b,c=()=>{}){b.google_llp||(b.google_llp={});b=b.google_llp;let d=b[a];if(d)return d;d=tT();b[a]=d;c();return d}function vT(a,b,c){return uT(a,b,()=>{rd(b.document,c)}).promise};function wT(a){return a.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||""]||0}function xT(a){let b;a.visibilityState?b="visibilitychange":a.mozVisibilityState?b="mozvisibilitychange":a.webkitVisibilityState&&(b="webkitvisibilitychange");return b}function yT(a){return a.hidden!=null?a.hidden:a.mozHidden!=null?a.mozHidden:a.webkitHidden!=null?a.webkitHidden:null} 
function zT(a,b){if(wT(b)==3)var c=!1;else a(),c=!0;if(!c){const d=()=>{rb(b,"prerenderingchange",d);a()};qb(b,"prerenderingchange",d)}};Array.from({length:11},(a,b)=>b/10);function AT(a,b=!1){let c=0;try{c|=gr(a);var d;if(!(d=!a.navigator)){var e=a.navigator;d="brave"in e&&"isBrave"in e.brave||!1}c|=d||/Android 2/.test(a.navigator.userAgent)?1048576:0;c|=jr(a,b?Number.MAX_SAFE_INTEGER:2500,!0)}catch(f){c|=32}return c};const BT="body div footer header html main section".split(" ");function CT(a,b=null,c=!1,d=!1,e=!1){let f=gr(a);fT(a.navigator?.userAgent)&&(f|=1048576);const g=a.innerWidth;g<1200&&(f|=65536);const h=a.innerHeight;h<650&&(f|=2097152);b&&f===0&&(b=b===3?"left":"right",(c=DT({L:a,Qj:!1,Hk:1,position:b,V:g,Z:h,Yb:new Set,minWidth:120,minHeight:500,Df:c,ag:d,Zf:e}))?BD(a).sideRailPlasParam.set(b,`${c.width}x${c.height}_${String(b).charAt(0)}`):f|=16);return f} 
function ET(a){a=BD(a).sideRailPlasParam;return[...Array.from(a.values())].join("|")}function FT(a,b){return Ol(a,c=>c.nodeType===Node.ELEMENT_NODE&&b.has(c))!==null}function GT(a,b){return Ol(a,c=>c.nodeType===Node.ELEMENT_NODE&&b.getComputedStyle(c,null).position==="fixed")}function HT(a){const b=[];for(const c of a.document.querySelectorAll("*")){const d=a.getComputedStyle(c,null);d.position==="fixed"&&d.display!=="none"&&d.visibility!=="hidden"&&b.push(c)}return b} 
function IT(a,b){const {top:c,left:d,bottom:e,right:f}=b.getBoundingClientRect();return c>=0&&d>=0&&e<=a.innerHeight&&f<=a.innerWidth}function JT(a){return Math.round(Math.round(a/10)*10)}function KT(a){return`${a.position}-${JT(a.V)}x${JT(a.Z)}-${JT(a.scrollY+a.wc)}Y`}function LT(a){return`f-${KT({position:a.position,wc:a.wc,scrollY:0,V:a.V,Z:a.Z})}`}function MT(a,b){a=Math.min(a??Infinity,b??Infinity);return a!==Infinity?a:0} 
function NT(a,b,c){const d=BD(c.L).sideRailProcessedFixedElements;if(!d.has(a)){var e=a.getBoundingClientRect();if(e){var f=Math.max(e.top-10,0),g=Math.min(e.bottom+10,c.Z),h=Math.max(e.left-10,0);e=Math.min(e.right+10,c.V);for(var k=c.V*.3;f<=g;f+=10){if(e>0&&h<k){var l=LT({position:"left",wc:f,V:c.V,Z:c.Z});b.set(l,MT(b.get(l),h))}if(h<c.V&&e>c.V-k){l=LT({position:"right",wc:f,V:c.V,Z:c.Z});const m=c.V-e;b.set(l,MT(b.get(l),m))}}d.add(a)}}} 
function OT(a,b){const c=b.L,d=b.Df,e=b.Zf;var f=`f-${JT(b.V)}x${JT(b.Z)}`;a.has(f)||(a.set(f,0),f=HT(c),d||e?(PT(a,b,f.filter(g=>IT(c,g))),QT(c,f.filter(g=>!IT(c,g)).concat(e?Array.from(c.document.querySelectorAll("[google-side-rail-overlap=false]")):[]))):PT(a,b,f))} 
function PT(a,b,c){var d=b.Yb;const e=b.L;BD(e).sideRailProcessedFixedElements.clear();d=new Set([...Array.from(e.document.querySelectorAll("[data-anchor-status],[data-side-rail-status]")),...d]);for(const f of c)FT(f,d)||NT(f,a,b)} 
function RT(a){if(a.V<1200||a.Z<650)return null;var b=BD(a.L).sideRailAvailableSpace;a.Qj||OT(b,{L:a.L,V:a.V,Z:a.Z,Yb:a.Yb,Df:a.Df,Zf:a.Zf});const c=[];var d=a.Z*.9,e=rr(a.L),f=(a.Z-d)/2,g=f,h=d/7;for(var k=0;k<8;k++){var l=c,m=l.push;a:{var n=g;var p=a.position,t=b,z={L:a.L,V:a.V,Z:a.Z,Yb:a.Yb,ag:a.ag};const T=LT({position:p,wc:n,V:z.V,Z:z.Z}),R=KT({position:p,wc:n,scrollY:e,V:z.V,Z:z.Z});if(t.has(R)){n=MT(t.get(T),t.get(R));break a}const U=p==="left"?20:z.V-20;let X=U;p=z.V*.3/5*(p==="left"?1:-1); 
for(let $a=0;$a<6;$a++){var w=DF(z.L.document,{x:Math.round(X),y:Math.round(n)}),B=FT(w,z.Yb),I=GT(w,z.L);if(!B&&I!==null){NT(I,t,z);t.delete(R);break}B||(B=z,w.getAttribute("google-side-rail-overlap")==="true"?B=!0:w.getAttribute("google-side-rail-overlap")==="false"||B.ag&&!BT.includes(w.tagName.toLowerCase())?B=!1:(I=w.offsetHeight>=B.Z*.25,B=w.offsetWidth>=B.V*.9&&I));if(B)t.set(R,Math.round(Math.abs(X-U)+20));else if(X!==U)X-=p,p/=2;else{t.set(R,0);break}X+=p}n=MT(t.get(T),t.get(R))}m.call(l, 
n);g+=h}b=a.Hk;e=a.position;d=Math.round(d/8);f=Math.round(f);g=a.minWidth;a=a.minHeight;m=[];h=Array(c.length).fill(0);for(l=0;l<c.length;l++){for(;m.length!==0&&c[m[m.length-1]]>=c[l];)m.pop();h[l]=m.length===0?0:m[m.length-1]+1;m.push(l)}m=[];k=c.length-1;l=Array(c.length).fill(0);for(n=k;n>=0;n--){for(;m.length!==0&&c[m[m.length-1]]>=c[n];)m.pop();l[n]=m.length===0?k:m[m.length-1]-1;m.push(n)}m=null;for(k=0;k<c.length;k++)if(n={position:e,width:Math.round(c[k]),height:Math.round((l[k]-h[k]+1)* 
d),offsetY:f+h[k]*d},t=n.width>=g&&n.height>=a,b===0&&t){m=n;break}else b===1&&t&&(!m||n.width*n.height>m.width*m.height)&&(m=n);return m}function QT(a,b){const c=BD(a);if(b.length&&!c.g){var d=new MutationObserver(()=>{setTimeout(()=>{ST(a);for(const e of c.sideRailMutationCallbacks)e()},500)});for(const e of b)d.observe(e,{attributes:!0});c.g=d}}function ST(a){({sideRailAvailableSpace:a}=BD(a));const b=Array.from(a.keys()).filter(c=>c.startsWith("f-"));for(const c of b)a.delete(c)} 
function DT(a){if(a.za)return a.za.mb(1228,()=>RT(a))||null;try{return RT(a)}catch{}return null};const TT={[27]:512,[26]:128}; 
var UT=(a,b,c,d)=>{d=mN(d);switch(c){case 1:case 2:return lT(a,c)===0;case 3:case 4:return CT(a,c,!0,M(Ev),!0)===0;case 8:return AT(a,M(Qu))===0;case 9:return b=!(b.google_adtest==="on"||wQ(a.location,"google_scr_debug")),!TJ(a,b,d);case 30:return JL(a)===0;case 26:return nT(a)===0;case 27:return mT(a)===0;case 40:return!0;default:return!1}},VT=(a,b,c,d)=>{d=d?mN(d):null;switch(c){case 0:case 40:case 10:case 11:return 0;case 1:case 2:return lT(a,c);case 3:case 4:return CT(a,c,!1,M(Ev));case 8:return AT(a, 
M(Qu));case 9:return TJ(a,!(b.google_adtest==="on"||wQ(a.location,"google_scr_debug")),d);case 16:return sx(b,a)?0:8388608;case 30:return JL(a);case 26:return nT(a);case 27:return mT(a);default:return 32}},WT=(a,b,c)=>{const d=b.google_reactive_ad_format;if(!Sb(d))return!1;a=qd(a);if(!a||!UT(a,b,d,c))return!1;b=BD(a);if(nr(b,d))return!1;b.adCount[d]||(b.adCount[d]=0);b.adCount[d]++;return!0},YT=a=>{const b=a.google_reactive_ad_format;return!a.google_reactive_ads_config&&XT(a)&&b!==16&&b!==10&&b!== 
11&&b!==40&&b!==41},ZT=a=>{if(!a.hash)return null;let b=null;vd(tQ,c=>{!b&&wQ(a,c)&&(b=uQ[c]||null)});return b},aU=(a,b)=>{const c=BD(a).tagSpecificState[1]||null;c!==null&&c.debugCard==null&&vd(vQ,d=>{!c.debugCardRequested&&typeof d==="number"&&zQ(d,a.location)&&(c.debugCardRequested=!0,$T(a,b,e=>{c.debugCard=e.createDebugCard(d,a)}))})},cU=(a,b,c)=>{if(!b)return null;const d=BD(b);let e=0;vd(Tb,f=>{const g=TT[f];g&&bU(a,b,f,c)===0&&(e|=g)});d.wasPlaTagProcessed&&(e|=256);a.google_reactive_tag_first&& 
(e|=1024);return e?`${e}`:null},dU=(a,b,c)=>{const d=[];vd(Tb,e=>{const f=bU(b,a,e,c);f!==0&&d.push(`${e}:${f}`)});return d.join(",")||null},eU=a=>{const b=[],c={};vd(a,(d,e)=>{if((e=dr[e])&&!c[e]){c[e]=!0;if(d)d=1;else if(d===!1)d=2;else return;b.push(`${e}:${d}`)}});return b.join(",")},fU=a=>{a=a.overlays;if(!a)return"";a=a.bottom;return typeof a==="boolean"?a?"1":"0":""},bU=(a,b,c,d)=>{if(!b)return 256;let e=0;const f=BD(b),g=nr(f,c);if(a.google_reactive_ad_format===c||g)e|=64;let h=!1;vd(f.reactiveTypeDisabledByPublisher, 
(k,l)=>{String(c)===String(l)&&(h=!0)});return h&&ZT(b.location)!==c&&(e|=128,c===2||c===1||c===3||c===4||c===8)?e:e|VT(b,a,c,d)},gU=(a,b)=>{if(a){var c=BD(a),d={};vd(b,(e,f)=>{(f=dr[f])&&(e===!1||/^false$/i.test(e))&&(d[f]=!0)});vd(Tb,e=>{d[er[e]]&&(c.reactiveTypeDisabledByPublisher[e]=!0)})}},hU=(a,b,c)=>{b=wB(b,c);c={...sT()};const d=V(Kw);[0,1].includes(d)&&(c.osttc=`${d}`);return vT(1,window,id(a,new Map(Object.entries(c)))).then(b)},$T=(a,b,c)=>{c=wB(212,c);vT(3,a,b).then(c)},iU=(a,b,c)=>{a.dataset.adsbygoogleStatus= 
"reserved";a.className+=" adsbygoogle-noablate";c.adsbygoogle||(c.adsbygoogle=[],rd(c.document,hd`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js`));c.adsbygoogle.push({element:a,params:b})},jU=a=>{a=a.google_reactive_ad_format;return Sb(a)?`${a}`:null},XT=a=>!!jU(a)||a.google_pgb_reactive!=null,kU=a=>{a=Number(jU(a));return a===26||a===27||a===30||a===16||a===40||a===41};function lU(a){return typeof a.google_reactive_sra_index==="number"} 
function mU(a,b,c){const d=b.L||b.pubWin,e=b.D,f=mN(c);c=dU(d,e,c);e.google_reactive_plat=c;(c=eU(a))&&(e.google_reactive_plaf=c);(c=fU(a))&&(e.google_reactive_fba=c);(c=ET(d))&&(e.google_plas=c);nU(a,e);c=ZT(b.pubWin.location);oU(a,c,e);c?(e.fra=c,e.google_pgb_reactive=6):e.google_pgb_reactive=5;e.asro=M(lw);e.aihb=M(Hv);e.ailel=dT(lx(Zv));e.aiael=dT(lx(Jv));e.aicel=dT(lx(Lv));e.aifxl=dT(lx(Rv));e.aiixl=dT(lx(Wv));V(pw)&&(e.aiapm=V(pw));V(qw)&&(e.aiapmi=V(qw));V(Iv)&&(e.aiact=V(Iv));V(Kv)&&(e.aicct= 
V(Kv));V(Yv)&&(e.ailct=V(Yv));e.aiof=dT(lx(gw));e.fsapi=!0;c!==8&&(f&&PJ(f)?(c=SJ(f,86400,"__lsv__"),c?.length&&(c=Math.floor((Date.now()-Math.max(...c))/6E4),c>=0&&(e.vmsli=c))):e.vmsli=-1);c=SJ(f,600,"__lsa__");c?.length&&Math.floor((Date.now()-Math.max(...c))/6E4)<=V(Pu)&&(e.dap=3);zl()||VS(b.pubWin.top);c=VP(b.pubWin,"rsrai",wB(429,(g,h)=>pU(b,d,e.google_ad_client,a,g,h,f)),wB(430,(g,h)=>ur(b.pubWin,"431",qB,h)));b.i.push(c);BD(d).wasReactiveTagRequestSent=!0;qU(b,a,f)} 
function qU(a,b,c){const d=a.D,e=oa(b.page_level_pubvars)?b.page_level_pubvars:{};b=VP(a.pubWin,"apcnf",wB(353,(f,g)=>{var h=a.pubWin,k=d.google_ad_client;return Vd(g.origin)?cR(h,k,e,f.config,c,null):!1}),wB(353,(f,g)=>ur(a.pubWin,"353",qB,g)));a.i.push(b)} 
function pU(a,b,c,d,e,f,g){if(!Vd(f.origin))return!1;f=e.data;if(!Array.isArray(f))return!1;if(!JH(b,1))return!0;f&&kl(6,[f]);e=e.amaConfig;const h=[],k=[],l=BD(b);let m=null;for(let n=0;n<f.length;n++){if(!f[n])continue;const p=f[n],t=p.adFormat;l&&p.enabledInAsfe&&(l.reactiveTypeEnabledInAsfe[t]=!0);if(!p.noCreative){p.google_reactive_sra_index=n;if(t===9&&e){p.pubVars=Object.assign(p.pubVars||{},rU(d,p));const z=new UJ;if(NJ(z,p)&&z.H(p)){m=z;continue}}h.push(p);k.push(t)}}h.length&&hU(a.Ka.Jh, 
522,n=>{sU(h,b,n,d,g)});e&&cR(b,c,d,e,g,m);return!0}function rU(a,b){const c=b.adFormat,d=b.adKey;delete b.adKey;const e={};a=a.page_level_pubvars;oa(a)&&Object.assign(e,a);e.google_ad_unit_key=d;e.google_reactive_sra_index=b.google_reactive_sra_index;c===30&&(e.google_reactive_ad_format=30);e.google_pgb_reactive=e.google_pgb_reactive||5;return b.pubVars=e} 
function sU(a,b,c,d,e){const f=[];for(let g=0;g<a.length;g++){const h=a[g],k=h.adFormat,l=h.adKey,m=c.configProcessorForAdFormat(k);k&&m&&l&&(h.pubVars=rU(d,h),delete h.google_reactive_sra_index,f.push(k),vB(466,()=>m.verifyAndProcessConfig(b,h,e)))}} 
function nU(a,b){const c=[];let d=!1;vd(dr,(e,f)=>{let g;a.hasOwnProperty(f)&&(f=a[f],f?.google_ad_channel&&(g=String(f.google_ad_channel)));--e;c[e]&&c[e]!=="+"||(c[e]=g?g.replace(/,/g,"+"):"+",d||(d=!!g))});d&&(b.google_reactive_sra_channels=c.join(","))}function oU(a,b,c){if(!c.google_adtest){var d=a.page_level_pubvars;if(a.google_adtest==="on"||d?.google_adtest==="on"||b)c.google_adtest="on"}};$b("script");var tU={"image-top":0,"image-middle":1,"image-side":2,"text-only":3,"in-article":4};function uU(a,b){if(!sx(b,a))return()=>{};a=vU(b,a);if(!a)return()=>{};const c=FH();b=Vb(b);const d={Ib:a,D:b,offsetWidth:a.offsetWidth};c.push(d);return()=>Ra(c,d)}function vU(a,b){a=b.document.getElementById(a.google_async_iframe_id);if(!a)return null;for(a=a.parentElement;a&&!xx.test(a.className);)a=a.parentElement;return a} 
function wU(a,b){for(let f=0;f<a.childNodes.length;f++){const g={},h=a.childNodes[f];var c=h.style,d=["width","height"];for(let k=0;k<d.length;k++){const l="google_ad_"+d[k];if(!g.hasOwnProperty(l)){var e=Dd(c[d[k]]);e=e===null?null:Math.round(e);e!=null&&(g[l]=e)}}if(g.google_ad_width==b.google_ad_width&&g.google_ad_height==b.google_ad_height)return h}return null} 
function xU(a,b){a.style.display=b?"inline-block":"none";const c=a.parentElement;b?c.dataset.adStatus=a.dataset.adStatus:(a.dataset.adStatus=c.dataset.adStatus,delete c.dataset.adStatus)} 
function yU(a,b){const c=b.innerHeight>=b.innerWidth?1:2;if(a.g!=c){a.g=c;a=FH();for(const d of a)if(d.Ib.offsetWidth!=d.offsetWidth||d.D.google_full_width_responsive_allowed)d.offsetWidth=d.Ib.offsetWidth,vB(467,()=>{var e=d.Ib,f=d.D;const g=wU(e,f);f.google_full_width_responsive_allowed&&(e.style.marginLeft=f.gfwroml||"",e.style.marginRight=f.gfwromr||"",e.style.height=f.gfwroh?`${f.gfwroh}px`:"",e.style.width=f.gfwrow?`${f.gfwrow}px`:"",e.style.zIndex=f.gfwroz||"",delete f.google_full_width_responsive_allowed); 
delete f.google_ad_format;delete f.google_ad_width;delete f.google_ad_height;delete f.google_content_recommendation_ui_type;delete f.google_content_recommendation_rows_num;delete f.google_content_recommendation_columns_num;b.google_spfd(e,f,b);const h=wU(e,f);!h&&g&&e.childNodes.length==1?(xU(g,!1),f.google_reactive_ad_format=16,f.google_ad_section="responsive_resize",iU(e,f,b)):h&&g&&h!=g&&(xU(g,!1),xU(h,!0))})}} 
var zU=class extends N{constructor(){super(...arguments);this.g=null}M(a){const b=uH();if(!zH(b,27,!1)){AH(b,27,!0);this.g=a.innerHeight>=a.innerWidth?1:2;var c=()=>{yU(this,a)};qb(a,"resize",c);Mr(this,()=>{rb(a,"resize",c)})}}};var AU=class{constructor(a,b){this.L=a;this.Ib=b;this.g=null;this.j=0}run(){this.g=q.setInterval(xa(this.i,this),500);this.i()}i(){++this.j>=10&&q.clearInterval(this.g);var a=vx(this.L,this.Ib);wx(this.L,this.Ib,a);a=rx(this.Ib,this.L);a!=null&&a.x===0||q.clearInterval(this.g)}};var BU=class{constructor(a){this.g=0;this.j=this.O=null;this.K=0;this.i=[];this.Rc=this.F="";this.l=this.G=null;this.H=!1;this.L=a.L;this.pubWin=a.pubWin;this.D=a.D;this.sa=a.sa;this.Ka=a.Ka;this.jb=a.jb;this.ba=a.ba;this.pageState=a.pageState}};function CU(a,b,c=1E5){a-=b;return a>=c?"M":a>=0?a:"-M"};var kf={yo:0,uo:1,vo:9,ro:2,so:3,xo:5,wo:7,to:10};var DU=class extends G{},EU=Uj(DU),FU=[1,3];const GU=hd`https://securepubads.g.doubleclick.net/static/topics/topics_frame.html`; 
function HU(a){const b=a.google_tag_topics_state??(a.google_tag_topics_state={});return b.messageChannelSendRequestFn?Promise.resolve(b.messageChannelSendRequestFn):new Promise(c=>{function d(h){return g.g(h).then(({data:k})=>k)}const e=sd("IFRAME");e.style.display="none";e.name="goog_topics_frame";e.src=hc(GU).toString();const f=(new URL(GU.toString())).origin,g=nQ({destination:a,Ba:e,origin:f,cf:"goog:gRpYw:doubleclick"});g.g("goog:topics:frame:handshake:ack").then(({data:h})=>{h==="goog:topics:frame:handshake:ack"&& 
c(d)});b.messageChannelSendRequestFn=d;a.document.documentElement.appendChild(e)})} 
function IU(a,b,c){var d=sB,e=M(Ww);const {Hd:f,Gd:g}=JU(c);b=b.google_tag_topics_state??(b.google_tag_topics_state={});b.getTopicsPromise||(a=a({message:"goog:topics:frame:get:topics",skipTopicsObservation:e}).then(h=>{var k=g;if(h instanceof Uint8Array){if(!k){if(k=f instanceof Uint8Array)a:if(na(h)&&na(f)&&h.length==f.length){k=h.length;for(let n=0;n<k;n++)if(h[n]!==f[n]){k=!1;break a}k=!0}else k=!1;k=!k}}else if(jf()(h))k||(k=h!==f);else return d.ma(989,Error(JSON.stringify(h))),7;if(k&&c)try{var l= 
new DU;var m=ci(l,2,sm());h instanceof Uint8Array?qh(m,1,FU,Xe(h,!1)):qh(m,3,FU,h==null?h:Of(h));c.setItem("goog:cached:topics",ii(m))}catch{}return h}),b.getTopicsPromise=a);return f&&!g?Promise.resolve(f):b.getTopicsPromise} 
function JU(a){if(!a)return{Hd:null,Gd:!0};try{const l=a.getItem("goog:cached:topics");if(!l)return{Hd:null,Gd:!0};const m=EU(l);let n;const p=uh(m,FU);switch(p){case 0:n=null;break;case 1:a=m;var b=th(m,FU,1);const z=a.T;var c=Zg(z,z[u]|0,b,lh);var d=c==null?te():c;b=Uint8Array;we(re);var e=d.g;if(e==null||e!=null&&e instanceof Uint8Array)var f=e;else{if(typeof e==="string"){oe.test(e)&&(e=e.replace(oe,qe));let w;w=atob(e);const B=new Uint8Array(w.length);for(e=0;e<w.length;e++)B[e]=w.charCodeAt(e); 
var g=B}else g=null;f=g}var h=f;var k=h==null?h:d.g=h;n=new b(k||0);break;case 3:n=D(m,th(m,FU,3));break;default:wc(p,void 0)}const t=xu(Rh(m,2))+6048E5<sm();return{Hd:n,Gd:t}}catch{return{Hd:null,Gd:!0}}};function KU(a){return M(Pw)&&a?!!a.match(kx(Nw)):!1}function LU(a,b){if(!M(Uw)&&b.g()){b=KQ("shared-storage",a.document);const c=KQ("browsing-topics",a.document);if(b||c)try{return HU(a)}catch(d){zB(984,d)}}return null}async function MU(a,b,c,d,e,f){if(KQ("browsing-topics",b.document)&&e&&!M(Tw)&&!KU(f?.label))if(NU(c,d)){a.l=1;const g=mN(c,b);c=e.then(async h=>{await IU(h,b,g).then(k=>{a.l=k})});M(Vw)&&(d=V(Xw),d>0?await Promise.race([c,ae(d)]):await c)}else a.l=5} 
function NU(a,b){return!b.google_restrict_data_processing&&b.google_tag_for_under_age_of_consent!==1&&b.google_tag_for_child_directed_treatment!==1&&!!a.g()&&!EH()&&!A(a,9)&&!A(a,13)&&!A(a,12)&&(typeof b.google_privacy_treatments!=="string"||!b.google_privacy_treatments.split(",").includes("disablePersonalization"))&&!A(a,14)&&!A(a,16)};function OU(a,b,c){const d=b.parentElement?.classList.contains("adsbygoogle")?b.parentElement:b;c.addEventListener("load",()=>{PU(d)});return VP(a,"adpnt",(e,f)=>{if(qr(f,c.contentWindow)){e=tr(e).qid;try{c.setAttribute("data-google-query-id",e),a.googletag??(a.googletag={cmd:[]}),a.googletag.queryIds=a.googletag.queryIds??[],a.googletag.queryIds.push(e),a.googletag.queryIds.length>500&&a.googletag.queryIds.shift()}catch{}d.dataset.adStatus="filled";e=!0}else e=!1;return e})} 
function PU(a){setTimeout(()=>{a.dataset.adStatus!=="filled"&&(a.dataset.adStatus="unfilled")},1E3)};function QU(a,b,{fg:c,gg:d}){return A(b,8)||(c||!b.g())&&d||!oN(a.g)?!1:!0}function RU(a,b,{fg:c,gg:d}){if(QU(a,b,{fg:c,gg:d}))return qN("__eoi",a.g)??void 0}var SU=class{constructor(a){this.g=a}};function TU(a,b,c){try{if(!Vd(c.origin)||!qr(c,a.g.contentWindow))return}catch(f){return}const d=b.msg_type;let e=null;typeof d==="string"&&(e=a.messageHandlers[d])&&a.za.mb(168,()=>{e.call(a,b,c)})} 
var UU=class extends N{constructor(a,b){var c=sB,d=qB;super();this.j=a;this.g=b;this.za=c;this.I=d;this.messageHandlers={};this.X=[];this.Ha=this.za.nb(168,(e,f)=>void TU(this,e,f));this.Ag=this.za.nb(169,(e,f)=>ur(this.j,"ras::xsf",this.I,f));this.M({})}M(){this.aa(this.messageHandlers);this.X.push(UP(this.j,"sth",this.Ha,this.Ag))}i(){for(const a of this.X)a();this.X.length=0;super.i()}};var VU=class extends UU{};function WU(a,b,c=null){return new XU(a,b,c)} 
var XU=class extends VU{constructor(a,b,c){super(a,b);this.B=c;this.F=L(XH);bf(FQ,ef);this.Fa=FQ;this.l=()=>{};qb(this.g,"load",this.l)}i(){rb(this.g,"load",this.l);super.i()}aa(a){a["adsense-labs"]=b=>{if(b=tr(b).settings)if(b=ji(Wk,JSON.parse(b)),y(b,1)!=null){if(yh(b,b.T[u]|0,Vk,4,3,!0).length>0){var c=zh(b,Vk,4,eh($e)),d=c,e=this.F;const h=new wo;for(var f of d)switch(f.getVersion()){case 1:$h(h,1,!0);break;case 2:$h(h,2,!0)}f=new xo;f=Ah(f,1,yo,h);eI(e,f);e=this.j;f=this.B;if(!zH(uH(),37,!1)){e= 
new XQ(e);for(var g of c)switch(g.getVersion()){case 1:UQ(e,"__gads",g,f);break;case 2:UQ(e,"__gpi",g,f)}AH(uH(),37,!0)}$g(b,4)}if(g=v(b,Vk,5))c=this.j,zH(uH(),40,!1)||(c=new SU(c),e=xu(Rh(g,2))-Date.now()/1E3,e={ce:Math.max(e,0),path:C(g,3),domain:C(g,4),ze:!1},rN("__eoi",g.getValue(),e,c.g),AH(uH(),40,!0));$g(b,5);g=this.j;c=C(b,1)||"";e=this.Fa;if(Ys(zN({A:g,Fa:e}))){e=zN({A:g,Fa:e});e=Ys(e)?HQ(e.getValue()):{};b!==null&&(e[c]=Lg(b));try{g.localStorage.setItem("google_adsense_settings",JSON.stringify(e))}catch(h){}}}}}};function YU(a){a.l=a.B;a.F.style.transition="height 500ms";a.ta.style.transition="height 500ms";a.g.style.transition="height 500ms";ZU(a)}function $U(a,b){WP(a.g.contentWindow,"sth",{msg_type:"expand-on-scroll-result",eos_success:!0,eos_amount:b},"*")} 
function ZU(a){const b=`rect(0px, ${a.g.width}px, ${a.l}px, 0px)`;a.g.style.clip=b;a.ta.style.clip=b;a.g.setAttribute("height",a.l.toString());a.g.style.height=`${a.l}px`;a.ta.setAttribute("height",a.l.toString());a.ta.style.height=`${a.l}px`;a.F.style.height=`${a.l}px`} 
function aV(a,b){b=Cd(b.r_nh);a.B=b==null?0:b;if(a.B<=0)return"1";a.K=Yl(a.F).y;a.G=rr(a.j);if(a.K+a.l<a.G)return"2";if(a.K>lr(a.j)-a.j.innerHeight)return"3";b=a.G;a.g.setAttribute("height",a.B.toString());a.g.style.height=`${a.B}px`;a.ta.style.overflow="hidden";a.F.style.position="relative";a.F.style.transition="height 100ms";a.ta.style.transition="height 100ms";a.g.style.transition="height 100ms";b=Math.min(b+a.j.innerHeight-a.K,a.l);Rl(a.ta,{position:"relative",top:"auto",bottom:"auto"});b=`rect(0px, ${a.g.width}px, ${b}px, 0px)`; 
Rl(a.g,{clip:b});Rl(a.ta,{clip:b});return"0"} 
var bV=class extends VU{constructor(a,b){super(a.L,b);this.bb=this.yd=!1;this.qa=this.G=this.B=0;this.ta=a.ba;this.F=this.ta.parentElement&&this.ta.parentElement.classList.contains("adsbygoogle")?this.ta.parentElement:this.ta;this.l=parseInt(this.ta.style.height,10);this.ki=this.l/5;this.K=Yl(this.F).y;this.ji=mb(wB(651,()=>{this.K=Yl(this.F).y;var c=this.G;this.G=rr(this.j);this.l<this.B?(c=this.G-c,c>0&&(this.qa+=c,this.qa>=this.ki?(YU(this),$U(this,this.B)):(this.l=Math.min(this.B,this.l+c),$U(this, 
c),ZU(this)))):rb(this.j,"scroll",this.O)}),this);this.O=()=>{var c=this.ji;cl.requestAnimationFrame?cl.requestAnimationFrame(c):c()}}aa(a){a["expand-on-scroll"]=(b,c)=>{b=tr(b);this.yd||(this.yd=!0,b=aV(this,b),b==="0"&&qb(this.j,"scroll",this.O,nb),WP(c.target,"sth",{msg_type:"expand-on-scroll-result",eos_success:b==="0"},"*"))};a["expand-on-scroll-force-expand"]=()=>{this.bb||(this.bb=!0,YU(this),rb(this.j,"scroll",this.O))}}i(){this.O&&rb(this.j,"scroll",this.O,nb);super.i()}};function cV(a){const b=a.K.getBoundingClientRect(),c=b.top+b.height<0;return!(b.top>a.g.innerHeight)&&!c} 
var dV=class extends N{constructor(a,b,c){super();this.g=a;this.B=b;this.K=c;this.F=0;this.l=cV(this);const d=lb(this.G,this);this.j=wB(433,()=>{cl.requestAnimationFrame?cl.requestAnimationFrame(d):d()});qb(this.g,"scroll",this.j,nb)}G(){const a=cV(this);if(a&&!this.l){var b={rr:"vis-bcr"};const c=this.B.contentWindow;c&&(XP(c,"ig",b,"*",2),++this.F>=10&&this.dispose())}this.l=a}dispose(){this.j&&rb(this.g,"scroll",this.j,nb)}};function eV(a,b){Array.isArray(b)||(b=[b]);b=b.map(function(c){return typeof c==="string"?c:c.property+" "+c.duration+"s "+c.timing+" "+c.delay+"s"});Rl(a,"transition",b.join(","))}var fV=jb(function(){const a=Hl(document,"DIV"),b=Wc?"-webkit":Vc?"-moz":null;let c="transition:opacity 1s linear;";b&&(c+=b+"-transition:opacity 1s linear;");Dc(a,fd("div",{style:c}));return Ul(a.firstChild,"transition")!=""});function gV(a,b,c){a.i[b].indexOf(c)<0&&(a.i[b]+=c)}function hV(a,b){a.g.indexOf(b)>=0||(a.g=b+a.g)}function iV(a,b){a.errors.indexOf(b)<0&&(a.errors=b+a.errors)}function jV(a,b,c,d){return a.errors!=""||b?null:a.g.replace(kV,"")==""?c!=null&&a.i[0]||d!=null&&a.i[1]?!1:!0:!1}function lV(a){var b=jV(a,"",null,0);if(b===null)return"XS";b=b?"C":"N";a=a.g;return a.indexOf("a")>=0?b+"A":a.indexOf("f")>=0?b+"F":b+"S"} 
var mV=class{constructor(a,b){this.i=["",""];this.g=a||"";this.errors=b||""}toString(){return[this.i[0],this.i[1],this.g,this.errors].join("|")}}; 
function nV(a){let b=a.aa;a.F=()=>{};oV(a,a.B,b);let c=a.B.parentElement;if(!c)return a.g;let d=!0,e=null;for(;c;){try{e=/^head|html$/i.test(c.nodeName)?null:td(c,b)}catch(g){iV(a.g,"c")}const f=pV(a,b,c,e);c.classList.contains("adsbygoogle")&&e&&(/^\-.*/.test(e["margin-left"])||/^\-.*/.test(e["margin-right"]))&&(a.X=!0);if(d&&!f&&qV(e)){hV(a.g,"l");a.G=c;break}d=d&&f;if(e&&rV(a,e))break;c=c.parentElement;if(!c){if(b===a.pubWin)break;try{if(c=b.frameElement,b=b.parent,!kd(b)){hV(a.g,"c");break}}catch(g){hV(a.g, 
"c");break}}}a.H&&a.C&&sV(a);return a.g} 
function tV(a){function b(m){for(let n=0;n<m.length;n++)Rl(k,m[n],"0px")}function c(){uV(d,g,h);!k||l||h||(b(vV),b(wV))}const d=a.B;d.style.overflow=a.Cd?"visible":"hidden";a.H&&(a.G?(eV(d,xV()),eV(a.G,xV())):eV(d,"opacity 1s cubic-bezier(.4, 0, 1, 1), width .2s cubic-bezier(.4, 0, 1, 1) .3s, height .5s cubic-bezier(.4, 0, 1, 1)"));a.O!==null&&(d.style.opacity=String(a.O));const e=a.width!=null&&a.j!=null&&(a.xe||a.j>a.width)?a.j:null,f=a.height!=null&&a.i!=null&&(a.xe||a.i>a.height)?a.i:null;if(a.K){const m= 
a.K.length;for(let n=0;n<m;n++)uV(a.K[n],e,f)}const g=a.j,h=a.i,k=a.G,l=a.X;a.H?q.setTimeout(c,1E3):c()} 
function yV(a){if(a.C&&!a.qa||a.j==null&&a.i==null&&a.O==null&&a.C)return a.g;var b=a.C;a.C=!1;nV(a);a.C=b;if(!b||a.check!=null&&!jV(a.g,a.check,a.j,a.i))return a.g;a.g.g.indexOf("n")>=0&&(a.width=null,a.height=null);if(a.width==null&&a.j!==null||a.height==null&&a.i!==null)a.H=!1;(a.j==0||a.i==0)&&a.g.g.indexOf("l")>=0&&(a.j=0,a.i=0);b=a.g;b.i[0]="";b.i[1]="";b.g="";b.errors="";tV(a);return nV(a)} 
function rV(a,b){let c=!1;b.display=="none"&&(hV(a.g,"n"),a.C&&(c=!0));b.visibility!="hidden"&&b.visibility!="collapse"||hV(a.g,"v");b.overflow=="hidden"&&hV(a.g,"o");b.position=="absolute"?(hV(a.g,"a"),c=!0):b.position=="fixed"&&(hV(a.g,"f"),c=!0);return c} 
function oV(a,b,c){let d=0;if(!b||!b.parentElement)return!0;let e=!1,f=0;const g=b.parentElement.childNodes;for(let k=0;k<g.length;k++){var h=g[k];h==b?e=!0:(h=zV(a,h,c),d|=h,e&&(f|=h))}f&1&&(d&2&&gV(a.g,0,"o"),d&4&&gV(a.g,1,"o"));return!(d&1)} 
function pV(a,b,c,d){let e=null;try{e=c.style}catch(w){iV(a.g,"s")}var f=c.getAttribute("width"),g=Cd(f),h=c.getAttribute("height"),k=Cd(h),l=d&&/^block$/.test(d.display)||e&&/^block$/.test(e.display);b=oV(a,c,b);var m=d&&d.width;const n=d&&d.height;var p=e&&e.width,t=e&&e.height,z=Dd(m)==a.width&&Dd(n)==a.height;m=z?m:p;t=z?n:t;p=Dd(m);z=Dd(t);g=a.width!==null&&(p!==null&&a.width>=p||g!==null&&a.width>=g);z=a.height!==null&&(z!==null&&a.height>=z||k!==null&&a.height>=k);k=!b&&qV(d);z=b||z||k||!(f|| 
m||d&&(!AV(String(d.minWidth))||!BV(String(d.maxWidth))));l=b||g||k||l||!(h||t||d&&(!AV(String(d.minHeight))||!BV(String(d.maxHeight))));CV(a,0,z,c,"width",f,a.width,a.j);DV(a,0,"d",z,e,d,"width",m,a.width,a.j);DV(a,0,"m",z,e,d,"minWidth",e&&e.minWidth,a.width,a.j);DV(a,0,"M",z,e,d,"maxWidth",e&&e.maxWidth,a.width,a.j);a.kg?(c=/^html|body$/i.test(c.nodeName),f=Dd(n),h=d?d.overflowY==="auto"||d.overflowY==="scroll":!1,h=a.i!=null&&d&&f&&Math.round(f)!==a.i&&!h&&d.minHeight!=="100%",a.C&&!c&&h&&(e.setProperty("height", 
"auto","important"),d&&!AV(String(d.minHeight))&&e.setProperty("min-height","0px","important"),d&&!BV(String(d.maxHeight))&&a.i&&Math.round(f)<a.i&&e.setProperty("max-height","none","important"))):(CV(a,1,l,c,"height",h,a.height,a.i),DV(a,1,"d",l,e,d,"height",t,a.height,a.i),DV(a,1,"m",l,e,d,"minHeight",e&&e.minHeight,a.height,a.i),DV(a,1,"M",l,e,d,"maxHeight",e&&e.maxHeight,a.height,a.i));return b} 
function sV(a){function b(){if(c>0){var l=td(e,d)||{width:0,height:0};const m=Dd(l.width);l=Dd(l.height);m!==null&&f!==null&&h&&h(0,f-m);l!==null&&g!==null&&h&&h(1,g-l);--c}else q.clearInterval(k),h&&(h(0,0),h(1,0))}let c=31.25;const d=a.aa,e=a.B,f=a.j,g=a.i,h=a.F;let k;q.setTimeout(()=>{k=q.setInterval(b,16)},990)} 
function zV(a,b,c){if(b.nodeType==3)return/\S/.test(b.data)?1:0;if(b.nodeType==1){if(/^(head|script|style)$/i.test(b.nodeName))return 0;let d=null;try{d=td(b,c)}catch(e){}if(d){if(d.display=="none"||d.position=="fixed")return 0;if(d.position=="absolute"){if(!a.l.boundingClientRect||d.visibility=="hidden"||d.visibility=="collapse")return 0;c=null;try{c=b.getBoundingClientRect()}catch(e){return 0}return(c.right>a.l.boundingClientRect.left?2:0)|(c.bottom>a.l.boundingClientRect.top?4:0)}}return 1}return 0} 
function CV(a,b,c,d,e,f,g,h){if(h!=null){if(typeof f=="string"){if(f=="100%"||!f)return;f=Cd(f);f==null&&(iV(a.g,"n"),gV(a.g,b,"d"))}if(f!=null)if(c){if(a.C)if(a.H){const k=Math.max(f+h-(g||0),0),l=a.F;a.F=(m,n)=>{m==b&&Ec(d,e,String(k-n));l&&l(m,n)}}else Ec(d,e,String(h))}else gV(a.g,b,"d")}} 
function DV(a,b,c,d,e,f,g,h,k,l){if(l!=null){f=f&&f[g];typeof f!="string"||(c=="m"?AV(f):BV(f))||(f=Dd(f),f==null?hV(a.g,"p"):k!=null&&hV(a.g,f==k?"E":"e"));if(typeof h=="string"){if(c=="m"?AV(h):BV(h))return;h=Dd(h);h==null&&(iV(a.g,"p"),gV(a.g,b,c))}if(h!=null)if(d&&e){if(a.C)if(a.H){const m=Math.max(h+l-(k||0),0),n=a.F;a.F=(p,t)=>{p==b&&(e[g]=`${m-t}px`);n&&n(p,t)}}else e[g]=`${l}px`}else gV(a.g,b,c)}} 
var IV=class{constructor(a,b,c,d,e,f,g){this.pubWin=a;this.B=b;this.K=c;this.G=this.F=null;this.X=!1;this.l=new EV(this.B);this.aa=(a=this.B.ownerDocument)&&(a.defaultView||a.parentWindow);this.l=new EV(this.B);this.C=g;this.qa=FV(this.l,d.wg,d.height,d.rd);this.width=this.C?this.l.boundingClientRect?this.l.boundingClientRect.right-this.l.boundingClientRect.left:null:e;this.height=this.C?this.l.boundingClientRect?this.l.boundingClientRect.bottom-this.l.boundingClientRect.top:null:f;this.j=GV(d.width); 
this.i=GV(d.height);this.O=this.C?GV(d.opacity):null;this.check=d.check;this.rd=!!d.rd;this.H=d.wg=="animate"&&!HV(this.l,this.i,this.rd)&&fV();this.Cd=!!d.Cd;this.g=new mV;HV(this.l,this.i,this.rd)&&hV(this.g,"r");e=this.l;e.g&&e.i>=e.Z&&hV(this.g,"b");this.xe=!!d.xe;this.kg=!!d.kg}};function HV(a,b,c){var d;(d=a.g)&&!(d=!a.visible)&&(c?(b=a.i+Math.min(b,GV(a.getHeight())),a=a.g&&b>=a.Z):a=a.g&&a.i>=a.Z,d=a);return d} 
var EV=class{constructor(a){this.boundingClientRect=null;var b=a&&a.ownerDocument,c=b&&(b.defaultView||b.parentWindow);c=c&&qd(c);this.g=!!c;if(a)try{this.boundingClientRect=a.getBoundingClientRect()}catch(g){}var d=a;let e=0,f=this.boundingClientRect;for(;d;)try{f&&(e+=f.top);const g=d.ownerDocument,h=g&&(g.defaultView||g.parentWindow);(d=h&&h.frameElement)&&(f=d.getBoundingClientRect())}catch(g){break}this.i=e;c=c||q;this.Z=(c.document.compatMode=="CSS1Compat"?c.document.documentElement:c.document.body).clientHeight; 
b=b&&wT(b);this.visible=!!a&&!(b==2||b==3)&&!(this.boundingClientRect&&this.boundingClientRect.top>=this.boundingClientRect.bottom&&this.boundingClientRect.left>=this.boundingClientRect.right)}isVisible(){return this.visible}getWidth(){return this.boundingClientRect?this.boundingClientRect.right-this.boundingClientRect.left:null}getHeight(){return this.boundingClientRect?this.boundingClientRect.bottom-this.boundingClientRect.top:null}}; 
function FV(a,b,c,d){switch(b){case "no_rsz":return!1;case "force":case "animate":return!0;default:return HV(a,c,d)}}function qV(a){return!!a&&/^left|right$/.test(a.cssFloat||a.styleFloat)}function JV(a,b,c,d){return yV(new IV(a,b,d,c,null,null,!0))}var KV=new mV("s",""),kV=RegExp("[lonvafrbpEe]","g");function BV(a){return!a||/^(auto|none|100%)$/.test(a)}function AV(a){return!a||/^(0px|auto|none|0%)$/.test(a)} 
function uV(a,b,c){b!==null&&Cd(a.getAttribute("width"))!==null&&a.setAttribute("width",String(b));c!==null&&Cd(a.getAttribute("height"))!==null&&a.setAttribute("height",String(c));b!==null&&(a.style.width=`${b}px`);c!==null&&(a.style.height=`${c}px`)}var vV="margin-left margin-right padding-left padding-right border-left-width border-right-width".split(" "),wV="margin-top margin-bottom padding-top padding-bottom border-top-width border-bottom-width".split(" "); 
function xV(){let a="opacity 1s cubic-bezier(.4, 0, 1, 1), width .2s cubic-bezier(.4, 0, 1, 1), height .3s cubic-bezier(.4, 0, 1, 1) .2s",b=vV;for(var c=0;c<b.length;c++)a+=", "+b[c]+" .2s cubic-bezier(.4, 0, 1, 1)";b=wV;for(c=0;c<b.length;c++)a+=", "+b[c]+" .3s cubic-bezier(.4, 0, 1, 1) .2s";return a}function GV(a){return typeof a==="string"?Cd(a):typeof a==="number"&&isFinite(a)?a:null};var LV=class extends VU{constructor(a,b,c){super(a,b);this.ba=c}aa(a){a["resize-me"]=(b,c)=>{b=tr(b);var d=b.r_chk;if(d==null||d===""){var e=Cd(b.r_nw),f=Cd(b.r_nh),g=Cd(b.r_no);g!=null||e!==0&&f!==0||(g=0);var h=b.r_str;h=h?h:null;{var k=/^true$/.test(b.r_ao),l=/^true$/.test(b.r_ifr),m=/^true$/.test(b.r_cab);const t=window;if(t)if(h==="no_rsz")b.err="7",e=!0;else{var n=new EV(this.g);if(n.g){var p=n.getWidth();p!=null&&(b.w=p);p=n.getHeight();p!=null&&(b.h=p);FV(n,h,f,m)?(n=this.ba,d=JV(t,n,{width:e, 
height:f,opacity:g,check:d,wg:h,Cd:k,xe:l,rd:m},[this.g]),b.r_cui&&/^true$/.test(b.r_cui.toString())&&r(n,{height:`${f===null?0:f-48}px`,top:"24px"}),e!=null&&(b.nw=e),f!=null&&(b.nh=f),b.rsz=d.toString(),b.abl=lV(d),b.frsz=(h==="force").toString(),b.err="0",e=!0):(b.err="1",e=!1)}else b.err="3",e=!1}else b.err="2",e=!1}WP(c.source,"sth",{msg_type:"resize-result",r_str:h,r_status:e},"*");this.g.dataset.googleQueryId||this.g.setAttribute("data-google-query-id",b.qid)}}}};function MV(a,b){return new IntersectionObserver(b,a)}function NV(a,b,c){qb(a,b,c);return()=>rb(a,b,c)}let OV=null;function PV(){OV=sm()}function QV(a,b){return b?OV===null?(qb(a,"mousemove",PV,{passive:!0}),qb(a,"scroll",PV,{passive:!0}),PV(),!1):sm()-OV>=b*1E3:!1} 
function RV({A:a,element:b,Rk:c,Lk:d,Kk:e=0,hb:f,kj:g,options:h={},Wj:k=!0,Oo:l=MV}){let m,n=!1,p=!1;const t=[],z=l(h,(w,B)=>{try{const I=()=>{t.length||(d&&(t.push(NV(b,"mouseenter",()=>{n=!0;I()})),t.push(NV(b,"mouseleave",()=>{n=!1;I()}))),t.push(NV(a.document,"visibilitychange",()=>I())));const T=QV(a,e),R=yT(a.document);if(p&&!n&&!T&&!R)m=m||a.setTimeout(()=>{QV(a,e)?I():(f(),B.disconnect())},c*1E3);else if(k||n||T||R)a.clearTimeout(m),m=void 0};({isIntersecting:p}=w[w.length-1]);I()}catch(I){g&& 
g(I)}});z.observe(b);return()=>{z.disconnect();for(const w of t)w();m!=null&&a.clearTimeout(m)}};function SV(a,b,c,d,e){return new TV(a,b,c,d,e)}function UV(a,b,c){const d=a.g,e=a.F;if(e!=null&&d!=null&&qr(c,d.contentWindow)&&(b=b.config,typeof b==="string")){try{var f=JSON.parse(b);if(!Array.isArray(f))return;a.l=ji(Xk,f)}catch(g){return}a.dispose();f=Qh(a.l,1);f<=0||(a.B=RV({A:a.j,element:e,Rk:f-.2,Lk:!Lb(),Kk:Qh(a.l,3),hb:()=>void VY(a,e),kj:g=>Wq.ma(1223,g,void 0,void 0),options:{threshold:Sh(a.l,2,1)},Wj:!0}))}} 
function VY(a,b){a.G();setTimeout(Wq.nb(1224,()=>{var c=Number(a.D.rc);a.D.rc=c?c+1:1;c=b.parentElement||null;c&&xx.test(c.className)||(c=Hl(document,"INS"),c.className="adsbygoogle",b.parentNode&&b.parentNode.insertBefore(c,b.nextSibling));M(yv)?(WY(a,c,b),a.D.no_resize=!0,Xr(nJ(c),"filled",()=>{Il(b)})):Il(b);iU(c,a.D,a.j)}),200)} 
function WY(a,b,c){a.j.getComputedStyle(b).position==="static"&&(b.style.position="relative");c.style.position="absolute";c.style.top="0";c.style.left="0";delete b.dataset.adsbygoogleStatus;delete b.dataset.adStatus;b.classList.remove("adsbygoogle-noablate")} 
var TV=class extends VU{constructor(a,b,c,d,e){super(a,b);this.D=c;this.F=d;this.G=e;this.l=this.B=null;(b=(b=b.contentWindow)&&b.parent)&&a!==b&&this.X.push(UP(b,"sth",this.Ha,this.Ag))}aa(a){a.av_ref=(b,c)=>{UV(this,b,c)}}i(){super.i();this.F=null;this.B&&this.B()}};const XY=/^blogger$/,YY=/^wordpress(.|\s|$)/i,ZY=/^joomla!/i,$Y=/^drupal/i,aZ=/\/wp-content\//,bZ=/\/wp-content\/plugins\/advanced-ads/,cZ=/\/wp-content\/themes\/genesis/,dZ=/\/wp-content\/plugins\/genesis/; 
function eZ(a){var b=a.getElementsByTagName("script"),c=b.length;for(var d=0;d<c;++d){var e=b[d];if(e.hasAttribute("src")){e=e.getAttribute("src")||"";if(bZ.test(e))return 5;if(dZ.test(e))return 6}}b=a.getElementsByTagName("link");c=b.length;for(d=0;d<c;++d)if(e=b[d],e.hasAttribute("href")&&(e=e.getAttribute("href")||"",cZ.test(e)||dZ.test(e)))return 6;a=a.getElementsByTagName("meta");d=a.length;for(e=0;e<d;++e){var f=a[e];if(f.getAttribute("name")=="generator"&&f.hasAttribute("content")){f=f.getAttribute("content")|| 
"";if(XY.test(f))return 1;if(YY.test(f))return 2;if(ZY.test(f))return 3;if($Y.test(f))return 4}}for(a=0;a<c;++a)if(d=b[a],d.getAttribute("rel")=="stylesheet"&&d.hasAttribute("href")&&(d=d.getAttribute("href")||"",aZ.test(d)))return 2;return 0};var fZ={google_ad_block:"ad_block",google_ad_client:"client",google_ad_intent_query:"ait_q",google_ad_output:"output",google_ad_callback:"callback",google_ad_height:"h",google_ad_resize:"twa",google_ad_slot:"slotname",google_ad_unit_key:"adk",google_ad_dom_fingerprint:"adf",google_ad_intent_qetid:"aiqeid",google_ad_intents_format:"ait_f",google_placement_id:"pi",google_daaos_ts:"daaos",google_erank:"epr",google_ad_width:"w",abgtt:"abgtt",google_captcha_token:"captok",google_content_recommendation_columns_num:"cr_col", 
google_content_recommendation_rows_num:"cr_row",google_ctr_threshold:"ctr_t",google_cust_criteria:"cust_params",gfwrnwer:"fwrn",gfwrnher:"fwrnh",google_image_size:"image_size",google_last_modified_time:"lmt",google_loeid:"loeid",google_max_num_ads:"num_ads",google_max_radlink_len:"max_radlink_len",google_mtl:"mtl",google_native_settings_key:"nsk",google_enable_content_recommendations:"ecr",google_num_radlinks:"num_radlinks",google_num_radlinks_per_unit:"num_radlinks_per_unit",google_pucrd:"pucrd", 
google_reactive_plaf:"plaf",google_reactive_plat:"plat",google_reactive_fba:"fba",google_reactive_sra_channels:"plach",google_responsive_auto_format:"rafmt",armr:"armr",google_plas:"plas",google_rl_dest_url:"rl_dest_url",google_rl_filtering:"rl_filtering",google_rl_mode:"rl_mode",google_rt:"rt",google_video_play_muted:"vpmute",google_source_type:"src_type",google_restrict_data_processing:"rdp",google_tag_for_child_directed_treatment:"tfcd",google_tag_for_under_age_of_consent:"tfua",google_tag_origin:"to", 
google_ad_semantic_area:"sem",google_tfs:"tfs",google_package:"pwprc",google_tag_partner:"tp",fra:"fpla",google_ml_rank:"mlr",google_apsail:"psa",google_ad_channel:"channel",google_ad_type:"ad_type",google_ad_format:"format",google_color_bg:"color_bg",google_color_border:"color_border",google_color_link:"color_link",google_color_text:"color_text",google_color_url:"color_url",google_page_url:"url",google_ad_section:"region",google_cpm:"cpm",google_encoding:"oe",google_safe:"adsafe",google_font_face:"f", 
google_font_size:"fs",google_hints:"hints",google_ad_host:"host",google_ad_host_channel:"h_ch",google_ad_host_tier_id:"ht_id",google_kw_type:"kw_type",google_kw:"kw",google_contents:"contents",google_targeting:"targeting",google_adtest:"adtest",google_alternate_color:"alt_color",google_alternate_ad_url:"alternate_ad_url",google_cust_age:"cust_age",google_cust_gender:"cust_gender",google_cust_l:"cust_l",google_cust_lh:"cust_lh",google_language:"hl",google_city:"gcs",google_country:"gl",google_region:"gr", 
google_content_recommendation_ad_positions:"ad_pos",google_content_recommendation_ui_type:"crui",google_content_recommendation_use_square_imgs:"cr_sq_img",sso:"sso",google_color_line:"color_line",google_disable_video_autoplay:"disable_video_autoplay",google_full_width_responsive_allowed:"fwr",google_full_width_responsive:"fwrattr",efwr:"efwr",google_pgb_reactive:"pra",rc:"rc",google_resizing_allowed:"rs",google_resizing_height:"rh",google_resizing_width:"rw",rpe:"rpe",google_responsive_formats:"resp_fmts", 
google_safe_for_responsive_override:"sfro",google_video_doc_id:"video_doc_id",google_video_product_type:"video_product_type",google_webgl_support:"wgl",aihb:"aihb",aiof:"aiof",asro:"asro",ailel:"ailel",aiael:"aiael",aicel:"aicel",aifxl:"aifxl",aiixl:"aiixl",vmsli:"itsi",dap:"dap",aiapm:"aiapm",aiapmi:"aiapmi",aiact:"aiact",aicct:"aicct",ailct:"ailct"};function gZ(a){a.g===-1&&(a.g=a.data.reduce((b,c,d)=>b+(c?2**d:0),0));return a.g}var hZ=class{constructor(){this.data=[];this.g=-1}set(a,b=!0){0<=a&&a<52&&Number.isInteger(a)&&this.data[a]!==b&&(this.data[a]=b,this.g=-1)}get(a){return!!this.data[a]}};function iZ(){const a=new hZ;"SVGElement"in q&&"createElementNS"in q.document&&a.set(0);const b=Hd();b["allow-top-navigation-by-user-activation"]&&a.set(1);b["allow-popups-to-escape-sandbox"]&&a.set(2);q.crypto&&q.crypto.subtle&&a.set(3);"TextDecoder"in q&&"TextEncoder"in q&&a.set(4);return gZ(a)};var jZ=Tj(CN);function kZ(a=document){const b=[],c=[];for(const f of Array.from(a.querySelectorAll("meta[name=generator][content]"))){if(!f)continue;var d=f.getAttribute("content")??"";const [,g,h]=/^([^0-9]+)(?:\s([0-9]+(?:\.[0-9]+){0,2})[.0-9]*)?[^0-9]*$/.exec(d)??[],k=g,l=h;a=new BN;l&&ei(a,3,l.substring(0,20));let m,n;if(k){for(const [p,t]of(new Map([[1,"WordPress"],[2,"Drupal"],[3,"MediaWiki"],[4,"Blogger"],[5,"SEOmatic"],[7,"Flutter"],[8,"Joomla! - Open Source Content Management"]])).entries()){var e=p;if(t=== 
k.trim()){m=e;break}}for(const [p,t]of(new Map([[1,"All in One SEO (AIOSEO)"],[2,"All in One SEO Pro (AIOSEO)"],[3,"AMP for WP"],[4,"Site Kit by Google"],[5,"Elementor"],[6,"Powered by WPBakery Page Builder - drag and drop page builder for WordPress."]])).entries())if(e=p,t===k.trim()){n=e;break}}n?(d=gi(a,1,1),gi(d,2,n)):m?gi(a,1,m):(e=gi(a,1,0),$g(e,3),c.push({content:d,name:k,version:l}));b.push(a)}return{labels:b,Vo:c}};const lZ=new Map([["navigate",1],["reload",2],["back_forward",3],["prerender",4]]),mZ=new Map([[0,1],[1,2],[2,3]]);function nZ(a){try{const b=a.performance?.getEntriesByType("navigation")?.[0];if(b?.type)return lZ.get(b.type)??null}catch{}return mZ.get(a.performance?.navigation?.type)??null};var oZ=class extends G{};var pZ=class extends G{};function qZ(a,b){if(Kb()){var c=a.document.documentElement.lang;rZ(a)?sZ(b,Zd(a),!0,"",c):(new MutationObserver((d,e)=>{rZ(a)&&(sZ(b,Zd(a),!1,c,a.document.documentElement.lang),e.disconnect())})).observe(a.document.documentElement,{attributeFilter:["class"]})}}function rZ(a){a=a.document?.documentElement?.classList;return!(!a?.contains("translated-rtl")&&!a?.contains("translated-ltr"))}function sZ(a,b,c,d,e){$k({ptt:`${a}`,pvsid:`${b}`,ibatl:String(c),pl:d,nl:e},"translate-event")};function tZ(a){if(a=a.navigator?.userActivation){var b=0;a?.hasBeenActive&&(b|=1);a?.isActive&&(b|=2);return b}};const uZ=/[+, ]/; 
function vZ(a,b){const c=a.D;var d=a.pubWin,e={},f=d.document,g=be(d);a:{var h=c.google_ad_width||d.google_ad_width;var k=c.google_ad_height||d.google_ad_height;if(d&&d.top===d)var l=!1;else{l=d.document;var m=l.documentElement;if(h&&k){let p=1,t=1;d.innerHeight?(p=d.innerWidth,t=d.innerHeight):m&&m.clientHeight?(p=m.clientWidth,t=m.clientHeight):l.body&&(p=l.body.clientWidth,t=l.body.clientHeight);if(t>2*k||p>2*h){l=!1;break a}}l=!0}}m=rH(g).Lf;h=d.top==d?0:kd(d.top)?1:2;k=4;l||h!==1?l||h!==2?l&& 
h===1?k=7:l&&h===2&&(k=8):k=6:k=5;m&&(k|=16);m=String(k);h=tH(d);k=!!c.google_page_url;e.google_iframing=m;h!==0&&(e.google_iframing_environment=h);if(!k&&f.domain==="ad.yieldmanager.com"){for(m=f.URL.substring(f.URL.lastIndexOf("http"));m.indexOf("%")>-1;)try{m=decodeURIComponent(m)}catch(p){break}c.google_page_url=m;k=!!m}k?(e.google_page_url=c.google_page_url,e.google_page_location=(l?f.referrer:f.URL)||"EMPTY"):(l&&kd(d.top)&&f.referrer&&d.top.document.referrer===f.referrer?e.google_page_url= 
d.top.document.URL:e.google_page_url=l?f.referrer:f.URL,e.google_page_location=null);if(f.URL===e.google_page_url)try{var n=Math.round(Date.parse(f.lastModified)/1E3)||null}catch{n=null}else n=null;e.google_last_modified_time=n;d=g===g.top?g.document.referrer:(d=vl())&&d.referrer||"";e.google_referrer_url=d;sH(e,c);b.g()?(e=c.google_page_location||c.google_page_url,"EMPTY"===e&&(e=c.google_page_url),e?(e=e.toString(),e.indexOf("http://")==0?e=e.substring(7,e.length):e.indexOf("https://")==0&&(e=e.substring(8, 
e.length)),d=e.indexOf("/"),d===-1&&(d=e.length),e=e.substring(0,d).split("."),d=!1,e.length>=3&&(d=e[e.length-3]in rS),e.length>=2&&(d=d||e[e.length-2]in rS),e=d):e=!1,e=e?"pagead2.googlesyndication.com":"googleads.g.doubleclick.net"):e="pagead2.googlesyndication.com";b=wZ(a,b);d=a.D;f=d.google_ad_channel;g="/pagead/ads?";d.google_ad_client==="ca-pub-6219811747049371"&&xZ.test(f)&&(g="/pagead/lopri?");e=`https://${e}${g}`;a=(hi(a.pageState.g(),6)?A(a.pageState.g(),6):A(a.sa,9))&&c.google_debug_params? 
c.google_debug_params:"";a=cm(b,e+a);return c.google_ad_url=a}function yZ(a){try{if(a.parentNode)return a.parentNode}catch{return null}if(a.nodeType===9)a:{try{const c=a?a.defaultView:window;if(c){const d=c.frameElement;if(d&&kd(c.parent)){var b=d;break a}}}catch{}b=null}else b=null;return b} 
function zZ(a,b){var c=$Q(a.pubWin);a.D.saaei&&(c+=(c===""?"":",")+a.D.saaei);a.D.google_ad_intent_eids&&(c+=`${c===""?"":","}${a.D.google_ad_intent_eids}`);b.eid=c;c=a.D.google_loeid;typeof c==="string"&&(a.g|=4096,b.loeid=c)}function AZ(a,b){a=(a=qd(a.pubWin))&&a.document?XS(a.document,a):new ll(-12245933,-12245933);b.scr_x=Math.round(a.x);b.scr_y=Math.round(a.y)}function BZ(a){try{const b=q.top.location.hash;if(b){const c=b.match(a);return c&&c[1]||""}}catch{}return""} 
function CZ(a,b,c){const d=a.D;var e=a.pubWin,f=a.L,g=be(window);d.fsapi&&(b.fsapi=!0);b.ref=d.google_referrer_url;b.loc=d.google_page_location;var h;(h=vl(e))&&oa(h.data)&&typeof h.data.type==="string"?(h=h.data.type.toLowerCase(),h=h=="doubleclick"||h=="adsense"?null:h):h=null;h&&(b.apn=h.substr(0,10));g=rH(g);b.url||b.loc||!g.url||(b.url=g.url,g.Lf||(b.usrc=1));g.url!=(b.loc||b.url)&&(b.top=g.url);a.Rc&&(b.etu=a.Rc);(c=cU(d,f,c))&&(b.fc=c);if(!hm(d)){c=a.pubWin.document;g="";if(c.documentMode&& 
(h=Pl(new Dl(c),"IFRAME"),h.frameBorder="0",h.style.height=0,h.style.width=0,h.style.position="absolute",c.body)){c.body.appendChild(h);try{const pa=h.contentWindow.document;pa.open();var k=yc("<!DOCTYPE html>");pa.write(zc(k));pa.close();g+=pa.documentMode}catch(pa){}c.body.removeChild(h)}b.docm=g}let l,m,n,p,t,z,w,B,I,T;try{l=e.screenX,m=e.screenY}catch(pa){}try{n=e.outerWidth,p=e.outerHeight}catch(pa){}try{t=e.innerWidth,z=e.innerHeight}catch(pa){}try{w=e.screenLeft,B=e.screenTop}catch(pa){}try{t= 
e.innerWidth,z=e.innerHeight}catch(pa){}try{I=e.screen.availWidth,T=e.screen.availTop}catch(pa){}b.brdim=[w,B,l,m,I,T,n,p,t,z].join();k=0;q.postMessage===void 0&&(k|=1);k>0&&(b.osd=k);b.vis=wT(e.document);k=a.ba;e=XT(d)?KV:yV(new IV(e,k,null,{width:0,height:0},d.google_ad_width,d.google_ad_height,!1));b.rsz=e.toString();b.abl=lV(e);if(!XT(d)&&(e=im(d),e!=null)){k=0;a:{try{{var R=d.google_async_iframe_id;const pa=window.document;if(R)var U=pa.getElementById(R);else{var X=pa.getElementsByTagName("script"), 
$a=X[X.length-1];U=$a&&$a.parentNode||null}}if(R=U){U=[];X=0;for(var Pb=Date.now();++X<=100&&Date.now()-Pb<50&&(R=yZ(R));)R.nodeType===1&&U.push(R);var ta=U;b:{for(Pb=0;Pb<ta.length;Pb++){c:{var Fa=ta[Pb];try{if(Fa.parentNode&&Fa.offsetWidth>0&&Fa.offsetHeight>0&&Fa.style&&Fa.style.display!=="none"&&Fa.style.visibility!=="hidden"&&(!Fa.style.opacity||Number(Fa.style.opacity)!==0)){const pa=Fa.getBoundingClientRect();var nd=pa.right>0&&pa.bottom>0;break c}}catch(pa){}nd=!1}if(!nd){var Rc=!1;break b}}Rc= 
!0}if(Rc){b:{const pa=Date.now();Rc=/^html|body$/i;nd=/^fixed/i;for(Fa=0;Fa<ta.length&&Date.now()-pa<50;Fa++){const Id=ta[Fa];if(!Rc.test(Id.tagName)&&nd.test(Id.style.position||Wl(Id,"position"))){var Sc=Id;break b}}Sc=null}break a}}}catch{}Sc=null}Sc&&Sc.offsetWidth*Sc.offsetHeight<=e.width*e.height*4&&(k=1);b.pfx=k}a:{if(Math.random()<.05&&f)try{const pa=f.document.getElementsByTagName("head")[0];var le=pa?eZ(pa):0;break a}catch(pa){}le=0}f=le;f!==0&&(b.cms=f);d.google_lrv!==a.jb&&(b.alvm=d.google_lrv|| 
"none")}function DZ(a,b){let c=0;a.location&&a.location.ancestorOrigins?c=a.location.ancestorOrigins.length:ld(()=>{c++;return!1},a);c&&(b.nhd=c)}function EZ(a,b){const c=zH(b,8,{});b=zH(b,9,{});const d=a.google_ad_section,e=a.google_ad_format;a=a.google_ad_slot;e?c[d]=c[d]?c[d]+`,${e}`:e:a&&(b[d]=b[d]?b[d]+`,${a}`:a)} 
function FZ(a,b,c){const d=a.D;var e=a.D;b.dt=Yq;e.google_async_iframe_id&&e.google_bpp&&(b.bpp=e.google_bpp);a:{try{var f=q.performance;if(f&&f.timing&&f.now){var g=f.timing.navigationStart+Math.round(f.now())-f.timing.domLoading;break a}}catch(m){}g=null}(e=(e=g)?CU(e,q.Date.now()-Yq,1E6):null)&&(b.bdt=e);b.idt=CU(a.K,Yq);e=a.D;b.shv=a.pageState.g().g()||C(a.sa,2);a.jb&&(b.mjsv=a.jb);e.google_loader_used=="sd"?b.ptt=5:e.google_loader_used=="aa"&&(b.ptt=9);/^\w{1,3}$/.test(e.google_loader_used)&& 
(b.saldr=e.google_loader_used);if(e=vl(a.pubWin))b.is_amp=1,b.amp_v=wl(e),(e=xl(e))&&(b.act=e);e=a.pubWin;e==e.top&&(b.abxe=1);e=new XQ(a.pubWin);(g=TQ(e,"__gads",c))?b.cookie=g:c.g()&&oN(e.A)&&(b.cookie_enabled="1");(g=TQ(e,"__gpi",c))&&!g.includes("&")&&(b.gpic=g);TQ(e,"__gpi_opt_out",c)==="1"&&(b.pdopt="1");e=new SU(a.pubWin);g={fg:!1,gg:!a.H};(f=RU(e,c,g))?b.eo_id_str=f:QU(e,c,g)&&(b.eoidce="1");c=uH();g=zH(c,8,{});e=d.google_ad_section;g[e]&&(b.prev_fmts=g[e]);g=zH(c,9,{});g[e]&&(b.prev_slotnames= 
g[e].toLowerCase());EZ(d,c);e=zH(c,15,0);e>0&&(b.nras=String(e));(g=vl(window))?(g?(e=g.pageViewId,g=g.clientId,typeof g==="string"&&(e+=g.replace(/\D/g,"").substr(0,6))):e=null,e=+e):(e=be(window),g=e.google_global_correlator,g||(e.google_global_correlator=g=1+Math.floor(Math.random()*8796093022208)),e=g);b.correlator=zH(c,7,e);M(cx)&&(b.rume=1);if(d.google_ad_channel){e=zH(c,10,{});g="";f=d.google_ad_channel.split(uZ);for(var h=0;h<f.length;h++){var k=f[h];e[k]?g+=k+"+":e[k]=!0}b.pv_ch=g}if(d.google_ad_host_channel){e= 
d.google_ad_host_channel;g=zH(c,11,[]);f=e.split("|");c=-1;e=[];for(h=0;h<f.length;h++){k=f[h].split(uZ);g[h]||(g[h]={});let m="";for(let n=0;n<k.length;n++){const p=k[n];p!==""&&(g[h][p]?m+="+"+p:g[h][p]=!0)}m=m.slice(1);e[h]=m;m!==""&&(c=h)}g="";if(c>-1){for(f=0;f<c;f++)g+=e[f]+"|";g+=e[c]}b.pv_h_ch=g}b.frm=d.google_iframing;b.ife=d.google_iframing_environment;a:{c=d.google_ad_client;try{const m=be(window);let n=m.google_prev_clients;n||(n=m.google_prev_clients={});if(c in n){var l=1;break a}n[c]= 
!0;l=2;break a}catch{l=0;break a}l=void 0}b.pv=l;a.L&&M(Cv)&&(l=a.L,l=Kb()&&rZ(l)?l.document.documentElement.lang:void 0,l&&(b.tl=l));M(Dv)&&a.pubWin.location.host.endsWith("h5games.usercontent.goog")&&(b.cdm=a.pubWin.location.host);DZ(a.pubWin,b);(a=d.google_ad_layout)&&tU[a]>=0&&(b.rplot=tU[a])} 
function GZ(a,b){a=a.j;EH()&&(b.npa=1);if(a){hi(a,3)&&(b.gdpr=a.l()?"1":"0");var c=y(a,1);c&&(b.us_privacy=c);(c=y(a,2))&&(b.gdpr_consent=c);(c=y(a,4))&&(b.addtl_consent=c);(c=Ph(a,7))&&(b.tcfe=c);(c=C(a,11))&&(b.gpp=c);(a=fh(a,10,kg,eh(),void 0,0))&&a.length>0&&(b.gpp_sid=a.join(","))}}function HZ(a,b){const c=a.D;GZ(a,b);vd(fZ,(d,e)=>{b[d]=c[e]});XT(c)&&(a=jU(c),b.fa=a);b.pi||c.google_ad_slot==null||(a=Ky(c),Ys(a)&&(a=lt(a.getValue()),b.pi=a))} 
function IZ(a,b){var c=zl()||VS(a.pubWin.top);c&&(b.biw=c.width,b.bih=c.height);c=a.pubWin;c!==c.top&&(a=VS(a.pubWin))&&(b.isw=a.width,b.ish=a.height)}function JZ(a,b){var c=a.pubWin;c!==null&&c!=c.top?(a=[c.document.URL],c.name&&a.push(c.name),c=VS(c,!1),a.push(c.width.toString()),a.push(c.height.toString()),a=xd(a.join(""))):a=0;a!==0&&(b.ifk=a)}function KZ(a,b){(a=CH()[a.D.google_ad_client])&&(b.psts=a.join())}function LZ(a,b){(a=Qh(a.pageState.g(),2))&&a>=0&&(b.tmod=a)} 
function MZ(a,b){(a=a.pubWin.google_user_agent_client_hint)&&(b.uach=ke(a))}function NZ(a,b){try{const e=a.pubWin&&a.pubWin.external&&a.pubWin.external.getHostEnvironmentValue&&a.pubWin.external.getHostEnvironmentValue.bind(a.pubWin.external);if(e){var c=JSON.parse(e("os-mode")),d=parseInt(c["os-mode"],10);d>=0&&(b.wsm=d+1)}}catch{}}function OZ(a,b){a.D.google_ad_public_floor>=0&&(b.pubf=a.D.google_ad_public_floor);a.D.google_ad_private_floor>=0&&(b.pvtf=a.D.google_ad_private_floor)} 
function PZ(a,b){const c=Number(a.D.google_traffic_source);c&&Object.values(Ca).includes(c)&&(b.trt=a.D.google_traffic_source)}function QZ(a,b){var c;if(c=!M(ix))c=a.B?.label,c=!(M(Pw)&&c&&c.match(kx(Nw)));c&&("runAdAuction"in a.pubWin.navigator&&"joinAdInterestGroup"in a.pubWin.navigator&&(b.td=1),c=a.pubWin.navigator,a.pubWin.isSecureContext&&"runAdAuction"in c&&c.runAdAuction instanceof Function&&KQ("run-ad-auction",a.pubWin.document)&&(c=new hZ,c.set(1,LQ(a.pubWin.navigator)),b.tdf=gZ(c)))} 
function RZ(a,b){if(M(Lw)&&navigator.deprecatedRunAdAuctionEnforcesKAnonymity){var c=new pZ;var d=new oZ;d=fi(d,4,"deprecated_kanon");c=x(c,2,d)}a.B!=null&&Kb()&&(c??(c=new pZ),d=fi(c,3,a.B.label),F(d,4,a.B.status));c&&(b.psd=ke(ii(c)))}function SZ(a,b){M($w)||KQ("attribution-reporting",a.pubWin.document)&&(b.nt=1)} 
function TZ(a,b){if(typeof a.D.google_privacy_treatments==="string"){var c=new Map([["disablePersonalization",1]]);a=a.D.google_privacy_treatments.split(",");var d=[];for(const [e,f]of c.entries())c=f,a.includes(e)&&d.push(c);d.length&&(b.ppt=d.join("~"))}}function UZ(a,b){if(a.C){a.C.Lj&&(b.xatf=1);try{a.C.Cf?.disconnect(),a.C.Cf=void 0}catch{}}}function VZ(a,b=document){if(M(Tu))try{const {labels:c}=kZ(b);c.length&&(a.pgls=c.map(d=>ie(jZ(d))).join("~"))}catch(c){sB.ma(1278,c)}} 
function wZ(a,b){const c={};HZ(a,c);MZ(a,c);FZ(a,c,b);c.u_tz=-(new Date).getTimezoneOffset();try{var d=cl.history.length}catch(e){d=0}c.u_his=d;c.u_h=cl.screen?.height;c.u_w=cl.screen?.width;c.u_ah=cl.screen?.availHeight;c.u_aw=cl.screen?.availWidth;c.u_cd=cl.screen?.colorDepth;c.u_sd=WS(a.pubWin);c.dmc=a.pubWin.navigator?.deviceMemory;vB(889,()=>{if(a.L==null)c.adx=-12245933,c.ady=-12245933;else{var e=ZS(a.L,a.ba);c.adx&&c.adx!=-12245933&&c.ady&&c.ady!=-12245933||(c.adx=Math.round(e.x),c.ady=Math.round(e.y)); 
YS(a.ba)||(c.adx=-12245933,c.ady=-12245933,a.g|=32768)}});IZ(a,c);JZ(a,c);AZ(a,c);zZ(a,c);c.oid=2;KZ(a,c);c.pvsid=Zd(a.pubWin,sB);LZ(a,c);NZ(a,c);c.uas=tZ(a.pubWin);(d=nZ(a.pubWin))&&(c.nvt=d);a.F&&(c.scar=a.F);a.l instanceof Uint8Array?c.topics=ie(a.l):a.l&&(c.topics=a.l,c.tps=a.l);UZ(a,c);CZ(a,c,b);c.fu=a.g;c.bc=iZ();if(hi(a.pageState.g(),6)?A(a.pageState.g(),6):A(a.sa,9))if(aR(c),c.creatives=BZ(/\b(?:creatives)=([\d,]+)/),c.adgroups=BZ(/\b(?:adgroups)=([\d,]+)/),c.adgroups||c.sso)c.adtest="on", 
c.disable_budget_throttling=!0,c.use_budget_filtering=!1,c.retrieve_only=!0,c.disable_fcap=!0;il()&&(c.atl=!0);c.bz=ce(a.pubWin);OZ(a,c);PZ(a,c);QZ(a,c);RZ(a,c);SZ(a,c);TZ(a,c);String(a.D.google_special_category_data)==="true"&&(c.scd=1);VZ(c,a.pubWin.document);return c}const xZ=/YtLoPri/;var WZ=class extends G{};function XZ(a){return zh(a,WZ,15,eh())}var YZ=class extends G{},ZZ=Uj(YZ);function $Z(){var a=new a_;var b=new Zt;b=gi(b,2,4);b=gi(b,8,1);var c=new ht;c=ei(c,7,"#dpId");b=x(b,1,c);return Mh(a,3,Zt,b)}var a_=class extends G{},b_=Uj(a_);function c_(a,b){return C(a,10).replace("TERM",b)};var d_=class{constructor(a){this.Eb=a.Eb??[];this.pf=!!a.pf;this.rf=!!a.rf;this.qf=!!a.qf;this.ne=a.ne??250;this.me=a.me??300;this.Re=a.Re??15E3;this.Oe=a.Oe??"#FFFFFF";this.Rb=a.Rb??"#1A73E8";this.Qe=a.Qe??15E3;this.Se=a.Se??0;this.fe=a.fe??0;this.qc=a.qc??670;this.Mb=!!a.Mb;this.Pd=a.Pd??[];this.og=a.og||"450px";this.Qd=!!a.Qd;this.Hf=!!a.Hf;this.he=a.he??0;this.we=a.we??!0;this.de=a.de??0;this.Hh=!!a.Hh;this.Vf=!!a.Vf;this.ke=a.ke??0;this.nc=a.nc??0;this.Sf=new Set(a.Sf??[]);this.Ce=!!a.Ce;this.ig= 
!!a.ig;this.hg=!!a.hg}};function e_(a,b,c,d,e,f,g,h,k){const l=k(999,a.top,m=>{m.data.action==="init"&&m.data.adChannel==="ShoppingVariant"&&f_(a,b,d,c,e,f,g,h)});g(()=>{a.top.removeEventListener("message",l)})}function f_(a,b,c,d,e,f,g,h){A(f,13)||zD(c,d,e);const k=b.contentDocument.documentElement,l=new ResizeObserver(()=>{b.height=`${Math.ceil(k.offsetHeight+26)}px`});l.observe(k);const m=h(1066,a,()=>{const n=k.offsetHeight;n&&(b.height=`${n+26}px`)},1E3);g(()=>{l.disconnect();a.clearInterval(m)})} 
var g_=class{constructor(a){this.gb=a}Sb(a){const b=a.A.document.createElement("iframe"),c=a.P,d=new AD({Ba:b,La:C(c,16),Vb:"anno-cse",Wa:this.gb.replace("ca","partner"),xd:"ShoppingVariant",location:a.A.location,language:C(c,7),jc:c_(c,a.na),Ta:a.J.Eb.filter(e=>e!==42),Va:!1,Hb:void 0,md:!0,rg:void 0,Ua:!0});d.M();e_(a.A,b,a.na,d,a.qd,c,a.Da,a.Ra,a.Ia);return b}};function h_(a,b,c){return a?b.innerWidth:Math.min(b.document.body.clientWidth,c)};var i_=class{constructor(a,b,c,d){this.gb=a;this.bd=b;this.g=c;this.ib=d}Sb(a){var b=a.A;b=a.ea?.95*b.innerHeight-30:b.innerHeight;var c=a.na;var d=a.Qc||"",e=this.gb,f=this.bd??"",g=a.xb,h=a.J.Vf,k=a.W,l=!!A(a.P,13),m={};const n=a.J.Pd.join(","),p=a.J.we,t=this.g,z=a.J.Ce?this.ib:null,w=a.format,B=a.J.Qd,I=m&&m.Wb;m=m&&m.Ug;c=QC((l?"":'<link href="https://fonts.googleapis.com/css?family=Google+Sans:500" rel="stylesheet"'+(I?' nonce="'+Y(rD(I))+'"':"")+">")+"<style"+(I?' nonce="'+Y(rD(I))+'"':"")+ 
">#gda-search-term {height: 24px; font-size: 18px; font-weight: 500; color: #202124; font-family: 'Google Sans'; padding-bottom: 6px;"+(k?"padding-right: 16px;":"padding-left: 16px;")+"}"+(h?".adsbygoogle {display: inline-block;}.adsbygoogle {width: calc(100% - 2.5px); height: max(280px, calc(50vh - 25px));}@media (min-width: 488px) {.adsbygoogle {width: calc(50% - 2.5px); height: max(280px, calc(50vh - 50px));}}":"")+'</style><div id="gda-search-term">'+OC(c)+"</div>"+(t!==-1?"<script"+(m?' nonce="'+ 
Y(rD(m))+'"':"")+">window["+$C(aD("goog_pvsid"))+"] = "+$C(aD(t))+";\x3c/script>":"")+(f!==""?'<meta name="google-adsense-platform-account" content="'+Y(f)+'">':"")+(l?"":'<script data-ad-intent-query="'+Y(c)+'" data-ad-intent-qetid="'+Y(d)+'" data-ad-intent-eids="'+Y(n)+'"'+(z?' data-page-url="'+Y(fD(z))+'"':"")+(B?' data-ad-intents-format="'+Y(w)+'"':"")+' async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client='+eD(e)+'" crossorigin="anonymous"'+(m?' nonce="'+Y(rD(m))+ 
'"':"")+">\x3c/script>")+(h?'<ins class="adsbygoogle" data-ad-client="'+Y(e)+'"> </ins><ins class="adsbygoogle" data-ad-client="'+Y(e)+'"> </ins>'+(g>=488?'<ins class="adsbygoogle" data-ad-client="'+Y(e)+'"> </ins><ins class="adsbygoogle" data-ad-client="'+Y(e)+'"> </ins>':"")+"<script"+(m?' nonce="'+Y(rD(m))+'"':"")+">for (let slot = document.getElementsByClassName('adsbygoogle').length; slot; slot--) (adsbygoogle=window.adsbygoogle||[]).push({});\x3c/script>":'<ins class="adsbygoogle" style="display:inline-block;width:'+ 
Y(Z(g))+"px;height:"+Y(Z(b-30))+'px" data-ad-client="'+Y(e)+'"></ins>')+(p?"<script"+(m?' nonce="'+Y(rD(m))+'"':"")+">(adsbygoogle=window.adsbygoogle||[]).requestNonPersonalizedAds=1;\x3c/script>":"")+(l?"<script"+(m?' nonce="'+Y(rD(m))+'"':"")+">const el = document.querySelector('ins.adsbygoogle'); el.dir = 'ltr'; el.style.backgroundColor = 'lightblue'; el.style.fontSize = '25px'; el.style.textDecoration = 'none'; el.textContent = \"Loading display ads inside this slot for query = "+bD(c)+' and " + "property code = '+ 
bD(e)+'";\x3c/script>':""));c=fd("body",{dir:a.W?"rtl":"ltr",lang:C(a.P,7),style:"margin:0px;height:100%;padding-top: 0px;overflow: hidden;"},LC(c));a=a.A.document.createElement("IFRAME");r(a,{border:"0",width:"100%",height:`${b}px`});a.srcdoc=zc(c);return a}};function j_(a){var b={};const c=a.na,d=a.jj,e=a.gb,f=a.bd,g=a.xb,h=a.li,k=a.Rj,l=a.Vk,m=a.eids,n=a.Dk,p=a.ik,t=a.jk,z=a.lk,w=a.mi;a=a.Ik;const B=b&&b.Wb;b=b&&b.Ug;return QC((l?"":'<link href="https://fonts.googleapis.com/css?family=Google+Sans:500" rel="stylesheet"'+(B?' nonce="'+Y(rD(B))+'"':"")+">")+"<style"+(B?' nonce="'+Y(rD(B))+'"':"")+">#gda-search-term {height: 24px; font-size: 18px; font-weight: 500; color: #202124; font-family: 'Google Sans'; padding-bottom: 6px;"+(k?"padding-right: 16px;": 
"padding-left: 16px;")+"}</style>"+(t!==-1?"<script"+(b?' nonce="'+Y(rD(b))+'"':"")+">window["+$C(aD(p))+"] = "+$C(aD(t))+";\x3c/script>":"")+(f!==""?'<meta name="google-adsense-platform-account" content="'+Y(f)+'">':"")+'<ins id="display-slot" class="adsbygoogle" style="display:inline-block;width:'+Y(Z(g))+"px;height:calc("+Y(Z(h))+')" data-ad-client="'+Y(e)+'"></ins>'+(n?"<script"+(b?' nonce="'+Y(rD(b))+'"':"")+">(adsbygoogle=window.adsbygoogle||[]).requestNonPersonalizedAds=1;\x3c/script>":"")+ 
(l?"<script"+(b?' nonce="'+Y(rD(b))+'"':"")+">const el = document.querySelector('ins.adsbygoogle'); el.dir = 'ltr'; el.style.backgroundColor = 'lightblue'; el.style.fontSize = '25px'; el.style.textDecoration = 'none'; el.textContent = \"Loading display ads inside this slot for query = "+bD(c)+' and " + "property code = '+bD(e)+'";\x3c/script>':"")+"<script"+(b?' nonce="'+Y(rD(b))+'"':"")+">top.postMessage({'action':'sgda-ready'}, top.location.origin);\x3c/script>"+(l?"":'<script data-ad-intent-query="'+ 
Y(c)+'" data-ad-intent-qetid="'+Y(d)+'" data-ad-intent-eids="'+Y(m)+'"'+(z?' data-page-url="'+Y(fD(z))+'"':"")+(a?' data-ad-intents-format="'+Y(w)+'"':"")+' async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client='+eD(e)+'" crossorigin="anonymous"'+(b?' nonce="'+Y(rD(b))+'"':"")+">\x3c/script>"))};var n_=class{constructor(a,b,c,d){this.gb=a;this.bd=b;this.g=c;this.ib=d}Sb(a){var b=j_({na:a.na,jj:a.Qc||"",gb:this.gb,bd:this.bd??"",xb:a.xb,li:a.J.og,Rj:a.W,Vk:!!A(a.P,13),eids:a.J.Pd.join(","),Dk:a.J.we,ik:"goog_pvsid",jk:this.g,lk:a.J.Ce?this.ib:null,mi:a.format,Ik:a.J.Qd});b=fd("body",{dir:a.W?"rtl":"ltr",lang:C(a.P,7),style:"margin:0;height:100%;padding-top:0;overflow:hidden"},LC(b));const c=a.A.document.createElement("iframe");r(c,{border:"0",width:"100%"});c.height="24px";const d=a.Ia(999, 
a.A,e=>{if(e.data.action==="sgda-ready"&&(e=c.contentDocument,e?.body)){var f=e.getElementById("display-slot");if(f){var g=e.createElement("iframe"),h=new MutationObserver((k,l)=>{if(f.getAttribute("data-ad-status")==="unfilled")Il(f);else if(f.getAttribute("data-ad-status")!=="filled")return;k_(g,c);l_(a.A,g,c,a.Da,a.Ra);l.disconnect()});h.observe(f,{attributes:!0,attributeFilter:["data-ad-status"]});a.Da(()=>void h.disconnect());m_(this.gb,g,a);e.body.append(g)}}});a.Da(()=>{a.A.removeEventListener("message", 
d)});c.srcdoc=zc(b);return c}};function m_(a,b,c){const d=new AD({Ba:b,La:C(c.P,16),Vb:"anno-cse",Wa:a.replace("ca","partner"),xd:"ShoppingVariant",location:c.A.location,language:C(c.P,7),jc:c_(c.P,c.na),Ta:c.J.Eb.filter(f=>f!==42),Va:!1,Hb:void 0,md:!0,rg:void 0,Ua:!0,sg:!0}),e=c.Ia(999,c.A,f=>{f.data.action==="init"&&f.data.adChannel==="ShoppingVariant"&&(A(c.P,13)||zD(d,c.na,c.qd))});d.M();c.Da(()=>{c.A.removeEventListener("message",e)})} 
function l_(a,b,c,d,e){const f=b.contentDocument.documentElement,g=new ResizeObserver(()=>void k_(b,c));g.observe(f);const h=e(1066,a,()=>void k_(b,c),1E3);d(()=>{g.disconnect();a.clearInterval(h)})}function k_(a,b){const c=a.contentDocument?.documentElement?.offsetHeight;if(c){var d=b.contentDocument?.getElementById("display-slot")?.offsetHeight??0;a.height=`${Math.ceil(c+26)}px`;b.height=`${Math.ceil(c+26+d)}px`}};function o_(a,b){a=HA(jy([...b],a),a);if(a.length!==0)return a.reduce((c,d)=>c.pa.g>d.pa.g?c:d)};async function p_(a,b){await new Promise(c=>void a.A.setTimeout(c,0));a.i=a.g.la(b)+a.j}var q_=class{constructor(a,b){var c=V(vw);this.A=a;this.g=b;this.j=c;this.i=b.la(2)+c}};var r_=class{constructor(a){this.performance=a}la(){return this.performance.now()}},s_=class{la(){return Date.now()}};const t_=[255,255,255];function u_(a){function b(d){return[Number(d[1]),Number(d[2]),Number(d[3]),d.length>4?Number(d[4]):1]}var c=a.match(/rgb\(([0-9]+),\s*([0-9]+),\s*([0-9]+)\)/);if(c||(c=a.match(/rgba\(([0-9]+),\s*([0-9]+),\s*([0-9]+),\s*([0-9\\.]+)\)/)))return b(c);if(a==="transparent"||a==="")return[0,0,0,0];throw Error(`Invalid color: ${a}`);} 
function v_(a){var b=getComputedStyle(a);if(b.backgroundImage!=="none")return null;b=u_(b.backgroundColor);var c=w_(b);if(c)return c;a=(a=a.parentElement)?v_(a):t_;if(!a)return null;c=b[3];return[Math.round(c*b[0]+(1-c)*a[0]),Math.round(c*b[1]+(1-c)*a[1]),Math.round(c*b[2]+(1-c)*a[2])]}function w_(a){return a[3]===1?[a[0],a[1],a[2]]:null};function x_(a,b){const c=b.ea===b.W;var d=y_(a,b,c);if(!d)return null;d=d.position.Rd();a=z_(a,d,b,function(f){f=f.getBoundingClientRect();return c?b.V-f.right:f.left});if(!a||a-16<200)return null;const e=b.V;return{Ca:c?e-a:16,Qa:c?16:e-a,ia:d}}function A_(a,b){const c=hr(a),d=ir(a);return EF(new JF(a),new ml(d-b.ia-50,c-b.Qa,d-b.ia,b.Ca)).size>0} 
function y_(a,b,c){b=Math.floor(b.Z*.3);return b<66?null:MF(a,{xc:c?SF({ia:16,Qa:16}):QF({ia:16,Ca:16}),Nf:b-66,Pb:200,Tf:50,ee:b,wb:16},[a.document.body]).af}function z_(a,b,c,d){a=c.ea?B_(a,b,c):C_(a,b,c);b=c.V;let e=c.ea?b:b*.35;a.forEach(f=>{e=Math.min(e,d(f))});return e<16?null:e-16}function B_(a,b,c){const d=c.Z;return EF(new JF(a),new ml(d-b-50,c.V-16,d-b,16))}function C_(a,b,c){const d=c.Z,e=c.V;c=c.W;return EF(new JF(a),new ml(d-b-50,(c?e*.35:e)-16,d-b,(c?16:e*.65)+16))} 
function D_(a,b,c){const d=a.W;return{Ca:d?E_(a,b,c):c,Qa:d?c:E_(a,b,c),ia:16}}function E_(a,b,c){const d=a.V;return a.ea?d-b+16:Math.max(d-c-d*.35,d-b+16)}function F_(a,b){const c=b.W,d=b.V;return[...(b.ea?B_(a,16,b):C_(a,16,b))].map(e=>new LF(c?d-e.getBoundingClientRect().right:e.getBoundingClientRect().left,c?d-e.getBoundingClientRect().left:e.getBoundingClientRect().right)).sort((e,f)=>e.start-f.start)};function G_(a){r(a,{border:"0","box-shadow":"none",display:"inline","float":"none",margin:"0",outline:"0",padding:"0"})}function H_(a,b){b=a.document.createElement(b);Du(a,b);r(b,{color:"inherit",cursor:"inherit",direction:"inherit","font-family":"inherit","font-size":"inherit","font-weight":"inherit","text-align":"inherit","text-orientation":"inherit",visibility:"inherit","writing-mode":"inherit"});return b}function I_(a){a.dataset.googleVignette="false";a.dataset.googleInterstitial="false"};const J_=[{ng:"1907259590",pe:480,Me:220},{ng:"2837189651",pe:400,Me:180},{ng:"9211025045",pe:360,Me:160},{ng:"6584860439",pe:-Infinity,Me:150}];function K_(a){J_.find(b=>b.pe<=a)};function L_(a){for(const b of a.g)b();a.g.length=0}var M_=class{constructor(){this.g=[]}};const N_=new M_;let O_=!1;function P_(a){Q_(a.config,1065,a.A,()=>{if(!a.g){var b=kp(new np,a.i);var c=new jp;b=Ah(b,2,mp,c);a.config.U.nd(b)}},1E4)}var R_=class{constructor(a,b,c){this.A=a;this.config=b;this.i=c;this.g=!1}cancel(a){this.A.clearTimeout(a)}};function S_(a){N_.g.push(a)} 
function T_(a,b,c,d,e,f,g=null){K_(a.document.body.clientWidth);d=b.ea?U_(a,b,d,e,g,f):V_(a,b,d,e,g,f);Yr(d.isVisible(),!1,()=>{O_=!1;L_(N_)});d.show({tf:!0});O_=!0;const h=new R_(a,b,c);P_(h);S_(()=>{var k=b.U,l=k.nd;var m=lp(kp(new np,c));l.call(k,m);h.g=!0})} 
function U_(a,b,c,d,e,f){d=b.Xb.Sb({A:a,na:c,qd:d,J:b.J,ea:b.ea,W:b.W(),P:b.P,Qc:e,xb:h_(b.ea,a,b.J.qc),Ia:b.Ia.bind(b),Ra:b.Ra.bind(b),Da:S_,format:f});return fF(a,d,{dg:.95,yf:.95,zIndex:2147483647,Ab:!0,Kc:"adpub-drawer-root",Jc:!1,Ma:!0,Oc:new O(c_(b.P,c))})} 
function V_(a,b,c,d,e,f){const g=h_(b.ea,a,b.J.qc);d=b.Xb.Sb({A:a,na:c,qd:d,J:b.J,ea:b.ea,W:b.W(),P:b.P,Qc:e,xb:g,Ia:b.Ia.bind(b),Ra:b.Ra.bind(b),Da:S_,format:f});return oE(a,d,{Vc:`${g}px`,Tc:b.W(),Ec:C(b.P,14),zIndex:2147483647,Ab:!0,uf:!0,Kc:"adpub-drawer-root",Jc:!1,Ma:!0,Oc:new O(c_(b.P,c))})};function W_(a,b,c){a=X_(a,"100 -1000 840 840",`calc(${b} - 2px)`,b,Y_[c]);r(a,{color:"inherit",cursor:"inherit",fill:"currentcolor"});return a}function Z_(a,b,c){a=$_(a,"20px",b.Rb,c);a.classList.add("google-anno-sa-intent-icon");return a} 
function a0(a,b,c){a=X_(a,"0 -960 960 960","20px","20px","m256-200-56-56 224-224-224-224 56-56 224 224 224-224 56 56-224 224 224 224-56 56-224-224-224 224Z");r(a,{left:"13px",right:"","pointer-events":"initial",position:"absolute",top:"15px",transform:"none",fill:b.Rb});a.role="button";a.ariaLabel=c;a.tabIndex=0;return a} 
const Y_={[0]:"M503-104q-24 24-57 24t-57-24L103-390q-23-23-23-56.5t23-56.5l352-353q11-11 26-17.5t32-6.5h286q33 0 56.5 23.5T879-800v286q0 17-6.5 32T855-456L503-104Zm196-536q25 0 42.5-17.5T759-700q0-25-17.5-42.5T699-760q-25 0-42.5 17.5T639-700q0 25 17.5 42.5T699-640ZM446-160l353-354v-286H513L160-446l286 286Zm353-640Z",[1]:"m274-274-128-70 42-42 100 14 156-156-312-170 56-56 382 98 157-155q17-17 42.5-17t42.5 17q17 17 17 42.5T812-726L656-570l98 382-56 56-170-312-156 156 14 100-42 42-70-128Z",[2]:"M784-120 532-372q-30 24-69 38t-83 14q-109 0-184.5-75.5T120-580q0-109 75.5-184.5T380-840q109 0 184.5 75.5T640-580q0 44-14 83t-38 69l252 252-56 56ZM380-400q75 0 127.5-52.5T560-580q0-75-52.5-127.5T380-760q-75 0-127.5 52.5T200-580q0 75 52.5 127.5T380-400Z", 
[3]:"M480-80q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm200-500 54-18 16-54q-32-48-77-82.5T574-786l-54 38v56l160 112Zm-400 0 160-112v-56l-54-38q-54 17-99 51.5T210-652l16 54 54 18Zm-42 308 46-4 30-54-58-174-56-20-40 30q0 65 18 118.5T238-272Zm242 112q26 0 51-4t49-12l28-60-26-44H378l-26 44 28 60q24 8 49 12t51 4Zm-90-200h180l56-160-146-102-144 102 54 160Zm332 88q42-50 60-103.5T800-494l-40-28-56 18-58 174 30 54 46 4Z", 
[4]:"M120-680v-160l160 80-160 80Zm600 0v-160l160 80-160 80Zm-280-40v-160l160 80-160 80Zm0 640q-76-2-141.5-12.5t-114-26.5Q136-135 108-156t-28-44v-360q0-25 31.5-46.5t85.5-38q54-16.5 127-26t156-9.5q83 0 156 9.5t127 26q54 16.5 85.5 38T880-560v360q0 23-28 44t-76.5 37q-48.5 16-114 26.5T520-80v-160h-80v160Zm40-440q97 0 167.5-11.5T760-558q0-5-76-23.5T480-600q-128 0-204 18.5T200-558q42 15 112.5 26.5T480-520ZM360-166v-154h240v154q80-8 131-23.5t69-27.5v-271q-55 22-138 35t-182 13q-99 0-182-13t-138-35v271q18 12 69 27.5T360-166Zm120-161Z", 
[5]:"M200-80q-33 0-56.5-23.5T120-160v-480q0-33 23.5-56.5T200-720h80q0-83 58.5-141.5T480-920q83 0 141.5 58.5T680-720h80q33 0 56.5 23.5T840-640v480q0 33-23.5 56.5T760-80H200Zm0-80h560v-480H200v480Zm280-240q83 0 141.5-58.5T680-600h-80q0 50-35 85t-85 35q-50 0-85-35t-35-85h-80q0 83 58.5 141.5T480-400ZM360-720h240q0-50-35-85t-85-35q-50 0-85 35t-35 85ZM200-160v-480 480Z",[6]:"M80-160v-120h80v-440q0-33 23.5-56.5T240-800h600v80H240v440h240v120H80Zm520 0q-17 0-28.5-11.5T560-200v-400q0-17 11.5-28.5T600-640h240q17 0 28.5 11.5T880-600v400q0 17-11.5 28.5T840-160H600Zm40-120h160v-280H640v280Zm0 0h160-160Z", 
[7]:"M400-40v-80H200q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h200v-80h80v880h-80ZM200-240h200v-240L200-240Zm360 120v-360l200 240v-520H560v-80h200q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H560Z",[8]:"M300-240q25 0 42.5-17.5T360-300q0-25-17.5-42.5T300-360q-25 0-42.5 17.5T240-300q0 25 17.5 42.5T300-240Zm0-360q25 0 42.5-17.5T360-660q0-25-17.5-42.5T300-720q-25 0-42.5 17.5T240-660q0 25 17.5 42.5T300-600Zm180 180q25 0 42.5-17.5T540-480q0-25-17.5-42.5T480-540q-25 0-42.5 17.5T420-480q0 25 17.5 42.5T480-420Zm180 180q25 0 42.5-17.5T720-300q0-25-17.5-42.5T660-360q-25 0-42.5 17.5T600-300q0 25 17.5 42.5T660-240Zm0-360q25 0 42.5-17.5T720-660q0-25-17.5-42.5T660-720q-25 0-42.5 17.5T600-660q0 25 17.5 42.5T660-600ZM200-120q-33 0-56.5-23.5T120-200v-560q0-33 23.5-56.5T200-840h560q33 0 56.5 23.5T840-760v560q0 33-23.5 56.5T760-120H200Zm0-80h560v-560H200v560Zm0-560v560-560Z", 
[9]:"M160-80v-440H80v-240h208q-5-9-6.5-19t-1.5-21q0-50 35-85t85-35q23 0 43 8.5t37 23.5q17-16 37-24t43-8q50 0 85 35t35 85q0 11-2 20.5t-6 19.5h208v240h-80v440H160Zm400-760q-17 0-28.5 11.5T520-800q0 17 11.5 28.5T560-760q17 0 28.5-11.5T600-800q0-17-11.5-28.5T560-840Zm-200 40q0 17 11.5 28.5T400-760q17 0 28.5-11.5T440-800q0-17-11.5-28.5T400-840q-17 0-28.5 11.5T360-800ZM160-680v80h280v-80H160Zm280 520v-360H240v360h200Zm80 0h200v-360H520v360Zm280-440v-80H520v80h280Z",[10]:"m456-200 174-340H510v-220L330-420h126v220Zm24 120q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"}; 
function $_(a,b,c,d){a=X_(a,"0 -960 960 960",b,b,Y_[d]);r(a,{fill:c,cursor:"inherit"});return a}function X_(a,b,c,d,e){const f=a.document.createElementNS("http://www.w3.org/2000/svg","path");f.setAttribute("d",e);e=a.document.createElementNS("http://www.w3.org/2000/svg","svg");Du(a,e);e.setAttribute("viewBox",b);e.setAttribute("width",c);e.setAttribute("height",d);G_(e);e.appendChild(f);return e};function b0(a,b,c,d){const e=document.createElement("SPAN");Du(a,e);e.id="gda";e.appendChild(a0(a,b.J,C(b.P,18)));I_(e);c0(b,1064,e,f=>{d?.();Il(c);f.preventDefault();f.stopImmediatePropagation();return!1});return e} 
function d0(a,b,c,d,e){const f=document.createElement("SPAN");Du(a,f);G_(f);r(f,{position:"absolute",top:"2.5px",bottom:"2.5px",left:(b.W(),"50px"),right:b.W()?"24px":"12px",display:"flex","flex-direction":"row",color:b.J.Rb,cursor:"pointer",transition:"width 5s"});b.ea||r(f,{"justify-content":""});const g=Z_(a,b.J,b.g.get(d.ya)||0),h=document.createElement("SPAN");r(h,{display:"inline-block",cursor:"inherit"});r(h,{"margin-left":b.W()?"6px":"4px","margin-right":b.W()?"4px":"6px","margin-top":"12px"}); 
f.appendChild(h);h.appendChild(g);c.classList?.add("google-anno-sa-qtx","google-anno-skip");c.tabIndex=0;c.role="link";c.ariaLive="polite";e0(c,d.ya,C(b.P,19));r(c,{height:"40px","align-items":"center","line-height":"44px","font-size":"16px","font-weight":"400","font-style":"normal","font-family":"Roboto","text-overflow":"ellipsis","white-space":"nowrap",overflow:"hidden","-webkit-tap-highlight-color":"transparent",color:b.J.Rb});I_(f);c0(b,999,f,k=>{k.preventDefault();if((d.Hg??0)+800<=b.la(26)){k= 
d.ya;const m=b.B.get(k)||"";var l=dp(bp(k),d.gd);l=ci(l,3,d.Zd);l=b.U.Dc(l);e?f0(e,a,b,l,k,m,2,b.J.Mb?b.i.get(k)||"":null):T_(a,b,l,k,m,2,b.J.Mb?b.i.get(k)||"":null)}return!1});f.appendChild(c);return f} 
function g0(a,b,c,d,e,f){const g=document.createElement("div");Du(a,g);g.id="google-anno-sa";g.dir=b.W()?"rtl":"ltr";g.tabIndex=0;r(g,{background:b.J.Oe,"border-style":"solid",bottom:`${d.ia}px`,"border-radius":"16px",height:"50px",position:"fixed","text-align":"center",border:"0px",left:`${d.Ca}px`,right:`${d.Qa}px`,"box-shadow":"0px 1px 2px rgba(0, 0, 0, 0.3), 0px 1px 3px 1px rgba(0, 0, 0, 0.15)","z-index":"1000"});r(g,{fill:"white"});d=document.createElement("SPAN");Du(a,d);r(d,{cursor:"inherit"}); 
g.appendChild(d0(a,b,d,c,f));g.appendChild(b0(a,b,g,e));return g}function h0(a,b,c,d,e){var f=c.getElementsByClassName("google-anno-sa-qtx")[0];f instanceof HTMLElement&&(f.innerText=a.ya);if((d.g.get(e)||0)!==(d.g.get(a.ya)||0)){b=Z_(b,d.J,d.g.get(a.ya)||0);for(const g of c.getElementsByClassName("google-anno-sa-intent-icon"))g.replaceWith(b)}e0(f,a.ya,C(d.P,19));c=d.U;d=c.Ue;f=new Fo;f=$g(f,2,ng(a.gd));a=fi(f,4,a.ya);return d.call(c,a)} 
function i0(a,b,c,d,e){if(A_(b,d))return null;a.Hg=c.la(25);d=g0(b,c,a,d,()=>{a.g=!0;var f=c.U,g=f.Pe;var h=new Do;h=ph(h,3,ng(a.gd),"0");h=fi(h,2,a.ya);g.call(f,h)},e);e=h0(a,b,d,c,a.ya);b.document.body.appendChild(d);return e}function j0(a,b,c,d,e,f,g,h){if(!(a.g||a.ya===e&&a.gd===d&&a.i===f)){if(a.Zd!==null){var k=a.Zd,l=c.U,m=l.Te,n=new Eo;k=di(n,1,k);m.call(l,k)}l=a.ya;a.ya=e;a.gd=d;a.i=f;A(c.P,17)||(d=b.document.getElementById("google-anno-sa"),a.Zd=d?h0(a,b,d,c,l):i0(a,b,c,g,h))}} 
var k0=class{constructor(){this.ya="";this.gd=null;this.i="";this.Zd=null;this.g=!1;this.Hg=null}};function e0(a,b,c){a.ariaLabel=c.replace("TERM",b)};function l0(a,b){a.i>=a.j.length&&(a.i=0,a.C++);if(!(a.config.J.fe&&a.C>=a.config.J.fe)){if(a.g){if(a.g.g){a.g.Da(()=>void l0(a,b));return}}else if(O_){S_(()=>void l0(a,b));return}var c=a.j[a.i++];a.l=!1;j0(a.H,a.A,a.config,c.g,c.na,c.i,a.B,a.g);Q_(a.config,898,a.A,()=>void l0(a,b),a.xg)}}var m0=class{constructor(a,b,c,d){var e=new k0;this.A=a;this.config=b;this.H=e;this.B=c;this.g=d;this.j=[];this.l=!0;this.C=this.i=0;this.xg=b.params.xg}}; 
class n0{constructor(a,b,c){this.g=a;this.na=b;this.i=c}};function o0(a){return a.ge>0&&a.i.j>=a.ge}var q0=class{constructor(a,b,c,d){this.mg=b;this.Ze=c;this.ge=d;this.g=0;this.i=new p0(a)}};function r0(a,b){b-=a.l;for(const c of a.g.keys()){const d=a.g.get(c);let e=0;for(;e<d.length&&d[e]<b;)e++;a.i-=e;e>0&&a.g.set(c,d.slice(e))}}function s0(a,b,c){let d=[];a.g.has(b)&&(d=a.g.get(b));d.push(c);a.i++;a.g.set(b,d)}class p0{constructor(a){this.l=a;this.g=new Map;this.i=0}get j(){return this.i}};function t0(a,b,c,d){const e=H_(a,"div");e.classList.add("google-anno-skip","google-anno-sc");d=a.getComputedStyle(d).fontSize||"16px";var f=e.appendChild;var g=b.g.get(c)||0;g=$_(a,d,"#FFFFFF",g);r(g,{position:"relative",top:"3px"});const h=H_(a,"span");r(h,{display:"inline-block","padding-left":b.W()?"":"3px","padding-right":b.W()?"3px":""});h.appendChild(g);f.call(e,h);f=e.appendChild;g=H_(a,"span");g.appendChild(a.document.createTextNode(c));r(g,{position:"relative",left:b.W()?"":"3px",right:b.W()? 
"3px":"","padding-left":b.W()?"6px":"","padding-right":b.W()?"":"6px"});f.call(e,g);r(e,{display:"inline-block","border-radius":"20px","padding-left":b.W()?"7px":"6px","padding-right":b.W()?"6px":"7px","padding-top":"3px","padding-bottom":"3px","border-width":"1px","border-style":"solid",color:"#FFFFFF","font-family":"Roboto","font-weight":"500","font-size":d,"border-color":"#D7D7D7",background:"#0B57D0",cursor:"pointer","margin-top":"-3px"});e.tabIndex=0;e.role="link";e.ariaLabel=c;return e};const u0=["BTN","BUTTON"]; 
function v0(a,b){switch(a.tagName?.toUpperCase?.()){case "IFRAME":case "A":case "AUDIO":case "BUTTON":case "CANVAS":case "CITE":case "CODE":case "EMBED":case "FOOTER":case "FORM":case "KBD":case "LABEL":case "OBJECT":case "PRE":case "SAMP":case "SCRIPT":case "SELECT":case "STYLE":case "SUB":case "SUPER":case "SVG":case "TEXTAREA":case "TIME":case "VAR":case "VIDEO":case null:return!1}return(a.className.toUpperCase?.()?.includes("CRUMB")||a.id.toUpperCase?.()?.includes("CRUMB"))&&a.offsetHeight<=50|| 
w0(a)?!1:b.hg?!a.classList?.contains("google-anno-skip")&&!a.classList?.contains("adsbygoogle"):!0}function w0(a){return u0.some(b=>a.className.toUpperCase?.()?.includes(b)||a.id.toUpperCase?.()?.includes(b))};function x0(a,b,c){b=b.getBoundingClientRect();a=Wo(Vo(new Xo,a),3);c=fi(a,4,c);c=bi(c,6,Math.round(b.x));return bi(c,7,Math.round(b.y))}function y0(a){a=u_(a);var b=new To;b=bi(b,1,a[0]);b=bi(b,2,a[1]);b=bi(b,3,a[2]);return ph(b,4,If(a[3]),0)};const z0=/[\s!'",:;\\(\\)\\?\\.\u00bf\u00a1\u30a0\uff1d\u037e\u061f\u3002\uff1f\uff1b\uff1a\u2014\u2014\uff5e\u300a\u300b\u3008\u3009\uff08\uff09\u300c\u300d\u3001\u00b7\u2026\u2025\uff01\uff0c\u00b7\u2019\u060c\u061b\u060d\u06d4\u0648]/;function A0(a,b){switch(b){case 1:return!0;default:return a===""||z0.test(a)}};function B0(a,b){const c=new C0(b);for(const d of a)C(d,5)&&Uh(d,3).forEach(e=>{D0(c,e,e)});E0(c);return new F0(c)}function G0(a,b){b=a.match(b);a=new Map;for(const c of b)if(b=c.j,a.has(b)){const d=a.get(b);c.length>d.length&&a.set(b,c)}else a.set(b,c);return[...a.values()]}var F0=class{constructor(a){this.g=a}isEmpty(){return this.g.isEmpty()}match(a){return this.g.match(a)}}; 
function D0(a,b,c){const d=a.i.has(c)?a.i.get(c):a.l++;a.i.set(c,d);a.C.set(d,c);c=0;for(let e=0;e<b.length;e++){const f=b.charCodeAt(e);a.g[c].contains(f)||(a.g.push(new H0),a.g[a.size].C=c,a.g[a.size].H=f,a.g[c].j.set(f,a.size),a.size++);c=a.g[c].j.get(f)}a.g[c].l=!0;a.g[c].B=d;a.g[c].F=a.j.length;a.j.push(b.length)} 
function E0(a){const b=[];for(b.push(0);b.length>0;){const f=b.shift();var c=a,d=c.g[f];if(f===0)d.g=0,d.i=0;else if(d.C===0)d.g=0,d.i=d.l?f:c.g[c.g[f].g].i;else{d=c.g[c.g[f].C].g;for(var e=c.g[f].H;;){if(c.g[d].contains(e)){c.g[f].g=c.g[d].j.get(e);break}if(d===0){c.g[f].g=0;break}d=c.g[d].g}c.g[f].i=c.g[f].l?f:c.g[c.g[f].g].i}for(const g of a.g[f].bb)b.push(g)}} 
class C0{constructor(a){this.B=a;this.size=1;this.g=[new H0];this.j=[];this.i=new Map;this.C=new Map;this.l=0}isEmpty(){return this.l===0}match(a){let b=0;const c=[];for(let g=0;g<a.length;g++){for(;;){var d=a.charCodeAt(g),e=this.g[b];if(e.contains(d)){b=e.j.get(d);break}if(b===0)break;b=e.g}let h=b;for(;;){h=this.g[h].i;if(h===0)break;const k=g+1-this.j[this.g[h].F],l=g;d=a;e=l;var f=this.B;A0(d.charAt(k-1),f)&&A0(d.charAt(e+1),f)&&c.push(new I0(k,l,this.C.get(this.g[h].B)));h=this.g[h].g}}return c}} 
class H0{constructor(){this.j=new Map;this.O=!1;this.Ha=this.K=this.G=this.qa=this.X=this.aa=-1}contains(a){return this.j.has(a)}set C(a){this.aa=a}get C(){return this.aa}set H(a){this.X=a}get H(){return this.X}set l(a){this.O=a}get l(){return this.O}set B(a){this.K=a}get B(){return this.K}set g(a){this.qa=a}get g(){return this.qa}set i(a){this.G=a}get i(){return this.G}set F(a){this.Ha=a}get F(){return this.Ha}get bb(){return this.j.values()}} 
var I0=class{constructor(a,b,c){this.i=a;this.g=b;this.C=c}get j(){return this.i}get l(){return this.g}get na(){return this.C}get length(){return this.g-this.i}};async function J0(a,b,c,d,e,f){const g=B0(XZ(b.P),b.j);if(!g.isEmpty()){var h=new Map;for(const k of XZ(b.P).filter(l=>C(l,5)))Uh(k,3).forEach(l=>{h.set(l,C(k,1))});await K0(a,a.document.body,b,g,h,new Set,c,d,b.J.he?new q0(0,0,0,b.J.he):null,b.J.ke?new L0(b.J.ke):null,e,f)}} 
async function K0(a,b,c,d,e,f,g,h,k,l,m,n){g.g.la(9)>=g.i&&await p_(g,10);if(b.nodeType!==Node.ELEMENT_NODE||!b.classList?.contains("google-anno-skip")&&v0(b,c.J))if(c.J.Hf&&f.size&&b.nodeType===Node.ELEMENT_NODE&&M0(a,b)&&b.parentElement&&!N0(a,c,b.parentElement)&&O0(a,l,b.getBoundingClientRect().top)&&P0(a,f,c,h,b.parentElement,b,k,l,n),b.nodeType===Node.TEXT_NODE&&b.textContent)G0(d,b.textContent).map(p=>e.get(p.na)).filter(p=>!!p).forEach(p=>void f.add(p));else{for(const p of b.childNodes)await K0(a, 
p,c,d,e,f,g,h,k,l,m,n);f.size&&b.nodeType===Node.ELEMENT_NODE&&["block","table-cell"].includes(a.getComputedStyle(b).display)&&!N0(a,c,b)&&O0(a,l,b.getBoundingClientRect().bottom)&&P0(a,f,c,h,b,null,k,l,n)}} 
function P0(a,b,c,d,e,f,g,h,k){for(const [m,n]of[...b].entries()){var l=m;const p=n;if(g&&o0(g)||c.J.nc&&l===c.J.nc)return;c.J.nc&&b.delete(p);const t=x0(c.U.Dd(),f??e,p);d.entries.push(Vg(t));g&&s0(g.i,p,g.g);if(A(c.P,17))continue;l=t0(a,c,p,e);const z=Q0(l,c,og(Yg(t,10))??"0");I_(l);c0(c,999,l,w=>{try{var B=dp(bp(p),og(Yg(t,10))??"0");var I=ci(B,7,z.i);const T=c.U.Dc(I);k?f0(k,a,c,T,p,c.l.get(p)||"",3):T_(a,c,T,p,c.l.get(p)||"",3);return!1}finally{w.preventDefault(),w.stopImmediatePropagation()}}); 
e.insertBefore(l,f);h&&R0(h,l.getBoundingClientRect().bottom+window.scrollY)}c.J.nc||b.clear()}function M0(a,b){return["BR","IMG","TABLE"].includes(b.tagName)||a.getComputedStyle(b).display==="block"}function N0(a,b,c){if(!b.J.de)return!1;a=Dd(a.getComputedStyle(c).fontSize);return a!==null&&a>b.J.de}function O0(a,b,c){return b?b.g===void 0||c+a.scrollY-b.g>b.i:!0}class S0{constructor(){this.g=null}get i(){return this.g}} 
function Q0(a,b,c){const d=new S0;T0(b,e=>{for(const k of e)if(k.isIntersecting){var f=c;e=b.U;var g=e.ff,h=new Co;f=ph(h,1,ng(f),"0");d.g=g.call(e,f)}else d.g&&(e=b.U,g=e.ef,f=new Bo,f=di(f,1,d.g),g.call(e,f),d.g=null)}).observe(a);return d}function R0(a,b){a.g=b}class L0{constructor(a){this.i=a;this.g=void 0}};function U0(a,b,c,d,e,f,g){if(!a.g){var h=b.document.createElement("span");h.appendChild(W_(b,"12px",f));h.appendChild(b.document.createTextNode(d));YG(b,c||null,{informationText:h},e,g?k=>{g.sf(k)}:g);a.g=!0}}var V0=class{constructor(){this.g=!1}};function f0(a,b,c,d,e,f,g,h=null){K_(b.document.body.clientWidth);e=c.ea?W0(a,b,c,e,f,h,g):X0(a,b,c,e,f,h,g);Yr(e.isVisible(),!1,()=>{a.g=!1;L_(a.i)});e.show({tf:!0});a.g=!0;const k=new R_(b,c,d);P_(k);a.Da(()=>{var l=c.U,m=l.nd;var n=lp(kp(new np,d));m.call(l,n);k.g=!0})} 
function W0(a,b,c,d,e,f,g){a=c.Xb.Sb({A:b,na:d,qd:e,J:c.J,ea:c.ea,W:c.W(),P:c.P,Qc:f,xb:h_(c.ea,b,c.J.qc),Ia:c.Ia.bind(c),Ra:c.Ra.bind(c),Da:a.Da,format:g});return fF(b,a,{dg:.95,yf:.95,zIndex:2147483647,Ab:!0,Kc:"adpub-drawer-root",Jc:!1,Ma:!0,Oc:new O(c_(c.P,d))})} 
function X0(a,b,c,d,e,f,g){const h=h_(c.ea,b,c.J.qc);a=c.Xb.Sb({A:b,na:d,qd:e,J:c.J,ea:c.ea,W:c.W(),P:c.P,Qc:f,xb:h,Ia:c.Ia.bind(c),Ra:c.Ra.bind(c),Da:a.Da,format:g});return oE(b,a,{Vc:`${h}px`,Tc:c.W(),Ec:C(c.P,14),zIndex:2147483647,Ab:!0,uf:!0,Kc:"adpub-drawer-root",Jc:!1,Ma:!0,Oc:new O(c_(c.P,d))})}var Y0=class{constructor(){this.g=!1;this.i=new M_}Da(a){this.i.g.push(a)}};const Z0=["block","inline","inline-block","list-item","table-cell"];async function $0(a,b,c,d,e){d.g.la(5)>=d.i&&await p_(d,6);const f=c.J.ig?new Y0:void 0;c.J.pf||a1(a,b,c,e,XZ(c.P),f);c.J.qf&&!b1(a)||await c.Pa(898,J0(a,c,d,e,b,f));c.J.rf||await c1(a,c,()=>new V0,d,e,f)}function b1(a){try{const b=a.location?.href?.match(/goog_fac=1/);return b!==null&&b!==void 0}catch(b){return!1}} 
async function c1(a,b,c,d,e,f){var g=XZ(b.P);var h=new C0(b.j);for(const k of g)C(k,6)!==""&&(g=C(k,1),D0(h,g,g));E0(h);h=new F0(h);h.isEmpty()||await b.Pa(898,d1(a,b,d,e,h,new q0(b.params.Yk,b.params.mg,b.params.Ze,b.params.ge),c(),f))} 
async function d1(a,b,c,d,e,f,g,h){let k=a.document.body;if(A(b.P,17)||v(b.P,St,21))for(;k;){c.g.la(7)>=c.i&&await p_(c,8);if(k.nodeType===Node.TEXT_NODE&&k.textContent!==""&&k.parentElement){const Tc=k.parentElement;a:{var l=a,m=b,n=Tc,p=k.textContent,t=d,z=e,w=f,B=h;const rc=[];b:{var I=p;switch(m.j){case 1:var T=I;const Uc=Array(T.length);let Zb=0;for(let sc=0;sc<T.length;sc++)z0.test(T[sc])||Zb++,Uc[sc]=Zb;var R=Uc;break b;default:var U=I;const od=Array(U.length);let Kd=0,wb=0;for(;wb<U.length;){for(;/\s/.test(U[wb]);)od[wb]= 
Kd,wb++;let sc=!1;for(;wb<U.length&&!/\s/.test(U[wb]);)sc=!0,od[wb]=Kd,wb++;sc&&(Kd++,od[wb-1]=Kd)}R=od}}const Yb=R,Cp=p.includes("\u00bb")?[]:G0(z,p);let Xf=-1;for(const Uc of Cp){const Zb=Uc.j,od=Uc.l;if(Zb<Xf)continue;var X=w,$a=Uc.na;r0(X.i,X.g+Yb[Zb]);var Pb=X,ta=Pb.i,Fa=$a;if(!((ta.g.has(Fa)?ta.g.get(Fa).length:0)<Pb.mg&&X.i.j<X.Ze))continue;const Kd=l.getComputedStyle(n),wb=Kd.fontSize.match(/\d+/);if(!(wb&&Number(wb[0])>=12&&Number(wb[0])<=22&&Qa(Z0,Kd.display))){w.g+=Yb[Yb.length-1];var nd= 
[];break a}const sc=Xf+1;sc<Zb&&rc.push(l.document.createTextNode(p.substring(sc,Zb)));const Hk=p.substring(Zb,od+1);var Rc=p,Sc=Zb,le=od+1;const Ik=Rc.substring(Math.max(Sc-30,0),Sc)+"~~"+Rc.substring(le,Math.min(le+30,Rc.length));var pa=l,Id=m.U.Dd(),Wf=n,kk=Hk,lk=Ik,mk=Uc.na,nk=Yb[Zb];const Jk=Wf.getBoundingClientRect();var ok=Wo(Vo(new Xo,Id),2);var pk=fi(ok,2,kk);var qk=fi(pk,3,lk);var rk=fi(qk,4,mk);var sk=bi(rk,5,nk);var tk=bi(sk,6,Math.round(Jk.x));var uk=bi(tk,7,Math.round(Jk.y));const pd= 
pa.getComputedStyle(Wf);var vk=new Uo;var wk=fi(vk,1,pd.fontFamily);var xk=y0(pd.color);var yk=x(wk,7,xk);var zk=y0(pd.backgroundColor);var Ak=x(yk,8,zk);const Fh=pd.fontSize.match(/^(\d+(\.\d+)?)px$/);var Ch=bi(Ak,4,Fh?Math.round(Number(Fh[1])):0);const Yf=Math.round(Number(pd.fontWeight));isNaN(Yf)||Yf===400||bi(Ch,5,Yf);pd.textDecorationLine!=="none"&&fi(Ch,6,pd.textDecorationLine);var up=x(uk,8,Ch);const Zf=[];let Ue=Wf;for(;Ue&&Zf.length<20;){var Ck=Zf,vp=Ck.push,Dh=Ue,wp=new So;const Hh=fi(wp, 
1,Dh.tagName);Dh.className!==""&&oh(Hh,2,Dh.className.split(" "),rg);vp.call(Ck,Hh);if(Ue.tagName==="BODY")break;Ue=Ue.parentElement}var xp=Zf.reverse();const Lk=Kh(up,9,xp);t.entries.push(Vg(Lk));rc.push(e1(l,m,og(Yg(Lk,10))??"0",Uc.na,Hk,n,B));s0(w.i,Uc.na,w.g+Yb[Zb]);Xf=od;if(o0(w))break}const Eh=Xf+1;Eh!==0&&Eh<p.length&&rc.push(l.document.createTextNode(p.substring(Eh)));w.g+=Yb[Yb.length-1];nd=rc}const Jd=nd;if(Jd.length&&!A(b.P,17)){!b.J.Mb&&U0(g,a,b.params.Vg?o_(a,b.params.Vg):void 0,C(b.P, 
3),Wg(v(b.P,St,21)),b.params.sh,b.U);for(const rc of Jd)Tc.insertBefore(rc,k),f1(rc);Tc.removeChild(k);for(k=Jd[Jd.length-1];k.lastChild;)k=k.lastChild;if(o0(f))break}}a:{var yp=a,qc=k,zp=f,Ap=b.j,Bp=b.J;if(qc.firstChild&&(qc.nodeType!==Node.ELEMENT_NODE?0:!qc.classList?.contains("google-anno-skip")&&(qc.offsetHeight||yp.getComputedStyle(qc).display==="contents"))){if(v0(qc,Bp)){k=qc.firstChild;break a}if(qc.textContent?.length){var Dk=zp;b:{var Ek=qc.textContent;switch(Ap){case 1:var Fk=Ek;let Jd= 
0;for(let Yb=Fk.length-1;Yb>=0;Yb--)z0.test(Fk[Yb])||Jd++;var Gk=Jd;break b;default:const rc=Ek.trim();Gk=rc===""?0:rc.split(/\s+/).length}}r0(Dk.i,Dk.g+Gk)}}let Tc=qc;for(;;){if(Tc.nextSibling){k=Tc.nextSibling;break a}if(!Tc.parentNode){k=null;break a}Tc=Tc.parentNode}k=void 0}}} 
function g1(a,b){b={W:b.W(),ea:b.ea,V:hr(a),Z:ir(a)};if(b.Z>=400){var c;if((c=x_(a,b))!=null)var d=c;else a:{c=b.V;var e=F_(a,b);a=16;for(d of e){e=d.start;const f=d.end;if(e>a){if(e-a-16>=200){d=D_(b,e,a);break a}a=f+16}else f>=a&&(a=f+16)}d=c-a-16>=200?D_(b,c,a):null}}else d=null;return d} 
function a1(a,b,c,d,e,f){function g(){return k??(k=c.Ra(898,a,()=>{if(!h){var m=c.la(12);a.clearInterval(k);h=!0;var n=g1(a,c);n&&h1(a,b,c,d,m,e,n,f)}},c.J.Re))}if(e.filter(m=>C(m,7).length).length){var h=!1,k=void 0,l=i1(c,a,()=>{if(!(a.scrollY<=c.J.Se||h)){var m=c.la(12),n=g1(a,c);n?(h=!0,a.removeEventListener("scroll",l),h1(a,b,c,d,m,e,n,f)):k=g()}});Q_(c,898,a,()=>{if(!h){var m=c.la(12),n=g1(a,c);n?(h=!0,h1(a,b,c,d,m,e,n,f)):k=g()}},c.J.Qe)}} 
function h1(a,b,c,d,e,f,g,h){const k=new m0(a,c,g,h);f.filter(l=>C(l,7).length).forEach(l=>{var m=c.U.Dd();var n=C(l,1);m=Wo(Vo(new Xo,m),1);n=fi(m,4,n);d.entries.push(Vg(n));n=og(Yg(n,10))??"0";m=C(l,1);l=C(l,1);k.j.push(new n0(n,m,l));k.l&&l0(k,b)});c.U.Mf(j1(d,c.la(13)-e))} 
function f1(a){if(a.nodeType===Node.ELEMENT_NODE){if(a.tagName==="A"){var b=w_(u_(getComputedStyle(a.parentElement).color)),c=w_(u_(getComputedStyle(a).color));var d=v_(a);if(d=b&&c&&d?TP(c,d)<Math.min(TP(b,d),2.5)?b:null:b){b=d[0];c=d[1];d=d[2];b=Number(b);c=Number(c);d=Number(d);if(b!=(b&255)||c!=(c&255)||d!=(d&255))throw Error('"('+b+","+c+","+d+'") is not a valid RGB color');c=b<<16|c<<8|d;b=b<16?"#"+(16777216|c).toString(16).slice(1):"#"+c.toString(16);r(a,{color:b})}}for(b=0;b<a.childElementCount;b++)f1(a.children[b])}} 
class k1{constructor(){this.g=null}get i(){return this.g}} 
function e1(a,b,c,d,e,f,g){const h=a.document.createElement("SPAN");h.className="google-anno-t";G_(h);r(h,{"text-decoration":"underline"});r(h,{"text-decoration-style":"dotted"});r(h,{"-webkit-text-decoration-line":"underline","-webkit-text-decoration-style":"dotted"});r(h,{color:"inherit","font-family":"inherit","font-size":"inherit","font-style":"inherit","font-weight":"inherit"});h.appendChild(a.document.createTextNode(e));e=a.document.createElement("A");G_(e);r(e,{"text-decoration":"none",fill:"currentColor"}); 
vc(e);e.className="google-anno";I_(e);e.appendChild(l1(a,b,f));e.appendChild(a.document.createTextNode("\u00a0"));e.appendChild(h);const k=m1(b,c,e);c0(b,999,e,l=>{try{var m=dp(bp(d),c);var n=ci(m,2,k.i);const p=b.U.Dc(n);g?f0(g,a,b,p,d,b.C.get(d)||"",1,b.J.Mb?b.i.get(d)||"":null):T_(a,b,p,d,b.C.get(d)||"",1,b.J.Mb?b.i.get(d)||"":null);return!1}finally{l.preventDefault(),l.stopImmediatePropagation()}});return e}function l1(a,b,c){return W_(a,a.getComputedStyle(c).fontSize,b.params.sh)} 
function m1(a,b,c){const d=new k1;T0(a,e=>{for(const k of e)if(k.isIntersecting){var f=b;e=a.U;var g=e.Pf,h=new hp;f=ph(h,2,ng(f),"0");d.g=g.call(e,f)}else d.g&&(e=a.U,g=e.Of,f=new gp,f=di(f,1,d.g),g.call(e,f),d.g=null)}).observe(c);return d};function j1(a,b){const c=a.g;a.g=a.entries.length;var d=new fp,e=new Yo;a=Kh(e,2,a.entries.slice(c));d=x(d,1,a);b!==0&&di(d,2,Math.round(b));return d}var n1=class{constructor(){this.entries=[];this.language=null;this.g=0}};function o1(a){return a?1:0};function p1(a,b,c){q1(a);var d=new Map;for(const e of b)b=r1(e),d.set(b,(d.get(b)??0)+1);for(const [e,f]of d)d=e,s1(a,f,d,c),t1(a,d)}function u1(a,b,c,d){a.i.forEach(e=>{v1(e,{...a.g,outcome:b,cd:c,zf:d})})}function w1(a,b,c,d,e){a.i.forEach(f=>{f.We(b,{...a.g,outcome:c,cd:d,zf:e})})}function q1(a){a.l||(a.l=!0,a.i.forEach(b=>{x1(b,a.g)}))}function s1(a,b,c,d){a.i.forEach(e=>{e.Ye(b,{...a.g,format:c,cd:d})})}function t1(a,b){a.B.has(b)||(a.B.add(b),a.i.forEach(c=>{y1(c,{...a.g,format:b})}))} 
function z1(a,b){a.i.forEach(c=>{A1(c,{...a.g,reason:B1(b)})})} 
var J1=class{constructor(a,b,c){this.H=this.j=1;this.C=this.l=!1;this.g={language:a.Sf.has(b)?b:"other",xa:Kb()?2:Ib()?4:Jb()?7:10};this.B=new Set;this.i=[...c]}Dd(){return this.H++}Xe(a){a:switch(uh(a,$o)){case 4:var b=1;break a;case 5:b=2;break a;default:b=0}const c=C1(a);var d=xu(Rh(a,3)),e=c.length>0;u1(this,b,!1,e);w1(this,d,b,!1,e);a.g()&&c.length>0&&p1(this,c,!1);if(ch(a,Ro,th(a,$o,5))!==void 0){a=Wh(a,Ro,5,$o);for(const f of zh(a,Mo,1,eh()))z1(this,f)}this.j++}Mf(a){const b=a.g()?1:0,c=C1(a); 
var d=xu(Rh(a,2)),e=c.length>0;u1(this,b,!0,e);w1(this,d,b,!0,e);a.g()&&c.length>0&&p1(this,c,!0);this.j++}Pf(){this.i.forEach(a=>{D1(a,{...this.g,format:2})});return this.j++}Of(){this.i.forEach(a=>{E1(a,{...this.g,format:2})});this.j++}Ue(){this.i.forEach(a=>{D1(a,{...this.g,format:1})});return this.j++}Te(){this.i.forEach(a=>{E1(a,{...this.g,format:1})});this.j++}ff(){this.i.forEach(a=>{D1(a,{...this.g,format:3})});return this.j++}ef(){this.i.forEach(a=>{E1(a,{...this.g,format:3})});this.j++}Dc(a){let b= 
0;kg(Yg(a,2))!=null?b=2:kg(Yg(a,3))!=null?b=1:kg(Yg(a,7))!=null&&(b=3);this.i.forEach(c=>{c.click({...this.g,format:b})});return this.j++}nd(a){let b=0;ch(a,jp,th(a,mp,2))!==void 0?b=1:ch(a,ip,th(a,mp,3))!==void 0&&(b=2);this.i.forEach(c=>{F1(c,{...this.g,type:b})});this.j++}sf(a){a:switch(D(a,1)){case 1:a=1;break a;case 2:a=2;break a;default:a=0}const b=a;this.i.forEach(c=>{G1(c,{...this.g,type:b})});this.C||(this.C=!0,this.i.forEach(c=>{H1(c,this.g)}));this.j++}Pe(){this.i.forEach(a=>{I1(a,this.g)}); 
this.j++}};function C1(a){a.g()?(a=a.i(),a=[...zh(a,Xo,2,eh())]):a=[];return a}function B1(a){switch(uh(a,No)){case 1:return 1;case 2:return 2;case 3:return 3;case 9:return 4;case 11:return 5;case 12:return 6;case 13:return 7;default:return 0}}function r1(a){switch(D(a,1)){case 1:return 1;case 2:return 2;case 3:return 3;default:return 0}};function K1(a,b){var c=new op;var d=a.j++;c=di(c,1,d);b=di(c,2,Math.round(a.l.la(b)-a.B));b=x(b,10,a.H);return $h(b,15,a.F?!0:void 0)} 
var L1=class{constructor(a,b,c,d,e,f,g,h){this.l=b;this.B=c;this.H=d;this.F=f;this.i=this.j=1;this.C=[...g];this.g=h.length?new J1(e,a,h):null}Dd(){return this.i++}Xe(a){this.g?.Xe(a);var b=this.handle,c=K1(this,11);a=Ah(c,3,pp,a);b.call(this,a)}Mf(a){this.g?.Mf(a);var b=this.handle,c=K1(this,11);a=Ah(c,14,pp,a);b.call(this,a)}Pf(a){this.g?.Pf(a);var b=this.handle,c=K1(this,15);a=Ah(c,4,pp,a);return b.call(this,a)}Of(a){this.g?.Of(a);var b=this.handle,c=K1(this,16);a=Ah(c,5,pp,a);b.call(this,a)}Ue(a){this.g?.Ue(a); 
var b=this.handle,c=K1(this,17);a=Ah(c,6,pp,a);return b.call(this,a)}Te(a){this.g?.Te(a);var b=this.handle,c=K1(this,18);a=Ah(c,7,pp,a);b.call(this,a)}ff(a){this.g?.ff(a);var b=this.handle,c=K1(this,19);a=Ah(c,16,pp,a);return b.call(this,a)}ef(a){this.g?.ef(a);var b=this.handle,c=K1(this,20);a=Ah(c,17,pp,a);b.call(this,a)}Dc(a){this.g?.Dc(a);var b=this.handle,c=K1(this,14);a=Ah(c,8,pp,a);return b.call(this,a)}nd(a){this.g?.nd(a);var b=this.handle,c=K1(this,21);a=Ah(c,9,pp,a);b.call(this,a)}sf(a){this.g?.sf(a); 
var b=this.handle,c=K1(this,22);a=Ah(c,11,pp,a);b.call(this,a)}Pe(a){this.g?.Pe(a);var b=this.handle,c=K1(this,24);a=Ah(c,12,pp,a);b.call(this,a)}handle(a){for(const b of this.C)b(a);return xu(Rh(a,1))}};function Q_(a,b,c,d,e){c.setTimeout(M1(a,b,d),e)}function c0(a,b,c,d){c.addEventListener("click",M1(a,b,d))}function T0(a,b){return new IntersectionObserver(M1(a,1065,b),{threshold:.98})}function i1(a,b,c){a=M1(a,898,c);b.addEventListener("scroll",a,{passive:!0});return a}function M1(a,b,c){return a.za.nb(b,c,void 0,d=>{d.es=a.J.Eb.join(",")})} 
var O1=class{constructor(a,b,c,d,e,f,g,h){this.ea=a;this.P=b;this.za=c;this.U=d;this.H=e;this.params=f;this.J=g;this.Xb=h;this.C=new Map;this.B=new Map;this.l=new Map;this.g=new Map;this.i=new Map;this.j=Qa(N1,C(b,7))?1:0;for(const k of XZ(this.P))y(k,6)!=null&&this.C.set(C(k,1),C(k,6)),y(k,7)!=null&&this.B.set(C(k,1),C(k,7)),y(k,5)!=null&&this.l.set(C(k,1),C(k,5)),Ph(k,10)!=null&&this.g.set(C(k,1),D(k,10)),y(k,11)!=null&&this.i.set(C(k,1),C(k,11))}Ia(a,b,c){a=M1(this,a,c);b.addEventListener("message", 
a);return a}Ra(a,b,c,d){return b.setInterval(M1(this,a,c),d)}Pa(a,b){this.za.Pa(a,b,c=>{c.es=this.J.Eb.join(",")});return b}la(a){return this.H.la(a)}W(){return D(this.P,12)===2}};const N1=["ja","zh_CN","zh_TW"];const P1=new Map([[1,1],[2,2]]); 
async function Q1(a,b,c,d,e,f,g,h){var k=sB;h=a?(h=TQ(new XQ(a),"__gads",h))?xd(h+"t2Z7mVic")%20:null:null;var l=h??Math.floor(ud()*20),m=f.la(0),n=!!a&&hr(a)<488;h=c.P;var p;p=(p=C(h,7))?(p=p.match(/^[a-z]{2,3}/i))?p[0].toLowerCase():"":"";var t=c.J,z=new ep;l=bi(z,2,l);l=Lh(l,3,Uf,t.Eb,kg,void 0,!0);d=new L1(p,f,m,l,t,A(h,17),d,e);e=new O1(n,h,k,d,f,c.params,c.J,c.Xb);k=new n1;k.language=p;g=await R1(a,b,e,g,k);b=d.Xe;e=c.ib;a=a?.location?.hostname||"";c=c.yj;f=f.la(11)-m;m=new ap;p=new Go;e=fi(p, 
1,e);a=fi(e,2,a);n=E(a,3,n);n=x(m,1,n);a=new Ho;a=fi(a,2,k.language);c=fi(a,3,c);n=x(n,2,c);n=di(n,3,Math.round(f));e=XZ(h);h=f=c=a=m=0;for(w of e)m+=o1(C(w,6)!=="")+o1(C(w,7)!=="")+o1(C(w,5)!==""),a+=o1(C(w,6)!=="")+o1(C(w,7)!=="")+o1(C(w,5)!==""),c+=o1(C(w,6)!==""),f+=o1(C(w,7)!==""),h+=o1(C(w,5)!=="");var w=new Zo;w=ai(w,1,e.length);w=ai(w,2,m);w=$g(w,3,a==null?a:Sf(a));w=$g(w,4,c==null?c:Sf(c));w=$g(w,5,f==null?f:Sf(f));w=ai(w,6,h);w=x(n,6,w);if(g.length){var B=new Ro;B=Kh(B,1,g);Ah(w,5,$o,B)}else{k.g= 
k.entries.length;h=new Yo;f=k.entries;k=h.T[u]|0;Ye(k);k=yh(h,k,Xo,2,2,!0);n=g=0;if(Array.isArray(f))for(B=f.length,c=0;c<B;c++)a=f[c],k.push(a),(a=!!((a.T[u]|0)&2))&&!g++&&Oe(k,8),a||n++||Oe(k,16);else for(B of f)f=B,k.push(f),(f=!!((f.T[u]|0)&2))&&!g++&&Oe(k,8),f||n++||Oe(k,16);Ah(w,4,$o,h)}b.call(d,w)} 
async function R1(a,b,c,d,e){if(!a)return[Oo()];var f=a.document.body;if(!f||!S1(f))return[Lo()];d.g.la(3)>=d.i&&await p_(d,4);f=[];(c.J.ne&&hr(a)<c.J.ne||c.J.me&&ir(a)<c.J.me)&&f.push(Lo());if(Vh(c.P,1).length){const g=Vh(c.P,1).map(h=>P1.get(h)??0);f.push(Qo(new Mo,Io(g)))}zd()&&f.push(Po());f.length||await $0(a,b,c,d,e);return f}function S1(a){try{(new ResizeObserver(()=>{})).disconnect()}catch{return!1}return a.classList&&a.classList.contains!==void 0&&a.attachShadow!==void 0};async function T1(a,b,c,d,e,f,g){const h=a.performance?.now?new r_(a.performance):new s_,k=new q_(a,h);if(typeof e!=="string")throw Error(`Invalid config string ${e}`);e=b_(e);var l=xh(e,YZ,1),m=c.google_ad_client;if(typeof m!=="string")throw new pB(`Invalid property code ${m}`);c=c.google_page_url;c=typeof c==="string"?c:"";m=D(e,4)===2?M(hw)?new n_(m,M(Vv)?GQ(a):null,Zd(a),c):new i_(m,M(Vv)?GQ(a):null,Zd(a),c):new g_(m);a=L(XH);l=U1(l);a:{try{const t=b?.location?.hash?.match(/\bgoog_cpmi=([^&]*)/); 
if(!t){var n=null;break a}var p=decodeURIComponent(t[1]);n=ZZ(p);break a}catch(t){n=null;break a}n=void 0}n=n||xh(e,YZ,1);p=yh(e,e.T[u]|0,Zt,3,1);p={Yk:V(uw),mg:V(zw),Ze:V(xw),ge:V(yw),Vg:p,xg:V(Cw),sh:2};g={P:n,ib:c,yj:g,params:p,J:new d_({Eb:l,pf:M(Mv),rf:M(Ov),ne:V(Bw),me:V(Aw),Re:V(sw),Oe:kx(Pv),Rb:kx(Qv),Qe:V(rw),Se:V(tw),fe:V(ww),qf:M(Nv),qc:V(aw),Mb:D(e,4)===2,Pd:lx(Tv),og:kx(Sv),Qd:M(Uv),Hf:M(Xv),he:V($v),we:M(mw),de:V(cw),Hh:M(iw),Vf:M(fw),ke:V(ew),nc:V(bw),Sf:L(jx).g(dw.g,dw.defaultValue), 
Ce:M(nw),ig:M(kw),hg:M(jw)}),Xb:m};await V1(b,d,a,g,h,k,f)}function U1(a){const b=L(Sq).g();a&&b.push(...Th(a,24));b.push(...lx(ow).map(Number));b.push(42);b.sort((c,d)=>c-d);return b}async function V1(a,b,c,d,e,f,g){if(a){const h=BD(a);if(h.wasReactiveAdConfigReceived[42])return;h.wasReactiveAdConfigReceived[42]=!0}await Q1(a,b,d,[h=>{sB.Pa(1214,aI(c,h,e.la(23)),k=>{k.es=U1(d.P)})}],[new W1(c,d.P)],e,f,g)}function x1(a,b){X1(a,c=>c.si,{ga:1,...b})}function y1(a,b){X1(a,c=>c.sj,{ga:1,...b})} 
function v1(a,b){X1(a,c=>c.ti,{ga:1,...b})}function A1(a,b){X1(a,c=>c.ui,{ga:1,...b})}function D1(a,b){X1(a,c=>c.wi,{ga:1,...b})}function E1(a,b){X1(a,c=>c.vi,{ga:1,...b})}function F1(a,b){X1(a,c=>c.uk,{ga:1,...b})}function G1(a,b){X1(a,c=>c.bj,{ga:1,...b})}function H1(a,b){X1(a,c=>c.aj,{ga:1,...b})}function I1(a,b){X1(a,c=>c.ri,{ga:1,...b})}function X1(a,b,c){a.g&&a.za.Pa(1214,bI(a.g,b,c),d=>{d.es=U1(a.i)})}function Y1(a,b,c){a.g&&a.za.Pa(1214,cI(a.g,b,c),d=>{d.es=U1(a.i)})} 
class W1{constructor(a,b){var c=sB;this.g=a;this.za=c;this.i=b}We(a,b){Y1(this,c=>c.We,{ud:a,...b})}Ye(a,b){X1(this,c=>c.Ye,{ga:a,...b})}click(a){X1(this,b=>b.Oi,{ga:1,...a})}};function Z1(a,b){const c=sd("STYLE",a);c.textContent=Hc(Xc`* { pointer-events: none; }`);a?.head.appendChild(c);setTimeout(()=>{a?.head.removeChild(c)},b)}function $1(a,b,c){if(!a.body)return null;const d=new a2;d.apply(a,b);return()=>{var e=c||0;e>0&&Z1(b.document,e);Rl(a.body,{filter:d.g,webkitFilter:d.g,overflow:d.j,position:d.l,top:d.C});b.scrollTo(0,d.i)}} 
class a2{constructor(){this.g=this.C=this.l=this.j=null;this.i=0}apply(a,b){this.j=a.body.style.overflow;this.l=a.body.style.position;this.C=a.body.style.top;this.g=a.body.style.filter?a.body.style.filter:a.body.style.webkitFilter;this.i=rr(b);Rl(a.body,"top",`${-this.i}px`)}};function b2(a,b){var c;if(!a.j)for(a.j=[],c=a.g.parentElement;c;){a.j.push(c);if(a.K(c))break;c=c.parentNode&&c.parentNode.nodeType===1?c.parentNode:null}c=a.j.slice();let d,e;for(d=0;d<c.length;++d)(e=c[d])&&b.call(a,e,d,c)}var c2=class extends N{constructor(a,b,c){super();this.g=a;this.X=b;this.F=c;this.j=null;Mr(this,()=>this.j=null)}K(a){return this.F===a}};function d2(a,b){const c=a.F;c&&(b?(KD(a.G),r(c,{display:"block"}),a.B.body&&!a.l&&(a.l=$1(a.B,a.X,a.aa)),c.setAttribute("tabindex","0"),c.setAttribute("aria-hidden","false"),a.B.body.setAttribute("aria-hidden","true")):(LD(a.G),r(c,{display:"none"}),a.l&&(a.l(),a.l=null),a.B.body.setAttribute("aria-hidden","false"),c.setAttribute("aria-hidden","true")))} 
function e2(a){d2(a,!1);const b=a.F;if(b){var c=f2(a.O);b2(a,d=>{r(d,c);vr(d)});a.g.setAttribute("width","");a.g.setAttribute("height","");Rl(a.g,c);Rl(a.g,g2);Rl(b,h2);Rl(b,{background:"transparent"});r(b,{display:"none",position:"fixed"});vr(b);vr(a.g);(Kb()&&Lb()?ce(a.O):1)<=1||(Rl(b,{overflow:"scroll","max-width":"100vw"}),Qd(b))}}class i2 extends c2{constructor(a,b,c){var d=V(Yw);super(a,b,c);this.l=null;this.B=b.document;this.aa=d;this.G=ED(new JD(b),2147483646);this.O=b}} 
function f2(a){a=Kb()&&Lb()?ce(a):1;a=100*(a<1?1:a);return{width:`${a}vw`,height:`${a}vh`}}var h2={backgroundColor:"white",opacity:"1",position:"fixed",left:"0px",top:"0px",margin:"0px",padding:"0px",display:"none",zIndex:"2147483647"},g2={left:"0",position:"absolute",top:"0"};var j2=class extends i2{constructor(a,b,c){super(b,a,c);e2(this)}K(a){return a.classList?a.classList.contains("adsbygoogle"):Qa(a.classList?a.classList:(typeof a.className=="string"?a.className:a.getAttribute&&a.getAttribute("class")||"").match(/\S+/g)||[],"adsbygoogle")}};const k2={[1]:"closed",[2]:"viewed",[3]:"dismissed"};async function l2(a,b,c,d,e){a=new m2(a,b,c,d,e);await a.M();return a}function n2(a){return setTimeout(wB(728,()=>{o2(()=>{a.B.reject()});a.dispose()}),V(Qw)*1E3)}function p2(a,b){var c=$P(a.g).then(()=>{clearTimeout(b);a.B.resolve()});xB(1005,c);c=aQ(a.g).then(d=>{q2(a,k2[d.status],d.payload)});xB(1006,c);c=bQ(a.g).then(()=>{q2(a,"error")});xB(1004,c)} 
function r2(a){if(M(Rw)){a.A.location.hash!==""&&yB("pub_hash",{o_url:a.A.location.href},.1);a.A.location.hash="goog_fullscreen_ad";var b=wB(950,c=>{c.oldURL.endsWith("#goog_fullscreen_ad")&&(a.j===10?(q2(a,"closed"),a.A.removeEventListener("hashchange",b)):(a.A.location.hash="goog_fullscreen_ad",WP(a.g.jf,"fullscreen",{eventType:"backButton"},"*")))});a.A.addEventListener("hashchange",b);Mr(a,()=>{a.A.removeEventListener("hashchange",b);a.A.location.hash==="#goog_fullscreen_ad"&&a.A.history.back()})}} 
function o2(a){try{a()}catch(b){}}function q2(a,b,c){d2(a.G,!1);a.l&&(c&&b==="viewed"?o2(()=>{a.l({status:b,reward:c})}):o2(()=>{a.l({status:b})}));a.j===11&&yB("fs_ad",{tgorigin:a.D.google_tag_origin,client:a.D.google_ad_client,url:a.D.google_page_url??"",slot:a.D.google_ad_slot??"0",ratype:a.j,clostat:b},1);a.dispose()} 
var m2=class extends N{constructor(a,b,c,d,e){super();this.A=a;this.F=b;this.K=c;this.j=d;this.D=e;this.l=null;this.G=new j2(a,c,b);a=new dQ(this.j===10?1:2,this.A,this.K.contentWindow);a.M();this.g=a;this.B=new ZP;this.F.dataset["slotcar"+(this.j===10?"Interstitial":"Rewarded")]="true"}async M(){const a=n2(this);p2(this,a);Mr(this,()=>{this.g.dispose();clearTimeout(a);Il(this.F)});await this.B.promise}show(a){this.C||(this.l=a,d2(this.G,!0),q.IntersectionObserver||WP(this.g.jf,"fullscreen",{eventType:"visible"}, 
"*"),r2(this))}disposeAd(){this.dispose()}};function s2({Pg:a,Qh:b}){return a||(b==="dev"?"dev":"")};function t2(a){sB.j(b=>{b.shv=String(a);b.mjsv=s2({Pg:cq(),Qh:a});b.eid=$Q(q)})}function u2(a,b){const c=b?.g();b=c?.g()||C(a,2);a=c?.i()?A(c,4):A(a,6);t2(b);bf(FQ,hf);FQ=a};var v2=class extends G{i(){return A(this,1)}g(){return v(this,$t,2)}};var w2=class extends G{},x2=[27,28];var y2=typeof sttc==="undefined"?void 0:sttc;function z2(a){var b=sB;try{if(bf(a,gf),a.length>0)return new w2(JSON.parse(a))}catch(c){b.ma(838,c instanceof Error?c:Error(String(c)))}return new w2};function A2(a,b,c,d){const e=new ZP;let f="";const g=k=>{try{const l=typeof k.data==="object"?k.data:JSON.parse(k.data);f===l.paw_id&&(rb(a,"message",g),l.error?e.reject(Error(l.error)):e.resolve(d(l)))}catch(l){}};var h=typeof a.gmaSdk?.getQueryInfo==="function"?a.gmaSdk:void 0;if(h)return qb(a,"message",g),f=c(h),e.promise;c=typeof a.webkit?.messageHandlers?.getGmaQueryInfo?.postMessage==="function"||typeof a.webkit?.messageHandlers?.getGmaSig?.postMessage==="function"?a.webkit.messageHandlers: 
void 0;return c?(f=String(Math.floor(ud()*2147483647)),qb(a,"message",g),b(c,f),e.promise):null}function B2(a){return A2(a,(b,c)=>void(b.getGmaQueryInfo??b.getGmaSig)?.postMessage(c),b=>b.getQueryInfo(),b=>b.signal)}(function(a){return df(b=>{if(!lf(b))return!1;for(const [c,d]of Object.entries(a)){const e=c,f=d;if(!(e in b)){if(f.Pj===!0)continue;return!1}if(!f(b[e]))return!1}return!0})})({vc:gf,pn:gf,eid:mf(),vnm:mf(),js:gf},"RawGmaSdkStaticSignalObject");const C2=(a,b)=>{try{const l=A(b,6)===void 0?!0:A(b,6);var c=fk(D(b,2)),d=C(b,3);a:switch(D(b,4)){case 1:var e="pt";break a;case 2:e="cr";break a;default:e=""}var f=new Tk(c,d,e),g=v(b,ak,5)?.g()??"";f.dd=g;f.g=l;var h=!!A(b,7);f.Za=h;f.A=a;var k=f.build();Yj(k)}catch{}};function D2(a,b){a.goog_sdr_l||(Object.defineProperty(a,"goog_sdr_l",{value:!0}),a.document.readyState==="complete"?C2(a,b):qb(a,"load",()=>void C2(a,b)))};function E2(a){const b=RegExp("^https?://[^/#?]+/?$");return!!a&&!b.test(a)} 
function F2(a){if(a===a.top||kd(a.top))return Promise.resolve({status:4});a:{try{var b=(a.top?.frames??{}).google_ads_top_frame;break a}catch(d){}b=null}if(!b)return Promise.resolve({status:2});if(a.parent===a.top&&E2(a.document.referrer))return Promise.resolve({status:3});const c=new ZP;a=new MessageChannel;a.port1.onmessage=d=>{d.data.msgType==="__goog_top_url_resp"&&c.resolve({Rc:d.data.topUrl,status:d.data.topUrl?0:1})};b.postMessage({msgType:"__goog_top_url_req"},"*",[a.port2]);return c.promise} 
;let G2=null;function HH(){return navigator.cookieDeprecationLabel?Promise.race([navigator.cookieDeprecationLabel.getValue().then(a=>({status:1,label:a})).catch(()=>({status:2})),ae(V(Ow),{status:5})]):Promise.resolve({status:3})}function H2(a){a=a.innerInsElement;if(!a)throw Error("no_wrapper_element_in_loader_provided_slot");return a} 
async function I2({sa:a,Ka:b,jb:c,slot:d,pageState:e}){const f=d.vars,g=qd(d.pubWin);var h=H2(d);const k=new BU({L:g,pubWin:d.pubWin,D:f,sa:a,Ka:b,jb:c,ba:h,pageState:e});k.K=Date.now();kl(1,[k.D]);vB(1032,()=>{if(g&&M(gx)){var m=k.D;zH(uH(),32,!1)||(AH(uH(),32,!0),qZ(g,m.google_loader_used==="sd"?5:9))}});try{await J2(k)}catch(m){if(!zB(159,m))throw m;}vB(639,()=>{var m;var n=k.D;(m=k.L)&&n.google_responsive_auto_format===1&&n.google_full_width_responsive_allowed===!0?(n=(n=m.document.getElementById(n.google_async_iframe_id))? 
Nl(n,"INS","adsbygoogle"):null)?((new AU(m,n)).run(),m=!0):m=!1:m=!1;return m});if(g?.location?.hash?.match(/\bgoog_cpmi=([^&]*)/)){b=d.pubWin;c=k.i;h=$Z();try{var l=!!g?.location?.hash?.match(/\bgoog_aidd/)}catch(m){l=!1}xB(1008,K2(b,g,f,c,ii(gi(h,4,l?2:1)),k.j,C(e.g(),8)||C(a,8)),m=>{m.es=U1(null)})}else VP(k.pubWin,"affa",m=>{xB(1008,K2(d.pubWin,g,f,k.i,m.config,k.j,C(e.g(),8)||C(a,8)),n=>{n.es=U1(null)});return!0});L2(k);return k}async function K2(a,b,c,d,e,f,g){await T1(a,b,c,d,e,f,g)} 
function J2(a){if(/_sdo/.test(a.D.google_ad_format))return Promise.resolve();var b=a.pubWin;YQ(13,b);YQ(11,b);a.H=Wh(a.sa,v2,28,x2)?.i()??!0;b=uH();var c=zH(b,23,!1);c||AH(b,23,!0);if(!c){var d=Wh(a.sa,v2,28,x2)?.g()??null;b=a.pubWin;c=a.D.google_ad_client;var e=A(a.sa,6),f=A(a.sa,20);b=new AN(b,c,d,e,f);b.i=!0;b.run()}M(zv)&&(a.pubWin.googFloatingToolbarManagerAsyncPositionUpdate=!0,a.L&&a.L!==a.pubWin&&(a.L.googFloatingToolbarManagerAsyncPositionUpdate=!0));M(Bv)&&(WD(a.pubWin).dontOverrideDocumentOverflowUnlessNeeded= 
!0,a.L&&a.L!==a.pubWin&&(WD(a.L).dontOverrideDocumentOverflowUnlessNeeded=!0));b=!vl()&&!Hb();return!b||b&&!M2(a)?N2(a):Promise.resolve()}function M2(a){return O2(a)||P2(a)} 
function O2(a){const b=a.D;if(!b.google_pause_ad_requests)return!1;const c=q.setTimeout(()=>{yB("abg:cmppar",{client:a.D.google_ad_client,url:a.D.google_page_url})},1E4),d=wB(450,()=>{b.google_pause_ad_requests=!1;q.clearTimeout(c);a.pubWin.removeEventListener("adsbygoogle-pub-unpause-ad-requests-event",d);if(!M2(a)){const e=N2(a);xB(1222,e)}});a.pubWin.addEventListener("adsbygoogle-pub-unpause-ad-requests-event",d);return!0} 
function P2(a){const b=a.pubWin.document,c=a.ba;if(wT(b)===3)return zT(wB(332,()=>{if(!Q2(a,R2().visible,c)){const g=N2(a);xB(1222,g)}}),b),!0;const d=R2();if(d.hidden<0&&d.visible<0)return!1;const e=xT(b);if(!e)return!1;if(!yT(b))return Q2(a,d.visible,c);if($S(a.L,a.pubWin,c)<=d.hidden)return!1;let f=wB(332,()=>{if(!yT(b)&&f){rb(b,e,f);if(!Q2(a,d.visible,c)){const g=N2(a);xB(1222,g)}f=null}});return qb(b,e,f)} 
function R2(){var a=V($u);const b=V(av);return b===3&&a===6?(a={hidden:0,visible:3},q.IntersectionObserver||(a.visible=-1),Lb()&&(a.visible*=2),a):{hidden:0,visible:q.IntersectionObserver?Lb()?a:b:-1}} 
function Q2(a,b,c){if(!c||b<0)return!1;var d=a.D;if(!pr(d.google_reactive_ad_format)&&(XT(d)||d.google_reactive_ads_config)||!YS(c)||$S(a.L,a.pubWin,c)<=b)return!1;var e=uH(),f=zH(e,8,{});e=zH(e,9,{});d=d.google_ad_section||d.google_ad_region||"";const g=!!a.pubWin.google_apltlad;if(!f[d]&&!e[d]&&!g)return!1;f=new Promise(h=>{const k=new q.IntersectionObserver((l,m)=>{Ga(l,n=>{n.intersectionRatio<=0||(m.unobserve(n.target),h(void 0))})},{rootMargin:`${b*100}%`});a.O=k;k.observe(c)});e=new Promise(h=> 
{c.addEventListener("adsbygoogle-close-to-visible-event",()=>{h(void 0)})});ha(Promise,"any").call(Promise,[f,e]).then(()=>{vB(294,()=>{const h=N2(a);xB(1222,h)})});return!0} 
function N2(a){vB(326,()=>{var c=a.pubWin,d=a.L,e=a.sa,f=a.pageState,g=a.Ka;if(fm(a.D)===1){var h=M(hx);if((h||M(fx))&&c===d){var k=new el;d=new fl;var l=k.setCorrelator(Zd(c));var m=$Q(c);l=fi(l,5,m);F(l,2,1);k=x(d,1,k);l=new dl;l=E(l,10,!0);m=M(ax);l=E(l,8,m);m=M(bx);l=E(l,12,m);m=M(ex);l=E(l,7,m);m=M(dx);l=E(l,13,m);x(k,2,l);c.google_rum_config=Lg(d);e=(hi(f.g(),6)?A(f.g(),6):A(e,9))&&h?g.wk:g.xk;rd(c.document,e)}else zm(tB)}});a.D.google_ad_channel=S2(a,a.D.google_ad_channel);a.D.google_tag_partner= 
T2(a,a.D.google_tag_partner);bR(a.L,a.D);const b=a.D.google_start_time;typeof b==="number"&&(Yq=b,a.D.google_start_time=null);qS(a);a.L&&aU(a.L,id(a.Ka.Vi,new Map(Object.entries(sT()))));XT(a.D)&&(yQ()&&(a.D.google_adtest=a.D.google_adtest||"on"),a.D.google_pgb_reactive=a.D.google_pgb_reactive||3);return U2(a)}function S2(a,b){return(b?[b]:[]).concat(IH(a.pubWin).ad_channels||[]).join("+")}function T2(a,b){return(b?[b]:[]).concat(IH(a.pubWin).tag_partners||[]).join("+")} 
function V2(a){const b=sd("IFRAME");vd(a,(c,d)=>{c!=null&&b.setAttribute(d,c)});return b}function W2(a,b,c){return a.D.google_reactive_ad_format===9&&Nl(a.ba,null,"fsi_container")?(a.ba.appendChild(b),Promise.resolve(b)):hU(a.Ka.Jh,525,d=>{a.ba.appendChild(b);d.createAdSlot(a.L,a.D,b,a.ba.parentElement,c,a.pubWin);return b})} 
function X2(a,b,c,d){WH();L(XH).ib=a.D.google_page_url;d=dk(ck(F(F(bk(new ek,Zj(new ak,String(Zd(a.pubWin)))),4,1),2,1),a.pageState.g().g()||C(a.sa,2)),d.g());M(Su)&&E(d,7,!0);D2(a.pubWin,d);const e=a.L;if(a.D.google_acr)if(a.D.google_wrap_fullscreen_ad){const g=a.D.google_acr;l2(a.pubWin,a.ba.parentElement,b,a.D.google_reactive_ad_format,a.D).then(g).catch(()=>{g(null)})}else a.D.google_acr(b);qb(b,"load",()=>{b&&b.setAttribute("data-load-complete",!0);const g=e?IH(e).enable_overlap_observer||!1: 
!1;(a.D.ovlp||g)&&e&&e===a.pubWin&&Y2(e,a,a.ba,b)});d=g=>{g&&a.i.push(()=>{g.dispose()})};const f=cT(a,b);bT(a.pubWin,a.j,b.contentWindow,a.i);!e||XT(a.D)&&!kU(a.D)||(a.D.no_resize||d(new LV(e,b,a.ba)),d(new bV(a,b)),d(e.IntersectionObserver?null:new dV(e,b,a.ba)),e.IntersectionObserver&&d(SV(e,b,a.D,a.ba,wB(1225,()=>{f();for(const g of a.i)g();a.i.length=0}))));e&&(d(WU(e,b,a.j)),a.i.push(uU(e,a.D)),L(zU).M(e),a.i.push(OU(e,a.ba,b)));b&&b.setAttribute("data-google-container-id",c);c=a.D.iaaso;if(c!= 
null){d=a.ba;const g=d.parentElement;(g&&xx.test(g.className)?g:d).setAttribute("data-auto-ad-size",c)}b.setAttribute("tabindex","0");b.setAttribute("title","Advertisement");b.setAttribute("aria-label","Advertisement");Z2(a);M(Ru)&&lS(a,b)}function Z2(a){const b=vl(a.pubWin);if(b)if(b.container==="AMP-STICKY-AD"){const c=d=>{d.data==="fill_sticky"&&b.renderStart&&b.renderStart()};qb(a.pubWin,"message",wB(616,c));a.i.push(()=>{rb(a.pubWin,"message",c)})}else b.renderStart&&b.renderStart()} 
function Y2(a,b,c,d){iR(new sR(a),c).then(e=>{kl(8,[e]);return e}).then(e=>{const f=c.parentElement;(f&&xx.test(f.className)?f:c).setAttribute("data-overlap-observer-io",String(e.qh));return e}).then(e=>{const f=b.D.armr||"",g=e.lj||"",h=b.D.iaaso==null?"":Number(b.D.iaaso),k=Number(e.qh),l=Ma(e.entries,B=>`${B.Gb}:${B.bg}:${B.Eh}`),m=Number(e.hk.toFixed(2)),n=d.dataset.googleQueryId||"",p=m*e.zc.width*e.zc.height,t=`${e.Oh.scrollX},${e.Oh.scrollY}`,z=gm(e.target),w=[e.zc.left,e.zc.top,e.zc.right, 
e.zc.bottom].join();e=`${e.ai.width}x${e.ai.height}`;yB("ovlp",{adf:b.D.google_ad_dom_fingerprint,armr:f,client:b.D.google_ad_client,eid:$Q(b.D),et:g,fwrattr:b.D.google_full_width_responsive,iaaso:h,io:k,saldr:b.D.google_loader_used,oa:m,oe:l.join(","),qid:n,rafmt:b.D.google_responsive_auto_format,roa:p,slot:b.D.google_ad_slot,sp:t,tgt:z,tr:w,url:b.D.google_page_url,vp:e,pvc:Zd(a)},1)}).catch(e=>{kl(8,["Error:",e.message,c]);yB("ovlp-err",{err:e.message},1)})} 
function $2(a,b){b.allow=b.allow&&b.allow.length>0?b.allow+("; "+a):a} 
function a3(a,b,c){var d=a.D,e=d.google_async_iframe_id;const f=d.google_ad_width,g=d.google_ad_height,h=lU(d);e={id:e,name:e};var k=a.D,l=a.B;KQ("browsing-topics",a.pubWin.document)&&NU(c,k)&&!M(Sw)&&!KU(l?.label)&&(e.browsingTopics="true");e.style=h?[`width:${f}px !IMPORTANT`,`height:${g}px !IMPORTANT`].join(";"):"left:0;position:absolute;top:0;border:0;"+`width:${f}px;`+`height:${g}px;`;k=Hd();if(k["allow-top-navigation-by-user-activation"]&&k["allow-popups-to-escape-sandbox"]){if(!h)if(k=b,b= 
"fsb="+encodeURIComponent("1")){l=k.indexOf("#");l<0&&(l=k.length);let m=k.indexOf("?"),n;m<0||m>l?(m=l,n=""):n=k.substring(m+1,l);k=[k.slice(0,m),n,k.slice(l)];l=k[1];k[1]=b?l?l+"&"+b:b:l;b=k[0]+(k[1]?"?"+k[1]:"")+k[2]}else b=k;e.sandbox=Gd().join(" ")}d.google_video_play_muted===!1&&$2("autoplay",e);k=b;k.length>61440&&(k=k.substring(0,61432),k=k.replace(/%\w?$/,""),k=k.replace(/&[^=]*=?$/,""),k+="&trunc=1");k!==b&&(l=b.lastIndexOf("&",61432),l===-1&&(l=b.lastIndexOf("?",61432)),yB("trn",{ol:b.length, 
tr:l===-1?"":b.substring(l+1),url:b},.01));b=k;f!=null&&(e.width=String(f));g!=null&&(e.height=String(g));e.frameborder="0";e.marginwidth="0";e.marginheight="0";e.vspace="0";e.hspace="0";e.allowtransparency="true";e.scrolling="no";d.dash&&(e.srcdoc=d.dash);JQ("attribution-reporting",a.pubWin.document)&&$2("attribution-reporting",e);JQ("run-ad-auction",a.pubWin.document)&&$2("run-ad-auction",e);M(Fw)&&(d=a.pubWin,d.credentialless!==void 0&&(M(Gw)||d.crossOriginIsolated)&&(e.credentialless="true")); 
if(h)e.src=b,e=V2(e),a=W2(a,e,c);else{c={};c.dtd=CU((new Date).getTime(),Yq);c=cm(c,b);e.src=c;c=a.pubWin;c=c==c.top;e=V2(e);c&&a.i.push(Bl(a.pubWin,e));a.ba.style.visibility="visible";for(a=a.ba;c=a.firstChild;)a.removeChild(c);a.appendChild(e);a=Promise.resolve(e)}return a} 
async function b3(a){var b=a.D,c=a.pubWin;const d=a.j;c3(a);d.g()&&aT(new XQ(a.pubWin),a.j,a.pubWin.location.hostname);if(!d.g()&&!a.H)return yB("afc_noc_req",{client:a.D.google_ad_client,isGdprCountry:(a.pageState.g().i()?A(a.pageState.g(),4):A(a.sa,6)).toString()},V(Zu)),Promise.resolve();var e=d3(a.Ka,d);e&&document.documentElement.appendChild(e);M(Mw)&&d.g()&&(a.B=await GH());a.G=LU(a.pubWin,d);ZQ(a.pubWin,d);if(e=a.D.google_reactive_ads_config)gU(a.L,e),mU(e,a,d),e=e.page_level_pubvars,oa(e)&& 
Xb(a.D,e);e=KQ("shared-storage",a.pubWin.document);M(Ew)?vS(a.pubWin,a.H):a.G&&d.g()&&e&&!M(Dw)&&!zH(uH(),34,!1)&&(AH(uH(),34,!0),e=a.G.then(g=>{g({message:"goog:spam:client_age",pvsid:Zd(a.pubWin)})}),xB(1069,e));await MU(a,a.pubWin,d,a.D,a.G,a.B);await a.C?.Yi;e="";lU(b)?(e=(d.g()?a.Ka.fi:a.Ka.ei).toString()+"#"+(encodeURIComponent("RS-"+b.google_reactive_sra_index+"-")+"&"+bm({adk:b.google_ad_unit_key,client:b.google_ad_client,fa:b.google_reactive_ad_format})),EZ(b,uH()),e3(b)):(b.google_pgb_reactive=== 
5&&b.google_reactive_ads_config||!YT(b)||WT(c,b,d))&&e3(b)&&(e=vZ(a,d));kl(2,[b,e]);if(!e)return Promise.resolve();b.google_async_iframe_id||em(c);var f=fm(b);b=a.pubWin===a.L?"a!"+f.toString(36):`${f.toString(36)}.${Math.floor(Math.random()*2147483648).toString(36)+Math.abs(Math.floor(Math.random()*2147483648)^Date.now()).toString(36)}`;c=$S(a.L,a.pubWin,a.ba,!0)>0;f={ifi:f,uci:b};c&&(c=uH(),f.btvi=zH(c,21,1),BH(c,21));e=cm(f,e);c=a3(a,e,d);a.D.rpe&&JV(a.pubWin,a.ba,{height:a.D.google_ad_height, 
wg:"force",Cd:!0,kg:!0,Je:a.D.google_ad_client});c=await c;try{X2(a,c,b,d)}catch(g){zB(223,g)}}function f3(a){const b=V(Wu);if(b<=0)return null;const c=sm(),d=B2(a.pubWin);if(!d)return null;a.F="0";return Promise.race([d,ae(b,"0")]).then(e=>{yB("adsense_paw",{time:sm()-c});e?.length>1E4?zB(809,Error(`ML:${e.length}`)):a.F=e}).catch(e=>{zB(809,e)})} 
function c3(a){var b=a.pubWin;const c=a.ba;var d=a.D;const e=a.jb,f=V(Zw);d=!pr(d.google_reactive_ad_format)&&(XT(d)||d.google_reactive_ads_config);if(!(a.C?.Cf||f<=0||qd(b)||!q.IntersectionObserver||d)){a.C={};var g=new Lq(e),h=sm();b=new Promise(k=>{let l=0;const m=a.C,n=new q.IntersectionObserver(wB(1236,p=>{if(p=p.find(t=>t.target===c))g.je.Fe.Bd.g.g.od({ud:sm()-h,Sk:++l}),m.Lj=p.isIntersecting&&p.intersectionRatio>=.8,k()}),{threshold:[.8]});n.observe(c);m.Cf=n});a.C.Yi=Promise.race([b,ae(f, 
null)]).then(k=>{g.je.Fe.Bd.g.i.od({ud:sm()-h,status:k===null?"TIMEOUT":"OK"})})}}function g3(a){const b=sm();return Promise.race([vB(832,()=>F2(a)),ae(200)]).then(c=>{yB("afc_etu",{etus:c?.status??100,sig:sm()-b,tms:200});return c?.Rc})}async function h3(a){const b=f3(a),c=vB(868,()=>g3(a.pubWin));await RS(a);await b;a.Rc=await c||"";await b3(a)} 
function U2(a){be(a.pubWin)!==a.pubWin&&(a.g|=4);wT(a.pubWin.document)===3&&(a.g|=32);var b;if(b=a.L){b=a.L;const c=hr(b);b=!(mr(b).scrollWidth<=c)}b&&(a.g|=1024);a.pubWin.Prototype?.Version&&(a.g|=16384);a.D.google_loader_features_used&&(a.g|=a.D.google_loader_features_used);return h3(a)}function e3(a){const b=uH(),c=a.google_ad_section;XT(a)&&BH(b,15);if(hm(a)){if(BH(b,5)>100)return!1}else if(BH(b,6)-zH(b,15,0)>100&&c==="")return!1;return!0} 
function d3(a,b){a:{var c=[q.top];var d=[];let f=0,g;for(;g=c[f++];){d.push(g);try{if(g.frames)for(let h=0;h<g.frames.length&&c.length<1024;++h)c.push(g.frames[h])}catch{}}c=d;for(d=0;d<c.length;d++)try{var e=c[d].frames.google_esf;if(e){bl=e;break a}}catch(h){}bl=null}if(bl)return null;e=sd("IFRAME");e.id="google_esf";e.name="google_esf";a=b.g()?a.fi:a.ei;e.src=hc(a).toString();e.style.display="none";return e} 
function L2(a){DH()&&q.setTimeout(wB(1244,()=>void YR(a.L||a.pubWin,{Fa:a.pageState.g().i()?A(a.pageState.g(),4):A(a.sa,6)})),1E3)};(function(a,b,c){vB(843,()=>{if(!q.google_sa_impl){G2=G2??new Pq(b);try{ze(k=>{gB(G2,1192,k)})}catch(k){}var d=rT(),e=d.g(),f=z2(a);u2(f,d);kl(16,[3,Lg(f)]);var g=s2({Pg:b,Qh:e.g()||C(f,2)}),h=c(g,f,Qh(e,1),e.g(),C(e,9));q.google_sa_impl=k=>I2({sa:f,Ka:h,jb:g,slot:k,pageState:d});RQ(IQ(q));q.google_process_slots?.();e=(q.Prototype||{}).Version;e!=null&&yB("prtpjs",{version:e})}})})(y2,cq(),function(a,b,c,d,e){c=c>2012?`_fy${c}`:"";e||(e=C(b,3));d||(d=C(b,2));return{xk:hd`https://pagead2.googlesyndication.com/pagead/js/${d}/${e}/rum${c}.js`, 
wk:hd`https://pagead2.googlesyndication.com/pagead/js/${d}/${e}/rum_debug${c}.js`,Jh:hd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/reactive_library${c}.js`,Vi:hd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/debug_card_library${c}.js`,To:hd`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/slotcar_library${c}.js`,fi:hd`https://googleads.g.doubleclick.net/pagead/html/${d}/${e}/zrt_lookup${c}.html`,ei:hd`https://pagead2.googlesyndication.com/pagead/html/${d}/${e}/zrt_lookup${c}.html`}}); 
}).call(this,"");
