(function(){var n,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ca=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");},ea=
da(this),p=function(a,b){if(b)a:{var c=ea;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ca(c,a,{configurable:!0,writable:!0,value:b})}};
p("Symbol",function(a){if(a)return a;var b=function(f,g){this.Kg=f;ca(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.Kg};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("b");return new b(c+(f||"")+"_"+d++,f)};return e});
p("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=ea[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ca(d.prototype,a,{configurable:!0,writable:!0,value:function(){return fa(aa(this))}})}return a});p("Symbol.asyncIterator",function(a){return a?a:Symbol("Symbol.asyncIterator")});
var fa=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a},ha=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ia;if(typeof Object.setPrototypeOf=="function")ia=Object.setPrototypeOf;else{var ja;a:{var ka={a:!0},ma={};try{ma.__proto__=ka;ja=ma.a;break a}catch(a){}ja=!1}ia=ja?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError("c`"+a);return a}:null}
var na=ia,q=function(a,b){a.prototype=ha(b.prototype);a.prototype.constructor=a;if(na)na(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Fi=b.prototype},w=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error("d`"+String(a));},z=function(a){if(!(a instanceof Array)){a=w(a);for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a},pa=function(a){return oa(a,a)},oa=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},qa=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},ra=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)qa(d,e)&&(a[e]=d[e])}return a};p("Object.assign",function(a){return a||ra});
var sa=function(){this.pc=!1;this.Wa=null;this.aa=void 0;this.o=1;this.Ea=this.ua=0;this.be=this.ea=null};sa.prototype.Ka=function(){if(this.pc)throw new TypeError("f");this.pc=!0};sa.prototype.wc=function(a){this.aa=a};sa.prototype.Hc=function(a){this.ea={wf:a,Uf:!0};this.o=this.ua||this.Ea};sa.prototype.return=function(a){this.ea={return:a};this.o=this.Ea};var ta=function(a,b,c){a.o=c;return{value:b}};sa.prototype.Ia=function(a){this.o=a};
var ua=function(a,b,c){a.ua=b;c!=void 0&&(a.Ea=c)},va=function(a){a.ua=0;var b=a.ea.wf;a.ea=null;return b},wa=function(a,b,c,d){d?a.be[d]=a.ea:a.be=[a.ea];a.ua=b||0;a.Ea=c||0},xa=function(a,b,c){c=a.be.splice(c||0)[0];(c=a.ea=a.ea||c)?c.Uf?a.o=a.ua||a.Ea:c.Ia!=void 0&&a.Ea<c.Ia?(a.o=c.Ia,a.ea=null):a.o=a.Ea:a.o=b};sa.prototype.forIn=function(a){return new ya(a)};var ya=function(a){this.ig=[];for(var b in a)this.ig.push(b);this.ig.reverse()},za=function(a){this.s=new sa;this.ii=a};
za.prototype.wc=function(a){this.s.Ka();if(this.s.Wa)return Aa(this,this.s.Wa.next,a,this.s.wc);this.s.wc(a);return Ba(this)};var Ca=function(a,b){a.s.Ka();var c=a.s.Wa;if(c)return Aa(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.s.return);a.s.return(b);return Ba(a)};za.prototype.Hc=function(a){this.s.Ka();if(this.s.Wa)return Aa(this,this.s.Wa["throw"],a,this.s.wc);this.s.Hc(a);return Ba(this)};
var Aa=function(a,b,c,d){try{var e=b.call(a.s.Wa,c);if(!(e instanceof Object))throw new TypeError("e`"+e);if(!e.done)return a.s.pc=!1,e;var f=e.value}catch(g){return a.s.Wa=null,a.s.Hc(g),Ba(a)}a.s.Wa=null;d.call(a.s,f);return Ba(a)},Ba=function(a){for(;a.s.o;)try{var b=a.ii(a.s);if(b)return a.s.pc=!1,{value:b.value,done:!1}}catch(c){a.s.aa=void 0,a.s.Hc(c)}a.s.pc=!1;if(a.s.ea){b=a.s.ea;a.s.ea=null;if(b.Uf)throw b.wf;return{value:b.return,done:!0}}return{value:void 0,done:!0}},Da=function(a){this.next=
function(b){return a.wc(b)};this.throw=function(b){return a.Hc(b)};this.return=function(b){return Ca(a,b)};this[Symbol.iterator]=function(){return this}},Ea=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})},Fa=function(a){return Ea(new Da(new za(a)))},Ga=function(a){this[Symbol.asyncIterator]=function(){return this};this[Symbol.iterator]=function(){return a};
this.next=function(b){return Promise.resolve(a.next(b))};this["throw"]=function(b){return new Promise(function(c,d){var e=a["throw"];e!==void 0?c(e.call(a,b)):(c=a["return"],c!==void 0&&c.call(a),d(new TypeError("g")))})};a["return"]!==void 0&&(this["return"]=function(b){return Promise.resolve(a["return"](b))})},B=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};p("globalThis",function(a){return a||ea});
p("Reflect.setPrototypeOf",function(a){return a?a:na?function(b,c){try{return na(b,c),!0}catch(d){return!1}}:null});
p("Promise",function(a){function b(){this.La=null}function c(g){return g instanceof e?g:new e(function(h){h(g)})}if(a)return a;b.prototype.ff=function(g){if(this.La==null){this.La=[];var h=this;this.gf(function(){h.mh()})}this.La.push(g)};var d=ea.setTimeout;b.prototype.gf=function(g){d(g,0)};b.prototype.mh=function(){for(;this.La&&this.La.length;){var g=this.La;this.La=[];for(var h=0;h<g.length;++h){var k=g[h];g[h]=null;try{k()}catch(l){this.Sg(l)}}}this.La=null};b.prototype.Sg=function(g){this.gf(function(){throw g;
})};var e=function(g){this.Ub=0;this.Dc=void 0;this.Nb=[];this.Yf=!1;var h=this.Ud();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}};e.prototype.Ud=function(){function g(l){return function(m){k||(k=!0,l.call(h,m))}}var h=this,k=!1;return{resolve:g(this.vi),reject:g(this.De)}};e.prototype.vi=function(g){if(g===this)this.De(new TypeError("h"));else if(g instanceof e)this.zi(g);else{a:switch(typeof g){case "object":var h=g!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.ui(g):this.zf(g)}};
e.prototype.ui=function(g){var h=void 0;try{h=g.then}catch(k){this.De(k);return}typeof h=="function"?this.Ai(h,g):this.zf(g)};e.prototype.De=function(g){this.rg(2,g)};e.prototype.zf=function(g){this.rg(1,g)};e.prototype.rg=function(g,h){if(this.Ub!=0)throw Error("i`"+g+"`"+h+"`"+this.Ub);this.Ub=g;this.Dc=h;this.Ub===2&&this.wi();this.nh()};e.prototype.wi=function(){var g=this;d(function(){if(g.Zh()){var h=ea.console;typeof h!=="undefined"&&h.error(g.Dc)}},1)};e.prototype.Zh=function(){if(this.Yf)return!1;
var g=ea.CustomEvent,h=ea.Event,k=ea.dispatchEvent;if(typeof k==="undefined")return!0;typeof g==="function"?g=new g("unhandledrejection",{cancelable:!0}):typeof h==="function"?g=new h("unhandledrejection",{cancelable:!0}):(g=ea.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.Dc;return k(g)};e.prototype.nh=function(){if(this.Nb!=null){for(var g=0;g<this.Nb.length;++g)f.ff(this.Nb[g]);this.Nb=null}};var f=new b;e.prototype.zi=function(g){var h=
this.Ud();g.Sc(h.resolve,h.reject)};e.prototype.Ai=function(g,h){var k=this.Ud();try{g.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.then=function(g,h){function k(r,u){return typeof r=="function"?function(x){try{l(r(x))}catch(v){m(v)}}:u}var l,m,t=new e(function(r,u){l=r;m=u});this.Sc(k(g,l),k(h,m));return t};e.prototype.catch=function(g){return this.then(void 0,g)};e.prototype.Sc=function(g,h){function k(){switch(l.Ub){case 1:g(l.Dc);break;case 2:h(l.Dc);break;default:throw Error("j`"+
l.Ub);}}var l=this;this.Nb==null?f.ff(k):this.Nb.push(k);this.Yf=!0};e.resolve=c;e.reject=function(g){return new e(function(h,k){k(g)})};e.race=function(g){return new e(function(h,k){for(var l=w(g),m=l.next();!m.done;m=l.next())c(m.value).Sc(h,k)})};e.all=function(g){var h=w(g),k=h.next();return k.done?c([]):new e(function(l,m){function t(x){return function(v){r[x]=v;u--;u==0&&l(r)}}var r=[],u=0;do r.push(void 0),u++,c(k.value).Sc(t(r.length-1),m),k=h.next();while(!k.done)})};return e});
p("Object.setPrototypeOf",function(a){return a||na});p("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});p("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)qa(b,d)&&c.push(b[d]);return c}});var Ha=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};
p("Array.prototype.keys",function(a){return a?a:function(){return Ha(this,function(b){return b})}});
p("WeakMap",function(a){function b(){}function c(k){var l=typeof k;return l==="object"&&k!==null||l==="function"}function d(k){if(!qa(k,f)){var l=new b;ca(k,f,{value:l})}}function e(k){var l=Object[k];l&&(Object[k]=function(m){if(m instanceof b)return m;Object.isExtensible(m)&&d(m);return l(m)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),m=new a([[k,2],[l,3]]);if(m.get(k)!=2||m.get(l)!=3)return!1;m.delete(k);m.set(l,4);return!m.has(k)&&m.get(l)==4}catch(t){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var g=0,h=function(k){this.kc=(g+=Math.random()+1).toString();if(k){k=w(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};h.prototype.set=function(k,l){if(!c(k))throw Error("k");d(k);if(!qa(k,f))throw Error("l`"+k);k[f][this.kc]=l;return this};h.prototype.get=function(k){return c(k)&&qa(k,f)?k[f][this.kc]:void 0};h.prototype.has=function(k){return c(k)&&qa(k,f)&&qa(k[f],this.kc)};h.prototype.delete=function(k){return c(k)&&
qa(k,f)&&qa(k[f],this.kc)?delete k[f][this.kc]:!1};return h});
p("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(w([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var l=k.entries(),m=l.next();if(m.done||m.value[0]!=h||m.value[1]!="s")return!1;m=l.next();return m.done||m.value[0].x!=4||m.value[1]!="t"||!l.next().done?!1:!0}catch(t){return!1}}())return a;var b=new WeakMap,c=function(h){this[0]={};this[1]=
f();this.size=0;if(h){h=w(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=h===0?0:h;var l=d(this,h);l.list||(l.list=this[0][l.id]=[]);l.T?l.T.value=k:(l.T={next:this[1],Ja:this[1].Ja,head:this[1],key:h,value:k},l.list.push(l.T),this[1].Ja.next=l.T,this[1].Ja=l.T,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.T&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.T.Ja.next=h.T.next,h.T.next.Ja=h.T.Ja,
h.T.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].Ja=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).T};c.prototype.get=function(h){return(h=d(this,h).T)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,k){for(var l=this.entries(),
m;!(m=l.next()).done;)m=m.value,h.call(k,m[1],m[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(h,k){var l=k&&typeof k;l=="object"||l=="function"?b.has(k)?l=b.get(k):(l=""+ ++g,b.set(k,l)):l="p_"+k;var m=h[0][l];if(m&&qa(h[0],l))for(h=0;h<m.length;h++){var t=m[h];if(k!==k&&t.key!==t.key||k===t.key)return{id:l,list:m,index:h,T:t}}return{id:l,list:m,index:-1,T:void 0}},e=function(h,k){var l=h[1];return fa(function(){if(l){for(;l.head!=h[1];)l=l.Ja;for(;l.next!=l.head;)return l=
l.next,{done:!1,value:k(l)};l=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.Ja=h.next=h.head=h},g=0;return c});
p("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(w([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;var b=function(c){this.xa=new Map;if(c){c=
w(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.xa.size};b.prototype.add=function(c){c=c===0?0:c;this.xa.set(c,c);this.size=this.xa.size;return this};b.prototype.delete=function(c){c=this.xa.delete(c);this.size=this.xa.size;return c};b.prototype.clear=function(){this.xa.clear();this.size=0};b.prototype.has=function(c){return this.xa.has(c)};b.prototype.entries=function(){return this.xa.entries()};b.prototype.values=function(){return this.xa.values()};b.prototype.keys=b.prototype.values;
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.xa.forEach(function(f){return c.call(d,f,f,e)})};return b});p("Array.prototype.entries",function(a){return a?a:function(){return Ha(this,function(b,c){return[b,c]})}});var Ia=function(a,b,c){if(a==null)throw new TypeError("m`"+c);if(b instanceof RegExp)throw new TypeError("n`"+c);return a+""};
p("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=Ia(this,b,"startsWith");b+="";var e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});p("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});
p("String.prototype.repeat",function(a){return a?a:function(b){var c=Ia(this,null,"repeat");if(b<0||b>1342177279)throw new RangeError("Invalid count value");b|=0;for(var d="";b;)if(b&1&&(d+=c),b>>>=1)c+=c;return d}});p("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}});
p("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});p("String.prototype.includes",function(a){return a?a:function(b,c){return Ia(this,b,"includes").indexOf(b,c||0)!==-1}});p("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)qa(b,d)&&c.push([d,b[d]]);return c}});
p("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}});p("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});p("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});
p("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(h){return h};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});p("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});
p("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});p("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});p("Math.log2",function(a){return a?a:function(b){return Math.log(b)/Math.LN2}});p("Array.prototype.values",function(a){return a?a:function(){return Ha(this,function(b,c){return c})}});
p("Array.prototype.fill",function(a){return a?a:function(b,c,d){var e=this.length||0;c<0&&(c=Math.max(0,e+c));if(d==null||d>e)d=e;d=Number(d);d<0&&(d=Math.max(0,e+d));for(c=Number(c||0);c<d;c++)this[c]=b;return this}});var La=function(a){return a?a:Array.prototype.fill};p("Int8Array.prototype.fill",La);p("Uint8Array.prototype.fill",La);p("Uint8ClampedArray.prototype.fill",La);p("Int16Array.prototype.fill",La);p("Uint16Array.prototype.fill",La);p("Int32Array.prototype.fill",La);
p("Uint32Array.prototype.fill",La);p("Float32Array.prototype.fill",La);p("Float64Array.prototype.fill",La);p("String.prototype.padStart",function(a){return a?a:function(b,c){var d=Ia(this,null,"padStart");b-=d.length;c=c!==void 0?String(c):" ";return(b>0&&c?c.repeat(Math.ceil(b/c.length)).substring(0,b):"")+d}});
p("Array.prototype.flat",function(a){return a?a:function(b){b=b===void 0?1:b;var c=[];Array.prototype.forEach.call(this,function(d){Array.isArray(d)&&b>0?(d=Array.prototype.flat.call(d,b-1),c.push.apply(c,d)):c.push(d)});return c}});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Ma=this||self,Na=function(a,b){a:{var c=["CLOSURE_FLAGS"];for(var d=Ma,e=0;e<c.length;e++)if(d=d[c[e]],d==null){c=null;break a}c=d}a=c&&c[a];return a!=null?a:b},Oa=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"},Pa=function(a){var b=Oa(a);return b=="array"||b=="object"&&typeof a.length=="number"},Qa=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"},Ra=function(a){return a},Sa=function(a,b){function c(){}c.prototype=b.prototype;a.Fi=b.prototype;
a.prototype=new c;a.prototype.constructor=a;a.xj=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Ta=function(){this.yg=0};Ta.prototype.Vb=function(a,b){var c=this;return function(){var d=B.apply(0,arguments);c.yg=a;return b.apply(null,z(d))}};var Ua=function(){var a={};this.ya=(a[3]=[],a[2]=[],a[1]=[],a);this.re=!1},Wa=function(a,b,c){var d=Va(a,c);a.ya[c].push(b);d&&a.ya[c].length===1&&a.flush()},Va=function(a,b){return Object.keys(a.ya).map(function(c){return Number(c)}).filter(function(c){return!isNaN(c)&&c>b}).every(function(c){return a.ya[c].length===0})};
Ua.prototype.flush=function(){if(!this.re){this.re=!0;try{for(;Object.values(this.ya).some(function(a){return a.length>0});)Xa(this,3),Xa(this,2),Xa(this,1)}catch(a){throw Object.values(this.ya).forEach(function(b){return void b.splice(0,b.length)}),a;}finally{this.re=!1}}};var Xa=function(a,b){for(;Va(a,b)&&a.ya[b].length>0;)a.ya[b][0](),a.ya[b].shift()};
ea.Object.defineProperties(Ua.prototype,{lg:{configurable:!0,enumerable:!0,get:function(){return Object.values(this.ya).some(function(a){return a.length>0})}}});function Za(a,b){return a.toLowerCase().indexOf(b.toLowerCase())!=-1};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var $a={};var ab=globalThis.trustedTypes,bb;function cb(){var a=null;if(!ab)return a;try{var b=function(c){return c};a=ab.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){throw c;}return a};var db=function(a){if($a!==$a)throw Error("o");this.hg=a};db.prototype.toString=function(){return this.hg+""};function eb(a){bb===void 0&&(bb=cb());var b=bb;return new db(b?b.createScriptURL(a):a)};var fb=pa([""]),gb=oa(["\x00"],["\\0"]),hb=oa(["\n"],["\\n"]),kb=oa(["\x00"],["\\u0000"]),lb=pa([""]),mb=oa(["\x00"],["\\0"]),nb=oa(["\n"],["\\n"]),ob=oa(["\x00"],["\\u0000"]);function pb(a){return Object.isFrozen(a)&&Object.isFrozen(a.raw)}function qb(a){return a.toString().indexOf("`")===-1}var rb=qb(function(a){return a(fb)})||qb(function(a){return a(gb)})||qb(function(a){return a(hb)})||qb(function(a){return a(kb)}),sb=pb(lb)&&pb(mb)&&pb(nb)&&pb(ob);var tb=function(a){if($a!==$a)throw Error("o");this.hi=a};tb.prototype.toString=function(){return this.hi};new tb("about:blank");new tb("about:invalid#zClosurez");var ub=[],vb=function(a){console.warn("q`"+a)};ub.indexOf(vb)===-1&&ub.push(vb);function wb(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,wb);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b)}Sa(wb,Error);wb.prototype.name="CustomError";var xb;function zb(a,b){var c=wb.call;a=a.split("%s");for(var d="",e=a.length-1,f=0;f<e;f++)d+=a[f]+(f<b.length?b[f]:"%s");c.call(wb,this,d+a[e])}Sa(zb,wb);zb.prototype.name="AssertionError";function Ab(a,b,c,d){var e="Assertion failed";if(c){e+=": "+c;var f=d}else a&&(e+=": "+a,f=b);throw new zb(""+e,f||[]);}
var E=function(a,b,c){a||Ab("",null,b,Array.prototype.slice.call(arguments,2));return a},F=function(a,b,c){a==null&&Ab("Expected to exist: %s.",[a],b,Array.prototype.slice.call(arguments,2));return a},Bb=function(a,b){throw new zb("Failure"+(a?": "+a:""),Array.prototype.slice.call(arguments,1));},Cb=function(a,b,c){typeof a!=="number"&&Ab("Expected number but got %s: %s.",[Oa(a),a],b,Array.prototype.slice.call(arguments,2))},Db=function(a,b,c){typeof a!=="string"&&Ab("Expected string but got %s: %s.",
[Oa(a),a],b,Array.prototype.slice.call(arguments,2))},Eb=function(a,b,c){typeof a!=="function"&&Ab("Expected function but got %s: %s.",[Oa(a),a],b,Array.prototype.slice.call(arguments,2));return a},Fb=function(a,b,c){Qa(a)||Ab("Expected object but got %s: %s.",[Oa(a),a],b,Array.prototype.slice.call(arguments,2))},H=function(a,b,c){Array.isArray(a)||Ab("Expected array but got %s: %s.",[Oa(a),a],b,Array.prototype.slice.call(arguments,2));return a},Hb=function(a,b,c,d){a instanceof b||Ab("Expected instanceof %s but got %s.",
[Gb(b),Gb(a)],c,Array.prototype.slice.call(arguments,3));return a};function Gb(a){return a instanceof Function?a.displayName||a.name||"unknown type name":a instanceof Object?a.constructor.displayName||a.constructor.name||Object.prototype.toString.call(a):a===null?"null":typeof a};var Ib=Array.prototype.forEach?function(a,b){E(a.length!=null);Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)},Jb=Array.prototype.map?function(a,b){E(a.length!=null);return Array.prototype.map.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=Array(c),e=typeof a==="string"?a.split(""):a,f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d},Kb=Array.prototype.some?function(a,b){E(a.length!=
null);return Array.prototype.some.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=typeof a==="string"?a.split(""):a,e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1};function Lb(a){return Array.prototype.concat.apply([],arguments)}function Mb(a){var b=a.length;if(b>0){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]}function Nb(a,b,c){if(!Pa(a)||!Pa(b)||a.length!=b.length)return!1;var d=a.length;c=c||Ob;for(var e=0;e<d;e++)if(!c(a[e],b[e]))return!1;return!0}
function Ob(a,b){return a===b}function Pb(a,b){return Lb.apply([],Jb(a,b))};var Qb=function(a,b){this.name=a;this.value=b};Qb.prototype.toString=function(){return this.name};var Rb=new Qb("OFF",Infinity),Sb=new Qb("WARNING",900),Tb=new Qb("INFO",800),Ub=new Qb("CONFIG",700),Vb=function(){this.Tc=0;this.clear()},Wb;Vb.prototype.clear=function(){this.I=Array(this.Tc);this.pf=-1;this.Vf=!1};var Xb=function(a,b,c){this.reset(a||Rb,b,c,void 0,void 0)};Xb.prototype.reset=function(a,b,c,d){d||Date.now();this.Yh=b};Xb.prototype.getMessage=function(){return this.Yh};
var Yb=function(a,b){this.level=null;this.vh=[];this.parent=(b===void 0?null:b)||null;this.children=[];this.Qh={Fa:function(){return a}}},Zb=function(a){if(a.level)return a.level;if(a.parent)return Zb(a.parent);Bb("Root logger has no level set.");return Rb},$b=function(a,b){for(;a;)a.vh.forEach(function(c){c(b)}),a=a.parent},ac=function(){this.entries={};var a=new Yb("");a.level=Ub;this.entries[""]=a},bc,dc=function(a,b,c){var d=a.entries[b];if(d)return c!==void 0&&(d.level=c),d;d=b.lastIndexOf(".");
d=b.slice(0,Math.max(d,0));d=dc(a,d);var e=new Yb(b,d);a.entries[b]=e;d.children.push(e);c!==void 0&&(e.level=c);return e},ec=function(){bc||(bc=new ac);return bc},hc=function(a){var b=gc;if(b){var c=a,d=Sb;if(a=b)if(a=b&&d){a=d.value;var e=b?Zb(dc(ec(),b.Fa())):Rb;a=a>=e.value}if(a){d=d||Rb;a=dc(ec(),b.Fa());typeof c==="function"&&(c=c());Wb||(Wb=new Vb);e=Wb;b=b.Fa();if(e.Tc>0){var f=(e.pf+1)%e.Tc;e.pf=f;e.Vf?(e=e.I[f],e.reset(d,c,b),b=e):(e.Vf=f==e.Tc-1,b=e.I[f]=new Xb(d,c,b))}else b=new Xb(d,
c,b);$b(a,b)}}};var ic=function(){this.names=new Map};ic.prototype.Fa=function(a){var b=this.names.get(a);if(b)return b;var c;b=(c=a.description)!=null?c:Math.floor(Math.random()***********).toString(36)+Math.abs(Math.floor(Math.random()***********)^Date.now()).toString(36);this.names.set(a,b);return b};/*


 Copyright (c) 2015-2018 Google, Inc., Netflix, Inc., Microsoft Corp. and contributors
 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at
     http://www.apache.org/licenses/LICENSE-2.0
 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
*/
function jc(a){a=a(function(b){b.stack=Error().stack});a.prototype=Object.create(Error.prototype);return a.prototype.constructor=a};var kc=jc(function(a){return function(b){a(this);this.message=b?b.length+" errors occurred during unsubscription:\n"+b.map(function(c,d){return d+1+") "+c.toString()}).join("\n  "):"";this.name="UnsubscriptionError";this.errors=b}});function lc(a,b){a&&(b=a.indexOf(b),0<=b&&a.splice(b,1))};function I(a){return typeof a==="function"};var mc=function(a){this.Bh=a;this.closed=!1;this.Xb=this.zb=null};n=mc.prototype;
n.unsubscribe=function(){if(!this.closed){this.closed=!0;var a=this.zb;if(Array.isArray(a))for(var b=w(a),c=b.next();!c.done;c=b.next())c.value.remove(this);else a==null||a.remove(this);b=this.Bh;if(I(b))try{b()}catch(f){var d=f instanceof kc?f.errors:[f]}var e=this.Xb;if(e)for(this.Xb=null,b=w(e),c=b.next();!c.done;c=b.next()){c=c.value;try{I(c)?c():c.unsubscribe()}catch(f){c=void 0,d=(c=d)!=null?c:[],f instanceof kc?d=[].concat(z(d),z(f.errors)):d.push(f)}}if(d)throw new kc(d);}};
n.add=function(a){if(a&&a!==this)if(this.closed)I(a)?a():a.unsubscribe();else{if(a instanceof mc){if(a.closed||a.Mg(this))return;a.Lg(this)}var b;(this.Xb=(b=this.Xb)!=null?b:[]).push(a)}};n.Mg=function(a){var b=this.zb;return b===a||Array.isArray(b)&&b.includes(a)};n.Lg=function(a){var b=this.zb;this.zb=Array.isArray(b)?(b.push(a),b):b?[b,a]:a};n.Ng=function(a){var b=this.zb;b===a?this.zb=null:Array.isArray(b)&&lc(b,a)};n.remove=function(a){var b=this.Xb;b&&lc(b,a);a instanceof mc&&a.Ng(this)};
var nc=new mc;nc.closed=!0;mc.EMPTY=nc;function oc(a){return a instanceof mc||a&&"closed"in a&&I(a.remove)&&I(a.add)&&I(a.unsubscribe)};var pc=function(){setTimeout.apply(null,z(B.apply(0,arguments)))};function qc(){};function rc(a){pc(function(){throw a;})};var sc=function(a){mc.call(this);this.P=!1;this.destination=a instanceof sc?a:new tc(!a||I(a)?{next:a!=null?a:void 0}:a);oc(a)&&a.add(this)};q(sc,mc);sc.EMPTY=mc.EMPTY;sc.create=function(a,b,c){return new uc(a,b,c)};n=sc.prototype;n.next=function(a){this.P||this.Gd(a)};n.error=function(a){this.P||(this.P=!0,this.Ze(a))};n.complete=function(){this.P||(this.P=!0,this.Mc())};n.unsubscribe=function(){this.closed||(this.P=!0,mc.prototype.unsubscribe.call(this))};n.Gd=function(a){this.destination.next(a)};
n.Ze=function(a){this.destination.error(a);this.unsubscribe()};n.Mc=function(){this.destination.complete();this.unsubscribe()};var tc=function(a){this.ye=a};tc.prototype.next=function(a){var b=this.ye;if(b.next)try{b.next(a)}catch(c){rc(c)}};tc.prototype.error=function(a){var b=this.ye;if(b.error)try{b.error(a)}catch(c){rc(c)}else rc(a)};tc.prototype.complete=function(){var a=this.ye;if(a.complete)try{a.complete()}catch(b){rc(b)}};
var uc=function(a,b,c){sc.call(this);this.destination=new tc(I(a)||!a?{next:a!=null?a:void 0,error:b!=null?b:void 0,complete:c!=null?c:void 0}:a)};q(uc,sc);uc.EMPTY=sc.EMPTY;uc.create=sc.create;var vc=typeof Symbol==="function"&&Symbol.observable||"@@observable";function wc(a){return a};function J(){return xc(B.apply(0,arguments))}function xc(a){return a.length===0?wc:a.length===1?a[0]:function(b){return a.reduce(function(c,d){return d(c)},b)}};var K=function(a){a&&(this.Aa=a)};n=K.prototype;n.ob=function(a){var b=new K;b.source=this;b.operator=a;return b};n.subscribe=function(a,b,c){a=a&&a instanceof sc||a&&I(a.next)&&I(a.error)&&I(a.complete)&&oc(a)?a:new uc(a,b,c);b=this.operator;c=this.source;a.add(b?b.call(a,c):c?this.Aa(a):this.Id(a));return a};n.Id=function(a){try{return this.Aa(a)}catch(b){a.error(b)}};
n.forEach=function(a,b){var c=this;b=yc(b);return new b(function(d,e){var f=c.subscribe(function(g){try{a(g)}catch(h){e(h),f==null||f.unsubscribe()}},e,d)})};n.Aa=function(a){var b;return(b=this.source)==null?void 0:b.subscribe(a)};K.prototype[vc]=function(){return this};K.prototype.g=function(){var a=B.apply(0,arguments);return a.length?xc(a)(this):this};K.create=function(a){return new K(a)};function yc(a){var b;return(b=a!=null?a:void 0)!=null?b:Promise};var zc=jc(function(a){return function(){a(this);this.name="ObjectUnsubscribedError";this.message="object unsubscribed"}});var L=function(){this.Mb=[];this.ed=this.P=this.closed=!1;this.Ke=null};q(L,K);n=L.prototype;n.ob=function(a){var b=new Ac(this,this);b.operator=a;return b};n.Xa=function(){if(this.closed)throw new zc;};n.next=function(a){this.Xa();if(!this.P){var b=this.Mb.slice();b=w(b);for(var c=b.next();!c.done;c=b.next())c.value.next(a)}};n.error=function(a){this.Xa();if(!this.P){this.ed=this.P=!0;this.Ke=a;for(var b=this.Mb;b.length;)b.shift().error(a)}};
n.complete=function(){this.Xa();if(!this.P){this.P=!0;for(var a=this.Mb;a.length;)a.shift().complete()}};n.unsubscribe=function(){this.P=this.closed=!0;this.Mb=null};n.Id=function(a){this.Xa();return K.prototype.Id.call(this,a)};n.Aa=function(a){this.Xa();this.Ye(a);return this.bf(a)};n.bf=function(a){var b=this,c=this.P,d=this.Mb;return this.ed||c?mc.EMPTY:(d.push(a),new mc(function(){return lc(b.Mb,a)}))};n.Ye=function(a){var b=this.Ke,c=this.P;this.ed?a.error(b):c&&a.complete()};
n.R=function(){var a=new K;a.source=this;return a};L.create=function(a,b){return new Ac(a,b)};var Ac=function(a,b){L.call(this);this.destination=a;this.source=b};q(Ac,L);Ac.create=L.create;Ac.prototype.next=function(a){var b,c;(b=this.destination)==null||(c=b.next)==null||c.call(b,a)};Ac.prototype.error=function(a){var b,c;(b=this.destination)==null||(c=b.error)==null||c.call(b,a)};Ac.prototype.complete=function(){var a,b;(a=this.destination)==null||(b=a.complete)==null||b.call(a)};
Ac.prototype.Aa=function(a){var b,c;return(c=(b=this.source)==null?void 0:b.subscribe(a))!=null?c:mc.EMPTY};var Bc=function(a){L.call(this);this.Jd=a};q(Bc,L);Bc.create=L.create;Bc.prototype.Aa=function(a){var b=L.prototype.Aa.call(this,a);!b.closed&&a.next(this.Jd);return b};Bc.prototype.getValue=function(){var a=this.Ke,b=this.Jd;if(this.ed)throw a;this.Xa();return b};Bc.prototype.next=function(a){L.prototype.next.call(this,this.Jd=a)};ea.Object.defineProperties(Bc.prototype,{value:{configurable:!0,enumerable:!0,get:function(){return this.getValue()}}});var Cc=new K(function(a){return a.complete()});function Dc(a,b){return new K(function(c){var d=0;return b.D(function(){d===a.length?c.complete():(c.next(a[d++]),c.closed||this.D())})})};function Fc(a,b){if(!a)throw Error("r");return new K(function(c){var d=new mc;d.add(b.D(function(){var e=a[Symbol.asyncIterator]();d.add(b.D(function(){var f=this;e.next().then(function(g){g.done?c.complete():(c.next(g.value),f.D())})}))}));return d})};var Hc=typeof Symbol==="function"&&Symbol.iterator?Symbol.iterator:"@@iterator";function Ic(a,b,c){b=b.D(function(){try{c.call(this)}catch(d){a.error(d)}},0);a.add(b)};function Jc(a,b){return new K(function(c){var d;c.add(b.D(function(){d=a[Hc]();Ic(c,b,function(){var e=d.next(),f=e.value;e.done?c.complete():(c.next(f),this.D())})}));return function(){var e;return I((e=d)==null?void 0:e.return)&&d.return()}})};function Kc(a,b){return new K(function(c){var d=new mc;d.add(b.D(function(){var e=a[vc]();d.add(e.subscribe({next:function(f){d.add(b.D(function(){return c.next(f)}))},error:function(f){d.add(b.D(function(){return c.error(f)}))},complete:function(){d.add(b.D(function(){return c.complete()}))}}))}));return d})};function Lc(a,b){return new K(function(c){return b.D(function(){return a.then(function(d){c.add(b.D(function(){c.next(d);c.add(b.D(function(){return c.complete()}))}))},function(d){c.add(b.D(function(){return c.error(d)}))})})})};var Mc=function(a){return a&&typeof a.length==="number"&&typeof a!=="function"};function Nc(a){return new TypeError("s`"+(a!==null&&typeof a==="object"?"an invalid object":"'"+a+"'"))};function Oc(a,b){if(a!=null){if(I(a[vc]))return Kc(a,b);if(Mc(a))return Dc(a,b);if(I(a==null?void 0:a.then))return Lc(a,b);if(Symbol.asyncIterator&&I(a==null?void 0:a[Symbol.asyncIterator]))return Fc(a,b);if(I(a==null?void 0:a[Hc]))return Jc(a,b)}throw Nc(a);};function Pc(a,b){return b?Oc(a,b):Qc(a)}function Qc(a){if(a instanceof K)return a;if(a!=null){if(I(a[vc]))return Rc(a);if(Mc(a))return Sc(a);if(I(a==null?void 0:a.then))return Tc(a);if(Symbol.asyncIterator&&I(a==null?void 0:a[Symbol.asyncIterator]))return Uc(a);if(I(a==null?void 0:a[Hc]))return Vc(a)}throw Nc(a);}function Rc(a){return new K(function(b){var c=a[vc]();if(I(c.subscribe))return c.subscribe(b);throw new TypeError("t");})}
function Sc(a){return new K(function(b){for(var c=0;c<a.length&&!b.closed;c++)b.next(a[c]);b.complete()})}function Tc(a){return new K(function(b){a.then(function(c){b.closed||(b.next(c),b.complete())},function(c){return b.error(c)}).then(null,rc)})}function Vc(a){return new K(function(b){for(var c=a[Hc]();!b.closed;){var d=c.next(),e=d.value;d.done?b.complete():b.next(e)}return function(){return I(c==null?void 0:c.return)&&c.return()}})}
function Uc(a){return new K(function(b){Wc(a,b).catch(function(c){return b.error(c)})})}
function Wc(a,b){var c,d,e,f,g,h;return Fa(function(k){switch(k.o){case 1:ua(k,2,3);var l=a[Symbol.asyncIterator];f=l!==void 0?l.call(a):new Ga(w(a));case 5:return ta(k,f.next(),8);case 8:d=k.aa;if(d.done){k.Ia(3);break}g=d.value;b.next(g);k.Ia(5);break;case 3:wa(k);k.ua=0;k.Ea=9;if(!d||d.done||!(e=f.return)){k.Ia(9);break}return ta(k,e.call(f),9);case 9:wa(k,0,0,1);if(c)throw c.error;xa(k,10,1);break;case 10:xa(k,4);break;case 2:h=va(k);c={error:h};k.Ia(3);break;case 4:b.complete(),k.o=0}})};function Xc(a,b){return b?Dc(a,b):Sc(a)};function Yc(a){return I(a[a.length-1])?a.pop():void 0}function Zc(a){var b=a[a.length-1];return b&&I(b.D)?a.pop():void 0};function M(){var a=B.apply(0,arguments),b=Zc(a);return b?Dc(a,b):Xc(a)};function $c(a){var b=I(a)?a:function(){return a};return new K(function(c){return c.error(b())})};var ad={now:function(){return(ad.gh||Date).now()},gh:void 0};var bd=function(a,b,c){a=a===void 0?Infinity:a;b=b===void 0?Infinity:b;c=c===void 0?ad:c;L.call(this);this.bufferSize=a;this.Fg=b;this.zg=c;this.buffer=[];this.ke=b===Infinity;this.bufferSize=Math.max(1,a);this.Fg=Math.max(1,b)};q(bd,L);bd.create=L.create;bd.prototype.next=function(a){var b=this.buffer,c=this.ke,d=this.zg,e=this.Fg;this.P||(b.push(a),!c&&b.push(d.now()+e));cd(this);L.prototype.next.call(this,a)};
bd.prototype.Aa=function(a){this.Xa();cd(this);for(var b=this.bf(a),c=this.ke,d=this.buffer.slice(),e=0;e<d.length&&!a.closed;e+=c?1:2)a.next(d[e]);this.Ye(a);return b};var cd=function(a){var b=a.bufferSize,c=a.zg,d=a.buffer;a=a.ke;var e=(a?1:2)*b;b<Infinity&&e<d.length&&d.splice(0,d.length-e);if(!a){b=c.now();c=0;for(a=1;a<d.length&&d[a]<=b;a+=2)c=a;c&&d.splice(0,c+1)}};var ed=function(a,b){b=b===void 0?dd:b;this.xi=a;this.now=b};ed.prototype.D=function(a,b,c){b=b===void 0?0:b;return(new this.xi(this,a)).D(c,b)};var dd=ad.now;var fd=jc(function(a){return function(){a(this);this.name="EmptyError";this.message="no elements in sequence"}});function gd(a){return new Promise(function(b,c){var d=new uc({next:function(e){b(e);d.unsubscribe()},error:c,complete:function(){c(new fd)}});a.subscribe(d)})};var hd=function(a,b,c,d,e){sc.call(this,a);this.ei=e;b&&(this.Gd=function(f){try{b(f)}catch(g){this.destination.error(g)}});c&&(this.Ze=function(f){try{c(f)}catch(g){this.destination.error(g)}this.unsubscribe()});d&&(this.Mc=function(){try{d()}catch(f){this.destination.error(f)}this.unsubscribe()})};q(hd,sc);hd.EMPTY=sc.EMPTY;hd.create=sc.create;hd.prototype.unsubscribe=function(){var a;this.closed||(a=this.ei)!=null&&a.call(this);sc.prototype.unsubscribe.call(this)};function id(a){return function(b){if(I(b==null?void 0:b.ob))return b.ob(function(c){try{return a(c,this)}catch(d){this.error(d)}});throw new TypeError("u");}};function jd(){return id(function(a,b){var c=null;a.Nc++;var d=new hd(b,void 0,void 0,void 0,function(){if(!a||a.Nc<=0||0<--a.Nc)c=null;else{var e=a.yb,f=c;c=null;!e||f&&e!==f||e.unsubscribe();b.unsubscribe()}});a.subscribe(d);d.closed||(c=a.connect())})};var kd=function(a,b){this.source=a;this.tg=b;this.Oc=null;this.Nc=0;this.yb=null};q(kd,K);kd.create=K.create;kd.prototype.Aa=function(a){return ld(this).subscribe(a)};var ld=function(a){var b=a.Oc;if(!b||b.P)a.Oc=a.tg();return a.Oc};kd.prototype.Hd=function(){this.Nc=0;var a=this.yb;this.Oc=this.yb=null;a==null||a.unsubscribe()};
kd.prototype.connect=function(){var a=this,b=this.yb;if(!b){b=this.yb=new mc;var c=ld(this);b.add(this.source.subscribe(new hd(c,void 0,function(d){a.Hd();c.error(d)},function(){a.Hd();c.complete()},function(){return a.Hd()})));b.closed&&(this.yb=null,b=mc.EMPTY)}return b};function md(){var a=nd;var b=b===void 0?0:b;return id(function(c,d){d.add(a.D(function(){return c.subscribe(d)},b))})};function N(a){return id(function(b,c){var d=0;b.subscribe(new hd(c,function(e){c.next(a.call(void 0,e,d++))}))})};var od=Array.isArray;function pd(a){return N(function(b){return od(b)?a.apply(null,z(b)):a(b)})};var rd=Array.isArray,sd=Object,td=sd.getPrototypeOf,ud=sd.prototype,vd=sd.keys;function wd(a){if(a.length===1){var b=a[0];if(rd(b))return{args:b,keys:null};if(b&&typeof b==="object"&&td(b)===ud)return a=vd(b),{args:a.map(function(c){return b[c]}),keys:a}}return{args:a,keys:null}};function O(){var a=B.apply(0,arguments),b=Zc(a),c=Yc(a);a=wd(a);var d=a.args,e=a.keys;if(d.length===0)return Pc([],b);b=new K(xd(d,b,e?function(f){for(var g={},h=0;h<f.length;h++)g[e[h]]=f[h];return g}:wc));return c?b.g(pd(c)):b}var yd=function(a,b,c){sc.call(this,a);this.Gd=b;this.Ci=c};q(yd,sc);yd.EMPTY=sc.EMPTY;yd.create=sc.create;yd.prototype.Mc=function(){this.Ci()?sc.prototype.Mc.call(this):this.unsubscribe()};
function xd(a,b,c){c=c===void 0?wc:c;return function(d){zd(b,function(){for(var e=a.length,f=Array(e),g=e,h=a.map(function(){return!1}),k=!0,l={ib:0};l.ib<e;l={ib:l.ib},l.ib++)zd(b,function(m){return function(){Pc(a[m.ib],b).subscribe(new yd(d,function(t){f[m.ib]=t;k&&(h[m.ib]=!0,k=!h.every(wc));k||d.next(c(f.slice()))},function(){return--g===0}))}}(l),d)},d)}}function zd(a,b,c){a?c.add(a.D(b)):b()};function Ad(a,b,c,d){var e=[],f=0,g=0,h=!1,k=function(l){f++;Qc(c(l,g++)).subscribe(new hd(b,function(m){b.next(m)},void 0,function(){f--;for(var m={};e.length&&f<d;m={jf:void 0})m.jf=e.shift(),k(m.jf);!h||e.length||f||b.complete()}))};a.subscribe(new hd(b,function(l){return f<d?k(l):e.push(l)},void 0,function(){h=!0;!h||e.length||f||b.complete()}));return function(){e=null}};function Bd(a,b){var c=c===void 0?Infinity:c;if(I(b))return Bd(function(d,e){return N(function(f,g){return b(d,f,e,g)})(Qc(a(d,e)))},c);typeof b==="number"&&(c=b);return id(function(d,e){return Ad(d,e,a,c)})};function Cd(a){a=a===void 0?Infinity:a;return Bd(wc,a)};function Dd(){var a=B.apply(0,arguments);return Cd(1)(Xc(a,Zc(a)))};function Ed(a){return new K(function(b){Qc(a()).subscribe(b)})};var Fd=["addListener","removeListener"],Gd=["addEventListener","removeEventListener"],Hd=["on","off"];
function Id(a,b,c){if(I(c)){var d=c;c=void 0}if(d)return Id(a,b,c).g(pd(d));d=w(I(a.addEventListener)&&I(a.removeEventListener)?Gd.map(function(g){return function(h){return a[g](b,h,c)}}):I(a.addListener)&&I(a.removeListener)?Fd.map(Jd(a,b)):I(a.Tj)&&I(a.Gj)?Hd.map(Jd(a,b)):[]);var e=d.next().value,f=d.next().value;return!e&&Mc(a)?Bd(function(g){return Id(g,b,c)})(Xc(a)):new K(function(g){if(!e)throw new TypeError("v");var h=function(){var k=B.apply(0,arguments);return g.next(1<k.length?k:k[0])};
e(h);return function(){return f(h)}})}function Jd(a,b){return function(c){return function(d){return a[c](b,d)}}};var Kd=function(){mc.call(this)};q(Kd,mc);Kd.EMPTY=mc.EMPTY;Kd.prototype.D=function(){return this};var Ld=function(a,b){return setInterval.apply(null,[a,b].concat(z(B.apply(2,arguments))))};var Md=function(a,b){mc.call(this);this.scheduler=a;this.Se=b;this.pending=!1};q(Md,Kd);Md.EMPTY=Kd.EMPTY;Md.prototype.D=function(a,b){b=b===void 0?0:b;if(this.closed)return this;this.state=a;a=this.id;var c=this.scheduler;a!=null&&(this.id=Nd(this,a,b));this.pending=!0;this.delay=b;this.id=this.id||this.Fe(c,this.id,b);return this};Md.prototype.Fe=function(a,b,c){c=c===void 0?0:c;return Ld(a.flush.bind(a,this),c)};
var Nd=function(a,b,c){c=c===void 0?0:c;if(c!=null&&a.delay===c&&a.pending===!1)return b;clearInterval(b)};Md.prototype.execute=function(a,b){if(this.closed)return Error("w");this.pending=!1;if(a=this.af(a,b))return a;this.pending===!1&&this.id!=null&&(this.id=Nd(this,this.id,null))};Md.prototype.af=function(a){var b=!1;try{this.Se(a)}catch(d){b=!0;var c=!!d&&d||Error(d)}if(b)return this.unsubscribe(),c};
Md.prototype.unsubscribe=function(){if(!this.closed){var a=this.id,b=this.scheduler.actions;this.Se=this.state=this.scheduler=null;this.pending=!1;lc(b,this);a!=null&&(this.id=Nd(this,a,null));this.delay=null;Kd.prototype.unsubscribe.call(this)}};var Od=function(a,b){b=b===void 0?dd:b;ed.call(this,a,b);this.actions=[];this.active=!1};q(Od,ed);Od.prototype.flush=function(a){var b=this.actions;if(this.active)b.push(a);else{var c;this.active=!0;do if(c=a.execute(a.state,a.delay))break;while(a=b.shift());this.active=!1;if(c){for(;a=b.shift();)a.unsubscribe();throw c;}}};function Pd(){var a=B.apply(0,arguments),b=Zc(a);var c=typeof a[a.length-1]==="number"?a.pop():Infinity;return a.length?a.length===1?Qc(a[0]):Cd(c)(Xc(a,b)):Cc};var Qd=new K(qc);var Rd=Array.isArray;function Sd(a){return a.length===1&&Rd(a[0])?a[0]:a};function Td(){var a=Sd(B.apply(0,arguments));return id(function(b,c){var d=[b].concat(z(a)),e=function(){if(!c.closed)if(d.length>0){try{var f=Qc(d.shift())}catch(h){e();return}var g=new hd(c,void 0,qc,qc);c.add(f.subscribe(g));g.add(e)}else c.complete()};e()})};function Q(a){return id(function(b,c){var d=0;b.subscribe(new hd(c,function(e){return a.call(void 0,e,d++)&&c.next(e)}))})};function Ud(){var a=B.apply(0,arguments);a=Sd(a);return a.length===1?Qc(a[0]):new K(Vd(a))}function Vd(a){return function(b){for(var c=[],d={Db:0};c&&!b.closed&&d.Db<a.length;d={Db:d.Db},d.Db++)c.push(Qc(a[d.Db]).subscribe(new hd(b,function(e){return function(f){if(c){for(var g=0;g<c.length;g++)g!==e.Db&&c[g].unsubscribe();c=null}b.next(f)}}(d))))}};function Wd(){var a=B.apply(0,arguments),b=Yc(a),c=Sd(a);return c.length?new K(function(d){var e=c.map(function(){return[]}),f=c.map(function(){return!1});d.add(function(){e=f=null});for(var g={Ua:0};!d.closed&&g.Ua<c.length;g={Ua:g.Ua},g.Ua++)Qc(c[g.Ua]).subscribe(new hd(d,function(h){return function(k){e[h.Ua].push(k);e.every(function(l){return l.length})&&(k=e.map(function(l){return l.shift()}),d.next(b?b.apply(null,z(k)):k),e.some(function(l,m){return!l.length&&f[m]})&&d.complete())}}(g),void 0,
function(h){return function(){f[h.Ua]=!0;!e[h.Ua].length&&d.complete()}}(g)));return function(){e=f=null}}):Cc};jc(function(a){return function(b){b=b===void 0?null:b;a(this);this.message="Timeout has occurred";this.name="TimeoutError";this.info=b}});var Xd=function(a,b){Md.call(this,a,b);this.scheduler=a;this.Se=b};q(Xd,Md);Xd.EMPTY=Md.EMPTY;Xd.prototype.D=function(a,b){b=b===void 0?0:b;if(b>0)return Md.prototype.D.call(this,a,b);this.delay=b;this.state=a;this.scheduler.flush(this);return this};Xd.prototype.execute=function(a,b){return b>0||this.closed?Md.prototype.execute.call(this,a,b):this.af(a,b)};Xd.prototype.Fe=function(a,b,c){c=c===void 0?0:c;return c!=null&&c>0||c==null&&this.delay>0?Md.prototype.Fe.call(this,a,b,c):a.flush(this)};var Yd=function(){Od.apply(this,arguments)};q(Yd,Od);var nd=new Yd(Xd);jc(function(a){return function(){a(this);this.name="ArgumentOutOfRangeError";this.message="argument out of range"}});jc(function(a){return function(b){a(this);this.name="NotFoundError";this.message=b}});jc(function(a){return function(b){a(this);this.name="SequenceError";this.message=b}});var Zd=function(){this.F=new Ta;this.h=new Ua;this.Gh=Symbol();this.hc=new ic};Zd.prototype.ee=function(){return Qd};var $d=function(a,b){a.Ca!==null&&a.Ca.next(b)},ae=function(a){if((typeof a==="bigint"||typeof a==="number"||typeof a==="string")&&typeof BigInt==="function")return BigInt(a)};ea.Object.defineProperties(Zd.prototype,{ub:{configurable:!0,enumerable:!0,get:function(){return this.Gh}}});var be=function(a,b){b=Error.call(this,b?a+": "+b:String(a));this.message=b.message;"stack"in b&&(this.stack=b.stack);this.code=a;this.__proto__=be.prototype;this.name=String(a)};q(be,Error);var ce=function(a){be.call(this,1E3,'sfr:"'+a+'"');this.Uh=a;this.__proto__=ce.prototype};q(ce,be);var de=function(){be.call(this,1003);this.__proto__=de.prototype};q(de,be);var ee=function(){be.call(this,1009);this.__proto__=ee.prototype};q(ee,be);var fe=function(){be.call(this,1011);this.__proto__=fe.prototype};
q(fe,be);var ge=function(){be.call(this,1007);this.__proto__=de.prototype};q(ge,be);var he=function(){be.call(this,1008);this.__proto__=de.prototype};q(he,be);var ie=function(){be.call(this,1001);this.__proto__=ie.prototype};q(ie,be);var je=function(a){be.call(this,1004,String(a));this.Ch=a;this.__proto__=je.prototype};q(je,be);var le=function(a){be.call(this,1010,a);this.__proto__=ke.prototype};q(le,be);var ke=function(a){be.call(this,1005,a);this.__proto__=ke.prototype};q(ke,be);var me=function(a){var b=B.apply(1,arguments),c=this;this.Ob=[];this.Ob.push(a);b.forEach(function(d){c.Ob.push(d)})};me.prototype.K=function(a){return this.Ob.some(function(b){return b.K(a)})};me.prototype.H=function(a,b){for(var c=0;c<this.Ob.length;c++)if(this.Ob[c].K(b))return this.Ob[c].H(a,b);throw new ee;};function ne(a){var b,c,d;return!!a&&typeof a.active==="boolean"&&typeof((b=a.clock)==null?void 0:b.now)==="function"&&((c=a.clock)==null?void 0:c.timeline)!==void 0&&!((d=a.C)==null||!d.timestamp)&&typeof a.Y==="function"&&typeof a.ia==="function"&&typeof a.pa==="function"&&typeof a.map==="function"&&typeof a.ra==="function"};var oe=Symbol("time-origin"),pe=Symbol("date"),qe=function(a,b){this.value=a;this.timeline=b},re=function(a,b){if(b.timeline!==a.timeline)throw new ge;},se=function(a,b){re(a,b);return a.value-b.value};n=qe.prototype;n.equals=function(a){return se(this,a)===0};n.maximum=function(a){re(this,a);return this.value>=a.value?this:a};n.round=function(){return new qe(Math.round(this.value),this.timeline)};n.add=function(a){return new qe(this.value+a,this.timeline)};n.toString=function(){return String(this.value)};function te(a){function b(c){return typeof c==="boolean"||typeof c==="string"||typeof c==="number"||c===void 0||c===null}return b(a)?!0:Array.isArray(a)?a.every(b):typeof a==="object"?Object.keys(a).every(function(c){return typeof c==="string"})&&Object.values(a).every(function(c){return Array.isArray(c)?c.every(b):b(c)}):!1}
function ue(a){if(te(a))return a;if(ne(a))return{C:{value:ue(a.C.value),timestamp:se(a.C.timestamp,new qe(0,a.C.timestamp.timeline))},active:a.active};try{return JSON.parse(JSON.stringify(a))}catch(b){}return String(a)};var ve={Ti:"app",tj:"web"};var we=["sessionStart","sessionError","sessionFinish"],xe=function(a,b){this.ca=a;this.Cd=b;this.ready=!1;this.pb=[];this.ng=function(){};this.Dg=function(){};this.Af=function(){};this.Mf=function(){};this.sd=function(){}},Ae=function(a,b){a.ng=b},Be=function(a,b){a.Dg=b},Ce=function(a,b){a.Af=b},De=function(a,b){a.Mf=b},Ee=function(a,b){a.sd=b;a.sd(a.pb.length)},Je=function(a){for(var b=w("geometryChange impression loaded start firstQuartile midpoint thirdQuartile complete pause resume bufferStart bufferFinish skipped volumeChange playerStateChange adUserInteraction".split(" ")),
c=b.next();!c.done;c=b.next())a.ca.addEventListener(c.value,function(d){Fe(a,d)});Ge(a.ca,function(d){d.type!=="sessionStart"&&Fe(a,d)},a.Cd);Ge(a.ca,function(d){d.type==="sessionStart"&&(Fe(a,d),He(a),Ie(a))},a.Cd)},Fe=function(a,b){a.pb.push(b);a.sd(a.pb.length);Ie(a)},Ie=function(a){if(a.ready)for(;a.pb.length>0;){var b=a.pb.pop();b!==void 0&&(b.type==="geometryChange"?a.Af(b):b.type==="impression"?a.Mf(b):we.includes(b.type)?a.ng(b):a.Dg(b));a.sd(a.pb.length)}},He=function(a){a.ready||(a.ready=
!0,a.pb.sort(function(b,c){return c.timestamp-b.timestamp}))};function Ke(a){return id(function(b,c){var d=null,e=!1,f;d=b.subscribe(new hd(c,void 0,function(g){f=Qc(a(g,Ke(a)(b)));d?(d.unsubscribe(),d=null,f.subscribe(c)):e=!0}));e&&(d.unsubscribe(),d=null,f.subscribe(c))})};function Le(a,b,c){return function(d,e){var f=c,g=b,h=0;d.subscribe(new hd(e,function(k){var l=h++;g=f?a(g,k,l):(f=!0,k);e.next(g)},void 0,void 0))}};function Me(){var a=B.apply(0,arguments),b=Yc(a);return b?J(Me.apply(null,z(a)),pd(b)):id(function(c,d){xd([c].concat(z(Sd(a))))(d)})}function Ne(){return Me.apply(null,z(B.apply(0,arguments)))};function Oe(a){a=a===void 0?null:a;return id(function(b,c){var d=!1;b.subscribe(new hd(c,function(e){d=!0;c.next(e)},void 0,function(){d||c.next(a);c.complete()}))})};function Pe(){return id(function(a,b){a.subscribe(new hd(b,qc))})};function Qe(a){return id(function(b,c){b.subscribe(new hd(c,function(){return c.next(a)}))})};function Re(a){return a<=0?function(){return Cc}:id(function(b,c){var d=0;b.subscribe(new hd(c,function(e){++d<=a&&(c.next(e),a<=d&&c.complete())}))})};function Se(a){return Bd(function(b,c){return a(b,c).g(Re(1),Qe(b))})};function Te(a){return id(function(b,c){var d=new Set;b.subscribe(new hd(c,function(e){var f=a?a(e):e;d.has(f)||(d.add(f),c.next(e))}))})};function R(a){var b=b===void 0?wc:b;var c;a=(c=a)!=null?c:Ue;return id(function(d,e){var f,g=!0;d.subscribe(new hd(e,function(h){var k=b(h);if(g||!a(f,k))g=!1,f=k,e.next(h)}))})}function Ue(a,b){return a===b};function Ve(a){a=a===void 0?We:a;return id(function(b,c){var d=!1;b.subscribe(new hd(c,function(e){d=!0;c.next(e)},void 0,function(){return d?c.complete():c.error(a())}))})}function We(){return new fd};function Xe(){var a=B.apply(0,arguments);return function(b){return Dd(b,M.apply(null,z(a)))}};function Ye(a){return id(function(b,c){var d=0;b.subscribe(new hd(c,function(e){a.call(void 0,e,d++,b)||(c.next(!1),c.complete())},void 0,function(){c.next(!0);c.complete()}))})};function Ze(){return id(function(a,b){var c=[];a.subscribe(new hd(b,function(d){c.push(d);1<c.length&&c.shift()},void 0,function(){for(var d=w(c),e=d.next();!e.done;e=d.next())b.next(e.value);b.complete()},function(){c=null}))})};function $e(a,b){var c=arguments.length>=2;return function(d){return d.g(a?Q(function(e,f){return a(e,f,d)}):wc,Ze(),c?Oe(b):Ve(function(){return new fd}))}};function af(a){var b=I(a)?a:function(){return a};return I()?id(function(c,d){var e=b();(void 0)(e).subscribe(d).add(c.subscribe(e))}):function(c){var d=new kd(c,b);I(c==null?void 0:c.ob)&&(d.ob=c.ob);d.source=c;d.tg=b;return d}};function bf(a){var b=new bd(a,void 0,void 0);return function(c){return af(function(){return b})(c)}};function cf(){var a=a===void 0?Infinity:a;return a<=0?function(){return Cc}:id(function(b,c){var d=0,e,f=function(){var g=!1;e=b.subscribe(new hd(c,void 0,void 0,function(){++d<a?e?(e.unsubscribe(),e=null,f()):g=!0:c.complete()}));g&&(e.unsubscribe(),e=null,f())};f()})};function df(a,b){return id(Le(a,b,arguments.length>=2))};function ef(){var a=a||{};var b=a.Zg===void 0?function(){return new L}:a.Zg,c=a.pi===void 0?!0:a.pi,d=a.ri===void 0?!0:a.ri,e=a.si===void 0?!0:a.si;return function(f){var g=null,h=null,k=0,l=!1,m=!1,t=function(){g=h=null;l=m=!1};return id(function(r,u){k++;var x;h=(x=h)!=null?x:b();u.add(function(){k--;if(e&&!k&&!m&&!l){var v=g;t();v==null||v.unsubscribe()}});h.subscribe(u);!g&&k>0&&(g=new uc({next:function(v){return h.next(v)},error:function(v){m=!0;var y=h;d&&t();y.error(v)},complete:function(){l=
!0;var v=h;c&&t();v.complete()}}),Pc(r).subscribe(g))})(f)}};function T(){var a=B.apply(0,arguments),b=Zc(a);return id(function(c,d){(b?Dd(a,c,b):Dd(a,c)).subscribe(d)})};function U(a){return id(function(b,c){var d=null,e=0,f=!1;b.subscribe(new hd(c,function(g){var h;(h=d)==null||h.unsubscribe();h=e++;Qc(a(g,h)).subscribe(d=new hd(c,function(k){return c.next(k)},void 0,function(){d=null;f&&!d&&c.complete()}))},void 0,function(){(f=!0,!d)&&c.complete()}))})};function ff(a,b){b=b===void 0?!1:b;return id(function(c,d){var e=0;c.subscribe(new hd(d,function(f){var g=a(f,e++);(g||b)&&d.next(f);!g&&d.complete()}))})};function gf(a,b,c){var d=I(a)||b||c?{next:a,error:b,complete:c}:a;return d?id(function(e,f){e.subscribe(new hd(f,function(g){var h;(h=d.next)==null||h.call(d,g);f.next(g)},function(g){var h;(h=d.error)==null||h.call(d,g);f.error(g)},function(){var g;(g=d.complete)==null||g.call(d);f.complete()}))}):wc};function hf(){var a=B.apply(0,arguments),b=Yc(a);return id(function(c,d){for(var e=a.length,f=Array(e),g=a.map(function(){return!1}),h=!1,k={Pa:0};k.Pa<e;k={Pa:k.Pa},k.Pa++)Qc(a[k.Pa]).subscribe(new hd(d,function(l){return function(m){f[l.Pa]=m;h||g[l.Pa]||(g[l.Pa]=!0,(h=g.every(wc))&&(g=null))}}(k),void 0,qc));c.subscribe(new hd(d,function(l){h&&(l=[l].concat(z(f)),d.next(b?b.apply(null,z(l)):l))}))})};var jf=function(a){this.ca=a};jf.prototype.K=function(a){return(a==null?0:a.Yb)?!0:(a==null?void 0:a.ga)==="POST"||(a==null?0:a.eb)||(a==null?0:a.Yc)?!1:this.ca.K()};jf.prototype.ping=function(){var a=this,b=M.apply(null,z(B.apply(0,arguments))).g(Bd(function(c){return kf(a,c)}),Ye(function(c){return c}),bf(1));b.connect();return b};var kf=function(a,b){var c=new bd(1);lf(a.ca,b,function(){c.next(!0);c.complete()},function(){c.next(!1);c.complete()});return c};
jf.prototype.rd=function(a,b,c){this.ping.apply(this,z(B.apply(3,arguments)))};function mf(a,b){var c=!1;return new K(function(d){var e=a.setTimeout(function(){c=!0;d.next(!0);d.complete()},b);return function(){c||a.clearTimeout(e)}})};var nf=function(a){this.ca=a;this.timeline=pe};n=nf.prototype;n.setTimeout=function(a,b){return Number(this.ca.setTimeout(function(){return a()},b))};n.clearTimeout=function(a){this.ca.clearTimeout(a)};n.now=function(){return new qe(Date.now(),this.timeline)};n.interval=function(a,b){var c=this.Ha(a).subscribe(b);return function(){return void c.unsubscribe()}};n.Ha=function(a){return mf(this,a).g(cf(),df(function(b){return b+1},-1))};n.fa=function(){return!0};var of=function(a,b){this.context=a;this.Pb=b};of.prototype.K=function(a){return this.Pb.K(a)};of.prototype.H=function(a,b){if(!this.K(b))throw new ee;return new pf(this.context,this.Pb,b!=null?b:void 0,a)};var pf=function(a,b,c,d){var e=this;this.Pb=b;this.properties=c;this.url=d;this.jd=!0;this.eb=new Map;this.body=void 0;var f;this.method=(f=c==null?void 0:c.ga)!=null?f:"GET";this.Tg=a.ee().subscribe(function(){e.sendNow()})};pf.prototype.deactivate=function(){this.jd=!1};
pf.prototype.sendNow=function(){if(this.jd)if(this.Tg.unsubscribe(),this.Pb.K(this.properties))try{if(this.eb.size>0||this.body!==void 0){var a,b;this.Pb.rd((a=this.properties)!=null?a:{},this.eb,(b=this.body)!=null?b:"",this.url)}else this.Pb.ping(this.url);this.jd=!1}catch(c){}else this.jd=!1};var rf=function(a,b,c,d,e,f){this.mode=a;this.j=b;this.setTime=c;this.Cc=d;this.Ii=e;this.Yg=f;this.completed=!1;this.id=this.mode===0?qf(this):0},qf=function(a){return a.j.setTimeout(function(){sf(a)},a.Cc)},tf=function(a,b){var c=se(b,a.setTime);c>=a.Cc?sf(a):(a.setTime=b,a.Cc-=c)},sf=function(a){try{a.Ii(a.setTime.add(a.Cc))}finally{a.completed=!0,a.Yg()}};
rf.prototype.Oe=function(a,b){this.completed||(this.mode===1&&a===1?tf(this,b):this.mode===1&&a===0?(this.mode=a,tf(this,this.j.now()),this.completed||(this.id=qf(this))):this.mode===0&&a===1&&(this.mode=a,this.clear(),tf(this,b)))};rf.prototype.clear=function(){this.completed||this.j.clearTimeout(this.id)};var uf=function(a){this.Zc=a;this.Eh=this.mode=0;this.Hb={};this.timeline=a.timeline;this.nb=a.now()};n=uf.prototype;
n.Oe=function(a,b){this.mode=a;re(this.nb,b);this.nb=b;Object.values(this.Hb).forEach(function(c){return void c.Oe(a,b)})};n.now=function(){return this.mode===1?this.nb:this.Zc.now()};n.setTimeout=function(a,b){var c=this,d=++this.Eh,e=this.mode===1?this.nb:this.Zc.now();this.Hb[d]=new rf(this.mode,this.Zc,e,b,function(f){var g=c.nb;c.mode===1&&(c.nb=f);a();c.nb=g},function(){delete c.Hb[d]});return d};n.clearTimeout=function(a){this.Hb[a]&&(this.Hb[a].clear(),delete this.Hb[a])};
n.interval=function(){throw Error("x");};n.Ha=function(){throw Error("y");};n.fa=function(){return this.Zc.fa()};function vf(a,b){var c=new uf(a);a=b.subscribe(function(d){c.Oe(d.value?1:0,d.timestamp)});return{j:c,Fj:a}};function wf(a){var b=Object.assign({},a);delete b.timestamp;return{timestamp:new qe(a.timestamp,pe),value:b}};function xf(a){return a!==void 0&&typeof a.x==="number"&&typeof a.y==="number"&&typeof a.width==="number"&&typeof a.height==="number"};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function yf(a){var b=B.apply(1,arguments),c=b.length;if(!Array.isArray(a)||!Array.isArray(a.raw)||a.length!==a.raw.length||!rb&&a===a.raw||!(rb&&!sb||pb(a))||c+1!==a.length)throw new TypeError("p");if(b.length===0)return eb(a[0]);c=a[0].toLowerCase();if(/^data:/.test(c))throw Error("F");if(/^https:\/\//.test(c)||/^\/\//.test(c)){var d=c.indexOf("//")+2;var e=c.indexOf("/",d);if(e<=d)throw Error("z");d=c.substring(d,e);if(!/^[0-9a-z.:-]+$/i.test(d))throw Error("A");if(!/^[^:]*(:[0-9]+)?$/i.test(d))throw Error("B");
if(!/(^|\.)[a-z][^.]*$/i.test(d))throw Error("C");d=!0}else d=!1;if(!d)if(/^\//.test(c))if(c==="/"||c.length>1&&c[1]!=="/"&&c[1]!=="\\")d=!0;else throw Error("E");else d=!1;if(!(d=d||RegExp("^[^:\\s\\\\/]+/").test(c)))if(/^about:blank/.test(c)){if(c!=="about:blank"&&!/^about:blank#/.test(c))throw Error("D");d=!0}else d=!1;if(!d)throw Error("G");c=a[0];for(d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return eb(c)};var zf=pa(["https://www.googleadservices.com/pagead/managed/js/activeview/","/reach_worklet.html"]),Af=pa(["./reach_worklet.js"]),Bf=pa(["./reach_worklet.js"]),Cf=pa(["./reach_worklet.html"]),Df=pa(["./reach_worklet.js"]),Ef=pa(["./reach_worklet.js"]);function Ff(a){var b={};return b[0]=yf(zf,a),b[1]=yf(Af),b[2]=yf(Bf),b}yf(Cf);yf(Df);yf(Ef);var Hf=function(a,b,c,d){c=c===void 0?null:c;d=d===void 0?Ff("current"):d;Zd.call(this);this.ca=a;this.Cd=b;this.Ca=c;this.Ie=d;this.Ra=null;this.Ge=new bd(3);this.Ge.g(Q(function(e){return e.value.type==="sessionStart"}));this.yi=this.Ge.g(Q(function(e){return e.value.type==="sessionFinish"}));this.Nf=new bd(1);this.Mi=new bd;this.Bf=new bd(10);this.G=new of(this,new jf(a));this.Lh=this.ca.K();this.j=Gf(this,new nf(this.ca))};q(Hf,Zd);var If=function(a){a.Ra!==null&&Je(a.Ra)};
Hf.prototype.validate=function(){return this.Lh};
var Gf=function(a,b){a.Ra=new xe(a.ca,a.Cd);var c=new bd;Ae(a.Ra,function(f){f=wf(f);c.next({timestamp:f.timestamp,value:!0});a.Ge.next(f)});Ce(a.Ra,function(f){if(f===void 0)var g=!1;else{g=f.data;var h;(h=g===void 0)||(h=g.viewport,h=h===void 0||h!==void 0&&typeof h.width==="number"&&typeof h.height==="number");h?(g=g.adView,g=g!==void 0&&typeof g.percentageInView==="number"&&(g.geometry===void 0||xf(g.geometry))&&(g.onScreenGeometry===void 0||xf(g.onScreenGeometry))):g=!1}g?(f=wf(f),c.next({timestamp:f.timestamp,
value:!0}),a.Bf.next(f)):.01>=Math.random()&&(f="https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=error&name=invalid_geo&context=1092&msg="+JSON.stringify(f),a.G.H(f).sendNow())});Be(a.Ra,function(f){f=wf(f);c.next({timestamp:f.timestamp,value:!0});a.Mi.next(f)});De(a.Ra,function(f){f=wf(f);c.next({timestamp:f.timestamp,value:!0});a.Nf.next(f)});var d=0;Ee(a.Ra,function(f){d+=f;d>0&&f===0&&c.next({timestamp:a.j.now(),value:!1})});var e=c.g(ff(function(f){return f.value},!0));return vf(b,
e).j};ea.Object.defineProperties(Hf.prototype,{global:{configurable:!0,enumerable:!0,get:function(){return Jf}}});var Jf={};function Kf(a,b){if(!b)throw Error("H`"+a);if(typeof b!=="string"&&!(b instanceof String))throw Error("I`"+a);if(b.trim()==="")throw Error("J`"+a);}function Lf(a){if(!a)throw Error("M`functionToExecute");}function Mf(a,b){if(b==null)throw Error("K`"+a);if(typeof b!=="number"||isNaN(b))throw Error("L`"+a);if(b<0)throw Error("N`"+a);};function Nf(){return/\d+\.\d+\.\d+(-.*)?/.test("1.5.2-google_20241009")}function Of(){for(var a=["1","5","2"],b=["1","0","3"],c=0;c<3;c++){var d=parseInt(a[c],10),e=parseInt(b[c],10);if(d>e)break;else if(d<e)return!1}return!0};var Pf=function(a,b,c,d){this.Lf=a;this.method=b;this.version=c;this.args=d},Qf=function(a){return!!a&&a.omid_message_guid!==void 0&&a.omid_message_method!==void 0&&a.omid_message_version!==void 0&&typeof a.omid_message_guid==="string"&&typeof a.omid_message_method==="string"&&typeof a.omid_message_version==="string"&&(a.omid_message_args===void 0||a.omid_message_args!==void 0)},Rf=function(a){return new Pf(a.omid_message_guid,a.omid_message_method,a.omid_message_version,a.omid_message_args)};
Pf.prototype.Ta=function(){var a={};a=(a.omid_message_guid=this.Lf,a.omid_message_method=this.method,a.omid_message_version=this.version,a);this.args!==void 0&&(a.omid_message_args=this.args);return a};var Sf=function(a){this.wd=a};Sf.prototype.Ta=function(){return JSON.stringify(void 0)};function Tf(a,b){try{return a.frames&&!!a.frames[b]}catch(c){return!1}}var Uf=function(a){return["omid_v1_present","omid_v1_present_web","omid_v1_present_app"].some(function(b){return Tf(a,b)})},Vf=function(a){for(var b=w(Object.values(ve)),c=b.next();!c.done;c=b.next()){c=c.value;var d={};d=(d.app="omid_v1_present_app",d.web="omid_v1_present_web",d)[c];if(Tf(a,d))return c}return null};function Wf(a,b){return a&&(a[b]||(a[b]={}))};function Xf(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(a){var b=Math.random()*16|0;return a==="y"?(b&3|8).toString(16):b.toString(16)})};function Yf(){var a=B.apply(0,arguments);Zf(function(){throw new (Function.prototype.bind.apply(Error,[null,"Could not complete the test successfully - "].concat(z(a))));},function(){return console.error.apply(console,z(a))})}function Zf(a,b){typeof jasmine!=="undefined"&&jasmine?a():typeof console!=="undefined"&&console&&console.error&&b()};var $f=function(){if(typeof omidGlobal!=="undefined"&&omidGlobal)return omidGlobal;if(typeof global!=="undefined"&&global)return global;if(typeof window!=="undefined"&&window)return window;if(typeof globalThis!=="undefined"&&globalThis)return globalThis;var a=Function("return this")();if(a)return a;throw Error("O");}();var ag=function(a){this.wd=a;this.handleExportedMessage=ag.prototype.th.bind(this)};q(ag,Sf);ag.prototype.sendMessage=function(a,b){b=b===void 0?this.wd:b;if(!b)throw Error("P");b.handleExportedMessage(a.Ta(),this)};ag.prototype.th=function(a,b){if(Qf(a)&&this.onMessage)this.onMessage(Rf(a),b)};function bg(a){return a!=null&&typeof a.top!=="undefined"&&a.top!=null}function cg(a){if(a===$f)return!1;try{if(typeof a.location.hostname==="undefined")return!0}catch(b){return!0}return!1}function dg(){var a;typeof a==="undefined"&&typeof window!=="undefined"&&window&&(a=window);return bg(a)?a:$f};var eg=function(a,b){this.wd=b=b===void 0?$f:b;var c=this;a.addEventListener("message",function(d){if(typeof d.data==="object"){var e=d.data;if(Qf(e)&&d.source&&c.onMessage)c.onMessage(Rf(e),d.source)}})};q(eg,Sf);eg.prototype.sendMessage=function(a,b){b=b===void 0?this.wd:b;if(!b)throw Error("P");b.postMessage(a.Ta(),"*")};var fg=["omid","v1_VerificationServiceCommunication"],gg=["omidVerificationProperties","serviceWindow"];function hg(a,b){return b.reduce(function(c,d){return c&&c[d]},a)};var kg=function(a){if(!a){a=dg();var b=b===void 0?Uf:b;var c=[],d=hg(a,gg);d&&c.push(d);c.push(bg(a)?a.top:$f);a:{c=w(c);for(var e=c.next();!e.done;e=c.next()){b:{d=a;e=e.value;var f=b;if(!cg(e))try{var g=hg(e,fg);if(g){var h=new ag(g);break b}}catch(k){}h=f(e)?new eg(d,e):null}if(d=h){a=d;break a}}a=null}}if(this.cc=a)this.cc.onMessage=this.uh.bind(this);else if(b=(b=$f.omid3p)&&typeof b.registerSessionObserver==="function"&&typeof b.addEventListener==="function"?b:null)this.xc=b;this.mi=this.ni=
0;this.Od={};this.he=[];this.oc=(b=$f.omidVerificationProperties)?b.injectionId:void 0};kg.prototype.K=function(){var a=dg();var b=(b=$f.omidVerificationProperties)&&b.injectionSource?b.injectionSource:void 0;return(b||Vf(a)||Vf(bg(a)?a.top:$f))!=="web"||this.oc?!(!this.cc&&!this.xc):!1};var Ge=function(a,b,c){Lf(b);a.xc?a.xc.registerSessionObserver(b,c,a.oc):a.Sb("addSessionListener",b,c,a.oc)};
kg.prototype.addEventListener=function(a,b){Kf("eventType",a);Lf(b);this.xc?this.xc.addEventListener(a,b,this.oc):this.Sb("addEventListener",b,a,this.oc)};
var lf=function(a,b,c,d){Kf("url",b);$f.document&&$f.document.createElement?lg(a,b,c,d):a.Sb("sendUrl",function(e){e&&c?c():!e&&d&&d()},b)},lg=function(a,b,c,d){var e=$f.document.createElement("img");a.he.push(e);var f=function(g){var h=a.he.indexOf(e);h>=0&&a.he.splice(h,1);g&&g()};e.addEventListener("load",f.bind(a,c));e.addEventListener("error",f.bind(a,d));e.src=b};
kg.prototype.setTimeout=function(a,b){Lf(a);Mf("timeInMillis",b);if(mg())return $f.setTimeout(a,b);var c=this.ni++;this.Sb("setTimeout",a,c,b);return c};kg.prototype.clearTimeout=function(a){Mf("timeoutId",a);mg()?$f.clearTimeout(a):this.mg("clearTimeout",a)};kg.prototype.setInterval=function(a,b){Lf(a);Mf("timeInMillis",b);if(ng())return $f.setInterval(a,b);var c=this.mi++;this.Sb("setInterval",a,c,b);return c};
kg.prototype.clearInterval=function(a){Mf("intervalId",a);ng()?$f.clearInterval(a):this.mg("clearInterval",a)};var mg=function(){return typeof $f.setTimeout==="function"&&typeof $f.clearTimeout==="function"},ng=function(){return typeof $f.setInterval==="function"&&typeof $f.clearInterval==="function"};
kg.prototype.uh=function(a){var b=a.method,c=a.Lf;a=a.args;if(b==="response"&&this.Od[c]){var d=Nf()&&Of()?a?a:[]:a&&typeof a==="string"?JSON.parse(a):[];this.Od[c].apply(this,d)}b==="error"&&window.console&&Yf(a)};kg.prototype.mg=function(a){this.Sb.apply(this,[a,null].concat(z(B.apply(1,arguments))))};
kg.prototype.Sb=function(a,b){var c=B.apply(2,arguments);if(this.cc){var d=Xf();b&&(this.Od[d]=b);var e="VerificationService."+a;c=Nf()&&Of()?c:JSON.stringify(c);this.cc.sendMessage(new Pf(d,e,"1.5.2-google_20241009",c))}};var og=void 0;if(og=og===void 0?typeof omidExports==="undefined"?null:omidExports:og){var pg=["OmidVerificationClient"];pg.slice(0,pg.length-1).reduce(Wf,og)[pg[pg.length-1]]=kg};function qg(a,b){return function(c){return new K(function(d){return c.subscribe(function(e){a.Vb(b,function(){d.next(e)})()},function(e){a.Vb(b,function(){d.error(e)})()},function(){a.Vb(b,function(){d.complete()})()})})}};var sg=function(){for(var a=w(B.apply(0,arguments)),b=a.next();!b.done;b=a.next())if(b=b.value,b.fa()){this.j=b;return}this.j=new rg};n=sg.prototype;n.fa=function(){return this.j.fa()};n.now=function(){return this.j.now()};n.setTimeout=function(a,b){return this.j.setTimeout(a,b)};n.clearTimeout=function(a){this.j.clearTimeout(a)};n.interval=function(a,b){var c=this.Ha(a).subscribe(b);return function(){return void c.unsubscribe()}};n.Ha=function(a){return this.j.Ha(a)};
ea.Object.defineProperties(sg.prototype,{timeline:{configurable:!0,enumerable:!0,get:function(){return this.j.timeline}}});var rg=function(){this.timeline=Symbol()};n=rg.prototype;n.fa=function(){return!1};n.now=function(){return new qe(0,this.timeline)};n.setTimeout=function(){return 0};n.clearTimeout=function(){};n.interval=function(){return function(){}};n.Ha=function(){return Qd};var tg=function(a,b){this.L=a;this.F=b};n=tg.prototype;n.setTimeout=function(a,b){return this.L.setTimeout(this.F.Vb(734,a),b)};n.clearTimeout=function(a){this.L.clearTimeout(a)};n.interval=function(a,b){var c=this.Ha(a).subscribe(b);return function(){return void c.unsubscribe()}};n.Ha=function(a){var b=this;return new K(function(c){var d=0,e=b.L.setInterval(function(){c.next(d++)},a);return function(){b.L.clearInterval(e)}})};
n.fa=function(){return!!this.L.clearTimeout&&"setTimeout"in this.L&&"setInterval"in this.L&&!!this.L.clearInterval};var ug=function(a,b){tg.call(this,a,b);this.timeline=pe};q(ug,tg);ug.prototype.now=function(){return new qe(this.L.Date.now(),this.timeline)};ug.prototype.fa=function(){return!!this.L.Date&&!!this.L.Date.now&&tg.prototype.fa.call(this)};var vg=function(a,b){tg.call(this,a,b);this.timeline=oe};q(vg,tg);vg.prototype.now=function(){return new qe(this.L.performance.now(),this.timeline)};vg.prototype.fa=function(){return!!this.L.performance&&!!this.L.performance.now&&tg.prototype.fa.call(this)};function wg(a){a=a.global;if(a.fetchLater)return a.fetchLater.bind(a)}
function xg(a){var b,c,d=(b=a.global)==null?void 0:(c=b.document)==null?void 0:c.createElement("meta");if(d)try{return d.httpEquiv="origin-trial",d.content="AxjhRadLCARYRJawRjMjq4U8V8okQvSnrBIJWdMajuEkN3/DfVAcLcFhMVrUWnOXagwlI8dQD84FwJDGj9ohqAYAAABveyJvcmlnaW4iOiJodHRwczovL2dvb2dsZWFkc2VydmljZXMuY29tOjQ0MyIsImZlYXR1cmUiOiJGZXRjaExhdGVyQVBJIiwiZXhwaXJ5IjoxNzI1NDA3OTk5LCJpc1RoaXJkUGFydHkiOnRydWV9",a.global.document.head.append(d),d}catch(e){}}
var zg=function(a){this.context=a;yg===void 0&&(yg=xg(a))},yg;zg.prototype.K=function(a){return wg(this.context)!==void 0&&!(a==null||!a.sf)&&!Ag(this.context)&&!(a==null?0:a.Yb)&&!(a==null?0:a.eb)&&!(a==null?0:a.Yc)};zg.prototype.H=function(a,b){if(!this.K(b))throw new ee;return new Bg(this.context,a,b)};
var Bg=function(a,b,c){this.context=a;this.properties=c;this.xb=b;var d;this.ga=(d=c==null?void 0:c.ga)!=null?d:"GET";a=wg(this.context);if(a===void 0)throw Error();this.fetchLater=a;Cg(this,this.sc())},Cg=function(a,b){a.Ma&&a.Ma.activated||(a.Zb=new AbortController,a.Ma=a.fetchLater(b,{method:a.ga,cache:"no-cache",mode:"no-cors",signal:a.Zb.signal,activateAfter:96E4}))};Bg.prototype.sc=function(){var a=this.xb;return(a.slice(-1)[0]==="&"?a:a+"&")+"flapi=1"};
Bg.prototype.deactivate=function(){this.Ma&&!this.Ma.activated&&this.Zb&&(this.Zb.abort(),this.Ma=void 0)};Bg.prototype.sendNow=function(){};ea.Object.defineProperties(Bg.prototype,{url:{configurable:!0,enumerable:!0,get:function(){return this.xb},set:function(a){this.xb=a;a=this.sc();this.Ma&&this.Ma.activated||!this.Zb||(this.Zb.abort(),this.Ma=void 0);Cg(this,a)}},method:{configurable:!0,enumerable:!0,get:function(){return this.ga}}});var Dg=function(a){this.context=a};Dg.prototype.K=function(){return!Ag(this.context)&&!!this.context.global.fetch};Dg.prototype.ping=function(){var a=this;return Pd.apply(null,z(B.apply(0,arguments).map(function(b){return Pc(a.context.global.fetch(b,{method:"GET",cache:"no-cache",keepalive:!0,mode:"no-cors"})).g(N(function(c){return c.status===200}))}))).g(Ye(function(b){return b}),$e())};
Dg.prototype.rd=function(a,b,c){for(var d=B.apply(3,arguments),e=this,f=new Headers,g=w(b.entries()),h=g.next();!h.done;h=g.next()){var k=w(h.value);h=k.next().value;k=k.next().value;f.set(h,k)}var l,m=(l=a.keepAlive)!=null?l:!1;Pd.apply(null,z(d.map(function(t){return Pc(e.context.global.fetch(t,Object.assign({},{method:String(a.ga),cache:"no-cache"},m?{keepalive:!0}:{},{mode:"no-cors",headers:f,body:c}))).g(N(function(r){return r.status===200}))}))).g(Ye(function(t){return t}),$e())};var Eg=function(a,b,c){a.addEventListener&&a.addEventListener(b,c,!1)};var Fg=Na(1,!0),Gg=Na(610401301,!1);Na(899588437,!1);Na(725719775,!1);Na(513659523,!0);Na(568333945,!1);Na(651175828,!0);Na(722764542,!0);Na(2147483644,!1);Na(2147483645,!0);Na(2147483646,Fg);Na(2147483647,!0);function Hg(){var a=Ma.navigator;return a&&(a=a.userAgent)?a:""}var Ig,Jg=Ma.navigator;Ig=Jg?Jg.userAgentData||null:null;function Kg(a){if(!Gg||!Ig)return!1;for(var b=0;b<Ig.brands.length;b++){var c=Ig.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function V(a){return Hg().indexOf(a)!=-1};function Lg(){return Gg?!!Ig&&Ig.brands.length>0:!1}function Mg(){return Lg()?!1:V("Opera")}function Ng(){return V("Firefox")||V("FxiOS")}function Og(){return V("Safari")&&!(Pg()||(Lg()?0:V("Coast"))||Mg()||(Lg()?0:V("Edge"))||(Lg()?Kg("Microsoft Edge"):V("Edg/"))||(Lg()?Kg("Opera"):V("OPR"))||Ng()||V("Silk")||V("Android"))}function Pg(){return Lg()?Kg("Chromium"):(V("Chrome")||V("CriOS"))&&!(Lg()?0:V("Edge"))||V("Silk")}function Qg(){return V("Android")&&!(Pg()||Ng()||Mg()||V("Silk"))};var Sg=function(){return Gg&&Ig?Ig.mobile:!Rg()&&(V("iPod")||V("iPhone")||V("Android")||V("IEMobile"))},Rg=function(){return Gg&&Ig?!Ig.mobile&&(V("iPad")||V("Android")||V("Silk")):V("iPad")||V("Android")&&!V("Mobile")||V("Silk")};var Tg=function(a){Tg[" "](a);return a};Tg[" "]=function(){};var Ug=function(a,b){try{return Tg(a[b]),!0}catch(c){}return!1};function Vg(){return Gg?!!Ig&&!!Ig.platform:!1}function Wg(){return V("iPhone")&&!V("iPod")&&!V("iPad")}function Xg(){Wg()||V("iPad")||V("iPod")};Mg();var Yg=Lg()?!1:V("Trident")||V("MSIE");V("Edge");var Zg=V("Gecko")&&!(Za(Hg(),"WebKit")&&!V("Edge"))&&!(V("Trident")||V("MSIE"))&&!V("Edge"),$g=Za(Hg(),"WebKit")&&!V("Edge");$g&&V("Mobile");Vg()||V("Macintosh");Vg()||V("Windows");(Vg()?Ig.platform==="Linux":V("Linux"))||Vg()||V("CrOS");Vg()||V("Android");Wg();V("iPad");V("iPod");Xg();Za(Hg(),"KaiOS");var ah=function(a){try{return!!a&&a.location.href!=null&&Ug(a,"foo")}catch(b){return!1}};var bh=function(a){this.context=a};bh.prototype.K=function(a){return(a==null?0:a.Yb)||(a==null?void 0:a.ga)==="POST"||(a==null?0:a.eb)||(a==null?0:a.Yc)||(a==null?0:a.keepAlive)?!1:!Ag(this.context)};bh.prototype.ping=function(){var a=this;return M(B.apply(0,arguments).map(function(b){try{var c=a.context.global;c.google_image_requests||(c.google_image_requests=[]);var d=c.document;d=d===void 0?document:d;var e=d.createElement("img");e.src=b;c.google_image_requests.push(e);return!0}catch(f){return!1}}).every(function(b){return b}))};
bh.prototype.rd=function(a,b,c){this.ping.apply(this,z(B.apply(3,arguments)))};function ch(a){a=a.global;if(a.PendingGetBeacon)return a.PendingGetBeacon}var dh=function(a){this.context=a};dh.prototype.K=function(a){return eh&&!Ag(this.context)&&ch(this.context)!==void 0&&!(a==null?0:a.Yb)&&(a==null?void 0:a.ga)!=="POST"&&!(a==null?0:a.eb)&&!(a==null?0:a.Yc)};dh.prototype.H=function(a,b){if(!this.K(b))throw new ee;return new fh(this.context,a)};var eh=!1,fh=function(a,b){this.context=a;this.xb=b;a=ch(this.context);if(a===void 0)throw Error();this.Te=new a(this.sc(),{})};
fh.prototype.sc=function(){var a=this.xb;return(a.slice(-1)[0]==="&"?a:a+"&")+"pbapi=1"};fh.prototype.deactivate=function(){this.Te.deactivate()};fh.prototype.sendNow=function(){this.Te.sendNow()};ea.Object.defineProperties(fh.prototype,{url:{configurable:!0,enumerable:!0,get:function(){return this.xb},set:function(a){this.xb=a;this.Te.setURL(this.sc())}},method:{configurable:!0,enumerable:!0,get:function(){return"GET"},set:function(a){if(a!=="GET")throw new ee;}}});var gh=function(a){this.context=a};gh.prototype.K=function(a){if((a==null?0:a.Yb)||(a==null?void 0:a.ga)==="GET"||(a==null?0:a.eb)||(a==null?0:a.Yc)||(a==null?0:a.keepAlive))return!1;var b;return!Ag(this.context)&&((b=this.context.global.navigator)==null?void 0:b.sendBeacon)!==void 0};gh.prototype.ping=function(){var a=this;return M(B.apply(0,arguments).map(function(b){var c;return(c=a.context.global.navigator)==null?void 0:c.sendBeacon(b)}).every(function(b){return b}))};
gh.prototype.rd=function(a,b,c){this.ping.apply(this,z(B.apply(3,arguments)))};function hh(a){return function(b){return b.g(ih(a,af(new L)))}}function W(a,b){return function(c){return c.g(ih(a,bf(b)))}}function ih(a,b){function c(d){return new K(function(e){return d.subscribe(function(f){Wa(a,function(){return void e.next(f)},3)},function(f){Wa(a,function(){return void e.error(f)},3)},function(){Wa(a,function(){return void e.complete()},3)})})}return J(c,md(),b,jd(),c)};var X=function(a){this.value=a};X.prototype.R=function(a){return M(this.value).g(W(a,1))};var jh=new X(!1);Ng();Wg()||V("iPod");V("iPad");Qg();Pg();Og()&&Xg();var kh={},lh=null,mh=Zg||$g||typeof Ma.btoa=="function",oh=function(a){var b;E(Pa(a),"encodeByteArray takes an array as a parameter");b===void 0&&(b=0);nh();b=kh[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,f=0;e<a.length-2;e+=3){var g=a[e],h=a[e+1],k=a[e+2],l=b[g>>2];g=b[(g&3)<<4|h>>4];h=b[(h&15)<<2|k>>6];k=b[k&63];c[f++]=""+l+g+h+k}l=0;k=d;switch(a.length-e){case 2:l=a[e+1],k=b[(l&15)<<2]||d;case 1:a=a[e],c[f]=""+b[a>>2]+b[(a&3)<<4|l>>4]+k+d}return c.join("")},qh=function(a){var b=
a.length,c=b*3/4;c%3?c=Math.floor(c):"=.".indexOf(a[b-1])!=-1&&(c="=.".indexOf(a[b-2])!=-1?c-2:c-1);var d=new Uint8Array(c),e=0;ph(a,function(f){d[e++]=f});return e!==c?d.subarray(0,e):d},ph=function(a,b){function c(k){for(;d<a.length;){var l=a.charAt(d++),m=lh[l];if(m!=null)return m;if(!/^[\s\xa0]*$/.test(l))throw Error("R`"+l);}return k}nh();for(var d=0;;){var e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}},nh=function(){if(!lh){lh=
{};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));kh[c]=d;for(var e=0;e<d.length;e++){var f=d[e],g=lh[f];g===void 0?lh[f]=e:E(g===e)}}}};function rh(a){var b=sh(a);return b===null?new X(null):b.g(N(function(c){c=c.Ta();if(mh)c=Ma.btoa(c);else{for(var d=[],e=0,f=0;f<c.length;f++){var g=c.charCodeAt(f);if(g>255)throw Error("Q");d[e++]=g}c=oh(d)}return c}),Re(1),W(a.h,1))};function th(a){var b=b===void 0?{}:b;if(typeof Event==="function")return new Event(a,b);if(typeof document!=="undefined"){var c=document.createEvent("CustomEvent");c.initCustomEvent(a,b.bubbles||!1,b.cancelable||!1,b.detail);return c}throw Error();};var uh=function(a){this.value=a;this.Ee=new L};uh.prototype.release=function(){this.Ee.next();this.Ee.complete();this.value=void 0};ea.Object.defineProperties(uh.prototype,{i:{configurable:!0,enumerable:!0,get:function(){return this.value}},released:{configurable:!0,enumerable:!0,get:function(){return this.Ee}}});var vh=["FRAME","IMG","IFRAME"],wh=/^[01](px)?$/,xh=function(){this.Nh=this.df=this.gg=this.nf=!1},yh=function(){var a=new xh;a.nf=!0;a.gg=!0;return a};function zh(a){return typeof a==="string"?document.getElementById(a):a}
function Ah(a,b){b=b===void 0?!1:b;if(a.tagName==="IMG"){if(a.complete&&(!a.naturalWidth||!a.naturalHeight))return!0;var c;if(b&&((c=a.style)==null?void 0:c.display)==="none")return!0}var d,e;return wh.test((d=a.getAttribute("width"))!=null?d:"")&&wh.test((e=a.getAttribute("height"))!=null?e:"")}
function Bh(a,b){if(a.tagName==="IMG")return a.naturalWidth&&a.naturalHeight?!0:!1;try{if(a.readyState)var c=a.readyState;else{var d,e;c=(d=a.contentWindow)==null?void 0:(e=d.document)==null?void 0:e.readyState}return c==="complete"}catch(f){return b===void 0?!1:b}}function Ch(a){a||(a=function(b,c,d){b.addEventListener(c,d)});return a}
function Dh(a,b){var c=yh();c=c===void 0?new xh:c;if(a=zh(a)){var d=Ch(d);for(var e=!1,f=function(y){e||(e=!0,b(y))},g,h=2,k=0;k<vh.length;++k)if(vh[k]===a.tagName){h=3;g=[a];break}g||(g=a.querySelectorAll(vh.join(",")));var l=0,m=0,t=!c.df,r=a=!1;k={};for(var u=0;u<g.length;k={kd:void 0},u++){var x=g[u];if(!Ah(x,c.df))if(k.kd=x.tagName==="IMG",Bh(x,c.nf))a=!0,k.kd&&(t=!0);else{l++;var v=function(y){return function(A){l--;!l&&t&&f(h);y.kd&&(A=A&&A.type==="error",m--,A||(t=!0),!m&&r&&t&&f(h))}}(k);
d(x,"load",v);k.kd&&(m++,d(x,"error",v))}}m===0&&(t=!0);g=null;g=Ma.document.readyState==="complete";if(c.Nh&&g){if(m>0){r=!0;return}h=5}else if(l===0&&!a&&g)h=5;else if(l||!a){d(Ma,"load",function(){!c.gg||!m&&t?f(4):r=!0});return}f(h)}};function Eh(a,b,c){if(a)for(var d=0;a!=null&&d<500&&!c(a);++d)a=b(a)}function Fh(a,b){Eh(a,function(c){try{return c===c.parent?null:c.parent}catch(d){}return null},b)}function Gh(a,b){if(a.tagName=="IFRAME")b(a);else{a=a.querySelectorAll("IFRAME");for(var c=0;c<a.length&&!b(a[c]);++c);}}function Hh(a){return(a=a.ownerDocument)&&(a.parentWindow||a.defaultView)||null}
function Ih(a,b,c){try{var d=JSON.parse(c.data)}catch(g){}if(typeof d==="object"&&d&&d.type==="creativeLoad"){var e=Hh(a);if(c.source&&e){var f;Fh(c.source,function(g){try{if(g.parent===e)return f=g,!0}catch(h){}});f&&Gh(a,function(g){if(g.contentWindow===f)return b(d),!0})}}}function Jh(a){return typeof a==="string"?document.getElementById(a):a}
var Kh=function(a,b){var c=Jh(a);if(c)if(c.onCreativeLoad)c.onCreativeLoad(b);else{var d=b?[b]:[],e=function(f){for(var g=0;g<d.length;++g)try{d[g](1,f)}catch(h){}d={push:function(h){h(1,f)}}};c.onCreativeLoad=function(f){d.push(f)};c.setAttribute("data-creative-load-listener","");c.addEventListener("creativeLoad",function(f){e(f.detail)});Ma.addEventListener("message",function(f){Ih(c,e,f)})}};var Lh=function(a,b){var c=this;this.global=a;this.qd=b;this.fi=this.document?Pd(M(!0),Id(this.document,"visibilitychange")).g(qg(this.qd.F,748),N(function(){return c.document?c.document.visibilityState:"visible"}),R()):M("visible");this.ci=this.document?Id(this.document,"DOMContentLoaded").g(qg(this.qd.F,739),Re(1)):M(th("DOMContentLoaded"))},Mh=function(a){return a.document?a.document.readyState:"complete"},Nh=function(a){return a.document!==null&&a.document.visibilityState!==void 0};
Lh.prototype.querySelector=function(a){return this.document?this.document.querySelector(a):null};Lh.prototype.querySelectorAll=function(a){return this.document?Mb(this.document.querySelectorAll(a)):[]};Lh.prototype.elementFromPoint=function(a,b){if(!this.document||this.document===null||typeof this.document.elementFromPoint!=="function")return null;a=this.document.elementFromPoint(a,b);return a===null?null:new uh(a)};
var Oh=function(a,b,c){c=c===void 0?!1:c;if(b.i===void 0||!a.document)return M(b).g(qg(a.qd.F,749));var d=new bd(1),e=function(){d.next(b)};c||Kh(b.i,e);Dh(b.i,e);return d.g(qg(a.qd.F,749),Re(1))},Ph=function(a,b){a=a.document;if(!a)return M(!1);var c=Pd(M(null),Id(a,"DOMContentLoaded",{once:!0}),Id(a,"load",{once:!0})),d=new uh({document:a,element:b});return c.g(N(function(){if(!d.i)return!1;var e=d.i,f=e.document;e=e.element;var g,h,k=(h=(g=f.body)!=null?g:f.children[0])!=null?h:f;try{k.appendChild(e),
d.release()}catch(l){}return!d.i}),Q(function(e){return e}),Re(1),Oe(!1),gf({complete:function(){return void d.release()}}))},Qh=function(a,b,c){var d,e,f;return Fa(function(g){if(g.o==1){d=a.global.document.createElement("iframe");e=new Promise(function(k){d.onload=k;d.onerror=k});if(b instanceof db)var h=b.hg;else throw Error("Unexpected type when unwrapping TrustedResourceUrl");d.src=h.toString();return ta(g,gd(Ph(a,d)),2)}if(g.o!=3){if(!g.aa)return g.return();d.style.display="none";return ta(g,
e,3)}f=d.contentWindow;if(!f)return g.return();f.postMessage(c,"*");return g.return(d)})};ea.Object.defineProperties(Lh.prototype,{document:{configurable:!0,enumerable:!0,get:function(){return Ug(this.global,"document")?this.global.document||null:null}}});var Rh={left:0,top:0,width:0,height:0};function Sh(a,b){return a.left===b.left&&a.top===b.top&&a.width===b.width&&a.height===b.height}function Th(a,b){return{left:Math.max(a.left,b.left),top:Math.max(a.top,b.top),width:Math.max(0,Math.min(a.left+a.width,b.left+b.width)-Math.max(a.left,b.left)),height:Math.max(0,Math.min(a.top+a.height,b.top+b.height)-Math.max(a.top,b.top))}}function Uh(a,b){return{left:Math.round(a.left+b.x),top:Math.round(a.top+b.y),width:a.width,height:a.height}};var Vh=function(a,b){this.x=a!==void 0?a:0;this.y=b!==void 0?b:0};n=Vh.prototype;n.clone=function(){return new Vh(this.x,this.y)};n.toString=function(){return"("+this.x+", "+this.y+")"};n.equals=function(a){return a instanceof Vh&&(this==a?!0:this&&a?this.x==a.x&&this.y==a.y:!1)};n.ceil=function(){this.x=Math.ceil(this.x);this.y=Math.ceil(this.y);return this};n.floor=function(){this.x=Math.floor(this.x);this.y=Math.floor(this.y);return this};
n.round=function(){this.x=Math.round(this.x);this.y=Math.round(this.y);return this};n.translate=function(a,b){a instanceof Vh?(this.x+=a.x,this.y+=a.y):(this.x+=Number(a),typeof b==="number"&&(this.y+=b));return this};n.scale=function(a,b){this.x*=a;this.y*=typeof b==="number"?b:a;return this};var Wh=function(a,b){this.width=a;this.height=b};n=Wh.prototype;n.clone=function(){return new Wh(this.width,this.height)};n.toString=function(){return"("+this.width+" x "+this.height+")"};n.aspectRatio=function(){return this.width/this.height};n.isEmpty=function(){return!(this.width*this.height)};n.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};n.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};
n.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};n.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};var Zh=function(a){return a?new Xh(Yh(a)):xb||(xb=new Xh)},$h=function(a){var b=a.scrollingElement?a.scrollingElement:$g||a.compatMode!="CSS1Compat"?a.body||a.documentElement:a.documentElement;a=a.defaultView;return new Vh(a.pageXOffset||b.scrollLeft,a.pageYOffset||b.scrollTop)},ai=function(a,b,c){function d(h){h&&b.appendChild(typeof h==="string"?a.createTextNode(h):h)}for(var e=1;e<c.length;e++){var f=c[e];if(!Pa(f)||Qa(f)&&f.nodeType>0)d(f);else{a:{if(f&&typeof f.length=="number"){if(Qa(f)){var g=
typeof f.item=="function"||typeof f.item=="string";break a}if(typeof f==="function"){g=typeof f.item=="function";break a}}g=!1}Ib(g?Mb(f):f,d)}}},Yh=function(a){E(a,"Node cannot be null or undefined.");return a.nodeType==9?a:a.ownerDocument||a.document},bi=function(a,b){a&&(a=a.parentNode);for(var c=0;a;){E(a.name!="parentNode");if(b(a))return a;a=a.parentNode;c++}return null},Xh=function(a){this.ic=a||Ma.document||document};n=Xh.prototype;n.getElementsByTagName=function(a,b){return(b||this.ic).getElementsByTagName(String(a))};
n.createElement=function(a){var b=this.ic;a=String(a);b.contentType==="application/xhtml+xml"&&(a=a.toLowerCase());return b.createElement(a)};n.createTextNode=function(a){return this.ic.createTextNode(String(a))};n.appendChild=function(a,b){E(a!=null&&b!=null,"goog.dom.appendChild expects non-null arguments");a.appendChild(b)};n.append=function(a,b){ai(Yh(a),a,arguments)};n.canHaveChildren=function(a){if(a.nodeType!=1)return!1;switch(a.tagName){case "APPLET":case "AREA":case "BASE":case "BR":case "COL":case "COMMAND":case "EMBED":case "FRAME":case "HR":case "IMG":case "INPUT":case "IFRAME":case "ISINDEX":case "KEYGEN":case "LINK":case "NOFRAMES":case "NOSCRIPT":case "META":case "OBJECT":case "PARAM":case "SCRIPT":case "SOURCE":case "STYLE":case "TRACK":case "WBR":return!1}return!0};
n.removeNode=function(a){return a&&a.parentNode?a.parentNode.removeChild(a):null};n.isElement=function(a){return Qa(a)&&a.nodeType==1};n.contains=function(a,b){if(!a||!b)return!1;if(a.contains&&b.nodeType==1)return a==b||a.contains(b);if(typeof a.compareDocumentPosition!="undefined")return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};function ci(a,b,c,d){this.top=a;this.right=b;this.bottom=c;this.left=d}n=ci.prototype;n.Jf=function(){return this.right-this.left};n.Gf=function(){return this.bottom-this.top};n.clone=function(){return new ci(this.top,this.right,this.bottom,this.left)};n.toString=function(){return"("+this.top+"t, "+this.right+"r, "+this.bottom+"b, "+this.left+"l)"};
n.contains=function(a){return this&&a?a instanceof ci?a.left>=this.left&&a.right<=this.right&&a.top>=this.top&&a.bottom<=this.bottom:a.x>=this.left&&a.x<=this.right&&a.y>=this.top&&a.y<=this.bottom:!1};n.expand=function(a,b,c,d){Qa(a)?(this.top-=a.top,this.right+=a.right,this.bottom+=a.bottom,this.left-=a.left):(this.top-=a,this.right+=Number(b),this.bottom+=Number(c),this.left-=Number(d));return this};
n.ceil=function(){this.top=Math.ceil(this.top);this.right=Math.ceil(this.right);this.bottom=Math.ceil(this.bottom);this.left=Math.ceil(this.left);return this};n.floor=function(){this.top=Math.floor(this.top);this.right=Math.floor(this.right);this.bottom=Math.floor(this.bottom);this.left=Math.floor(this.left);return this};n.round=function(){this.top=Math.round(this.top);this.right=Math.round(this.right);this.bottom=Math.round(this.bottom);this.left=Math.round(this.left);return this};
n.translate=function(a,b){a instanceof Vh?(this.left+=a.x,this.right+=a.x,this.top+=a.y,this.bottom+=a.y):(Cb(a),this.left+=a,this.right+=a,typeof b==="number"&&(this.top+=b,this.bottom+=b));return this};n.scale=function(a,b){b=typeof b==="number"?b:a;this.left*=a;this.right*=a;this.top*=b;this.bottom*=b;return this};function di(a,b){var c=Rg()||Sg();try{if(a){if(!b.top)return new ci(-12245933,-12245933,-12245933,-12245933);b=b.top}a:{var d=b;if(a&&d!==null&&d!=d.top){if(!d.top){var e=new Wh(-12245933,-12245933);break a}d=d.top}try{if(c===void 0?0:c)var f=(new Wh(d.innerWidth,d.innerHeight)).round();else{var g=(d||window).document,h=g.compatMode=="CSS1Compat"?g.documentElement:g.body;f=(new Wh(h.clientWidth,h.clientHeight)).round()}e=f}catch(u){e=new Wh(-12245933,-12245933)}}a=e;var k=a.height,l=a.width;if(l===
-12245933)return new ci(l,l,l,l);var m=$h(Zh(b.document).ic),t=m.x,r=m.y;return new ci(r,t+l,r+k,t)}catch(u){return new ci(-12245933,-12245933,-12245933,-12245933)}};function ei(a,b){if(a)throw Error("S");b.push(65533)}function fi(a,b){b=String.fromCharCode.apply(null,b);return a==null?b:a+b}var gi=void 0,hi,ii,ji=typeof TextDecoder!=="undefined",ki,li=typeof String.prototype.isWellFormed==="function",mi=typeof TextEncoder!=="undefined";var ni=typeof Uint8Array!=="undefined",oi=!Yg&&typeof btoa==="function",pi=/[-_.]/g,qi={"-":"+",_:"/",".":"="};function ri(a){return qi[a]||""}function si(a){if(!oi)return qh(a);var b=a;pi.test(b)&&(b=b.replace(pi,ri));try{var c=atob(b)}catch(d){throw Error("U`"+a+"`"+d);}a=new Uint8Array(c.length);for(b=0;b<c.length;b++)a[b]=c.charCodeAt(b);return a}var ti={};var xi=function(a,b){if(b!==ti)throw Error("W");this.Kc=a;if(a!=null&&a.length===0)throw Error("V");this.dontPassByteStringToStructuredClone=ui};xi.prototype.isEmpty=function(){return this.Kc==null};var yi;function ui(){};function zi(a){Hb(a,xi);if(ti!==ti)throw Error("W");var b=a.Kc;b==null||ni&&b!=null&&b instanceof Uint8Array||(typeof b==="string"?b=si(b):(Bb("Cannot coerce to Uint8Array: "+Oa(b)),b=null));return(b==null?b:a.Kc=b)||new Uint8Array(0)};function Ai(a,b){if(typeof a==="string")return{buffer:si(a),lb:b};if(Array.isArray(a))return{buffer:new Uint8Array(a),lb:b};if(a.constructor===Uint8Array)return{buffer:a,lb:!1};if(a.constructor===ArrayBuffer)return{buffer:new Uint8Array(a),lb:!1};if(a.constructor===xi)return{buffer:zi(a),lb:!0};if(a instanceof Uint8Array)return{buffer:new Uint8Array(a.buffer,a.byteOffset,a.byteLength),lb:!1};throw Error("ea");};function Bi(){return typeof BigInt==="function"};function Ci(a,b){b=b===void 0?new Set:b;if(b.has(a))return"(Recursive reference)";switch(typeof a){case "object":if(a){var c=Object.getPrototypeOf(a);switch(c){case Map.prototype:case Set.prototype:case Array.prototype:b.add(a);var d="["+Array.from(a,function(e){return Ci(e,b)}).join(", ")+"]";b.delete(a);c!==Array.prototype&&(d=Di(c.constructor)+"("+d+")");return d;case Object.prototype:return b.add(a),c="{"+Object.entries(a).map(function(e){var f=w(e);e=f.next().value;f=f.next().value;return e+
": "+Ci(f,b)}).join(", ")+"}",b.delete(a),c;default:return d="Object",c&&c.constructor&&(d=Di(c.constructor)),typeof a.toString==="function"&&a.toString!==Object.prototype.toString?d+"("+String(a)+")":"(object "+d+")"}}break;case "function":return"function "+Di(a);case "number":if(!Number.isFinite(a))return String(a);break;case "bigint":return a.toString(10)+"n";case "symbol":return a.toString()}return JSON.stringify(a)}
function Di(a){var b=a.displayName;return b&&typeof b==="string"||(b=a.name)&&typeof b==="string"?b:(a=/function\s+([^\(]+)/m.exec(String(a)))?a[1]:"(Anonymous)"};function Ei(a,b){var c=Fi,d=[];Gi(b,a,d)||Hi.apply(null,[void 0,c,"Guard "+b.Kf().trim()+" failed:"].concat(z(d.reverse())))}function Ii(a,b){a.Aj=!0;a.Kf=typeof b==="function"?b:function(){return b};return a}function Gi(a,b,c){var d=a(b,c);d||Ji(c,function(){var e="";e.length>0&&(e+=": ");return e+"Expected "+a.Kf().trim()+", got "+Ci(b)});return d}function Ji(a,b){a==null||a.push((typeof b==="function"?b():b).trim())}var Fi=void 0;function Ki(a){return typeof a==="function"?a():a}
function Hi(){throw Error(B.apply(0,arguments).map(Ki).filter(Boolean).join("\n").trim().replace(/:$/,""));};var Li=Ii(function(a){return typeof a==="string"},"string"),Mi=Ii(function(a){return typeof a==="bigint"},"bigint");function Ni(){var a=Error;return Ii(function(b){return b instanceof a},function(){return Di(a)})};var Oi=typeof Ma.BigInt==="function"&&typeof Ma.BigInt(0)==="bigint";var Ui=Ii(function(a){if(Oi)return Ei(Pi,Mi),Ei(Qi,Mi),a=BigInt(a),a>=Pi&&a<=Qi;Ei(a,Li);return a[0]==="-"?Ri(a,Si):Ri(a,Ti)},"isSafeInt52"),Si=Number.MIN_SAFE_INTEGER.toString(),Pi=Oi?BigInt(Number.MIN_SAFE_INTEGER):void 0,Ti=Number.MAX_SAFE_INTEGER.toString(),Qi=Oi?BigInt(Number.MAX_SAFE_INTEGER):void 0;
function Ri(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}c=Fi;Hi("Assertion fail:","isInRange weird case. Value was: "+a+". Boundary was: "+b+"."||c)};var Vi=typeof Uint8Array.prototype.slice==="function",Wi=0,Xi=0,Yi;function Zi(a){var b=a>>>0;Wi=b;Xi=(a-b)/4294967296>>>0}function $i(a){if(a<0){Zi(0-a);var b=w(aj(Wi,Xi));a=b.next().value;b=b.next().value;Wi=a>>>0;Xi=b>>>0}else Zi(a)}function bj(a){E(a<=8);return Yi||(Yi=new DataView(new ArrayBuffer(8)))}function cj(a,b){var c=b*4294967296+(a>>>0);return Number.isSafeInteger(c)?c:dj(a,b)}
function ej(a,b){var c=b&**********;c&&(a=~a+1>>>0,b=~b>>>0,a==0&&(b=b+1>>>0));a=cj(a,b);return typeof a==="number"?c?-a:a:c?"-"+a:a}function dj(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else Bi()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),E(b),c=b+fj(c)+fj(a));return c}function fj(a){a=String(a);return"0000000".slice(a.length)+a}
function gj(a){E(a.length>0);if(a.length<16)$i(Number(a));else if(Bi())a=BigInt(a),Wi=Number(a&BigInt(4294967295))>>>0,Xi=Number(a>>BigInt(32)&BigInt(4294967295));else{E(a.length>0);var b=+(a[0]==="-");Xi=Wi=0;for(var c=a.length,d=0+b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),Xi*=1E6,Wi=Wi*1E6+d,Wi>=4294967296&&(Xi+=Math.trunc(Wi/4294967296),Xi>>>=0,Wi>>>=0);b&&(b=w(aj(Wi,Xi)),a=b.next().value,b=b.next().value,Wi=a,Xi=b)}}function aj(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};var hj=function(a,b,c,d){this.ta=null;this.Nd=!1;this.O=this.Da=this.Ka=0;this.nc(a,b,c,d)};n=hj.prototype;n.nc=function(a,b,c,d){var e=d===void 0?{}:d;d=e.Rc===void 0?!1:e.Rc;e=e.Ad===void 0?!1:e.Ad;this.Rc=d;this.Ad=e;a&&(a=Ai(a,this.Ad),this.ta=a.buffer,this.Nd=a.lb,this.Ka=b||0,this.Da=c!==void 0?this.Ka+c:this.ta.length,this.O=this.Ka)};n.ce=function(){this.clear();ij.length<100&&ij.push(this)};n.clear=function(){this.ta=null;this.Nd=!1;this.O=this.Da=this.Ka=0;this.Rc=!1};
n.setEnd=function(a){this.Da=a};n.reset=function(){this.O=this.Ka};n.Z=function(){return this.O};n.advance=function(a){jj(this,this.O+a)};
var kj=function(a,b){var c=0,d=0,e=0,f=a.ta,g=a.O;do{var h=f[g++];c|=(h&127)<<e;e+=7}while(e<32&&h&128);e>32&&(d|=(h&127)>>4);for(e=3;e<32&&h&128;e+=7)h=f[g++],d|=(h&127)<<e;jj(a,g);if(h<128)return b(c>>>0,d>>>0);throw Error("ba");},jj=function(a,b){a.O=b;if(b>a.Da)throw Error("ca`"+b+"`"+a.Da);},lj=function(a){var b=a.ta,c=a.O,d=b[c++],e=d&127;if(d&128&&(d=b[c++],e|=(d&127)<<7,d&128&&(d=b[c++],e|=(d&127)<<14,d&128&&(d=b[c++],e|=(d&127)<<21,d&128&&(d=b[c++],e|=d<<28,d&128&&b[c++]&128&&b[c++]&128&&
b[c++]&128&&b[c++]&128&&b[c++]&128)))))throw Error("ba");jj(a,c);return e},mj=function(a){return lj(a)>>>0},nj=function(a){return kj(a,cj)},oj=function(a){var b=a.ta,c=a.O,d=b[c+0],e=b[c+1],f=b[c+2];b=b[c+3];a.advance(4);return(d<<0|e<<8|f<<16|b<<24)>>>0},pj=function(a){for(var b=0,c=a.O,d=c+10,e=a.ta;c<d;){var f=e[c++];b|=f;if((f&128)===0)return jj(a,c),!!(b&127)}throw Error("ba");},qj=function(a){return lj(a)},rj=function(a,b){if(b<0)throw Error("da`"+b);var c=a.O,d=c+b;if(d>a.Da)throw Error("ca`"+
(a.Da-c)+"`"+b);a.O=d;return c};
hj.prototype.jg=function(a,b){var c=rj(this,a),d=E(this.ta);if(ji){var e;b?(e=hi)||(e=hi=new TextDecoder("utf-8",{fatal:!0})):(e=ii)||(e=ii=new TextDecoder("utf-8",{fatal:!1}));var f=c+a;d=c===0&&f===d.length?d:d.subarray(c,f);try{var g=e.decode(d)}catch(m){if(b){if(gi===void 0){try{e.decode(new Uint8Array([128]))}catch(t){}try{e.decode(new Uint8Array([97])),gi=!0}catch(t){gi=!1}}b=!gi}b&&(hi=void 0);throw m;}}else{a=c+a;g=[];for(var h=null,k,l;c<a;)k=d[c++],k<128?g.push(k):k<224?c>=a?ei(b,g):(l=
d[c++],k<194||(l&192)!==128?(c--,ei(b,g)):(k=(k&31)<<6|l&63,E(k>=128&&k<=2047),g.push(k))):k<240?c>=a-1?ei(b,g):(l=d[c++],(l&192)!==128||k===224&&l<160||k===237&&l>=160||((e=d[c++])&192)!==128?(c--,ei(b,g)):(k=(k&15)<<12|(l&63)<<6|e&63,E(k>=2048&&k<=65535),E(k<55296||k>57343),g.push(k))):k<=244?c>=a-2?ei(b,g):(l=d[c++],(l&192)!==128||(k<<28)+(l-144)>>30!==0||((e=d[c++])&192)!==128||((f=d[c++])&192)!==128?(c--,ei(b,g)):(k=(k&7)<<18|(l&63)<<12|(e&63)<<6|f&63,E(k>=65536&&k<=1114111),k-=65536,g.push((k>>
10&1023)+55296,(k&1023)+56320))):ei(b,g),g.length>=8192&&(h=fi(h,g),g.length=0);E(c===a,"expected "+c+" === "+a);g=fi(h,g)}return g};hj.prototype.Ce=function(a){if(a==0)return yi||(yi=new xi(null,ti));var b=rj(this,a);if(this.Rc&&this.Nd)b=this.ta.subarray(b,b+a);else{var c=E(this.ta);a=b+a;b=b===a?new Uint8Array(0):Vi?c.slice(b,a):new Uint8Array(c.subarray(b,a))}Hb(b,Uint8Array);return b.length==0?yi||(yi=new xi(null,ti)):new xi(b,ti)};var ij=[];E(!0);var tj=function(a,b,c,d){if(ij.length){var e=ij.pop();e.nc(a,b,c,d);a=e}else a=new hj(a,b,c,d);this.m=a;this.fb=this.m.Z();this.l=this.pd=this.Lb=-1;sj(this,d)},sj=function(a,b){b=b===void 0?{}:b;a.Xd=b.Xd===void 0?!1:b.Xd},vj=function(a,b,c,d){if(uj.length){var e=uj.pop();sj(e,d);e.m.nc(a,b,c,d);return e}return new tj(a,b,c,d)};tj.prototype.ce=function(){this.m.clear();this.l=this.Lb=this.pd=-1;uj.length<100&&uj.push(this)};tj.prototype.Z=function(){return this.m.Z()};
tj.prototype.reset=function(){this.m.reset();this.fb=this.m.Z();this.l=this.Lb=this.pd=-1};tj.prototype.advance=function(a){this.m.advance(a)};
var wj=function(a){var b=a.m;if(b.O==b.Da)return!1;a.pd!==-1&&(b=a.m.Z(),a.m.O=a.fb,mj(a.m),a.l===4||a.l===3?E(b===a.m.Z(),"Expected to not advance the cursor.  Group tags do not have values."):E(b>a.m.Z(),"Expected to read the field, did you forget to call a read or skip method?"),a.m.O=b);a.fb=a.m.Z();b=mj(a.m);var c=b>>>3,d=b&7;if(!(d>=0&&d<=5))throw Error("Y`"+d+"`"+a.fb);if(c<1)throw Error("Z`"+c+"`"+a.fb);a.pd=b;a.Lb=c;a.l=d;return!0},xj=function(a){switch(a.l){case 0:a.l!=0?(Bb("Invalid wire type for skipVarintField"),
xj(a)):pj(a.m);break;case 1:E(a.l===1);a.m.advance(8);break;case 2:if(a.l!=2)Bb("Invalid wire type for skipDelimitedField"),xj(a);else{var b=mj(a.m);a.m.advance(b)}break;case 5:E(a.l===5);a.m.advance(4);break;case 3:b=a.Lb;do{if(!wj(a))throw Error("$");if(a.l==4){if(a.Lb!=b)throw Error("aa");break}xj(a)}while(1);break;default:throw Error("Y`"+a.l+"`"+a.fb);}},yj=function(a,b,c){E(a.l==2);var d=a.m.Da,e=mj(a.m),f=a.m.Z()+e,g=f-d;g<=0&&(a.m.setEnd(f),c(b,a,void 0,void 0,void 0),g=f-a.m.Z());if(g)throw Error("X`"+
e+"`"+(e-g));a.m.O=f;a.m.setEnd(d)},zj=function(a){E(a.l==0);return lj(a.m)},Aj=function(a){E(a.l==0);return mj(a.m)},Bj=function(a){E(a.l==0);return nj(a.m)},Cj=function(a){E(a.l==0);return lj(a.m)};tj.prototype.jg=function(){return Dj(this)};var Dj=function(a){E(a.l==2);var b=mj(a.m);return a.m.jg(b,!0)};tj.prototype.Ce=function(){E(this.l==2);var a=mj(this.m);return this.m.Ce(a)};var Ej=function(a,b,c){E(a.l==2);var d=mj(a.m);for(d=a.m.Z()+d;a.m.Z()<d;)c.push(b(a.m))},uj=[];function Fj(a){a=Error(a);a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity="warning";return a};var Gj=typeof Symbol==="function"&&typeof Symbol()==="symbol";function Hj(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b}var Ij=Hj("jas",void 0,!0),Jj=Hj("defaultInstance","0di"),Kj=Hj("unknownBinaryFields",Symbol()),Lj=Hj("m_m","Dj",!0),Mj=Hj("validPivotSelector","vps");E(Math.round(Math.log2(Math.max.apply(Math,z(Object.values({kj:1,jj:2,ij:4,oj:8,nj:16,mj:32,Ui:64,rj:128,gj:256,fj:512,Zi:1024,qj:2048,aj:4096,Xi:8192,hj:16384})))))===14);var Nj={Dh:{value:0,configurable:!0,writable:!0,enumerable:!1}},Oj=Object.defineProperties,Y=Gj?F(Ij):"Dh",Pj,Qj=[];Rj(Qj,55);Pj=Object.freeze(Qj);function Sj(a){H(a,"state is only maintained on arrays.");return a[Y]|0}function Tj(a,b){E((b&33554431)===b);H(a,"state is only maintained on arrays.");Gj||Y in a||Oj(a,Nj);a[Y]|=b}
function Rj(a,b){E((b&33554431)===b);H(a,"state is only maintained on arrays.");Gj||Y in a||Oj(a,Nj);a[Y]=b}function Uj(a){E(!0);E(Y in a);H(a,"state is only maintained on arrays.");a[Y]&=-3}
function Vj(a,b,c){(c===void 0||!c||b&16384)&&E(b&64,"state for messages must be constructed");E((b&5)===0,"state for messages should not contain repeated field state");c=b>>15&1023||536870912;var d=a.length;E(c+(b&512?0:-1)>=d-1,"pivot %s is pointing at an index earlier than the last index of the array, length: %s",c,d);b&512&&E(typeof a[0]==="string","arrays with a message_id bit must have a string in the first position, got: %s",a[0]);a=d?a[d-1]:void 0;E((a!=null&&typeof a==="object"&&a.constructor===
Object)===!!(b&256),"arraystate and array disagree on sparseObject presence")}function Wj(a){H(a,"state is only maintained on arrays.");var b=a[Y]|0;Vj(a,b);return b}function Xj(a){H(a,"state is only maintained on arrays.");return!!((a[Y]|0)&2)}function Yj(a,b){Cb(b);E(b>0&&b<=1023||536870912===b,"pivot must be in the range [1, 1024) or NO_PIVOT got %s",b);return a&-33521665|(b&1023)<<15}var Zj=Object.getOwnPropertyDescriptor(Array.prototype,"Mh");
Object.defineProperties(Array.prototype,{Mh:{get:function(){var a=ak(this);return Zj?Zj.get.call(this)+"|"+a:a},configurable:!0,enumerable:!1}});
function ak(a){function b(e,f){e&c&&d.push(f)}var c=Sj(a),d=[];b(1,"IS_REPEATED_FIELD");b(2,"IS_IMMUTABLE_ARRAY");b(4,"IS_API_FORMATTED");b(2048,"STRING_FORMATTED");b(4096,"GBIGINT_FORMATTED");b(4096,"BINARY");b(8,"ONLY_MUTABLE_VALUES");b(32,"MUTABLE_REFERENCES_ARE_OWNED");b(64,"CONSTRUCTED");b(128,"TRANSFERRED");b(256,"HAS_SPARSE_OBJECT");b(512,"HAS_MESSAGE_ID");b(1024,"FROZEN_ARRAY");b(8192,"DESERIALIZED_FROM_BINARY");b(16384,"HAS_WRAPPER");a=c>>15&1023||536870912;a!==536870912&&d.push("pivot: "+
a);return d.join(",")};var bk=Gj&&Math.random()<.5,ck=bk?Symbol():void 0,dk,ek=typeof Lj==="symbol",fk={};function Z(a){var b=a[Lj],c=b===fk;E(!dk||c===a instanceof dk);if(ek&&b&&!c)throw Error("ma");return c}function gk(a,b){Cb(a);E(a>0);E(b===0||b===-1);return a+b}function hk(a,b){Cb(a);E(a>=0);E(b===0||b===-1);return a-b}function ik(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object}function jk(a){if(a&2)throw Error("na");}
var kk=typeof Symbol!="undefined"&&typeof Symbol.hasInstance!="undefined";function lk(){}function mk(a,b){var c=Sj(H(a));b||E(!(c&2&&c&4||c&1024)||Object.isFrozen(a));b=!!(c&8);var d=!!(c&16&&c&32);if(b||d){var e,f,g;a.forEach(function(h){Array.isArray(h)?g=!0:h&&Z(h)&&(E(Z(h)),h=bk?h[F(ck)]:h.u,Xj(h)?f=!0:e=!0)});g&&E(!(c&4));d&&E(!e);b&&E(!g&&!f)}nk(a)}
function nk(a){H(a,"state is only maintained on arrays.");var b=a[Y]|0;var c=b&4,d=(2048&b?1:0)+(4096&b?1:0);E(c&&d<=1||!c&&d===0,"Expected at most 1 type-specific formatting bit, but got "+d+" with state: "+b);H(a,"state is only maintained on arrays.");if(2048&(a[Y]|0))for(b=0;b<a.length;b++)typeof a[b]!=="string"&&Bb("Unexpected element of type "+typeof a[b]+" in string formatted repeated 64-bit int field")}var ok=Object.freeze({}),pk=Symbol("debugExtensions");
function qk(a,b,c){var d=b&512?0:-1,e=a.length;b=b&64?b&256:!!e&&ik(a[e-1]);var f=e+(b?-1:0);E(!!b===ik(a[e-1]));for(var g=0;g<f;g++){var h=a[g];c(hk(g,d),h)}if(b){a=a[e-1];for(var k in a)!isNaN(k)&&c(+k,a[k])}}var rk={};function sk(a){return Array.prototype.slice.call(a)};var tk=typeof BigInt==="function"?BigInt.asIntN:void 0,uk=typeof BigInt==="function"?BigInt.asUintN:void 0,vk=Number.isSafeInteger,wk=Number.isFinite,xk=Math.trunc,yk=Number.MAX_SAFE_INTEGER;function zk(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function Ak(a){return a.displayName||a.name||"unknown type name"}function Bk(a){if(a==null||typeof a==="boolean")return a;if(typeof a==="number")return!!a}var Ck=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;
function Dk(a){switch(typeof a){case "bigint":return!0;case "number":return wk(a);case "string":return Ck.test(a);default:return!1}}function Ek(a){if(!wk(a))throw a="Expected enum as finite number but got "+Oa(a)+": "+a,Fj(a);return a|0}function Fk(a){return a==null?a:wk(a)?a|0:void 0}function Gk(a){return"Expected int32 as finite number but got "+Oa(a)+": "+a}function Hk(a){if(typeof a!=="number")throw Fj(Gk(a));if(!wk(a))throw Fj(Gk(a));return a|0}
function Ik(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return wk(a)?a|0:void 0}function Jk(a){return"Expected uint32 as finite number but got "+Oa(a)+": "+a}function Kk(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return wk(a)?a>>>0:void 0}function Lk(a){if(a[0]==="-")return!1;var b=a.length;return b<20?!0:b===20&&Number(a.substring(0,6))<184467}
function Mk(a){E(a<0||!(0<a&&a<yk));E(Number.isInteger(a));if(a<0){$i(a);var b=dj(Wi,Xi);a=Number(b);return vk(a)?a:b}b=String(a);if(Lk(b))return b;$i(a);return cj(Wi,Xi)}
function Nk(a){if(a==null)return a;var b=typeof a;if(b==="bigint")return String(tk(64,a));if(Dk(a)){if(b==="string")return E(Dk(a)),E(!0),b=xk(Number(a)),vk(b)?a=String(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),E(a.indexOf(".")===-1),b=a.length,(a[0]==="-"?b<20||b===20&&Number(a.substring(0,7))>-922337:b<19||b===19&&Number(a.substring(0,6))<922337)||(gj(a),a=Wi,b=Xi,b&**********?Bi()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=w(aj(a,b)),a=b.next().value,b=b.next().value,a="-"+dj(a,b)):
a=dj(a,b))),a;if(b==="number")return E(Dk(a)),E(!0),a=xk(a),vk(a)||(E(!vk(a)),E(Number.isInteger(a)),$i(a),a=ej(Wi,Xi)),a}}function Ok(a){if(a==null)return a;var b=typeof a;if(b==="bigint")return String(uk(64,a));if(Dk(a)){if(b==="string")return E(Dk(a)),E(!0),b=xk(Number(a)),vk(b)&&b>=0?a=String(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),E(a.indexOf(".")===-1),Lk(a)||(gj(a),a=dj(Wi,Xi))),a;if(b==="number")return E(Dk(a)),E(!0),a=xk(a),a>=0&&vk(a)?a:Mk(a)}}
function Pk(a){return a==null||typeof a==="string"?a:void 0}function Qk(a,b){if(!(a instanceof b))throw Error("ra`"+Ak(b)+"`"+(a&&Ak(a.constructor)));}function Rk(a,b,c){if(a!=null&&typeof a==="object"&&Z(a))return a;if(Array.isArray(a)){var d;H(a,"state is only maintained on arrays.");var e=d=a[Y]|0;e===0&&(e|=c&32);e|=c&2;e!==d&&Rj(a,e);return new b(a)}};function Sk(a){return a}Sk[Mj]={};var Tk=function(){throw Error("sa");};if(kk){var Uk=function(){throw Error("ta");},Vk={};Object.defineProperties(Tk,(Vk[Symbol.hasInstance]={value:Uk,configurable:!1,writable:!1,enumerable:!1},Vk));E(Tk[Symbol.hasInstance]===Uk,"defineProperties did not work: was it monkey-patched?")};function Wk(a){var b=Ra(Kj);return b?H(a)[b]:void 0}var Xk=function(){},Yk=function(a,b){for(var c in a)!isNaN(c)&&b(a,+c,H(a[c]))},Zk=function(a){var b=new Xk;Yk(a,function(c,d,e){b[d]=sk(e)});b.kg=a.kg;return b};function $k(a,b){E(Z(a));E(Z(a));a=bk?a[F(ck)]:a.u;H(a);var c=Ra(Kj),d;if(Gj&&c&&((d=a[c])==null?void 0:d[b])!=null)throw Error("ua");};function al(a,b,c,d,e){var f=d?!!(b&32):void 0;d=[];var g=a.length,h=!1;if(b&64){if(b&256){g--;E(g>=0);var k=a[g];var l=g;E(ik(k))}else l=4294967295,k=void 0,E(!(g&&ik(a[g-1])));if(!(e||b&512)){h=!0;E(-1===(b&512?0:-1));var m=k?hk(l,-1):b>>15&1023||536870912;var t;m=((t=bl)!=null?t:Sk)(m,-1,a,k);l=gk(m,-1)}}else l=4294967295,b&1||(k=g&&a[g-1],ik(k)?(g--,l=g,m=0):k=void 0);t=void 0;for(var r=0;r<g;r++){var u=a[r];if(u!=null&&(u=c(u,f))!=null)if(r>=l){E((b&512?0:-1)==-1);var x=void 0;((x=t)!=null?x:
t={})[hk(r,-1)]=u}else d[r]=u}if(k)for(var v in k)g=k[v],g!=null&&(g=c(g,f))!=null&&(r=+v,r<m?(E((b&512?0:-1)==-1),d[gk(r,-1)]=g):(r=void 0,((r=t)!=null?r:t={})[v]=g));t&&(h?d.push(t):d[F(l)]=t);e&&(Rj(d,b&33522241|(t!=null?290:34)),Ra(Kj)&&(H(d),H(a),E(d[Kj]===void 0),(a=Wk(a))&&a instanceof Xk&&(d[Kj]=Zk(a))));return d}
function cl(a){F(a);switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return Ui(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){mk(a);H(a,"state is only maintained on arrays.");var b=a[Y]|0;return a.length===0&&b&1?void 0:al(a,b,cl,!1,!1)}if(Z(a))return dl(a);if(a instanceof xi){b=a.Kc;if(b==null)a="";else if(typeof b==="string")a=b;else{if(oi){for(var c="",d=0,e=b.length-10240;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));
c+=String.fromCharCode.apply(null,d?b.subarray(d):b);b=btoa(c)}else b=oh(b);a=a.Kc=b}return a}E(!(a instanceof Uint8Array));return}return a}var bl;function el(a){E(!bl);return dl(a)}function dl(a){E(Z(a));a=bk?a[F(ck)]:a.u;H(a,"state is only maintained on arrays.");var b=a[Y]|0;Vj(a,b);return al(a,b,cl,void 0,!1)};if(typeof Proxy!=="undefined"){var gl=fl;new Proxy({},{getPrototypeOf:gl,setPrototypeOf:gl,isExtensible:gl,preventExtensions:gl,getOwnPropertyDescriptor:gl,defineProperty:gl,has:gl,get:gl,set:gl,deleteProperty:gl,apply:gl,construct:gl})}function fl(){throw Error("za");};var hl,il;function jl(a){switch(typeof a){case "boolean":return hl||(hl=[0,void 0,!0]);case "number":return a>0?void 0:a===0?il||(il=[0,void 0]):[-a,void 0];case "string":return[0,a];case "object":return H(a),E(a.length===2||a.length===3&&a[2]===!0),E(a[0]==null||typeof a[0]==="number"&&a[0]>=0),E(a[1]==null||typeof a[1]==="string"),a}}function kl(a,b,c){H(b);a=ll(a,b[0],b[1],c?1:2);b!==hl&&c&&Tj(a,8192);return a}function ml(a){H(a);return!!a[1]}
function ll(a,b,c,d){if(a!=null)for(var e=0;e<a.length;e++){var f=a[e];Array.isArray(f)&&mk(f)}if(a==null)e=96,c?(a=[c],e|=512):a=[],b&&(e=Yj(e,b));else{if(!Array.isArray(a))throw Error("Aa`"+JSON.stringify(a)+"`"+Oa(a));e=a;H(e,"state is only maintained on arrays.");e=e[Y]|0;if(1&e)throw Error("Ba");8192&e||!(64&e)||2&e||nl();if(Object.isFrozen(a)||!Object.isExtensible(a)||Object.isSealed(a))throw Error("Ca");if(e&1024)throw Error("Da");if(e&64)return d!==3||e&16384||Rj(a,e|=16384),Vj(a,e),d!==1&&
e&8192&&E(e&2,"state from binary must be immutable"),a;d===1||d===2||(e|=64);if(c&&(e|=512,c!==a[0]))throw Error("Ea`"+c+"`"+JSON.stringify(a[0])+"`"+Oa(a[0]));a:{c=a;var g=c.length;if(g){var h=g-1;f=c[h];if(ik(f)){e|=256;b=e&512?0:-1;g=hk(h,b);if(g>=1024)throw Error("Ga`"+g);for(var k in f)h=+k,h<g&&(h=gk(h,b),E(c[h]==null),c[h]=f[k],delete f[k]);e=Yj(e,g);break a}}if(b){k=Math.max(b,hk(g,e&512?0:-1));if(k>1024)throw Error("Ha`"+g);e=Yj(e,k)}}}d===3&&(e|=16384);Rj(a,e);d===1||d===2||E(e&64);return a}
function nl(){throw Error("Fa");};function ol(a,b){F(a);if(typeof a!=="object")return a;if(Array.isArray(a)){mk(a);H(a,"state is only maintained on arrays.");var c=a[Y]|0;if(a.length===0&&c&1)return;if(c&2)return a;var d;if(d=b)d=c===0||!!(c&32)&&!(c&64||!(c&16));return d?(Tj(a,34),c&4&&Object.freeze(a),a):al(a,c,ol,b!==void 0,!0)}if(Z(a))return E(Z(a)),E(Z(a)),b=bk?a[F(ck)]:a.u,H(b,"state is only maintained on arrays."),c=b[Y]|0,Vj(b,c),c&2?a:al(b,c,ol,!0,!0);if(a instanceof xi)return a;E(!(a instanceof Uint8Array))}
function pl(a){var b=a;E(Z(b));b=bk?b[F(ck)]:b.u;H(b,"state is only maintained on arrays.");var c=b[Y]|0;Vj(b,c);if(!(c&2))return a;H(b,"state is only maintained on arrays.");c=b[Y]|0;Vj(b,c);b=a=new a.constructor(al(b,c,ol,!0,!0));E(Z(b));b=bk?b[F(ck)]:b.u;Uj(b);return a};var rl=function(a,b){E(Object.isExtensible(a));E(Z(a));a=bk?a[F(ck)]:a.u;H(a,"state is only maintained on arrays.");var c=a[Y]|0;Vj(a,c);return ql(a,c,b)},ql=function(a,b,c,d){H(a,"state is only maintained on arrays.");(a[Y]|0)&512?E(d===rk):E(d===void 0);if(c===-1)return null;d=gk(c,b&512?0:-1);E(d>=0);var e=a.length-1;if(d>=e&&b&256)a=a[e][c];else if(d<=e)a=a[d];else return;return a},tl=function(a,b,c){E(Z(a));var d=bk?a[F(ck)]:a.u;H(d,"state is only maintained on arrays.");var e=d[Y]|0;Vj(d,e);
jk(e);sl(d,e,b,c);return a};function sl(a,b,c,d,e){H(a,"state is only maintained on arrays.");(a[Y]|0)&512?E(e===rk):E(e===void 0);e=b&512?0:-1;var f=gk(c,e);E(f>=0);var g=a.length-1;if(f>=g&&b&256)return a[g][c]=d,b;if(f<=g)return a[f]=d,b;d!==void 0&&(g=b>>15&1023||536870912,c>=g?(E(g!==536870912),E(!(b&256)),d!=null&&(f={},a[gk(g,e)]=(f[c]=d,f),b|=256,Rj(a,b))):a[f]=d);return b}function ul(a,b){if(!a)return a;Xj(b)?(E(Z(a)),b=bk?a[F(ck)]:a.u,b=Xj(b)):b=!0;E(b);return a}
function vl(a,b,c,d){c=c===void 0?!1:c;d=d===void 0?!1:d;mk(a,c);H(a,"state is only maintained on arrays.");var e=a[Y]|0;E(e&1);e&8192&&E(e&2,"state from binary must be immutable");c||(d||((c=Object.isFrozen(a))||(H(a,"state is only maintained on arrays."),c=!((a[Y]|0)&32)),E(c)),E(Xj(b)?Object.isFrozen(a):!0))}var xl=function(a,b){H(a,"state is only maintained on arrays.");var c=a[Y]|0;Vj(a,c,!0);return wl(a,c,b)};
function yl(a,b,c,d,e){E(Z(a));a=bk?a[F(ck)]:a.u;H(a,"state is only maintained on arrays.");var f=a[Y]|0;Vj(a,f);var g=2&f?1:d;e=!!e;d=zl(a,f,b);var h=d;H(h,"state is only maintained on arrays.");h=h[Y]|0;nk(d);if(!(4&h)){4&h&&(d=sk(d),h=Al(h,f),f=sl(a,f,b,d));for(var k=0,l=0;k<d.length;k++){var m=c(d[k]);m!=null&&(d[l++]=m)}l<k&&(d.length=l);h=Bl(h,f);c=(h|20)&-2049;h=c&=-4097;Rj(d,h);2&h&&Object.freeze(d)}g===1||g===4&&32&h?Cl(h)||(b=h,h|=2,h!==b&&Rj(d,h),Object.freeze(d)):(g===2&&Cl(h)&&(d=sk(d),
h=Al(h,f),h=Dl(h,f,e),Rj(d,h),f=sl(a,f,b,d)),Cl(h)||(b=h,h=Dl(h,f,e),h!==b&&Rj(d,h)));nk(d);e||vl(d,a,!1,e);return d}function zl(a,b,c,d){a=ql(a,b,c,d);return Array.isArray(a)?a:Pj}function Bl(a,b){a===0&&(a=Al(a,b),a|=16);return a|1}function Cl(a){return!!(2&a)&&!!(4&a)||!!(1024&a)}
function wl(a,b,c){jk(b);var d=!!(64&b)||!(8192&b),e=b&512?rk:void 0,f=zl(a,b,c,e),g=f!==Pj;if(d||!g){f===Pj?d=55:(d=f,H(d,"state is only maintained on arrays."),d=d[Y]|0);g=d;if(2&g||Cl(g)||4&g&&!(32&g))f=sk(f),d=0,g=Al(g,b),b=sl(a,b,c,f,e);g=Bl(g,b)&-13;g=Dl(g,b,!0);g!==d&&Rj(f,g)}return f}
var El=function(a,b,c){H(a,"state is only maintained on arrays.");var d=a[Y]|0;Vj(a,d,!0);var e=d&512?rk:void 0,f=ql(a,d,c,e);if(f!=null&&Z(f))return b=pl(f),b!==f&&sl(a,d,c,b,e),E(Z(b)),bk?b[F(ck)]:b.u;if(Array.isArray(f)){H(f,"state is only maintained on arrays.");var g=f[Y]|0;if(g&2){var h=kl(al(f,g,ol,!0,!0),b,!0);Uj(h)}else h=g&64?f:kl(h,b,!0)}else h=kl(void 0,b,!0);h!==f&&sl(a,d,c,h,e);return h};
function Fl(a,b,c,d){E(Z(a));a=bk?a[F(ck)]:a.u;H(a,"state is only maintained on arrays.");var e=a[Y]|0;Vj(a,e);var f=ql(a,e,c,d);b=Rk(f,b,e);b!==f&&b!=null&&sl(a,e,c,b,d);return ul(b,a)}
var Hl=function(a){var b=Gl;if(!(a=Fl(a,b,2))&&!(a=b[Jj])){a=new b;E(Z(a));var c=bk?a[F(ck)]:a.u;Tj(c,34);a=b[Jj]=a}return a},Il=function(a,b,c,d){b=Fl(a,b,c,d);if(b==null)return b;E(Z(a));a=bk?a[F(ck)]:a.u;H(a,"state is only maintained on arrays.");var e=a[Y]|0;Vj(a,e);if(!(e&2)){var f=pl(b);f!==b&&(b=f,sl(a,e,c,b,d))}return ul(b,a)},Kl=function(a){var b=Jl;E(Z(a));var c=bk?a[F(ck)]:a.u;H(c,"state is only maintained on arrays.");var d=c[Y]|0;Vj(c,d);var e=void 0,f=void 0;E(Z(a));a=bk?a[F(ck)]:a.u;
e=!!e;f&&(f=!(2&d));c=zl(a,d,10);var g=c;H(g,"state is only maintained on arrays.");var h=g[Y]|0;g=!!(4&h);if(!g){h=Bl(h,d);var k=c,l=d,m=!!(2&h);m&&(l|=2);for(var t=!m,r=!0,u=0,x=0;u<k.length;u++){var v=Rk(k[u],b,l);if(v instanceof b){if(!m){E(Z(v));var y=bk?v[F(ck)]:v.u;y=Xj(y);t&&(t=!y);r&&(r=y)}k[x++]=v}}x<u&&(k.length=x);h|=4;h=r?h|16:h&-17;h=t?h|8:h&-9;Rj(k,h);m&&Object.freeze(k)}if(f&&!(8&h)&&c.length){Cl(h)&&(c=sk(c),h=Al(h,d),sl(a,d,10,c));b=c;d=h;for(f=0;f<b.length;f++)k=b[f],h=pl(k),k!==
h&&(b[f]=h);d|=8;d=b.length?d&-17:d|16;Rj(b,d);h=d}Cl(h)||(b=h,h|=!c.length||16&h&&(!g||32&h)?2:1024,h!==b&&Rj(c,h),Object.freeze(c));if(!e){e=c;g=!1;g=g===void 0?!1:g;b=Xj(a);d=Xj(e);f=Object.isFrozen(e)&&d;vl(e,a,g);if(b||d)g?E(d):E(f);H(e,"state is only maintained on arrays.");E(!!((e[Y]|0)&4));if(d&&e.length)for(g=0;g<1;g++)ul(e[g],a)}return c},Ll=function(a,b,c,d){d!=null?Qk(d,F(b)):d=void 0;return tl(a,c,d)};function Al(a,b){2&a&&(a|=16);a=(2&b?a|2:a&-3)|32;return a&=-1025}
function Dl(a,b,c){32&b&&c||(a&=-33);return a}
var Ml=function(a,b,c){c=c===void 0?!1:c;var d;return(d=Bk(rl(a,b)))!=null?d:c},Nl=function(a,b){var c=c===void 0?0:c;var d;return(d=Ik(rl(a,b)))!=null?d:c},Ol=function(a,b){var c=c===void 0?"":c;var d;return(d=Pk(rl(a,b)))!=null?d:c},Pl=function(a){var b;return(b=Bk(rl(a,5)))!=null?b:void 0},Ql=function(a,b){var c;return(c=Pk(rl(a,b)))!=null?c:void 0},Rl=function(a,b,c){if(c!=null&&typeof c!=="boolean")throw Error("pa`"+Oa(c)+"`"+c);return tl(a,b,c)},Sl=function(a,b,c){if(c!=null){if(typeof c!==
"number")throw Fj(Jk(c));if(!wk(c))throw Fj(Jk(c));c>>>=0}return tl(a,b,c)},Tl=function(a,b,c){if(c!=null&&typeof c!=="string")throw Error("qa`"+c+"`"+Oa(c));return tl(a,b,c)},Ul=function(a,b,c){return tl(a,b,c==null?c:Ek(c))},Vl=function(a,b,c){E(Z(a));var d=bk?a[F(ck)]:a.u;H(d,"state is only maintained on arrays.");var e=d[Y]|0;Vj(d,e);jk(e);b=yl(a,b,Fk,2,!0);H(b,"state is only maintained on arrays.");if(Array.isArray(c))for(d=c.length,e=0;e<d;e++)b.push(Ek(c[e]));else for(c=w(c),d=c.next();!d.done;d=
c.next())b.push(Ek(d.value));nk(b);return a};var Wl=function(a,b,c){this.preventPassingToStructuredClone=lk;Hb(this,Wl,"The message constructor should only be used by subclasses");E(this.constructor!==Wl,"Message is an abstract class and cannot be directly constructed");a=ll(a,b,c,3);E(Z(this));H(a);bk?this[F(ck)]=a:this.u=a};n=Wl.prototype;n.toJSON=function(){return el(this)};n.Ta=function(){return JSON.stringify(el(this))};
n.getExtension=function(a){Hb(this,a.xf);var b=Hb(this,Wl);$k(b,a.va);return a.cb?a.ld?a.Bb(b,a.cb,a.va,void 0===ok?2:4,a.hb):a.Bb(b,a.cb,a.va,a.hb):a.ld?a.Bb(b,a.va,void 0===ok?2:4,a.hb):a.Bb(b,a.va,a.defaultValue,a.hb)};
n.hasExtension=function(a){E(!a.ld,"repeated extensions don't support hasExtension");var b=Hb(this,Wl);$k(b,a.va);a.cb?a=Fl(b,a.cb,a.va,a.hb)!==void 0:(E(!a.ld,"repeated extensions don't support getExtensionOrUndefined"),Hb(b,a.xf),b=Hb(b,Wl),$k(b,a.va),a=a.cb?a.Bb(b,a.cb,a.va,a.hb):a.Bb(b,a.va,null,a.hb),a=(a===null?void 0:a)!==void 0);return a};
n.clone=function(){var a=Hb(this,Wl);E(Z(a));var b=a;E(Z(b));b=bk?b[F(ck)]:b.u;H(b,"state is only maintained on arrays.");var c=b[Y]|0;Vj(b,c);b=a=new a.constructor(al(b,c,ol,!0,!0));E(Z(b));b=bk?b[F(ck)]:b.u;Uj(b);return a};n.lb=function(){E(Z(this));var a=bk?this[F(ck)]:this.u;return Xj(a)};dk=Wl;Wl.prototype[Lj]=fk;Wl.prototype.toString=function(){E(Z(this));return(bk?this[F(ck)]:this.u).toString()};var Xl=function(a,b){this.qc=a>>>0;this.jc=b>>>0},Zl=function(a){if(!a)return Yl||(Yl=new Xl(0,0));if(!/^\d+$/.test(a))return null;gj(a);return new Xl(Wi,Xi)},Yl,$l=function(a,b){this.qc=a>>>0;this.jc=b>>>0},bm=function(a){if(!a)return am||(am=new $l(0,0));if(!/^-?\d+$/.test(a))return null;gj(a);return new $l(Wi,Xi)},am;var cm=function(){this.I=[]};cm.prototype.length=function(){return this.I.length};cm.prototype.end=function(){var a=this.I;this.I=[];return a};cm.prototype.Va=function(a,b){E(a==Math.floor(a));E(b==Math.floor(b));E(a>=0&&a<4294967296);for(E(b>=0&&b<4294967296);b>0||a>127;)this.I.push(a&127|128),a=(a>>>7|b<<25)>>>0,b>>>=7;this.I.push(a)};cm.prototype.Xe=function(a,b){E(a==Math.floor(a));E(b==Math.floor(b));E(a>=0&&a<4294967296);E(b>=0&&b<4294967296);this.za(a);this.za(b)};
var dm=function(a,b){E(b==Math.floor(b));for(E(b>=0&&b<4294967296);b>127;)a.I.push(b&127|128),b>>>=7;a.I.push(b)},em=function(a,b){E(b==Math.floor(b));E(b>=-**********&&b<**********);if(b>=0)dm(a,b);else{for(var c=0;c<9;c++)a.I.push(b&127|128),b>>=7;a.I.push(1)}};n=cm.prototype;n.za=function(a){E(a==Math.floor(a));E(a>=0&&a<4294967296);this.I.push(a>>>0&255);this.I.push(a>>>8&255);this.I.push(a>>>16&255);this.I.push(a>>>24&255)};
n.Ig=function(a){E(a==Math.floor(a));E(a>=0&&a<1.8446744073709552E19);Zi(a);this.za(Wi);this.za(Xi)};n.Gg=function(a){E(a==Math.floor(a));E(a>=-**********&&a<**********);this.I.push(a>>>0&255);this.I.push(a>>>8&255);this.I.push(a>>>16&255);this.I.push(a>>>24&255)};n.Hg=function(a){E(a==Math.floor(a));E(a>=-0x7fffffffffffffff&&a<0x7fffffffffffffff);$i(a);this.Xe(Wi,Xi)};
n.We=function(a){E(a==Infinity||a==-Infinity||isNaN(a)||typeof a==="number"&&a>=-3.4028234663852886E38&&a<=3.4028234663852886E38);var b=bj(4);b.setFloat32(0,+a,!0);Xi=0;Wi=b.getUint32(0,!0);this.za(Wi)};n.Ve=function(a){E(typeof a==="number"||a==="Infinity"||a==="-Infinity"||a==="NaN");var b=bj(8);b.setFloat64(0,+a,!0);Wi=b.getUint32(0,!0);Xi=b.getUint32(4,!0);this.za(Wi);this.za(Xi)};n.Ue=function(a){E(typeof a==="boolean"||typeof a==="number");this.I.push(a?1:0)};
n.Dd=function(a){E(a==Math.floor(a));E(a>=-**********&&a<**********);em(this,a)};var fm=function(){this.Md=[];this.vb=0;this.B=new cm},gm=function(a,b){b.length!==0&&(a.Md.push(b),a.vb+=b.length)},im=function(a,b){hm(a,b,2);b=a.B.end();gm(a,b);b.push(a.vb);return b},jm=function(a,b){var c=b.pop();c=a.vb+a.B.length()-c;for(E(c>=0);c>127;)b.push(c&127|128),c>>>=7,a.vb++;b.push(c);a.vb++},hm=function(a,b,c){E(b>=1&&b==Math.floor(b));dm(a.B,b*8+c)},km=function(a,b,c){if(c!=null)switch(hm(a,b,0),typeof c){case "number":a=a.B;E(c==Math.floor(c));E(c>=0&&c<1.8446744073709552E19);$i(c);
a.Va(Wi,Xi);break;case "bigint":c=BigInt.asUintN(64,c);c=new Xl(Number(c&BigInt(4294967295)),Number(c>>BigInt(32)));a.B.Va(c.qc,c.jc);break;default:c=Zl(c),a.B.Va(c.qc,c.jc)}};n=fm.prototype;n.Gg=function(a,b){b!=null&&(lm(a,b,b>=-**********&&b<**********),b!=null&&(mm(a,b),hm(this,a,0),em(this.B,b)))};
n.Hg=function(a,b){if(b!=null){switch(typeof b){case "string":lm(a,b,bm(b));break;case "number":lm(a,b,b>=-0x7fffffffffffffff&&b<0x7fffffffffffffff);break;default:lm(a,b,b>=BigInt(-0x7fffffffffffffff)&&b<BigInt(0x7fffffffffffffff))}if(b!=null)switch(hm(this,a,0),typeof b){case "number":a=this.B;E(b==Math.floor(b));E(b>=-0x7fffffffffffffff&&b<0x7fffffffffffffff);$i(b);a.Va(Wi,Xi);break;case "bigint":b=BigInt.asUintN(64,b);b=new $l(Number(b&BigInt(4294967295)),Number(b>>BigInt(32)));this.B.Va(b.qc,
b.jc);break;default:b=bm(b),this.B.Va(b.qc,b.jc)}}};n.za=function(a,b){b!=null&&(lm(a,b,b>=0&&b<4294967296),b!=null&&(hm(this,a,0),dm(this.B,b)))};n.Ig=function(a,b){if(b!=null){switch(typeof b){case "string":lm(a,b,Zl(b));break;case "number":lm(a,b,b>=0&&b<1.8446744073709552E19);break;default:lm(a,b,b>=BigInt(0)&&b<BigInt(1.8446744073709552E19))}km(this,a,b)}};n.We=function(a,b){b!=null&&(hm(this,a,5),this.B.We(b))};n.Ve=function(a,b){b!=null&&(hm(this,a,1),this.B.Ve(b))};
n.Ue=function(a,b){b!=null&&(lm(a,b,typeof b==="boolean"||typeof b==="number"),hm(this,a,0),this.B.Ue(b))};n.Dd=function(a,b){b!=null&&(b=parseInt(b,10),mm(a,b),hm(this,a,0),em(this.B,b))};n.Xe=function(a,b){hm(this,a,1);this.B.Xe(b)};n.Va=function(a,b){hm(this,a,0);this.B.Va(b)};function mm(a,b){lm(a,b,b===Math.floor(b));lm(a,b,b>=-**********&&b<**********)}function lm(a,b,c){c||Bb("for ["+b+"] at ["+a+"]")};function nm(){var a=function(){throw Error("Ja");};Object.setPrototypeOf(a,a.prototype);return a}var om=nm(),pm=nm(),qm=nm(),rm=nm(),sm=nm(),tm=nm();var um=function(a,b,c,d){this.Ed=a;this.Fd=b;a=Ra(pm);this.Jg=!!a&&d===a||!1};function vm(a){var b=wm;var c=c===void 0?pm:c;return new um(a,b,!1,c)}function wm(a,b,c,d,e){b=xm(b,d);b!=null&&(c=im(a,c),e(b,a),jm(a,c))}var ym=vm(function(a,b,c,d,e){if(a.l!==2)return!1;yj(a,El(b,d,c),e);return!0}),zm=vm(function(a,b,c,d,e){if(a.l!==2)return!1;yj(a,El(b,d,c),e);return!0}),Am=Symbol(),Bm=Symbol(),Cm=Symbol(),Dm=Symbol(),Em=Symbol(),Fm,Gm;
function Hm(a,b,c,d){var e=d[a];if(e)return e;e={};e.Ug=d;e.Kb=E(jl(d[0]));var f=d[1],g=1;f&&f.constructor===Object&&(e.ae=f,f=d[++g],typeof f==="function"&&(Fm!=null&&(E(Fm===f),E(Gm===d[1+g])),e.Xf=!0,Fm!=null||(Fm=f),Gm!=null||(Gm=Eb(d[g+1])),f=d[g+=2]));for(var h={};f&&Im(f);){for(var k=0;k<f.length;k++)h[f[k]]=f;f=d[++g]}for(k=1;f!==void 0;){typeof f==="number"&&(E(f>0),k+=f,f=d[++g]);var l=void 0;if(f instanceof um)var m=f;else m=ym,g--;f=void 0;if((f=m)==null?0:f.Jg){f=d[++g];l=d;var t=g;typeof f===
"function"&&(E(f.length===0),f=f(),l[t]=f);Jm(f);l=f}f=d[++g];t=k+1;typeof f==="number"&&f<0&&(t-=f,f=d[++g]);for(;k<t;k++){var r=h[k];l?c(e,k,E(m),l,r):b(e,k,E(m),r)}}return d[a]=e}function Im(a){return Array.isArray(a)&&!!a.length&&typeof a[0]==="number"&&a[0]>0}function Jm(a){if(Array.isArray(a)&&a.length){var b=a[0];var c=jl(b);c!=null&&c!==b&&(a[0]=c);b=c!=null}else b=!1;E(b);return a}
function Km(a){return Array.isArray(a)?a[0]instanceof um?(E(a.length===2),Jm(a[1]),a):[zm,Jm(a)]:[Hb(a,um),void 0]}function xm(a,b){if(a instanceof Wl)return E(Z(a)),bk?a[F(ck)]:a.u;if(Array.isArray(a))return kl(a,b,!1)};function Om(a,b,c,d){var e=c.Ed;a[b]=d?function(f,g,h){return e(f,g,h,d)}:e}function Pm(a,b,c,d,e){var f=c.Ed,g,h;a[b]=function(k,l,m){return f(k,l,m,h||(h=Hm(Bm,Om,Pm,d).Kb),g||(g=Qm(d)),e)}}
function Qm(a){var b=a[Cm];if(b!=null)return b;var c=Hm(Bm,Om,Pm,a);b=c.Xf?function(d,e){return E(Fm)(d,e,c)}:function(d,e){H(d,"state is only maintained on arrays.");var f=d[Y]|0;Vj(d,f,!0);for(E(!(f&2));wj(e)&&e.l!=4;){var g=e.Lb,h=c[g];if(h==null){var k=c.ae;k&&(k=k[g])&&(k=Rm(k),k!=null&&(h=c[g]=k))}if(h==null||!h(e,d,g)){k=e;h=k.fb;xj(k);if(k.Xd)var l=void 0;else{var m=k.m.Z(),t=m-h;k.m.O=h;h=k.m.Ce(t);E(m==k.m.Z());l=h}m=k=h=void 0;t=d;H(t);l&&((h=(k=(m=t[Kj])!=null?m:t[Kj]=new Xk)[g])!=null?
h:k[g]=[]).push(l)}}if(e=Wk(d))e.kg=F(c.Ug[Em]);f&8192&&Tj(d,34);return!0};a[Cm]=b;a[Em]=Sm.bind(a);return b}
function Sm(a,b,c){var d=this[Bm],e=this[Cm],f=kl(void 0,d.Kb,!1),g=Wk(a);if(g){var h=!1,k=d.ae;if(k&&(g==null||Yk(g,function(t,r,u){if(u.length!==0)if(k[r])for(t=w(u),r=t.next();!r.done;r=t.next()){r=vj(r.value);try{h=!0,e(f,r)}finally{r.ce()}}else c==null||c(a,r,u)}),h)){var l=Wj(a);if(l&2&&l&16384)throw Error("Ka");var m=l&512?rk:void 0;qk(f,Wj(f),function(t,r){if(ql(a,l,t,m)!=null)switch(b==null?void 0:b.Yj){case 1:return;default:throw Error("La`"+t);}l=sl(a,l,t,r,m);delete g[t]})}}}
function Rm(a){a=Km(a);var b=Hb(a[0],um).Ed;if(a=a[1]){var c=Qm(Jm(a)),d=Hm(Bm,Om,Pm,Jm(a)).Kb;return function(e,f,g){return b(e,f,g,d,c)}}return b};function Tm(a,b,c){a[b]=c.Fd}function Um(a,b,c,d){var e,f,g=c.Fd;a[b]=function(h,k,l){return g(h,k,l,f||(f=Hm(Am,Tm,Um,d).Kb),e||(e=Vm(d)))}}function Vm(a){var b=a[Dm];if(!b){var c=Hm(Am,Tm,Um,a);b=function(d,e){return Wm(d,e,c)};a[Dm]=b}return b}function Wm(a,b,c){var d=Sj(a);qk(a,d|(ml(c.Kb)?512:0),function(e,f){if(f!=null){var g=Xm(c,e);g&&g(b,f,e)}});(a=Wk(a))&&Yk(a,function(e,f,g){gm(b,b.B.end());for(e=0;e<g.length;e++)gm(b,zi(g[e]))})}
function Xm(a,b){var c=a[b];if(c)return c;if(c=a.ae)if(c=c[b]){c=Km(c);var d=Hb(c[0],um).Fd;if(c=c[1]){c=Jm(c);var e=Vm(c),f=Hm(Am,Tm,Um,c).Kb;c=a.Xf?E(Gm)(f,e):function(g,h,k){return d(g,h,k,f,e)}}else c=d;return a[b]=c}};function Ym(a,b,c){if(Array.isArray(b)){H(b,"state is only maintained on arrays.");var d=b[Y]|0;if(d&4)return b;for(var e=0,f=0;e<b.length;e++){var g=a(b[e]);g!=null&&(E(typeof g!=="object"||g instanceof xi),b[f++]=g)}f<e&&(b.length=f);c&&(Rj(b,(d|21)&-6145),d&2&&Object.freeze(b));return b}}function Zm(a,b,c){return new um(a,b,!1,c)}function $m(a,b,c){return new um(a,b,om,c)}
function an(a,b,c){H(a,"state is only maintained on arrays.");var d=a[Y]|0;Vj(a,d,!0);H(a,"state is only maintained on arrays.");sl(a,d,b,c,(a[Y]|0)&512?rk:void 0)}function bn(a,b,c){if(a.l!==0&&a.l!==2)return!1;b=xl(b,c);a.l==2?Ej(a,qj,b):b.push(Cj(a));return!0}
var cn=Zm(function(a,b,c){if(a.l!==1)return!1;E(a.l==1);var d=a.m;a=oj(d);var e=oj(d);d=(e>>31)*2+1;var f=e>>>20&2047;a=4294967296*(e&1048575)+a;an(b,c,f==2047?a?NaN:d*Infinity:f==0?d*4.9E-324*a:d*Math.pow(2,f-1075)*(a+4503599627370496));return!0},function(a,b,c){a.Ve(c,zk(b))},nm()),dn=Zm(function(a,b,c){if(a.l!==5)return!1;E(a.l==5);var d=oj(a.m);a=(d>>31)*2+1;var e=d>>>23&255;d&=8388607;an(b,c,e==255?d?NaN:a*Infinity:e==0?a*1.401298464324817E-45*d:a*Math.pow(2,e-150)*(d+8388608));return!0},function(a,
b,c){a.We(c,zk(b))},nm()),en=Zm(function(a,b,c){if(a.l!==0)return!1;E(a.l==0);a=kj(a.m,ej);an(b,c,a);return!0},function(a,b,c){a.Hg(c,Nk(b))},nm()),fn=Zm(function(a,b,c){if(a.l!==0)return!1;an(b,c,Bj(a));return!0},function(a,b,c){a.Ig(c,Ok(b))},sm),gn=$m(function(a,b,c){if(a.l!==0&&a.l!==2)return!1;b=xl(b,c);a.l==2?Ej(a,nj,b):b.push(Bj(a));return!0},function(a,b,c){b=Ym(Ok,b,!1);if(b!=null)for(var d=0;d<b.length;d++)km(a,c,b[d])},sm),hn=Zm(function(a,b,c){if(a.l!==0)return!1;an(b,c,zj(a));return!0},
function(a,b,c){a.Gg(c,Ik(b))},qm),jn=$m(function(a,b,c){if(a.l!==0&&a.l!==2)return!1;b=xl(b,c);a.l==2?Ej(a,lj,b):b.push(zj(a));return!0},function(a,b,c){b=Ym(Ik,b,!0);if(b!=null)for(var d=0;d<b.length;d++){var e=a,f=c,g=b[d];g!=null&&(mm(f,g),hm(e,f,0),em(e.B,g))}},qm),kn=Zm(function(a,b,c){if(a.l!==5)return!1;E(a.l==5);a=oj(a.m);an(b,c,a);return!0},function(a,b,c){b=Kk(b);b!=null&&(lm(c,b,b>=0&&b<4294967296),hm(a,c,5),a.B.za(b))},nm()),ln=Zm(function(a,b,c){if(a.l!==0)return!1;E(a.l==0);a=pj(a.m);
an(b,c,a);return!0},function(a,b,c){a.Ue(c,Bk(b))},nm()),mn=Zm(function(a,b,c){if(a.l!==2)return!1;an(b,c,Dj(a));return!0},function(a,b,c){b=Pk(b);if(b!=null){var d=!0;d=d===void 0?!1:d;Db(b);if(mi){if(d&&(li?!b.isWellFormed():/(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])/.test(b)))throw Error("T");b=(ki||(ki=new TextEncoder)).encode(b)}else{for(var e=0,f=new Uint8Array(3*b.length),g=0;g<b.length;g++){var h=b.charCodeAt(g);if(h<128)f[e++]=h;else{if(h<2048)f[e++]=h>>6|192;
else{E(h<65536);if(h>=55296&&h<=57343){if(h<=56319&&g<b.length){var k=b.charCodeAt(++g);if(k>=56320&&k<=57343){h=(h-55296)*1024+k-56320+65536;f[e++]=h>>18|240;f[e++]=h>>12&63|128;f[e++]=h>>6&63|128;f[e++]=h&63|128;continue}else g--}if(d)throw Error("T");h=65533}f[e++]=h>>12|224;f[e++]=h>>6&63|128}f[e++]=h&63|128}}b=e===f.length?f:f.subarray(0,e)}hm(a,c,2);dm(a.B,b.length);gm(a,a.B.end());gm(a,b)}},nm()),nn,on=void 0;on=on===void 0?pm:on;
nn=new um(function(a,b,c,d,e){if(a.l!==2)return!1;d=kl(void 0,d,!0);H(b,"state is only maintained on arrays.");var f=b[Y]|0;Vj(b,f,!0);wl(b,f,c).push(d);yj(a,d,e);return!0},function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++){var g=a,h=c,k=e,l=xm(b[f],d);l!=null&&(h=im(g,h),k(l,g),jm(g,h))}},om,on);
var pn=Zm(function(a,b,c){if(a.l!==0)return!1;an(b,c,Aj(a));return!0},function(a,b,c){a.za(c,Kk(b))},rm),qn=$m(function(a,b,c){if(a.l!==0&&a.l!==2)return!1;b=xl(b,c);a.l==2?Ej(a,mj,b):b.push(Aj(a));return!0},function(a,b,c){b=Ym(Kk,b,!0);if(b!=null&&b.length){c=im(a,c);for(var d=0;d<b.length;d++)dm(a.B,b[d]);jm(a,c)}},rm),rn=Zm(function(a,b,c){if(a.l!==0)return!1;an(b,c,Cj(a));return!0},function(a,b,c){a.Dd(c,Ik(b))},tm),sn=$m(bn,function(a,b,c){b=Ym(Ik,b,!0);if(b!=null)for(var d=0;d<b.length;d++)a.Dd(c,
b[d])},tm),tn=$m(bn,function(a,b,c){b=Ym(Ik,b,!0);if(b!=null&&b.length){c=im(a,c);for(var d=0;d<b.length;d++)a.B.Dd(b[d]);jm(a,c)}},tm);var wn=function(){var a=un,b=vn;E(!0);this.va=4156379;this.xf=a;this.cb=b;this.ld=0;this.Bb=Il;this.defaultValue=void 0;this.hb=a.Cj!=null?rk:void 0};wn.prototype.register=function(){Tg(this)};function xn(a){if(a instanceof Wl)return a.constructor.U};(function(){var a=Ma.jspbGetTypeName;Ma.jspbGetTypeName=a?function(b){return a(b)||xn(b)}:xn})();var yn=Wl;function zn(a,b){return function(c,d){var e={Ad:!0};d&&Object.assign(e,d);c=vj(c,void 0,void 0,e);try{var f=new a;E(Z(f));var g=bk?f[F(ck)]:f.u;Qm(b)(g,c);var h=f}finally{c.ce()}return h}}
function An(a){return function(){var b=new fm;var c=Hb(this,Wl);E(Z(c));c=bk?c[F(ck)]:c.u;Wm(c,b,Hm(Am,Tm,Um,a));gm(b,b.B.end());c=new Uint8Array(b.vb);for(var d=b.Md,e=d.length,f=0,g=0;g<e;g++){var h=d[g];c.set(h,f);f+=h.length}E(f==c.length);b.Md=[c];return c}}function Bn(a){return function(b){Eb(a);if(b==null||b=="")b=Hb(new a,Wl);else{Db(b);b=JSON.parse(b);if(!Array.isArray(b))throw Error("Ia`"+Oa(b)+"`"+b);Tj(b,32);b=new a(b)}return b}};var Jl=function(a){yn.call(this,a)};q(Jl,yn);Jl.prototype.If=function(){return Ol(this,2)};Jl.U="wireless.mdl.UserAgentClientHints.BrandAndVersion";var Cn=[0,mn,-1];Jl.prototype.M=An(Cn);var Dn=function(a){yn.call(this,a)};q(Dn,yn);
var En=function(a,b){return Tl(a,2,b)},Fn=function(a,b){return Tl(a,3,b)},Gn=function(a,b){return Tl(a,4,b)},Hn=function(a,b){return Tl(a,5,b)},In=function(a,b){return Tl(a,9,b)},Jn=function(a,b){var c=Jl;E(Z(a));var d=bk?a[F(ck)]:a.u;H(d,"state is only maintained on arrays.");var e=d[Y]|0;Vj(d,e);jk(e);if(b==null)sl(d,e,10);else{var f=b;if(!Array.isArray(f))throw a="Expected array but got "+Oa(f)+": "+f,Fj(a);f=b;H(f,"state is only maintained on arrays.");for(var g=f=f[Y]|0,h=Cl(f),k=h||Object.isFrozen(b),
l=!0,m=!0,t=0;t<b.length;t++){var r=b[t];Qk(r,F(c));h||(E(Z(r)),r=bk?r[F(ck)]:r.u,r=Xj(r),l&&(l=!r),m&&(m=r))}h||(f=l?13:5,f=m?f|16:f&-17);k&&f===g||(b=sk(b),g=0,f=Al(f,e),f=Dl(f,e,!0));f!==g&&Rj(b,f);mk(b);sl(d,e,10,b)}return a},Kn=function(a,b){return Rl(a,11,b)},Ln=function(a,b){return Tl(a,1,b)},Mn=function(a,b){return Rl(a,7,b)};Dn.U="wireless.mdl.UserAgentClientHints";Dn.prototype.M=An([0,mn,-4,nn,Cn,ln,rn,mn,nn,Cn,ln]);var Nn="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function On(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Pn(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}
function Qn(a){if(!Pn(a))return null;var b=On(a);if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Nn).then(function(c){b.uach!=null||(b.uach=c);return c});return b.uach_promise=a}
function Rn(a){var b;return Kn(Jn(Hn(En(Ln(Gn(Mn(In(Fn(new Dn,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),((b=a.fullVersionList)==null?void 0:b.map(function(c){var d=new Jl;d=Tl(d,1,c.brand);return Tl(d,2,c.version)}))||[]),a.wow64||!1)}function Sn(a){var b,c;return(c=(b=Qn(a))==null?void 0:b.then(function(d){return Rn(d)}))!=null?c:null};var Tn=function(a,b,c,d){a=a===void 0?window:a;b=b===void 0?null:b;c=c===void 0?new Ta:c;d=d===void 0?Ff("current"):d;Zd.call(this);var e=this;this.global=a;this.Ca=b;this.F=c;this.Ie=d;this.fg=Ed(function(){return Id(e.global,"pagehide")}).g(qg(this.F,941));this.eg=Ed(function(){return Id(e.global,"load")}).g(qg(this.F,738),Re(1));this.di=Ed(function(){return Id(e.global,"resize")}).g(qg(this.F,741));this.onMessage=Ed(function(){return Id(e.global,"message")}).g(qg(this.F,740));this.document=new Lh(this.global,
this);this.j=new sg(new vg(this.L,this.F),new ug(this.L,this.F));this.G=new me(new zg(this),new dh(this),new of(this,new Dg(this)),new of(this,new gh(this)),new of(this,new bh(this)))};q(Tn,Zd);var Un=function(a){try{return!!a.global.sharedStorage}catch(b){return b}},Ag=function(a){var b=a.global;return!!a.global.HTMLFencedFrameElement&&!!b.fence&&typeof b.fence.reportEvent==="function"};Tn.prototype.Qb=function(a){Ag(this)&&this.global.fence.reportEvent(a)};
Tn.prototype.ee=function(){return this.fg.g(qg(this.F,942),W(this.h,1),N(function(){}))};var Vn=function(a){var b=new Tn(a.global.top,a.Ca);b.G=a.G;return b},Wn=function(a,b){b.start();return Id(b,"message").g(qg(a.F,740))};Tn.prototype.postMessage=function(a,b,c){c=c===void 0?[]:c;this.global.postMessage(a,b,c)};Tn.prototype.Jf=function(){return ah(this.global)?this.global.width:0};Tn.prototype.Gf=function(){return ah(this.global)?this.global.height:0};
var Xn=function(a,b){try{var c=di(b,a.global);return{left:c.left,top:c.top,width:c.Jf(),height:c.Gf()}}catch(d){return Rh}};Tn.prototype.validate=function(){var a=this.G.K()||Ag(this);return this.global&&this.j.fa()&&a};var sh=function(a){return(a=Sn(a.global))?Pc(a):null};
ea.Object.defineProperties(Tn.prototype,{sharedStorage:{configurable:!0,enumerable:!0,get:function(){try{return this.global.sharedStorage}catch(a){}}},L:{configurable:!0,enumerable:!0,get:function(){return window}},lc:{configurable:!0,enumerable:!0,get:function(){return!ah(this.global.top)}},je:{configurable:!0,enumerable:!0,get:function(){return this.lc||this.global.top!==this.global}},scrollY:{configurable:!0,enumerable:!0,get:function(){return this.global.scrollY}},MutationObserver:{configurable:!0,
enumerable:!0,get:function(){return this.L.MutationObserver}},ResizeObserver:{configurable:!0,enumerable:!0,get:function(){return this.L.ResizeObserver}},Wg:{configurable:!0,enumerable:!0,get:function(){return"vu"in this.global||"vv"in this.global}}});var Yn=!Yg&&!Og();function Zn(a,b){if(/-[a-z]/.test(b))return null;if(Yn&&a.dataset){if(Qg()&&!(b in a.dataset))return null;a=a.dataset[b];return a===void 0?null:a}return a.getAttribute("data-"+String(b).replace(/([A-Z])/g,"-$1").toLowerCase())};var $n={},ao=($n["data-google-av-cxn"]="_avicxn_",$n["data-google-av-cpmav"]="_cvu_",$n["data-google-av-metadata"]="_avm_",$n["data-google-av-adk"]="_adk_",$n["data-google-av-btr"]=void 0,$n["data-google-av-override"]=void 0,$n["data-google-av-dm"]=void 0,$n["data-google-av-immediate"]=void 0,$n["data-google-av-aid"]=void 0,$n["data-google-av-naid"]=void 0,$n["data-google-av-inapp"]=void 0,$n["data-google-av-slift"]=void 0,$n["data-google-av-itpl"]=void 0,$n["data-google-av-ext-cxn"]=void 0,$n["data-google-av-rs"]=
void 0,$n["data-google-av-flags"]=void 0,$n["data-google-av-turtlex"]=void 0,$n["data-google-av-ufs-integrator-metadata"]=void 0,$n["data-google-av-vattr"]=void 0,$n["data-google-av-vrus"]=void 0,$n),bo={},co=(bo["data-google-av-adk"]="googleAvAdk",bo["data-google-av-btr"]="googleAvBtr",bo["data-google-av-cpmav"]="googleAvCpmav",bo["data-google-av-dm"]="googleAvDm",bo["data-google-av-ext-cxn"]="googleAvExtCxn",bo["data-google-av-immediate"]="googleAvImmediate",bo["data-google-av-inapp"]="googleAvInapp",
bo["data-google-av-itpl"]="googleAvItpl",bo["data-google-av-metadata"]="googleAvMetadata",bo["data-google-av-naid"]="googleAvNaid",bo["data-google-av-override"]="googleAvOverride",bo["data-google-av-rs"]="googleAvRs",bo["data-google-av-slift"]="googleAvSlift",bo["data-google-av-cxn"]="googleAvCxn",bo["data-google-av-aid"]=void 0,bo["data-google-av-flags"]="googleAvFlags",bo["data-google-av-turtlex"]="googleAvTurtlex",bo["data-google-av-ufs-integrator-metadata"]="googleAvUfsIntegratorMetadata",bo["data-google-av-vattr"]=
"googleAvVattr",bo["data-google-av-vrus"]="googleAvVurs",bo);function eo(a,b){if(a.i===void 0)return null;try{var c;var d=(c=a.i.getAttribute(b))!=null?c:null;if(d!==null)return d}catch(g){}try{var e=ao[b];if(e&&(d=a.i[e],d!==void 0))return d}catch(g){}try{var f=co[b];if(f)return Zn(a.i,f)}catch(g){}return null}function fo(a){return N(function(b){return eo(b,a)})};var go=J(function(a){return N(function(b){return a.map(function(c){return eo(b,c)})})}(["data-google-av-cxn","data-google-av-turtlex"]),N(function(a){var b=w(a);a=b.next().value;b=b.next().value;if(!a){if(b!==null)return[];throw new ie;}return a.split("|")}));var ho=function(){return J(Bd(function(a){return a.element.g(go,Ke(function(){return M([""])})).g(N(function(b){return{ma:b,Wc:a}}))}),Te(function(a){return a.ma.sort().join(";")}),N(function(a){return a.Wc}))};function io(){return Bd(function(a){return Pc(jo(a)).g(hh(a.h))})}function jo(a){return a.document.querySelectorAll(".GoogleActiveViewElement,.GoogleActiveViewClass").map(function(b){return new uh(b)})};function ko(a){var b=a.eg,c=a.document.ci;return Pd(M({}),c,b).g(N(function(){return a}))};var mo=N(lo);function lo(a){var b=Number(eo(a,"data-google-av-rs"));if(!isNaN(b)&&b!==0)return b;var c;return(a=(c=a.i)==null?void 0:c.id)?a.startsWith("DfaVisibilityIdentifier")?6:a.startsWith("YtKevlarVisibilityIdentifier")?15:a.startsWith("YtSparklesVisibilityIdentifier")?17:a.startsWith("YtKabukiVisibilityIdentifier")?18:0:0};function no(){return J(Q(function(a){return a!==void 0}),N(function(a){return a}))};function oo(){return function(a){var b=[];return a.g(Q(function(c){if(c.i===void 0||b.some(function(d){return d.i===c.i}))return!1;b.push(c);return!0}))}};function po(a,b){b=b===void 0?Cc:b;return Pd(ko(a),b).g(io(),oo(),no(),W(a.h,1))};function qo(a,b){return new K(function(c){var d=!1,e=Array(b.length);e.fill(void 0);var f=new Set,g=new Set,h=function(t,r){a.lg?(e[r]=t,f.add(r),d||(d=!0,Wa(a,function(){d=!1;c.next(Mb(e))},1))):c.error(new je(r))},k=function(t,r){g.add(r);f.add(r);Wa(a,function(){c.error(t)},1)},l=function(t){g.add(t);Wa(a,function(){g.size===b.length&&c.complete()},1)},m=b.map(function(t,r){return t.subscribe(function(u){return void h(u,r)},function(u){return void k(u,r)},function(){return void l(r)})});return function(){m.forEach(function(t){return void t.unsubscribe()})}})}
;function ro(a,b,c){function d(){if(b.Ca){var y=b.Ca,A=y.next;var P={creativeId:b.hc.Fa(c),requiredSignals:e,signals:Object.assign({},f),hasPrematurelyCompleted:g,errorMessage:h,erroredSignalKey:k};P={specMajor:2,specMinor:0,specPatch:0,timestamp:se(b.j.now(),new qe(0,b.j.timeline)),instanceId:b.hc.Fa(b.ub),creativeState:P};A.call(y,P)}}for(var e=Object.keys(a),f={},g=!1,h=null,k=null,l={},m=new Set,t=[],r=[],u=w(e),x=u.next(),v={};!x.done;v={ja:void 0},x=u.next())v.ja=x.value,x=a[v.ja],x instanceof
X?(l[v.ja]=x.value,m.add(v.ja),b.Ca&&(f[String(v.ja)]=ue(x.value))):(x=x.g(R(function(y,A){return ne(y)||ne(A)?!1:y===A}),N(function(y){return function(A){b.Ca&&(f[String(y.ja)]=ue(A),d());var P={};return P[y.ja]=A,P}}(v)),Ke(function(y){return function(A){if(A instanceof je)throw new le(String(y.ja));throw A;}}(v)),gf(function(y){return function(){m.add(y.ja)}}(v),function(y){return function(A){k=String(y.ja);h=String(A);d()}}(v),function(y){return function(){m.has(y.ja)||(g=!0,d())}}(v))),r.push(v.ja),
t.push(x));(a=Object.keys(f).length>0)&&d();u=qo(b.h,t).g(Ke(function(y){if(y instanceof je)throw new ke(String(r[y.Ch]));throw y;}),N(function(y){return Object.freeze(Object.assign.apply(Object,[{},l].concat(z(y))))}));return(t=t.length>0)&&a?Pd(M(Object.freeze(l)),u):t?u:M(Object.freeze(l))};function so(a,b,c,d){var e=to(uo(vo(),wo),xo,yo);return a.F.Vb.bind(a.F)(733,function(){var f={};try{return b.g(Ke(function(g){d(Object.assign({},f,{error:g}));return Cc}),Bd(function(g){try{var h=c(a,g)}catch(l){return d(Object.assign({},f,{error:l instanceof Error?l:String(l)})),Cc}var k={};return ro(h,a,g.ub).g(gf(function(l){k=l}),bf(1),jd()).g(e,Ke(function(l){d(Object.assign({},k,{error:l}));return Cc}),Xe(void 0),N(function(){return!0}))})).g(df(function(g){return g+1},0),Ke(function(g){d(Object.assign({},
f,{error:g}));return Cc}))}catch(g){return d(Object.assign({},f,{error:g})),Cc}})()};function zo(a,b){return J(U(function(c){var d=a(c),e=b(c),f={};return d&&e&&f?new K(function(g){e(d,f,function(h){g.next(Object.assign({},c,{ab:h}));g.complete()});return function(){}}):Qd}),Q(function(c){return c.ab}))};var xo=J(Q(function(a){var b=a.G;var c=a.bc;var d=a.Wb;var e=a.Qb;var f=a.mb;var g=a.Oa;a=a.ac;return g!==void 0&&a!==void 0&&b!==void 0&&c!==void 0&&d!==void 0&&(!f||e!==void 0)}),ff(function(a){return!(a.Tf===!1&&a.tf!==void 0)},!1),Q(function(a){var b=a.Tf;var c=a.fd;var d=a.Oi;a=a.bc;return d?!!c&&a!==void 0&&(a==null?void 0:a.length)>0:!!b}),zo(function(a){return a.ac},function(a){return a.Oa}),N(function(a){a.mb||a.Wb(a.bc,a).forEach(function(b){a.G.H(b).sendNow()})}),Re(1),Pe());function Ao(a){var b=new Map;if(typeof a!=="object"||a===null)return b;Object.values(a).forEach(function(c){c&&typeof c.ia==="function"&&(b.has(c.clock.timeline)||b.set(c.clock.timeline,c.clock.now()))});return b};function Bo(a,b,c){var d=Co,e=Do;c=c===void 0?.01:c;return function(f){c>0&&Math.random()<=c&&(a.global.HTMLFencedFrameElement&&a.global.fence&&typeof a.global.fence.reportEvent==="function"&&a.global.fence.reportEvent({eventType:"active-view-error",eventData:"",destination:["buyer"]}),f=Object.assign({},f,{errorMessage:f.error instanceof Error&&f.error.message?f.error.message:String(f.error),uf:f.error instanceof Error&&f.error.stack?String(f.error.stack):null,jh:f.error instanceof Error&&f.error.name?
String(f.error.name):null,hh:String(a.F.yg),ih:f.escapedQueryId}),d(Object.assign({},f,{W:function(){return function(g){try{return e(Object.assign({},g))}catch(h){return{}}}}(),ma:[b]}),Ao(f)).forEach(function(g){a.G.H(g).sendNow()}))}};var yo=J(N(function(a){var b=a.G;var c=a.ph;if(b===void 0||c===void 0)return!1;if(a.tf!==void 0)return!0;if(c===null)return!1;for(a=0;a<c;a++)b.H("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=extra&rnd="+Math.floor(Math.random()*1E7)).sendNow();return!0}),ff(function(a){return!a}),Pe());var Eo=J(Q(function(a){return!!a.fd}),Q(function(a){var b=a.shouldSendExplicitDisplayMeasurablePing;a=a.jb;var c,d;return(d=b&&((c=a==null?void 0:a.length)!=null?c:0)>0)!=null?d:!1}),Q(function(a){return a.W!==void 0&&a.jb!==void 0&&a.wb!==void 0&&a.Gb!==void 0&&a.G!==void 0}),N(function(a){return Object.assign({},a,{vd:Ao(a)})}),N(function(a){a.wb(Object.assign({},a,{ma:a.jb,W:a.W,zc:a.Gb,Lc:3,Bc:"m"}),a.vd).forEach(function(b){a.G.H(b).sendNow()});return!0}),ff(function(a){return!a}),Pe());var Do=function(a){return{id:a.zc,mcvt:a.uc,p:a.Xc,asp:a.uj,tm:a.xd,tu:a.yd,mtos:a.vc,tos:a.Ic,v:a.Vg,bin:a.Ld,avms:a.cg,bs:a.hf,mc:a.ag,"if":a.dh,vu:a.fh,app:a.kb,mse:a.we,mtop:a.xe,itpl:a.le,adk:a.Kd,exk:a.wj,rs:a.Sa,la:a.Wf,cr:a.qe,uach:a.Jc,vs:a.Lc,r:a.Bc,pay:a.wh,co:a.Xg,rst:a.Pg,rpt:a.Og,isd:a.Ah,lsd:a.Ph,context:a.hh,msg:a.errorMessage,stack:a.uf,name:a.jh,ec:a.xh,sfr:a.Je,met:a.dc,wmsd:a.Re,pv:a.Uj,epv:a.yj,pbe:a.Rf,fle:a.yh,vae:a.zh,spb:a.vg,sfl:a.ug,ffslot:a.Jh,reach:a.Bi,io2:a.Bd,rxdbg:a.Zj,
omida:a.Ij,omidp:a.Pj,omidpv:a.Qj,omidor:a.Oj,omidv:a.Sj,omids:a.Rj,omidam:a.Hj,omidct:a.Jj,omidia:a.Mj,omiddc:a.Kj,omidlat:a.Nj,omiddit:a.Lj,qid:a.ih}};function to(){var a=B.apply(0,arguments);return function(b){var c=b.g(bf(1),jd());b=a.map(function(d){return c.g(d,Xe(!0))});return O(b).g(Re(1),Pe())}};function uo(){var a=B.apply(0,arguments);return function(b){var c=b.g(bf(1),jd());b=a.map(function(d){return c.g(d,Xe(!0))});return Pd.apply(null,z(b)).g(Re(1),Pe())}};function vo(){var a=to(Eo,Fo),b=Go;return function(c){var d=c.g(bf(1),jd());c=d.g(a,Xe(!0));d=d.g(J(b,bf(),jd()),Xe(!0));c=O([c,d]);return Ud(c,d).g(Re(1),Pe())}};var Go=function(a){var b=[];return a.g(N(function(c){var d=c.G,e=c.qh,f=c.Ic,g=c.Hi,h=c.W,k=c.Gi,l=c.wg,m=c.wb,t=c.Pe,r=c.fd,u=c.Rf,x=c.vg,v=c.ug,y=c.Ne;if(!c.Cf||!r||c.vc===void 0||f===void 0||g===void 0||h===void 0||k===void 0||m===void 0||d===void 0)return!1;if(c.mb){if(l===void 0)return!1;g=c.Qb;if(!g)return!1;g({eventType:"active-view-time-on-screen",eventData:y!=null?y:"",destination:["buyer"]});return!0}if(!(u||v||l))return!1;y=Ao(c);var A;t=(A=t==null?void 0:t.qa(y).value)!=null?A:!1;A=m(Object.assign({},
c,{zc:k,Lc:t?4:3,Bc:l!=null?l:"u",W:h,ma:g}),y);if(u){for(;b.length>g.length;)c=void 0,(c=b.shift())==null||c.deactivate();A.forEach(function(S,la){la>=b.length?b.push(d.H(S)):b[la].url=S});return x&&e&&l!==void 0?(A.forEach(function(S){e.H(S).sendNow()}),!0):l!==void 0}if(x&&e&&l!==void 0)return A.forEach(function(S){e.H(S).sendNow()}),!0;if(v&&e){for(;b.length>g.length;)x=void 0,(x=b.shift())==null||x.deactivate();var P=m(Object.assign({},c,{zc:k,Lc:t?4:3,Bc:l!=null?l:"u",W:h,ma:["https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=fetch&later&lidartos"]}),
y)[0];A.forEach(function(S,la){la>=b.length?b.push(d.H(P,{sf:!0})):b[la].url=P});return l!==void 0?(A.forEach(function(S){e.H(S).sendNow()}),!0):l!==void 0}return l!==void 0?(A.forEach(function(S){d.H(S).sendNow()}),!0):!1}),ff(function(c){return!c}),Pe())};function Ho(a){return function(b){return b.g(N(function(c){a.lg||Bb("Assertion on queued Observable output failed");return c}))}};function Io(a){return function(b){return new K(function(c){var d=!1,e=b.g(Ho(a)).subscribe(function(f){d=!0;c.next(f)},c.error.bind(c),c.complete.bind(c));Wa(a,function(){d||c.next(null)},3);return e})}};function Jo(a,b){return function(c){return c.g(U(function(d){return new K(function(e){function f(){h.disconnect();k.unsubscribe()}var g=a.MutationObserver;if(g&&d.i!==void 0){var h=new g(function(l){e.next(l)});h.observe(d.i,b);var k=d.released.subscribe(f);return f}})}))}};var Ko={sj:0,Vi:1,Yi:2,Wi:3,0:"UNKNOWN",1:"DEFER_MEASUREMENT",2:"DO_NOT_DEFER_MEASUREMENT",3:"DEFER_MEASUREMENT_AND_PING"};function Lo(a,b){var c=b.g(Jo(a,{attributes:!0}),W(a.h,1));return O([b,c.g(W(a.h,1),Io(a.h))]).g(N(function(d){return w(d).next().value}),fo("data-google-av-dm"),N(Mo))}function Mo(a){return a&&a in Ko?Number(a):2};function No(a){if(a.Th===3)return null;if(a.wg!==void 0){var b=a.bh===!1?"n":null;if(b!==null)return b}return a.bd instanceof ce?"msf":a.Rd instanceof de?"c":a.ah===!1?"pv":a.bd||a.Rd?"x":null}
var wo=J(Q(function(a){return a.jb!==void 0&&a.W!==void 0&&a.wb!==void 0&&a.Gb!==void 0&&a.G!==void 0}),Q(function(a){return No(a)!==null}),zo(function(a){return a.Pc},function(a){return a.Oa}),N(function(a){if(a.mb){var b=a.Qb;if(b){var c;b({eventType:"active-view-unmeasurable",eventData:(c=a.Ne)!=null?c:"",destination:["buyer"]})}}else{c=void 0;var d=No(a);if(d==="x"){var e,f=(e=a.bd)!=null?e:a.Rd;f&&(b=f.stack,c=f.message)}a.wb(Object.assign({},a,{ma:a.jb,W:a.W,zc:a.Gb,Lc:2,Bc:d,errorMessage:c,
uf:b}),Ao(a)).forEach(function(g){a.G.H(g).sendNow()})}}),Re(1),Pe());var Oo=function(){this.startTime=Math.floor(Date.now()/1E3-1704067200);this.sequenceNumber=0},Po=function(a){var b=a.sequenceNumber.toString(10).padStart(2,"0");b=""+a.startTime+b;a.sequenceNumber<99&&a.sequenceNumber++;return b};function Qo(a,b){return typeof a==="string"?encodeURIComponent(a):typeof a==="number"?String(a):Array.isArray(a)?a.map(function(c){return Qo(c,b)}).join(","):a instanceof qe?a.toString():a&&typeof a.ia==="function"?Qo(a.qa(b).value,b):a===!0?"1":a===!1?"0":a===void 0||a===null?null:a instanceof Oo?Po(a):[a.top,a.left,a.top+a.height,a.left+a.width].join()}
function Ro(a,b){a=Object.entries(a).map(function(c){var d=w(c);c=d.next().value;d=d.next().value;d=Qo(d,b);return d===null?"":c+"="+d}).filter(function(c){return c!==""});return a.length?a.join("&"):""};var So=/(?:\[|%5B)([a-zA-Z0-9_]+)(?:\]|%5D)/g,gc=dc(ec(),"google3.javascript.ads.common.url_macros_substitutor",Tb).Qh;
function To(a,b){return a.replace(So,function(c,d){try{var e=b!==null&&d in b?b[d]:void 0;if(e==null)return hc("No value supplied for unsupported macro: "+d),c;if(e.toString()==null)return hc("The toString method of value returns null for macro: "+d),c;e=e.toString();if(e==""||!/^[\s\xa0]*$/.test(e==null?"":String(e)))return encodeURIComponent(e).replace(/%2C/g,",");hc("Null value supplied for macro: "+d)}catch(f){hc("Failed to set macro: "+d)}return c})};function Uo(a,b){var c=Object.assign({},a),d=a.Jc;c=(delete c.Jc,c);c=a.W(c);var e=Ro(c,b);return Jb(a.ma,function(f){var g="";typeof d==="string"&&(g="&"+Ro({uach:d},b));var h={};return To(f,(h.VIEWABILITY=e,h))+g})};function Co(a,b){var c=a.W(a),d=Ro(c,b);return d?Jb(a.ma,function(e){e=e.indexOf("?")>=0?e:e+"?";e="?&".indexOf(e.slice(-1))>=0?e:e+"&";return e+d}):a.ma};function Vo(a,b){return Jb(a,function(c){if(typeof b.Jc==="string"){var d="&"+Ro({uach:b.Jc},new Map);return c.substring(c.length-7)=="&adurl="?c.substring(0,c.length-7)+d+"&adurl=":c+d}return c})};var Fo=J(Q(function(a){return a.W!==void 0&&a.jb!==void 0&&a.wb!==void 0&&a.Gb!==void 0&&a.G!==void 0}),N(function(a){return Object.assign({},a,{vd:Ao(a)})}),Q(function(a){var b=a.Pe;var c=a.fd;a=a.vd;var d;return!!c&&((d=b==null?void 0:b.qa(a).value)!=null?d:!1)}),zo(function(a){return a.Qc},function(a){return a.Oa}),N(function(a){var b=a.G,c=a.Ne;if(a.mb){var d=a.Qb;if(!d)return!1;d({eventType:"active-view-viewable",eventData:c!=null?c:"",destination:["buyer"]});return!0}c=a.wb(Object.assign({},
a,{ma:a.jb,W:a.W,zc:a.Gb,Lc:4,Bc:"v"}),a.vd);(d=a.Td)&&d.length>0&&a.Wb&&a.Wb(d,a).forEach(function(e){b.H(e).sendNow()});(d=a.Qe)&&d.length>0&&a.Wb&&a.Wb(d,a).forEach(function(e){b.H(e).sendNow()});c.forEach(function(e){b.H(e,{Yb:a.te}).sendNow()});return!0}),ff(function(a){return!a}),Pe());function Wo(a,b,c,d){var e=Object.keys(c).map(function(h){return h}),f=e.filter(function(h){var k=c[h];h=d[h];return k instanceof X&&h instanceof X&&k.value===h.value}),g=f.reduce(function(h,k){var l={};return Object.assign({},h,(l[k]=c[k],l))},{});return e.reduce(function(h,k){if(f.indexOf(k)>=0)return h;var l={};return Object.assign({},h,(l[k]=b.g(U(function(m){return(m=m?c[k]:d[k])&&(m instanceof K||I(m.ob)&&I(m.subscribe))?m:m.R(a)})),l))},g)};function Xo(a){return J(N(function(){return!0}),T(!1),W(a,1))};function Yo(a){return a.length<=0?Cc:O(a.map(function(b){var c=0;return b.g(N(function(d){return{index:c++,value:d}}))})).g(Q(function(b){return b.every(function(c){return c.index===b[0].index})}),N(function(b){return b.map(function(c){return c.value})}))};function Zo(a,b){a.Ba&&(a.rb=a.Ba);a.Ba=b;a.rb&&a.rb.value?(b=Math.max(0,se(b.timestamp,a.rb.timestamp)),a.totalTime+=b,a.oa+=b):a.oa=0;return a}function $o(){return J(df(Zo,{totalTime:0,oa:0}),N(function(a){return a.totalTime}))}function ap(){return J(df(Zo,{totalTime:0,oa:0}),N(function(a){return a.oa}))};function bp(a,b){return J(fo("data-google-av-metadata"),N(function(c){if(c===null)return b(void 0);c=c.split("&").map(function(d){return d.split("=")}).filter(function(d){return d[0]===a});if(c.length===0)return b(void 0);c=c[0].slice(1).join("=");return b(c)}))};var cp={Ri:"asmreq",Si:"asmres"};var dp=function(a){yn.call(this,a)};q(dp,yn);dp.prototype.og=function(a){Sl(this,1,a)};dp.U="tagging.common.osd.AdSpeedMetricsRequest";dp.prototype.M=An([0,pn]);var ep=function(a){yn.call(this,a)};q(ep,yn);ep.U="tagging.common.osd.AdSpeedMetricsResponse.Box";var fp=[0,hn,-3];ep.prototype.M=An(fp);var gp=function(a){yn.call(this,a)};q(gp,yn);gp.prototype.og=function(a){Sl(this,1,a)};var hp=Bn(gp);gp.U="tagging.common.osd.AdSpeedMetricsResponse";gp.prototype.M=An([0,pn,ln,fp,hn,-1]);function ip(a,b){var c=c===void 0?Vn(a):c;var d=new MessageChannel;b=b.g(N(function(f){return Number(f)}),Q(function(f){return!isNaN(f)&&f!==0}),gf(function(f){var g=new dp;g.og(f);f={type:"asmreq",payload:g.Ta()};c.postMessage(f,"*",[d.port2])}),Re(1));var e=Wn(a,d.port1).g(Q(function(f){return typeof f.data==="object"}),N(function(f){var g=f.data,h=Object.values(cp).includes(g.type);g=typeof g.payload==="string";if(!h||!g||f.data.type!=="asmres")return null;try{return hp(f.data.payload)}catch(k){return null}}),
Q(function(f){return f!==null}),N(function(f){return f}));return b.g(U(function(f){return M(f).g(Ne(e))}),Q(function(f){var g=w(f);f=g.next().value;g=g.next().value;if(Kk(rl(g,1))!=null){var h=h===void 0?0:h;var k;g=((k=Kk(rl(g,1)))!=null?k:h)===f}else g=!1;return g}),N(function(f){f=w(f);f.next();return f.next().value}),hh(a.h))};function jp(a,b,c){var d=b.rc.g(Re(1),U(function(){return ip(a,c)}),Q(function(f){return Ml(f,2)&&Fl(f,ep,3)!==void 0&&Ik(rl(f,4))!=null&&Ik(rl(f,5))!=null}),Re(1),hh(a.h));b=d.g(N(function(f){return{x:Nl(Il(f,ep,3),2),y:Nl(Il(f,ep,3),1)}}),R(function(f,g){return f.x===g.x&&f.y===g.y}),W(a.h,1));var e=d.g(N(function(f){return Nl(f,4)}),W(a.h,1));d=d.g(N(function(f){return Nl(f,5)}),W(a.h,1));return{Ah:e,Rg:b,Ph:d}};function kp(a,b){return b.rc.g(Re(1),N(function(){return a.j.now().round()}))};var lp=N(function(a){return[a.value.X.width,a.value.X.height]});function mp(a,b){return function(c){return Yo(b.map(function(d){return c.g(a(d))}))}};function np(){var a;return J(gf(function(b){return void(a=b.timestamp)}),ap(),N(function(b){return{timestamp:a,value:Math.round(b)}}))};var op=function(a,b){this.kf=a;this.options=b;this.ne=this.me=null},pp=function(a,b){b?a.ne||(b=Object.assign({},a.options,{delay:100,trackVisibility:!0}),a.ne=new IntersectionObserver(a.kf,b)):a.me||(a.me=new IntersectionObserver(a.kf,a.options))},qp=function(a,b){a=b?a.ne:a.me;if(!a)throw new fe;return a};op.prototype.observe=function(a,b){qp(this,a).observe(b)};op.prototype.unobserve=function(a,b){qp(this,a).unobserve(b)};op.prototype.disconnect=function(a){qp(this,a).disconnect()};
op.prototype.takeRecords=function(a){return qp(this,a).takeRecords()};var rp={da:"ns",ha:Rh,X:Rh,ba:new L,S:"ns",J:Rh,V:Rh,na:{x:0,y:0}};function sp(a,b){return Sh(a.X,b.X)&&Sh(a.J,b.J)&&Sh(a.ha,b.ha)&&Sh(a.V,b.V)&&a.S===b.S&&a.ba===b.ba&&a.da===b.da&&a.na.x===b.na.x&&a.na.y===b.na.y};var tp=function(a){try{return a.getBoundingClientRect()}catch(b){return{left:0,top:0,right:0,bottom:0}}};function up(a,b){return function(c){return function(d){var e=d.g(af(new L),jd());d=c.element.g(R());e=e.g(N(function(f){return f.value}));return O([d,e,b]).g(N(function(f){var g=w(f);f=g.next().value;var h=g.next().value;g=g.next().value;if(f.i===void 0)var k={top:0,left:0,width:0,height:0};else{k=f.i.getBoundingClientRect();var l=f.i,m=a.global,t=new Vh(0,0);var r=(r=Yh(l))?r.defaultView:window;if(Ug(r,"parent")){do{if(r==m){var u=l,x=Yh(u);Fb(u,"Parameter is required");var v=new Vh(0,0);var y=(x?
Yh(x):document).documentElement;u!=y&&(u=tp(u),x=$h(Zh(x).ic),v.x=u.left+x.x,v.y=u.top+x.y)}else v=E(l),v=tp(v),v=new Vh(v.left,v.top);t.x+=v.x;t.y+=v.y}while(r&&r!=m&&r!=r.parent&&(l=r.frameElement)&&(r=r.parent))}k={top:t.y,left:t.x,width:k.width,height:k.height}}k=Uh(k,h.na);m=Th(k,h.ha);t=a.j.now();r=Object;l=r.assign;if(g!==2||a.lc||m.width<=0||m.height<=0)var A=!1;else try{var P=a.document.elementFromPoint(m.left+m.width/2,m.top+m.height/2);A=P?!vp(P,f):!1}catch(S){A=!1}return{timestamp:t,value:l.call(r,
{},h,{S:"geo",V:A?rp.V:m,J:k})}}),hh(a.h))}}}function vp(a,b,c){c=c===void 0?0:c;return a.i===void 0||b.i===void 0?!1:a.i===b.i||bi(b.i,function(d){return d===a.i})?!0:b.i.ownerDocument&&b.i.ownerDocument.defaultView&&b.i.ownerDocument.defaultView===b.i.ownerDocument.defaultView.top?!1:c<10&&b.i.ownerDocument&&b.i.ownerDocument.defaultView&&b.i.ownerDocument.defaultView.frameElement?vp(a,new uh(b.i.ownerDocument.defaultView.frameElement),c+1):!0};function wp(a){return function(b){return b.g(a.ResizeObserver?xp(a):yp(a),bf(1),jd())}}
function xp(a){return function(b){return b.g(U(function(c){var d=a.ResizeObserver;if(!d||c.i===void 0)return M(rp.J);var e=(new K(function(f){function g(){c.i!==void 0&&h.unobserve(c.i);h.disconnect();k.unsubscribe()}if(c.i===void 0)return f.complete(),function(){};var h=new d(function(l){l.forEach(function(m){f.next(m)})});h.observe(c.i);var k=c.released.subscribe(g);return g})).g(qg(a.F,736),N(function(f){return f.contentRect}));return Pd(M(c.i.getBoundingClientRect()),e)}),R(Sh))}}
function yp(a){return function(b){var c=b.g(Jo(a,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),d=a.di;c=Pd(b.g(N(function(){return th("resize")})),c,d);return O(b,c).g(qg(a.F,737),N(function(e){e=w(e).next().value;return e.i===void 0?void 0:e.i.getBoundingClientRect()}),no(),R(Sh))}};function zp(a,b){var c=Ap(a,b).g(bf(1),jd());return function(d){return function(e){e=e.g(U(function(f){return f.element}),R());return O([c,e]).g(U(function(f){var g=w(f);f=g.next().value;g=g.next().value;return Bp(a,f.Hh,wp(a),f.bi,d,f.rh,g)}),hh(a.h))}}}
function Cp(a,b,c){var d=zp(a,c)(b);return function(e){var f=d(M(e));return function(g){return O([g,f]).g(N(function(h){var k=w(h);h=k.next().value;k=k.next().value;var l=Uh(k.value.J,h.value.na),m=Th(Uh(k.value.V,h.value.na),h.value.ha);return{timestamp:h.timestamp.maximum(k.timestamp),value:Object.assign({},h.value,{S:"nio",V:m,J:l})}}))}}}function Dp(a){return N(function(b){return b.value.da!=="nio"?b:Object.assign({},b,{value:Object.assign({},b.value,{ha:Xn(a,!0),X:Xn(a,!0)})})})}
function Ep(a,b){return M(b).g(a,N(function(){return b}))}function Ap(a,b){return a.j.timeline!==oe?$c(new ce(2)):a.MutationObserver?typeof IntersectionObserver==="undefined"?$c(new ce(0)):(new K(function(c){var d=new L,e=new op(d.next.bind(d),{threshold:[].concat(z(b))});c.next({bi:d.g(qg(a.F,735)),Hh:e,rh:function(f){f=e.takeRecords(f);f.length>0&&d.next(f)}})})).g(Re(1),bf(1),jd()):$c(new ce(1))}function Fp(a){return Oc(a.sort(function(b,c){return b.time-c.time}),nd)}
function Bp(a,b,c,d,e,f,g){return new K(function(h){function k(){x||(x=!0,g.i!==void 0&&b.unobserve(e,g.i),m.unsubscribe(),u.unsubscribe(),r.unsubscribe(),v.unsubscribe())}if(g.i!==void 0){pp(b,e);b.observe(e,g.i);var l=new Bc({timestamp:a.j.now(),value:Object.assign({},rp,{da:"nio",S:"nio"})}),m=d.g(Bd(function(y){return Fp(y)}),Q(function(y){return y.target===g.i}),N(function(y){return{timestamp:new qe(y.time,oe),value:{da:"nio",ha:y.rootBounds||Rh,X:y.rootBounds||Xn(a,!0),ba:t,S:"nio",V:y.intersectionRect,
J:y.boundingClientRect,na:{x:0,y:0},isIntersecting:y.isIntersecting,Zf:y.isVisible}}}),af(l),jd()).subscribe(h),t=new L,r=t.subscribe(function(){f(e);h.next({timestamp:a.j.now(),value:l.value.value});g.i!==void 0&&(b.unobserve(e,g.i),b.observe(e,g.i))}),u=Ep(c,g).subscribe(function(){t.next()}),x=!1,v=g.released.subscribe(function(){return k()});return k}})};function Gp(a,b){var c=a.ee().g(N(function(){return"b"}));return Ud(b,c).g(Re(1),W(a.h,1))};function Hp(a){return function(b){var c;return b.g(gf(function(d){return void(c=d.timestamp)}),N(function(d){return d.value}),a,N(function(d){return{timestamp:c,value:d}}))}};var Ip=function(a){return a.V.width*a.V.height/(a.J.width*a.J.height)},Jp=Hp(J(N(function(a){var b;return(b=a.ad)!=null?b:Ip(a)}),N(function(a){return isFinite(a)?a:0}))),Kp=Hp(J(N(function(a){var b;return(b=a.ad)!=null?b:Ip(a)}),N(function(a){return isFinite(a)?a:-1})));var Lp=function(a,b){this.a=a;this.b=b;if(a.clock.timeline!==b.clock.timeline)throw Error();};Lp.prototype.Y=function(a){return a instanceof Lp?this.a.Y(a.a)&&this.b.Y(a.b):!1};Lp.prototype.pa=function(a){var b=this.a.pa(a).value,c=this.b.pa(a).value;return{timestamp:a,value:[b,c]}};
ea.Object.defineProperties(Lp.prototype,{active:{configurable:!0,enumerable:!0,get:function(){return this.a.active||this.b.active}},clock:{configurable:!0,enumerable:!0,get:function(){return this.a.clock}},C:{configurable:!0,enumerable:!0,get:function(){var a=this.a.C.timestamp.maximum(this.b.C.timestamp),b=this.a.C.timestamp.equals(a)?this.a.C.value:this.a.pa(a).value,c=this.b.C.timestamp.equals(a)?this.b.C.value:this.b.pa(a).value;return{timestamp:a,value:[b,c]}}}});var Mp=function(a,b){this.input=a;this.nd=b;this.C={timestamp:this.input.C.timestamp,value:this.nd(this.input.C.value)}};Mp.prototype.Y=function(a){return a instanceof Mp?this.input.Y(a.input)&&this.nd===a.nd:!1};Mp.prototype.pa=function(a){a=this.input.pa(a);return{timestamp:a.timestamp,value:this.nd(a.value)}};ea.Object.defineProperties(Mp.prototype,{active:{configurable:!0,enumerable:!0,get:function(){return this.input.active}},clock:{configurable:!0,enumerable:!0,get:function(){return this.input.clock}}});function Np(a,b,c){c=c===void 0?function(d,e){return d===e}:c;return a.timestamp.equals(b.timestamp)&&c(a.value,b.value)};var Op=function(a,b,c){this.clock=a;this.C=b;this.active=c};Op.prototype.Y=function(a){return a instanceof Op?this.active===a.active&&this.clock.timeline===a.clock.timeline&&Np(this.C,a.C):!1};Op.prototype.pa=function(a){return{timestamp:a,value:this.C.value+(this.active?Math.max(0,se(a,this.C.timestamp)):0)}};var Pp=function(){};Pp.prototype.ia=function(){return this.pa(this.clock.now())};Pp.prototype.qa=function(a){var b=this.clock.timeline,c,d=(c=a.get(b))!=null?c:this.clock.now();a.set(b,d);return this.pa(d)};Pp.prototype.map=function(a){return new Qp(this,a)};Pp.prototype.ra=function(a){return new Rp(this,a)};var Rp=function(){Lp.apply(this,arguments);this.map=Pp.prototype.map;this.ra=Pp.prototype.ra;this.ia=Pp.prototype.ia;this.qa=Pp.prototype.qa};q(Rp,Lp);
var Sp=function(){Op.apply(this,arguments);this.map=Pp.prototype.map;this.ra=Pp.prototype.ra;this.ia=Pp.prototype.ia;this.qa=Pp.prototype.qa};q(Sp,Op);var Qp=function(){Mp.apply(this,arguments);this.map=Pp.prototype.map;this.ra=Pp.prototype.ra;this.ia=Pp.prototype.ia;this.qa=Pp.prototype.qa};q(Qp,Mp);function Tp(a,b){a.Ba&&(a.rb=a.Ba);a.Ba=b;a.rb&&a.rb.value?(b=Math.max(0,se(b.timestamp,a.rb.timestamp)),a.totalTime+=b,a.oa+=b):a.oa=0;return a}function Up(a){return J(df(Tp,{totalTime:0,oa:0}),N(function(b){return new Sp(a,{timestamp:b.Ba.timestamp,value:b.totalTime},b.Ba.value)}))}function Vp(a){return J(df(Tp,{totalTime:0,oa:0}),N(function(b){return new Sp(a,{timestamp:b.Ba.timestamp,value:b.oa},b.Ba.value)}))};function Wp(a){return J(Vp(a),N(function(b){return b.map(function(c){return Math.round(c)})}))};var Xp=function(a,b){this.C=b;this.ia=Pp.prototype.ia;this.qa=Pp.prototype.qa;this.map=Pp.prototype.map;this.ra=Pp.prototype.ra;this.clock=a};Xp.prototype.Y=function(a){return a.active};Xp.prototype.pa=function(){return this.C};ea.Object.defineProperties(Xp.prototype,{active:{configurable:!0,enumerable:!0,get:function(){return!1}}});function Yp(a,b){return b.g(N(function(c){return new Xp(a.j,{timestamp:a.j.now(),value:c})}))};function Zp(a,b){return a>=1?!0:a<=0?!1:a>=b};function $p(a){return function(b){return b.g(hf(a),N(function(c){var d=w(c);c=d.next().value;d=d.next().value;return{timestamp:c.timestamp,value:Zp(c.value,d)}}))}};var aq=N(function(a){if(a.value.da==="omid"){if(a.value.S==="nio")return"omio";if(a.value.S==="geo")return"omgeo"}return a.value.S==="geo"||a.value.S==="nio"?a.value.da:a.value.S});function bq(){return J(Q(function(a,b){return b>0}),cq,T(-1),R())}var cq=J(Q(function(a){return!isNaN(a)}),df(function(a,b){return isNaN(a)?b:Math.min(a,b)},NaN),R());var dq=Hp(J(N(function(a){return a.V.width*a.V.height/(a.ha.width*a.ha.height)}),N(function(a){return isFinite(a)?Math.min(1,a):0})));function eq(a,b,c){return a?O([b,c]).g(Q(function(d){var e=w(d);d=e.next().value;e=e.next().value;return d.timestamp.equals(e.timestamp)}),N(function(d){var e=w(d);d=e.next().value;e=e.next().value;return d.value>e.value?d:e})):b}function fq(a){return function(b){var c=b.g(Jp),d=b.g(dq);return a instanceof K?a.g(U(function(e){return eq(e,c,d)})):eq(a.value,c,d)}};var gq=J(Hp(N(function(a){a=a.ad?a.J.width*a.J.height*a.ad/(a.X.width*a.X.height):a.V.width*a.V.height/(a.X.width*a.X.height);return isFinite(a)?a:0})));function hq(a,b,c,d){var e=d.cd,f=d.Zd,g=d.Eg,h=d.ef,k=d.ue,l=d.bg,m=d.gd;d=d.Bg;b=iq(a,c,b);c=jq(a,c);d=kq(b,d);var t=lq(a,e,l,b),r=t.g(N(function(C){return C.value}),R(),W(a,1),df(function(C,ba){return Math.max(C,ba)},0)),u=t.g(N(function(C){return C.value}),bq(),W(a,1)),x=b.g(Kp,N(function(C){return C.value}),Re(2),R(),W(a,1));g=mq(a,b,g,h);var v=g.g(T(!1),R(),N(function(C){return C?k:f}));h=t.g($p(v),R(),W(a,1));var y=O([h,b]).g(Q(function(C){var ba=w(C);C=ba.next().value;ba=ba.next().value;return C.timestamp.equals(ba.timestamp)}),
N(function(C){var ba=w(C);C=ba.next().value;ba=ba.next().value;return{visible:C.value,geometry:ba.value.J}}),df(function(C,ba){return!ba.visible&&C.visible?C:ba},{visible:!1,geometry:Rh}),N(function(C){return C.geometry}),T(Rh),W(a,1),R(Sh));l=l instanceof K?l.g(R(),Qe()):Qd;v=O([l,v]).g(Qe());var A=b.g(Q(function(C){return C.value.da!=="ns"&&C.value.S!=="ns"}),df(function(C){return C+1},0),T(0),W(a,1)),P=c.g(Qe(!0),T(!1),W(a,1));P=O([m,P]).g(N(function(C){var ba=w(C);C=ba.next().value;ba=ba.next().value;
return C&&!ba}),W(a,1));var S=b.g(gq,R()),la=S.g(N(function(C){return C.value}),df(function(C,ba){return Math.max(C,ba)},0),R(),W(a,1)),G=S.g(N(function(C){return C.value}),bq(),W(a,1));return{He:l,Gc:v,wa:{ki:b,cg:b.g(aq),Xc:y.g(R(Sh)),visible:h.g(R(Np)),Le:t.g(R(Np)),ag:r,Xh:u,hf:b.g(lp,R(Nb)),Ji:S,Rh:la,Wh:G,bd:c,ba:(new X(new L)).R(a),Wf:g,cd:e,gd:m,Cf:P,Ki:A,Oh:x,Bd:d}}}function jq(a,b){return b.g(Q(function(){return!1}),N(function(c){return c}),Ke(function(c){return(new X(c)).R(a)}))}
function kq(a,b){a=O([a,b]).g(N(function(e){var f=w(e);e=f.next().value;if(f.next().value&&e.value.isIntersecting)return e.value.Zf}),R());var c=a.g(N(function(e){return e===void 0?!0:e}),df(function(e,f){return e||!f},!1)),d=a.g(df(function(e,f){return f===void 0?e:f?!1:e!=null?e:!0},void 0),N(function(e){return!!e}));return O([b,Wd(a,c,d)]).g(N(function(e){var f=w(e);e=f.next().value;var g=w(f.next().value);f=g.next().value;var h=g.next().value;g=g.next().value;var k=0;if(!e)return 0;if(f===void 0)return 16;
f&&(k|=1);f||(k|=2);h&&(k|=4);g&&(k|=8);return k}))}function iq(a,b,c){return b.g(Td(Qd),W(a,1)).g(R(function(d,e){return Np(d,e,sp)}),T({timestamp:c.now(),value:rp}),W(a,1))}function lq(a,b,c,d){c=d.g(fq(c),Hp(N(function(e){return Math.round(e*100)/100})),W(a,1));return b instanceof X?c:O([c,b]).g(N(function(e){var f=w(e);e=f.next().value;f=f.next().value;return{timestamp:f.timestamp.maximum(e.timestamp),value:f.value?0:e.value}}),R(Np),W(a,10))}
function mq(a,b,c,d){b=[b.g(N(function(e){return e.value.J.width*e.value.J.height>=242500}))];c instanceof K&&b.push(c.g(N(function(e){return!!e})));c=O(b);return d?c.g(N(function(e){return e.some(function(f){return f})}),T(!1),R(),W(a,1)):(new X(!1)).R(a)};var nq=function(a){this.j=a;this.ud=null;this.timeout=new L},pq=function(a,b){oq(a);a.ud=a.j.setTimeout(function(){return void a.timeout.next()},b)},oq=function(a){a.ud!==null&&(a.j.clearTimeout(a.ud),a.ud=null)};function qq(a,b,c,d){var e=rq.xg,f=new nq(b);c=c.g(T(void 0),U(function(){oq(f);return d})).g(N(function(g){oq(f);var h=g.C,k=g.active;h.value>=e||!k||(k=b.now(),k=Math.max(0,se(k,h.timestamp)),pq(f,Math.max(0,e-h.value-k)));return g.map(function(l){return l>=e})}));return O([c,Pd(f.timeout,M(void 0))]).g(N(function(g){return w(g).next().value}),ff(function(g){return!g.ia().value},!0),W(a,1))};function sq(a){var b=new Sp(a,{timestamp:a.now(),value:0},!1);return J(Vp(a),df(function(c,d){return c.C.value>d.C.value?new Sp(a,c.C,!1):d},b),N(function(c){return c.map(function(d){return Math.round(d)})}))};function tq(a){return function(b){return J($p(M(b)),sq(a))}};function uq(a){return function(b){return J(Hp(N(function(c){return Zp(c,b)})),Up(a),N(function(c){return c.map(function(d){return Math.round(d)})}))}};function vq(a){return a.map(function(b){return b.map(function(c){return[c]})}).reduce(function(b,c){return b.ra(c).map(function(d){return d.flat()})})}function wq(a,b){return a.ra(b).map(function(c){var d=w(c);c=d.next().value;d=d.next().value;return c-d})}
function xq(a,b,c,d,e,f){var g=yq;if(g.length>1)for(var h=0;h<g.length-1;h++)if(g[h]<g[h+1])throw Error();h=f.g(T(void 0),U(function(){return d.g(Wp(a))}),R(function(k,l){return k.Y(l)}),W(b,1));f=f.g(T(void 0),U(function(){return d.g(sq(a))}),R(function(k,l){return k.Y(l)}),W(b,1));return{xd:e.g(T(void 0),U(function(){return c.g(N(function(k){return{timestamp:k.timestamp,value:!0}}),Up(a))}),R(function(k,l){return k.Y(l)}),W(b,1)),yd:e.g(T(void 0),U(function(){return c.g(N(function(k){return{timestamp:k.timestamp,
value:k.value===0}}),Up(a))}),R(function(k,l){return k.Y(l)}),W(b,1)),vc:e.g(T(void 0),U(function(){return c.g(mp(tq(a),g))}),N(vq),R(function(k,l){return k.Y(l)}),W(b,1)),Ic:e.g(T(void 0),U(function(){return c.g(mp(uq(a),g),N(function(k){return k.map(function(l,m){return m>0?wq(l,k[m-1]):l})}))}),N(vq),R(function(k,l){return k.Y(l)}),W(b,1)),uc:f,bb:h.g(R(function(k,l){return k.Y(l)}),W(b,1))}};function zq(a){var b;if(b=Aq(a))b=!Bq(a,"abgcp")&&!Bq(a,"abgc")&&!(typeof a.id==="string"&&a.id==="abgb")&&!(typeof a.id==="string"&&a.id==="mys-abgc")&&!Bq(a,"cbb");return b}function Bq(a,b){return a.classList?a.classList.contains(b):(" "+a.className+" ").indexOf(" "+b+" ")>-1}function Aq(a){try{var b=a.getBoundingClientRect();return b&&b.height>=30&&b.width>=30}catch(c){return!1}}
function Cq(a,b){if(a.i===void 0||!a.i.children)return a;for(var c=Mb(a.i.children);c.length;){var d=b?c.filter(zq):c.filter(Aq);if(d.length===1)return new uh(d[0]);if(d.length>1)break;c=Pb(c,function(e){return Mb(e.children)})}return a}
function Dq(a,b,c,d,e){if(c)return{Wc:b,sb:M(null)};c=b.element.g(N(function(f){a:if(f.i===void 0||Aq(f.i))f={od:f,sb:"mue"};else{var g=Cq(f,e);if(g.i!==void 0&&Aq(g.i))f={od:g,sb:"ie"};else{if(d||a.je)if(g=a.document.querySelector(".GoogleActiveViewInnerContainer")){f={od:new uh(g),sb:"ce"};break a}f={od:f,sb:"mue"}}}return f}),ef());return{Wc:{ub:b.ub,element:c.g(N(function(f){return f.od}))},sb:c.g(N(function(f){return f.sb}))}};function Eq(a,b,c,d){var e=d.cd,f=d.Zd,g=d.Eg,h=d.ef,k=d.ue,l=d.bg,m=d.gd;d=d.Bg;b=Fq(a,c,b);c=Gq(a,c);d=Hq(b,d);var t=Iq(a,e,l,b),r=t.g(N(function(G){return G.value}),R(),W(a,1),df(function(G,C){return Math.max(G,C)},0)),u=t.g(N(function(G){return G.value}),bq(),W(a,1)),x=b.g(Kp,N(function(G){return G.value}),Re(2),R(),W(a,1));g=Jq(a,b,g,h);var v=g.g(T(!1),R(),N(function(G){return G?k:f}));h=t.g($p(v),R(),W(a,1));var y=O([h,b]).g(Q(function(G){var C=w(G);G=C.next().value;C=C.next().value;return G.timestamp.equals(C.timestamp)}),
N(function(G){var C=w(G);G=C.next().value;C=C.next().value;return{visible:G.value,geometry:C.value.J}}),df(function(G,C){return!C.visible&&G.visible?G:C},{visible:!1,geometry:Rh}),N(function(G){return G.geometry}),T(Rh),W(a,1),R(Sh));l=l instanceof K?l.g(R(),Qe()):Qd;v=O([l,v]).g(Qe());var A=b.g(Q(function(G){return G.value.da!=="ns"&&G.value.S!=="ns"}),df(function(G){return G+1},0),T(0),W(a,1)),P=c.g(Qe(!0),T(!1),W(a,1));P=O([m,P]).g(N(function(G){var C=w(G);G=C.next().value;C=C.next().value;return G&&
!C}),W(a,1));var S=b.g(gq,R()),la=S.g(N(function(G){return G.value}),df(function(G,C){return Math.max(G,C)},0),R(),W(a,1));a=S.g(N(function(G){return G.value}),bq(),W(a,1));return{He:l,Gc:v,wa:{ki:b,cg:b.g(aq),Xc:y.g(R(Sh)),visible:h.g(R(Np)),Le:t.g(R(Np)),ag:r,Xh:u,hf:b.g(lp,R(Nb)),Ji:S,Rh:la,Wh:a,bd:c,ba:b.g(N(function(G){return G.value.ba})),Wf:g,cd:e,gd:m,Cf:P,Ki:A,Oh:x,Bd:d}}}
function Gq(a,b){return b.g(Q(function(){return!1}),N(function(c){return c}),Ke(function(c){return(new X(c)).R(a)}))}function Fq(a,b,c){return b.g(Td(Qd),W(a,1)).g(R(function(d,e){return Np(d,e,sp)}),T({timestamp:c.now(),value:rp}),W(a,1))}
function Iq(a,b,c,d){c=d.g(fq(c),Hp(N(function(e){return Math.round(e*100)/100})),W(a,1));return b instanceof X?c:O([c,b]).g(N(function(e){var f=w(e);e=f.next().value;f=f.next().value;return{timestamp:f.timestamp.maximum(e.timestamp),value:f.value?0:e.value}}),R(Np),W(a,1))}
function Jq(a,b,c,d){b=[b.g(N(function(e){return e.value.J.width*e.value.J.height>=242500}))];c instanceof K&&b.push(c.g(N(function(e){return!!e})));c=O(b);return d?c.g(N(function(e){return e.some(function(f){return f})}),T(!1),R(),W(a,1)):(new X(!1)).R(a)}
function Hq(a,b){a=O([a,b]).g(N(function(e){var f=w(e);e=f.next().value;if(f.next().value&&e.value.isIntersecting)return e.value.Zf}),R());var c=a.g(N(function(e){return e===void 0?!0:e}),df(function(e,f){return e||!f},!1)),d=a.g(df(function(e,f){return f===void 0?e:f?!1:e!=null?e:!0},void 0),N(function(e){return!!e}));return O([b,Wd(a,c,d)]).g(N(function(e){var f=w(e);e=f.next().value;var g=w(f.next().value);f=g.next().value;var h=g.next().value;g=g.next().value;var k=0;if(!e)return 0;if(f===void 0)return 16;
f&&(k|=1);f||(k|=2);h&&(k|=4);g&&(k|=8);return k}))};var Kq=J(fo("data-google-av-itpl"),N(function(a){return Number(a)}),N(function(a){return isNaN(a)?1:a}));var Lq={Qi:"addEventListener",bj:"getMaxSize",cj:"getScreenSize",dj:"getState",ej:"getVersion",pj:"removeEventListener",lj:"isViewable"},Mq=function(a,b){this.sa=null;this.Fh=new L;b=b||this.Li;var c=a.je,d=!a.lc;if(c&&d){var e=a.global.top.mraid;if(e){this.Vc=b(e);this.sa=e;this.tb=3;return}}(a=a.global.mraid)?(this.Vc=b(a),this.sa=a,this.tb=c?d?2:1:0):(this.tb=-1,this.Vc=2)};Mq.prototype.addEventListener=function(a,b){return this.Rb("addEventListener",a,b)};
Mq.prototype.removeEventListener=function(a,b){return this.Rb("removeEventListener",a,b)};Mq.prototype.If=function(){var a=this.Rb("getVersion");return typeof a==="string"?a:""};Mq.prototype.getState=function(){var a=this.Rb("getState");return typeof a==="string"?a:""};var Nq=function(a){a=a.Rb("isViewable");return typeof a==="boolean"?a:!1},Oq=function(a){if(a.sa)return a=a.sa.AFMA_LIDAR,typeof a==="string"?a:void 0};
Mq.prototype.Li=function(a){return a?a.IS_GMA_SDK?Object.values(Lq).every(function(b){return typeof a[b]==="function"})?0:1:2:1};Mq.prototype.Rb=function(a){var b=B.apply(1,arguments);if(this.sa)try{return this.sa[a].apply(this.sa,z(b))}catch(c){this.Fh.next(a)}};ea.Object.defineProperties(Mq.prototype,{rf:{configurable:!0,enumerable:!0,get:function(){if(this.sa){var a=this.sa.AFMA_LIDAR_EXP_1;return a===void 0?void 0:!!a}},set:function(a){this.sa&&(this.sa.AFMA_LIDAR_EXP_1=a)}}});function Pq(a,b){return(new Mq(a)).tb!==-1?(new X(!0)).R(a.h):b.g(fo("data-google-av-inapp"),N(function(c){return c!==null}),W(a.h,1))};var Rq=function(a,b){var c=this;this.j=a;this.ve=this.md=null;this.oi=b.g(R()).subscribe(function(d){Qq(c);c.ve=d})},Sq=function(a,b){Qq(a);a.md=a.j.setTimeout(function(){var c;return void((c=a.ve)==null?void 0:c.next())},b)},Qq=function(a){a.md!==null&&a.j.clearTimeout(a.md);a.md=null};Rq.prototype.dispose=function(){Qq(this);this.oi.unsubscribe();this.ve=null};function Tq(a,b,c,d,e){var f=rq.xg;var g=g===void 0?new Rq(b,d):g;return(new K(function(h){var k=c.g(T(void 0),U(function(){return Uq(e)})).g(N(function(l){var m=l.value;l=l.timestamp;var t=m.visible;m=m.bb;var r=m>=f;r||!t?Qq(g):(l=Math.max(0,se(b.now(),l)),Sq(g,Math.max(0,f-m-l)));return r}),df(function(l,m){return m||l},!1),R()).subscribe(h);return function(){g.dispose();k.unsubscribe()}})).g(ff(function(h){return!h},!0),W(a,1))}
function Uq(a){return Yo([a,a.g(np())]).g(N(function(b){var c=w(b);b=c.next().value;c=c.next().value;return{timestamp:b.timestamp,value:{visible:b.value,bb:c.value}}}),R(function(b,c){return Np(b,c,function(d,e){return d.bb===e.bb&&d.visible===e.visible})}))};function Vq(a,b){return{Kd:b.g(fo("data-google-av-adk")),bc:b.g(fo("data-google-av-btr"),R(),N(function(c){return c===null?[]:c.split("|").filter(function(d){return d!==""})})),Td:b.g(fo("data-google-av-cpmav"),R(),N(function(c){return c===null?[]:c.split("|").filter(function(d){return d!==""})})),Qe:b.g(fo("data-google-av-vrus"),R(),N(function(c){return c===null?[]:c.split("|").filter(function(d){return d!==""})})),eh:Lo(a,b),flags:b.g(fo("data-google-av-flags"),R()),kb:Pq(a,b),qe:b.g(bp("cr",function(c){return c===
"1"}),R()),Kh:b.g(bp("omid",function(c){return c==="1"}),R()),le:b.g(Kq),metadata:b.g(fo("data-google-av-metadata")),Sa:b.g(mo),ma:b.g(go),Pi:b.g(bp("la",function(c){return c==="1"}),R()),mb:b.g(fo("data-google-av-turtlex"),N(function(c){return c!==null}),R()),te:b.g(fo("data-google-av-vattr"),N(function(c){return c!==null}),R())}};function Wq(){return J(ap(),df(function(a,b){return Math.max(a,b)},0),N(function(a){return Math.round(a)}))};function Xq(a){return J($p(M(a)),Wq())};function Yq(a,b,c,d,e){c=c.g(N(function(){return!1}));d=O([e,d]).g(U(function(f){f=w(f).next().value;return Zq(b,f)}));return Pd(M(!1),c,d).g(R(),W(a.h,1))}function Zq(a,b){return a.g(N(function(c){return b||c===0||c===2}))};var $q=[33,32],ar=J(Kq,N(function(a){return $q.indexOf(a)>=0}),R());function br(a,b,c,d,e,f){var g=c.g(N(function(k){return k===9})),h=b.element.g(ar);c=e.g(Q(function(k){return k}),U(function(){return O([g,h])}),N(function(k){var l=w(k);k=l.next().value;return!l.next().value||k}),R());f=O([c,d.g(R()),f]).g(N(function(k){var l=w(k);k=l.next().value;var m=l.next().value;l=l.next().value;return Dq(a,b,!k,m,l)}),bf(1),jd());d=f.g(N(function(k){return k.Wc}));f=f.g(U(function(k){return k.sb}),T(null),R(),W(a.h,1));return{Qa:d,dc:f}};function cr(a){var b=b===void 0?!1:b;return J(U(function(c){return Oh(a.document,c,b)}),W(a.h,1))};var dr=function(a,b,c,d,e,f){this.rc=b.element.g(cr(a),W(a.h,1));this.sg=Yq(a,c,b.element,this.rc,d);c=br(a,b,e,d,this.sg,f);d=c.dc;this.Qa=c.Qa;this.dc=d;this.Re=Pd((new X(1)).R(a.h),b.element.g(Re(1),N(function(){return 2}),W(a.h,1)),this.rc.g(Re(1),N(function(){return 3}),W(a.h,1)),this.sg.g(Q(Boolean),Re(1),N(function(){return 0}),W(a.h,1))).g(ff(function(g){return g!==0},!0),W(a.h,0))};function er(a,b){return a&&b===0?15:a||b!==1?null:14}function fr(a,b,c){return b instanceof K?b.g(U(function(d){return(d=er(d,c))?$c(new ce(d)):a})):(b=er(b.value,c))?$c(new ce(b)):a};function gr(a){var b=new ce(13);if(a.length<1)return{chain:Cc,Pd:Cc};var c=new L,d=a[0];return{chain:a.slice(1).reduce(function(e,f){return e.g(Ke(function(g){c.next(g);return f}))},d).g(Ke(function(e){c.next(e);return $c(b)}),af(new L),jd()),Pd:c}};var hr=function(){};var ir=function(a,b){this.context=a;this.Ei=b};q(ir,hr);ir.prototype.Za=function(a,b){var c=this.Ei.map(function(f){return f.Za(a,b)}),d=gr(c.map(function(f){return f.gb})),e=d.Pd.g(jr());return{gb:d.chain.g(W(this.context.h,1)),Ya:Object.assign.apply(Object,[{Je:e,bk:d.Pd}].concat(z(c.map(function(f){return f.Ya}))))}};var jr=function(){return df(function(a,b){b instanceof ce?a.push(b.Uh):a.push(-1);return a},[])};function kr(a,b){var c=a.g(af(new L),jd());return U(function(d){return c.g(b(d))})};function lr(a,b){if(a.lc)return $c(new ce(6));var c=new L;return Pd(M({}),b,c).g(N(function(){return{timestamp:a.j.now(),value:{da:"geo",ha:mr(a),X:Xn(a,!0),ba:c,na:{x:0,y:0}}}}),hh(a.h))}function mr(a){var b=Xn(a,!1);if(!a.je||!ah(a.global.parent)||a.global.parent===a.global)return b;var c=new Tn(a.global.parent,a.Ca);c.G=a.G;c=mr(c);a=a.global.frameElement.getBoundingClientRect();return Th(Uh(Th(c,a),{x:b.left-a.left,y:b.top-a.top}),b)};var nr=function(a,b){this.context=a;this.qb=b};q(nr,hr);nr.prototype.Za=function(a,b){var c=kr(lr(this.context,this.qb),up(this.context,b.Sa));return{gb:fr(a.Qa.g(c),b.kb,0),Ya:{}}};var or=function(a,b,c){c=c===void 0?zp(a,b):c;this.context=a;this.Ih=c};q(or,hr);or.prototype.Za=function(a,b){var c=this.Ih(b.Cg);return{gb:fr(a.Qa.g(c,Dp(this.context)),b.kb,0),Ya:{}}};function pr(a,b,c,d,e){var f=f===void 0?new Mq(a):f;var g=g===void 0?mf(a.j,500):g;var h=h===void 0?mf(a.j,100):h;e=M(f).g(qr(c),gf(function(k){d.next(k.tb)}),rr(a,h),sr(a),tr(a,e),bf(1),jd());f=new L;b=Pd(M({}),b,f);return e.g(ur(a,f,b,g,c),W(a.h,1))}
function tr(a,b){return J(function(c){return O([c,b])},Se(function(c){var d=w(c);c=d.next().value;return d.next().value!==9||Nq(c)?M(!0):vr(a,c,"viewableChange").g(Q(function(e){return w(e).next().value}),Re(1))}),N(function(c){return w(c).next().value}))}
function qr(a){return U(function(b){if(b.tb===-1)return a.next("if"),$c(new ce(7));if(b.Vc!==0)switch(b.Vc){case 1:return a.next("mm"),$c(new ce(18));case 2:return a.next("ng"),$c(new ce(17));default:return a.next("i"),$c(new ce(8))}return M(b)})}function rr(a,b){return Se(function(){var c=a.eg;return Mh(a.document)==="complete"?M(!0):c.g(Se(function(){return b}))})}var sr=function(a){return U(function(b){return b.getState()!=="loading"?M(b):vr(a,b,"ready").g(N(function(){return b}))})};
function ur(a,b,c,d,e){return U(function(f){var g=Oq(f);if(typeof g!=="string")return e.next("nc"),$c(new ce(9));f.rf!==void 0&&(f.rf=!0);g=vr(a,f,g,wr);var h={version:f.If(),tb:f.tb};g=g.g(N(function(l){return xr.apply(null,[a,b,f,h].concat(z(l)))}));var k=d.g(gf(function(){e.next("mt")}),U(function(){return $c(new ce(10))}));g=Ud(g,k);return O([g,c]).g(N(function(l){l=w(l).next().value;return Object.assign({},l,{timestamp:a.j.now()})}))})}
function wr(a,b){return(b===null||typeof b==="number")&&(a===null||!!a&&typeof a.height==="number"&&typeof a.width==="number"&&typeof a.x==="number"&&typeof a.y==="number")}
function xr(a,b,c,d,e,f){e=e?{left:e.x,top:e.y,width:e.width,height:e.height}:Rh;c=c.Rb("getMaxSize");var g=c!=null&&typeof c.width==="number"&&typeof c.height==="number"?c:{width:0,height:0};c={left:0,top:0,width:-1,height:-1};if(g){var h=Number(String(g.width));g=Number(String(g.height));c=isNaN(h)||isNaN(g)?c:{left:0,top:0,width:h,height:g}}a={value:{ha:e,X:c,da:"mraid",ba:b,na:{x:0,y:0}},timestamp:a.j.now()};return Object.assign({},a,d,{vj:f})}
function vr(a,b,c,d){d=d===void 0?function(){return!0}:d;return(new K(function(e){var f=a.F.Vb(745,function(){e.next(B.apply(0,arguments))});b.addEventListener(c,f);return function(){b.removeEventListener(c,f)}})).g(Q(function(e){return d.apply(null,z(e))}))};var yr=function(a,b){this.context=a;this.qb=b};q(yr,hr);yr.prototype.Za=function(a,b){var c=new bd(1),d=new bd(1),e=kr(pr(this.context,this.qb,c,d,b.Sa),up(this.context,b.Sa));return{gb:fr(a.Qa.g(e),b.kb,1),Ya:{we:c.g(W(this.context.h,1)),xe:d.g(W(this.context.h,1))}}};function zr(a){return["backgrounded","notFound","hidden","noOutputDevice"].includes(a)};function Ar(a,b){var c=c===void 0?null:c;var d=new L,e=void 0,f=a.Bf,g=d.g(N(function(){return e?Object.assign({},e,{timestamp:a.j.now()}):null}),Q(function(k){return k!==null}),N(function(k){return k}));b=O([Pd(f,g),b]);var h=c;return b.g(Q(function(k){k=w(k).next().value;h===null&&(h=k.value.Qg);return k.value.Qg===h}),gf(function(k){return void(e=w(k).next().value)}),N(function(k){var l=w(k);k=l.next().value;l=l.next().value;try{var m=k.value.data,t=k.timestamp,r=m.viewport,u,x,v=Object.assign({},
r,{width:(u=r==null?void 0:r.width)!=null?u:0,height:(x=r==null?void 0:r.height)!=null?x:0,x:0,y:0,Vj:r?r.width*r.height:0}),y=Br(v),A=m.adView,P=A.measuringElement&&A.containerGeometry?Br(A.containerGeometry):Br(A.geometry),S=Br(A.geometry),la=A.reasons.some(zr),G=la?Rh:Br(A.onScreenGeometry),C;l&&(C=A.percentageInView/100);l&&la&&(C=0);return{timestamp:t,value:{da:"omid",ha:P,X:y,ba:d,S:"omid",J:S,na:{x:P.left,y:P.top},V:G,ad:C}}}catch(cc){Ei(cc,Ni());var ba,Ec;m=(Ec=(ba=cc)==null?void 0:ba.message)!=
null?Ec:"An unknown error occurred";ba="Error while processing geometryChange event: "+JSON.stringify(k.value)+"; "+m;throw Error(ba);}}),bf(1),jd())}function Br(a){var b,c,d,e;return{left:Math.floor((b=a==null?void 0:a.x)!=null?b:0),top:Math.floor((c=a==null?void 0:a.y)!=null?c:0),width:Math.floor((d=a==null?void 0:a.width)!=null?d:0),height:Math.floor((e=a==null?void 0:a.height)!=null?e:0)}};function Cr(a,b,c,d){c=c===void 0?Qd:c;var e=a.h;if(b===null)return $c(new ce(20));if(!b.validate())return $c(new ce(21));var f;d=Dr(e,b,d).g(N(function(g){var h=g.value;g=g.timestamp;var k=b.j,l=a.j;if(k.timeline!==g.timeline)throw new he;g=new qe(g.value-k.now().value+l.now().value,l.timeline);return f={value:h,timestamp:g}}));return Pd(d,c.g(N(function(){return f}))).g(Q(function(g){return g!==void 0}),N(function(g){return g}),W(a.h,1))}
function Dr(a,b,c){return Ar(b,c).g(W(a,1),N(function(d){return{timestamp:d.timestamp,value:{na:{x:d.value.J.left,y:d.value.J.top},ha:d.value.V,X:d.value.X,da:d.value.S,ba:d.value.ba}}}))};var Er=function(a,b,c){this.ka=a;this.N=b;this.qb=c};q(Er,hr);Er.prototype.Za=function(a,b){var c=b.Sa;b=Cr(this.N,this.ka,this.qb,b.dg);c=kr(b,up(this.N,c));return{gb:a.Qa.g(c),Ya:{}}};var Fr=function(a,b,c){this.ka=a;this.N=b;this.oh=c};q(Fr,hr);Fr.prototype.Za=function(a,b){var c=Cr(this.N,this.ka,void 0,b.dg);b=Cp(this.N,b.Cg,this.oh);c=kr(c,b);return{gb:a.Qa.g(c),Ya:{}}};function Gr(a){if(a.prerendering)return 3;var b;return(b={visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||""])!=null?b:0}function Hr(a){var b;a.visibilityState?b="visibilitychange":a.mozVisibilityState?b="mozvisibilitychange":a.webkitVisibilityState&&(b="webkitvisibilitychange");return b};function Ir(a){return a.document.fi.g(N(function(b){return b==="visible"}),R(),W(a.h,1))};var Jr;Jr=["2025032601"].slice(-1)[0].substring(0,8);function Kr(a,b,c){var d;return b.g(R(),U(function(e){return c.g(N(function(){if(!d){d=!0;try{e.next()}finally{d=!1}}return!0}))}),T(!1),W(a.h,1))};function Lr(a){return J(Hp(N(function(b){return Zp(b,a)})),$o(),N(function(b){return Math.round(b)}))};function Mr(a,b,c,d,e){var f=yq;if(f.length>1)for(var g=0;g<f.length-1;g++)if(f[g]<f[g+1])throw Error();g=e.g(T(void 0),U(function(){return c.g(np())}),R(),W(a,1));e=e.g(T(void 0),U(function(){return c.g(Wq())}),R(),W(a,1));return{xd:d.g(T(void 0),U(function(){return b.g(N(function(h){return{timestamp:h.timestamp,value:!0}}),$o())}),R(),W(a,1)),yd:d.g(T(void 0),U(function(){return b.g(N(function(h){return{timestamp:h.timestamp,value:h.value===0}}),$o())}),R(),W(a,1)),vc:d.g(T(void 0),U(function(){return b.g(mp(Xq,
f))}),R(Nb),W(a,1)),Ic:d.g(T(void 0),U(function(){return b.g(mp(Lr,f),N(function(h){return h.map(function(k,l){return l>0?k-h[l-1]:k})}))}),R(Nb),W(a,1)),uc:e,bb:g.g(R(Np),W(a,1))}};function Nr(a,b,c){var d=c.g(N(function(e){return{value:e,timestamp:a.j.now()}}),R(Np));return b instanceof K?b.g(R(),U(function(e){return e?(new X({value:!1,timestamp:a.j.now()})).R(a.h):d})):b.value===!1?d:new X(!1)}function Or(a,b,c,d,e,f,g){var h=rq;b=b instanceof K?b.g(T(!1),R()):b;var k=!(Rg()||Sg());c=Nr(a,c,d);a=g.Qa.g(Xo(a.h));return Object.assign({},h,{cd:c,Eg:e,ef:k,bg:b,gd:a,Bg:f})};function Pr(a){a=a.global;if(typeof a.__google_lidar_==="undefined")return a.__google_lidar_=1,!1;a.__google_lidar_=Number(a.__google_lidar_)+1;var b=a.__google_lidar_adblocks_count_;if(typeof b==="number"&&b>0&&(a=a.__google_lidar_radf_,typeof a==="function"))try{a()}catch(c){}return!0}
function Qr(a){var b=a.global;b.osdlfm=function(){return b.__google_lidar_radf_};if(b.__google_lidar_radf_!==void 0)return Cc;b.__google_lidar_adblocks_count_=1;var c=new L;b.__google_lidar_radf_=function(){return void c.next(a)};return c.g(qg(a.F,743))};var Rr=function(a){var b=this;this.se=!1;this.Ae=[];this.ze=[];a(function(c){b.se=!0;b.ti=c;b.evaluate()},function(c){b.li=c;b.evaluate()})},Sr=function(a){return new Rr(function(b,c){var d=[],e=0;a.forEach(function(f,g){f.then(function(h){d[g]=h;++e===a.length&&b(d)}).catch(function(h){c(h)})})})};
Rr.prototype.evaluate=function(){var a=this.ti,b=this.li;if(b!==void 0||this.se)this.se&&this.Ae.forEach(function(c){return void c(a)}),b!==void 0&&this.ze.forEach(function(c){return void c(b)}),this.Ae=[],this.ze=[]};Rr.prototype.then=function(a){this.Ae.push(a);this.evaluate();return this};Rr.prototype.catch=function(a){this.ze.push(a);this.evaluate();return this};var Tr=function(a){this.children=a;this.pe=!1;this.Qd=[]};Tr.prototype.complete=function(){var a=this;this.pe=!0;this.Qd.forEach(function(b){return void b(a)});this.Qd.splice(0)};Tr.prototype.onComplete=function(a){this.pe?a(this):this.Qd.push(a)};Tr.prototype.ab=function(a){var b=this.children.map(function(c){return c.ab(a)});return b.find(function(c){return c!==2})===void 0?2:this.completed?0:b.some(function(c){return c===1})?1:0};
ea.Object.defineProperties(Tr.prototype,{completed:{configurable:!0,enumerable:!0,get:function(){return this.pe}}});var Ur=function(){var a=B.apply(0,arguments);Tr.call(this,a);var b=this;this.events=a;var c=this.events.length;this.events.forEach(function(d){d.onComplete(function(){--c===0&&b.complete()})})};q(Ur,Tr);Ur.prototype.clone=function(){return new (Function.prototype.bind.apply(Ur,[null].concat(z(this.events.map(function(a){return a.clone()})))))};
Ur.prototype.Me=function(a,b){var c=this;if(!this.completed){var d=this.events.find(function(e){return e.ab(a)===1});d!==void 0&&d.Me(a,function(){c.completed||b()})}};var Vr=function(a,b){Tr.call(this,[]);this.Yd=a;this.hd=Symbol(b);this.gi=a};q(Vr,Tr);Vr.prototype.clone=function(){var a=new Vr(this.gi,this.hd.description);a.hd=this.hd;return a};Vr.prototype.ab=function(a){return a!==this.event?2:this.completed||this.Yd===0?0:1};Vr.prototype.Me=function(a,b){this.ab(a)===1&&(this.Yd--,b(),this.Yd===0&&this.complete())};ea.Object.defineProperties(Vr.prototype,{event:{configurable:!0,enumerable:!0,get:function(){return this.hd}}});
var Wr=function(a){Vr.call(this,1,a)};q(Wr,Vr);var Xr=function(a,b,c){var d=B.apply(3,arguments);this.Na=a;this.lh=b;this.Vd=c;this.tc=new Set;this.Jb=d;if(this.Na.N)this.context=this.Na.N;else if(this.Na.ka)this.context=this.Na.ka;else throw Error("Ma");var e=d.reduce(function(h,k){k.subscribedEvents.forEach(function(l){return void h.add(l)});return h},new Set);e=w(e.values());for(var f=e.next(),g={};!f.done;g={vf:void 0},f=e.next()){g.vf=f.value;f=d.filter(function(h){return function(k){return k.controlledEvents.indexOf(h.vf)>=0}}(g));if(f.length===
0)throw Error("Na");if(f.length>1)throw Error("Oa");}};Xr.prototype.start=function(){var a=this;this.Jb.forEach(function(b){return void b.start(a.Na)});this.Vd.start(this.Na,this.sh.bind(this),this.Cb.bind(this),function(){})};Xr.prototype.dispose=function(){var a=this;this.Vd.dispose();this.tc.forEach(function(b){return void a.Cb(b)});this.Jb.forEach(function(b){return void b.dispose()})};
var Yr=function(a,b){b={measuringCreativeIds:[].concat(z(a.tc.values())).map(function(c){return a.context.hc.Fa(c)}),hasCreativeSourceCompleted:!!a.Vd.td,colleagues:a.Jb.map(function(c){return{name:c.name,controlledEvents:c.controlledEvents.map(function(d){var e;return(e=d.description)!=null?e:"n/a"}),subscribedEvents:c.subscribedEvents.map(function(d){var e;return(e=d.description)!=null?e:"n/a"})}}),ephemeralCreativeStateChanges:b};b={specMajor:2,specMinor:0,specPatch:0,instanceId:a.context.hc.Fa(a.context.ub),
timestamp:se(a.context.j.now(),new qe(0,a.context.j.timeline)),mediatorState:b};$d(a.context,b)},Zr=function(a,b,c,d,e){var f={};Yr(a,(f[b]={events:[{timestamp:c,description:d,status:e}]},f))};
Xr.prototype.sh=function(a,b,c){var d=this;if(!this.tc.has(a)){var e=this.lh.clone();this.tc.add(a);Yr(this,{});var f=!1,g=[];this.Jb.forEach(function(h){var k=function(l,m,t){var r=d.context.hc.Fa(a),u=se(d.context.j.now(),new qe(0,d.context.j.timeline)),x,v=(x=l.description)!=null?x:"n/a";if(h.controlledEvents.indexOf(l)<0||e.ab(l)!==1)return t(!1),Zr(d,r,u,v,1),new Rr(function(A){return void A()});var y=new Rr(function(A){e.Me(l,function(){d.Jb.filter(function(P){return P.subscribedEvents.indexOf(l)>=
0}).forEach(function(P){return void P.handleEvent(a,l,m)});A()})});return new Rr(function(A){y.then(function(){t(!0);Zr(d,r,u,v,2);A()})})};h.ge(a,b,c,function(l,m,t){return f?k(l,m,t):new Rr(function(r){g.push(function(){k(l,m,t).then(function(){r()})})})},function(l){try{d.context.G.H("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=colleague-executed&name="+l,{ga:"GET"}).sendNow()}catch(m){}})});f=!0;g.forEach(function(h){return void h()})}};
Xr.prototype.Cb=function(a){this.tc.delete(a);this.Jb.forEach(function(b){b.Cb(a)});Yr(this,{})};var $r=function(a,b){this.key=a;this.defaultValue=b===void 0?!1:b;this.valueType="boolean"},as=function(a){this.key=a;this.defaultValue=0;this.valueType="number"};var bs={considerOmidZOrderOcclusions:[new $r("100006"),!1],extraPings:[new as("45362137"),0],extrapolators:[new $r("45377435"),!1],rxlidarStatefulBeacons:[new $r("45372163"),!1],shouldIgnoreAdChoicesIcon:[new $r("45382077"),!1],dedicatedViewableAttributionPing:[new as("45389692"),0],useReachIntegrationPolyfill:[new $r("45407239"),!1],useReachIntegrationSharedStorage:[new $r("45407240",!0),!0],sendBrowserIdInsteadOfVPID:[new $r("45407241"),!1],waitForImpressionColleague:[new $r("45430682"),!1],fetchLaterBeacons:[new $r("45618478"),
!1],rxInNonrx:[new $r("45642405"),!1],addQueryIdToErrorPing:[new $r("45653435"),!1],shouldSendExplicitDisplayMeasurablePing:[new $r("45658589"),!1],reachUseCreateWorklet:[new $r("45661569"),!1]};
function cs(a){return Object.entries(bs).reduce(function(b,c){var d=w(c);c=d.next().value;var e=w(d.next().value);d=e.next().value;e=e.next().value;var f;if(a==null)var g=void 0;else a:{var h=a.yf[d.key];if(d.valueType==="proto"){try{var k=JSON.parse(h);if(Array.isArray(k)){g=k;break a}}catch(l){}g=d.defaultValue}else g=typeof h===typeof d.defaultValue?h:d.defaultValue}b[c]=(f=g)!=null?f:e;return b},{})};var Gl=function(a){yn.call(this,a)};q(Gl,yn);Gl.prototype.de=function(){return Ml(this,4,!0)};Gl.prototype.dd=function(){return Nl(this,6)};Gl.U="ads.branding.measurement.client.serving.integrations.active_view.ActiveViewMetadata";var ds=[0,mn,-2,ln,-1,hn];Gl.prototype.M=An(ds);var es=function(a){yn.call(this,a)};q(es,yn);es.prototype.getType=function(){var a=a===void 0?0:a;var b=Fk(rl(this,6));return b!=null?b:a};var fs=Bn(es);es.U="ads.geo.GeoTargetMessage";var gs=function(a){yn.call(this,a)};q(gs,yn);n=gs.prototype;n.Df=function(){return Ol(this,2)};n.Ef=function(){return Ql(this,2)};n.de=function(){return Ml(this,3,!0)};n.Ff=function(){return Il(this,es,4)};n.pg=function(a){return Ll(this,es,4,a)};n.Hf=function(){return yl(this,7,Fk,void 0===ok?2:4)};n.cf=function(a){return Vl(this,7,a)};n.qg=function(a){return Rl(this,9,a)};n.dd=function(){return Nl(this,10)};gs.U="ads.branding.measurement.client.serving.integrations.reach.ReachMetadata";var hs=[0,pn,-4,rn,ln,hn,dn,pn,dn,pn,hn,pn,-1,[0,hn,-3],qn,gn,pn,fn,-1,hn,-1,fn,dn,[0,fn,hn,-1,rn,dn,fn],cn,pn];es.prototype.M=An(hs);var is=[0,mn,-1,ln,hs,jn,-1,sn,hn,ln,hn];gs.prototype.M=An(is);var js=function(a){yn.call(this,a)};q(js,yn);js.prototype.Df=function(){return Ol(this,1)};js.prototype.Ef=function(){return Ql(this,1)};js.prototype.dd=function(){return Nl(this,2)};js.U="ads.branding.measurement.client.serving.integrations.shared.SharedMetadata";var ks=[0,mn,hn];js.prototype.M=An(ks);var ls=function(a){yn.call(this,a)};q(ls,yn);var ms=function(a){return Il(a,gs,1)};ls.U="ads.branding.measurement.client.serving.IntegratorMetadata";var ns=[0,is,ds,ks];ls.prototype.M=An(ns);var os=zn(ls,ns);var ps=function(){this.yf={}};var qs=function(){this.qf=!1;this.mf=new Map};
qs.prototype.start=function(a,b,c,d){var e=this;if(this.td===void 0&&a.N){var f=a.N;this.lf=d;c=!this.qf&&Pr(f);d=this.qf?Cc:Qr(f);d=po(f,d);this.td=(c?Cc:d.g(N(function(g){var h=h===void 0?Symbol():h;return Object.freeze({ub:h,element:(new X(g)).R(f.h)})}),ho())).subscribe(function(g){var h=g.ub;e.mf.set(h,g);g.element.g(Re(1)).subscribe(function(k){var l=eo(k,"data-google-av-flags"),m=new ps;if(l!==null)try{var t=JSON.parse(l)[0];l="";for(var r=0;r<t.length;r++)l+=String.fromCharCode(t.charCodeAt(r)^
"\u0003\u0007\u0003\u0007\b\u0004\u0004\u0006\u0005\u0003".charCodeAt(r%10));m.yf=JSON.parse(l)}catch(x){}m=cs(m);k=eo(k,"data-google-av-ufs-integrator-metadata");a:{if(k!==null)try{var u=os(k);break a}catch(x){}u=new ls}b(h,u,m)})});c&&this.dispose();a.ka&&If(a.ka)}};qs.prototype.dispose=function(){var a,b;(a=this.td)==null||(b=a.unsubscribe)==null||b.call(a);this.td=void 0;var c;(c=this.lf)==null||c.call(this);this.lf=void 0};var rs=function(a){yn.call(this,a)};q(rs,yn);var ss=function(a,b){return Tl(a,1,b)};rs.U="contentads.bow.rendering.client.TurtleDoveReportingData";rs.prototype.M=An([0,mn,hn,mn,-5,rn,mn,-4]);function ts(){var a=Hg();return a?Kb("AmazonWebAppPlatform;Android TV;Apple TV;AppleTV;BRAVIA;BeyondTV;Freebox;GoogleTV;HbbTV;LongTV;MiBOX;MiTV;NetCast.TV;Netcast;Opera TV;PANASONIC;POV_TV;SMART-TV;SMART_TV;SWTV;Smart TV;SmartTV;TV Store;UnionTV;WebOS".split(";"),function(b){return Za(a,b)})||Za(a,"OMI/")&&!Za(a,"XiaoMi/")?!0:Za(a,"Presto")&&Za(a,"Linux")&&!Za(a,"X11")&&!Za(a,"Android")&&!Za(a,"Mobi"):!1};var rq=Object.freeze({xg:1E3,Zd:.5,ue:.3}),yq=Object.freeze([1,.75,rq.Zd,rq.ue,0]),us=function(a,b,c,d,e,f,g){this.ji=a;this.Ib=b;this.Eb=c;this.ac=d;this.Pc=e;this.Qc=f;this.Ld=g;this.name="rxlidar";this.Sh=new bd;this.controlledEvents=[];this.subscribedEvents=[];this.Wd=new bd;this.Ga=new bd;this.controlledEvents.push(this.ac,this.Pc,this.Qc);this.subscribedEvents.push(this.Eb)};n=us.prototype;
n.start=function(a){if(this.fe===void 0&&a.N){var b;if((b=this.Ib)!=null)var c=b;else{b=a.N;var d=(c=a.ka)!=null?c:null;c={kh:.01,Vh:mf(b.j,36E5),qb:b.j.Ha(100).g(W(b.h,1)),ka:d}}this.Ib=c;a=a.N;this.fe=vs(a,this.Wd.g(W(a.h,1)),this.Ib.kh,this.Ib.Vh,this.Ib.qb,this.Ib.ka,this.Ga.g(T(!1),W(a.h,1)),this.ac,this.Pc,this.Qc,this.Ld).subscribe(this.Sh)}};n.dispose=function(){this.Wd.complete();this.Ga.complete();var a;(a=this.fe)==null||a.unsubscribe();this.fe=void 0};
n.ge=function(a,b,c,d,e){if(Fl(b,Gl,2)===void 0||Il(b,Gl,2).de()){this.Wd.next(Object.assign({},this.ji.mf.get(a),{metadata:b,experimentState:c,ck:a,Oa:d}));var f,g;e((g=(f=Il(b,Gl,2))==null?void 0:f.dd())!=null?g:-1)}};n.Cb=function(){};n.handleEvent=function(a,b){b===this.Eb&&(this.Ga.next(!0),this.Ga.complete())};
function vs(a,b,c,d,e,f,g,h,k,l,m){var t=Ir(a).g(N(function(u){return!u})),r=new ir(a,[new or(a,yq),new nr(a,e),new Fr(f,a,yq),new Er(f,a,e),new yr(a,e)]);return so(a,b,function(u,x){var v=Vq(u,x.element),y=v.Kd,A=v.bc,P=v.Td,S=v.Qe,la=v.eh,G=v.kb,C=v.Kh,ba=v.le,Ec=v.qe,cc=v.Sa,qd=v.ma,Ja=v.Pi,ig=v.mb;v=v.te;var jg,ib=(jg=Ql(Hl(x.metadata),3))!=null?jg:"";jg=ss(new rs,atob(ib)).Ta();ib=(new X(x.experimentState)).R(u.h);var Lm=new X(new of(u,new Dg(u))),Mm=ib.g(N(function(D){return D.fetchLaterBeacons}),
T(!1),R(),W(u.h,1)),gt=Mm.g(N(function(D){return D&&(new zg(u)).K({sf:!0})}),gf(function(D){D&&Lm.value.H("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=fetch&later&start&control&fle=1&sfl=1").sendNow()})),ye=ib.g(N(function(D){return D.shouldIgnoreAdChoicesIcon})),Ka=G.g(Ne(C),N(function(D){var jb=w(D);D=jb.next().value;jb=jb.next().value;(D=D||jb)||((D=Za(Hg(),"CrKey")&&!(Za(Hg(),"CrKey")&&Za(Hg(),"SmartSpeaker"))||Za(Hg(),"PlayStation")||Za(Hg(),"Roku")||ts()||Za(Hg(),"Xbox"))||
(D=Hg(),D=Za(D,"AppleTV")||Za(D,"Apple TV")||Za(D,"CFNetwork")||Za(D,"tvOS")),D||(D=Hg(),D=Za(D,"sdk_google_atv_x86")||Za(D,"Android TV")));return D}));C=new dr(u,x,la,G,cc,ye);ye=ib.g(N(function(D){return D.considerOmidZOrderOcclusions}));var Gc,yb=(Gc=Pl(Hl(x.metadata)))!=null?Gc:!1;Gc=r.Za(C,{kb:G,Cg:yb,Sa:cc,dg:ye});var Ya=Gc.gb,ze=Gc.Ya;Gc=ze.we;ye=ze.xe;ze=ze.Je;yb=(new X(yb)).R(u.h);var fc=Or(u,Ec,Ka,t,Ja,yb,C);Ja=Eq(u.h,u.j,Ya,fc);Ka=Mr(u.h,Ja.wa.Le,Ja.wa.visible,Ja.He,Ja.Gc);yb=Tq(u.h,u.j,
Ja.Gc,Ja.wa.ba,Ja.wa.visible);Ya=hq(u.h,u.j,Ya,fc);fc=xq(u.j,u.h,Ya.wa.Le,Ya.wa.visible,Ya.He,Ya.Gc);var vi={Pe:qq(u.h,u.j,Ya.Gc,fc.uc)},wi=ib.g(N(function(D){return D.extrapolators}),T(!1));Ya=Wo(u.h,wi,Object.assign({},Ya.wa,fc,vi),Object.assign({},Ja.wa,{Pe:Yp(u,yb),vc:Yp(u,Ka.vc),Ic:Yp(u,Ka.Ic),uc:Yp(u,Ka.uc),bb:Ka.bb.g(N(function(D){return new Xp(u.j,D)})),xd:Yp(u,Ka.xd),yd:Yp(u,Ka.yd)}));Ka=Gp(u,d.g(Qe("t")));yb=(f!==null&&f.validate()?f.yi:Qd).g(W(u.h,1),Qe("u"));Ka=Ud(Ka,yb);yb=Kr(u,Ya.ba,
Ka.g(Q(function(D){return D!==null})));fc=ws(u,C,y);vi=xs(u,Ka,x.element);wi=fc.Rg.g(T({x:0,y:0}));var kt=ib.g(N(function(D){return D.rxlidarStatefulBeacons}),T(!1),R(),gf(function(D){eh=D}),W(u.h,1)),Nm=ba.g(N(function(D){return D===40||D===41||D===42})),lt=ib.g(N(function(D){return D.waitForImpressionColleague}),T(!1),R(),W(u.h,1)),mt=b.g(N(function(D){var jb;return D.experimentState.addQueryIdToErrorPing?(jb=Il(D.metadata,js,3))==null?void 0:jb.Ef():void 0}));return Object.assign({},{G:new X(u.G),
Gb:new X("lidar2"),Gi:new X("lidartos"),Vg:new X(Jr),Ld:new X(m),Rd:new X(u.validate()?null:new de),ah:new X(Nh(u.document)),W:new X(Do),tf:Ka,wg:Ka,Xj:yb,fd:g,Oi:lt,Oa:new X(x.Oa),ac:new X(h),Pc:new X(k),Qc:new X(l),dh:new X(u.lc?1:void 0),fh:new X(u.Wg?1:void 0),kb:G,mb:ig,Ne:new X(jg),Qb:ig.g(Q(function(D){return D}),N(function(){return u.Qb.bind(u)})),we:Gc.g(W(u.h,1)),xe:ye.g(W(u.h,1)),ph:ib.g(N(function(D){return D.extraPings})),Rf:kt,yh:Mm,ug:gt,te:v,Jh:Nm,zh:ib.g(N(function(D){return D.dedicatedViewableAttributionPing})),
qh:Lm,vg:new X(eh&&(new dh(u)).K({ga:"GET"})),Bi:new X(Number(x.experimentState.useReachIntegrationSharedStorage)<<0+Number(x.experimentState.useReachIntegrationPolyfill)<<1+Number(x.experimentState.sendBrowserIdInsteadOfVPID)<<2),bh:x.element.g(N(function(D){return D!==null})),jb:qd,Hi:qd,Td:P.g(T([])),Qe:S.g(T([])),wh:P.g(N(function(D){return D.length>0?!0:null}),T(null),R()),bc:A.g(T([]),W(u.h,1)),Bj:ib,shouldSendExplicitDisplayMeasurablePing:ib.g(N(function(D){return D.shouldSendExplicitDisplayMeasurablePing})),
Kd:y,dc:C.dc,le:ba.g(T(0),W(u.h,1)),Th:la,Sa:cc.g(T(0),W(u.h,1)),wb:Nm.g(N(function(D){return D?Uo:Co})),Wb:new X(Vo),qe:Ec,Tf:C.rc.g(Xo(u.h)),Re:C.Re},Ya,{Xc:O([Ya.Xc,wi]).g(N(function(D){var jb=w(D);D=jb.next().value;jb=jb.next().value;return Uh(D,jb)}),R(Sh))},fc,{Jc:rh(u),xh:vi,Je:ze,Bd:Ja.wa.Bd,Xg:new X(new Oo),escapedQueryId:mt})},Bo(a,"https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=error&bin="+m+"&v="+Jr,c))}
function ws(a,b,c){var d=d===void 0?Ma:d;var e,f;d=((e=d.performance)==null?void 0:(f=e.timing)==null?void 0:f.navigationStart)||0;return Object.assign({},{Pg:new X(d),Og:kp(a,b)},jp(a,b,c))}function xs(a,b,c){return b.g(Q(function(d){return d!==null}),U(function(){return c}),N(function(d){var e=jo(a);return e.length>0&&e.indexOf(d)>=0}),N(function(d){return!d}))};var ys=function(a){var b=b===void 0?[]:b;var c=c===void 0?[a]:c;this.Eb=a;this.subscribedEvents=b;this.controlledEvents=c;this.name="impression";this.ie=new Map};n=ys.prototype;n.start=function(a){this.Na=a};n.dispose=function(){this.ie.clear()};n.ge=function(a,b,c,d){if(b=this.Na)c=new zs(b,c,this.Eb,d),this.ie.set(a,c)};n.Cb=function(a){this.ie.delete(a)};n.handleEvent=function(){};
var zs=function(a,b,c,d){var e=this;this.context=a;this.Eb=c;this.Ag=function(){};this.Qf=[];this.Of="&avradf=1";this.Pf=Sr([]);this.Ga=new bd;c=a.ka;var f=c!==null&&(c==null?void 0:c.validate()),g,h=(g=a.N)==null?void 0:g.h;this.Ga.g(T(!b.waitForImpressionColleague),W(h,1));this.Di=f?c==null?void 0:c.Nf.g(Re(1),Qe(!0),T(!1)):(new X(!0)).R(h);this.Ag=function(k,l){e.Ga.next(!0);e.Ga.complete();O([e.Ga,e.Di]).subscribe(function(m){var t=w(m);m=t.next().value;t=t.next().value;if(!t)return Qd;m&&t&&
d(e.Eb,k,l);return!0})};this.nc(a.N)};zs.prototype.nc=function(a){var b=this;this.yc=a.global.document;this.Qf.push(As(this));var c={};this.Pf=Sr(this.Qf);this.Pf.then(function(){b.Of="&vis="+Gr(b.yc)+"&uach=0&ms=0";c.paramString=b.Of;c.view_type="DELAYED_IMPRESSION";b.Ag(c,function(){})})};var As=function(a){return new Rr(function(b){var c=Hr(a.yc);if(c)if(Gr(a.yc)===3){var d=function(){var e=a.yc;e.removeEventListener&&e.removeEventListener(c,d,!1);b(!0)};Eg(a.yc,c,d)}else b(!0)})};function Bs(a){var b=sh(a);return b?b.g(N(function(c){var d;c=(d=Kl(c).find(function(f){return Ql(f,1)==="Google Chrome"}))==null?void 0:Ql(d,2);if(!c)return!1;var e;return((e=w(c.split(".").map(function(f){return Number(f)})).next().value)!=null?e:0)>=121})):jh.R(a.h)};function Cs(a,b){b="https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=reach&proto="+encodeURIComponent(oh(b.M()));a.G.H(b,{ga:"GET"}).sendNow()};function Ds(a){return[{Ab:2,Ec:!1,fc:!0,filterIds:Es(a==null?void 0:a.productionFilterIds)},{Ab:2,Ec:!0,fc:!0,filterIds:Es(a==null?void 0:a.testFilterIds)},{Ab:2,Ec:!1,fc:!1,filterIds:Es(a==null?void 0:a.testFilterIds)}]}function Es(a){if(a!==void 0&&typeof BigInt==="function")return a.map(function(b){return BigInt(b)})};var Fs=function(a){yn.call(this,a)};q(Fs,yn);var Gs=function(a,b){return Ul(a,1,b)},Hs=function(a,b){return Tl(a,2,b)},Is=function(a,b){return Tl(a,3,b)};Fs.prototype.Fc=function(a){return Tl(this,10,a)};Fs.prototype.Ff=function(){return Il(this,es,11)};Fs.prototype.pg=function(a){return Ll(this,es,11,a)};Fs.U="ads.branding.measurement.client.frontend.integrations.reach.ReachStatusMessage";Fs.prototype.M=An([0,rn,mn,-1,rn,-2,mn,-1,hn,mn,hs,sn,hn]);var Js=function(a){this.context=a;this.points=[]},Ks=function(a,b){Fa(function(c){if(c.o==1)return c.ua=0,c.Ea=2,ta(c,b(),4);if(c.o!=2)return c.return(c.aa);wa(c);a.flush();return xa(c,0)})};Js.prototype.flush=function(){if(!(this.points.length<=0)){var a=new Fs;Gs(a,9);var b=Ds().length;tl(a,13,b==null?b:Hk(b));Vl(a,12,this.points);this.points.splice(0);Cs(this.context,a)}};function Ls(){this.blockSize=-1};function Ms(a,b){this.blockSize=-1;this.blockSize=64;this.Uc=Ma.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.zd=this.Fb=0;this.A=[];this.ai=a;this.Sf=b;this.Ni=Ma.Int32Array?new Int32Array(64):Array(64);Ns===void 0&&(Ns=Ma.Int32Array?new Int32Array(Os):Os);this.reset()}Sa(Ms,Ls);for(var Ps=[],Qs=0;Qs<63;Qs++)Ps[Qs]=0;var Rs=[].concat(128,Ps);Ms.prototype.reset=function(){this.zd=this.Fb=0;this.A=Ma.Int32Array?new Int32Array(this.Sf):Mb(this.Sf)};
var Ss=function(a){var b=a.Uc;E(b.length==a.blockSize);for(var c=a.Ni,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(b=16;b<64;b++)d=c[b-15]|0,e=c[b-2]|0,c[b]=((c[b-16]|0)+((d>>>7|d<<25)^(d>>>18|d<<14)^d>>>3)|0)+((c[b-7]|0)+((e>>>17|e<<15)^(e>>>19|e<<13)^e>>>10)|0)|0;b=a.A[0]|0;d=a.A[1]|0;e=a.A[2]|0;for(var f=a.A[3]|0,g=a.A[4]|0,h=a.A[5]|0,k=a.A[6]|0,l=a.A[7]|0,m=0;m<64;m++){var t=((b>>>2|b<<30)^(b>>>13|b<<19)^(b>>>22|b<<10))+(b&d^b&e^d&e)|0,r=(l+((g>>>6|g<<26)^(g>>>11|
g<<21)^(g>>>25|g<<7))|0)+(((g&h^~g&k)+(Ns[m]|0)|0)+(c[m]|0)|0)|0;l=k;k=h;h=g;g=f+r|0;f=e;e=d;d=b;b=r+t|0}a.A[0]=a.A[0]+b|0;a.A[1]=a.A[1]+d|0;a.A[2]=a.A[2]+e|0;a.A[3]=a.A[3]+f|0;a.A[4]=a.A[4]+g|0;a.A[5]=a.A[5]+h|0;a.A[6]=a.A[6]+k|0;a.A[7]=a.A[7]+l|0};
Ms.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.Fb;if(typeof a==="string")for(;c<b;)this.Uc[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ss(this),d=0);else if(Pa(a))for(;c<b;){var e=a[c++];if(!("number"==typeof e&&0<=e&&255>=e&&e==(e|0)))throw Error("Pa");this.Uc[d++]=e;d==this.blockSize&&(Ss(this),d=0)}else throw Error("Qa");this.Fb=d;this.zd+=b};
Ms.prototype.digest=function(){var a=[],b=this.zd*8;this.Fb<56?this.update(Rs,56-this.Fb):this.update(Rs,this.blockSize-(this.Fb-56));for(var c=63;c>=56;c--)this.Uc[c]=b&255,b/=256;Ss(this);for(c=b=0;c<this.ai;c++)for(var d=24;d>=0;d-=8)a[b++]=this.A[c]>>d&255;return a};
var Os=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Ns;function Ts(){Ms.call(this,8,Us)}Sa(Ts,Ms);var Us=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var vn=function(a){yn.call(this,a)};q(vn,yn);vn.U="EventIdMessage";var Vs=function(a){yn.call(this,a)};q(Vs,yn);Vs.prototype.Tb=function(a){return Sl(this,4,a)};Vs.prototype.Hf=function(){return yl(this,8,Fk,void 0===ok?2:4)};Vs.prototype.cf=function(a){return Vl(this,8,a)};Vs.prototype.qg=function(a){return Rl(this,9,a)};Vs.U="ads.branding.measurement.client.frontend.integrations.reach.ContextIdMessage";var Ws=[0,en,kn,-1];vn.prototype.M=An(Ws);Vs.prototype.M=An([0,Ws,ln,-1,pn,-3,tn,ln]);var un=function(a){yn.call(this,a,1)};q(un,yn);un.U="proto2.bridge.MessageSet";var Xs={};un[pk]=Xs;var Ys=zn(vn,Ws);Xs[4156379]={Ej:new wn};var Zs=function(){var a;this.message=a=a===void 0?new Vs:a};Zs.prototype.Fc=function(a){var b=this.message;a=Ys(qh(a));this.message=Ll(b,vn,1,a);return this};var $s=function(a,b){var c=Rl(a.message,2,b.Ab===2);b=Rl(c,3,!b.Ec);a.message=b;return a};Zs.prototype.Tb=function(a){this.message=this.message.Tb(Math.max(1,a));return this};
var at=function(a,b){a.message=a.message.cf(b);return a},bt=function(a){var b=Jr.match(/m\d{12}/g),c=Jr.match(/\d{8}/g);if(b&&b.length>0){b=b[0].slice(1);c=a.message;var d=Number(b.slice(0,8));c=Sl(c,5,d);d=Number(b.slice(8,10));c=Sl(c,6,d);b=Number(b.slice(10,12));b=Sl(c,7,b);a.message=b;return a}if(c&&c.length>0)return b=Sl(a.message,5,Number(c[0])),b=tl(b,6),b=tl(b,7),a.message=b,a;Jr==="unreleased"&&(b=tl(a.message,5),b=Sl(b,6,0),a.message=tl(b,7));return a};
Zs.prototype.encode=function(){var a=this.message,b=oh(a.M());b.length>64&&(a=a.Tb(1),b=oh(a.M()));b.length>64&&(a=tl(a,6),b=oh(a.M()));b.length>64&&(a=tl(a,7),b=oh(a.M()));b.length>64&&(a=tl(a,5),b=oh(a.M()));return b};function ct(a,b){if(b===void 0||b.length===0)return Cs(a,Gs(new Fs,7)),[ae(0)].filter(function(d){return d!==void 0});var c=ae(-**********);return c===void 0?[]:b.map(function(d){var e=d%c;d!==e&&Cs(a,Gs(new Fs,6));return e})};function dt(a,b){var c=c===void 0?BigInt(0):c;return{bucket:a,value:b?1:16384,filteringId:c}};function et(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}if(b.length>=24)throw Error("Ra");return[96|b.length].concat(z(b))}
function ft(a){if(a.length>=24)throw Error("Sa");return[160|a.length].concat(z(a.sort(ht).map(function(b){return[].concat(z(b[0]),z(b[1]))}).flat()))}function jt(a){if(a.length>=24)throw Error("Ta");return[128|a.length].concat(z(a.flat()))}function nt(a,b){for(var c=[];a>0;)c.push(Number(a%BigInt(255))),a/=BigInt(255);for(;c.length<b;)c.push(0);return c.reverse()}
function ht(a,b){a=a[0];b=b[0];if(a.length!==b.length)return a.length-b.length;for(var c=0;c<a.length;c++)if(a[c]!==b[c])return a[c]-b[c];return 0};function ot(a,b,c,d){var e=dt(BigInt(c),d);b={shared_info:JSON.stringify({api:"shared-storage",report_id:"PRE_WORKLET_ERROR",reporting_origin:"https://www.googleadservices.com",scheduled_report_time:String((new Date).getUTCSeconds()),version:"polyfill"}),aggregation_service_payloads:[],context_id:b,aggregation_coordinator_origin:"https://publickeyservice.msmt.gcp.privacysandboxservices.com"};d?(b.debug_key="0",b.aggregation_service_payloads.push({payload:String(c),key_id:"0",debug_cleartext_payload:pt([e])})):
b.aggregation_service_payloads.push({payload:String(c),key_id:"0"});try{var f,g;(f=a.global)==null||(g=f.fetch)==null||g.call(f,"https://www.googleadservices.com/.well-known/private-aggregation/report-shared-storage",{method:"POST",cache:"no-cache",keepalive:!0,mode:"no-cors",headers:{"content-type":"application/json"},body:JSON.stringify(b)}).catch(function(){})}catch(h){}}
function pt(a){a=ft([[et("data"),jt(a.map(function(b){return ft([[et("value"),[68].concat(z(nt(BigInt(b.value),4)))],[et("bucket"),[80].concat(z(nt(b.bucket,16)))],[et("filteringId"),[68].concat(z(nt(b.filteringId,4)))]])}))],[et("operation"),et("histogram")]]);return btoa(String.fromCharCode.apply(String,z(new Uint8Array(a))))};var qt={},rt=(qt[2]="prod",qt[1]="canary",qt);
function st(a,b,c,d){var e,f,g,h,k,l,m,t;return Fa(function(r){switch(r.o){case 1:e=Ds(c);f=function(u){e.forEach(function(x){var v,y=bt($s(at((new Zs).Fc(c.escapedQueryId),(v=c.trafficTypes)!=null?v:[0]),x)).Tb(-1).encode();ot(a,y,u,x.fc)})};g=Un(a);if(g instanceof Error)return f(-16),h=Is(Hs(Gs(new Fs,8),g.name),g.message),Cs(a,h),r.return();d.points.push(7);k=tt(a,c,e);return ta(r,c.experimentState.reachUseCreateWorklet?ut(a,b,f):vt(a,b,f),2);case 2:return l=r.aa,ta(r,k,3);case 3:return m=r.aa,
d.points.push(8),t=e.map(function(u){var x,v,y;return wt(a,l,u,m,(x=c.deviceType)!=null?x:1,c.escapedQueryId,(v=c.trafficTypes)!=null?v:[0],(y=c.isProductSplitVpidLogsExperiment)!=null?y:!1,function(A){var P,S=bt($s(at((new Zs).Fc(c.escapedQueryId),(P=c.trafficTypes)!=null?P:[0]).Tb(-1),u)).encode();ot(a,S,A,u.fc)})}),ta(r,Promise.all(t),4);case 4:d.points.push(9),r.o=0}})}
function vt(a,b,c){var d,e,f;return Fa(function(g){switch(g.o){case 1:d=a.sharedStorage;if(!d)return g.return(Promise.reject(Error("Ua")));ua(g,2);return ta(g,d.worklet.addModule(b),4);case 4:g.o=3;g.ua=0;break;case 2:e=va(g),c(-17),f=Is(Hs(Gs(new Fs,1),e.name),e.message),Cs(a,f);case 3:return g.return(d)}})}
function ut(a,b,c){var d,e,f;return Fa(function(g){if(g.o==1){d=a.sharedStorage;if(!d)return g.return(Promise.reject(Error("Ua")));ua(g,2);return ta(g,d.createWorklet(b,{dataOrigin:"script-origin"}),4)}if(g.o!=2)return g.return(g.aa);e=va(g);c(-17);f=Is(Hs(Gs(new Fs,1),e.name),e.message);Cs(a,f);return g.return(Promise.reject(e))})}
function tt(a,b,c){var d,e,f;return Fa(function(g){if(g.o==1)return d=[].concat(z(new Set(c.map(function(h){return h.Ab})))),e=d.map(function(h){return xt(a,b,h)}),ta(g,Promise.all(e),2);f=g.aa;return g.return(new Map(f.map(function(h,k){return[d[k],h]})))})}
function xt(a,b,c){var d,e,f,g,h,k,l,m,t;return Fa(function(r){switch(r.o){case 1:return e=(d=b.clientsideModelFilename)!=null?d:"model_person_country_code_XX_person_region_code_5858.json",f=void 0,g=1,h={method:"GET"},k=200,l=b.geoTargetMessage?fs(b.geoTargetMessage):void 0,m=(new Fs).Fc(b.escapedQueryId).pg(l),ua(r,2),ta(r,a.global.fetch(yt(c,e),h),4);case 4:f=r.aa;k=f.status;if(f.ok){r.Ia(5);break}return ta(r,a.global.fetch(yt(c,"model_person_country_code_XX_person_region_code_5858.json"),h),6);
case 6:f=r.aa,g=2;case 5:r.o=3;r.ua=0;break;case 2:t=va(r),k=-1,t instanceof Error&&Is(Hs(m,t.name),t.message);case 3:var u=Gs(m,2);tl(u,9,k==null?k:Hk(k));if(!f||!f.ok)return u=Ul(m,4,4),u=Tl(u,8,e),Tl(u,7,""),Cs(a,m),r.return();u=Ul(m,4,g);Tl(u,7,g===1?e:"");Cs(a,m);return ta(r,f.text(),7);case 7:return r.return(r.aa)}})}function yt(a,b){return"https://www.googletagservices.com/agrp/"+rt[a]+"/"+b}
function wt(a,b,c,d,e,f,g,h,k){var l,m,t,r,u,x,v;return Fa(function(y){switch(y.o){case 1:l=d.get(c.Ab);if(l===void 0)return y.return();var A=ae(-**********);if(A===void 0)A=-1;else{var P=Number,S=new Ts;S.update(l);var la=S.digest();S=BigInt(0);la=w(la);for(var G=la.next();!G.done;G=la.next())S=(S*BigInt(256)+BigInt(G.value))%A;A=P(S)}m=A;A=bt($s(at((new Zs).Fc(f),g),c).Tb(m));A.message=A.message.qg(h);t=A.encode();r={contextId:t,aggregationCoordinatorOrigin:"https://publickeyservice.msmt.gcp.privacysandboxservices.com",
filteringIdMaxBytes:4};u={modelJson:l,modelHash:m,deviceType:e,enableDebugMode:c.fc,reportBrowserIdInsteadOfVPID:c.Ec,filterIds:ct(a,c.filterIds)};x=b.run("google_reach",{privateAggregationConfig:r,data:u,keepAlive:!0});if(x===void 0){y.Ia(2);break}ua(y,3);return ta(y,x,5);case 5:y.o=2;y.ua=0;break;case 3:v=va(y),k(-18),A=v,la=Is(Hs(Gs(new Fs,3),(P=A==null?void 0:A.name)!=null?P:"unknown"),(S=A==null?void 0:A.message)!=null?S:""),Cs(a,la);case 2:A=Gs(new Fs,5),A=Ul(A,5,c.Ab===1?1:2),A=Ul(A,6,c.Ec?
1:2),Cs(a,A),y.o=0}})};var zt=function(a){var b=b===void 0?[]:b;var c=c===void 0?[a]:c;this.Be=a;this.subscribedEvents=b;this.controlledEvents=c;this.name="reach";this.Ac=new Map};n=zt.prototype;n.start=function(a){a.N&&(this.context=a.N)};n.dispose=function(){this.Ac.forEach(function(a){return void a.dispose()});this.Ac.clear()};
n.ge=function(a,b,c,d,e){var f=this,g=this.context;if(g){var h=new Js(g);Ks(h,function(){var k,l,m,t;return Fa(function(r){if(r.o==1){h.points.push(1);if(Fl(b,gs,1)!==void 0&&!ms(b).de())return r.return();h.points.push(2);return Un(g)?ta(r,gd(Bs(g)),2):r.return()}if(r.o!=3){k=r.aa;if(!k)return r.return();h.points.push(3);l=new At(g,b,f.Be,c,d,h);f.Ac.set(a,l);return ta(r,l.run(),3)}e((t=(m=ms(b))==null?void 0:m.dd())!=null?t:-1);r.o=0})})}};
n.Cb=function(a){var b;(b=this.Ac.get(a))==null||b.dispose();this.Ac.delete(a)};n.handleEvent=function(){};var At=function(a,b,c,d,e,f){this.context=a;this.metadata=b;this.Be=c;this.experimentState=d;this.Oa=e;this.Sd=f};At.prototype.run=function(){var a=this,b,c;return Fa(function(d){if(d.o==1)return b={},ta(d,new Promise(function(e){a.Oa(a.Be,b,e)}),2);c=d.aa;if(!c)return d.return();a.Sd.points.push(4);return ta(d,Bt(a),0)})};
var Bt=function(a){var b,c,d,e,f,g,h,k,l,m,t,r,u,x,v,y,A,P;return Fa(function(S){var la=a.experimentState,G=(l=(b=ms(a.metadata))==null?void 0:b.Df())!=null?l:"",C=(m=(c=ms(a.metadata))==null?void 0:c.Hf())!=null?m:void 0,ba=(d=ms(a.metadata))==null?void 0:Ql(d,1),Ec=(t=(e=ms(a.metadata))==null?void 0:(f=e.Ff())==null?void 0:f.Ta())!=null?t:void 0,cc=(r=(g=ms(a.metadata))==null?void 0:Nl(g,8))!=null?r:void 0,qd=Ct;var Ja=(h=ms(a.metadata))==null?void 0:yl(h,5,Ik,ok===ok?2:4);qd=qd(a,(u=Ja)!=null?
u:void 0);Ja=Ct;var ig=(k=ms(a.metadata))==null?void 0:yl(k,6,Ik,ok===ok?2:4);v={experimentState:la,escapedQueryId:G,trafficTypes:C,isProductSplitVpidLogsExperiment:!0,clientsideModelFilename:ba,geoTargetMessage:Ec,deviceType:cc,productionFilterIds:qd,testFilterIds:Ja(a,(x=ig)!=null?x:void 0)};if(a.experimentState.reachUseCreateWorklet)return P=a.context.Ie[2],a.Sd.points.push(10),ta(S,st(a.context,P,v,a.Sd),0);y=a.context.Ie[0];A=btoa(JSON.stringify(v));return ta(S,Qh(a.context.document,y,A),0)})},
Ct=function(a,b){if(b!==void 0)return b.map(function(c){var d;return String((d=ae(c))!=null?d:0)})};At.prototype.dispose=function(){};var Dt=Ff("m202503260101".match(/^m\d{10}$/g)!==null?"m202503260101":"current"),Et;a:{try{var Ft=new kg;Et=new Hf(Ft,"doubleclickbygoogle.com-omid",void 0,Dt);break a}catch(a){}Et=void 0}var Gt=Et,Ht={N:new Tn(void 0,void 0,void 0,Dt),ka:Gt};
(function(a){if(a&&xg(a)){var b=wg(a);if(b){a.global.fetch("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=fle-fetch-start2",{method:"GET",cache:"no-cache",keepalive:!0,mode:"no-cors"});try{b("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=fle-fetch-later2",{method:"GET",cache:"no-cache",mode:"no-cors",activateAfter:96E4})}catch(c){a.global.fetch("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=fle-fetch-fallback2",{method:"GET",cache:"no-cache",
keepalive:!0,mode:"no-cors"})}a.fg.subscribe(function(){a.global.fetch("https://pagead2.googlesyndication.com/pagead/gen_204?id=av-js&type=fle-fetch-pagehide2",{method:"GET",cache:"no-cache",keepalive:!0,mode:"no-cors"})})}}})(Ht.N);
(function(a,b,c){var d=new Wr("impression"),e=new Wr("begin to render"),f=new Wr("unmeasurable"),g=new Wr("viewable"),h=new Wr("reach vpid"),k=new Ur(d,h,e,g,f),l=new qs,m=new ys(d.event);b=new us(l,c,d.event,e.event,f.event,g.event,b);h=new zt(h.event);var t=new Xr(a,k,l,m,b,h);t.start();return{dispose:function(){return void t.dispose()},colleagues:{zj:m,ak:b,Wj:h}}})(Ht,7);}).call(this);
