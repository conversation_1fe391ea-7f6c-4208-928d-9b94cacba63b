var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; };

$(function () {
  if (!isGitCodeBlog && !isOpenSourceBlog) {
    var isWap = function isWap() {
      return !!window.navigator.userAgent.toLowerCase().match(/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone)/i);
    };

    var gitCodeUrl = function gitCodeUrl(url) {
      if (isiOS) {
        window.location.href = 'https://link.csdn.net?' + 'from_id=' + articleId + '&target=' + encodeURIComponent(url);
        return;
      }
      var alink = document.createElement("a");
      alink.style.display = "none";
      alink.href = 'https://link.csdn.net?' + 'from_id=' + articleId + '&target=' + encodeURIComponent(url);
      alink.target = "_blank";
      document.body.appendChild(alink);
      alink.click();
      document.body.removeChild(alink);
    };

    var goUrl = function goUrl(url) {
      var alink = document.createElement("a");
      alink.style.display = "none";
      alink.href = url;
      alink.target = "_blank";
      document.body.appendChild(alink);
      alink.click();
      document.body.removeChild(alink);
    };

    var isInApp = function isInApp() {
      return !!window.navigator.userAgent.toLowerCase().match(/(csdn)/i) || !!isApp;
    };

    var getClass = function getClass(name, type) {
      if ((typeof name === 'undefined' ? 'undefined' : _typeof(name)) === undefined || name === null || name === '') return;
      name = name.toLowerCase();
      class_name = 'hl-1';
      if (type) {
        class_name = 'hl-git-1';
      }
      return class_name;
    };

    var isEmpty = function isEmpty(obj) {
      if (obj !== undefined && obj !== null && obj !== '') {
        return false;
      } else {
        return true;
      }
    };

    var mergeKeyword = function mergeKeyword(cache, keyword) {
      if (isEmpty(keyword)) return;
      keyword = keyword.toLowerCase().replace(/(^\s*)|(\s*$)/g, "");
      if (isEmpty(keyword)) return;
      if (keyword.indexOf(',') > 0) {
        $.each(keyword.split(','), function (i, n) {
          mergeKeyword(cache, n);
        });
      } else if (keyword !== '...' && keyword.length > 1 && keyword !== '&nbsp;' && keyword !== 'csdn' && keyword !== '||') {
        cache[keyword] = 0;
      }
    };

    var getKeyword = function getKeyword(arr) {
      var keywords = {};
      // var keyword = 'Markdown 欢迎';
      // var arr = ['python', 'opencv'];
      var keyword = arr.join(",");
      mergeKeyword(keywords, keyword);
      var keyword_list = Object.keys(keywords);
      keyword_list.sort(function (x, y) {
        return y.length - x.length;
      });
      return keyword_list;
    };

    var hlDocument = function hlDocument(keyword_list, document, type) {
      var body = document.html();
      var htmlBody = '';
      var textChildrenArr = document.context.childNodes;
      for (var idx = 0; idx < textChildrenArr.length; idx++) {
        var item = textChildrenArr[idx];

        if (item.nodeName === '#text') {
          var curText = item.textContent.replace(/</ig, '&lt;');
          var isChinese = /[\u4e00-\u9fa5]/ig;
          var regKeywordList = keyword_list.map(function (val) {
            var str = val.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
            if (!isChinese.test(str)) {
              return "\\b" + str + "\\b";
            }
            return str;
          });
          var regKeyWord = new RegExp(regKeywordList.join('|'), 'ig');
          var hasHighlight = false;
          curText = curText.replace(regKeyWord, function (val) {
            var keyword = val.toLowerCase();
            var searchHref = "https://so.csdn.net/so/search?q=" + encodeURIComponent(val) + '&spm=1001.2101.3001.7020';
            searchHref = isInApp() ? "csdnapp://app.csdn.net/search/searchRoot?keyword=" + encodeURIComponent(val) + "&t=blog" + "&u=" + username + '&from_tracking_code=art_highlight_word&from_code=app_blog_art&backSearch=true' : searchHref;
            var reportData = isInApp() ? '{"key_word":"' + val + '","spm":"1001.2101.3001.7020"}' : '{"spm":"1001.2101.3001.7020","dest":"' + searchHref + '","extra":"{\\"searchword\\":\\"' + val + '\\"}"}';
            if (type === 'gitcode') {
              reportData = '{"spm":"1001.2101.3001.10283","extra":"{\\"words\\":\\"' + val + '\\"}"}';
            }
            if (cache[keyword] >= 1 || preHlKeyword === keyword || hasHighlight) {
              return val;
            } else {
              hasHighlight = true;
              cache[keyword] = cache[keyword] ? cache[keyword] + 1 : 1;
              preHlKeyword = keyword;
              if (isInApp()) {
                return '<a data-href="' + searchHref + '" class="app-hl hl ' + getClass(keyword) + '" data-track-view=' + reportData + ' data-report-click=' + reportData + '>' + val + '</a>';
              } else {
                if (type === 'gitcode') {
                  return '<span class="words-blog ' + getClass(keyword, 'gitcode') + '" data-report-view=' + reportData + ' data-tit=' + val + ' data-pretit=' + preHlKeyword + '>' + val + '</span>';
                } else {
                  return '<a href="' + searchHref + '" target="_blank" class="hl ' + getClass(keyword) + '" data-report-view=' + reportData + ' data-report-click=' + reportData + ' data-tit=' + val + ' data-pretit=' + preHlKeyword + '>' + val + '</a>';
                }
              }
            }
          });
          htmlBody += curText;
        } else {
          // console.log(item.outerHTML)
          htmlBody += item.outerHTML ? item.outerHTML : '';
        }
      }
      document[0].innerHTML = htmlBody;
      if (!isInApp()) {
        window.csdn.report.viewCheck();
      } else {
        $(window).on("scroll", function () {
          appTrack.viewCheck();
        });
        appTrack.viewCheck();
      }
    };

    var hlDocumentForEdu = function hlDocumentForEdu(keyword_list, document) {
      var body = document.html();
      var htmlBody = '';
      var textChildrenArr = document.context.childNodes;
      for (var idx = 0; idx < textChildrenArr.length; idx++) {
        var item = textChildrenArr[idx];

        if (item.nodeName === '#text') {
          var curText = item.textContent.replace(/</ig, '&lt;');
          var isChinese = /[\u4e00-\u9fa5]/ig;
          var regKeywordList = keyword_list.map(function (val) {
            var str = val.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
            if (!isChinese.test(str)) {
              return "\\b" + str + "\\b";
            }
            return str;
          });
          var regKeyWord = new RegExp(regKeywordList.join('|'), 'ig');
          var hasHighlight = false;
          curText = curText.replace(regKeyWord, function (val) {
            var keyword = val.toLowerCase();
            var reportData = '{"spm":"1001.2101.3001.7020","extra":"{\\"word\\":\\"' + val + '\\"}"}';

            if (cache[keyword] >= 1 || preHlKeyword === keyword || hasHighlight) {
              return val;
            } else {
              hasHighlight = true;
              cache[keyword] = cache[keyword] ? cache[keyword] + 1 : 1;
              preHlKeyword = keyword;
              return '<span class="edu-hl hl ' + getClass(keyword) + '" data-report-view=' + reportData + ' data-report-click=' + reportData + ' data-tit=' + val + ' data-pretit=' + preHlKeyword + '>' + val + '</span>';
            }
          });
          htmlBody += curText;
        } else {
          htmlBody += item.outerHTML ? item.outerHTML : '';
        }
      }
      document[0].innerHTML = htmlBody;
      if (!isInApp()) {
        window.csdn.report.viewCheck();
      } else {
        $(window).on("scroll", function () {
          appTrack.viewCheck();
        });
        appTrack.viewCheck();
      }
    };

    var appClickFun = function appClickFun() {
      $('a.app-hl').off('click');
      $('a.app-hl').click(function (event) {
        event.preventDefault();
        var href = $(this).data('href');
        var report = $(this).attr('data-report-click');
        var reportData = {
          'action': 'app_blog_highlight',
          'trackingInfo': report,
          'trackingCode': 'art_highlight_word'
        };
        var hrefData = {
          url: href
        };
        if (isAndroid) {
          javascript: window.jsCallBackListener.csdntrackevent(JSON.stringify(reportData));
          javascript: window.jsCallBackListener.csdnjumpnewpage(JSON.stringify(hrefData));
        }
        if (isiOS) {
          window.webkit.messageHandlers.csdntrackevent.postMessage(JSON.stringify(reportData));
          window.webkit.messageHandlers.csdnjumpnewpage.postMessage(JSON.stringify(hrefData));
        }
      });
    };

    var initHighlight = function initHighlight() {
      window.keyword_list_init = true;
      $.ajax({
        type: "GET",
        url: 'https://redisdatarecall.csdn.net/recommend/get_head_word' + '?bid=blog-' + articleId,
        dataType: 'json',
        timeout: 2000,
        xhrFields: { withCredentials: true },
        success: function success(data) {
          // test
          // data.content = ["希尔排序", "冒泡排序", "基数排序"];
          // https://test-blog-web.csdn.net/cpongo6/article/details/120159768
          if (data.status == 200 && data.content) {
            var keyword_list = getKeyword(data.content);
            var keyword_list_json = data.ext ? data.ext : {};
            if (keyword_list === undefined || keyword_list.length === 0) {
              return;
            }
            var arr = [];
            keyword_list.forEach(function (item, index) {
              item = item.toLowerCase();
              if (gitcodekey.indexOf(item) == -1) {
                arr.push(item);
              }
            });
            if (arr === undefined || arr.length === 0) {
              return;
            }
            window.keyword_list = arr;
            window.keyword_list_json = keyword_list_json;
            var domList = $("#content_views").find("p,h1,h2,h3,h4,h5,h6");
            window.keyword_list_doms = domList;
            $.each(domList, function (i, n) {
              var n = $(n);
              hlDocument(arr, n);
            });
            if (isInApp()) {
              appClickFun();
            }
          }
        }
      });
    };

    var gitHighlighterPost = function gitHighlighterPost() {
      var updateTime = lastTime ? lastTime : postTime;
      updateTime = new Date(updateTime).getTime();
      $.ajax({
        url: 'https://clink-api.gitcode.com/api/v1/highlight/get',
        type: 'get',
        xhrFields: { crossDomain: true, withCredentials: true },
        data: {
          article_id: articleId,
          username: username,
          updated_at: updateTime
        },
        success: function success(data) {},
        error: function error(err) {}
      });
    };

    var id = 1;
    var cache = {};
    var preHlKeyword = '';
    var isApp = window.isApp ? window.isApp : false;
    var u = navigator.userAgent;
    var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 || u.indexOf('OHOS') > -1; //android终端
    var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
    var gitcodekey = getKeyword(privateData) || [];
    var eduKey = getKeyword(privateEduData) || [];

    if (!isApp && gitcodekey && gitcodekey.length > 0) {
      var domList = $("#content_views").find("p,h1,h2,h3,h4,h5,h6");
      $.each(domList, function (i, n) {
        var n = $(n);
        hlDocument(gitcodekey, n, 'gitcode');
      });
      var gircodeclickflag = true;
      $(document).on('click', '.words-blog', function () {
        var code = $(this).data('pretit').toLowerCase();
        if (gircodeclickflag) {
          gircodeclickflag = false;
          $.ajax({
            // url: 'https://test.gitcode.net/api/v1/search/nauth/project/words',
            url: 'https://web-api.gitcode.com/api/v1/search/nauth/project/words',
            type: 'get',
            data: {
              words: code
            },
            xhrFields: {
              crossDomain: true,
              withCredentials: true
            },
            success: function success(data) {
              var url = '';
              if (data.status == 200 && data.data && data.data[code]) {
                gitCodeUrl(data.data[code]);
                url = data.data[code];
              } else {
                gitCodeUrl('https://gitcode.com?utm_source=highlight_word_gitcode&is_default=1&word=' + code);
              }
              window.csdn.report.reportClick({
                'spm': '1001.2101.3001.10283',
                'extra': { 'words': code },
                'dest': url
              });
              gircodeclickflag = true;
            },
            error: function error(data) {
              gitCodeUrl('https://gitcode.com?utm_source=highlight_word_gitcode&is_default=1&word=' + code);
              gircodeclickflag = true;
            }
          });
        }
      });
    }

    if (!isApp && eduKey && eduKey.length > 0) {
      var domList = $("#content_views").find("p,h1,h2,h3,h4,h5,h6");
      $.each(domList, function (i, n) {
        var n = $(n);
        hlDocumentForEdu(eduKey, n);
      });

      var educlickflag = true;
      $(document).on('click', 'span.edu-hl.hl', function () {
        var keyword = $(this).data('pretit').toLowerCase();
        if (educlickflag) {
          educlickflag = false;
          $.ajax({
            url: 'https://edu-api.csdn.net/edu/web/external/v1/hl/getWordsUrl',
            type: 'get',
            data: {
              keywords: keyword
            },
            xhrFields: {
              crossDomain: true,
              withCredentials: true
            },
            success: function success(data) {
              var url = '';
              if (data.code == 200 && data.data) {
                url = data.data + '&spm=1001.2101.3001.7020&utm_term=' + encodeURIComponent(keyword);
                window.csdn.report.reportClick({
                  'spm': '1001.2101.3001.7020',
                  'extra': { 'words': keyword },
                  'dest': url
                });
                goUrl(url);
              }
              educlickflag = true;
            },
            error: function error(data) {
              educlickflag = true;
            }
          });
        }
      });
    }


    var appTrackCheckTimer;
    var appTrack = {
      scrollTop: function scrollTop() {
        return Math.max(
        //chrome
        document.body.scrollTop,
        //firefox/IE
        document.documentElement.scrollTop);
      },
      //获取页面浏览器视口的高度
      windowHeight: function windowHeight() {
        //document.compatMode有两个取值。BackCompat：标准兼容模式关闭。CSS1Compat：标准兼容模式开启。
        return document.compatMode == "CSS1Compat" ? document.documentElement.clientHeight : document.body.clientHeight;
      },
      getElementBottom: function getElementBottom(elem) {
        if (typeof jQuery !== 'undefined') {
          return $(elem).offset().top + $(elem).height();
        }
        var elemTop = elem.offsetTop; //获得elem元素距相对定位的父元素的top

        elem = elem.offsetParent; //将elem换成起相对定位的父元素

        while (elem != null) {
          //只要还有相对定位的父元素

          //获得父元素 距他父元素的top值,累加到结果中

          elemTop += elem.offsetTop;

          //再次将elem换成他相对定位的父元素上;

          elem = elem.offsetParent;
        }
        return elemTop;
      },
      isView: function isView(node) {
        var $this = this;
        if (!node) {
          return false;
        }
        var nodeTop = appTrack.getElementBottom(node);
        var bottomTop = nodeTop + node.offsetHeight;

        if ($this.scrollTop() < nodeTop && nodeTop < $this.scrollTop() + $this.windowHeight()) {
          return true;
        } else if ($this.scrollTop() < bottomTop && bottomTop < $this.scrollTop() + $this.windowHeight()) {
          return true;
        } else {
          return false;
        }
      },
      reportView: function reportView(reportData) {
        console.log('app report exposure', reportData);
        if (isAndroid) {
          console.log('isAndroid');
          javascript: window.jsCallBackListener.csdntrackexpo(JSON.stringify(reportData));
        }
        if (isiOS) {
          console.log('isiOS');
          window.webkit.messageHandlers.csdntrackexpo.postMessage(JSON.stringify(reportData));
        }
      },
      viewCheck: function viewCheck() {
        var _this = this;
        //收集,检查带曝光的节点,曝光后
        clearTimeout(appTrackCheckTimer);
        appTrackCheckTimer = setTimeout(function () {
          //查找所有未曝光的节点,加入List,判断:如果在可是区域内,执行曝光
          $("[data-track-view]").each(function () {
            var $this = $(this);
            if (appTrack.isView($this.get(0))) {
              //console.log("曝光区域",data.mod);
              var report = $this.attr('data-track-view');
              var reportData = {
                'trackingCode': 'art_highlight_word',
                'trackingInfo': report,
                'action': 'app_blog_highlight'
              };
              appTrack.reportView(reportData);
              $this.removeData("trackView");
              $this.removeAttr("data-track-view");
              // $this.removeClass("track-ready").addClass("track-finish");
            }
          });
        }, 200);
      }
    };

    initHighlight();
    var keywordDecBox = $('#keywordDecBox');
    if (keywordDecBox.length) {
      $(document).on('mouseover', '#article_content .hl', function () {
        var tit = $(this).attr("data-tit");
        var pretit = $(this).attr("data-pretit");
        var dec = window.keyword_list_json ? Object(keyword_list_json)[pretit] : '';
        if (tit && dec) {
          var offsetLeft = $(this).offset().left;
          var offsetTop = $(this).offset().top;
          var height = $(this).outerHeight(true);
          var decDom = '<span class="tit">' + tit + '</span><span class="dec">' + dec + '</span>';
          keywordDecBox.html(decDom).css({
            'left': offsetLeft,
            'top': offsetTop + height
          }).show();
        }
      });
      $(document).on('mouseout', '#article_content .hl', function () {
        keywordDecBox.hide();
      });
      keywordDecBox.on({
        mouseover: function mouseover() {
          $(this).show();
        },
        mouseout: function mouseout() {
          $(this).hide();
        }
      });
    }
    if (!isApp) {
      // gitHighlighterPost()
    }
  }
});
;(function () {
  //判断是否手机端访问
  function isWap() {
    return !!window.navigator.userAgent.toLowerCase().match(/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone)/i);
  }
  //判断是否是app
  function isApp() {
    return !!window.navigator.userAgent.toLowerCase().match(/(csdn)/i);
  }
  function reportRecommendLength() {
    var recommendDomList = null;
    var baiduList = [];
    if (isWap()) {
      recommendDomList = document.querySelectorAll('[class^="container-fluid container-fluid-flex container-"]');
      baiduList = document.querySelectorAll(".container-fluid.bdinsert");
    } else if (isApp()) {
      return false;
    } else {
      recommendDomList = document.querySelectorAll("div[class^='recommend-item-box type_'],div.recommend-item-box.baiduSearch");
      baiduList = document.querySelectorAll("div.recommend-item-box.baiduSearch");
    }

    recommendDomList = recommendDomList !== null ? recommendDomList.length : 0;
    var defaultCount = recommendDomList - baiduList.length;
    if (defaultCount === 0) {
      $.get('https://statistic.csdn.net/blog/recommend?count=' + defaultCount + '&baiducount=' + baiduList.length + '&articleId=' + articleId);
    }
  }
  reportRecommendLength();
})();
;(function () {
  //判断是否手机端访问
  function isWap() {
    return !!window.navigator.userAgent.toLowerCase().match(/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone)/i);
  }
  //判断是否是app
  function isApp() {
    return !!window.navigator.userAgent.toLowerCase().match(/(csdn)/i);
  }
  var contentDemo = $('#content_views');
  if ($('.vipmaskclassname').length > 0) {
    if (!isApp()) {
      var device = 'pc';
      if (isWap()) {
        device = 'wap';
      }
      if (!!window.csdn.report && typeof window.csdn.report.reportView === 'function') {
        window.csdn.report.reportView({
          'spm': '3001.10335',
          'extra': { 'articleId': articleId + '', 'articleHeight': contentDemo.height(), 'device': device }
        });
      }
    }
    var demoHeight = 0;
    if (400 <= contentDemo.height() && contentDemo.height() < 1500) {
      demoHeight = contentDemo.height() / 2;
    }
    if (contentDemo.height() >= 1500) {
      demoHeight = 1000;
    }
    if (demoHeight > 0) {
      contentDemo.css({
        'height': demoHeight + 'px',
        'overflow': 'hidden'
      });
      $('.vipmaskclassname').show();
    } else {
      $('.vipmaskclassname').remove();
    }
  }
})();