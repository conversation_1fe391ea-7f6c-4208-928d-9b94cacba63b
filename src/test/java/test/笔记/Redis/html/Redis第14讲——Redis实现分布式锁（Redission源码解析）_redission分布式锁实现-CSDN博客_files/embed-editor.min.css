.rc-dialog-magnetic{display:inline-block;z-index:1001}.rc-dialog-hover-ball{align-items:center;background:#fff;box-shadow:0 5px 5px -3px rgba(0,0,0,.1),0 8px 10px 1px rgba(0,0,0,.06),0 3px 14px 2px rgba(0,0,0,.05);box-sizing:border-box;cursor:pointer;display:flex;height:40px;justify-content:space-between;padding:4px 12px 4px 4px;transition:all .1s;width:72px}.rc-dialog-hover-ball-hover{width:240px}.rc-dialog-hover-ball-hide{display:none}.rc-dialog-hover-ball-content{align-items:center;display:flex;flex:auto;max-width:calc(100% - 24px);overflow:hidden}.rc-dialog-hover-ball-logo{align-items:center;background:linear-gradient(84deg,#1063ff .04%,#3185ff 100.04%);border-radius:50%;display:flex;flex-shrink:0;height:32px;justify-content:center;width:32px}.rc-dialog-hover-ball-logo-img{height:11px;width:20px}.rc-dialog-hover-ball-addon{align-items:center;display:flex;flex:auto;margin-left:8px}.rc-dialog-hover-ball-addon-code{flex:auto;height:34px;overflow:hidden;width:120px}.rc-dialog-hover-ball-addon-code>div{color:rgba(0,0,0,.6);font-family:PingFang SC;font-size:13px;font-style:normal;font-weight:400;line-height:16px;overflow:hidden;text-overflow:ellipsis}.rc-dialog-hover-ball-addon-code-content{white-space:nowrap}.rc-dialog-hover-ball-close{align-items:center;display:flex;height:100%;margin-left:4px;width:20px}.rc-dialog-hover-ball-close>img{height:20px}.rc-dialog-hover-ball-drag{cursor:move}.rc-dialog-magnetic-inner-left{border-radius:0 40px 40px 0;flex-direction:row-reverse}.rc-dialog-magnetic-inner-left .rc-dialog-hover-ball-content{flex-direction:row-reverse}.rc-dialog-magnetic-inner-right{border-radius:40px 0 0 40px}.rc-dialog-tools-bar{align-items:center;display:flex;gap:12px;height:44px;justify-content:center;position:absolute;right:42px}.rc-dialog-tools-bar-btn{cursor:pointer}.rc-dialog-tools-bar-action-btn{align-items:center;border-radius:4px;cursor:pointer;display:inline-flex;height:24px;justify-content:center;width:24px}.rc-dialog-tools-bar-action-btn:active{background-color:#d8e2ef}.rc-dialog-mask{background-color:#373737;background-color:rgba(55,55,55,.6);bottom:0;height:100%;left:0;position:fixed;right:0;top:0;z-index:2000}.rc-dialog-mask-hidden{display:none}.rc-dialog-fade-appear,.rc-dialog-fade-enter{opacity:0}.rc-dialog-fade-appear,.rc-dialog-fade-enter,.rc-dialog-fade-leave{animation-duration:.3s;animation-fill-mode:both;animation-play-state:paused;animation-timing-function:cubic-bezier(.55,0,.55,.2)}.rc-dialog-fade-appear.rc-dialog-fade-appear-active,.rc-dialog-fade-enter.rc-dialog-fade-enter-active{animation-name:rc-dialog-fade-in;animation-play-state:running}.rc-dialog-fade-leave.rc-dialog-fade-leave-active{animation-name:rc-dialog-fade-out;animation-play-state:running}@keyframes rc-dialog-fade-out{0%{opacity:1}to{opacity:0}}.rc-dialog{height:720px;margin:0 auto;position:relative;top:100px;width:66.66vw}.rc-dialog-legacy{bottom:0!important;height:222px!important;margin:0!important;position:absolute!important;top:auto!important;width:100vw!important}.rc-dialog-legacy-tip{padding:40px 27px 32px}.rc-dialog-legacy-tip-title{color:#181818;font-size:18px;font-weight:500;line-height:26px;margin-bottom:12px}.rc-dialog-legacy-tip-content,.rc-dialog-legacy-tip-title{font-family:PingFang SC;font-style:normal;text-align:center}.rc-dialog-legacy-tip-content{color:#4c5b76;font-size:14px;font-weight:400;line-height:22px;margin-bottom:32px}.rc-dialog-legacy-tip-content>p{margin:0}.rc-dialog-legacy-tip-action{display:flex;gap:20px}.rc-dialog-legacy-tip-action>button{background:#fff;border:1px solid #0052d9;color:#0052d9;flex:1 1;flex-shrink:0;font-size:14px;height:36px;width:150px}.rc-dialog-legacy-tip-action>button.primary{background:#0052d9;color:#fff;font-family:PingFang SC;font-size:14px;font-style:normal;font-weight:400;line-height:22px;text-align:center}.rc-dialog-legacy,.rc-dialog-legacy .rc-dialog-body{background-color:#fff}.rc-dialog-UNKNOWN{height:636px;width:596px!important}.rc-dialog-UNKNOWN .rc-dialog-content{border-radius:8px;overflow:hidden}.rc-dialog-NO{height:527px;width:496px!important}.rc-dialog-NO .rc-dialog-content{border-radius:8px;overflow:hidden}.rc-dialog-mask-minimized{left:-100%;top:-100%}.rc-dialog-wrap{-webkit-overflow-scrolling:touch;bottom:0;left:0;outline:0;overflow:auto;position:fixed;right:0;top:0;z-index:2000}.rc-dialog-wrap-maximized{overflow:hidden}.rc-dialog-wrap-minimized{bottom:auto;left:-100%;position:absolute;right:auto;top:-100%}.rc-dialog-title{font-size:14px;font-weight:700;line-height:21px;margin:0}.rc-dialog-content{background-clip:padding-box;background-color:#fff;border:none;height:100%;position:relative}.rc-dialog>div{height:100%}.rc-dialog-body{background-color:#eef2f9;height:100%}.rc-dialog-close{background:transparent;border:0;color:#455267;cursor:pointer;font-size:21px;font-weight:700;line-height:1;opacity:1;padding:0;position:absolute;right:8px;text-decoration:none;text-shadow:0 1px 0 #fff;top:10px}.rc-dialog-close:hover{text-decoration:none}.rc-dialog-close>img:hover{background:#eef2f9;background:var(--blue-gray-blue-gray-2,#eef2f9);border-radius:4px}.rc-dialog-close>img:active{background:#dbe6f2;background:var(--blue-gray-blue-gray-3,#dbe6f2)}.rc-dialog-header{background:#fff;border-bottom:1px solid #e9e9e9;border-radius:5px 5px 0 0;color:#666;padding:13px 20px 14px}.rc-dialog-footer{border-radius:0 0 5px 5px;border-top:1px solid #e9e9e9;padding:10px 20px;text-align:right}.rc-dialog-maximized{height:100vh!important;margin:0!important;position:static;width:100vw!important}.rc-dialog-zoom-appear,.rc-dialog-zoom-enter{animation-duration:.3s;animation-fill-mode:both;animation-play-state:paused;animation-timing-function:cubic-bezier(.08,.82,.17,1);opacity:0}.rc-dialog-zoom-leave{animation-duration:.3s;animation-fill-mode:both;animation-play-state:paused;animation-timing-function:cubic-bezier(.6,.04,.98,.34)}.rc-dialog-zoom-appear.rc-dialog-zoom-appear-active,.rc-dialog-zoom-enter.rc-dialog-zoom-enter-active{animation-name:rc-dialog-zoom-in;animation-play-state:running}.rc-dialog-zoom-leave.rc-dialog-zoom-leave-active{animation-name:rc-dialog-zoom-out;animation-play-state:running}@keyframes rc-dialog-zoom-in{0%{opacity:0;transform:scale(0)}to{opacity:1;transform:scale(1)}}@keyframes rc-dialog-zoom-out{0%{transform:scale(1)}to{opacity:0;transform:scale(0)}}@media (max-width:1300px){.rc-dialog{width:730px}}@media (max-width:2560px){.rc-dialog{top:60px;width:1200px}}@media (min-width:2560px){.rc-dialog{height:840px;top:100px;width:1400px}}@media (max-width:530px){.rc-dialog{height:calc(100vh - 24px);margin:12px auto;top:0;width:calc(100vw - 24px)}}