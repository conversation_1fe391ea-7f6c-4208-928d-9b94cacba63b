!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.lib=e():t.lib=e()}(window,function(){return n=[function(t,e,n){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===o(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0}),e.webPrint=void 0;var a=n(3),n=function(){function t(){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");this.logLevel=a.LogLevel.INFO}var e,n,r;return e=t,(n=[{key:"initLogLevel",value:function(t){t&&(t.debug?t.logLevel&&a.LogLevel[t.logLevel]?this.logLevel=t.logLevel:this.logLevel=a.LogLevel.INFO:this.logLevel=a.LogLevel.OFF)}},{key:"log",value:function(t){var e=this.generateLevels();0<e&&e<=3&&console.log("LTSSDK:".concat(t))}},{key:"warn",value:function(t){var e=this.generateLevels();1<=e&&e<=2&&console.warn("LTSSDK:".concat(t))}},{key:"error",value:function(t){1===this.generateLevels()&&console.error("LTSSDK:".concat(t))}},{key:"generateLevels",value:function(){return this.logLevel===a.LogLevel.INFO?3:this.logLevel===a.LogLevel.WARN?2:this.logLevel===a.LogLevel.ERROR?1:0}}])&&i(e.prototype,n),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();e.webPrint=new n},function(t,r,e){"use strict";function o(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,c=[],u=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(c.push(r.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){var n;if(t)return"string"==typeof t?i(t,e):"Map"===(n="Object"===(n=Object.prototype.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(t,e):void 0}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Object.defineProperty(r,"__esModule",{value:!0}),r.checkConfigKey=r.checkLabels=r.checkObjectTypeAndLength=r.checkPattern=r.checkLogParamString=r.excludeObject=r.isValidString=r.isEmpty=void 0;var c=e(0),u="The length of $1 exceeds the maximum value of $2.",l="$1 is null.",f="$1 is invalid.";function n(t){return!t||""===t}r.isEmpty=n;r.isValidString=function(t,e){return n(t)?(c.webPrint.warn("LTS.0001|".concat(l.replace("$1",e))),!1):"string"==typeof t||(c.webPrint.warn("LTS.0002|".concat(f.replace("$1",e))),!1)},r.excludeObject=function(t,e){return"object"!==a(t)||(c.webPrint.warn("LTS.0002|".concat(f.replace("$1",e))),!1)},r.checkLogParamString=function(t,e,n){return e?!(String(e).length>n&&(c.webPrint.warn("LTS.0003|".concat(u.replace("$1",t).replace("$2",n))),1)):(c.webPrint.warn("LTS.0001|".concat(l.replace("$1",t))),!1)},r.checkPattern=function(t,e,n){return!!n.test(e)||(c.webPrint.warn("LTS.0006|".concat(t," doesn't match pattern.")),!1)},r.checkObjectTypeAndLength=function(t,e,n){return t&&"object"!==a(t)||"boolean"==typeof t?(c.webPrint.warn("LTS.0002|".concat(f.replace("$1",e))),!1):!(Object.keys(t).length>n&&(c.webPrint.warn("LTS.0003|".concat(u.replace("$1",e).replace("$2",n))),1))},r.checkLabels=function(t){var n;return!t||(n=!0,Object.entries(t||{}).forEach(function(t){var t=o(t,2),e=t[0],t=t[1];(0,r.checkLogParamString)("labels key:".concat(e),e,64)||(n=!1),(0,r.checkPattern)("labels key:".concat(e),e,/^[a-zA-Z][A-Za-z0-9_]*$/)||(n=!1),(0,r.checkLogParamString)("labels value",t,256)&&(0,r.excludeObject)(t,"labels value of :".concat(e))||(n=!1)}),n&&(0,r.checkObjectTypeAndLength)(t,"labels",50))};r.checkConfigKey=function(t){var e,n=new Set(["url","region","projectId","groupId","streamId","group","cacheThreshold","timeThreshold","debug","platform","logLevel","timeInterval"]);return!t||(e=!0,Object.keys(t).forEach(function(t){n.has(t)||(e=!1)}),e||c.webPrint.warn("LTS.0007|Invalid configuration parameters."),e)}},function(t,e,n){"use strict";function r(t){return function(t){if(Array.isArray(t))return o(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){var n;if(t)return"string"==typeof t?o(t,e):"Map"===(n="Object"===(n=Object.prototype.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(t,e):void 0}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}Object.defineProperty(e,"__esModule",{value:!0}),e.getLabelMap=e.getHeader=e.getBodyArray=void 0;var i=n(11),a=n(12).version,c=(e.getBodyArray=function(t,e,n){return u(n,t)?c(e,n):null},function(t,e){return{labels:t&&JSON.parse(t)||{},logs:(e||[]).map(function(t){return{contents:[Object.assign(Object.assign({},null==t?void 0:t.content),{__client_time__:t.log_time_ns})]}})}}),u=function(t,e){return!(!t||0===t.length||(e=(t=e||{}).groupId,t=t.streamId,!e)||!t)},l=(e.getLabelMap=function(t){var e=new Map;return t.forEach(function(t){e.has(t.labels)?e.set(t.labels,[].concat(r(e.get(t.labels)),[t])):e.set(t.labels,[t])}),e},e.getHeader=function(t){var e="X-Sdk-date",n="Lts-Sdk-Version",r={"Content-Type":"application/json"};return r["Lts-Sdk-Request-Id"]=i.UUID.create(),r[n]=a,r[e]=l(),Object.assign(Object.assign({},r),t)},function(){try{return(new Date).toISOString().replace(/\.[\d]{3}Z/,"Z").replace(/(\:)|(\-)/g,"")}catch(t){return""}})},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.LogLevel=e.REGIONS=e.POLICY_CONSTANTS=void 0,e.POLICY_CONSTANTS={TIME_MIN:1,TIME_MAX:60,THRESHOLD_MIN:30,THRESHOLD_MAX:1e3},e.REGIONS={"cn-north-7":"cn-north-7","cn-north-4":"cn-north-4","cn-north-6":"cn-north-6","cn-north-5":"cn-north-5","cn-north-9":"cn-north-9"},e.LogLevel={OFF:"OFF",INFO:"INFO",WARN:"WARN",ERROR:"ERROR"}},function(t,e,n){t.exports=n(5)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,e=n(6);n=window,r="LTS_WEB_SDK",e=e.default,n[r]||Object.defineProperty(n,r,{writable:!0,enumerable:!0,configurable:!0,value:e})},function(t,e,n){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===o(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(7),c=n(13),u=n(0),l=n(1),n=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this.uploading=!1,this.sendService={reportFlag:!1,url:""},this.logRecords=[],this.config(t)}var t,n,r;return t=e,(n=[{key:"config",value:function(t){(0,l.checkObjectTypeAndLength)(t,"Configuration",11)&&(this.webConfig=Object.assign(Object.assign({},this.webConfig),t),u.webPrint.initLogLevel(this.webConfig),this.webConfig.timeThreshold=(null==(t=this.webConfig)?void 0:t.timeInterval)||(null==(t=this.webConfig)?void 0:t.timeThreshold)||30),c.default.checkConfig(this.webConfig)?(this.sendService.reportFlag=!0,this.sendService.url=c.default.updateUrl(this.webConfig),this.logUploadPolicyScheduleTime(),u.webPrint.log("init success")):(this.sendService.reportFlag=!1,u.webPrint.error("Failed to initialize the sdk."))}},{key:"reportImmediately",value:function(t,e){u.webPrint.initLogLevel(this.webConfig),this.sendService.reportFlag?(a.default.writeLog(Object.assign(Object.assign({},this.webConfig),this.sendService),this.logRecords,t,e),a.default.uploadData(Object.assign(Object.assign({},this.webConfig),this.sendService),this.logRecords)):u.webPrint.log("LTS.0100|The LTSSDK is not initialized.")}},{key:"report",value:function(t,e){u.webPrint.initLogLevel(this.webConfig),this.sendService.reportFlag?t?a.default.writeLog(Object.assign(Object.assign({},this.webConfig),this.sendService),this.logRecords,t,e):u.webPrint.warn("LTS.0001|Content is null."):u.webPrint.log("LTS.0100|The LTSSDK is not initialized.")}},{key:"logUploadPolicyScheduleTime",value:function(){var t;this.webConfig.timeThreshold&&!this.uploading&&(t=1e3*this.webConfig.timeThreshold,this.setHeartbeatFunc(t))}},{key:"setHeartbeatFunc",value:function(t){var e=this;this.sendTimer=setTimeout(function(){e.uploading=!0,e.logRecords=e.logRecords.filter(function(t){return"SUCCESS"!==t.sendState}),a.default.uploadData(Object.assign(Object.assign({},e.webConfig),e.sendService),e.logRecords).then(function(){e.uploading=!1,e.logUploadPolicyScheduleTime()}).catch(function(){e.uploading=!1,e.logUploadPolicyScheduleTime()})},t)}}])&&i(t.prototype,n),r&&i(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();e.default=n},function(t,e,n){"use strict";function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function P(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */P=function(){return a};var a={},t=Object.prototype,u=t.hasOwnProperty,l=Object.defineProperty||function(t,e,n){t[e]=n.value},e="function"==typeof Symbol?Symbol:{},r=e.iterator||"@@iterator",n=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function i(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{i({},"")}catch(t){i=function(t,e,n){return t[e]=n}}function c(t,e,n,r){var o,i,a,c,e=e&&e.prototype instanceof d?e:d,e=Object.create(e.prototype),r=new S(r||[]);return l(e,"_invoke",{value:(o=t,i=n,a=r,c="suspendedStart",function(t,e){if("executing"===c)throw new Error("Generator is already running");if("completed"===c){if("throw"===t)throw e;return O()}for(a.method=t,a.arg=e;;){var n=a.delegate;if(n){n=function t(e,n){var r=n.method,o=e.iterator[r];if(void 0===o)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=void 0,t(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),s;r=f(o,e.iterator,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,s;o=r.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=void 0),n.delegate=null,s):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,s)}(n,a);if(n){if(n===s)continue;return n}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===c)throw c="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);c="executing";n=f(o,i,a);if("normal"===n.type){if(c=a.done?"completed":"suspendedYield",n.arg===s)continue;return{value:n.arg,done:a.done}}"throw"===n.type&&(c="completed",a.method="throw",a.arg=n.arg)}})}),e}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}a.wrap=c;var s={};function d(){}function h(){}function p(){}var e={},y=(i(e,r,function(){return this}),Object.getPrototypeOf),y=y&&y(y(L([]))),b=(y&&y!==t&&u.call(y,r)&&(e=y),p.prototype=d.prototype=Object.create(e));function g(t){["next","throw","return"].forEach(function(e){i(t,e,function(t){return this._invoke(e,t)})})}function v(a,c){var e;l(this,"_invoke",{value:function(n,r){function t(){return new c(function(t,e){!function e(t,n,r,o){var i,t=f(a[t],a,n);if("throw"!==t.type)return(n=(i=t.arg).value)&&"object"==j(n)&&u.call(n,"__await")?c.resolve(n.__await).then(function(t){e("next",t,r,o)},function(t){e("throw",t,r,o)}):c.resolve(n).then(function(t){i.value=t,r(i)},function(t){return e("throw",t,r,o)});o(t.arg)}(n,r,t,e)})}return e=e?e.then(t,t):t()}})}function m(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function w(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(m,this),this.reset(!0)}function L(e){if(e){var n,t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return n=-1,(t=function t(){for(;++n<e.length;)if(u.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t}).next=t}return{next:O}}function O(){return{value:void 0,done:!0}}return l(b,"constructor",{value:h.prototype=p,configurable:!0}),l(p,"constructor",{value:h,configurable:!0}),h.displayName=i(p,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,i(t,o,"GeneratorFunction")),t.prototype=Object.create(b),t},a.awrap=function(t){return{__await:t}},g(v.prototype),i(v.prototype,n,function(){return this}),a.AsyncIterator=v,a.async=function(t,e,n,r,o){void 0===o&&(o=Promise);var i=new v(c(t,e,n,r),o);return a.isGeneratorFunction(e)?i:i.next().then(function(t){return t.done?t.value:i.next()})},g(b),i(b,o,"Generator"),i(b,r,function(){return this}),i(b,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,n=Object(t),r=[];for(e in n)r.push(e);return r.reverse(),function t(){for(;r.length;){var e=r.pop();if(e in n)return t.value=e,t.done=!1,t}return t.done=!0,t}},a.values=L,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(w),!t)for(var e in this)"t"===e.charAt(0)&&u.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var r=this;function t(t,e){return i.type="throw",i.arg=n,r.next=t,e&&(r.method="next",r.arg=void 0),!!e}for(var e=this.tryEntries.length-1;0<=e;--e){var o=this.tryEntries[e],i=o.completion;if("root"===o.tryLoc)return t("end");if(o.tryLoc<=this.prev){var a=u.call(o,"catchLoc"),c=u.call(o,"finallyLoc");if(a&&c){if(this.prev<o.catchLoc)return t(o.catchLoc,!0);if(this.prev<o.finallyLoc)return t(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return t(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return t(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&u.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}var i=(o=o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc?null:o)?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,s):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),s},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),w(n),s}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n,r,o=this.tryEntries[e];if(o.tryLoc===t)return"throw"===(n=o.completion).type&&(r=n.arg,w(o)),r}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:L(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),s}},a}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==j(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==j(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===j(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0});var i=n(8),a=n(9),c=n(2),u=n(1),l=n(0),n=function(){function t(){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");this.failTime=0,this.failTimer=null,this.censor=function(t,e){return e==1/0||e==-1/0?String(e):e}}var e,n,r;return e=t,(n=[{key:"uploadData",value:function(r,o){return i.__awaiter(this,void 0,void 0,P().mark(function t(){var e,n=this;return P().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(r.reportFlag){t.next=2;break}return t.abrupt("return");case 2:if(10<=this.failTime)return this.initTimeLock(),t.abrupt("return");t.next=5;break;case 5:if(e=o.filter(function(t){return"READY"===t.sendState||"RESEND"===t.sendState}),(e=(0,c.getLabelMap)(e))&&0!==e.size){t.next=9;break}return t.abrupt("return");case 9:e.forEach(function(t,e){t=n.getLogListSplitByBodyLimit(t);n.sendDataByPackage(t,e,r)});case 10:case"end":return t.stop()}},t,this)}))}},{key:"getLogListSplitByBodyLimit",value:function(e){var n,r,o,i;return e&&0!==e.length?(n=[],i=o=r=0,e.forEach(function(t){t=JSON.stringify(t).length;96256<=(i+=t)&&(n.push(e.slice(r,o)),i=t,r=o),o++}),r!==o&&n.push(e.slice(r,o)),n):[]}},{key:"sendDataByPackage",value:function(t,r,o){var i=this;t.forEach(function(e){var t,n=(0,c.getBodyArray)(o,r,e);"string"==typeof n&&(0,u.isEmpty)(n)||(t=(0,c.getHeader)({}),e.forEach(function(t){t.sendState="SENDING"}),(0,a.default)(n,t,o).then(function(){i.doUploadSuccess(e,o)},function(t){i.doUploadFail(o,e,t)}).catch(function(t){i.doUploadFail(o,e,t)}))})}},{key:"initTimeLock",value:function(){var t=this;this.failTimer||(this.failTimer=setTimeout(function(){t.failTime=0,t.failTimer=null},6e4))}},{key:"doUploadFail",value:function(t,e,n){this.failTime++,e.forEach(function(t){t.sendState="RESEND"}),l.webPrint.initLogLevel(t),l.webPrint.error("LTS.0300|Request failed，response code=".concat(n.status||"--","，error=").concat(n.statusText||"send fail","."))}},{key:"doUploadSuccess",value:function(t,e){this.failTime=0,t.forEach(function(t){t.sendState="SUCCESS"}),l.webPrint.initLogLevel(e),l.webPrint.log("senddata success.")}},{key:"logUploadPolicyThreshold",value:function(t,e){t.cacheThreshold&&e.filter(function(t){return"READY"===t.sendState||"RESEND"===t.sendState}).length>=t.cacheThreshold&&this.uploadData(t,e)}},{key:"writeLog",value:function(e,n,t,r){var o=this;t&&(0,u.checkLabels)(r)&&(Array.isArray(t)?t.forEach(function(t){o.addLogToList(e,n,t,r)}):this.addLogToList(e,n,t,r))}},{key:"addLogToList",value:function(t,e,n,r){(0,u.checkObjectTypeAndLength)(n,"content",300)?(r={labels:JSON.stringify(r,this.censor),content:this.formatLogContent(n),sendState:"READY",log_time_ns:(new Date).getTime()},e.push(r),this.logUploadPolicyThreshold(t,e)):(l.webPrint.initLogLevel(t),l.webPrint.warn("LTS.0002|Log content is invalid."))}},{key:"formatLogContent",value:function(t){var e=JSON.stringify(t,this.censor);return 30720<(null==e?void 0:e.length)?{content:e.substring(0,30720)}:t}}])&&o(e.prototype,n),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();e.default=new n},function(t,e,n){"use strict";n.r(e),n.d(e,"__extends",function(){return o}),n.d(e,"__assign",function(){return i}),n.d(e,"__rest",function(){return a}),n.d(e,"__decorate",function(){return c}),n.d(e,"__param",function(){return u}),n.d(e,"__metadata",function(){return l}),n.d(e,"__awaiter",function(){return f}),n.d(e,"__generator",function(){return s}),n.d(e,"__createBinding",function(){return d}),n.d(e,"__exportStar",function(){return h}),n.d(e,"__values",function(){return p}),n.d(e,"__read",function(){return y}),n.d(e,"__spread",function(){return b}),n.d(e,"__spreadArrays",function(){return g}),n.d(e,"__await",function(){return v}),n.d(e,"__asyncGenerator",function(){return m}),n.d(e,"__asyncDelegator",function(){return w}),n.d(e,"__asyncValues",function(){return S}),n.d(e,"__makeTemplateObject",function(){return L}),n.d(e,"__importStar",function(){return O}),n.d(e,"__importDefault",function(){return j}),n.d(e,"__classPrivateFieldGet",function(){return P}),n.d(e,"__classPrivateFieldSet",function(){return _});
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])}))(t,e)};function o(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var i=function(){return(i=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};function a(t,e){var n={};for(o in t)Object.prototype.hasOwnProperty.call(t,o)&&e.indexOf(o)<0&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(t);r<o.length;r++)e.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(n[o[r]]=t[o[r]]);return n}function c(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;0<=c;c--)(o=t[c])&&(a=(i<3?o(a):3<i?o(e,n,a):o(e,n))||a);return 3<i&&a&&Object.defineProperty(e,n,a),a}function u(n,r){return function(t,e){r(t,e,n)}}function l(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function f(t,a,c,u){return new(c=c||Promise)(function(n,e){function r(t){try{i(u.next(t))}catch(t){e(t)}}function o(t){try{i(u.throw(t))}catch(t){e(t)}}function i(t){var e;t.done?n(t.value):((e=t.value)instanceof c?e:new c(function(t){t(e)})).then(r,o)}i((u=u.apply(t,a||[])).next())})}function s(r,o){var i,a,c,u={label:0,sent:function(){if(1&c[0])throw c[1];return c[1]},trys:[],ops:[]},t={next:e(0),throw:e(1),return:e(2)};return"function"==typeof Symbol&&(t[Symbol.iterator]=function(){return this}),t;function e(n){return function(t){var e=[n,t];if(i)throw new TypeError("Generator is already executing.");for(;u;)try{if(i=1,a&&(c=2&e[0]?a.return:e[0]?a.throw||((c=a.return)&&c.call(a),0):a.next)&&!(c=c.call(a,e[1])).done)return c;switch(a=0,(e=c?[2&e[0],c.value]:e)[0]){case 0:case 1:c=e;break;case 4:return u.label++,{value:e[1],done:!1};case 5:u.label++,a=e[1],e=[0];continue;case 7:e=u.ops.pop(),u.trys.pop();continue;default:if(!(c=0<(c=u.trys).length&&c[c.length-1])&&(6===e[0]||2===e[0])){u=0;continue}if(3===e[0]&&(!c||e[1]>c[0]&&e[1]<c[3]))u.label=e[1];else if(6===e[0]&&u.label<c[1])u.label=c[1],c=e;else{if(!(c&&u.label<c[2])){c[2]&&u.ops.pop(),u.trys.pop();continue}u.label=c[2],u.ops.push(e)}}e=o.call(r,u)}catch(t){e=[6,t],a=0}finally{i=c=0}if(5&e[0])throw e[1];return{value:e[0]?e[1]:void 0,done:!0}}}}function d(t,e,n,r){t[r=void 0===r?n:r]=e[n]}function h(t,e){for(var n in t)"default"===n||e.hasOwnProperty(n)||(e[n]=t[n])}function p(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return{value:(t=t&&r>=t.length?void 0:t)&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function y(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function b(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(y(arguments[e]));return t}function g(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;for(var r=Array(t),o=0,e=0;e<n;e++)for(var i=arguments[e],a=0,c=i.length;a<c;a++,o++)r[o]=i[a];return r}function v(t){return this instanceof v?(this.v=t,this):new v(t)}function m(t,e,n){var o,i,a;if(Symbol.asyncIterator)return o=n.apply(t,e||[]),i=[],a={},r("next"),r("throw"),r("return"),a[Symbol.asyncIterator]=function(){return this},a;throw new TypeError("Symbol.asyncIterator is not defined.");function r(r){o[r]&&(a[r]=function(n){return new Promise(function(t,e){1<i.push([r,n,t,e])||c(r,n)})})}function c(t,e){try{(n=o[t](e)).value instanceof v?Promise.resolve(n.value.v).then(u,l):f(i[0][2],n)}catch(t){f(i[0][3],t)}var n}function u(t){c("next",t)}function l(t){c("throw",t)}function f(t,e){t(e),i.shift(),i.length&&c(i[0][0],i[0][1])}}function w(r){var o,t={};return e("next"),e("throw",function(t){throw t}),e("return"),t[Symbol.iterator]=function(){return this},t;function e(e,n){t[e]=r[e]?function(t){return(o=!o)?{value:v(r[e](t)),done:"return"===e}:n?n(t):t}:n}}function S(a){var t,e;if(Symbol.asyncIterator)return(t=a[Symbol.asyncIterator])?t.call(a):(a=p(a),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);throw new TypeError("Symbol.asyncIterator is not defined.");function n(i){e[i]=a[i]&&function(o){return new Promise(function(t,e){var n,r;o=a[i](o),n=t,t=e,r=o.done,e=o.value,Promise.resolve(e).then(function(t){n({value:t,done:r})},t)})}}}function L(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function O(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e}function j(t){return t&&t.__esModule?t:{default:t}}function P(t,e){if(e.has(t))return e.get(t);throw new TypeError("attempted to get private field on non-instance")}function _(t,e,n){if(e.has(t))return e.set(t,n),n;throw new TypeError("attempted to set private field on non-instance")}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(10);e.default=function(t,e,n){return(new r.default).webRequest(t,e,n)}},function(t,e,n){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===o(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0});var a=n(2),n=function(){function t(){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function")}var e,n,r;return e=t,(n=[{key:"webRequest",value:function(t,e,n){return n.url?(e=(0,a.getHeader)(e),null!==window&&void 0!==window&&window.XMLHttpRequest?this.sendByAjax(t,e,n):"undefined"!=typeof fetch?this.sendByBeacon(t,e,n):Promise.reject(new Error("not supported"))):Promise.reject(new Error("check the config."))}},{key:"sendByBeacon",value:function(t,r,e){var o;return"undefined"!=typeof fetch?(o=null==e?void 0:e.url,new Promise(function(e,n){fetch(o,{method:"POST",body:JSON.stringify(t),headers:r}).then(function(t){200===t.status?e():n(t)}).catch(function(t){n(t)})})):Promise.reject(new Error("missing fetch method"))}},{key:"sendByAjax",value:function(s,d,h){return new Promise(function(t,e){var n=null==h?void 0:h.url;if(window.XMLHttpRequest&&n){var r=new XMLHttpRequest;if(r.timeout=15e3,r.onabort=function(){e(new Error("The request was aborted!"))},r.onerror=function(){e(new Error("An error occurred during the transaction!"))},r.ontimeout=function(){e(new Error("Timeout!"))},r.onload=function(){t()},h&&"get"===String(h.method).toLocaleLowerCase()){r.open("GET",n+"?"+s,!0);for(var o=0,i=Object.keys(d);o<i.length;o++){var a=i[o];r.setRequestHeader(a,d[a])}r.send()}else{var c=h&&h.async;r.open("POST",n,null==c||c);for(var u=0,l=Object.keys(d);u<l.length;u++){var f=l[u];r.setRequestHeader(f,d[f])}r.send(JSON.stringify(s))}}else e(new Error("url is missing"))})}}])&&i(e.prototype,n),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();e.default=n},function(t,e,n){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===o(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0}),e.UUID=void 0;var r=function(){function t(){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function")}var e,n,r;return e=t,(n=[{key:"create",value:function(){var t=Math.floor(Math.random()*(Math.pow(2,12)-1+1))+0,e=Math.floor(Math.random()*(Math.pow(2,32)-1+1))+0,n=Math.floor(Math.random()*(Math.pow(2,16)-1+1))+0,r=Math.floor(Math.random()*(Math.pow(2,6)-1+1))+0,o=Math.floor(Math.random()*(Math.pow(2,8)-1+1))+0,i=(0|Math.random()*(1<<30))+(0|Math.random()*(1<<18))*(1<<30);function a(t,e,n){n=n||"0";for(var r=e-(t=String(t)).length;0<r;r>>>=1,n+=n)1&r&&(t=n+t);return t}return[a(e.toString(16),8),a(n.toString(16),4),a((16384|t).toString(16),4),a((128|r).toString(16),2),a(o.toString(16),2),a(i.toString(16),12)].join("")}}])&&i(e.prototype,n),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();e.UUID=new r},function(t){t.exports=JSON.parse('{"name":"@cloud/lts-web-sdk","version":"1.0.15","description":"lts-web-sdk","main":"./websdk.min.js","license":"MIT","scripts":{"debug":"http-serve -p 8080","build":"rimraf dist && cross-env NODE_ENV=development webpack --config webpack.config.js","build:prod":"rimraf dist && tsc -p tsconfig.json && cross-env NODE_ENV=production webpack --config webpack.config.js","lint":"eslint --ext .js src/","jest":"jest --coverage --watch","fix-lint":"eslint --ext .js src/ --fix","build:baidu":"rimraf lib && npx babel src --out-dir dist/src","build:bd":"rimraf dist && tsc -p tsconfig.json && cross-env NODE_ENV=production webpack --config webpack.config.js","build:cdn":"rimraf dist && tsc -p tsconfig.json && cross-env NODE_ENV=production webpack --config webpack-cdn.config.js","tsc":"tsc -p tsconfig.json","test":"mocha --exit","test-report":"tsc -p tsconfig.json && nyc --reporter=html mocha --exit"},"devDependencies":{"@babel/cli":"^7.4.3","@babel/core":"^7.4.3","@babel/preset-env":"^7.4.3","@cloud/eslint-config-cbc":"^1.7.3","babel-eslint":"^10.1.0","babel-loader":"^8.0.5","babel-plugin-syntax-dynamic-import":"^6.18.0","cross-env":"^5.2.1","eslint":"^6.1.0","http-serve":"^1.0.1","jest":"^27.4.5","mocha":"^10.2.0","nyc":"^15.1.0","sinon":"^15.2.0","uglifyjs-webpack-plugin":"^2.1.2","webpack":"^4.29.6","webpack-cli":"^3.3.0"}}')},function(t,e,n){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===o(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0});var a=n(1),c=n(3),u=n(0),n=function(){function t(){var e=this;if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");this.checkConfig=function(t){return!!t&&e.checkReportPolices(t)&&e.checkLogConfig(t)&&e.checkUrl(t)&&e.checkRegion(t)&&e.checkProjectIdAndDebug(t)&&(0,a.checkConfigKey)(t)},this.checkProjectIdAndDebug=function(t){return null!=t&&t.debug&&"boolean"!=typeof t.debug?(u.webPrint.warn("LTS.0002|Debug value is invalid."),!1):(t=null==t?void 0:t.projectId,!(!(0,a.isValidString)(t,"projectId")||!(0,a.checkLogParamString)("projectId",t,128)))},this.checkRegion=function(t){return!(!(0,a.checkLogParamString)("region",null==t?void 0:t.region,128)||!c.REGIONS[null==t?void 0:t.region]&&(u.webPrint.warn("LTS.0009|Unsupported region."),1))},this.checkUrl=function(t){return!(null!=t&&t.url&&!(0,a.checkLogParamString)("url",null==t?void 0:t.url,128)||null!=t&&t.url&&("string"!=typeof(null==t?void 0:t.url)||!/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]+$/.test(null==t?void 0:t.url))&&(u.webPrint.warn("LTS.0006|Url doesn't match pattern."),1))},this.checkLogConfig=function(t){var e=null==t?void 0:t.groupId;return!!((0,a.isValidString)(e,"groupId")&&(0,a.checkLogParamString)("groupId",e,128)&&(e=null==t?void 0:t.streamId,(0,a.isValidString)(e,"streamId"))&&(0,a.checkLogParamString)("streamId",e,128))}}var e,n,r;return e=t,(n=[{key:"updateUrl",value:function(t){var e=null==t?void 0:t.url,e=(null!=(e=e||"https://lts-access.".concat(t.region,".myhuaweicloud.com"))?e:"").split(",")[0];return"".concat(e,"/v3/").concat(t.projectId,"/lts/groups/").concat(t.groupId,"/streams/").concat(t.streamId,"/logs")}},{key:"checkReportPolices",value:function(t){if(t)return this.checkThreshold(t,"timeThreshold",c.POLICY_CONSTANTS.TIME_MIN,c.POLICY_CONSTANTS.TIME_MAX,"timeInterval")&&this.checkThreshold(t,"cacheThreshold",c.POLICY_CONSTANTS.THRESHOLD_MIN,c.POLICY_CONSTANTS.THRESHOLD_MAX)}},{key:"checkThreshold",value:function(t,e,n,r,o){var i=t[e];if(void 0===i)t[e]=30;else{if("number"!=typeof i)return u.webPrint.warn("LTS.0002|".concat(o||e," is invalid.")),!1;if(i<n||r<i)return u.webPrint.warn("LTS.0004|The value of ".concat(o||e," must be between ").concat(n," and ").concat(r,".")),!1}return!0}}])&&i(e.prototype,n),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();e.default=new n}],r={},o.m=n,o.c=r,o.d=function(t,e,n){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)o.d(n,r,function(t){return e[t]}.bind(null,r));return n},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=4);function o(t){var e;return(r[t]||(e=r[t]={i:t,l:!1,exports:{}},n[t].call(e.exports,e,e.exports,o),e.l=!0,e)).exports}var n,r});
!function(e,t){void 0===e.csdn&&(e.csdn={}),e.csdn.reportCryptoJS=function(){var e=e||function(e,t){var o;if("undefined"!=typeof window&&window.crypto&&(o=window.crypto),"undefined"!=typeof self&&self.crypto&&(o=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(o=globalThis.crypto),!o&&"undefined"!=typeof window&&window.msCrypto&&(o=window.msCrypto),!o&&"undefined"!=typeof global&&global.crypto&&(o=global.crypto),!o&&"function"==typeof require)try{o=require("crypto")}catch(e){}var r=function(){if(o){if("function"==typeof o.getRandomValues)try{return o.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof o.randomBytes)try{return o.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},n=Object.create||function(){function e(){}return function(t){var o;return e.prototype=t,o=new e,e.prototype=null,o}}(),i={},a=i.lib={},s=a.Base=function(){return{extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),c=a.WordArray=s.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,o=e.words,r=this.sigBytes,n=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<n;i++){var a=o[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=a<<24-(r+i)%4*8}else for(var s=0;s<n;s+=4)t[r+s>>>2]=o[s>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,o=this.sigBytes;t[o>>>2]&=4294967295<<32-o%4*8,t.length=e.ceil(o/4)},clone:function(){var e=s.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],o=0;o<e;o+=4)t.push(r());return new c.init(t,e)}}),d=i.enc={},u=d.Hex={stringify:function(e){for(var t=e.words,o=e.sigBytes,r=[],n=0;n<o;n++){var i=t[n>>>2]>>>24-n%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,o=[],r=0;r<t;r+=2)o[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new c.init(o,t/2)}},f=d.Latin1={stringify:function(e){for(var t=e.words,o=e.sigBytes,r=[],n=0;n<o;n++){var i=t[n>>>2]>>>24-n%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,o=[],r=0;r<t;r++)o[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new c.init(o,t)}},l=d.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},p=a.BufferedBlockAlgorithm=s.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var o,r=this._data,n=r.words,i=r.sigBytes,a=this.blockSize,s=4*a,d=i/s;d=t?e.ceil(d):e.max((0|d)-this._minBufferSize,0);var u=d*a,f=e.min(4*u,i);if(u){for(var l=0;l<u;l+=a)this._doProcessBlock(n,l);o=n.splice(0,u),r.sigBytes-=f}return new c.init(o,f)},clone:function(){var e=s.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),g=(a.Hasher=p.extend({cfg:s.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,o){return new e.init(o).finalize(t)}},_createHmacHelper:function(e){return function(t,o){return new g.HMAC.init(e,o).finalize(t)}}}),i.algo={});return i}(Math);return e}()}(window),function(e,t){!function(e){(function(t){function o(e,t,o,r,n,i,a){var s=e+(t&o|~t&r)+n+a;return(s<<i|s>>>32-i)+t}function r(e,t,o,r,n,i,a){var s=e+(t&r|o&~r)+n+a;return(s<<i|s>>>32-i)+t}function n(e,t,o,r,n,i,a){var s=e+(t^o^r)+n+a;return(s<<i|s>>>32-i)+t}function i(e,t,o,r,n,i,a){var s=e+(o^(t|~r))+n+a;return(s<<i|s>>>32-i)+t}var a=e,s=a.lib,c=s.WordArray,d=s.Hasher,u=a.algo,f=[];!function(){for(var e=0;e<64;e++)f[e]=4294967296*t.abs(t.sin(e+1))|0}();var l=u.MD5=d.extend({_doReset:function(){this._hash=new c.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var a=0;a<16;a++){var s=t+a,c=e[s];e[s]=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8)}var d=this._hash.words,u=e[t+0],l=e[t+1],p=e[t+2],g=e[t+3],h=e[t+4],m=e[t+5],w=e[t+6],_=e[t+7],v=e[t+8],C=e[t+9],x=e[t+10],y=e[t+11],k=e[t+12],S=e[t+13],I=e[t+14],E=e[t+15],O=d[0],b=d[1],T=d[2],L=d[3];O=o(O,b,T,L,u,7,f[0]),L=o(L,O,b,T,l,12,f[1]),T=o(T,L,O,b,p,17,f[2]),b=o(b,T,L,O,g,22,f[3]),O=o(O,b,T,L,h,7,f[4]),L=o(L,O,b,T,m,12,f[5]),T=o(T,L,O,b,w,17,f[6]),b=o(b,T,L,O,_,22,f[7]),O=o(O,b,T,L,v,7,f[8]),L=o(L,O,b,T,C,12,f[9]),T=o(T,L,O,b,x,17,f[10]),b=o(b,T,L,O,y,22,f[11]),O=o(O,b,T,L,k,7,f[12]),L=o(L,O,b,T,S,12,f[13]),T=o(T,L,O,b,I,17,f[14]),b=o(b,T,L,O,E,22,f[15]),O=r(O,b,T,L,l,5,f[16]),L=r(L,O,b,T,w,9,f[17]),T=r(T,L,O,b,y,14,f[18]),b=r(b,T,L,O,u,20,f[19]),O=r(O,b,T,L,m,5,f[20]),L=r(L,O,b,T,x,9,f[21]),T=r(T,L,O,b,E,14,f[22]),b=r(b,T,L,O,h,20,f[23]),O=r(O,b,T,L,C,5,f[24]),L=r(L,O,b,T,I,9,f[25]),T=r(T,L,O,b,g,14,f[26]),b=r(b,T,L,O,v,20,f[27]),O=r(O,b,T,L,S,5,f[28]),L=r(L,O,b,T,p,9,f[29]),T=r(T,L,O,b,_,14,f[30]),b=r(b,T,L,O,k,20,f[31]),O=n(O,b,T,L,m,4,f[32]),L=n(L,O,b,T,v,11,f[33]),T=n(T,L,O,b,y,16,f[34]),b=n(b,T,L,O,I,23,f[35]),O=n(O,b,T,L,l,4,f[36]),L=n(L,O,b,T,h,11,f[37]),T=n(T,L,O,b,_,16,f[38]),b=n(b,T,L,O,x,23,f[39]),O=n(O,b,T,L,S,4,f[40]),L=n(L,O,b,T,u,11,f[41]),T=n(T,L,O,b,g,16,f[42]),b=n(b,T,L,O,w,23,f[43]),O=n(O,b,T,L,C,4,f[44]),L=n(L,O,b,T,k,11,f[45]),T=n(T,L,O,b,E,16,f[46]),b=n(b,T,L,O,p,23,f[47]),O=i(O,b,T,L,u,6,f[48]),L=i(L,O,b,T,_,10,f[49]),T=i(T,L,O,b,I,15,f[50]),b=i(b,T,L,O,m,21,f[51]),O=i(O,b,T,L,k,6,f[52]),L=i(L,O,b,T,g,10,f[53]),T=i(T,L,O,b,x,15,f[54]),b=i(b,T,L,O,l,21,f[55]),O=i(O,b,T,L,v,6,f[56]),L=i(L,O,b,T,E,10,f[57]),T=i(T,L,O,b,w,15,f[58]),b=i(b,T,L,O,S,21,f[59]),O=i(O,b,T,L,h,6,f[60]),L=i(L,O,b,T,y,10,f[61]),T=i(T,L,O,b,p,15,f[62]),b=i(b,T,L,O,C,21,f[63]),d[0]=d[0]+O|0,d[1]=d[1]+b|0,d[2]=d[2]+T|0,d[3]=d[3]+L|0},_doFinalize:function(){var e=this._data,o=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;o[n>>>5]|=128<<24-n%32;var i=t.floor(r/4294967296),a=r;o[15+(n+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),o[14+(n+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),e.sigBytes=4*(o.length+1),this._process();for(var s=this._hash,c=s.words,d=0;d<4;d++){var u=c[d];c[d]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return s},clone:function(){var e=d.clone.call(this);return e._hash=this._hash.clone(),e}});a.MD5=d._createHelper(l),a.HmacMD5=d._createHmacHelper(l)})(Math),e.MD5}(e.csdn.reportCryptoJS)}(window),function(e){var t,o,r,n,i,a,s,c,d,u,f,l,p,g,h;i=[],a=[],t={DELAY:500,DELAY_LTS_VIEW:200,API_VERSION:"0.6.0",SERVER_URL:"https://event.csdn.net/"},l=["utm_source"],t.SERVER_URL,t.SERVER_URL,t.API_VERSION,t.SERVER_URL,t.API_VERSION,t.SERVER_URL,n={SCROLL:"scroll",PV:"pv",VIEW:"view",DELAY_VIEW:"delay_view",CLICK:"click"},c={SKIPPED_AND_VISIBLE:"0",VISIBLE:"1"};var m={region:"cn-north-4",projectId:"06981375190026432f77c01bfca33e32",groupId:"dadde766-b087-42da-8e67-d2499a520ee7",tags:{userAgent:window.navigator.userAgent}},w={region:m.region,projectId:m.projectId,groupId:m.groupId,url:"https://eva2.csdn.net",debug:!1,logLevel:"ERROR",cacheThreshold:30,timeThreshold:2},_={PV:"5da0dae3-481b-478b-92ca-3caa293e4a29",CLICK:"099503df-60e8-4167-9a96-d6fb66f3fd01",VIEW:"ea448b34-0491-4546-87aa-059120b54c53",MOUSE:"a0119567-bf91-4314-ab75-f683ba6c0c0a"},v={PV:Object.assign({},m,{streamId:_.PV}),CLICK:Object.assign({},m,{streamId:_.CLICK}),VIEW:Object.assign({},m,{streamId:_.VIEW}),MOUSE:Object.assign({},m,{streamId:_.MOUSE})},C={PV:new LTS_WEB_SDK(Object.assign({},w,{streamId:_.PV})),CLICK:new LTS_WEB_SDK(Object.assign({},w,{streamId:_.CLICK})),VIEW:new LTS_WEB_SDK(Object.assign({},w,{streamId:_.VIEW})),MOUSE:new LTS_WEB_SDK(Object.assign({},w,{streamId:_.MOUSE}))};if(r={isCSDNApp:function(){var e=!!window.navigator.userAgent.toLowerCase().match(/(csdn)/i),t=r.getCookie("X-App-ID")||"";return e||"CSDN-APP"==t||"CSDN-EDU"==t},isMobile:function(){var e=/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone|csdn)/i.test(navigator.userAgent);return/(MicroMessenger)/i.test(navigator.userAgent)?!/(WindowsWechat|MacWechat)/i.test(navigator.userAgent):e},guid:function(){return+new Date+"-xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})},setLogIdCookie:function(e){if(-1!==["pv","click","view"].indexOf(e)){var t="log_Id_"+e,o=r.getCookie(t)||0;try{o=parseInt(o),"number"==typeof o&&isNaN(o)||o>=1e8?r.setCookie(t,1,18e5):r.setCookie(t,++o,18e5)}catch(e){void 0}}},getRequest:function(){for(var e=new Object,t=window.location.href.split("?")[1]||"",o=t.split("&"),r=0;r<o.length;r++){var n=o[r].split("=")[0],i=o[r].split("=")[1];n&&i&&(e[n]=unescape(i))}return e},initUTM:function(){f={};var e=r.getRequest(),t=r.isCSDNApp();if("{}"!==JSON.stringify(e)){for(var o in e)0==o.indexOf("utm_")&&e.hasOwnProperty(o)&&(void 0,0==o.indexOf("utm_source")?(r.setCookie("c_"+o,e[o],864e5),t&&r.setCookie(o+"_app",e[o],864e5)):(r.setCookie("c_"+o,e[o],36e5),t&&r.setCookie(o+"_app",e[o],36e5)));for(var o in l)if(l.hasOwnProperty(o)){var n=l[o],i=e[l[o]];i?(r.setCookie(n,i,36e5),f[n]=i):f[n]=""}}else for(var o in l)if(l.hasOwnProperty(o)){var n=l[o],i=r.getCookie(n);f[n]=i}return f},initTraceInfo:function(){for(var e=["blog","bbs","download","ask","edu","biwen"],t=0;t<e.length;t++)window.location.host.indexOf(e[t]+".csdn.net")>-1&&(r.setCookie("c_page_id","",-1),r.setCookie("c_mod","",-1))},preserveTraceInfo:function(e){e.mod&&r.setCookie("c_mod",e.mod,36e5),e.page_id?r.setCookie("c_page_id",e.page_id,36e5):r.setCookie("c_page_id","default",36e5)},getTimestamp:function(){return Math.round(new Date/1e3)},getXPath:function(e){if(""!==e.id)return'//*[@id="'+e.id+'"]';if(e==document.body)return"/html/"+e.tagName.toLowerCase();if(!e.parentNode)return"";for(var t=1,o=e.parentNode.childNodes,r=0,n=o.length;r<n;r++){var i=o[r];if(i==e)return arguments.callee(e.parentNode)+"/"+e.tagName.toLowerCase()+"["+t+"]";1==i.nodeType&&i.tagName==e.tagName&&t++}},getScreen:function(){return window.screen.width+"*"+window.screen.height},getPixel:function(){return window.screen.width*window.devicePixelRatio+"*"+window.screen.height*window.devicePixelRatio},getCookie:function(e){var t,o=new RegExp("(^| )"+e+"=([^;]*)(;|$)");return(t=document.cookie.match(o))?unescape(t[2]):""},getFuzzyCookie:function(e){var t,o=new RegExp(e+"[A-Za-z0-9_]+=([^;]*);","ig");return(t=document.cookie.match(o))?t.join(""):""},checkoutUtm:function(){var e=[],t=[],o=window.location.href.split("?")[1]||"";if(o.length){e=o.split("&");for(var r=0;r<e.length;r++)0==e[r].indexOf("utm_")&&t.push(e[r].split("=")[0])}return t},setCookie:function(e,t,o){var r=new Date;r.setTime(r.getTime()+o);var n=window.location.host,i=n.indexOf(".csdn.net")>0?"csdn.net":this.topDomain(n);document.cookie=e+"="+escape(t)+";expires="+r.toGMTString()+";path=/ ; domain=."+i},setUserSegment:function(){var e=(null!=(_ref1=/(; )?(uuid_tt_dd|_javaeye_cookie_id_)=([^;]+)/.exec(window.document.cookie))?_ref1[3]:void 0)||"",t=e?e.substring(e.length-6)%16:0;r.setCookie("c_segment",t)},setfirstPageInfo:function(){if(r.getCookie("c_first_ref")&&r.getCookie("c_first_ref").indexOf(".csdn.net")>-1)return void r.setCookie("c_first_ref","default");var e=new RegExp(/[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/),t=window.document.referrer?window.document.referrer.match(e)[0]:"default";[".csdn.net","wx.tenpay.com","graph.qq.com","openapi.baidu.com","api.weibo.com","account.dcloud.net.cn/oauth","github.com/login/oauth","passport.gitcode.net"].some(function(e){return e.indexOf("/")>-1?(window.document.referrer||"default").indexOf(e)>-1:t.indexOf(e)>-1})&&(t="default");var o="11_"+(new Date).getTime()+"."+r.randomNum(6);return"default"!=t?(r.setCookie("c_first_ref",t),r.setCookie("c_first_page",window.location.href),void r.setCookie("c_dsid",o,18e5)):r.getCookie("c_first_ref")&&window.document.referrer?void 0:(r.setCookie("c_first_ref","default"),r.setCookie("c_first_page",window.location.href),void r.setCookie("c_dsid",o,18e5))},randomNum:function(e){for(var t=[],o=["0","1","2","3","4","5","6","7","8","9"],r=0;r<e;r++){var n=Math.floor(10*Math.random());t[r]=o[n]}return t.join("")},initDefaultCookie:function(){var e,t=(null!=(e=/(; )?(uuid_tt_dd|_javaeye_cookie_id_)=([^;]+)/.exec(window.document.cookie))?e[3]:void 0)||"",o=r.getCookie("dc_session_id"),n=r.getCookie("c_dsid"),i=r.getCookie("fid");i||(i="20_"+r.randomNum(11)+"-"+(new Date).getTime()+"-"+r.randomNum(6),r.setCookie("fid",i,15768e7)),t||(t="11_"+r.randomNum(11)+"-"+(new Date).getTime()+"-"+r.randomNum(6),r.setCookie("uuid_tt_dd",t,15768e7)),o||(o="11_"+(new Date).getTime()+"."+r.randomNum(6),r.setCookie("dc_session_id",o,18e5)),n||(n="11_"+(new Date).getTime()+"."+r.randomNum(6),r.setCookie("c_dsid",n,18e5))},refreshDcSessionId:function(){var e=r.getCookie("dc_session_id");e&&r.setCookie("dc_session_id",e,18e5);var t=r.getCookie("c_dsid");t&&r.setCookie("c_dsid",t,18e5);const o=r.getCookie("log_Id_"+n.PV),i=r.getCookie("log_Id_"+n.VIEW),a=r.getCookie("log_Id_"+n.CLICK);return o&&r.setCookie("log_Id_"+n.PV,o,18e5),i&&r.setCookie("log_Id_"+n.VIEW,i,18e5),a&&r.setCookie("log_Id_"+n.CLICK,a,18e5),e},cCookieAppSuffix:function(){var e=r.getFuzzyCookie("c_dl"),t=r.isCSDNApp();if(t){!window.document.referrer&&r.getCookie("utm_medium_app")&&r.setCookie("c_utm_medium",r.getCookie("utm_medium_app"),36e5)}if(t&&e)try{for(var o=e.split(";").map(function(e){return e?e.split("=")[0]:""}).filter(function(e){return e}),n=0;n<o.length;){var i=o[n];(function(e,t){var o=e.length;return e.substring(o-t.length,o)===t})(i,"_app")||r.setCookie(i+"_app",r.getCookie(i),2592e6),n++}}catch(e){void 0}},analyticsWriteLog:function(e,t,o){if(e){if("PV"!==e){if(/spider|bot|crawler|scrapy|dnspod|ia_archiver|jiankongbao|slurp|transcoder|networkbench|oneapm|bophantomjst|bingpreview|headlesschrome/.test(window.navigator.userAgent.toLowerCase()))return}for(var r=o||["cid","uid","sid","dc_sid","did","utm","platform","un","fid"],n={},i=JSON.parse(JSON.stringify(t)),a=0;a<r.length;a++)Object.prototype.hasOwnProperty.call(t,r[a])&&(n[r[a]]=t[r[a]],delete i[r[a]]);var s={tags:Object.assign({},v[e].tags,n),data:i};"VIEW"===e?C[e]&&C[e].report(Object.assign({},s.data,s.tags)):C[e]&&C[e].reportImmediately(Object.assign({},s.data,s.tags))}},initData:function(){r.initDefaultCookie(),r.setfirstPageInfo(),r.initTraceInfo(),r.setUserSegment();var t,o,n,i=(null!=(t=/(; )?(uuid_tt_dd|_javaeye_cookie_id_)=([^;]+)/.exec(window.document.cookie))?t[3]:void 0)||"",a=r.refreshDcSessionId(),u=r.getCookie("fid");if(s={cid:i,fid:u,sid:a||"",pid:window.location.host.split(".csdn.net")[0],uid:r.getCookie("UserName"),did:r.getCookie("X-Device-ID")||i||"",dc_sid:r.getCookie("dc_sid"),ref:window.document.referrer||("(null)"===r.getCookie("refer_app")?"":r.getCookie("refer_app")),curl:window.location.href,dest:"",cfg:{viewStrategy:c.VISIBLE}},n=r.initUTM(),r.cCookieAppSuffix(),e("meta[name=report]").attr("content"))try{o=JSON.parse(e("meta[name=report]").attr("content")),d=e.extend(!0,{},o);for(var f=Object.prototype.hasOwnProperty,l=["percent"],p=0;p<l.length;p++)f.call(o,l[p])&&delete o[l[p]]}catch(e){o={},d={},void 0}else o={};if(o.extra){var g=r.parseExtra(o);g&&(o.extra=g)}return s=e.extend({},s,{utm:n.utm_source},o),r.preserveTraceInfo(s),s},tos:function(){var e,t,o,r;e=+new Date/1e3|0,o=null!=(t=/\bdc_tos=([^;]*)(?:$|;)/.exec(document.cookie))?t[1]:void 0;try{r=e-parseInt(o,36)}catch(e){void 0,r=-1}return document.cookie="dc_tos="+e.toString(36)+" ; expires="+new Date(1e3*(e+14400)).toGMTString()+" ; max-age=14400 ; path=/ ; domain=."+this.topDomain(window.location.host),r},topDomain:function(e){return e.split(".").length>3?e.match(/^(?:https?:\/\/)?(?:[^@\n]+@)?(?:www\.)?([^:\/\n]+)/im)[1].split(".").slice(-3).join("."):/\.?([a-z0-9\-]+\.[a-z0-9\-]+)(:\d+)?$/.exec(e)[1]},copyArr:function(e){for(var t=[],o=0;o<e.length;o++)t.push(e[o]);return t},isView:function(e,t){var o=this;if(!e)return!1;var r=this.getElementBottom(e),n=r+e.offsetHeight;return c.VISIBLE==t?o.scrollTop()<r&&r<o.scrollTop()+o.windowHeight()||o.scrollTop()<n&&n<o.scrollTop()+o.windowHeight():c.SKIPPED_AND_VISIBLE==t?r<=o.scrollTop()+o.windowHeight()||(o.scrollTop()<r&&r<o.scrollTop()+o.windowHeight()||o.scrollTop()<n&&n<o.scrollTop()+o.windowHeight()):void 0},scrollTop:function(){return Math.max(document.body.scrollTop,document.documentElement.scrollTop)},windowHeight:function(){return"CSS1Compat"==document.compatMode?document.documentElement.clientHeight:document.body.clientHeight},getElementTop:function(t){if("undefined"!=typeof jQuery)return e(t).offset().top;var o=t.offsetTop;for(t=t.offsetParent;null!=t;)o+=t.offsetTop,t=t.offsetParent;return o},getElementBottom:function(t){if("undefined"!=typeof jQuery)return e(t).offset().top+e(t).height();var o=t.offsetTop;for(t=t.offsetParent;null!=t;)o+=t.offsetTop,t=t.offsetParent;return o},url2Obj:function(e){var t={},o=e.split("&");for(var r in o)t.hasOwnProperty(r)&&(t[o[r].split("=")[0]]=decodeURIComponent(o[r].split("=")[1]));return t},fixParamConTop:function(t,o){return t.con.split(",top_")>-1?t:(t.con=t.con+",top_"+e(o).offset().top,t)},urlParamsToObj:function(e){var t={};return e.replace(/([^=&#]+)=([^&#]*)/g,function(){t[arguments[1]]=arguments[2]}),t},objToUrlParams:function(t){var o="";return e.each(t,function(e){o+="&"+e+"="+t[e]}),o.substr(1)},trackOrderSource:function(){var e=document.referrer;if(e){var t=document.createElement("a");t.href=e;var o=["passport","order.csdn.net","wx.tenpay.com","cart.csdn.net"],n=[/(^https:\/\/mall\.csdn\.net(:[0-9]{1,5})?\/cart$)/],i=!1;try{for(var a=0;a<o.length;a++)t.hostname.indexOf(o[a])>-1&&(i=!0);for(var a=0;a<n.length;a++)n[a].test(e)&&(i=!0)}catch(e){i=!1}if(!i){if(r.getCookie("c_ref")===e)return;t.hostname.indexOf(".csdn.net")>-1?(r.setCookie("c_pref",r.getCookie("c_ref")),r.setCookie("c_ref",e)):(r.setCookie("c_pref",r.getCookie("c_ref")),r.setCookie("c_ref",e.split("?")[0]))}}else window.navigator.userAgent.toLowerCase().indexOf("csdn")<0&&(r.setCookie("c_pref","default"),r.setCookie("c_ref","default"))},parseExtra:function(e){if(!Object.prototype.hasOwnProperty.call(e,"extra"))return"";var t=Object.prototype.toString.call(e.extra).slice(8,-1);if("Object"===t)return e.extra;if("String"!==t||!e.extra)return{};try{return JSON.parse(e.extra)}catch(t){void 0}},assignExtra:function(t){if(t&&(t.extra||s.extra)){var o=r.parseExtra(t);return o=e.extend(!0,{},s.extra||{},o||{}),JSON.stringify(o)}},getSortObjectMD5:function(e){var o=JSON.parse(JSON.stringify(e));if("Object"!==Object.prototype.toString.call(o).slice(8,-1))return"";try{o.APIVersion=t.API_VERSION;var r=[];for(var n in o)o.hasOwnProperty(n)&&r.push(n);r=r.sort();var i=r.reduce(function(e,t){return e+="&"+t+"="+o[t]},"").substring(1);return csdn.reportCryptoJS.MD5(i).toString()}catch(e){return void 0,""}},reportScroll:function(t){var o=e.extend(!0,{},s,t);try{var n=r.assignExtra(t||{});n&&(o.extra=n)}catch(e){t&&t.extra&&(o.extra=t.extra),void 0}o.tos=r.tos()+"",o.adb=(r.getCookie("c_adb")||0)+"",o.curl=window.location.href;var i=r.getFuzzyCookie("c_");i&&(o.cCookie=i),o.t=r.getTimestamp()+"",o.screen=r.getScreen(),o.un=r.getCookie("UN")||r.getCookie("UserName"),o.urn=p||r.guid(),o.vType=r.getCookie("p_uid")||"",o.hca=r.getCookie("HMACCOUNT"),Object.prototype.hasOwnProperty.call(o,"eleTop")&&(o.eleTop=o.eleTop+""),delete o.cfg,delete o.dest;var a={__source__:"csdn",__logs__:[o]},c=window.navigator.userAgent,d="PC";c.toLowerCase().indexOf("csdnedu")>-1?d="CSDNEDU":c.toLowerCase().indexOf("csdnapp")>-1?d="CSDNApp":c.toLowerCase().indexOf("mobile")>-1&&(d="mobile"),a.__tags__={useragent:c,platform:d}},windowFocusChange:function(e){"loading"!==document.readyState?g=document.hasFocus():window.addEventListener("DOMContentLoaded",function(){g=document.hasFocus(),e()},!0);var t=function(o){o.target===window&&(g=!0,e()),window.removeEventListener("focus",t,!0)};return window.addEventListener("focus",t,!0),g&&e(),g},throttleTrailing:function(e,t){var o=0,r=null;return function(){var n=Date.now(),i=this,a=arguments;n-o>t?(e.apply(i,a),o=n,r=null):(r&&(clearTimeout(r),r=null),r=setTimeout(function(){var n=Date.now();n-o>t&&(e.apply(i,a),o=n,r=null)},t-(n-o)))}},mousePositionChange:function(){var t={x:0,y:0,ev:"move"},o={x:0,y:0,ev:"click"},i=function(t){var o=e.extend(!0,{},s,t);try{var i=r.assignExtra(t||{});i&&(o.extra=i)}catch(e){t&&t.extra&&(o.extra=t.extra)}o.tos=r.tos(),o.adb=r.getCookie("c_adb")||0,o.curl=window.location.href;var a=r.getFuzzyCookie("c_");a&&(o.cCookie=a),o.t=r.getTimestamp(),o.screen=r.getScreen(),o.un=r.getCookie("UN")||r.getCookie("UserName"),o.urn=p,o.vType=r.getCookie("p_uid")||"",delete o.cfg,delete o.dest,o.log_id=r.getCookie("log_Id_"+n.PV),o.sign=r.getSortObjectMD5(o),o.hca=r.getCookie("HMACCOUNT"),r.analyticsWriteLog("MOUSE",o)};r.isMobile()?e(document).on("touchmove",r.throttleTrailing(function(e){var o=~~e.originalEvent.changedTouches[0].clientX,n=~~e.originalEvent.changedTouches[0].clientY;o===t.x&&n===t.y||(t.x=o,t.y=n,i({extra:JSON.stringify(t)}),r.refreshDcSessionId())},1e4)):e(document).on("mousemove",r.throttleTrailing(function(e){e.clientX===t.x&&e.clientY===t.y||(t.x=e.clientX,t.y=e.clientY,i({extra:JSON.stringify(t)}),r.refreshDcSessionId())},1e4)),e(document).on("click",r.throttleTrailing(function(e){e.clientX===o.x&&e.clientY===o.y||(o.x=e.clientX,o.y=e.clientY,i({extra:JSON.stringify(o)}),r.refreshDcSessionId())},1e4))}},o={timer:0,timerLts:0,checkTimer:0,getFullSpm:function(e){var t=e.split(".").length;if(2===t||3===t){var o=document.querySelector('meta[name="report"]'),r=o&&o.getAttribute("content")||"{}",n=JSON.parse(r);return n.spm?n.spm+"."+e:e}return e},reportUserAction:function(t,o){var n=this;t=t||["1px"],h=!1,r.windowFocusChange(function(){g&&!h&&(r.reportScroll({eleTop:1}),h=!0)}),e(function(){setTimeout(function(){n.reportPercent(function(e){h||(r.reportScroll({eleTop:e}),h=!0)},{scrollBar:!0,range:t,el:o||"",scope:"tenPrecentScrollEvent"})},800)})},reportPercent:function(t,o){o=o||{};for(var n=o.range||[25,50,75,100],i={},a=0;a<n.length;a++)i[n[a]]=!1;o.scope=o.scope||"precentScrollEvent",!o.el&&window[o.scope]&&e(o.el||window).off("scroll",window[o.scope]),window[o.scope]=function(){var a,s=0,c=0,d=!!o.scrollBar||!1;if(o.el){var u=e(o.el);u.length&&(a=d?u.scrollTop():u.scrollTop()+u.height(),s=a/u[0].scrollHeight*100)}else a=d?r.scrollTop():r.scrollTop()+r.windowHeight(),s=a/document.documentElement.scrollHeight*100;for(var f=0;f<n.length;f++){var l=n[f],p=s;if("string"==typeof l&&l.indexOf("px")>-1&&(l=+l.replace("px",""),p=a),p>=l){if(f===n.length-1){c=l;break}if(p<("string"==typeof n[f+1]&&n[f+1].indexOf("px")>-1?+n[f+1].replace("px",""):n[f+1])){c=l;break}}}if(c)for(var g=0;g<n.length;g++){var h=n[g],m="string"==typeof h&&h.indexOf("px")>-1?+h.replace("px",""):h;m<=c&&!i[h]&&(i[h]=!0,t&&t(m))}n.every(function(e){return i[e]})&&e(o.el||window).off("scroll",window[o.scope])&&delete window[o.scope]},window[o.scope](),e(o.el||window).on("scroll",window[o.scope])},reportServerLts:function(e,o){r.refreshDcSessionId();var i=this;if(e&&o){if(r.setLogIdCookie(n.VIEW),e===n.VIEW||e===n.DELAY_VIEW){var s=window.navigator.userAgent,c="PC";s.toLowerCase().indexOf("csdnedu")>-1?c="CSDNEDU":s.toLowerCase().indexOf("csdnapp")>-1?c="CSDNApp":s.toLowerCase().indexOf("mobile")>-1&&(c="mobile"),o.platform=c,o.log_id=r.getCookie("log_Id_"+n.VIEW)}var d=function(){if(a.length){for(var e=0;e<a.length;e++)r.analyticsWriteLog("VIEW",a[e]);a=[]}};"COMPLETE"===i.timerLts?(r.analyticsWriteLog("VIEW",o),d()):a.push(o),i.timerLts||(i.timerLts=setTimeout(function(){d(),clearTimeout(i.timerLts),i.timerLts="COMPLETE"},t.DELAY_LTS_VIEW))}},reportServer:function(e,t){r.refreshDcSessionId(),void 0!==e&&void 0!==t&&i.push(t);var o=r.copyArr(i);if(0!=o.length){i=[];var a={__source__:"csdn",__logs__:o};if(r.setLogIdCookie(n.VIEW),e===n.VIEW||e===n.DELAY_VIEW){var s=window.navigator.userAgent,c="PC";s.toLowerCase().indexOf("csdnedu")>-1?c="CSDNEDU":s.toLowerCase().indexOf("csdnapp")>-1?c="CSDNApp":s.toLowerCase().indexOf("mobile")>-1&&(c="mobile"),a.__tags__={useragent:s,platform:c,log_id:r.getCookie("log_Id_"+n.VIEW)}}}},reportServerDelay:function(e,o){i.push(o);var r=this;r.timer&&clearTimeout(r.timer),r.timer=setTimeout(function(){r.reportServer(n.DELAY_VIEW)},t.DELAY)},reportView:function(t,o,i){if(!t)return void void 0;t.spm&&(t.spm=this.getFullSpm(t.spm));var a=e.extend(!0,{},s,t);try{var c=r.assignExtra(t);c&&(a.extra=c)}catch(e){t&&t.extra&&(a.extra=t.extra),void 0}var d=r.getFuzzyCookie("c_");a.t=r.getTimestamp()+"",a.eleTop=o?o.offset().top+"":"",delete a.cfg,d&&(a.cCookie=d),a.__time__=r.getTimestamp(),a.curl=window.location.href,a.urn=r.guid(),a.pv_urn=p,a.hca=r.getCookie("HMACCOUNT"),this.reportServerLts(n.VIEW,a),"function"==typeof csdn.afterReportView&&csdn.afterReportView(o,t)},reportClick:function(t,o){r.refreshDcSessionId();var i=e.extend(!0,{},s,t);t.spm||(i.spm="");try{var a=r.assignExtra(t);a&&(i.extra=a)}catch(e){t&&t.extra&&(i.extra=t.extra),void 0}i.spm=this.getFullSpm(i.spm),i.t=r.getTimestamp(),i.elePath=o?r.getXPath(o[0])+"":"",i.eleTop=void 0!==i.eleTop?i.eleTop:o?o.offset().top+"":"",i.trace&&r.preserveTraceInfo(i);var c=r.getFuzzyCookie("c_");c&&(i.cCookie=c),i.curl=window.location.href,delete i.cfg,r.setLogIdCookie(n.CLICK),i.log_id=r.getCookie("log_Id_"+n.CLICK),i.sign=r.getSortObjectMD5(i),i.pv_urn=p,i.hca=r.getCookie("HMACCOUNT"),r.analyticsWriteLog("CLICK",i)},reportPageView:function(t){var o=e.extend(!0,{},s,t),i=this;try{var a=r.assignExtra(t||{});a&&(o.extra=a)}catch(e){t&&t.extra&&(o.extra=t.extra),void 0}d&&d.percent&&(u&&clearTimeout(u),u=setTimeout(function(){i.reportPercent(function(e){i.reportClick({spm:"3001.7333",eleTop:e})})},1e3)),this.reportUserAction(),p=r.guid(),o.tos=r.tos(),o.adb=r.getCookie("c_adb")||0,o.curl=window.location.href;var c=r.getFuzzyCookie("c_");c&&(o.cCookie=c),o.t=r.getTimestamp(),o.screen=r.getScreen(),o.un=r.getCookie("UN")||r.getCookie("UserName"),o.urn=p,o.vType=r.getCookie("p_uid")||"",delete o.cfg,delete o.dest,r.setLogIdCookie(n.PV),o.log_id=r.getCookie("log_Id_"+n.PV),o.sign=r.getSortObjectMD5(o),o.lscreen=r.getPixel(),o.hca=r.getCookie("HMACCOUNT"),r.analyticsWriteLog("PV",o)},viewCheck:function(){var t=this;clearTimeout(t.checkTimer),t.checkTimer=setTimeout(function(){r.refreshDcSessionId(),e("[data-report-view]").each(function(){var o=e(this),n=o.data("reportView"),i=e.extend({},s,n);n.spm||(i.spm=""),i.spm=t.getFullSpm(i.spm),i.curl=window.location.href,r.isView(o.get(0),i.cfg.viewStrategy)&&(csdn.report.reportView(i,o),o.removeData("reportView"),o.removeAttr("data-report-view"))})},200)},isView:function(e){return r.isView(e)},addSpmToHref:function(e){var t=e,o=this,n=t.data("reportQuery")||"",i=t.length&&t[0].hash?t[0].hash.split("#").map(function(e){return e.split("?")[0]}):[],a=i.length&&-1===i[i.length-1].indexOf("/");if(n){var s=t.attr("href"),c=s,d={};-1!==s.indexOf("?")&&(c=s.split("?")[0],d=r.urlParamsToObj(s.split("?")[1])),a&&(c=s.split("#")[0],-1!==c.indexOf("?")&&(d=Object.assign({},d,r.urlParamsToObj(c.split("?")[1])),c=s.split("?")[0]));var u=r.urlParamsToObj(n);if((n.indexOf("spm")>-1||n.indexOf("SPM")>-1)&&(u.spm=u.spm||u.SPM,u.spm=o.getFullSpm(u.spm)),a){var f=i.pop();c+=i.join("#")+"?"+r.objToUrlParams(Object.assign(d,u))+"#"+f}else c+="?"+r.objToUrlParams(Object.assign(d,u));t.attr("href",c)}}},void 0===window.csdn&&(window.csdn={}),csdn.report)return void void 0;r.trackOrderSource(),window.csdn.report=o,s=r.initData(),s.disabled||csdn.report.reportPageView(),r.mousePositionChange(),e(function(){var t=csdn.report;e(document).on("click","[data-report-click]",function(){var o=e(this).data("reportClick");t.reportClick(o,e(this))}),t.viewCheck(e("[data-report-view]")),e(window).on("scroll",function(){t.viewCheck(e("[data-report-view]"))}),e(document).on("contextmenu","a[data-report-query]",function(){t.addSpmToHref(e(this))}),e(document).on("click","a[data-report-query]",function(){t.addSpmToHref(e(this))}),e(document).on("click","a[href]",function(){var o=e(this),r=o.attr("href");if(function(e){return!(!/^https:\/\/|^http:\/\//gi.test(e)||"/"===e||e.indexOf(".csdn.net")>-1||e.indexOf(".iteye.com")>-1)}(r)){var n={mod:"1583921753_001",dest:r};t.reportClick(n,o)}})})}(jQuery);