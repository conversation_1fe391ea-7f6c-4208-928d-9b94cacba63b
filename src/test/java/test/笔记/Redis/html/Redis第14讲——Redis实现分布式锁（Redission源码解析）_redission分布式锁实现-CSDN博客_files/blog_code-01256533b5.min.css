.dp-highlighter { font-family: "Consolas" , "Courier New" , Courier, mono, serif; font-size: 12px; background-color: #E7E5DC; width: 99%; margin: 18px 0 18px 0 !important; padding-top: 1px; text-align: left; }

/* clear styles */
.dp-highlighter ol, .dp-highlighter ol li, .dp-highlighter ol li span { margin: 0; padding: 0; border: none; }

.dp-highlighter a, .dp-highlighter a:hover { background: none; border: none; padding: 0; margin: 0; }

.dp-highlighter .bar { padding-left: 45px; }

.dp-highlighter.collapsed .bar, .dp-highlighter.nogutter .bar { padding-left: 0px; }

.dp-highlighter ol { 
	list-style: decimal; /* for ie */ 
	background-color: #fff; margin: 0px 0px 1px 45px !important; /* 1px bottom margin seems to fix occasional Firefox scrolling */ 
	padding: 0px; color: #5C5C5C; 
	border-right:1px solid #E7E5DC;
}

.dp-highlighter.nogutter ol, .dp-highlighter.nogutter ol li { list-style: none !important; margin-left: 0px !important; }

.dp-highlighter ol li, .dp-highlighter .columns div { list-style: decimal-leading-zero; /* better look for others, override cascade from OL */ list-style-position: outside !important; border-left: 3px solid #6CE26C; background-color: #F8F8F8; color: #5C5C5C; padding: 0 3px 0 10px !important; margin: 0 !important; line-height: 150%; }

.dp-highlighter.nogutter ol li, .dp-highlighter.nogutter .columns div { border: 0; }

.dp-highlighter .columns { background-color: #F8F8F8; color: gray; overflow: hidden; width: 100%; }

.dp-highlighter .columns div { padding-bottom: 5px; }

.dp-highlighter ol li.alt { background-color: #FFF; color: inherit; }

.dp-highlighter ol li span { color: black; background-color: inherit; }

/* Adjust some properties when collapsed */

.dp-highlighter.collapsed ol { margin: 0px; }

.dp-highlighter.collapsed ol li { display: none; }

/* Additional modifications when in print-view */

.dp-highlighter.printing { border: none; }

.dp-highlighter.printing .tools { display: none !important; }

.dp-highlighter.printing li { display: list-item !important; }

/* Styles for the tools */

.dp-highlighter .tools { 
	padding: 3px 8px 3px 10px; 
	font: 9px Verdana, Geneva, Arial, Helvetica, sans-serif; color: silver; 
	background-color: #f8f8f8; 
	padding-bottom: 10px; 
	border-left: 3px solid #6CE26C;
	border-right:1px solid #E7E5DC;
	}

.dp-highlighter.nogutter .tools { border-left: 0; }

.dp-highlighter.collapsed .tools { border-bottom: 0; }

.dp-highlighter .tools a { font-size: 9px; color: #a0a0a0; background-color: inherit; text-decoration: none; margin-right: 10px; }

.dp-highlighter .tools a:hover { color: red; background-color: inherit; text-decoration: underline; }

/* About dialog styles */

.dp-about { background-color: #fff; color: #333; margin: 0px; padding: 0px; }
.dp-about table { width: 100%; height: 100%; font-size: 11px; font-family: Tahoma, Verdana, Arial, sans-serif !important; }
.dp-about td { padding: 10px; vertical-align: top; }
.dp-about .copy { border-bottom: 1px solid #ACA899; height: 95%; }
.dp-about .title { color: red; background-color: inherit; font-weight: bold; }
.dp-about .para { margin: 0 0 4px 0; }
.dp-about .footer { background-color: #ECEADB; color: #333; border-top: 1px solid #fff; text-align: right; }
.dp-about .close { font-size: 11px; font-family: Tahoma, Verdana, Arial, sans-serif !important; background-color: #ECEADB; color: #333; width: 60px; height: 22px; }

/* Language specific styles */

.dp-highlighter .comment, .dp-highlighter .comments { color: #008200; background-color: inherit; }
.dp-highlighter .string { color: blue; background-color: inherit; }
.dp-highlighter .keyword { color: #069; font-weight: bold; background-color: inherit; }
.dp-highlighter .preprocessor { color: gray; background-color: inherit; }

/*xml*/
.dp-xml .cdata { color: #ff1493; }
.dp-xml .tag, .dp-xml .tag-name { color: #993300; font-weight: bold; }
.dp-xml .attribute { color: red; }
.dp-xml .attribute-value { color: blue; }
/*sql*/
.dp-sql .func { color: #ff1493; }
.dp-sql .op { color: #808080; }
/*ruby*/
.dp-rb .symbol { color: #a70; }
.dp-rb .variable { color: #a70; font-weight: bold; }
/*python*/
.dp-py .builtins { color: #ff1493; }
.dp-py .magicmethods { color: #808080; }
.dp-py .exceptions { color: brown; }
.dp-py .types { color: brown; font-style: italic; }
.dp-py .commonlibs { color: #8A2BE2; font-style: italic; }
/*java*/
.dp-j .annotation { color: #646464; }
.dp-j .number { color: #C00000; }
/*delphi*/
.dp-delphi .number { color: blue; }
.dp-delphi .directive { color: #008284; }
.dp-delphi .vars { color: #000; }
/*css*/
.dp-css .value { color: black; }
.dp-css .important { color: red; }
/*csharp*/
.dp-c .vars { color: #d00; }
/*cpp*/
.dp-cpp .datatypes { color: #2E8B57; font-weight: bold; }


/*tools image*/
.dp-highlighter .bar a { background-repeat:no-repeat; background-position:left top; display:inline-block; width:16px; height:16px; padding:1px; text-indent:-2000px; _width:0px; _overflow:hidden; _padding-left:16px; _text-indent:0px; }
.dp-highlighter .bar a:hover { border:solid 1px #ccc; text-decoration:none; padding:0px; background-repeat:no-repeat;_padding-left:16px;}
.dp-highlighter .bar a.ExpandSource {}
.dp-highlighter .bar a.ViewSource { background-image:url(https://csdnimg.cn/release/phoenix/images/ico_plain.gif); }
.dp-highlighter .bar a.CopyToClipboard { background-image:url(https://csdnimg.cn/release/phoenix/images/ico_copy.gif);}
.dp-highlighter .bar a.PrintSource { display:none; }
.dp-highlighter .bar a.About { display:none;}
/* Pretty printing styles. Used with prettify.js. */

/* SPAN elements with the classes below are added by prettyprint. */
.pln { color: #000 }  /* plain text */

@media screen {
  .str { color: #080 }  /* string content */
  .kwd { color: #008 }  /* a keyword */
  .com { color: #800 }  /* a comment */
  .typ { color: #606 }  /* a type name */
  .lit { color: #066 }  /* a literal value */
  /* punctuation, lisp open bracket, lisp close bracket */
  .pun, .opn, .clo { color: #660 }
  .tag { color: #008 }  /* a markup tag name */
  .atn { color: #606 }  /* a markup attribute name */
  .atv { color: #080 }  /* a markup attribute value */
  .dec, .var { color: #606 }  /* a declaration; a variable name */
  .fun { color: red }  /* a function name */
}

/* Use higher contrast and text-weight for printable form. */
@media print, projection {
  .str { color: #060 }
  .kwd { color: #006; font-weight: bold }
  .com { color: #600; font-style: italic }
  .typ { color: #404; font-weight: bold }
  .lit { color: #044 }
  .pun, .opn, .clo { color: #440 }
  .tag { color: #006; font-weight: bold }
  .atn { color: #404 }
  .atv { color: #060 }
}

/* Put a border around prettyprinted code snippets. */
pre.prettyprint { padding: 2px; border: 0px solid #888 }

/* Specify class=linenums on a pre to get line numbering */
ol.linenums { margin-top: 0; margin-bottom: 0 } /* IE indents via margin-left */
li.L0,
li.L1,
li.L2,
li.L3,
li.L5,
li.L6,
li.L7,
li.L8 { list-style-type: none }
/* Alternate shading for lines 
li.L1,
li.L3,
li.L5,
li.L7,
li.L9 { background: #eee }
*/
