"use strict";!function(){function e(e){var t=document.createElement("link");t.rel="stylesheet",t.type="text/css",t.href=e,document.getElementsByTagName("head")[0].appendChild(t)}function t(e,t){var n=(new Date).getTime()+parseInt(t);n=new Date(n),document.cookie="csdn_newcert_"+e+"=1;expires="+n.toGMTString()+";domain=.csdn.net;path=/"}window.csdn=window.csdn||{};var n=function(e){var t=document.cookie;return t&&function(){var n,i={};t=t.split("; ");for(var s=0,r=t.length;s<r&&(n=t[s].split("="),!(n.length>0&&(n[0]===e&&(i.key=n[0],i.value=n[1],i.status=!0),"key"in i)));s++);return"key"in i&&i}()},i=function(e,t,n){window.addEventListener?function(){e.addEventListener(t,n,!1)}():function(){e.attachEvent("on"+t,n)}()},s=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:8,t="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",n="",i=e;i>0;--i)n+=t[Math.floor(Math.random()*t.length)];return n},r=function(e){window.document.domain="csdn.net";var t=s();this.defaultData={pageId:t,source:"",pathUrl:window.location.href,iframeUrl:"https://i.csdn.net/#/cert-iframe/new-cert",iframeName:"csdn-cert-iframe"},this.options=Object.assign({},this.defaultData,e),this.renderCss(),this.init()};r.prototype.renderCss=function(){e("https://g.csdnimg.cn/common/csdn-cert-new/csdn-cert-new.css")},r.prototype.init=function(){this.render()},r.prototype.render=function(){var e=this;this.options.iframeUrl=this.options.iframeUrl+"?pageId="+this.options.pageId+"&source="+this.options.source,this.$tql=$('<div class="csdn-cert-container">\n      <div class="csdn-cert-back"></div>\n      <div class="csdn-cert-iframe">\n        <iframe  width="100%" height="100%" id="csdn-cert-iframe" name="'+this.options.iframeName+'" src="'+this.options.iframeUrl+'" frameborder="0" scrolling="no"></iframe>\n      </div >\n    </div> '),$("body").append(this.$tql);var t=this,n=function(n){if("message"===n.type){var i=n.data.data;if(!i)return!1;var s=t.options.pageId;if(i.pageId!==s)return!1;switch(n.data.type){case"bodyHeight":var r=t.$tql.find(".csdn-cert-iframe")[0],o=parseInt(i.height)<=664?i.height:"664";r.style.height=o+"px",r.style.visibility="visible",r.style.opacity="1";break;case"finishCert":e.close(),window.location.href=t.options.pathUrl;break;case"close":e.close()}}};i(window,"message",n)},r.prototype.close=function(){this.$tql.remove()};var o=n("UserName")||null;if(o&&o.value.length>0){if(!function(e){return!n("csdn_newcert_"+e)}(o.value))return!1;$.ajax({url:"https://passport.csdn.net/v1/login/user/cert/collect/state",type:"Get",contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){e.status&&(e.data.showUserCertCollect&&window.csdn.certNewBox.show({source:e.data.source}),t(o.value,e.data.intervalTime))},error:function(e){t(o,864e5)}})}window.csdn.certNewBox={self:void 0,show:function(e){this.self=new r(e)},close:function(){return this.self.close()}}}();