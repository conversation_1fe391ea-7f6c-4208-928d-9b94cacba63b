(function(sttc){'use strict';var aa=Object.defineProperty,ba=globalThis,ca=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",da={},ea={};function fa(a,b,c){if(!c||a!=null){c=ea[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}} 
function ha(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in da?f=da:f=ba;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=ca&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?aa(da,d,{configurable:!0,writable:!0,value:b}):b!==c&&(ea[d]===void 0&&(a=Math.random()*1E9>>>0,ea[d]=ca?ba.Symbol(d):"$jscp$"+a+"$"+d),aa(f,ea[d],{configurable:!0,writable:!0,value:b})))}}ha("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")},"es_next");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var q=this||self;function ia(a){a=a.split(".");for(var b=q,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b}function ja(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function ka(a){return Object.prototype.hasOwnProperty.call(a,la)&&a[la]||(a[la]=++ma)}var la="closure_uid_"+(Math.random()*1E9>>>0),ma=0;function na(a,b,c){return a.call.apply(a.bind,arguments)} 
function oa(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function pa(a,b,c){pa=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?na:oa;return pa.apply(null,arguments)} 
function qa(a,b,c){a=a.split(".");c=c||q;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};function ra(a){q.setTimeout(()=>{throw a;},0)};function sa(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]} 
function ta(a,b){let c=0;a=sa(String(a)).split(".");b=sa(String(b)).split(".");const d=Math.max(a.length,b.length);for(let g=0;c==0&&g<d;g++){var e=a[g]||"",f=b[g]||"";do{e=/(\d*)(\D*)(.*)/.exec(e)||["","","",""];f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];if(e[0].length==0&&f[0].length==0)break;c=ua(e[1].length==0?0:parseInt(e[1],10),f[1].length==0?0:parseInt(f[1],10))||ua(e[2].length==0,f[2].length==0)||ua(e[2],f[2]);e=e[3];f=f[3]}while(c==0)}return c}function ua(a,b){return a<b?-1:a>b?1:0};var va,wa=ia("CLOSURE_FLAGS"),xa=wa&&wa[610401301];va=xa!=null?xa:!1;function ya(){var a=q.navigator;return a&&(a=a.userAgent)?a:""}var za;const Aa=q.navigator;za=Aa?Aa.userAgentData||null:null;function Ba(a){if(!va||!za)return!1;for(let b=0;b<za.brands.length;b++){const {brand:c}=za.brands[b];if(c&&c.indexOf(a)!=-1)return!0}return!1}function r(a){return ya().indexOf(a)!=-1};function Ca(){return va?!!za&&za.brands.length>0:!1}function Da(){return Ca()?!1:r("Trident")||r("MSIE")}function Ea(){return Ca()?Ba("Chromium"):(r("Chrome")||r("CriOS"))&&!(Ca()?0:r("Edge"))||r("Silk")}function Fa(a){const b={};a.forEach(c=>{b[c[0]]=c[1]});return c=>b[c.find(d=>d in b)]||""} 
function Ga(){var a=ya();if(Da()){var b=/rv: *([\d\.]*)/.exec(a);if(b&&b[1])a=b[1];else{b="";var c=/MSIE +([\d\.]+)/.exec(a);if(c&&c[1])if(a=/Trident\/(\d.\d)/.exec(a),c[1]=="7.0")if(a&&a[1])switch(a[1]){case "4.0":b="8.0";break;case "5.0":b="9.0";break;case "6.0":b="10.0";break;case "7.0":b="11.0"}else b="7.0";else b=c[1];a=b}return a}c=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g");b=[];let d;for(;d=c.exec(a);)b.push([d[1],d[2],d[3]||void 0]);a=Fa(b);return(Ca()?0:r("Opera"))?a(["Version", 
"Opera"]):(Ca()?0:r("Edge"))?a(["Edge"]):(Ca()?Ba("Microsoft Edge"):r("Edg/"))?a(["Edg"]):r("Silk")?a(["Silk"]):Ea()?a(["Chrome","CriOS","HeadlessChrome"]):(a=b[2])&&a[1]||""};function Ha(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(let c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1}function Ia(a,b){const c=a.length,d=[];let e=0;const f=typeof a==="string"?a.split(""):a;for(let g=0;g<c;g++)if(g in f){const h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d}function Ja(a,b){const c=a.length,d=Array(c),e=typeof a==="string"?a.split(""):a;for(let f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a));return d} 
function Ka(a,b){const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a))return!0;return!1}function La(a,b){a:{var c=a.length;const d=typeof a==="string"?a.split(""):a;for(--c;c>=0;c--)if(c in d&&b.call(void 0,d[c],c,a)){b=c;break a}b=-1}return b<0?null:typeof a==="string"?a.charAt(b):a[b]}function Ma(a,b){return Ha(a,b)>=0}function Na(a){const b=a.length;if(b>0){const c=Array(b);for(let d=0;d<b;d++)c[d]=a[d];return c}return[]};function Oa(a){Oa[" "](a);return a}Oa[" "]=function(){};var Pa=null;function Qa(a){const b=[];Ra(a,function(c){b.push(c)});return b}function Ra(a,b){function c(e){for(;d<a.length;){const f=a.charAt(d++),g=Pa[f];if(g!=null)return g;if(!/^[\s\xa0]*$/.test(f))throw Error("Unknown base64 encoding at char: "+f);}return e}Sa();let d=0;for(;;){const e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}} 
function Sa(){if(!Pa){Pa={};var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"];for(let c=0;c<5;c++){const d=a.concat(b[c].split(""));for(let e=0;e<d.length;e++){const f=d[e];Pa[f]===void 0&&(Pa[f]=e)}}}};function Ta(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};let Ua=void 0,Va;function Wa(a){if(Va)throw Error("");Va=b=>{q.setTimeout(()=>{a(b)},0)}}function Xa(a){if(Va)try{Va(a)}catch(b){throw b.cause=a,b;}}function Ya(a){a=Error(a);Ta(a,"warning");Xa(a);return a}function $a(a,b){if(a!=null){var c=Ua??(Ua={});var d=c[a]||0;d>=b||(c[a]=d+1,a=Error(),Ta(a,"incident"),Va?Xa(a):ra(a))}};function ab(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var bb=ab(),cb=ab(),db=ab(),eb=ab("m_m",!0),fb=ab();const t=ab("jas",!0);var gb;const hb=[];hb[t]=55;gb=Object.freeze(hb);function ib(a,b){a[t]|=b}function jb(a){if(4&a)return 2048&a?2048:4096&a?4096:0}function kb(a){ib(a,32);return a};const lb=typeof eb==="symbol";var mb={};function nb(a){a=a[eb];const b=a===mb;lb&&a&&!b&&$a(fb,3);return b}function ob(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object}function pb(a){if(a&2)throw Error();}var qb=Object.freeze({});function rb(a,b){const c=sb;if(!b(a))throw b=(typeof c==="function"?c():c)?.concat("\n")??"",Error(b+String(a));}function tb(a){a.tc=!0;return a}let sb=void 0;const ub=tb(a=>a!==null&&a!==void 0);var vb=tb(a=>typeof a==="number"),wb=tb(a=>typeof a==="string"),xb=tb(a=>a===void 0),yb=tb(a=>Array.isArray(a));function zb(){return tb(a=>yb(a)?a.every(b=>vb(b)):!1)};function Ab(a){if(wb(a)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(a))throw Error(String(a));}else if(vb(a)&&!Number.isSafeInteger(a))throw Error(String(a));return BigInt(a)}var Db=tb(a=>a>=Bb&&a<=Cb);const Bb=BigInt(Number.MIN_SAFE_INTEGER),Cb=BigInt(Number.MAX_SAFE_INTEGER);let Eb=0,Fb=0;function Gb(a){const b=a>>>0;Eb=b;Fb=(a-b)/4294967296>>>0}function Hb(a){if(a<0){Gb(-a);a=Eb;var b=Fb;b=~b;a?a=~a+1:b+=1;const [c,d]=[a,b];Eb=c>>>0;Fb=d>>>0}else Gb(a)}function Ib(a,b){b>>>=0;a>>>=0;var c;b<=2097151?c=""+(4294967296*b+a):c=""+(BigInt(b)<<BigInt(32)|BigInt(a));return c}function Jb(){var a=Eb,b=Fb,c;b&2147483648?c=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):c=Ib(a,b);return c};function Kb(a,b=`unexpected value ${a}!`){throw Error(b);};const Lb=typeof BigInt==="function"?BigInt.asIntN:void 0,Mb=Number.isSafeInteger,Nb=Number.isFinite,Ob=Math.trunc;function Pb(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function Qb(a){if(a!=null&&typeof a!=="boolean"){var b=typeof a;throw Error(`Expected boolean but got ${b!="object"?b:a?Array.isArray(a)?"array":b:"null"}: ${a}`);}return a}function Rb(a){if(a==null||typeof a==="boolean")return a;if(typeof a==="number")return!!a} 
const Sb=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function Tb(a){switch(typeof a){case "bigint":return!0;case "number":return Nb(a);case "string":return Sb.test(a);default:return!1}}function Ub(a){if(!Nb(a))throw Ya("enum");return a|0}function Vb(a){return a==null?a:Nb(a)?a|0:void 0}function Wb(a){if(typeof a!=="number")throw Ya("int32");if(!Nb(a))throw Ya("int32");return a|0}function Xb(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Nb(a)?a|0:void 0} 
function Yb(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Nb(a)?a>>>0:void 0}function Zb(a){if(!Tb(a))throw Ya("int64");switch(typeof a){case "string":return $b(a);case "bigint":return Ab(Lb(64,a));default:return ac(a)}}function bc(a){const b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337} 
function ac(a){a=Ob(a);if(!Mb(a)){Hb(a);var b=Eb,c=Fb;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);const d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:Ib(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function cc(a){a=Ob(a);if(Mb(a))a=String(a);else{{const b=String(a);bc(b)?a=b:(Hb(a),a=Jb())}}return a} 
function $b(a){var b=Ob(Number(a));if(Mb(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));bc(a)||(a.length<16?Hb(Number(a)):(a=BigInt(a),Eb=Number(a&BigInt(4294967295))>>>0,Fb=Number(a>>BigInt(32)&BigInt(4294967295))),a=Jb());return a}function dc(a){if(typeof a!=="string")throw Error();return a}function ec(a){if(a!=null&&typeof a!=="string")throw Error();return a}function gc(a){return a==null||typeof a==="string"?a:void 0} 
function hc(a,b,c,d){if(a!=null&&typeof a==="object"&&nb(a))return a;if(!Array.isArray(a))return c?d&2?((a=b[bb])||(a=new b,ib(a.C,34),a=b[bb]=a),b=a):b=new b:b=void 0,b;let e=c=a[t]|0;e===0&&(e|=d&32);e|=d&2;e!==c&&(a[t]=e);return new b(a)};function ic(a){return a};function jc(a,b,c,d,e){d=d?!!(b&32):void 0;const f=[];var g=a.length;let h,k,m,n=!1;b&64?(b&256?(g--,h=a[g],k=g):(k=4294967295,h=void 0),e||b&512||(n=!0,m=(kc??ic)(h?k- -1:b>>15&1023||536870912,-1,a,h),k=m+-1)):(k=4294967295,b&1||(h=g&&a[g-1],ob(h)?(g--,k=g,m=0):h=void 0));let l=void 0;for(let p=0;p<g;p++){let v=a[p];v!=null&&(v=c(v,d))!=null&&(p>=k?(l??(l={}))[p- -1]=v:f[p]=v)}if(h)for(let p in h)Object.prototype.hasOwnProperty.call(h,p)&&(a=h[p],a!=null&&(a=c(a,d))!=null&&(g=+p,g<m?f[g+-1]=a:(l?? 
(l={}))[p]=a));l&&(n?f.push(l):f[k]=l);e&&(f[t]=b&33522241|(l!=null?290:34));return f}function lc(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return Db(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[t]|0;return a.length===0&&b&1?void 0:jc(a,b,lc,!1,!1)}if(nb(a))return w(a);return}return a}var mc=typeof structuredClone!="undefined"?structuredClone:a=>jc(a,0,lc,void 0,!1);let kc; 
function w(a){a=a.C;return jc(a,a[t]|0,lc,void 0,!1)};function nc(){$a(db,5)};function oc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){const d=a[t]|0;if(a.length===0&&d&1)return;if(d&2)return a;var c;if(c=b)c=d===0||!!(d&32)&&!(d&64||!(d&16));return c?(ib(a,34),d&4&&Object.freeze(a),a):jc(a,d,oc,b!==void 0,!0)}if(nb(a))return b=a.C,c=b[t]|0,c&2?a:jc(b,c,oc,!0,!0)}function pc(a){var b=a.C;if(!((b[t]|0)&2))return a;a=new a.constructor(jc(b,b[t]|0,oc,!0,!0));b=a.C;b[t]&=-3;return a} 
function qc(a){const b=a.C,c=b[t]|0;return c&2?a:new a.constructor(jc(b,c,oc,!0,!0))};const rc=Ab(0);function sc(a,b){a=a.C;return tc(a,a[t]|0,b)}function tc(a,b,c,d){if(c===-1)return null;const e=c+(b&512?0:-1),f=a.length-1;let g;if(e>=f&&b&256)b=a[f][c],g=!0;else if(e<=f)b=a[e];else return;if(d&&b!=null){d=d(b);if(d==null)return d;if(d!==b)return g?a[f][c]=d:a[e]=d,d}return b}function uc(a,b,c){const d=a.C;let e=d[t]|0;pb(e);y(d,e,b,c);return a} 
function y(a,b,c,d){const e=b&512?0:-1,f=c+e;var g=a.length-1;if(f>=g&&b&256)return a[g][c]=d,b;if(f<=g)return a[f]=d,b;d!==void 0&&(g=b>>15&1023||536870912,c>=g?d!=null&&(a[g+e]={[c]:d},b|=256,a[t]=b):a[f]=d);return b}function vc(a,b,c){return wc(a,b,c)!==void 0}function z(a){return a===qb?2:4} 
function xc(a,b,c,d,e){const f=a.C;a=f[t]|0;const g=2&a?1:d;e=!!e;d=yc(f,a,b);let h=d[t]|0;if(!(4&h)){4&h&&(d=[...d],h=zc(h,a),a=y(f,a,b,d));let k=0,m=0;for(;k<d.length;k++){const n=c(d[k]);n!=null&&(d[m++]=n)}m<k&&(d.length=m);h=Ac(h,a);c=(h|20)&-2049;h=c&=-4097;d[t]=h;2&h&&Object.freeze(d)}g===1||g===4&&32&h?Bc(h)||(e=h,h|=2,h!==e&&(d[t]=h),Object.freeze(d)):(g===2&&Bc(h)&&(d=[...d],h=zc(h,a),h=Cc(h,a,e),d[t]=h,a=y(f,a,b,d)),Bc(h)||(b=h,h=Cc(h,a,e),h!==b&&(d[t]=h)));return d} 
function yc(a,b,c){a=tc(a,b,c);return Array.isArray(a)?a:gb}function Ac(a,b){a===0&&(a=zc(a,b),a|=16);return a|1}function Bc(a){return!!(2&a)&&!!(4&a)||!!(1024&a)} 
function Dc(a,b,c,d){const e=a.C;let f=e[t]|0;pb(f);if(c==null)return y(e,f,b),a;let g=c[t]|0,h=g;var k=Bc(g);let m=k||Object.isFrozen(c);k||(g=0);m||(c=[...c],h=0,g=zc(g,f),g=Cc(g,f,!0),m=!1);g|=21;k=jb(g)??0;for(let n=0;n<c.length;n++){const l=c[n],p=d(l,k);Object.is(l,p)||(m&&(c=[...c],h=0,g=zc(g,f),g=Cc(g,f,!0),m=!1),c[n]=p)}g!==h&&(m&&(c=[...c],g=zc(g,f),g=Cc(g,f,!0)),c[t]=g);y(e,f,b,c);return a} 
function Ec(a,b,c,d){const e=a.C;let f=e[t]|0;pb(f);y(e,f,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}function Fc(a,b,c,d){const e=a.C;var f=e[t]|0;pb(f);if(d==null){var g=Gc(e);if(Hc(g,e,f,c)===b)g.set(c,0);else return a}else{g=Gc(e);const h=Hc(g,e,f,c);h!==b&&(h&&(f=y(e,f,h)),g.set(c,b))}y(e,f,b,d);return a}function Ic(a,b,c){return Jc(a,b)===c?c:-1}function Jc(a,b){a=a.C;return Hc(Gc(a),a,a[t]|0,b)}function Gc(a){return a[cb]??(a[cb]=new Map)} 
function Hc(a,b,c,d){let e=a.get(d);if(e!=null)return e;e=0;for(let f=0;f<d.length;f++){const g=d[f];tc(b,c,g)!=null&&(e!==0&&(c=y(b,c,e)),e=g)}a.set(d,e);return e}function Kc(a,b,c){a=a.C;let d=a[t]|0;pb(d);const e=tc(a,d,c);b=pc(hc(e,b,!0,d));e!==b&&y(a,d,c,b);return b}function wc(a,b,c){a=a.C;let d=a[t]|0;const e=tc(a,d,c);b=hc(e,b,!1,d);b!==e&&b!=null&&y(a,d,c,b);return b}function Lc(a,b,c){(a=wc(a,b,c))||(a=b[bb])||(a=new b,ib(a.C,34),a=b[bb]=a);return a} 
function A(a,b,c){b=wc(a,b,c);if(b==null)return b;a=a.C;let d=a[t]|0;if(!(d&2)){const e=pc(b);e!==b&&(b=e,y(a,d,c,b))}return b} 
function B(a,b,c,d){var e=a.C[t]|0,f=e,g=!(2&e);a=a.C;var h=!!(2&f);e=h?1:d;g&&(g=!h);d=yc(a,f,c);var k=d[t]|0;h=!!(4&k);if(!h){k=Ac(k,f);var m=d,n=f;const l=!!(2&k);l&&(n|=2);let p=!l,v=!0,u=0,x=0;for(;u<m.length;u++){const K=hc(m[u],b,!1,n);if(K instanceof b){if(!l){const O=!!((K.C[t]|0)&2);p&&(p=!O);v&&(v=O)}m[x++]=K}}x<u&&(m.length=x);k|=4;k=v?k|16:k&-17;k=p?k|8:k&-9;m[t]=k;l&&Object.freeze(m)}if(g&&!(8&k||!d.length&&(e===1||e===4&&32&k))){Bc(k)&&(d=[...d],k=zc(k,f),f=y(a,f,c,d));b=d;g=k;for(m= 
0;m<b.length;m++)k=b[m],n=pc(k),k!==n&&(b[m]=n);g|=8;g=b.length?g&-17:g|16;k=b[t]=g}e===1||e===4&&32&k?Bc(k)||(f=k,k|=!d.length||16&k&&(!h||32&k)?2:1024,k!==f&&(d[t]=k),Object.freeze(d)):(e===2&&Bc(k)&&(d=[...d],k=zc(k,f),k=Cc(k,f,!1),d[t]=k,f=y(a,f,c,d)),Bc(k)||(c=k,k=Cc(k,f,!1),k!==c&&(d[t]=k)));return d}function Mc(a,b,c){c==null&&(c=void 0);return uc(a,b,c)}function Nc(a,b,c,d){d==null&&(d=void 0);return Fc(a,b,c,d)} 
function Oc(a,b,c){const d=a.C;let e=d[t]|0;pb(e);if(c==null)return y(d,e,b),a;let f=c[t]|0,g=f;const h=Bc(f),k=h||Object.isFrozen(c);let m=!0,n=!0;for(let p=0;p<c.length;p++){var l=c[p];h||(l=!!((l.C[t]|0)&2),m&&(m=!l),n&&(n=l))}h||(f=m?13:5,f=n?f|16:f&-17);k&&f===g||(c=[...c],g=0,f=zc(f,e),f=Cc(f,e,!0));f!==g&&(c[t]=f);y(d,e,b,c);return a}function zc(a,b){2&a&&(a|=16);a=(2&b?a|2:a&-3)|32;return a&=-1025}function Cc(a,b,c){32&b&&c||(a&=-33);return a} 
function Pc(a,b){pb(a.C[t]|0);a=xc(a,4,gc,2,!0);const c=jb(a[t]|0)??0;if(Array.isArray(b)){var d=b.length;for(let e=0;e<d;e++)a.push(dc(b[e],c))}else for(d of b)a.push(dc(d,c))}function Qc(a,b){a=sc(a,b);b=typeof a;a!=null&&(b==="bigint"?a=Ab(Lb(64,a)):Tb(a)?b==="string"?(b=Ob(Number(a)),Mb(b)?a=Ab(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=Ab(Lb(64,BigInt(a))))):a=Mb(a)?Ab(ac(a)):Ab(cc(a)):a=void 0);return a}function Rc(a,b){return Xb(sc(a,b))}function C(a,b){return gc(sc(a,b))} 
function D(a,b){return Vb(sc(a,b))}function E(a,b){return Rb(sc(a,b))??!1}function F(a,b){return Rc(a,b)??0}function Sc(a,b){return Qc(a,b)??rc}function Tc(a,b){a=a.C;return tc(a,a[t]|0,b,Pb)??0}function G(a,b){return C(a,b)??""}function H(a,b){return D(a,b)??0}function Uc(a,b){return xc(a,b,gc,z())}function Xc(a,b){return xc(a,b,Vb,z())}function Yc(a,b,c){return H(a,Ic(a,c,b))}function Zc(a,b,c,d){return A(a,b,Ic(a,d,c))}function $c(a,b,c){return uc(a,b,c==null?c:Wb(c))} 
function ad(a,b,c){return Ec(a,b,c==null?c:Wb(c),0)}function bd(a,b,c){return Ec(a,b,c==null?c:Zb(c),"0")}function cd(a,b){var c=performance.now();if(c!=null&&typeof c!=="number")throw Error(`Value of float/double field must be a number, found ${typeof c}: ${c}`);Ec(a,b,c,0)}function dd(a,b,c){return uc(a,b,ec(c))}function ed(a,b,c){return Ec(a,b,ec(c),"")}function fd(a,b,c){return uc(a,b,c==null?c:Ub(c))}function gd(a,b,c){return Ec(a,b,c==null?c:Ub(c),0)} 
function hd(a,b,c,d){return Fc(a,b,c,d==null?d:Ub(d))};var I=class{constructor(a){a:{if(a==null){var b=96;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[t]|0;8192&b||!(64&b)||2&b||nc();if(b&1024)throw Error("farr");if(b&64){b&16384||(a[t]=b|16384);var c=a;break a}var d=a;b|=64;var e=d.length;if(e){var f=e-1;e=d[f];if(ob(e)){b|=256;const g=b&512?0:-1;f-=g;if(f>=1024)throw Error("pvtlmt");for(c in e){if(!Object.prototype.hasOwnProperty.call(e,c))continue;const h=+c;if(h<f)d[h+g]=e[c],delete e[c];else break}b=b&-33521665|(f&1023)<<15}}}a[t]=b|16384; 
c=a}this.C=c}toJSON(){return w(this)}};I.prototype[eb]=mb;function id(a,b){if(b==null)return new a;if(!Array.isArray(b))throw Error("must be an array");if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error("arrays passed to jspb constructors must be mutable");ib(b,128);return new a(kb(b))};function jd(a){return()=>{var b;(b=a[bb])||(b=new a,ib(b.C,34),b=a[bb]=b);return b}}function kd(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b=new a(kb(b))}return b}};var ld=class extends I{};var md=class extends I{};function nd(a){return function(){return!a.apply(this,arguments)}}function od(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}function pd(a){let b=a;return function(){if(b){const c=b;b=null;c()}}};function qd(a,b,c){a.addEventListener&&a.addEventListener(b,c,!1)}function rd(a,b,c){return a.removeEventListener?(a.removeEventListener(b,c,!1),!0):!1};function sd(){return va&&za?za.mobile:!td()&&(r("iPod")||r("iPhone")||r("Android")||r("IEMobile"))}function td(){return va&&za?!za.mobile&&(r("iPad")||r("Android")||r("Silk")):r("iPad")||r("Android")&&!r("Mobile")||r("Silk")};function ud(a,b){const c={};for(const d in a)b.call(void 0,a[d],d,a)&&(c[d]=a[d]);return c}function vd(a,b){for(const c in a)if(b.call(void 0,a[c],c,a))return!0;return!1}function wd(a){const b=[];let c=0;for(const d in a)b[c++]=a[d];return b}function xd(a){const b={};for(const c in a)b[c]=a[c];return b};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
let yd=globalThis.trustedTypes,zd;function Ad(){let a=null;if(!yd)return a;try{const b=c=>c;a=yd.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a};var Bd=class{constructor(a){this.g=a}toString(){return this.g+""}};function Cd(a){zd===void 0&&(zd=Ad());var b=zd;return new Bd(b?b.createScriptURL(a):a)}function Dd(a){if(a instanceof Bd)return a.g;throw Error("");};var Ed=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function Fd(a=document){a=a.querySelector?.("script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};const Gd="alternate author bookmark canonical cite help icon license modulepreload next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" ");function Hd(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})};var Id=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Jd=/#|$/;function Kd(a,b){const c=a.search(Jd);a:{var d=0;for(var e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)break a;d+=e+1}d=-1}if(d<0)return null;e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,e!==-1?e:0).replace(/\+/g," "))};function Ld(a,...b){if(b.length===0)return Cd(a[0]);let c=a[0];for(let d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Cd(c)}function Md(a,b){a=Dd(a).toString();const c=a.split(/[?#]/),d=/[?]/.test(a)?"?"+c[1]:"";return Nd(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)} 
function Nd(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(k=>e(k,h)):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}let f=b.length?"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(g=>e(g[1],g[0])):d.forEach(e);return Cd(a+b+c)};function Od(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Oa(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch{return!1}}function Pd(a){return Od(a.top)?a.top:null}function Qd(a,b){const c=Rd("SCRIPT",a);c.src=Dd(b);(b=Fd(c.ownerDocument))&&c.setAttribute("nonce",b);(a=a.getElementsByTagName("script")[0])&&a.parentNode&&a.parentNode.insertBefore(c,a)}function Sd(a,b){return b.getComputedStyle?b.getComputedStyle(a,null):a.currentStyle} 
function Td(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function Ud(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function Vd(a){const b=a.length;if(b==0)return 0;let c=305419896;for(let d=0;d<b;d++)c^=(c<<5)+(c>>2)+a.charCodeAt(d)&4294967295;return c>0?c:4294967296+c}var Wd=/^([0-9.]+)px$/,Xd=/^(-?[0-9.]{1,30})$/; 
function Yd(a){if(!Xd.test(a))return null;a=Number(a);return isNaN(a)?null:a}function Zd(a){return(a=Wd.exec(a))?+a[1]:null}var $d=od(()=>sd()?2:td()?1:0),ae=a=>{Ud({display:"none"},(b,c)=>{a.style.setProperty(c,b,"important")})};let be=[];const ce=()=>{const a=be;be=[];for(const b of a)try{b()}catch{}};function de(){var a=J(ee).A(fe.g,fe.defaultValue),b=L.document;if(a.length&&b.head)for(const c of a)c&&b.head&&(a=Rd("META"),b.head.appendChild(a),a.httpEquiv="origin-trial",a.content=c)} 
var ge=()=>{var a=Math.random;return Math.floor(a()*2**52)},he=a=>{if(typeof a.goog_pvsid!=="number")try{Object.defineProperty(a,"goog_pvsid",{value:ge(),configurable:!1})}catch(b){}return Number(a.goog_pvsid)||-1},je=a=>{var b=ie;b.readyState==="complete"||b.readyState==="interactive"?(be.push(a),be.length==1&&(window.Promise?Promise.resolve().then(ce):window.setImmediate?setImmediate(ce):setTimeout(ce,0))):b.addEventListener("DOMContentLoaded",a)}; 
function Rd(a,b=document){return b.createElement(String(a).toLowerCase())};function ke(a,b,c=null,d=!1,e=!1){le(a,b,c,d,e)}function le(a,b,c,d,e=!1){a.google_image_requests||(a.google_image_requests=[]);const f=Rd("IMG",a.document);if(c||d){const g=h=>{c&&c(h);if(d){h=a.google_image_requests;const k=Ha(h,f);k>=0&&Array.prototype.splice.call(h,k,1)}rd(f,"load",g);rd(f,"error",g)};qd(f,"load",g);qd(f,"error",g)}e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)} 
function me(a,b){let c=`https://${"pagead2.googlesyndication.com"}/pagead/gen_204?id=${b}`;Ud(a,(d,e)=>{if(d||d===0)c+=`&${e}=${encodeURIComponent(String(d))}`});ne(c)}function ne(a){var b=window;b.fetch?b.fetch(a,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"}):ke(b,a,void 0,!1,!1)};var ie=document,L=window;let oe=null;var pe=(a,b=[])=>{let c=!1;q.google_logging_queue||(c=!0,q.google_logging_queue=[]);q.google_logging_queue.push([a,b]);if(a=c){if(oe==null){oe=!1;try{const d=Pd(q);d&&d.location.hash.indexOf("google_logging")!==-1&&(oe=!0)}catch(d){}}a=oe}a&&Qd(q.document,Ld`https://pagead2.googlesyndication.com/pagead/js/logging_library.js`)};function qe(a,b){this.width=a;this.height=b}qe.prototype.aspectRatio=function(){return this.width/this.height};qe.prototype.isEmpty=function(){return!(this.width*this.height)};qe.prototype.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};qe.prototype.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};qe.prototype.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this}; 
qe.prototype.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};function re(a=q){let b=a.context||a.AMP_CONTEXT_DATA;if(!b)try{b=a.parent.context||a.parent.AMP_CONTEXT_DATA}catch{}return b?.pageViewId&&b?.canonicalUrl?b:null}function se(a=re()){return a?Od(a.master)?a.master:null:null};function ue(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)}function ve(a){this.g=a||q.document||document}ve.prototype.contains=function(a,b){return a&&b?a==b||a.contains(b):!1};var we=a=>{a=se(re(a))||a;a.google_unique_id=(a.google_unique_id||0)+1;return a.google_unique_id},xe=a=>{a=a.google_unique_id;return typeof a==="number"?a:0},ye=a=>{if(!a)return"";a=a.toLowerCase();a.substring(0,3)!="ca-"&&(a="ca-"+a);return a};function ze(a){return!!(a.error&&a.meta&&a.id)}var Ae=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function Be(a){return new Ae(a,{message:Ce(a)})}function Ce(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(d){b=c;break a}b=void 0}return b};const De=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var Ee=class{constructor(a,b){this.g=a;this.i=b}},Fe=class{constructor(a,b,c){this.url=a;this.l=b;this.g=!!c;this.depth=null}};let Ge=null;function He(){var a=window;if(Ge===null){Ge="";try{let b="";try{b=a.top.location.hash}catch(c){b=a.location.hash}if(b){const c=b.match(/\bdeid=([\d,]+)/);Ge=c?c[1]:""}}catch(b){}}return Ge};function Ie(){const a=q.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function Je(){const a=q.performance;return a&&a.now?a.now():null};var Ke=class{constructor(a,b){var c=Je()||Ie();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const Le=q.performance,Me=!!(Le&&Le.mark&&Le.measure&&Le.clearMarks),Ne=od(()=>{var a;if(a=Me)a=He(),a=!!a.indexOf&&a.indexOf("1337")>=0;return a});function Oe(a){a&&Le&&Ne()&&(Le.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),Le.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))}function Pe(a){a.g=!1;if(a.i!==a.j.google_js_reporting_queue){if(Ne()){var b=a.i;const c=b.length;b=typeof b==="string"?b.split(""):b;for(let d=0;d<c;d++)d in b&&Oe.call(void 0,b[d])}a.i.length=0}} 
var Qe=class{constructor(a){this.i=[];this.j=a||q;let b=null;a&&(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.i=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.g=Ne()||(b!=null?b:Math.random()<1)}start(a,b){if(!this.g)return null;a=new Ke(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;Le&&Ne()&&Le.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(Je()||Ie())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;Le&&Ne()&&Le.mark(b);!this.g||this.i.length> 
2048||this.i.push(a)}}};function Re(a,b){const c={};c[a]=b;return[c]}function Se(a,b,c,d,e){const f=[];Ud(a,(g,h)=>{(g=Te(g,b,c,d,e))&&f.push(`${h}=${g}`)});return f.join(b)} 
function Te(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push(Te(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Se(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function Ue(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.j.length-1} 
function Ve(a,b,c){b="https://"+b+c;let d=Ue(a)-c.length;if(d<0)return"";a.g.sort((f,g)=>f-g);c=null;let e="";for(let f=0;f<a.g.length;f++){const g=a.g[f],h=a.i[g];for(let k=0;k<h.length;k++){if(!d){c=c==null?g:c;break}let m=Se(h[k],a.j,",$");if(m){m=e+m;if(d>=m.length){d-=m.length;b+=m;e=a.j;break}c=c==null?g:c}}}a="";c!=null&&(a=`${e}${"trn"}=${c}`);return b+a}var We=class{constructor(){this.j="&";this.i={};this.u=0;this.g=[]}};var Ze=class{constructor(a=null){this.G=Xe;this.j=a;this.i=null;this.B=!1;this.D=this.I}J(a){this.D=a}A(a){this.i=a}Y(a){this.B=a}g(a,b,c){let d,e;try{this.j&&this.j.g?(e=this.j.start(a.toString(),3),d=b(),this.j.end(e)):d=b()}catch(f){b=!0;try{Oe(e),b=this.D(a,Be(f),void 0,c)}catch(g){this.I(217,g)}if(b)window.console?.error?.(f);else throw f;}return d}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}I(a,b,c,d,e){e=e||"jserror";let f=void 0;try{const Za=new We;var g=Za;g.g.push(1);g.i[1]=Re("context", 
a);ze(b)||(b=Be(b));g=b;if(g.msg){b=Za;var h=g.msg.substring(0,512);b.g.push(2);b.i[2]=Re("msg",h)}var k=g.meta||{};h=k;if(this.i)try{this.i(h)}catch(X){}if(d)try{d(h)}catch(X){}d=Za;k=[k];d.g.push(3);d.i[3]=k;var m;if(!(m=p)){d=q;k=[];h=null;do{var n=d;if(Od(n)){var l=n.location.href;h=n.document&&n.document.referrer||null}else l=h,h=null;k.push(new Fe(l||"",n));try{d=n.parent}catch(X){d=null}}while(d&&n!==d);for(let X=0,Mg=k.length-1;X<=Mg;++X)k[X].depth=Mg-X;n=q;if(n.location&&n.location.ancestorOrigins&& 
n.location.ancestorOrigins.length===k.length-1)for(l=1;l<k.length;++l){const X=k[l];X.url||(X.url=n.location.ancestorOrigins[l-1]||"",X.g=!0)}m=k}var p=m;let Vc=new Fe(q.location.href,q,!1);m=null;const te=p.length-1;for(n=te;n>=0;--n){var v=p[n];!m&&De.test(v.url)&&(m=v);if(v.url&&!v.g){Vc=v;break}}v=null;const Jk=p.length&&p[te].url;Vc.depth!==0&&Jk&&(v=p[te]);f=new Ee(Vc,v);if(f.i){p=Za;var u=f.i.url||"";p.g.push(4);p.i[4]=Re("top",u)}var x={url:f.g.url||""};if(f.g.url){const X=f.g.url.match(Id); 
var K=X[1],O=X[3],Wc=X[4];u="";K&&(u+=K+":");O&&(u+="//",u+=O,Wc&&(u+=":"+Wc));var fc=u}else fc="";K=Za;x=[x,{url:fc}];K.g.push(5);K.i[5]=x;Ye(this.G,e,Za,this.B,c)}catch(Za){try{Ye(this.G,e,{context:"ecmserr",rctx:a,msg:Ce(Za),url:f?.g.url??""},this.B,c)}catch(Vc){}}return!0}ma(a,b){b.catch(c=>{c=c?c:"unknown rejection";this.I(a,c instanceof Error?c:Error(c),void 0,this.i||void 0)})}};var $e=class extends I{},af=[2,3,4];var bf=class extends I{},cf=[3,4,5],df=[6,7];var ef=class extends I{},ff=[4,5];function gf(a,b){var c=B(a,bf,2,z());if(!c.length)return hf(a,b);a=H(a,1);if(a===1)return c=gf(c[0],b),c.success?{success:!0,value:!c.value}:c;c=Ja(c,d=>gf(d,b));switch(a){case 2:return c.find(d=>d.success&&!d.value)??c.find(d=>!d.success)??{success:!0,value:!0};case 3:return c.find(d=>d.success&&d.value)??c.find(d=>!d.success)??{success:!0,value:!1};default:return{success:!1,O:3}}} 
function hf(a,b){var c=Jc(a,cf);a:{switch(c){case 3:var d=Yc(a,3,cf);break a;case 4:d=Yc(a,4,cf);break a;case 5:d=Yc(a,5,cf);break a}d=void 0}if(!d)return{success:!1,O:2};b=(b=b[c])&&b[d];if(!b)return{success:!1,property:d,da:c,O:1};let e;try{var f=Uc(a,8);e=b(...f)}catch(g){return{success:!1,property:d,da:c,O:2}}f=H(a,1);if(f===4)return{success:!0,value:!!e};if(f===5)return{success:!0,value:e!=null};if(f===12)a=G(a,Ic(a,df,7));else a:{switch(c){case 4:a=Tc(a,Ic(a,df,6));break a;case 5:a=G(a,Ic(a, 
df,7));break a}a=void 0}if(a==null)return{success:!1,property:d,da:c,O:3};if(f===6)return{success:!0,value:e===a};if(f===9)return{success:!0,value:e!=null&&ta(String(e),a)===0};if(e==null)return{success:!1,property:d,da:c,O:4};switch(f){case 7:c=e<a;break;case 8:c=e>a;break;case 12:c=wb(a)&&wb(e)&&(new RegExp(a)).test(e);break;case 10:c=e!=null&&ta(String(e),a)===-1;break;case 11:c=e!=null&&ta(String(e),a)===1;break;default:return{success:!1,O:3}}return{success:!0,value:c}} 
function jf(a,b){return a?b?gf(a,b):{success:!1,O:1}:{success:!0,value:!0}};var kf=class extends I{};var lf=class extends I{getValue(){return A(this,kf,2)}};var mf=class extends I{},nf=kd(mf),of=[1,2,3,6,7,8];var pf=class extends I{};function qf(a,b){try{const c=d=>[{[d.Aa]:d.ya}];return JSON.stringify([a.filter(d=>d.ka).map(c),w(b),a.filter(d=>!d.ka).map(c)])}catch(c){return rf(c,b),""}}function rf(a,b){try{me({m:Ce(a instanceof Error?a:Error(String(a))),b:H(b,1)||null,v:G(b,2)||null},"rcs_internal")}catch(c){}}var sf=class{constructor(a,b){var c=new pf;a=gd(c,1,a);b=ed(a,2,b);this.j=qc(b)}};var tf=class extends I{getWidth(){return F(this,3)}getHeight(){return F(this,4)}};var uf=class extends I{};function vf(a,b){return uc(a,1,b==null?b:Zb(b))}function wf(a,b){return uc(a,2,b==null?b:Zb(b))}var xf=class extends I{getWidth(){return Sc(this,1)}getHeight(){return Sc(this,2)}};var yf=class extends I{};var zf=class extends I{};var Af=class extends I{getValue(){return H(this,1)}};var Bf=class extends I{getContentUrl(){return G(this,4)}};var Cf=class extends I{};function Df(a){return Kc(a,Cf,3)}var Ef=class extends I{};var Ff=class extends I{getContentUrl(){return G(this,1)}};var Gf=class extends I{};function Hf(a){var b=new If;return gd(b,1,a)}var If=class extends I{};var Jf=class extends I{},Kf=[4,5,6,8,9,10,11,12,13,14,15,16,17];var Lf=class extends I{};function Mf(a,b){return gd(a,1,b)}function Nf(a,b){return gd(a,2,b)}var Of=class extends I{};var Pf=class extends I{},Qf=[1,2];function Rf(a,b){return Mc(a,1,b)}function Sf(a,b){return Oc(a,2,b)}function Tf(a,b){return Dc(a,4,b,Wb)}function Uf(a,b){return Oc(a,5,b)}function Vf(a,b){return gd(a,6,b)}var Wf=class extends I{};var Xf=class extends I{},Yf=[1,2,3,4,6];var Zf=class extends I{};function $f(a){var b=new ag;return Nc(b,4,bg,a)}var ag=class extends I{getTagSessionCorrelator(){return Sc(this,2)}},bg=[4,5,7,8,9];var cg=class extends I{};function dg(){var a=pc(eg());return ed(a,1,fg())}var gg=class extends I{};var hg=class extends I{};var ig=class extends I{getTagSessionCorrelator(){return Sc(this,1)}};var jg=class extends I{},kg=[1,7],lg=[4,6,8];class mg extends sf{constructor(){super(...arguments)}}function ng(a,...b){og(a,...b.map(c=>({ka:!0,Aa:3,ya:w(c)})))}function pg(a,...b){og(a,...b.map(c=>({ka:!0,Aa:4,ya:w(c)})))}function qg(a,...b){og(a,...b.map(c=>({ka:!0,Aa:7,ya:w(c)})))}var rg=class extends mg{};var sg=(a,b)=>{globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(()=>{})};function og(a,...b){try{a.D&&qf(a.g.concat(b),a.j).length>=65536&&tg(a),a.u&&!a.A&&(a.A=!0,ug(a.u,()=>{tg(a)})),a.g.push(...b),a.g.length>=a.B&&tg(a),a.g.length&&a.i===null&&(a.i=setTimeout(()=>{tg(a)},a.J))}catch(c){rf(c,a.j)}}function tg(a){a.i!==null&&(clearTimeout(a.i),a.i=null);if(a.g.length){var b=qf(a.g,a.j);a.G("https://pagead2.googlesyndication.com/pagead/ping?e=1",b);a.g=[]}} 
var vg=class extends rg{constructor(a,b,c,d,e,f){super(a,b);this.G=sg;this.J=c;this.B=d;this.D=e;this.u=f;this.g=[];this.i=null;this.A=!1}},wg=class extends vg{constructor(a,b,c=1E3,d=100,e=!1,f){super(a,b,c,d,e&&!0,f)}};function xg(a,b){var c=Date.now();c=Number.isFinite(c)?Math.round(c):0;b=bd(b,1,c);c=he(window);b=bd(b,2,c);return bd(b,6,a.A)}function yg(a,b,c,d,e,f){if(a.j){var g=Nf(Mf(new Of,b),c);b=Vf(Sf(Rf(Uf(Tf(new Wf,d),e),g),a.g.slice()),f);b=$f(b);pg(a.i,xg(a,b));if(f===1||f===3||f===4&&!a.g.some(h=>H(h,1)===H(g,1)&&H(h,2)===c))a.g.push(g),a.g.length>100&&a.g.shift()}}function zg(a,b,c,d){if(a.j){var e=new Lf;b=$c(e,1,b);c=$c(b,2,c);d=fd(c,3,d);c=new ag;d=Nc(c,8,bg,d);pg(a.i,xg(a,d))}} 
function Ag(a,b,c,d,e){if(a.j){var f=new ef;b=Mc(f,1,b);c=fd(b,2,c);d=$c(c,3,d);if(e.da===void 0)hd(d,4,ff,e.O);else switch(e.da){case 3:c=new $e;c=hd(c,2,af,e.property);e=fd(c,1,e.O);Nc(d,5,ff,e);break;case 4:c=new $e;c=hd(c,3,af,e.property);e=fd(c,1,e.O);Nc(d,5,ff,e);break;case 5:c=new $e,c=hd(c,4,af,e.property),e=fd(c,1,e.O),Nc(d,5,ff,e)}e=new ag;e=Nc(e,9,bg,d);pg(a.i,xg(a,e))}}var Bg=class{constructor(a,b,c,d=new wg(6,"unknown",b)){this.A=a;this.u=c;this.i=d;this.g=[];this.j=a>0&&Td()<1/a}};var J=a=>{var b="xa";if(a.xa&&a.hasOwnProperty(b))return a.xa;b=new a;return a.xa=b};var Cg=class{constructor(){this.N={[3]:{},[4]:{},[5]:{}}}};var Dg=/^true$/.test("false");function Eg(a,b){switch(b){case 1:return Yc(a,1,of);case 2:return Yc(a,2,of);case 3:return Yc(a,3,of);case 6:return Yc(a,6,of);case 8:return Yc(a,8,of);default:return null}}function Fg(a,b){if(!a)return null;switch(b){case 1:return E(a,1);case 7:return G(a,3);case 2:return Tc(a,2);case 3:return G(a,3);case 6:return Uc(a,4);case 8:return Uc(a,4);default:return null}} 
const Gg=od(()=>{if(!Dg)return{};try{var a=window;try{var b=a.sessionStorage.getItem("GGDFSSK")}catch{b=null}if(b)return JSON.parse(b)}catch{}return{}});function Hg(a,b,c,d=0){J(Ig).j[d]=J(Ig).j[d]?.add(b)??(new Set).add(b);const e=Gg();if(e[b]!=null)return e[b];b=Jg(d)[b];if(!b)return c;b=nf(JSON.stringify(b));b=Kg(b);a=Fg(b,a);return a!=null?a:c} 
function Kg(a){const b=J(Cg).N;if(b&&Jc(a,of)!==8){const c=La(B(a,lf,5,z()),d=>{d=jf(A(d,bf,1),b);return d.success&&d.value});if(c)return c.getValue()??null}return A(a,kf,4)??null}class Ig{constructor(){this.i={};this.u=[];this.j={};this.g=new Map}}function Lg(a,b=!1,c){return!!Hg(1,a,b,c)}function Ng(a,b=0,c){a=Number(Hg(2,a,b,c));return isNaN(a)?b:a}function Og(a,b="",c){a=Hg(3,a,b,c);return typeof a==="string"?a:b}function Pg(a,b=[],c){a=Hg(6,a,b,c);return Array.isArray(a)?a:b} 
function Qg(a,b=[],c){a=Hg(8,a,b,c);return Array.isArray(a)?a:b}function Jg(a){return J(Ig).i[a]||(J(Ig).i[a]={})}function Rg(a,b){const c=Jg(b);Ud(a,(d,e)=>{if(c[e]){const g=nf(JSON.stringify(d));if(D(g,Ic(g,of,8))!=null){var f=nf(JSON.stringify(c[e]));d=Kc(g,kf,4);f=Uc(Lc(f,kf,4),4);Pc(d,f)}c[e]=w(g)}else c[e]=d})} 
function Sg(a,b,c,d,e=!1){var f=[],g=[];for(const l of b){b=Jg(l);for(const p of a){var h=Jc(p,of);const v=Eg(p,h);if(v){a:{var k=v;var m=h,n=J(Ig).g.get(l)?.get(v)?.slice(0)??[];const u=new Xf;switch(m){case 1:hd(u,1,Yf,k);break;case 2:hd(u,2,Yf,k);break;case 3:hd(u,3,Yf,k);break;case 6:hd(u,4,Yf,k);break;case 8:hd(u,6,Yf,k);break;default:k=void 0;break a}Dc(u,5,n,Wb);k=u}k&&J(Ig).j[l]?.has(v)&&f.push(k);h===8&&b[v]?(k=nf(JSON.stringify(b[v])),h=Kc(p,kf,4),k=Uc(Lc(k,kf,4),4),Pc(h,k)):k&&J(Ig).g.get(l)?.has(v)&& 
g.push(k);e||(h=v,k=l,m=d,n=J(Ig),n.g.has(k)||n.g.set(k,new Map),n.g.get(k).has(h)||n.g.get(k).set(h,[]),m&&n.g.get(k).get(h).push(m));b[v]=w(p)}}}if(f.length||g.length)a=d??void 0,c.j&&c.u&&(d=new Zf,f=Oc(d,2,f),g=Oc(f,3,g),a&&ad(g,1,a),f=new ag,g=Nc(f,7,bg,g),pg(c.i,xg(c,g)))}function Tg(a,b){b=Jg(b);for(const c of a){a=nf(JSON.stringify(c));const d=Jc(a,of);(a=Eg(a,d))&&(b[a]||(b[a]=c))}}function Ug(){return Object.keys(J(Ig).i).map(a=>Number(a))} 
function Vg(a){J(Ig).u.includes(a)||Rg(Jg(4),a)};function M(a,b,c){c.hasOwnProperty(a)||Object.defineProperty(c,String(a),{value:b})}function Wg(a,b,c){return b[a]||c}function Xg(a){M(5,Lg,a);M(6,Ng,a);M(7,Og,a);M(8,Pg,a);M(17,Qg,a);M(13,Tg,a);M(15,Vg,a)}function Yg(a){M(4,b=>{J(Cg).N=b},a);M(9,(b,c)=>{var d=J(Cg);d.N[3][b]==null&&(d.N[3][b]=c)},a);M(10,(b,c)=>{var d=J(Cg);d.N[4][b]==null&&(d.N[4][b]=c)},a);M(11,(b,c)=>{var d=J(Cg);d.N[5][b]==null&&(d.N[5][b]=c)},a);M(14,b=>{var c=J(Cg);for(const d of[3,4,5])Object.assign(c.N[d],b[d])},a)} 
function Zg(a){a.hasOwnProperty("init-done")||Object.defineProperty(a,"init-done",{value:!0})};function $g(a,b,c){a.j=Wg(1,b,()=>{});a.u=(d,e)=>Wg(2,b,()=>[])(d,c,e);a.g=()=>Wg(3,b,()=>[])(c);a.i=d=>{Wg(16,b,()=>{})(d,c)}}class ah{j(){}i(){}u(){return[]}g(){return[]}};function Ye(a,b,c,d=!1,e){if((d?a.g:Math.random())<(e||.01))try{let f;c instanceof We?f=c:(f=new We,Ud(c,(h,k)=>{var m=f;const n=m.u++;h=Re(k,h);m.g.push(n);m.i[n]=h}));const g=Ve(f,a.domain,a.path+b+"&");g&&ke(q,g)}catch(f){}}function bh(a,b){b>=0&&b<=1&&(a.g=b)}var ch=class{constructor(){this.domain="pagead2.googlesyndication.com";this.path="/pagead/gen_204?id=";this.g=Math.random()}};let Xe,dh;const eh=new Qe(window);(function(a){Xe=a??new ch;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());bh(Xe,window.google_srt);dh=new Ze(eh);dh.A(()=>{});dh.Y(!0);window.document.readyState==="complete"?window.google_measure_js_timing||Pe(eh):eh.g&&qd(window,"load",()=>{window.google_measure_js_timing||Pe(eh)})})();let fh=(new Date).getTime();var gh={Xb:0,Wb:1,Tb:2,Ob:3,Ub:4,Pb:5,Vb:6,Rb:7,Sb:8,Nb:9,Qb:10,Yb:11};var hh={ac:0,bc:1,Zb:2};function ih(a){if(a.g!=0)throw Error("Already resolved/rejected.");}var lh=class{constructor(){this.i=new jh(this);this.g=0}resolve(a){ih(this);this.g=1;this.u=a;kh(this.i)}reject(a){ih(this);this.g=2;this.j=a;kh(this.i)}};function kh(a){switch(a.g.g){case 0:break;case 1:a.i&&a.i(a.g.u);break;case 2:a.j&&a.j(a.g.j);break;default:throw Error("Unhandled deferred state.");}}var jh=class{constructor(a){this.g=a}then(a,b){if(this.i)throw Error("Then functions already set.");this.i=a;this.j=b;kh(this)}};var mh=class{constructor(a){this.g=a.slice(0)}forEach(a){this.g.forEach((b,c)=>void a(b,c,this))}filter(a){return new mh(Ia(this.g,a))}apply(a){return new mh(a(this.g.slice(0)))}sort(a){return new mh(this.g.slice(0).sort(a))}get(a){return this.g[a]}add(a){const b=this.g.slice(0);b.push(a);return new mh(b)}};function nh(a,b){const c=[],d=a.length;for(let e=0;e<d;e++)c.push(a[e]);c.forEach(b,void 0)};var ph=class{constructor(){this.g={};this.i={}}set(a,b){const c=oh(a);this.g[c]=b;this.i[c]=a}get(a,b){a=oh(a);return this.g[a]!==void 0?this.g[a]:b}clear(){this.g={};this.i={}}};function oh(a){return a instanceof Object?String(ka(a)):a+""};function qh(a){return new rh({value:a},null)}function sh(a){return new rh(null,a)}function th(a){try{return qh(a())}catch(b){return sh(b)}}function uh(a){return a.g!=null?a.getValue():null}function vh(a,b){a.g!=null&&b(a.getValue());return a}function wh(a,b){a.g!=null||b(a.i);return a}var rh=class{constructor(a,b){this.g=a;this.i=b}getValue(){return this.g.value}map(a){return this.g!=null?(a=a(this.getValue()),a instanceof rh?a:qh(a)):this}};var xh=class{constructor(a){this.g=new ph;if(a)for(let b=0;b<a.length;++b)this.add(a[b])}add(a){this.g.set(a,!0)}contains(a){return this.g.g[oh(a)]!==void 0}};var yh=class{constructor(){this.g=new ph}set(a,b){let c=this.g.get(a);c||(c=new xh,this.g.set(a,c));c.add(b)}};var N=class extends I{getId(){return C(this,3)}};var zh=class{constructor({jb:a,ec:b,sc:c,Eb:d}){this.g=b;this.u=new mh(a||[]);this.j=d;this.i=c}};const Bh=a=>{const b=[],c=a.u;c&&c.g.length&&b.push({ba:"a",ca:Ah(c)});a.g!=null&&b.push({ba:"as",ca:a.g});a.i!=null&&b.push({ba:"i",ca:String(a.i)});a.j!=null&&b.push({ba:"rp",ca:String(a.j)});b.sort(function(d,e){return d.ba.localeCompare(e.ba)});b.unshift({ba:"t",ca:"aa"});return b},Ah=a=>{a=a.g.slice(0).map(Ch);a=JSON.stringify(a);return Vd(a)},Ch=a=>{const b={};C(a,7)!=null&&(b.q=C(a,7));Rc(a,2)!=null&&(b.o=Rc(a,2));Rc(a,5)!=null&&(b.p=Rc(a,5));return b};var Dh=class extends I{setLocation(a){return fd(this,1,a)}};function Eh(a){const b=[].slice.call(arguments).filter(nd(e=>e===null));if(!b.length)return null;let c=[],d={};b.forEach(e=>{c=c.concat(e.Ra||[]);d=Object.assign(d,e.Ya)});return new Fh(c,d)}function Gh(a){switch(a){case 1:return new Fh(null,{google_ad_semantic_area:"mc"});case 2:return new Fh(null,{google_ad_semantic_area:"h"});case 3:return new Fh(null,{google_ad_semantic_area:"f"});case 4:return new Fh(null,{google_ad_semantic_area:"s"});default:return null}} 
function Hh(a){if(a==null)var b=null;else{b=Fh;var c=Bh(a);a=[];for(let d of c)c=String(d.ca),a.push(d.ba+"."+(c.length<=20?c:c.slice(0,19)+"_"));b=new b(null,{google_placement_id:a.join("~")})}return b}var Fh=class{constructor(a,b){this.Ra=a;this.Ya=b}};var Ih=new Fh(["google-auto-placed"],{google_reactive_ad_format:40,google_tag_origin:"qs"});var Jh=kd(class extends I{});var Kh=class extends I{};var Lh=class extends I{};var Mh=class extends I{};function Nh(a){if(a.nodeType!=1)var b=!1;else if(b=a.tagName=="INS")a:{b=["adsbygoogle-placeholder"];var c=a.className?a.className.split(/\s+/):[];a={};for(let d=0;d<c.length;++d)a[c[d]]=!0;for(c=0;c<b.length;++c)if(!a[b[c]]){b=!1;break a}b=!0}return b};function Oh(a,b,c){switch(c){case 0:b.parentNode&&b.parentNode.insertBefore(a,b);break;case 3:if(c=b.parentNode){var d=b.nextSibling;if(d&&d.parentNode!=c)for(;d&&d.nodeType==8;)d=d.nextSibling;c.insertBefore(a,d)}break;case 1:b.insertBefore(a,b.firstChild);break;case 2:b.appendChild(a)}Nh(b)&&(b.setAttribute("data-init-display",b.style.display),b.style.display="block")};var P=class{constructor(a,b=!1){this.g=a;this.defaultValue=b}},Q=class{constructor(a,b=0){this.g=a;this.defaultValue=b}},Ph=class{constructor(a,b=[]){this.g=a;this.defaultValue=b}};var Qh=new P(1333),Rh=new Q(1359),Sh=new Q(1358),Th=new P(1360),Uh=new Q(1357),Vh=new P(1345),Wh=new P(1332),Xh=new Q(1130,100),Yh=new Q(1340,.2),Zh=new Q(1338,.3),$h=new Q(1336,1.2),ai=new Q(1339,.3),bi=new P(1337),ci=new class{constructor(a,b=""){this.g=a;this.defaultValue=b}}(14),di=new P(1342),ei=new P(1344),fi=new Q(1343,300),gi=new P(316),hi=new P(313),ii=new P(369),ji=new P(1318,!0),ki=new P(626390500),li=new Q(717888910,.5),mi=new Ph(627094447,"1 2 3 4 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 24 29 30 34".split(" ")), 
ni=new Q(717888911),oi=new Ph(641845510,["33","38"]),pi=new Ph(635821288,["29_18","30_19"]),qi=new Ph(636146137,["29_5","30_6"]),ri=new Q(717888912,.6),si=new Ph(627094446,"1 2 4 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 24 29 30 34".split(" ")),ti=new Ph(683929765),ui=new P(506914611),vi=new P(739956550),wi=new Q(643258048,.15),xi=new Q(643258049,.33938),yi=new P(711741274),zi=new P(45650663),Ai=new Q(684147711,-1),Bi=new Q(684147712,-1),Ci=new P(*********),Di=new P(45675667),Ei=new Q(1079,5),Fi= 
new P(10013),fe=new class{constructor(a,b=[]){this.g=a;this.defaultValue=b}}(1934,["AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==","Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==", 
"A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9","A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"]), 
Gi=new P(84);var ee=class{constructor(){const a={};this.i=(b,c)=>a[b]!=null?a[b]:c;this.u=(b,c)=>a[b]!=null?a[b]:c;this.g=(b,c)=>a[b]!=null?a[b]:c;this.A=(b,c)=>a[b]!=null?a[b]:c;this.j=(b,c)=>a[b]!=null?c.concat(a[b]):c;this.B=()=>{}}};function R(a){return J(ee).i(a.g,a.defaultValue)}function S(a){return J(ee).u(a.g,a.defaultValue)}function Hi(a){return J(ee).j(a.g,a.defaultValue)};function Ii(a,b){const c=e=>{e=Ji(e);return e==null?!1:0<e},d=e=>{e=Ji(e);return e==null?!1:0>e};switch(b){case 0:return{init:Ki(a.previousSibling,c),ga:e=>Ki(e.previousSibling,c),la:0};case 2:return{init:Ki(a.lastChild,c),ga:e=>Ki(e.previousSibling,c),la:0};case 3:return{init:Ki(a.nextSibling,d),ga:e=>Ki(e.nextSibling,d),la:3};case 1:return{init:Ki(a.firstChild,d),ga:e=>Ki(e.nextSibling,d),la:3}}throw Error("Un-handled RelativePosition: "+b);} 
function Ji(a){return a.hasOwnProperty("google-ama-order-assurance")?a["google-ama-order-assurance"]:null}function Ki(a,b){return a&&b(a)?a:null};var Li={rectangle:1,horizontal:2,vertical:4};var Mi={overlays:1,interstitials:2,vignettes:2,inserts:3,immersives:4,list_view:5,full_page:6,side_rails:7};function Ni(a){a=a.document;let b={};a&&(b=a.compatMode=="CSS1Compat"?a.documentElement:a.body);return b||{}}function T(a){return Ni(a).clientWidth??void 0};function Oi(a,b){do{const c=Sd(a,b);if(c&&c.position=="fixed")return!1}while(a=a.parentElement);return!0};function Pi(a,b){var c=["width","height"];for(let e=0;e<c.length;e++){const f="google_ad_"+c[e];if(!b.hasOwnProperty(f)){var d=Zd(a[c[e]]);d=d===null?null:Math.round(d);d!=null&&(b[f]=d)}}}function Qi(a,b){return!((Xd.test(b.google_ad_width)||Wd.test(a.style.width))&&(Xd.test(b.google_ad_height)||Wd.test(a.style.height)))}function Ri(a,b){return(a=Si(a,b))?a.y:0} 
function Si(a,b){try{const c=b.document.documentElement.getBoundingClientRect(),d=a.getBoundingClientRect();return{x:d.left-c.left,y:d.top-c.top}}catch(c){return null}} 
function Ti(a,b,c,d,e){if(a!==a.top)return Pd(a)?3:16;if(!(T(a)<488))return 4;if(!(a.innerHeight>=a.innerWidth))return 5;const f=T(a);if(!f||(f-c)/f>d)a=6;else{if(c=e.google_full_width_responsive!=="true")a:{c=b.parentElement;for(b=T(a);c;c=c.parentElement)if((d=Sd(c,a))&&(e=Zd(d.width))&&!(e>=b)&&d.overflow!=="visible"){c=!0;break a}c=!1}a=c?7:!0}return a} 
function Ui(a,b,c,d){const e=Ti(b,c,a,S(ai),d);e!==!0?a=e:d.google_full_width_responsive==="true"||Oi(c,b)?(b=T(b),a=b-a,a=b&&a>=0?!0:b?a<-10?11:a<0?14:12:10):a=9;return a}function Vi(a,b,c){a=a.style;b==="rtl"?a.marginRight=c:a.marginLeft=c} 
function Wi(a,b){if(b.nodeType===3)return/\S/.test(b.data);if(b.nodeType===1){if(/^(script|style)$/i.test(b.nodeName))return!1;let c;try{c=Sd(b,a)}catch(d){}return!c||c.display!=="none"&&!(c.position==="absolute"&&(c.visibility==="hidden"||c.visibility==="collapse"))}return!1}function Xi(a,b,c){a=Si(b,a);return c==="rtl"?-a.x:a.x} 
function Yi(a,b){var c;c=(c=b.parentElement)?(c=Sd(c,a))?c.direction:"":"";if(c){var d=b.style;d.border=d.borderStyle=d.outline=d.outlineStyle=d.transition="none";d.borderSpacing=d.padding="0";Vi(b,c,"0px");d.width=`${T(a)}px`;if(Xi(a,b,c)!==0){Vi(b,c,"0px");var e=Xi(a,b,c);Vi(b,c,`${-1*e}px`);a=Xi(a,b,c);a!==0&&a!==e&&Vi(b,c,`${e/(a-e)*e}px`)}d.zIndex="30"}};function Zi(a,b,c){let d;return a.style&&!!a.style[c]&&Zd(a.style[c])||(d=Sd(a,b))&&!!d[c]&&Zd(d[c])||null}function $i(a,b){const c=xe(a)===0;return b&&c?Math.max(250,2*Ni(a).clientHeight/3):250}function aj(a,b){let c;return a.style&&a.style.zIndex||(c=Sd(a,b))&&c.zIndex||null}function bj(a){return b=>b.g<=a}function cj(a,b,c,d){const e=a&&dj(c,b),f=$i(b,d);return g=>!(e&&g.height()>=f)}function ej(a){return b=>b.height()<=a}function dj(a,b){return Ri(a,b)<Ni(b).clientHeight-100} 
function fj(a,b){var c=Zi(b,a,"height");if(c)return c;var d=b.style.height;b.style.height="inherit";c=Zi(b,a,"height");b.style.height=d;if(c)return c;c=Infinity;do(d=b.style&&Zd(b.style.height))&&(c=Math.min(c,d)),(d=Zi(b,a,"maxHeight"))&&(c=Math.min(c,d));while(b.parentElement&&(b=b.parentElement)&&b.tagName!=="HTML");return c};var gj={google_ad_channel:!0,google_ad_client:!0,google_ad_host:!0,google_ad_host_channel:!0,google_adtest:!0,google_tag_for_child_directed_treatment:!0,google_tag_for_under_age_of_consent:!0,google_tag_partner:!0,google_restrict_data_processing:!0,google_page_url:!0,google_debug_params:!0,google_adbreak_test:!0,google_ad_frequency_hint:!0,google_admob_interstitial_slot:!0,google_admob_rewarded_slot:!0,google_admob_ads_only:!0,google_ad_start_delay_hint:!0,google_max_ad_content_rating:!0,google_traffic_source:!0, 
google_overlays:!0,google_privacy_treatments:!0,google_special_category_data:!0,google_ad_intent_query:!0,google_ad_intent_qetid:!0,google_ad_intent_eids:!0,google_ad_intents_format:!0};const hj=RegExp("(^| )adsbygoogle($| )");function ij(a,b){for(let c=0;c<b.length;c++){const d=b[c],e=Hd(d.property);a[e]=d.value}};var jj=class extends I{g(){return Rb(sc(this,23))}};var kj=class extends I{g(){return Qc(this,1)??void 0}};var lj=class extends I{};var mj=class extends I{};var nj=class extends I{};var oj=class extends I{};var pj=class extends I{getName(){return C(this,4)}},qj=[1,2,3];var rj=class extends I{};var sj=class extends I{};var uj=class extends I{g(){return Zc(this,sj,2,tj)}},tj=[1,2];var vj=class extends I{g(){return A(this,uj,3)}};var wj=class extends I{},xj=kd(wj);function yj(a){const b=[];nh(a.getElementsByTagName("p"),function(c){zj(c)>=100&&b.push(c)});return b}function zj(a){if(a.nodeType==3)return a.length;if(a.nodeType!=1||a.tagName=="SCRIPT")return 0;let b=0;nh(a.childNodes,function(c){b+=zj(c)});return b}function Aj(a){return a.length==0||isNaN(a[0])?a:"\\"+(30+parseInt(a[0],10))+" "+a.substring(1)} 
function Bj(a,b){if(a.g==null)return b;switch(a.g){case 1:return b.slice(1);case 2:return b.slice(0,b.length-1);case 3:return b.slice(1,b.length-1);case 0:return b;default:throw Error("Unknown ignore mode: "+a.g);}} 
function Cj(a,b){var c=[];try{c=b.querySelectorAll(a.u)}catch(d){}if(!c.length)return[];b=Na(c);b=Bj(a,b);typeof a.i==="number"&&(c=a.i,c<0&&(c+=b.length),b=c>=0&&c<b.length?[b[c]]:[]);if(typeof a.j==="number"){c=[];for(let d=0;d<b.length;d++){const e=yj(b[d]);let f=a.j;f<0&&(f+=e.length);f>=0&&f<e.length&&c.push(e[f])}b=c}return b} 
var Dj=class{constructor(a,b,c,d){this.u=a;this.i=b;this.j=c;this.g=d}toString(){return JSON.stringify({nativeQuery:this.u,occurrenceIndex:this.i,paragraphIndex:this.j,ignoreMode:this.g})}};var Ej=class{constructor(){this.i=Ld`https://pagead2.googlesyndication.com/pagead/js/err_rep.js`}I(a,b,c=.01,d="jserror"){if(Math.random()>c)return!1;ze(b)||(b=new Ae(b,{context:a,id:d}));q.google_js_errors=q.google_js_errors||[];q.google_js_errors.push(b);q.error_rep_loaded||(Qd(q.document,this.i),q.error_rep_loaded=!0);return!1}g(a,b){try{return b()}catch(c){if(!this.I(a,c,.01,"jserror"))throw c;}}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}ma(a,b){b.catch(c=>{c=c?c:"unknown rejection"; 
this.I(a,c instanceof Error?c:Error(c),void 0)})}};function Fj(a,b){b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];b.length<2048&&b.push(a)} 
function Gj(a,b,c,d,e=!1){const f=d||window,g=typeof queueMicrotask!=="undefined";return function(...h){e&&g&&queueMicrotask(()=>{f.google_rum_task_id_counter=f.google_rum_task_id_counter||1;f.google_rum_task_id_counter+=1});const k=Je();let m,n=3;try{m=b.apply(this,h)}catch(l){n=13;if(!c)throw l;c(a,l)}finally{f.google_measure_js_timing&&k&&Fj({label:a.toString(),value:k,duration:(Je()||0)-k,type:n,...(e&&g&&{taskId:f.google_rum_task_id_counter=f.google_rum_task_id_counter||1})},f)}return m}} 
function Hj(a,b){return Gj(a,b,(c,d)=>{(new Ej).I(c,d)},void 0,!1)};function Ij(a,b,c){return Gj(a,b,void 0,c,!0).apply()}function Jj(a){if(!a)return null;var b=C(a,7);if(C(a,1)||a.getId()||Uc(a,4).length>0){var c=a.getId(),d=C(a,1),e=Uc(a,4);b=Rc(a,2);var f=Rc(a,5);a=Kj(D(a,6));let g="";d&&(g+=d);c&&(g+="#"+Aj(c));if(e)for(c=0;c<e.length;c++)g+="."+Aj(e[c]);b=(e=g)?new Dj(e,b,f,a):null}else b=b?new Dj(b,Rc(a,2),Rc(a,5),Kj(D(a,6))):null;return b}const Lj={1:1,2:2,3:3,0:0};function Kj(a){return a==null?a:Lj[a]}const Mj={1:0,2:1,3:2,4:3}; 
function Nj(a){return a.google_ama_state=a.google_ama_state||{}}function Oj(a){a=Nj(a);return a.optimization=a.optimization||{}};var Pj=a=>{switch(D(a,8)){case 1:case 2:if(a==null)var b=null;else b=A(a,N,1),b==null?b=null:(a=D(a,2),b=a==null?null:new zh({jb:[b],Eb:a}));return b!=null?qh(b):sh(Error("Missing dimension when creating placement id"));case 3:return sh(Error("Missing dimension when creating placement id"));default:return sh(Error("Invalid type: "+D(a,8)))}};var Qj=(a,b)=>{const c=[];let d=a;for(a=()=>{c.push({anchor:d.anchor,position:d.position});return d.anchor==b.anchor&&d.position==b.position};d;){switch(d.position){case 1:if(a())return c;d.position=2;case 2:if(a())return c;if(d.anchor.firstChild){d={anchor:d.anchor.firstChild,position:1};continue}else d.position=3;case 3:if(a())return c;d.position=4;case 4:if(a())return c}for(;d&&!d.anchor.nextSibling&&d.anchor.parentNode!=d.anchor.ownerDocument.body;){d={anchor:d.anchor.parentNode,position:3};if(a())return c; 
d.position=4;if(a())return c}d&&d.anchor.nextSibling?d={anchor:d.anchor.nextSibling,position:1}:d=null}return c};function Rj(a,b){const c=new yh,d=new xh;b.forEach(e=>{if(Zc(e,nj,1,qj)){e=Zc(e,nj,1,qj);if(A(e,Kh,1)&&A(A(e,Kh,1),N,1)&&A(e,Kh,2)&&A(A(e,Kh,2),N,1)){const g=Sj(a,A(A(e,Kh,1),N,1)),h=Sj(a,A(A(e,Kh,2),N,1));if(g&&h)for(var f of Qj({anchor:g,position:D(A(e,Kh,1),2)},{anchor:h,position:D(A(e,Kh,2),2)}))c.set(ka(f.anchor),f.position)}A(e,Kh,3)&&A(A(e,Kh,3),N,1)&&(f=Sj(a,A(A(e,Kh,3),N,1)))&&c.set(ka(f),D(A(e,Kh,3),2))}else Zc(e,oj,2,qj)?Tj(a,Zc(e,oj,2,qj),c):Zc(e,mj,3,qj)&&Uj(a,Zc(e,mj,3,qj),d)});return new Vj(c, 
d)}var Vj=class{constructor(a,b){this.i=a;this.g=b}};const Tj=(a,b,c)=>{A(b,Kh,2)?(b=A(b,Kh,2),(a=Sj(a,A(b,N,1)))&&c.set(ka(a),D(b,2))):A(b,N,1)&&(a=Wj(a,A(b,N,1)))&&a.forEach(d=>{d=ka(d);c.set(d,1);c.set(d,4);c.set(d,2);c.set(d,3)})},Uj=(a,b,c)=>{A(b,N,1)&&(a=Wj(a,A(b,N,1)))&&a.forEach(d=>{c.add(ka(d))})},Sj=(a,b)=>(a=Wj(a,b))&&a.length>0?a[0]:null,Wj=(a,b)=>(b=Jj(b))?Cj(b,a):null;function fg(){return"m202503260101"};var Xj=jd(cg);var eg=jd(gg);function Yj(a,b){return b(a)?a:void 0} 
function Zj(a,b,c,d,e){c=c instanceof Ae?c.error:c;var f=new jg;const g=new ig;try{var h=he(window);bd(g,1,h)}catch(p){}try{var k=J(ah).g();Dc(g,2,k,Wb)}catch(p){}try{ed(g,3,window.document.URL)}catch(p){}h=Mc(f,2,g);k=new hg;b=gd(k,1,b);try{var m=wb(c?.name)?c.name:"Unknown error";ed(b,2,m)}catch(p){}try{var n=wb(c?.message)?c.message:`Caught ${c}`;ed(b,3,n)}catch(p){}try{var l=wb(c?.stack)?c.stack:Error().stack;l&&Dc(b,4,l.split(/\n\s*/),dc)}catch(p){}m=Nc(h,1,kg,b);if(e){n=0;switch(e.errSrc){case "LCC":n= 
1;break;case "PVC":n=2}l=dg();b=Yj(e.shv,wb);l=ed(l,2,b);n=gd(l,6,n);l=Xj();l=pc(l);b=Yj(e.es,zb());l=Dc(l,1,b,Wb);l=qc(l);n=Mc(n,4,l);l=Yj(e.client,wb);n=dd(n,3,l);l=Yj(e.slotname,wb);n=ed(n,7,l);e=Yj(e.tag_origin,wb);e=ed(n,8,e);e=qc(e)}else e=qc(dg());e=Nc(m,6,lg,e);d=bd(e,5,d??1);ng(a,d)};let ak,bk=64;function ck(){try{return ak??(ak=new Uint32Array(64)),bk>=64&&(crypto.getRandomValues(ak),bk=0),ak[bk++]}catch(a){return Math.floor(Math.random()*2**32)}};var ek=class{constructor(){this.g=dk}};function dk(){return{Bb:ck()+(ck()&2**21-1)*2**32,qb:Number.MAX_SAFE_INTEGER}};var hk=class{constructor(a=!1){var b=fk;this.D=gk;this.B=a;this.G=b;this.i=null;this.j=this.I}J(a){this.j=a}A(a){this.i=a}Y(){}g(a,b,c){let d;try{d=b()}catch(e){b=this.B;try{b=this.j(a,Be(e),void 0,c)}catch(f){this.I(217,f)}if(b)window.console?.error?.(e);else throw e;}return d}u(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}ma(a,b){b.catch(c=>{c=c?c:"unknown rejection";this.I(a,c instanceof Error?c:Error(c),void 0,void 0)})}I(a,b,c,d){try{const g=c===void 0?1/this.G:c===0?0:1/c;var e=(new ek).g(); 
if(g>0&&e.Bb*g<=e.qb){var f=this.D;c={};if(this.i)try{this.i(c)}catch(h){}if(d)try{d(c)}catch(h){}Zj(f,a,b,g,c)}}catch(g){}return this.B}};var U=class extends Error{constructor(a=""){super();this.name="TagError";this.message=a?"adsbygoogle.push() error: "+a:"";Error.captureStackTrace?Error.captureStackTrace(this,U):this.stack=Error().stack||""}};let gk,ik,jk,kk,fk;const lk=new Qe(q);function mk(a){a!=null&&(q.google_measure_js_timing=a);q.google_measure_js_timing||Pe(lk)}(function(a,b,c=!0){({Db:fk,tb:jk}=nk());ik=a||new ch;bh(ik,jk);gk=b||new wg(2,fg(),1E3);kk=new hk(c);q.document.readyState==="complete"?mk():lk.g&&qd(q,"load",()=>{mk()})})();function ok(a,b,c){return kk.g(a,b,c)}function pk(a,b){return kk.u(a,b)}function qk(a,b){kk.ma(a,b)}function rk(a,b,c=.01){const d=J(ah).g();!b.eid&&d.length&&(b.eid=d.toString());Ye(ik,a,b,!0,c)} 
function sk(a,b,c=fk,d,e){return kk.I(a,b,c,d,e)}function tk(a,b,c=fk,d,e){return(ze(b)?b.msg||Ce(b.error):Ce(b)).indexOf("TagError")===0?((ze(b)?b.error:b).pbr=!0,!1):sk(a,b,c,d,e)}function nk(){let a,b;typeof q.google_srt==="number"?(b=q.google_srt,a=q.google_srt===0?1:.01):(b=Math.random(),a=.01);return{Db:a,tb:b}};var uk=class{constructor(){this.g=ge();this.i=0}};function vk(a,b,c){switch(c){case 2:case 3:break;case 1:case 4:b=b.parentElement;break;default:throw Error("Unknown RelativePosition: "+c);}for(c=[];b;){if(wk(b))return!0;if(a.g.has(b))break;c.push(b);b=b.parentElement}c.forEach(d=>a.g.add(d));return!1}function xk(a){a=yk(a);return a.has("all")||a.has("after")}function zk(a){a=yk(a);return a.has("all")||a.has("before")}function yk(a){return(a=a&&a.getAttribute("data-no-auto-ads"))?new Set(a.split("|")):new Set} 
function wk(a){const b=yk(a);return a&&(a.tagName==="AUTO-ADS-EXCLUSION-AREA"||b.has("inside")||b.has("all"))}var Ak=class{constructor(){this.g=new Set;this.i=new uk}};function Bk(a,b){if(!a)return!1;a=Sd(a,b);if(!a)return!1;a=a.cssFloat||a.styleFloat;return a=="left"||a=="right"}function Ck(a){for(a=a.previousSibling;a&&a.nodeType!=1;)a=a.previousSibling;return a?a:null}function Dk(a){return!!a.nextSibling||!!a.parentNode&&Dk(a.parentNode)};function Ek(a=null){({googletag:a}=a??window);return a?.apiReady?a:void 0};function Fk(a){return{dc:Gk(a),fc:V(a,"body ins.adsbygoogle"),gb:Hk(a),hb:V(a,".google-auto-placed"),ib:Ik(a),rb:Kk(a),kc:Lk(a),uc:Mk(a),Ab:Nk(a),jc:V(a,"div.googlepublisherpluginad"),Lb:V(a,"html > ins.adsbygoogle")}}function Lk(a){return Ok(a)||V(a,"div[id^=div-gpt-ad]")}function Ok(a){const b=Ek(a);return b?Ia(Ja(b.pubads().getSlots(),c=>a.document.getElementById(c.getSlotElementId())),c=>c!=null):null}function V(a,b){return Na(a.document.querySelectorAll(b))} 
function Ik(a){return V(a,"ins.adsbygoogle[data-anchor-status]")}function Hk(a){return V(a,"iframe[id^=aswift_],iframe[id^=google_ads_frame]")}function Mk(a){return V(a,"ins.adsbygoogle[data-ad-format=autorelaxed]")}function Kk(a){return Lk(a).concat(V(a,"iframe[id^=google_ads_iframe]"))} 
function Nk(a){return V(a,"div.trc_related_container,div.OUTBRAIN,div[id^=rcjsload],div[id^=ligatusframe],div[id^=crt-],iframe[id^=cto_iframe],div[id^=yandex_], div[id^=Ya_sync],iframe[src*=adnxs],div.advertisement--appnexus,div[id^=apn-ad],div[id^=amzn-native-ad],iframe[src*=amazon-adsystem],iframe[id^=ox_],iframe[src*=openx],img[src*=openx],div[class*=adtech],div[id^=adtech],iframe[src*=adtech],div[data-content-ad-placement=true],div.wpcnt div[id^=atatags-]")} 
function Gk(a){return V(a,"ins.adsbygoogle-ablated-ad-slot")}function Pk(a){const b=[];for(const c of a){a=!0;for(let d=0;d<b.length;d++){const e=b[d];if(e.contains(c)){a=!1;break}if(c.contains(e)){a=!1;b[d]=c;break}}a&&b.push(c)}return b};function Qk(a,b){if(a.u)return!0;a.u=!0;const c=B(a.j,Mh,1,z());a.i=0;const d=Rk(a.G);var e=a.g;var f;try{var g=(f=e.localStorage.getItem("google_ama_settings"))?Jh(f):null}catch(p){g=null}f=g!==null&&E(g,2);g=Nj(e);f&&(g.eatf=!0,pe(7,[!0,0,!1]));b:{var h={vb:!1,wb:!1},k=V(e,".google-auto-placed"),m=Ik(e),n=Mk(e);const p=Kk(e),v=Nk(e),u=Gk(e),x=V(e,"div.googlepublisherpluginad"),K=V(e,"html > ins.adsbygoogle");let O=[].concat(...Hk(e),...V(e,"body ins.adsbygoogle"));f=[];for(const [Wc,fc]of[[h.mc, 
k],[h.vb,m],[h.qc,n],[h.nc,p],[h.rc,v],[h.lc,u],[h.oc,x],[h.wb,K]])Wc===!1?f=f.concat(fc):O=O.concat(fc);h=Pk(O);f=Pk(f);h=h.slice(0);for(l of f)for(f=0;f<h.length;f++)(l.contains(h[f])||h[f].contains(l))&&h.splice(f,1);var l=h;e=Ni(e).clientHeight;for(f=0;f<l.length;f++)if(!(l[f].getBoundingClientRect().top>e)){e=!0;break b}e=!1}e=e?g.eatfAbg=!0:!1;if(e)return!0;e=new xh([2]);for(g=0;g<c.length;g++){l=a;m=c[g];f=g;h=b;if(A(m,Dh,4)&&e.contains(D(A(m,Dh,4),1))&&D(m,8)===1&&Sk(m,d)){l.i++;if(h=Tk(l, 
m,h,d))k=Nj(l.g),k.numAutoAdsPlaced||(k.numAutoAdsPlaced=0),(n=!A(m,N,1))||(m=A(m,N,1),n=(Rc(m,5)??void 0)==null),n||(k.numPostPlacementsPlaced?k.numPostPlacementsPlaced++:k.numPostPlacementsPlaced=1),k.placed==null&&(k.placed=[]),k.numAutoAdsPlaced++,k.placed.push({index:f,element:h.ea}),pe(7,[!1,l.i,!0]);l=h}else l=null;if(l)return!0}pe(7,[!1,a.i,!1]);return!1} 
function Tk(a,b,c,d){if(!Sk(b,d)||(D(b,8)??void 0)!=1)return null;d=A(b,N,1);if(!d)return null;d=Jj(d);if(!d)return null;d=Cj(d,a.g.document);if(d.length==0)return null;d=d[0];var e=D(b,2);e=Mj[e];e=e===void 0?null:e;var f;if(!(f=e==null)){a:{f=a.g;switch(e){case 0:f=Bk(Ck(d),f);break a;case 3:f=Bk(d,f);break a;case 2:var g=d.lastChild;f=Bk(g?g.nodeType==1?g:Ck(g):null,f);break a}f=!1}if(c=!f&&!(!c&&e==2&&!Dk(d)))c=e==1||e==2?d:d.parentNode,c=!(c&&!Nh(c)&&c.offsetWidth<=0);f=!c}if(!(c=f)){c=a.B;f= 
D(b,2);g=c.i;var h=ka(d);g=g.g.get(h);if(!(g=g?g.contains(f):!1))a:{if(c.g.contains(ka(d)))switch(f){case 2:case 3:g=!0;break a;default:g=!1;break a}for(f=d.parentElement;f;){if(c.g.contains(ka(f))){g=!0;break a}f=f.parentElement}g=!1}c=g}if(!c){c=a.D;g=D(b,2);a:switch(g){case 1:f=xk(d.previousElementSibling)||zk(d);break a;case 4:f=xk(d)||zk(d.nextElementSibling);break a;case 2:f=zk(d.firstElementChild);break a;case 3:f=xk(d.lastElementChild);break a;default:throw Error("Unknown RelativePosition: "+ 
g);}g=vk(c,d,g);c=c.i;rk("ama_exclusion_zone",{typ:f?g?"siuex":"siex":g?"suex":"noex",cor:c.g,num:c.i++,dvc:$d()},.1);c=f||g}if(c)return null;f=A(b,Lh,3);c={};f&&(c.bb=C(f,1),c.Pa=C(f,2),c.ob=!!Rb(sc(f,3)));f=A(b,Dh,4)&&D(A(b,Dh,4),2)?D(A(b,Dh,4),2):null;f=Gh(f);g=Rc(b,12)!=null?Rc(b,12):null;g=g==null?null:new Fh(null,{google_ml_rank:g});b=Uk(a,b);b=Eh(a.A,f,g,b);f=a.g;a=a.J;h=f.document;var k=c.ob||!1;g=ue((new ve(h)).g,"DIV");const m=g.style;m.width="100%";m.height="auto";m.clear=k?"both":"none"; 
k=g.style;k.textAlign="center";c.Cb&&ij(k,c.Cb);h=ue((new ve(h)).g,"INS");k=h.style;k.display="block";k.margin="auto";k.backgroundColor="transparent";c.bb&&(k.marginTop=c.bb);c.Pa&&(k.marginBottom=c.Pa);c.fb&&ij(k,c.fb);g.appendChild(h);c={va:g,ea:h};c.ea.setAttribute("data-ad-format","auto");g=[];if(h=b&&b.Ra)c.va.className=h.join(" ");h=c.ea;h.className="adsbygoogle";h.setAttribute("data-ad-client",a);g.length&&h.setAttribute("data-ad-channel",g.join("+"));a:{try{var n=c.va;if(R(hi)){{const x=Ii(d, 
e);if(x.init){var l=x.init;for(d=l;d=x.ga(d);)l=d;var p={anchor:l,position:x.la}}else p={anchor:d,position:e}}n["google-ama-order-assurance"]=0;Oh(n,p.anchor,p.position)}else Oh(n,d,e);b:{var v=c.ea;v.dataset.adsbygoogleStatus="reserved";v.className+=" adsbygoogle-noablate";n={element:v};var u=b&&b.Ya;if(v.hasAttribute("data-pub-vars")){try{u=JSON.parse(v.getAttribute("data-pub-vars"))}catch(x){break b}v.removeAttribute("data-pub-vars")}u&&(n.params=u);(f.adsbygoogle=f.adsbygoogle||[]).push(n)}}catch(x){(v= 
c.va)&&v.parentNode&&(u=v.parentNode,u.removeChild(v),Nh(u)&&(u.style.display=u.getAttribute("data-init-display")||"none"));v=!1;break a}v=!0}return v?c:null}function Uk(a,b){return uh(wh(Pj(b).map(Hh),c=>{Nj(a.g).exception=c}))}var Vk=class{constructor(a,b,c,d,e){this.g=a;this.J=b;this.j=c;this.A=e||null;(this.G=d)?(a=a.document,d=B(d,pj,5,z()),d=Rj(a,d)):d=Rj(a.document,[]);this.B=d;this.D=new Ak;this.i=0;this.u=!1}};function Rk(a){const b={};a&&Xc(a,6).forEach(c=>{b[c]=!0});return b} 
function Sk(a,b){return a&&vc(a,Dh,4)&&b[D(A(a,Dh,4),2)]?!1:!0};var Wk=kd(class extends I{});function Xk(a){try{var b=a.localStorage.getItem("google_auto_fc_cmp_setting")||null}catch(d){b=null}const c=b;return c?th(()=>Wk(c)):qh(null)};function Yk(){if(Zk)return Zk;var a=se()||window;const b=a.google_persistent_state_async;return b!=null&&typeof b=="object"&&b.S!=null&&typeof b.S=="object"?Zk=b:a.google_persistent_state_async=Zk=new $k}function al(a){return bl[a]||`google_ps_${a}`}function cl(a,b,c){b=al(b);a=a.S;const d=a[b];return d===void 0?(a[b]=c(),a[b]):d}function dl(a,b,c){return cl(a,b,()=>c)}var $k=class{constructor(){this.S={}}},Zk=null;const bl={[8]:"google_prev_ad_formats_by_region",[9]:"google_prev_ad_slotnames_by_region"};function el(a){this.g=a||{cookie:""}} 
el.prototype.set=function(a,b,c){let d,e,f,g=!1,h;typeof c==="object"&&(h=c.vc,g=c.wc||!1,f=c.domain||void 0,e=c.path||void 0,d=c.yb);if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');d===void 0&&(d=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(e?";path="+e:"")+(d<0?"":d==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+d*1E3)).toUTCString())+(g?";secure":"")+(h!=null?";samesite="+h:"")}; 
el.prototype.get=function(a,b){const c=a+"=",d=(this.g.cookie||"").split(";");for(let e=0,f;e<d.length;e++){f=sa(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};el.prototype.isEmpty=function(){return!this.g.cookie}; 
el.prototype.clear=function(){var a=(this.g.cookie||"").split(";");const b=[];var c=[];let d,e;for(let f=0;f<a.length;f++)e=sa(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));for(c=b.length-1;c>=0;c--)a=b[c],this.get(a),this.set(a,"",{yb:0,path:void 0,domain:void 0})};function fl(a,b=window){if(E(a,5))try{return b.localStorage}catch{}return null};function gl(a){var b=new hl;return uc(b,5,Qb(a))}var hl=class extends I{};function il(){this.A=this.A;this.i=this.i}il.prototype.A=!1;il.prototype.dispose=function(){this.A||(this.A=!0,this.D())};il.prototype[fa(Symbol,"dispose")]=function(){this.dispose()};function jl(a,b){a.A?b():(a.i||(a.i=[]),a.i.push(b))}il.prototype.D=function(){if(this.i)for(;this.i.length;)this.i.shift()()};function kl(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3} 
function ll(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=kl(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(me({e:String(a.internalErrorState)},"tcfe"),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0} 
function ml(a){if(a.g)return a.g;a:{let d=a.j;for(let e=0;e<50;++e){try{var b=!(!d.frames||!d.frames.__tcfapiLocator)}catch{b=!1}if(b){b=d;break a}b:{try{const f=d.parent;if(f&&f!=d){var c=f;break b}}catch{}c=null}if(!(d=c))break}b=null}a.g=b;return a.g}function nl(a,b,c,d){c||(c=()=>{});var e=a.j;typeof e.__tcfapi==="function"?(a=e.__tcfapi,a(b,2,c,d)):ml(a)?(ol(a),e=++a.Y,a.B[e]=c,a.g&&a.g.postMessage({__tcfapiCall:{command:b,version:2,callId:e,parameter:d}},"*")):c({},!1)} 
function ol(a){if(!a.u){var b=c=>{try{var d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.B[d.callId](d.returnValue,d.success)}catch(e){}};a.u=b;qd(a.j,"message",b)}} 
var pl=class extends il{constructor(a){var b={};super();this.g=null;this.B={};this.Y=0;this.u=null;this.j=a;this.J=b.ab??500;this.G=b.hc??!1}D(){this.B={};this.u&&(rd(this.j,"message",this.u),delete this.u);delete this.B;delete this.j;delete this.g;super.D()}addEventListener(a){let b={internalBlockOnErrors:this.G};const c=pd(()=>a(b));let d=0;this.J!==-1&&(d=setTimeout(()=>{b.tcString="tcunavailable";b.internalErrorState=1;c()},this.J));const e=(f,g)=>{clearTimeout(d);f?(b=f,b.internalErrorState= 
kl(b),b.internalBlockOnErrors=this.G,g&&b.internalErrorState===0||(b.tcString="tcunavailable",g||(b.internalErrorState=3))):(b.tcString="tcunavailable",b.internalErrorState=3);a(b)};try{nl(this,"addEventListener",e)}catch(f){b.tcString="tcunavailable",b.internalErrorState=3,d&&(clearTimeout(d),d=0),c()}}removeEventListener(a){a&&a.listenerId&&nl(this,"removeEventListener",null,a.listenerId)}};var ul=({l:a,aa:b,ab:c,mb:d,ha:e=!1,ia:f=!1})=>{b=ql({l:a,aa:b,ha:e,ia:f});b.g!=null||b.i.message!="tcunav"?d(b):rl(a,c).then(g=>g.map(sl)).then(g=>g.map(h=>tl(a,h))).then(d)},ql=({l:a,aa:b,ha:c=!1,ia:d=!1})=>{if(!vl({l:a,aa:b,ha:c,ia:d}))return tl(a,gl(!0));b=Yk();return(b=dl(b,24))?tl(a,sl(b)):sh(Error("tcunav"))}; 
function vl({l:a,aa:b,ha:c,ia:d}){if(d=!d)d=new pl(a),d=typeof d.j.__tcfapi==="function"||ml(d)!=null;if(!d){if(c=!c){if(b){a=Xk(a);if(a.g!=null)if((a=a.getValue())&&D(a,1)!=null)b:switch(a=H(a,1),a){case 1:a=!0;break b;default:throw Error("Unhandled AutoGdprFeatureStatus: "+a);}else a=!1;else sk(806,a.i),a=!1;b=!a}c=b}d=c}return d?!0:!1}function rl(a,b){return Promise.race([wl(),xl(a,b)])} 
function wl(){return(new Promise(a=>{var b=Yk();a={resolve:a};const c=dl(b,25,[]);c.push(a);b.S[al(25)]=c})).then(yl)}function xl(a,b){return new Promise(c=>{a.setTimeout(c,b,sh(Error("tcto")))})}function yl(a){return a?qh(a):sh(Error("tcnull"))} 
function sl(a){var b={};if(ll(a))if(a.gdprApplies===!1)a=!0;else if(a.tcString==="tcunavailable")a=!b.Ta;else if((b.Ta||a.gdprApplies!==void 0||b.ic)&&(b.Ta||typeof a.tcString==="string"&&a.tcString.length)){b:{if(a.publisher&&a.publisher.restrictions&&(b=a.publisher.restrictions["1"],b!==void 0)){b=b["755"];break b}b=void 0}b===0?a=!1:a.purpose&&a.vendor?(b=a.vendor.consents,(b=!(!b||!b["755"]))&&a.purposeOneTreatment&&a.publisherCC==="CH"?a=!0:(b&&(a=a.purpose.consents,b=!(!a||!a["1"])),a=b)):a= 
!0}else a=!0;else a=!1;return gl(a)}function tl(a,b){return(a=fl(b,a))?qh(a):sh(Error("unav"))};var zl=class extends I{};var Al=class extends I{};var Bl=class extends I{g(){return A(this,Al,3)}};var Cl=class{constructor(a){this.exception=a}};function Dl(a,b){try{var c=a.i,d=c.resolve,e=a.g;Nj(e.g);B(e.j,Mh,1,z());d.call(c,new Cl(b))}catch(f){a.i.reject(f)}}var El=class{constructor(a,b,c){this.j=a;this.g=b;this.i=c}start(){this.u()}u(){try{switch(this.j.document.readyState){case "complete":case "interactive":Qk(this.g,!0);Dl(this);break;default:Qk(this.g,!1)?Dl(this):this.j.setTimeout(pa(this.u,this),100)}}catch(a){Dl(this,a)}}};var Fl=class extends I{getVersion(){return F(this,2)}};function Gl(a){return Qa(a.length%4!==0?a+"A":a).map(b=>b.toString(2).padStart(8,"0")).join("")}function Hl(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);return parseInt(a,2)}function Il(a){if(!/^[0-1]+$/.test(a))throw Error(`Invalid input [${a}] not a bit string.`);const b=[1,2,3,5];let c=0;for(let d=0;d<a.length-1;d++)b.length<=d&&b.push(b[d-1]+b[d-2]),c+=parseInt(a[d],2)*b[d];return c};function Jl(a){var b=Gl(a),c=Hl(b.slice(0,6));a=Hl(b.slice(6,12));var d=new Fl;c=ad(d,1,c);a=ad(c,2,a);b=b.slice(12);c=Hl(b.slice(0,12));d=[];let e=b.slice(12).replace(/0+$/,"");for(let k=0;k<c;k++){if(e.length===0)throw Error(`Found ${k} of ${c} sections [${d}] but reached end of input [${b}]`);var f=Hl(e[0])===0;e=e.slice(1);var g=Kl(e,b),h=d.length===0?0:d[d.length-1];h=Il(g)+h;e=e.slice(g.length);if(f)d.push(h);else{f=Kl(e,b);g=Il(f);for(let m=0;m<=g;m++)d.push(h+m);e=e.slice(f.length)}}if(e.length> 
0)throw Error(`Found ${c} sections [${d}] but has remaining input [${e}], entire input [${b}]`);return Dc(a,3,d,Wb)}function Kl(a,b){const c=a.indexOf("11");if(c===-1)throw Error(`Expected section bitstring but not found in [${a}] part of [${b}]`);return a.slice(0,c+2)};var Ll="a".charCodeAt(),Ml=wd(gh),Nl=wd(hh);function Ol(){var a=new Pl;return bd(a,1,0)}function Ql(a){var b=Number;{var c=sc(a,1);const d=typeof c;c=c==null?c:d==="bigint"?String(Lb(64,c)):Tb(c)?d==="string"?$b(c):cc(c):void 0}b=b(c??"0");a=F(a,2);return new Date(b*1E3+a/1E6)}var Pl=class extends I{};function Rl(a,b){if(a.g+b>a.i.length)throw Error("Requested length "+b+" is past end of string.");const c=a.i.substring(a.g,a.g+b);a.g+=b;return parseInt(c,2)}function Sl(a){let b=Rl(a,12);const c=[];for(;b--;){var d=!!Rl(a,1)===!0,e=Rl(a,16);if(d)for(d=Rl(a,16);e<=d;e++)c.push(e);else c.push(e)}c.sort((f,g)=>f-g);return c}function Tl(a,b,c){const d=[];for(let e=0;e<b;e++)if(Rl(a,1)){const f=e+1;if(c&&c.indexOf(f)===-1)throw Error(`ID: ${f} is outside of allowed values!`);d.push(f)}return d} 
function Ul(a){const b=Rl(a,16);return!!Rl(a,1)===!0?(a=Sl(a),a.forEach(c=>{if(c>b)throw Error(`ID ${c} is past MaxVendorId ${b}!`);}),a):Tl(a,b)}var Vl=class{constructor(a){if(/[^01]/.test(a))throw Error(`Input bitstring ${a} is malformed!`);this.i=a;this.g=0}skip(a){this.g+=a}};var Xl=(a,b)=>{try{var c=Qa(a.split(".")[0]).map(e=>e.toString(2).padStart(8,"0")).join("");const d=new Vl(c);c={};c.tcString=a;c.gdprApplies=b;d.skip(78);c.cmpId=Rl(d,12);c.cmpVersion=Rl(d,12);d.skip(30);c.tcfPolicyVersion=Rl(d,6);c.isServiceSpecific=!!Rl(d,1);c.useNonStandardStacks=!!Rl(d,1);c.specialFeatureOptins=Wl(Tl(d,12,Nl),Nl);c.purpose={consents:Wl(Tl(d,24,Ml),Ml),legitimateInterests:Wl(Tl(d,24,Ml),Ml)};c.purposeOneTreatment=!!Rl(d,1);c.publisherCC=String.fromCharCode(Ll+Rl(d,6))+String.fromCharCode(Ll+ 
Rl(d,6));c.vendor={consents:Wl(Ul(d),null),legitimateInterests:Wl(Ul(d),null)};return c}catch(d){return null}};const Wl=(a,b)=>{const c={};if(Array.isArray(b)&&b.length!==0)for(const d of b)c[d]=a.indexOf(d)!==-1;else for(const d of a)c[d]=!0;delete c[0];return c};var Yl=class extends I{g(){return C(this,2)!=null}};var Zl=class extends I{g(){return C(this,2)!=null}};var $l=class extends I{};var am=kd(class extends I{});function bm(a){a=cm(a);try{var b=a?am(a):null}catch(c){b=null}return b?A(b,$l,4)||null:null}function cm(a){a=(new el(a)).get("FCCDCF","");if(a)if(a.startsWith("%"))try{var b=decodeURIComponent(a)}catch(c){b=null}else b=a;else b=null;return b};wd(gh).map(a=>Number(a));wd(hh).map(a=>Number(a));function dm(a){a.__tcfapiPostMessageReady||em(new fm(a))} 
function em(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__tcfapiCall;e&&(e.command==="ping"||e.command==="addEventListener"||e.command==="removeEventListener")&&(0,a.l.__tcfapi)(e.command,e.version,(f,g)=>{const h={};h.__tcfapiReturn=e.command==="removeEventListener"?{success:f,callId:e.callId}:{returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f, 
b.origin);return f},e.parameter)};a.l.addEventListener("message",a.g);a.l.__tcfapiPostMessageReady=!0}var fm=class{constructor(a){this.l=a}};function gm(a){a.__uspapiPostMessageReady||hm(new im(a))} 
function hm(a){a.g=b=>{const c=typeof b.data==="string";let d;try{d=c?JSON.parse(b.data):b.data}catch(f){return}const e=d.__uspapiCall;e&&e.command==="getUSPData"&&a.l.__uspapi(e.command,e.version,(f,g)=>{const h={};h.__uspapiReturn={returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f})};a.l.addEventListener("message",a.g);a.l.__uspapiPostMessageReady=!0} 
var im=class{constructor(a){this.l=a;this.g=null}};var jm=class extends I{};var km=kd(class extends I{g(){return C(this,1)!=null}});function lm(a,b){function c(l){if(l.length<10)return null;var p=h(l.slice(0,4));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function d(l){if(l.length<10)return null;var p=h(l.slice(0,6));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function e(l){if(l.length<12)return null;var p=h(l.slice(0,6));p=k(p);l=h(l.slice(8,12));l=m(l);return"1"+p+l+"N"}function f(l){if(l.length<18)return null;var p=h(l.slice(0,8));p=k(p);l=h(l.slice(12,18));l=m(l);return"1"+p+l+"N"}function g(l){if(l.length<10)return null; 
var p=h(l.slice(0,6));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function h(l){const p=[];let v=0;for(let u=0;u<l.length/2;u++)p.push(Hl(l.slice(v,v+2))),v+=2;return p}function k(l){return l.every(p=>p===1)?"Y":"N"}function m(l){return l.some(p=>p===1)?"Y":"N"}if(a.length===0)return null;a=a.split(".");if(a.length>2)return null;a=Gl(a[0]);const n=Hl(a.slice(0,6));a=a.slice(6);if(n!==1)return null;switch(b){case 8:return c(a);case 10:case 12:case 9:return d(a);case 11:return e(a);case 7:return f(a); 
case 13:return g(a);default:return null}};function mm(a,b){const c=a.document,d=()=>{if(!a.frames[b])if(c.body){const e=Rd("IFRAME",c);e.style.display="none";e.style.width="0px";e.style.height="0px";e.style.border="none";e.style.zIndex="-1000";e.style.left="-1000px";e.style.top="-1000px";e.name=b;c.body.appendChild(e)}else a.setTimeout(d,5)};d()};function nm(a){if(a!=null)return om(a)}function om(a){return Db(a)?Number(a):String(a)};function pm(a){var b=R(zi);L!==L.top||L.__uspapi||L.frames.__uspapiLocator||(a=new qm(a,b),rm(a),sm(a))}function rm(a){!a.u||a.l.__uspapi||a.l.frames.__uspapiLocator||(a.l.__uspapiManager="fc",mm(a.l,"__uspapiLocator"),qa("__uspapi",(b,c,d)=>{typeof d==="function"&&b==="getUSPData"&&(b=a.i&&!E(a.j,3),d({version:1,uspString:b?"1---":a.u},!0))},a.l),gm(a.l))} 
function sm(a){!a.tcString||a.l.__tcfapi||a.l.frames.__tcfapiLocator||(a.l.__tcfapiManager="fc",mm(a.l,"__tcfapiLocator"),a.l.__tcfapiEventListeners=a.l.__tcfapiEventListeners||[],qa("__tcfapi",(b,c,d,e)=>{if(typeof d==="function")if(c&&(c>2.2||c<=1))d(null,!1);else{var f=a.l.__tcfapiEventListeners;c=a.i&&!E(a.j,1);switch(b){case "ping":d({gdprApplies:!c,cmpLoaded:!0,cmpStatus:"loaded",displayStatus:"disabled",apiVersion:"2.2",cmpVersion:2,cmpId:300});break;case "addEventListener":e=f.push(d);b=!c; 
--e;a.tcString?(b=Xl(a.tcString,b),b.addtlConsent=a.g!=null?a.g:void 0,b.cmpStatus="loaded",b.eventStatus="tcloaded",e!=null&&(b.listenerId=e)):b=null;d(b,!0);break;case "removeEventListener":e!==void 0&&f[e]?(f[e]=null,d(!0)):d(!1);break;case "getInAppTCData":case "getVendorList":d(null,!1);break;case "getTCData":d(null,!1)}}},a.l),dm(a.l))} 
function tm(a){if(!a?.g()||G(a,1).length===0||B(a,jm,2,z()).length===0)return null;const b=G(a,1);let c;try{var d=Jl(b.split("~")[0]);c=b.includes("~")?b.split("~").slice(1):[]}catch(e){return null}a=B(a,jm,2,z()).reduce((e,f)=>om(Sc(um(e),1))>om(Sc(um(f),1))?e:f);d=xc(d,3,Xb,z()).indexOf(F(a,1));return d===-1||d>=c.length?null:{uspString:lm(c[d],F(a,1)),ua:Ql(um(a))}}function vm(a){a=a.find(b=>b&&H(b,1)===13);if(a?.g())try{return km(G(a,2))}catch(b){}return null} 
function um(a){return vc(a,Pl,2)?A(a,Pl,2):Ol()} 
var qm=class{constructor(a,b){var c=L;this.l=c;this.j=a;this.i=b;a=cm(this.l.document);try{var d=a?am(a):null}catch(e){d=null}(a=d)?(d=A(a,Zl,5)||null,a=B(a,Yl,7,z()),a=vm(a??[]),d={Qa:d,Sa:a}):d={Qa:null,Sa:null};a=d;d=tm(a.Sa);a=a.Qa;a?.g()&&G(a,2).length!==0?(b=vc(a,Pl,1)?A(a,Pl,1):Ol(),a={uspString:G(a,2),ua:Ql(b)}):a=null;this.u=a&&d?d.ua>a.ua?d.uspString:a.uspString:a?a.uspString:d?d.uspString:null;this.tcString=(d=bm(c.document))&&C(d,1)!=null?G(d,1):null;this.g=(c=bm(c.document))&&C(c,2)!= 
null?G(c,2):null}};const wm={google_ad_channel:!0,google_ad_host:!0};function xm(a,b){a.location.href&&a.location.href.substring&&(b.url=a.location.href.substring(0,200));rk("ama",b,.01)}function ym(a){const b={};Ud(wm,(c,d)=>{d in a&&(b[d]=a[d])});return b};function zm(a){const b=/[a-zA-Z0-9._~-]/,c=/%[89a-zA-Z]./;return a.replace(/(%[a-zA-Z0-9]{2})/g,d=>{if(!d.match(c)){const e=decodeURIComponent(d);if(e.match(b))return e}return d.toUpperCase()})}function Am(a){let b="";const c=/[/%?&=]/;for(let d=0;d<a.length;++d){const e=a[d];b=e.match(c)?b+e:b+encodeURIComponent(e)}return b};function Bm(a){a=Xc(a,2);if(!a)return!1;for(let b=0;b<a.length;b++)if(a[b]==1)return!0;return!1}function Cm(a,b){a=Am(zm(a.location.pathname)).replace(/(^\/)|(\/$)/g,"");const c=Vd(a),d=Dm(a);return b.find(e=>{if(vc(e,lj,7)){var f=A(e,lj,7);f=Yb(sc(f,1))}else f=Yb(sc(e,1));e=vc(e,lj,7)?D(A(e,lj,7),2):2;if(typeof f!=="number")return!1;switch(e){case 1:return f==c;case 2:return d[f]||!1}return!1})||null} 
function Dm(a){const b={};for(;;){b[Vd(a)]=!0;if(!a)return b;a=a.substring(0,a.lastIndexOf("/"))}};let Em=void 0;function W(a){return a.google_ad_modifications=a.google_ad_modifications||{}}function Fm(a){a=W(a);const b=a.space_collapsing||"none";return a.remove_ads_by_default?{Oa:!0,Jb:b,sa:a.ablation_viewport_offset}:null}function Gm(a){a=W(a);a.had_ads_ablation=!0;a.remove_ads_by_default=!0;a.space_collapsing="slot";a.ablation_viewport_offset=1}function Hm(a){W(L).allow_second_reactive_tag=a}function Im(){const a=W(window);a.afg_slotcar_vars||(a.afg_slotcar_vars={});return a.afg_slotcar_vars};function Jm(a){return W(a)?.head_tag_slot_vars?.google_ad_host??Km(a)}function Km(a){return a.document?.querySelector('meta[name="google-adsense-platform-account"]')?.getAttribute("content")??null};const Lm=[2,7,1];function Mm(a,b,c="",d=null){return b===1&&d&&(Nm(a,c,d)?.A()??!1)?!0:Om(a,c,e=>Ka(B(e,ld,2,z()),f=>D(f,1)===b))}function Pm(a){const b=Pd(L)||L;return Qm(b,a)?!0:Om(L,"",c=>Ka(Xc(c,3),d=>d===a))}function Qm(a,b){a=(a=(a=a.location&&a.location.hash)&&a.match(/forced_clientside_labs=([\d,]+)/))&&a[1];return!!a&&Ma(a.split(","),b.toString())} 
function Om(a,b,c){a=Pd(a)||a;const d=Rm(a);b&&(b=ye(String(b)));return vd(d,(e,f)=>Object.prototype.hasOwnProperty.call(d,f)&&(!b||b===f)&&c(e))}function Rm(a){rb(Em,ub);a=Sm(a);const b={};Ud(a,(c,d)=>{try{const e=id(md,mc(c));b[d]=e}catch(e){}});return b}function Sm(a){a=ql({l:a,aa:Em});return a.g!=null?Tm(a.getValue()):{}} 
function Tm(a){try{const b=a.getItem("google_adsense_settings");if(!b)return{};const c=JSON.parse(b);return c!==Object(c)?{}:ud(c,(d,e)=>Object.prototype.hasOwnProperty.call(c,e)&&typeof e==="string"&&Array.isArray(d))}catch(b){return{}}} 
function Um(a,b){if(Jm(q))return Lm;if(R(Di))return(a=Nm(q,a,b)?.B())?[...Xc(a,3)]:Lm;if(b?.j()){const c=G(b.g(),9);b=b?.g()?.g()?.g();return a&&c===a&&b?[...Xc(b,3)]:Lm}return b?.u()&&b?.i()?.g()===a&&G(b,17)===q.location.host?(a=b?.i()?.i()?.g()?.g())?[...Xc(a,3)]:Lm:Lm}function Vm(a,b){const c=[];a=Um(a,b);a.includes(1)||c.push(1);a.includes(2)||c.push(2);a.includes(7)||c.push(7);return c} 
function Nm(a,b,c){if(!b)return null;const d=Wm(c)?.u();a=Wm(c)?.g()?.g()===b&&a.location.host&&G(c,17)===a.location.host;return d===b||a?Wm(c):null};function Xm(a,b,c,d){Ym(new Zm(a,b,c,d))}function Ym(a){wh(vh(ql({l:a.l,aa:E(a.g,6)}),b=>{$m(a,b,!0)}),()=>{an(a)})}function $m(a,b,c){wh(vh(bn(b),d=>{cn("ok");a.i(d,{fromLocalStorage:!0})}),()=>{var d=a.l;try{b.removeItem("google_ama_config")}catch(e){xm(d,{lserr:1})}c?an(a):a.i(null,null)})}function an(a){wh(vh(dn(a),b=>{a.i(b,{fromPABGSettings:!0})}),()=>{en(a)})} 
function bn(a){if(R(gi))var b=null;else try{b=a.getItem("google_ama_config")}catch(d){b=null}try{var c=b?xj(b):null}catch(d){c=null}return(a=(a=c)?(nm(A(a,kj,3)?.g())??0)>Date.now()?a:null:null)?qh(a):sh(Error("invlocst"))}function dn(a){if(Jm(a.l)&&!E(a.g,22))return sh(Error("invtag"));if(a=(a=Nm(a.l,a.j,a.g)?.j())&&B(a,Mh,1,z()).length>0?a:null){var b=new wj;var c=B(a,Mh,1,z());b=Oc(b,1,c);a=B(a,rj,2,z());a=Oc(b,7,a);a=qh(a)}else a=sh(Error("invtag"));return a} 
function en(a){ul({l:a.l,aa:E(a.g,6),ab:50,mb:b=>{fn(a,b)}})}function fn(a,b){wh(vh(b,c=>{$m(a,c,!1)}),c=>{cn(c.message);a.i(null,null)})}function cn(a){rk("abg::amalserr",{status:a,guarding:"true",timeout:50,rate:.01},.01)}class Zm{constructor(a,b,c,d){this.l=a;this.g=b;this.j=c;this.i=d}};function gn(a,b,c,d){var e=hn;try{const f=Cm(a,B(c,rj,7,z()));if(f&&Bm(f)){if(C(f,4)??void 0){const h=new Fh(null,{google_package:C(f,4)??void 0});d=Eh(d,h)}const g=e(a,b,c,f,d);Ij(1E3,()=>{const h=new lh;(new El(a,g,h)).start();return h.i},a).then(()=>{xm(a,{atf:1})},h=>{(a.google_ama_state=a.google_ama_state||{}).exception=h;xm(a,{atf:0})})}}catch(f){xm(a,{atf:-1})}}function hn(a,b,c,d,e){return new Vk(a,b,c,d,e)};function jn(a){return a.length?a.join("~"):void 0};function kn(a,b){if(!a)return!1;a=a.hash;if(!a||!a.indexOf)return!1;if(a.indexOf(b)!=-1)return!0;b=ln(b);return b!="go"&&a.indexOf(b)!=-1?!0:!1}function ln(a){let b="";Ud(a.split("_"),c=>{b+=c.substr(0,2)});return b};var mn=class extends I{};var nn=class extends I{g(){return G(this,3)}i(){return Rb(sc(this,4))!=null}};var on=class extends I{g(){return Lc(this,nn,1)}};function pn(a){const b=new on;var c=new nn;var d=F(a,1);c=$c(c,1,d);d=F(a,18);c=$c(c,2,d);d=G(a,2);c=dd(c,3,d);d=E(a,6);c=uc(c,4,Qb(d));d=E(a,20);c=uc(c,5,Qb(d));d=E(a,9);c=uc(c,6,Qb(d));d=E(a,25);c=uc(c,7,Qb(d));d=G(a,8);c=dd(c,8,d);d=G(a,3);c=dd(c,9,d);a=A(a,mn,26);a=Mc(c,10,a);Mc(b,1,a);return b};function qn(){const a={};J(ee).g(ci.g,ci.defaultValue)&&(a.bust=J(ee).g(ci.g,ci.defaultValue));return a};class rn{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};function sn(){const {promise:a,resolve:b}=new rn;return{promise:a,resolve:b}};function tn(a=()=>{}){q.google_llp||(q.google_llp={});const b=q.google_llp;let c=b[7];if(c)return c;c=sn();b[7]=c;a();return c}function un(a){return tn(()=>{Qd(q.document,a)}).promise};Array.from({length:11},(a,b)=>b/10);function vn(a){a.google_reactive_ads_global_state?(a.google_reactive_ads_global_state.sideRailProcessedFixedElements==null&&(a.google_reactive_ads_global_state.sideRailProcessedFixedElements=new Set),a.google_reactive_ads_global_state.sideRailAvailableSpace==null&&(a.google_reactive_ads_global_state.sideRailAvailableSpace=new Map),a.google_reactive_ads_global_state.sideRailPlasParam==null&&(a.google_reactive_ads_global_state.sideRailPlasParam=new Map),a.google_reactive_ads_global_state.sideRailMutationCallbacks== 
null&&(a.google_reactive_ads_global_state.sideRailMutationCallbacks=[])):a.google_reactive_ads_global_state=new wn;return a.google_reactive_ads_global_state} 
var wn=class{constructor(){this.wasPlaTagProcessed=!1;this.wasReactiveAdConfigReceived={};this.adCount={};this.wasReactiveAdVisible={};this.stateForType={};this.reactiveTypeEnabledInAsfe={};this.wasReactiveTagRequestSent=!1;this.reactiveTypeDisabledByPublisher={};this.tagSpecificState={};this.messageValidationEnabled=!1;this.floatingAdsStacking=new xn;this.sideRailProcessedFixedElements=new Set;this.sideRailAvailableSpace=new Map;this.sideRailPlasParam=new Map;this.sideRailMutationCallbacks=[];this.clickTriggeredInterstitialMayBeDisplayed= 
!1}},xn=class{constructor(){this.maxZIndexRestrictions={};this.nextRestrictionId=0;this.maxZIndexListeners=[]}};var yn=a=>{if(q.google_apltlad||a.google_ad_intent_query)return null;var b=a.google_loader_used!=="sd"&&R(ji)&&(q.top==q?0:Od(q.top)?1:2)===1;if(q!==q.top&&!b||!a.google_ad_client)return null;q.google_apltlad=!0;b={enable_page_level_ads:{pltais:!0},google_ad_client:a.google_ad_client};const c=b.enable_page_level_ads;Ud(a,(d,e)=>{gj[e]&&e!=="google_ad_client"&&(c[e]=d)});c.google_pgb_reactive=7;c.asro=R(ui);c.aihb=R(ki);c.ailel=jn(Hi(si));c.aiael=jn(Hi(mi));c.aicel=jn(Hi(oi));c.aifxl=jn(Hi(pi));c.aiixl= 
jn(Hi(qi));S(wi)&&(c.aiapm=S(wi));S(xi)&&(c.aiapmi=S(xi));S(li)&&(c.aiact=S(li));S(ni)&&(c.aicct=S(ni));S(ri)&&(c.ailct=S(ri));c.aiof=jn(Hi(ti));if("google_ad_section"in a||"google_ad_region"in a)c.google_ad_section=a.google_ad_section||a.google_ad_region;return b};function zn(a,b){W(L).ama_ran_on_page||Ij(1001,()=>{An(new Bn(a,b))},q)}function An(a){Xm(a.l,a.i,a.g.google_ad_client||"",(b,c)=>{var d=a.l,e=a.g;W(L).ama_ran_on_page||b&&Cn(d,e,b,c)})}class Bn{constructor(a,b){this.l=q;this.g=a;this.i=b}} 
function Cn(a,b,c,d){d&&(Nj(a).configSourceInAbg=d);vc(c,vj,24)&&(d=Oj(a),d.availableAbg=!0,d.ablationFromStorage=!!A(c,vj,24)?.g()?.g());if(ja(b.enable_page_level_ads)&&b.enable_page_level_ads.google_pgb_reactive===7){if(!Cm(a,B(c,rj,7,z()))){rk("amaait",{value:"true"});return}rk("amaait",{value:"false"})}W(L).ama_ran_on_page=!0;A(c,jj,15)?.g()&&(W(a).enable_overlap_observer=!0);A(c,vj,24)?.g()?.g()&&(Oj(a).ablatingThisPageview=!0,Gm(a));pe(3,[w(c)]);const e=b.google_ad_client||"";b=ym(ja(b.enable_page_level_ads)? 
b.enable_page_level_ads:{});const f=Eh(Ih,new Fh(null,b));ok(782,()=>{gn(a,e,c,f)})};function Dn(a,b){a=a.document;for(var c=void 0,d=0;!c||a.getElementById(c+"_host");)c="aswift_"+d++;a=c;c=Number(b.google_ad_width||0);b=Number(b.google_ad_height||0);d=document.createElement("div");d.id=a+"_host";const e=d.style;e.border="none";e.height=`${b}px`;e.width=`${c}px`;e.margin="0px";e.padding="0px";e.position="relative";e.visibility="visible";e.backgroundColor="transparent";e.display="inline-block";return{ub:a,Mb:d}};function En({ta:a,za:b}){return a||(b==="dev"?"dev":"")};function Fn(a){return a.google_ad_client?String(a.google_ad_client):W(a).head_tag_slot_vars?.google_ad_client??a.document.querySelector(".adsbygoogle[data-ad-client]")?.getAttribute("data-ad-client")??""};var Gn={"120x90":!0,"160x90":!0,"180x90":!0,"200x90":!0,"468x15":!0,"728x15":!0};function Hn(a,b){if(b==15){if(a>=728)return 728;if(a>=468)return 468}else if(b==90){if(a>=200)return 200;if(a>=180)return 180;if(a>=160)return 160;if(a>=120)return 120}return null};var In=class extends I{getVersion(){return G(this,2)}};function Jn(a,b){return dd(a,2,b)}function Kn(a,b){return dd(a,3,b)}function Ln(a,b){return dd(a,4,b)}function Mn(a,b){return dd(a,5,b)}function Nn(a,b){return dd(a,9,b)}function On(a,b){return Oc(a,10,b)}function Pn(a,b){return uc(a,11,Qb(b))}function Qn(a,b){return dd(a,1,b)}function Rn(a,b){return uc(a,7,Qb(b))}var Sn=class extends I{};const Tn="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Un(){var a=L;if(typeof a.navigator?.userAgentData?.getHighEntropyValues!=="function")return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Tn).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} 
function Vn(a){return Pn(On(Mn(Jn(Qn(Ln(Rn(Nn(Kn(new Sn,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),a.fullVersionList?.map(b=>{var c=new In;c=dd(c,1,b.brand);return dd(c,2,b.version)})||[]),a.wow64||!1)}function Wn(){return Un()?.then(a=>Vn(a))??null};function Xn(a,b){b.google_ad_host||(a=Km(a))&&(b.google_ad_host=a)}function Yn(a,b,c=""){L.google_sa_queue||(L.google_sa_queue=[],L.google_process_slots=pk(215,()=>{Zn(L.google_sa_queue)}),a=$n(c,a,b),Qd(L.document,a))}function Zn(a){const b=a.shift();typeof b==="function"&&ok(216,b);a.length&&q.setTimeout(pk(215,()=>{Zn(a)}),0)}function ao(a,b){a.google_sa_queue=a.google_sa_queue||[];a.google_sa_impl?b():a.google_sa_queue.push(b)} 
function $n(a,b,c){var d=L;b=E(c,4)?b.Fb:b.Gb;a:{if(E(c,4)){if(a=a||Fn(d)){b:{try{for(;d;){if(d.location?.hostname){var e=d.location.hostname;break b}d=d.parent}}catch(f){}e=""}e={client:ye(a),plah:e};break a}throw Error("PublisherCodeNotFoundForAma");}e={}}e={...e,...qn()};d=S(Ai);!E(c,4)&&[0,1].includes(d)&&(e.osttc=`${d}`);return Md(b,new Map(Object.entries(e)))} 
function bo(a,b,c,d){const {ub:e,Mb:f}=Dn(a,b);c.appendChild(f);co(a,c,b);c=b.google_start_time??fh;const g=(new Date).getTime();b.google_lrv=En({ta:fg(),za:G(d,2)});b.google_async_iframe_id=e;b.google_start_time=c;b.google_bpp=g>c?g-c:1;a.google_sv_map=a.google_sv_map||{};a.google_sv_map[e]=b;ao(a,()=>{var h=f;if(!h||!h.isConnected)if(h=a.document.getElementById(String(b.google_async_iframe_id)+"_host"),h==null)throw Error("no_div");(h=a.google_sa_impl({pubWin:a,vars:b,innerInsElement:h}))&&qk(911, 
h)})} 
function co(a,b,c){var d=c.google_ad_output,e=c.google_ad_format,f=c.google_ad_width||0,g=c.google_ad_height||0;e||d!=="html"&&d!=null||(e=`${f}x${g}`);R(Fi)&&(c.google_reactive_ad_format===10?e="interstitial":c.google_reactive_ad_format===11&&(e="rewarded"));d=!c.google_ad_slot||c.google_override_format||!Gn[c.google_ad_width+"x"+c.google_ad_height]&&c.google_loader_used==="aa";e=e&&d?e.toLowerCase():"";c.google_ad_format=e;if(typeof c.google_reactive_sra_index!=="number"||!c.google_ad_unit_key){e=[c.google_ad_slot, 
c.google_orig_ad_format||c.google_ad_format,c.google_ad_type,c.google_orig_ad_width||c.google_ad_width,c.google_orig_ad_height||c.google_ad_height];d=[];f=0;for(g=b;g&&f<25;g=g.parentNode,++f)g.nodeType===9?d.push(""):d.push(g.id);(d=d.join())&&e.push(d);c.google_ad_unit_key=Vd(e.join(":")).toString();e=[];for(d=0;b&&d<25;++d){f=(f=b.nodeType!==9&&b.id)?"/"+f:"";a:{if(b&&b.nodeName&&b.parentElement){g=b.nodeName.toString().toLowerCase();const h=b.parentElement.childNodes;let k=0;for(let m=0;m<h.length;++m){const n= 
h[m];if(n.nodeName&&n.nodeName.toString().toLowerCase()===g){if(b===n){g="."+k;break a}++k}}}g=""}e.push((b.nodeName&&b.nodeName.toString().toLowerCase())+f+g);b=b.parentElement}b=e.join()+":";e=[];if(a)try{let h=a.parent;for(d=0;h&&h!==a&&d<25;++d){const k=h.frames;for(f=0;f<k.length;++f)if(a===k[f]){e.push(f);break}a=h;h=a.parent}}catch(h){}c.google_ad_dom_fingerprint=Vd(b+e.join()).toString()}} 
function eo(){var a=Pd(q);a&&(a=vn(a),a.tagSpecificState[1]||(a.tagSpecificState[1]={debugCard:null,debugCardRequested:!1}))}function fo(){const a=Wn();a!=null&&a.then(b=>{L.google_user_agent_client_hint=JSON.stringify(w(b))});de()};var go=class{constructor(a,b){this.g=a;this.u=b}height(){return this.u}i(a){return a>S(fi)&&this.u>300?this.g:Math.min(1200,Math.round(a))}j(){}};function ho(a){return b=>!!(b.Z()&a)}var Y=class extends go{constructor(a,b,c,d=!1){super(a,b);this.B=c;this.A=d}Z(){return this.B}j(a,b,c){c.style.height=`${this.height()}px`;b.rpe=!0}};const io={image_stacked:1/1.91,image_sidebyside:1/3.82,mobile_banner_image_sidebyside:1/3.82,pub_control_image_stacked:1/1.91,pub_control_image_sidebyside:1/3.82,pub_control_image_card_stacked:1/1.91,pub_control_image_card_sidebyside:1/3.74,pub_control_text:0,pub_control_text_card:0},jo={image_stacked:80,image_sidebyside:0,mobile_banner_image_sidebyside:0,pub_control_image_stacked:80,pub_control_image_sidebyside:0,pub_control_image_card_stacked:85,pub_control_image_card_sidebyside:0,pub_control_text:80, 
pub_control_text_card:80},ko={pub_control_image_stacked:100,pub_control_image_sidebyside:200,pub_control_image_card_stacked:150,pub_control_image_card_sidebyside:250,pub_control_text:100,pub_control_text_card:150}; 
function lo(a){var b=0;a.R&&b++;a.K&&b++;a.L&&b++;if(b<3)return{X:"Tags data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num should be set together."};b=a.R.split(",");const c=a.L.split(",");a=a.K.split(",");if(b.length!==c.length||b.length!==a.length)return{X:'Lengths of parameters data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num must match. Example: \n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}; 
if(b.length>2)return{X:"The parameter length of attribute data-matched-content-ui-type, data-matched-content-columns-num and data-matched-content-rows-num is too long. At most 2 parameters for each attribute are needed: one for mobile and one for desktop, while "+`you are providing ${b.length} parameters. Example: ${'\n data-matched-content-rows-num="4,2"\ndata-matched-content-columns-num="1,6"\ndata-matched-content-ui-type="image_stacked,image_card_sidebyside"'}.`};const d=[],e=[];for(let g=0;g< 
b.length;g++){var f=Number(c[g]);if(Number.isNaN(f)||f===0)return{X:`Wrong value '${c[g]}' for ${"data-matched-content-rows-num"}.`};d.push(f);f=Number(a[g]);if(Number.isNaN(f)||f===0)return{X:`Wrong value '${a[g]}' for ${"data-matched-content-columns-num"}.`};e.push(f)}return{L:d,K:e,Va:b}} 
function mo(a){return a>=1200?{width:1200,height:600}:a>=850?{width:a,height:Math.floor(a*.5)}:a>=550?{width:a,height:Math.floor(a*.6)}:a>=468?{width:a,height:Math.floor(a*.7)}:{width:a,height:Math.floor(a*3.44)}}function no(a,b,c,d){b=Math.floor(((a-8*b-8)/b*io[d]+jo[d])*c+8*c+8);return a>1500?{width:0,height:0,Hb:`Calculated slot width is too large: ${a}`}:b>1500?{width:0,height:0,Hb:`Calculated slot height is too large: ${b}`}:{width:a,height:b}} 
function oo(a,b){const c=a-8-8;--b;return{width:a,height:Math.floor(c/1.91+70)+Math.floor((c*io.mobile_banner_image_sidebyside+jo.mobile_banner_image_sidebyside)*b+8*b+8)}};const po=Oa("script");var qo=class{constructor(a,b,c=null,d=null,e=null,f=null,g=null,h=null,k=null,m=null,n=null,l=null){this.D=a;this.V=b;this.Z=c;this.g=d;this.G=e;this.F=f;this.P=g;this.u=h;this.A=k;this.i=m;this.j=n;this.B=l}size(){return this.V}};const ro=["google_content_recommendation_ui_type","google_content_recommendation_columns_num","google_content_recommendation_rows_num"];var so=class extends go{i(a){return Math.min(1200,Math.max(this.g,Math.round(a)))}}; 
function to(a,b){uo(a,b);if(b.google_content_recommendation_ui_type==="pedestal")return new qo(9,new so(a,Math.floor(a*b.google_phwr)));if(R(Th)){var c=sd();var d=S(Uh);var e=S(Sh),f=S(Rh);a<468?c?(a=oo(a,d),d={W:a.width,U:a.height,K:1,L:d,R:"mobile_banner_image_sidebyside"}):(a=no(a,1,d,"image_sidebyside"),d={W:a.width,U:a.height,K:1,L:d,R:"image_sidebyside"}):(d=mo(a),e===1&&(d.height=Math.floor(d.height*.5)),d={W:d.width,U:d.height,K:f,L:e,R:"image_stacked"})}else d=sd(),a<468?d?(d=oo(a,12),d= 
{W:d.width,U:d.height,K:1,L:12,R:"mobile_banner_image_sidebyside"}):(d=mo(a),d={W:d.width,U:d.height,K:1,L:13,R:"image_sidebyside"}):(d=mo(a),d={W:d.width,U:d.height,K:4,L:2,R:"image_stacked"});vo(b,d);return new qo(9,new so(d.W,d.U))} 
function wo(a,b){uo(a,b);{const f=lo({L:b.google_content_recommendation_rows_num,K:b.google_content_recommendation_columns_num,R:b.google_content_recommendation_ui_type});if(f.X)a={W:0,U:0,K:0,L:0,R:"image_stacked",X:f.X};else{var c=f.Va.length===2&&a>=468?1:0;var d=f.Va[c];d=d.indexOf("pub_control_")===0?d:"pub_control_"+d;var e=ko[d];let g=f.K[c];for(;a/g<e&&g>1;)g--;e=g;c=f.L[c];a=no(a,e,c,d);a={W:a.width,U:a.height,K:e,L:c,R:d}}}if(a.X)throw new U(a.X);vo(b,a);return new qo(9,new so(a.W,a.U))} 
function uo(a,b){if(a<=0)throw new U(`Invalid responsive width from Matched Content slot ${b.google_ad_slot}: ${a}. Please ensure to put this Matched Content slot into a non-zero width div container.`);}function vo(a,b){a.google_content_recommendation_ui_type=b.R;a.google_content_recommendation_columns_num=b.K;a.google_content_recommendation_rows_num=b.L};var xo=class extends go{i(){return this.g}j(a,b,c){Yi(a,c);c.style.height=`${this.height()}px`;b.rpe=!0}};const yo={"image-top":a=>a<=600?284+(a-250)*.414:429,"image-middle":a=>a<=500?196-(a-250)*.13:164+(a-500)*.2,"image-side":a=>a<=500?205-(a-250)*.28:134+(a-500)*.21,"text-only":a=>a<=500?187-.228*(a-250):130,"in-article":a=>a<=420?a/1.2:a<=460?a/1.91+130:a<=800?a/4:200};var zo=class extends go{i(){return Math.min(1200,this.g)}}; 
function Ao(a,b,c,d,e){var f=e.google_ad_layout||"image-top";if(f==="in-article"){var g=a;if(e.google_full_width_responsive==="false")a=g;else if(a=Ti(b,c,g,S(Yh),e),a!==!0)e.gfwrnwer=a,a=g;else if(a=T(b))if(e.google_full_width_responsive_allowed=!0,c.parentElement){b:{g=c;for(let h=0;h<100&&g.parentElement;++h){const k=g.parentElement.childNodes;for(let m=0;m<k.length;++m){const n=k[m];if(n!==g&&Wi(b,n))break b}g=g.parentElement;g.style.width="100%";g.style.height="auto"}}Yi(b,c)}else a=g;else a= 
g}if(a<250)throw new U("Fluid responsive ads must be at least 250px wide: "+`availableWidth=${a}`);a=Math.min(1200,Math.floor(a));if(d&&f!=="in-article"){f=Math.ceil(d);if(f<50)throw new U("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);return new qo(11,new go(a,f))}if(f!=="in-article"&&(d=e.google_ad_layout_key)){f=`${d}`;if(d=(c=f.match(/([+-][0-9a-z]+)/g))&&c.length)for(b=[],e=0;e<d;e++)b.push(parseInt(c[e],36)/1E3);else b=null;if(!b)throw new U(`Invalid data-ad-layout-key value: ${f}`); 
f=(a+-725)/1E3;c=0;d=1;e=b.length;for(g=0;g<e;g++)c+=b[g]*d,d*=f;f=Math.ceil(c*1E3- -725+10);if(isNaN(f))throw new U(`Invalid height: height=${f}`);if(f<50)throw new U("Fluid responsive ads must be at least 50px tall: "+`height=${f}`);if(f>1200)throw new U("Fluid responsive ads must be at most 1200px tall: "+`height=${f}`);return new qo(11,new go(a,f))}d=yo[f];if(!d)throw new U("Invalid data-ad-layout value: "+f);c=dj(c,b);b=T(b);b=f!=="in-article"||c||a!==b?Math.ceil(d(a)):Math.ceil(d(a)*1.25);return new qo(11, 
f==="in-article"?new zo(a,b):new go(a,b))};function Bo(a){return b=>{for(let c=a.length-1;c>=0;--c)if(!a[c](b))return!1;return!0}}function Co(a,b){var c=Do.slice(0);const d=c.length;let e=null;for(let f=0;f<d;++f){const g=c[f];if(a(g)){if(b==null||b(g))return g;e===null&&(e=g)}}return e};var Z=[new Y(970,90,2),new Y(728,90,2),new Y(468,60,2),new Y(336,280,1),new Y(320,100,2),new Y(320,50,2),new Y(300,600,4),new Y(300,250,1),new Y(250,250,1),new Y(234,60,2),new Y(200,200,1),new Y(180,150,1),new Y(160,600,4),new Y(125,125,1),new Y(120,600,4),new Y(120,240,4),new Y(120,120,1,!0)],Do=[Z[6],Z[12],Z[3],Z[0],Z[7],Z[14],Z[1],Z[8],Z[10],Z[4],Z[15],Z[2],Z[11],Z[5],Z[13],Z[9],Z[16]];function Eo(a,b,c,d,e){e.google_full_width_responsive==="false"?c={H:a,F:1}:b==="autorelaxed"&&e.google_full_width_responsive||Fo(b)||e.google_ad_resize?(b=Ui(a,c,d,e),c=b!==!0?{H:a,F:b}:{H:T(c)||a,F:!0}):c={H:a,F:2};const {H:f,F:g}=c;return g!==!0?{H:a,F:g}:d.parentElement?{H:f,F:g}:{H:a,F:g}} 
function Go(a,b,c,d,e){const {H:f,F:g}=ok(247,()=>Eo(a,b,c,d,e));var h=g===!0;const k=Zd(d.style.width),m=Zd(d.style.height),{V:n,P:l,Z:p,Ua:v}=Ho(f,b,c,d,e,h);h=Io(b,p);var u;const x=(u=Zi(d,c,"marginLeft"))?`${u}px`:"",K=(u=Zi(d,c,"marginRight"))?`${u}px`:"";u=aj(d,c)||"";return new qo(h,n,p,null,v,g,l,x,K,m,k,u)}function Fo(a){return a==="auto"||/^((^|,) *(horizontal|vertical|rectangle) *)+$/.test(a)} 
function Ho(a,b,c,d,e,f){b=Jo(c,a,b);let g;var h=!1;let k=!1;var m=T(c)<488;if(m){g=Oi(d,c);var n=dj(d,c);h=!n&&g;k=n&&g}n=[bj(a),ho(b)];R(di)||n.push(cj(m,c,d,k));e.google_max_responsive_height!=null&&n.push(ej(e.google_max_responsive_height));m=[u=>!u.A];if(h||k)h=fj(c,d),m.push(ej(h));const l=Co(Bo(n),Bo(m));if(!l)throw new U(`No slot size for availableWidth=${a}`);const {V:p,P:v}=ok(248,()=>{var u;a:if(f){if(e.gfwrnh&&(u=Zd(e.gfwrnh))){u={V:new xo(a,u),P:!0};break a}u=S($h);u=u>0?a/u:a/1.2;if(e.google_resizing_allowed|| 
e.google_full_width_responsive==="true")var x=Infinity;else{x=d;let O=Infinity;do{var K=Zi(x,c,"height");K&&(O=Math.min(O,K));(K=Zi(x,c,"maxHeight"))&&(O=Math.min(O,K))}while(x.parentElement&&(x=x.parentElement)&&x.tagName!=="HTML");x=O}!(R(bi)&&x<=u*2)&&(x=Math.min(u,x),x<u*.5||x<100)&&(x=u);u={V:new xo(a,Math.floor(x)),P:x<u?102:!0}}else u={V:l,P:100};return u});return e.google_ad_layout==="in-article"&&Ko(c)?{V:Lo(a,c,d,p,e),P:!1,Z:b,Ua:g}:{V:p,P:v,Z:b,Ua:g}} 
function Io(a,b){if(a==="auto")return 1;switch(b){case 2:return 2;case 1:return 3;case 4:return 4;case 3:return 5;case 6:return 6;case 5:return 7;case 7:return 8;default:throw Error("bad mask");}}function Jo(a,b,c){if(c==="auto")c=Math.min(1200,T(a)),b=b/c<=.25?4:3;else{b=0;for(const d in Li)c.indexOf(d)!==-1&&(b|=Li[d])}return b}function Lo(a,b,c,d,e){const f=e.google_ad_height||Zi(c,b,"height");b=Ao(a,b,c,f,e).size();return b.g*b.height()>a*d.height()?new Y(b.g,b.height(),1):d} 
function Ko(a){return R(Qh)||a.location&&a.location.hash==="#hffwroe2etoq"};function Mo(a,b,c,d,e){var f;(f=T(b))?T(b)<488?b.innerHeight>=b.innerWidth?(e.google_full_width_responsive_allowed=!0,Yi(b,c),f={H:f,F:!0}):f={H:a,F:5}:f={H:a,F:4}:f={H:a,F:10};const {H:g,F:h}=f;if(h!==!0||a===g)return new qo(12,new go(a,d),null,null,!0,h,100);const {V:k,P:m,Z:n}=Ho(g,"auto",b,c,e,!0);return new qo(1,k,n,2,!0,h,m)};function No(a,b){var c=b.google_ad_format;if(c==="autorelaxed"){a:{if(b.google_content_recommendation_ui_type!=="pedestal")for(const d of ro)if(b[d]!=null){a=!0;break a}a=!1}return a?9:5}if(Fo(c))return 1;if(c==="link")return 4;if(c==="fluid"){if(c=b.google_ad_layout==="in-article")c=R(Wh)||a.location&&(a.location.hash==="#hffwroe2etop"||a.location.hash==="#hffwroe2etoq");return c?(Oo(b),1):8}if(b.google_reactive_ad_format===27)return Oo(b),1} 
function Po(a,b,c,d,e=!1){var f=b.offsetWidth||(c.google_ad_resize||e)&&Zi(b,d,"width")||c.google_ad_width||0;a===4&&(c.google_ad_format="auto",a=1);e=(e=Qo(a,f,b,c,d))?e:Go(f,c.google_ad_format,d,b,c);e.size().j(d,c,b);e.Z!=null&&(c.google_responsive_formats=e.Z);e.G!=null&&(c.google_safe_for_responsive_override=e.G);e.F!=null&&(e.F===!0?c.google_full_width_responsive_allowed=!0:(c.google_full_width_responsive_allowed=!1,c.gfwrnwer=e.F));e.P!=null&&e.P!==!0&&(c.gfwrnher=e.P);d=e.j||c.google_ad_width; 
d!=null&&(c.google_resizing_width=d);d=e.i||c.google_ad_height;d!=null&&(c.google_resizing_height=d);d=e.size().i(f);const g=e.size().height();c.google_ad_width=d;c.google_ad_height=g;var h=e.size();f=`${h.i(f)}x${h.height()}`;c.google_ad_format=f;c.google_responsive_auto_format=e.D;e.g!=null&&(c.armr=e.g);c.google_ad_resizable=!0;c.google_override_format=1;c.google_loader_features_used=128;e.F===!0&&(c.gfwrnh=`${e.size().height()}px`);e.u!=null&&(c.gfwroml=e.u);e.A!=null&&(c.gfwromr=e.A);e.i!=null&& 
(c.gfwroh=e.i);e.j!=null&&(c.gfwrow=e.j);e.B!=null&&(c.gfwroz=e.B);f=Pd(window)||window;kn(f.location,"google_responsive_dummy_ad")&&(Ma([1,2,3,4,5,6,7,8],e.D)||e.g===1)&&e.g!==2&&(f=JSON.stringify({googMsgType:"adpnt",key_value:[{key:"qid",value:"DUMMY_AD"}]}),c.dash=`<${po}>window.top.postMessage('${f}', '*'); 
          </${po}> 
          <div id="dummyAd" style="width:${d}px;height:${g}px; 
            background:#ddd;border:3px solid #f00;box-sizing:border-box; 
            color:#000;"> 
            <p>Requested size:${d}x${g}</p> 
            <p>Rendered size:${d}x${g}</p> 
          </div>`);a!==1&&(a=e.size().height(),b.style.height=`${a}px`)}function Qo(a,b,c,d,e){const f=d.google_ad_height||Zi(c,e,"height")||0;switch(a){case 5:const {H:g,F:h}=ok(247,()=>Eo(b,d.google_ad_format,e,c,d));h===!0&&b!==g&&Yi(e,c);h===!0?d.google_full_width_responsive_allowed=!0:(d.google_full_width_responsive_allowed=!1,d.gfwrnwer=h);return to(g,d);case 9:return wo(b,d);case 8:return Ao(b,e,c,f,d);case 10:return Mo(b,e,c,f,d)}}function Oo(a){a.google_ad_format="auto";a.armr=3};function Ro(a,b){a.google_resizing_allowed=!0;a.ovlp=!0;a.google_ad_format="auto";a.iaaso=!0;a.armr=b};function So(a,b){var c=Pd(b);if(c){c=T(c);const d=Sd(a,b)||{},e=d.direction;if(d.width==="0px"&&d.cssFloat!=="none")return-1;if(e==="ltr"&&c)return Math.floor(Math.min(1200,c-a.getBoundingClientRect().left));if(e==="rtl"&&c)return a=b.document.body.getBoundingClientRect().right-a.getBoundingClientRect().right,Math.floor(Math.min(1200,c-a-Math.floor((c-b.document.body.clientWidth)/2)))}return-1};function To(a,b){switch(a){case "google_reactive_ad_format":return a=parseInt(b,10),isNaN(a)?0:a;default:return b}} 
function Uo(a,b){if(a.getAttribute("src")){var c=a.getAttribute("src")||"";const d=Kd(c,"client");d&&(b.google_ad_client=To("google_ad_client",d));(c=Kd(c,"host"))&&(b.google_ad_host=To("google_ad_host",c))}for(const d of a.attributes)/data-/.test(d.name)&&(a=sa(d.name.replace("data-matched-content","google_content_recommendation").replace("data","google").replace(/-/g,"_")),b.hasOwnProperty(a)||(c=To(a,d.value),c!==null&&(b[a]=c)))} 
function Vo(a,b){if(a=re(a))switch(a.data&&a.data.autoFormat){case "rspv":return 13;case "mcrspv":return 15;default:return 14}else return b.google_ad_intent_query?17:12} 
function Wo(a,b,c,d){Uo(a,b);if(c.document&&c.document.body&&!No(c,b)&&!b.google_reactive_ad_format&&!b.google_ad_intent_query){var e=parseInt(a.style.width,10),f=So(a,c);if(f>0&&e>f){var g=parseInt(a.style.height,10);e=!!Gn[e+"x"+g];let h=f;if(e){const k=Hn(f,g);if(k)h=k,b.google_ad_format=k+"x"+g+"_0ads_al";else throw new U("No slot size for availableWidth="+f);}b.google_ad_resize=!0;b.google_ad_width=h;e||(b.google_ad_format=null,b.google_override_format=!0);f=h;a.style.width=`${f}px`;Ro(b,4)}}if(R(Vh)|| 
T(c)<488){f=Pd(c)||c;g=a.offsetWidth||Zi(a,c,"width")||b.google_ad_width||0;e=b.google_ad_client;if(d=kn(f.location,"google_responsive_slot_preview")||Mm(f,1,e,d))b:if(b.google_reactive_ad_format||b.google_ad_resize||No(c,b)||Qi(a,b))d=!1;else{for(d=a;d;d=d.parentElement){f=Sd(d,c);if(!f){b.gfwrnwer=18;d=!1;break b}if(!Ma(["static","relative"],f.position)){b.gfwrnwer=17;d=!1;break b}}if(!R(ei)&&(d=S(Zh),d=Ti(c,a,g,d,b),d!==!0)){b.gfwrnwer=d;d=!1;break b}d=c===c.top?!0:!1}d?(Ro(b,1),d=!0):d=!1}else d= 
!1;if(g=No(c,b))Po(g,a,b,c,d);else{if(Qi(a,b)){if(d=Sd(a,c))a.style.width=d.width,a.style.height=d.height,Pi(d,b);b.google_ad_width||(b.google_ad_width=a.offsetWidth);b.google_ad_height||(b.google_ad_height=a.offsetHeight);b.google_loader_features_used=256;b.google_responsive_auto_format=Vo(c,b)}else Pi(a.style,b);c.location&&c.location.hash==="#gfwmrp"||b.google_responsive_auto_format===12&&b.google_full_width_responsive==="true"?Po(10,a,b,c,!1):Math.random()<.01&&b.google_responsive_auto_format=== 
12&&(a=Ui(a.offsetWidth||parseInt(a.style.width,10)||b.google_ad_width,c,a,b),a!==!0?(b.efwr=!1,b.gfwrnwer=a):b.efwr=!0)}};function Xo(a){if(a===a.top)return 0;for(let b=a;b&&b!==b.top&&Od(b);b=b.parent){if(a.sf_)return 2;if(a.$sf)return 3;if(a.inGptIF)return 4;if(a.inDapIF)return 5}return 1};function Yo(a,b,c){for(const f of b)a:{b=a;var d=f,e=c;for(let g=0;g<b.g.length;g++){if(b.g[g].element.contains(d)){b.g[g].labels.add(e);break a}if(d.contains(b.g[g].element)){b.g[g].element=d;b.g[g].labels.add(e);break a}}b.g.push({element:d,labels:new Set([e])})}}class Zo{constructor(){this.g=[]}getSlots(){return this.g}} 
function $o(a){const b=Fk(a),c=new Zo;Yo(c,b.gb,1);Yo(c,b.hb,2);Yo(c,b.rb,3);Yo(c,b.Lb,4);Yo(c,b.ib,5);Yo(c,b.Ab,6);return c.getSlots().map(d=>{var e=new uf;var f=[...d.labels];e=Dc(e,1,f,Ub);d=d.element.getBoundingClientRect();f=new tf;f=$c(f,1,d.left+a.scrollX);f=$c(f,2,d.top+a.scrollY);f=$c(f,3,d.width);d=$c(f,4,d.height);d=qc(d);e=Mc(e,2,d);return qc(e)}).sort((d,e)=>F(A(d,tf,2),2)-F(A(e,tf,2),2))};function ug(a,b,c=0){a.g.size>0||ap(a);c=Math.min(Math.max(0,c),9);const d=a.g.get(c);d?d.push(b):a.g.set(c,[b])}function bp(a,b,c,d){qd(b,c,d);jl(a,()=>rd(b,c,d))}function cp(a,b){a.j!==1&&(a.j=1,a.g.size>0&&dp(a,b))} 
function ap(a){a.l.document.visibilityState?bp(a,a.l.document,"visibilitychange",b=>{a.l.document.visibilityState==="hidden"&&cp(a,b);a.l.document.visibilityState==="visible"&&(a.j=0)}):"onpagehide"in a.l?(bp(a,a.l,"pagehide",b=>{cp(a,b)}),bp(a,a.l,"pageshow",()=>{a.j=0})):bp(a,a.l,"beforeunload",b=>{cp(a,b)})}function dp(a,b){for(let c=9;c>=0;c--)a.g.get(c)?.forEach(d=>{d(b)})}var ep=class extends il{constructor(a){super();this.l=a;this.j=0;this.g=new Map}};async function fp(a,b){var c=10;return c<=0?Promise.reject(Error(`wfc bad input ${c} ${200}`)):b()?Promise.resolve():new Promise((d,e)=>{const f=a.setInterval(()=>{--c?b()&&(a.clearInterval(f),d()):(a.clearInterval(f),e(Error(`wfc timed out ${c}`)))},200)})};function gp(a){const b=a.g.pc;return b!==null&&b!==0?b:a.g.pc=he(a.l)}function hp(a){const b=a.g.wpc;return b!==null&&b!==""?b:a.g.wpc=Fn(a.l)}function ip(a,b){var c=new Jf;var d=gp(a);c=bd(c,1,d);d=hp(a);c=ed(c,2,d);c=bd(c,3,a.g.sd);return bd(c,7,Math.round(b||a.l.performance.now()))}async function jp(a){await fp(a.l,()=>!(!gp(a)||!hp(a)))}function kp(a){var b=J(lp);if(b.j){var c=b.A;a(c);b.g.psi=w(c)}} 
function mp(a){ug(a.u,()=>{var b=ip(a);b=Nc(b,12,Kf,a.B);a.j&&!a.g.le.includes(3)&&(a.g.le.push(3),qg(a.i,b))},9)}function np(a){const b=new Ff;ug(a.u,()=>{Mc(b,2,a.A);bd(b,3,a.g.tar);var c=a.l;var d=new yf;var e=$o(c);d=Oc(d,1,e);e=qc(wf(vf(new xf,T(c)),Ni(c).clientHeight));d=Mc(d,2,e);c=qc(wf(vf(new xf,Ni(c).scrollWidth),Ni(c).scrollHeight));c=Mc(d,3,c);c=qc(c);Mc(b,4,c);c=a.i;d=ip(a);d=Nc(d,8,Kf,b);qg(c,d)},9)} 
async function op(a){var b=J(lp);if(b.j&&!b.g.le.includes(1)){b.g.le.push(1);var c=b.l.performance.now();await jp(b);var d=new Bf;a=Ec(d,5,Qb(a),!1);d=wf(vf(new xf,Ni(b.l).scrollWidth),Ni(b.l).scrollHeight);a=Mc(a,2,d);d=wf(vf(new xf,T(b.l)),Ni(b.l).clientHeight);a=Mc(a,1,d);for(var e=d=b.l;d&&d!=d.parent;)d=d.parent,Od(d)&&(e=d);a=ed(a,4,e.location.href);d=Xo(b.l);d!==0&&(e=new Af,d=fd(e,1,d),Mc(a,3,d));d=b.i;c=ip(b,c);c=Nc(c,4,Kf,a);qg(d,c);mp(b);np(b)}} 
async function pp(a,b,c){if(a.j&&c.length&&!a.g.lgdp.includes(Number(b))){a.g.lgdp.push(Number(b));var d=a.l.performance.now();await jp(a);var e=a.i;a=ip(a,d);d=new zf;b=gd(d,1,b);c=Dc(b,2,c,Wb);c=Nc(a,9,Kf,c);qg(e,c)}}async function qp(a,b){await jp(a);var c=a.i;a=ip(a);a=bd(a,3,1);b=Nc(a,10,Kf,b);qg(c,b)} 
var lp=class{constructor(a,b){this.l=se()||window;this.u=b??new ep(this.l);this.i=a??new wg(2,fg(),100,100,!0,this.u);this.g=cl(Yk(),33,()=>{const c=S(Xh);return{sd:c,ssp:c>0&&Td()<1/c,pc:null,wpc:null,cu:null,le:[],lgdp:[],psi:null,tar:0,cc:null}})}get j(){return this.g.ssp}get A(){return ok(1178,()=>id(Ef,mc(this.g.psi||[])))||new Ef}get B(){return ok(1227,()=>id(Gf,mc(this.g.cc||[])))||new Gf}};function rp(){var a=window;return q.google_adtest==="on"||q.google_adbreak_test==="on"||a.location.host.endsWith("h5games.usercontent.goog")||a.location.host==="gamesnacks.com"?a.document.querySelector('meta[name="h5-games-eids"]')?.getAttribute("content")?.split(",").map(b=>Math.floor(Number(b))).filter(b=>!isNaN(b)&&b>0)||[]:[]};function sp(a,b){return a instanceof HTMLScriptElement&&b.test(a.src)?0:1}function tp(a){var b=L.document;if(b.currentScript)return sp(b.currentScript,a);for(const c of b.scripts)if(sp(c,a)===0)return 0;return 1};function up(a,b){return{[3]:{[55]:()=>a===0,[23]:c=>Mm(L,Number(c)),[24]:c=>Pm(Number(c)),[61]:()=>E(b,6),[63]:()=>E(b,6)||G(b,8)===".google.ch"},[4]:{},[5]:{[6]:()=>G(b,15)}}};function vp(a=q){return a.ggeac||(a.ggeac={})};function wp(a,b=document){return!!b.featurePolicy?.features().includes(a)}function xp(a,b=document){return!!b.featurePolicy?.allowedFeatures().includes(a)}function yp(a,b=navigator){try{return!!b.protectedAudience?.queryFeatureSupport?.(a)}catch(c){return!1}};function zp(a,b){try{const d=a.split(".");a=q;let e=0,f;for(;a!=null&&e<d.length;e++)f=a,a=a[d[e]],typeof a==="function"&&(a=f[d[e]]());var c=a;if(typeof c===b)return c}catch{}} 
var Ap={[3]:{[8]:a=>{try{return ia(a)!=null}catch{}},[9]:a=>{try{var b=ia(a)}catch{return}if(a=typeof b==="function")b=b&&b.toString&&b.toString(),a=typeof b==="string"&&b.indexOf("[native code]")!=-1;return a},[10]:()=>window===window.top,[6]:a=>Ma(J(ah).g(),Number(a)),[27]:a=>{a=zp(a,"boolean");return a!==void 0?a:void 0},[60]:a=>{try{return!!q.document.querySelector(a)}catch{}},[80]:a=>{try{return!!q.matchMedia(a).matches}catch{}},[69]:a=>wp(a,q.document),[70]:a=>xp(a,q.document),[79]:a=>yp(a, 
q.navigator)},[4]:{[3]:()=>$d(),[6]:a=>{a=zp(a,"number");return a!==void 0?a:void 0}},[5]:{[2]:()=>window.location.href,[3]:()=>{try{return window.top.location.hash}catch{return""}},[4]:a=>{a=zp(a,"string");return a!==void 0?a:void 0},[12]:a=>{try{const b=zp(a,"string");if(b!==void 0)return atob(b)}catch(b){}}}};var Bp=class extends I{getId(){return F(this,1)}};function Cp(a){return B(a,Bp,2,z())}var Dp=class extends I{};var Ep=class extends I{};var Fp=class extends I{g(){return Sc(this,2)}i(){return Sc(this,4)}j(){return E(this,3)}};var Gp=class extends I{};function Hp(a,b){const c=new Map;for(const [f,g]of a[1].entries()){var d=f,e=g;const {Za:h,Wa:k,Xa:m}=e[e.length-1];c.set(d,h+k*m)}for(const f of b)for(const g of B(f,Dp,2,z()))if(Cp(g).length!==0){b=Yb(sc(g,8))??0;!H(g,4)||H(g,13)||H(g,14)||(b=c.get(H(g,4))??0,d=(Yb(sc(g,1))??0)*Cp(g).length,c.set(H(g,4),b+d));d=[];for(e=0;e<Cp(g).length;e++){const h={Za:b,Wa:Yb(sc(g,1))??0,Xa:Cp(g).length,zb:e,fa:H(f,1),na:g,T:Cp(g)[e]};d.push(h)}Ip(a[2],H(g,10),d)||Ip(a[1],H(g,4),d)||Ip(a[0],Cp(g)[0].getId(),d)}return a} 
function Ip(a,b,c){if(!b)return!1;a.has(b)||a.set(b,[]);a.get(b).push(...c);return!0};function Jp(a=Td()){return b=>Vd(`${b} + ${a}`)%1E3};const Kp=[12,13,20];function Lp(a,b){var c=J(Cg).N;const d=jf(A(b.na,bf,3),c);if(!d.success)return Ag(a.M,A(b.na,bf,3),b.fa,b.T.getId(),d),!1;if(!d.value)return!1;c=jf(A(b.T,bf,3),c);return c.success?c.value?!0:!1:(Ag(a.M,A(b.T,bf,3),b.fa,b.T.getId(),c),!1)}function Mp(a,b,c){a.g[c]||(a.g[c]=[]);a=a.g[c];a.includes(b)||a.push(b)} 
function Np(a,b,c,d){const e=[];var f;if(f=b!==9)a.u[b]?f=!0:(a.u[b]=!0,f=!1);if(f)return yg(a.M,b,c,e,[],4),e;f=Kp.includes(b);const g=[],h=[];for(const l of[0,1,2])for(const [p,v]of a.ja[l].entries()){var k=p,m=v;const u=new Pf;var n=m.filter(x=>x.fa===b&&a.i[x.T.getId()]&&Lp(a,x));if(n.length)for(const x of n)h.push(x.T);else if(!a.wa&&(l===2?(n=d[1],hd(u,2,Qf,k)):n=d[0],k=n?.(String(k))??(l===2&&H(m[0].na,11)===1?void 0:d[0](String(k))),k!==void 0)){for(const x of m){if(x.fa!==b)continue;m=k- 
x.Za;n=x.Wa;const K=x.Xa,O=x.zb;m<0||m>=n*K||m%K!==O||!Lp(a,x)||(m=H(x.na,13),m!==0&&m!==void 0&&(n=a.j[String(m)],n!==void 0&&n!==x.T.getId()?zg(a.M,a.j[String(m)],x.T.getId(),m):a.j[String(m)]=x.T.getId()),h.push(x.T))}Jc(u,Qf)!==0&&(ad(u,3,k),g.push(u))}}for(const l of h)d=l.getId(),e.push(d),Mp(a,d,f?4:c),Sg(B(l,mf,2,z()),f?Ug():[c],a.M,d);yg(a.M,b,c,e,g,1);return e}function Op(a,b){b=b.map(c=>new Ep(c)).filter(c=>!Kp.includes(H(c,1)));a.ja=Hp(a.ja,b)} 
function Pp(a,b){M(1,c=>{a.i[c]=!0},b);M(2,(c,d,e)=>Np(a,c,d,e),b);M(3,c=>(a.g[c]||[]).concat(a.g[4]),b);M(12,c=>void Op(a,c),b);M(16,(c,d)=>void Mp(a,c,d),b)}var Qp=class{constructor(a,b,c,{wa:d=!1,xc:e=[]}={}){this.ja=a;this.M=c;this.u={};this.wa=d;this.g={[b]:[],[4]:[]};this.i={};this.j={};if(a=He()){a=a.split(",")||[];for(const f of a)(a=Number(f))&&(this.i[a]=!0)}for(const f of e)this.i[f]=!0}};function Rp(a,b){a.g=Wg(14,b,()=>{})}class Sp{constructor(){this.g=()=>{}}}function Tp(a){J(Sp).g(a)};function Up({sb:a,N:b,config:c,kb:d=vp(),lb:e=0,M:f=new Bg(nm(A(a,Fp,5)?.g())??0,nm(A(a,Fp,5)?.i())??0,A(a,Fp,5)?.j()??!1),ja:g=Hp({[0]:new Map,[1]:new Map,[2]:new Map},B(a,Ep,2,z(qb)))}){d.hasOwnProperty("init-done")?(Wg(12,d,()=>{})(B(a,Ep,2,z()).map(h=>w(h))),Wg(13,d,()=>{})(B(a,mf,1,z()).map(h=>w(h)),e),b&&Wg(14,d,()=>{})(b),Vp(e,d)):(Pp(new Qp(g,e,f,c),d),Xg(d),Yg(d),Zg(d),Vp(e,d),Sg(B(a,mf,1,z(qb)),[e],f,void 0,!0),Dg=Dg||!(!c||!c.xb),Tp(Ap),b&&Tp(b))} 
function Vp(a,b=vp()){$g(J(ah),b,a);Wp(b,a);Rp(J(Sp),b);J(ee).B()}function Wp(a,b){const c=J(ee);c.i=(d,e)=>Wg(5,a,()=>!1)(d,e,b);c.u=(d,e)=>Wg(6,a,()=>0)(d,e,b);c.g=(d,e)=>Wg(7,a,()=>"")(d,e,b);c.A=(d,e)=>Wg(8,a,()=>[])(d,e,b);c.j=(d,e)=>Wg(17,a,()=>[])(d,e,b);c.B=()=>{Wg(15,a,()=>{})(b)}};function Xp(a,b){b={[0]:Jp(he(b).toString())};b=J(ah).u(a,b);dh.ma(1085,pp(J(lp),a,b))}function Yp(a,b,c){var d=W(a);if(d.plle)Vp(1,vp(a));else{d.plle=!0;d=A(b,Gp,12);var e=E(b,9);Up({sb:d,N:up(c,b),config:{wa:e&&!!a.google_disable_experiments,xb:e},kb:vp(a),lb:1});if(c=G(b,15))c=Number(c),J(ah).j(c);for(const f of xc(b,19,Xb,z()))J(ah).i(f);Xp(12,a);Xp(10,a);a=Pd(a)||a;kn(a.location,"google_mc_lab")&&J(ah).i(44738307)}};function Zp(a){kk.A(b=>{b.shv=String(a);b.mjsv=En({ta:fg(),za:a});const c=J(ah).g(),d=rp();b.eid=c.concat(d).join(",")})}function $p(a,b){const c=b?.g();b=c?.g()||G(a,2);a=c?.i()?E(c,4):E(a,6);Zp(b);rb(Em,xb);Em=a};var aq={google_pause_ad_requests:!0,google_user_agent_client_hint:!0};var bq=class extends I{g(){return A(this,Bl,2)}};var cq=class extends I{g(){return G(this,1)}i(){return H(this,2)}};var dq=class extends I{u(){return G(this,1)}g(){return A(this,cq,2)}A(){return E(this,3)}i(){return E(this,4)}j(){return A(this,zl,5)}B(){return A(this,Al,6)}};var eq=class extends I{i(){return A(this,bq,2)}g(){return G(this,4)}};function Wm(a){return Zc(a,dq,27,fq)}var hq=class extends I{g(){return Zc(this,bq,13,gq)}j(){return wc(this,bq,Ic(this,gq,13))!==void 0}i(){return Zc(this,eq,14,gq)}u(){return wc(this,eq,Ic(this,gq,14))!==void 0}},gq=[13,14],fq=[27,28];function iq(a){var b=kk;try{if(rb(a,wb),a.length>0)return new hq(JSON.parse(a))}catch(c){b.I(838,c instanceof Error?c:Error(String(c)))}return new hq};function jq(a,b){if(E(b,22))return 7;if(E(b,16))return 6;const c=Wm(b)?.g()?.g();b=Wm(b)?.g()?.i()??0;a=c===a;switch(b){case 1:return a?9:8;case 2:return a?11:10;case 3:return a?13:12}return 1}function kq(a,b,c){b.google_loader_used!=="sd"&&(b.abgtt=jq(a,c))};function lq(a,b){var c=new mq;try{const f=a.createElement("link");if(f.relList?.supports("compression-dictionary")&&Ea()){var d=f;if(b instanceof Bd)d.href=Dd(b).toString(),d.rel="compression-dictionary";else{if(Gd.indexOf("compression-dictionary")===-1)throw Error('TrustedResourceUrl href attribute required with rel="compression-dictionary"');var e=Ed.test(b)?b:void 0;e!==void 0&&(d.href=e,d.rel="compression-dictionary")}a.head.appendChild(f)}}catch(f){c.nb.I(1296,f instanceof Error?f:Error(String(f)))}} 
;var mq=class{constructor(){this.nb=kk}};function nq(a){qd(window,"message",b=>{let c;try{c=JSON.parse(b.data)}catch(d){return}!c||c.googMsgType!=="sc-cnf"||a(c,b)})};function oq(a,b){return b==null?`&${a}=null`:`&${a}=${Math.floor(b)}`}function pq(a,b){return`&${a}=${b.toFixed(3)}`}function qq(){const a=new Set,b=Ek();try{if(!b)return a;const c=b.pubads();for(const d of c.getSlots())a.add(d.getSlotId().getDomId())}catch{}return a}function rq(a){a=a.id;return a!=null&&(qq().has(a)||a.startsWith("google_ads_iframe_")||a.startsWith("aswift"))} 
function sq(a,b,c){if(!a.sources)return!1;switch(tq(a)){case 2:const d=uq(a);if(d)return c.some(f=>vq(d,f));break;case 1:const e=wq(a);if(e)return b.some(f=>vq(e,f))}return!1}function tq(a){if(!a.sources)return 0;a=a.sources.filter(b=>b.previousRect&&b.currentRect);if(a.length>=1){a=a[0];if(a.previousRect.top<a.currentRect.top)return 2;if(a.previousRect.top>a.currentRect.top)return 1}return 0}function wq(a){return xq(a,b=>b.currentRect)}function uq(a){return xq(a,b=>b.previousRect)} 
function xq(a,b){return a.sources.reduce((c,d)=>{d=b(d);return c?d&&d.width*d.height!==0?d.top<c.top?d:c:c:d},null)}function vq(a,b){const c=Math.min(a.right,b.right)-Math.max(a.left,b.left);a=Math.min(a.bottom,b.bottom)-Math.max(a.top,b.top);return c<=0||a<=0?!1:c*a*100/((b.right-b.left)*(b.bottom-b.top))>=50} 
function yq(){const a=Array.from(document.getElementsByTagName("iframe")).filter(rq),b=[...qq()].map(c=>document.getElementById(c)).filter(c=>c!==null);zq=window.scrollX;Aq=window.scrollY;return Bq=[...a,...b].map(c=>c.getBoundingClientRect())} 
function Cq(){var a=new Dq;if(R(Ci)){var b=window;if(!b.google_plmetrics&&window.PerformanceObserver){b.google_plmetrics=!0;b=["layout-shift","largest-contentful-paint","first-input","longtask"];a.eb.pb&&b.push("event");for(const c of b)b={type:c,buffered:!0},c==="event"&&(b.durationThreshold=40),Eq(a).observe(b);Fq(a)}}} 
function Gq(a,b){const c=zq!==window.scrollX||Aq!==window.scrollY?[]:Bq,d=yq();for(const e of b.getEntries())switch(b=e.entryType,b){case "layout-shift":Hq(a,e,c,d);break;case "largest-contentful-paint":b=e;a.Ga=Math.floor(b.renderTime||b.loadTime);a.Fa=b.size;break;case "first-input":b=e;a.Ca=Number((b.processingStart-b.startTime).toFixed(3));a.Da=!0;a.g.some(f=>f.entries.some(g=>e.duration===g.duration&&e.startTime===g.startTime))||Iq(a,e);break;case "longtask":b=Math.max(0,e.duration-50);a.B+= 
b;a.J=Math.max(a.J,b);a.qa+=1;break;case "event":Iq(a,e);break;default:Kb(b,void 0)}}function Eq(a){a.M||(a.M=new PerformanceObserver(Hj(640,b=>{Gq(a,b)})));return a.M} 
function Fq(a){const b=Hj(641,()=>{var d=document;(d.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[d.visibilityState||d.webkitVisibilityState||d.mozVisibilityState||""]??0)===2&&Jq(a)}),c=Hj(641,()=>void Jq(a));document.addEventListener("visibilitychange",b);document.addEventListener("pagehide",c);a.Ba=()=>{document.removeEventListener("visibilitychange",b);document.removeEventListener("pagehide",c);Eq(a).disconnect()}} 
function Jq(a){if(!a.Ja){a.Ja=!0;Eq(a).takeRecords();var b="https://pagead2.googlesyndication.com/pagead/gen_204?id=plmetrics";window.LayoutShift&&(b+=pq("cls",a.D),b+=pq("mls",a.Y),b+=oq("nls",a.pa),window.LayoutShiftAttribution&&(b+=pq("cas",a.A),b+=oq("nas",a.Ia),b+=pq("was",a.Na)),b+=pq("wls",a.ra),b+=pq("tls",a.Ma));window.LargestContentfulPaint&&(b+=oq("lcp",a.Ga),b+=oq("lcps",a.Fa));window.PerformanceEventTiming&&a.Da&&(b+=oq("fid",a.Ca));window.PerformanceLongTaskTiming&&(b+=oq("cbt",a.B), 
b+=oq("mbt",a.J),b+=oq("nlt",a.qa));let d=0;for(var c of document.getElementsByTagName("iframe"))rq(c)&&d++;b+=oq("nif",d);b+=oq("ifi",xe(window));c=J(ah).g();b+=`&${"eid"}=${encodeURIComponent(c.join())}`;b+=`&${"top"}=${q===q.top?1:0}`;b+=a.La?`&${"qqid"}=${encodeURIComponent(a.La)}`:oq("pvsid",he(q));window.googletag&&(b+="&gpt=1");c=Math.min(a.g.length-1,Math.floor((a.M?a.Ea:performance.interactionCount||0)/50));c>=0&&(c=a.g[c].latency,c>=0&&(b+=oq("inp",c)));window.fetch(b,{keepalive:!0,credentials:"include", 
redirect:"follow",method:"get",mode:"no-cors"});a.Ba()}}function Hq(a,b,c,d){if(!b.hadRecentInput){a.D+=Number(b.value);Number(b.value)>a.Y&&(a.Y=Number(b.value));a.pa+=1;if(c=sq(b,c,d))a.A+=b.value,a.Ia++;if(b.startTime-a.Ha>5E3||b.startTime-a.Ka>1E3)a.Ha=b.startTime,a.i=0,a.j=0;a.Ka=b.startTime;a.i+=b.value;c&&(a.j+=b.value);a.i>a.ra&&(a.ra=a.i,a.Na=a.j,a.Ma=b.startTime+b.duration)}} 
function Iq(a,b){Kq(a,b);const c=a.g[a.g.length-1],d=a.G[b.interactionId];if(d||a.g.length<10||b.duration>c.latency)d?(d.entries.push(b),d.latency=Math.max(d.latency,b.duration)):(b={id:b.interactionId,latency:b.duration,entries:[b]},a.G[b.id]=b,a.g.push(b)),a.g.sort((e,f)=>f.latency-e.latency),a.g.splice(10).forEach(e=>{delete a.G[e.id]})}function Kq(a,b){b.interactionId&&(a.oa=Math.min(a.oa,b.interactionId),a.u=Math.max(a.u,b.interactionId),a.Ea=a.u?(a.u-a.oa)/7+1:0)} 
var Dq=class{constructor(){this.j=this.i=this.pa=this.Y=this.D=0;this.Ka=this.Ha=Number.NEGATIVE_INFINITY;this.g=[];this.G={};this.Ea=0;this.oa=Infinity;this.Ca=this.Fa=this.Ga=this.Ia=this.Na=this.A=this.Ma=this.ra=this.u=0;this.Da=!1;this.qa=this.J=this.B=0;this.M=null;this.Ja=!1;this.Ba=()=>{};const a=document.querySelector("[data-google-query-id]");this.La=a?a.getAttribute("data-google-query-id"):null;this.eb={pb:!0}}},zq,Aq,Bq=[];let Lq=null;const Mq=[],Nq=new Map;let Oq=-1;function Pq(a){return hj.test(a.className)&&a.dataset.adsbygoogleStatus!=="done"}function Qq(a,b,c){a.dataset.adsbygoogleStatus="done";Rq(a,b,c)} 
function Rq(a,b,c){var d=window;d.google_spfd||(d.google_spfd=Wo);var e=b.google_reactive_ads_config;e||Wo(a,b,d,c);Xn(d,b);if(!Sq(a,b,d)){if(e){e=e.page_level_pubvars||{};if(W(L).page_contains_reactive_tag&&!W(L).allow_second_reactive_tag){if(e.pltais){Hm(!1);return}throw new U("Only one 'enable_page_level_ads' allowed per page.");}W(L).page_contains_reactive_tag=!0;Hm(e.google_pgb_reactive===7)}b.google_unique_id=we(d);Ud(aq,(f,g)=>{b[g]=b[g]||d[g]});b.google_loader_used!=="sd"&&(b.google_loader_used= 
"aa");b.google_reactive_tag_first=(W(L).first_tag_on_page||0)===1;ok(164,()=>{bo(d,b,a,c)})}} 
function Sq(a,b,c){var d=b.google_reactive_ads_config,e=typeof a.className==="string"&&RegExp("(\\W|^)adsbygoogle-noablate(\\W|$)").test(a.className),f=Fm(c);if(f&&f.Oa&&b.google_adtest!=="on"&&!e){e=Ri(a,c);const g=Ni(c).clientHeight;e=g===0?null:e/g;if(!f.sa||f.sa&&(e||0)>=f.sa)return a.className+=" adsbygoogle-ablated-ad-slot",c=c.google_sv_map=c.google_sv_map||{},d=ka(a),b.google_element_uid=d,c[b.google_element_uid]=b,a.setAttribute("google_element_uid",String(d)),f.Jb==="slot"&&(Yd(a.getAttribute("width"))!== 
null&&a.setAttribute("width","0"),Yd(a.getAttribute("height"))!==null&&a.setAttribute("height","0"),a.style.width="0px",a.style.height="0px"),!0}if((f=Sd(a,c))&&f.display==="none"&&!(b.google_adtest==="on"||b.google_reactive_ad_format>0||d))return c.document.createComment&&a.appendChild(c.document.createComment("No ad requested because of display:none on the adsbygoogle tag")),!0;a=b.google_pgb_reactive==null||b.google_pgb_reactive===3;return b.google_reactive_ad_format!==1&&b.google_reactive_ad_format!== 
8||!a?!1:(q.console&&q.console.warn("Adsbygoogle tag with data-reactive-ad-format="+String(b.google_reactive_ad_format)+" is deprecated. Check out page-level ads at https://www.google.com/adsense"),!0)}function Tq(a){var b=document.getElementsByTagName("INS");for(let d=0,e=b[d];d<b.length;e=b[++d]){var c=e;if(Pq(c)&&c.dataset.adsbygoogleStatus!=="reserved"&&(!a||e.id===a))return e}return null} 
function Uq(a,b,c){if(a&&"shift"in a){kp(e=>{Tc(Df(e),2)||(e=Df(e),cd(e,2))});for(var d=20;a.length>0&&d>0;){try{Vq(a.shift(),b,c)}catch(e){setTimeout(()=>{throw e;})}--d}}}function Wq(){const a=Rd("INS");a.className="adsbygoogle";a.className+=" adsbygoogle-noablate";ae(a);return a} 
function Xq(a,b){const c={},d=Vm(a.google_ad_client,b);Ud(Mi,(g,h)=>{a.enable_page_level_ads===!1?c[h]=!1:a.hasOwnProperty(h)?c[h]=a[h]:d.includes(g)&&(c[h]=!1)});ja(a.enable_page_level_ads)&&(c.page_level_pubvars=a.enable_page_level_ads);const e=Wq();ie.body.appendChild(e);const f={google_reactive_ads_config:c,google_ad_client:a.google_ad_client};f.google_pause_ad_requests=!!W(L).pause_ad_requests;kq(Yq(a)||Fn(L),f,b);Qq(e,f,b);kp(g=>{Tc(Df(g),6)||(g=Df(g),cd(g,6))})} 
function Zq(a,b){vn(q).wasPlaTagProcessed=!0;const c=()=>{Xq(a,b)},d=q.document;if(d.body||d.readyState==="complete"||d.readyState==="interactive")Xq(a,b);else{const e=pd(pk(191,c));qd(d,"DOMContentLoaded",e);q.MutationObserver!=null&&(new q.MutationObserver((f,g)=>{d.body&&(e(),g.disconnect())})).observe(d,{childList:!0,subtree:!0})}} 
function Vq(a,b,c){const d={};ok(165,()=>{$q(a,d,b,c)},e=>{e.client=e.client||d.google_ad_client||a.google_ad_client;e.slotname=e.slotname||d.google_ad_slot;e.tag_origin=e.tag_origin||d.google_tag_origin})}function ar(a){delete a.google_checked_head;Ud(a,(b,c)=>{gj[c]||(delete a[c],b=c.replace("google","data").replace(/_/g,"-"),q.console.warn(`AdSense head tag doesn't support ${b} attribute.`))})} 
function br(a,b){var c=L.document.querySelector('script[src*="/pagead/js/adsbygoogle.js?client="]:not([data-checked-head])')||L.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js?client="]:not([data-checked-head])')||L.document.querySelector('script[src*="/pagead/js/adsbygoogle.js"][data-ad-client]:not([data-checked-head])')||L.document.querySelector('script[src*="/pagead/js/adsbygoogle_direct.js"][data-ad-client]:not([data-checked-head])');if(c){c.setAttribute("data-checked-head", 
"true");var d=W(window);if(d.head_tag_slot_vars)cr(c);else{kp(g=>{g=Df(g);Ec(g,7,Qb(!0),!1)});var e={};Uo(c,e);ar(e);e.google_ad_intent_query&&(e.google_responsive_auto_format=17,R(vi)&&(e.google_reactive_ad_format=42));var f=xd(e);d.head_tag_slot_vars=f;c={google_ad_client:e.google_ad_client,enable_page_level_ads:e};e.google_ad_intent_query&&(c.enable_ad_intent_display_ads=!0);e.google_overlays==="bottom"&&(c.overlays={bottom:!0});delete e.google_overlays;L.adsbygoogle||(L.adsbygoogle=[]);d=L.adsbygoogle; 
d.loaded?d.push(c):d.splice&&d.splice(0,0,c);b=Wm(b)?.i();e.google_adbreak_test||b?dr(f,a):nq(()=>{dr(f,a)})}}}function cr(a){const b=W(window).head_tag_slot_vars,c=a.getAttribute("src")||"";if((a=Kd(c,"client")||a.getAttribute("data-ad-client")||"")&&a!==b.google_ad_client)throw new U("Warning: Do not add multiple property codes with AdSense tag to avoid seeing unexpected behavior. These codes were found on the page "+a+", "+b.google_ad_client);} 
function er(a){if(typeof a==="object"&&a!=null){if(typeof a.type==="string")return 2;if(typeof a.sound==="string"||typeof a.preloadAdBreaks==="string"||typeof a.h5AdsConfig==="object")return 3}return 0} 
function $q(a,b,c,d){if(a==null)throw new U("push() called with no parameters.");kp(f=>{Tc(Df(f),3)||(f=Df(f),cd(f,3))});var e=er(a);if(e!==0)if(d=Im(),d.first_slotcar_request_processing_time||(d.first_slotcar_request_processing_time=Date.now(),d.adsbygoogle_execution_start_time=fh),Lq==null)fr(a),Mq.push(a);else if(e===3){const f=Lq;ok(787,()=>{f.handleAdConfig(a)})}else qk(730,Lq.handleAdBreak(a));else{fh=(new Date).getTime();Yn(c,d,Yq(a));gr();a:{if(!a.enable_ad_intent_display_ads&&a.enable_page_level_ads!= 
void 0){if(typeof a.google_ad_client==="string"){e=!0;break a}throw new U("'google_ad_client' is missing from the tag config.");}e=!1}if(e)kp(f=>{Tc(Df(f),4)||(f=Df(f),cd(f,4))}),hr(a,d);else if((e=a.params)&&Ud(e,(f,g)=>{b[g]=f}),b.google_ad_output==="js")console.warn("Ads with google_ad_output='js' have been deprecated and no longer work. Contact your AdSense account manager or switch to standard AdSense ads.");else{kq(Yq(a)||Fn(L),b,d);e=ir(b,a);Uo(e,b);c=W(q).head_tag_slot_vars||{};Ud(c,(f,g)=> 
{b.hasOwnProperty(g)||(b[g]=f)});if(e.hasAttribute("data-require-head")&&!W(q).head_tag_slot_vars)throw new U("AdSense head tag is missing. AdSense body tags don't work without the head tag. You can copy the head tag from your account on https://adsense.com.");if(!b.google_ad_client)throw new U("Ad client is missing from the slot.");if(c=(W(L).first_tag_on_page||0)===0&&yn(b))kp(f=>{Tc(Df(f),5)||(f=Df(f),cd(f,5))}),jr(c);(W(L).first_tag_on_page||0)===0&&(W(L).first_tag_on_page=2);b.google_pause_ad_requests= 
!!W(L).pause_ad_requests;Qq(e,b,d)}}}function Yq(a){return a.google_ad_client?a.google_ad_client:(a=a.params)&&a.google_ad_client?a.google_ad_client:""}function gr(){if(R(ii)){const a=Fm(L);a&&a.Oa||Gm(L)}}function jr(a){je(()=>{vn(q).wasPlaTagProcessed||q.adsbygoogle&&q.adsbygoogle.push(a)})}function hr(a,b){(W(L).first_tag_on_page||0)===0&&(W(L).first_tag_on_page=1);if(a.tag_partner){var c=a.tag_partner;const d=W(q);d.tag_partners=d.tag_partners||[];d.tag_partners.push(c)}zn(a,b);Zq(a,b)} 
function ir(a,b){if(a.google_ad_format==="rewarded"){if(a.google_ad_slot==null)throw new U("Rewarded format does not have valid ad slot");if(a.google_ad_loaded_callback==null)throw new U("Rewarded format does not have ad loaded callback");a.google_reactive_ad_format=11;a.google_wrap_fullscreen_ad=!0;a.google_video_play_muted=!1;a.google_acr=a.google_ad_loaded_callback;delete a.google_ad_loaded_callback;delete a.google_ad_format}var c=!!a.google_wrap_fullscreen_ad;if(c)b=Wq(),b.dataset.adsbygoogleStatus= 
"reserved",ie.documentElement.appendChild(b);else if(b=b.element){if(!Pq(b)&&(b.id?b=Tq(b.id):b=null,!b))throw new U("'element' has already been filled.");if(!("innerHTML"in b))throw new U("'element' is not a good DOM element.");}else if(b=Tq(),!b)throw new U("All 'ins' elements in the DOM with class=adsbygoogle already have ads in them.");if(c){c=L;try{const e=(c||window).document,f=e.compatMode=="CSS1Compat"?e.documentElement:e.body;var d=(new qe(f.clientWidth,f.clientHeight)).round()}catch(e){d= 
new qe(-12245933,-12245933)}a.google_ad_height=d.height;a.google_ad_width=d.width;a.fsapi=!0}return b}function kr(a){Yk().S[al(26)]=!!Number(a)} 
function lr(a){Number(a)?W(L).pause_ad_requests=!0:(W(L).pause_ad_requests=!1,a=()=>{if(!W(L).pause_ad_requests){var b={};let c;typeof window.CustomEvent==="function"?c=new CustomEvent("adsbygoogle-pub-unpause-ad-requests-event",b):(c=document.createEvent("CustomEvent"),c.initCustomEvent("adsbygoogle-pub-unpause-ad-requests-event",!!b.bubbles,!!b.cancelable,b.detail));L.dispatchEvent(c)}},q.setTimeout(a,0),q.setTimeout(a,1E3))} 
function mr(a){a&&a.call&&typeof a==="function"&&window.setTimeout(a,0)}function dr(a,b){const c={...qn()},d=S(Bi);[0,1].includes(d)&&(c.osttc=`${d}`);b=un(Md(b.Ib,new Map(Object.entries(c)))).then(e=>{Lq==null&&(e.init(a),Lq=e,nr(e))});qk(723,b);b.finally(()=>{Mq.length=0;rk("slotcar",{event:"api_ld",time:Date.now()-fh,time_pr:Date.now()-Oq});qp(J(lp),Hf(23))})} 
function nr(a){for(const [c,d]of Nq){var b=c;const e=d;e!==-1&&(q.clearTimeout(e),Nq.delete(b))}for(b=0;b<Mq.length;b++){if(Nq.has(b))continue;const c=Mq[b],d=er(c);ok(723,()=>{d===3?a.handleAdConfig(c):d===2&&qk(730,a.handleAdBreakBeforeReady(c))})}} 
function fr(a){var b=Mq.length;if(er(a)===2&&a.type==="preroll"&&a.adBreakDone!=null){var c=a.adBreakDone;Oq===-1&&(Oq=Date.now());var d=q.setTimeout(()=>{try{c({breakType:"preroll",breakName:a.name,breakFormat:"preroll",breakStatus:"timeout"}),Nq.set(b,-1),rk("slotcar",{event:"pr_to",source:"adsbygoogle"}),qp(J(lp),Hf(22))}catch(e){console.error("[Ad Placement API] adBreakDone callback threw an error:",e instanceof Error?e:Error(String(e)))}},S(Ei)*1E3);Nq.set(b,d)}};(function(a,b,c,d=()=>{}){kk.J(tk);ok(166,()=>{const e=new wg(2,a);try{Wa(n=>{Zj(e,1191,n)})}catch(n){}const f=iq(b);var g=pn(f);$p(f,g);d();pe(16,[1,w(f)]);var h=se(re(L))||L,k=En({ta:a,za:G(f,2)});const m=c(k,f);k=L.document.currentScript===null?1:tp(m.Kb);Yp(h,f,k);R(yi)&&G(f,29)&&lq(h.document,Ld`https://googleads.g.doubleclick.net/pagead/managed/dict/${G(f,29)}/adsbygoogle`);kp(n=>{var l=F(n,1)+1;ad(n,1,l);L.top===L&&(l=F(n,2)+1,ad(n,2,l));Tc(Df(n),1)||(n=Df(n),cd(n,1))});qk(1086,op(k===0)); 
if(!Da()||ta(Ga(),11)>=0){mk(R(Gi));fo();pm(Kc(f,mn,26));try{Cq()}catch{}eo();br(m,f);h=window;k=h.adsbygoogle;if(!k||!k.loaded){g={push:n=>{Vq(n,m,f)},loaded:!0,pageState:JSON.stringify(w(g))};try{Object.defineProperty(g,"requestNonPersonalizedAds",{set:kr}),Object.defineProperty(g,"pauseAdRequests",{set:lr}),Object.defineProperty(g,"onload",{set:mr})}catch{}if(k)for(const n of["requestNonPersonalizedAds","pauseAdRequests"])k[n]!==void 0&&(g[n]=k[n]);Uq(k,m,f);h.adsbygoogle=g;k&&(g.onload=k.onload)}}})})(fg(), 
typeof sttc==="undefined"?void 0:sttc,function(a,b){b=F(b,1)>2012?`_fy${F(b,1)}`:"";Ld`data:text/javascript,//show_ads_impl_preview.js`;return{Ib:Ld`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/slotcar_library${b}.js`,Gb:Ld`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/show_ads_impl${b}.js`,Fb:Ld`https://pagead2.googlesyndication.com/pagead/managed/js/adsense/${a}/show_ads_impl_with_ama${b}.js`,Kb:/^(?:https?:)?\/\/(?:pagead2\.googlesyndication\.com|securepubads\.g\.doubleclick\.net)\/pagead\/(?:js\/)?(?:show_ads|adsbygoogle(_direct)?)\.js(?:[?#].*)?$/}}); 
}).call(this,"[2021,\"r20250327\",\"r20190131\",null,null,null,null,\".google.com.sg\",null,null,null,[[[698926295,null,null,[1]],[null,619278254,null,[null,10]],[676894296,null,null,[1]],[682658313,null,null,[1]],[null,1130,null,[null,100]],[null,1340,null,[null,0.2]],[null,1338,null,[null,0.3]],[null,1336,null,[null,1.2]],[null,1339,null,[null,0.3]],[null,1032,null,[null,200],[[[12,null,null,null,4,null,\"Android\",[\"navigator.userAgent\"]],[null,500]]]],[null,1224,null,[null,0.01]],[null,1346,null,[null,6]],[null,1347,null,[null,3]],[null,1343,null,[null,300]],[null,1263,null,[null,-1]],[null,1323,null,[null,-1]],[null,1265,null,[null,-1]],[null,1264,null,[null,-1]],[1267,null,null,[1]],[null,66,null,[null,-1]],[null,65,null,[null,-1]],[1241,null,null,[1]],[1300,null,null,[1]],[null,null,null,[null,null,null,[\"en\",\"de\",\"fr\",\"es\",\"ja\"]],null,1273],[null,null,null,[null,null,null,[\"44786015\",\"44786016\"]],null,1261],[1318,null,null,[1]],[1372,null,null,[1]],[45682913,null,null,[1]],[null,717888910,null,[null,0.5]],[null,null,null,[null,null,null,[\"1\",\"2\",\"3\",\"4\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\",\"13\",\"14\",\"15\",\"16\",\"17\",\"18\",\"19\",\"20\",\"21\",\"24\",\"29\",\"30\",\"34\"]],null,null,null,627094447],[null,null,null,[null,null,null,[\"33\",\"38\"]],null,null,null,641845510],[622128248,null,null,[]],[null,null,589752731,[null,null,\"#FFFFFF\"]],[null,null,589752730,[null,null,\"#1A73E8\"]],[null,null,null,[null,null,null,[\"29_18\",\"30_19\"]],null,null,null,635821288],[null,null,null,[null,null,null,[\"29_5\",\"30_6\"]],null,null,null,636146137],[636570127,null,null,[1]],[null,717888912,null,[null,0.6]],[null,null,null,[null,null,null,[\"1\",\"2\",\"4\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\",\"13\",\"14\",\"15\",\"16\",\"17\",\"18\",\"19\",\"20\",\"21\",\"24\",\"29\",\"30\",\"34\"]],null,null,null,627094446],[null,652486359,null,[null,3]],[null,626062006,null,[null,670]],[null,688905693,null,[null,2]],[null,666400580,null,[null,22]],[null,null,null,[null,null,null,[\"\",\"ar\",\"bn\",\"en\",\"es\",\"fr\",\"hi\",\"id\",\"ja\",\"ko\",\"mr\",\"pt\",\"ru\",\"sr\",\"te\",\"th\",\"tr\",\"uk\",\"vi\",\"zh\"]],null,712458671],[null,687270738,null,[null,500]],[null,null,null,[],null,null,null,683929765],[655991266,null,null,[1]],[725532016,null,null,[1]],[null,643258048,null,[null,0.15]],[null,643258049,null,[null,0.33938]],[null,618163195,null,[null,15000]],[null,624950166,null,[null,15000]],[null,623405755,null,[null,300]],[null,508040914,null,[null,500]],[null,547455356,null,[null,49]],[null,727864505,null,[null,3]],[null,650548030,null,[null,2]],[null,650548032,null,[null,300]],[null,650548031,null,[null,1]],[null,655966487,null,[null,300]],[null,655966486,null,[null,250]],[null,469675170,null,[null,60000]],[675298507,null,null,[]],[711741274,null,null,[]],[null,684147713,null,[null,-1]],[null,684147711,null,[null,-1]],[null,684147712,null,[null,-1]],[678806782,null,null,[1]],[570863962,null,null,[1]],[null,null,570879859,[null,null,\"control_1\\\\.\\\\d\"]],[null,570863961,null,[null,50]],[570879858,null,null,[1]],[null,1085,null,[null,5]],[null,63,null,[null,30]],[null,1080,null,[null,5]],[null,10019,null,[null,5]],[10016,null,null,[1]],[null,1027,null,[null,10]],[null,57,null,[null,120]],[null,1079,null,[null,5]],[null,1050,null,[null,30]],[null,58,null,[null,120]],[null,10021,null,[null,2]],[715572365,null,null,[1]],[715572366,null,null,[1]],[555237685,null,null,[1]],[45460956,null,null,[]],[45414947,null,null,[1]],[null,472785970,null,[null,500]],[null,732217386,null,[null,10000]],[null,732217387,null,[null,500]],[null,733329086,null,[null,30000]],[null,629808663,null,[null,100]],[null,736623795,null,[null,250]],[null,550718588,null,[null,250]],[716210739,null,null,[1]],[null,624290870,null,[null,50]],[null,null,null,[null,null,null,[\"AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"Amm8\/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq\/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW\/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\",\"A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\"]],null,1934],[*********,null,null,[]]],[[12,[[10,[[31061690],[31061691,[[83,null,null,[1]],[84,null,null,[1]]]]],null,59],[40,[[95340252],[95340253,[[*********,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5],[40,[[95340254],[95340255,[[*********,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5]]],[13,[[500,[[31061692],[31061693,[[77,null,null,[1]],[78,null,null,[1]],[80,null,null,[1]],[76,null,null,[1]]]]],[4,null,6,null,null,null,null,[\"31061691\"]]]]],[10,[[50,[[31067422],[31067423,[[null,1032,null,[]]]]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[null,[[31081539],[31081540,[[711741274,null,null,[1]]]]]],[10,[[31083552],[44776368]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[10,[[31084127],[31084128]]],[1,[[31089421],[31089422,[[676460084,null,null,[1]]]]],null,139,null,null,null,998,null,null,null,null,null,8],[1,[[31089423],[31089424]],[4,null,61],139,null,null,null,998,null,null,null,null,null,8],[100,[[31090664],[31090665,[[45675667,null,null,[1]]]]]],[200,[[31091180],[31091181,[[null,728201648,null,[null,100]]]]]],[100,[[31091239],[31091240,[[732327995,null,null,[1]]]]]],[50,[[31091241],[31091242,[[736171003,null,null,[1]]]]]],[1000,[[31091323,[[null,null,14,[null,null,\"31091323\"]]],[6,null,null,null,6,null,\"31091323\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31091324,[[null,null,14,[null,null,\"31091324\"]]],[6,null,null,null,6,null,\"31091324\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[50,[[31091333],[31091334,[[736254284,null,null,[1]]]]]],[1000,[[31091361,[[null,null,14,[null,null,\"31091361\"]]],[6,null,null,null,6,null,\"31091361\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31091362,[[null,null,14,[null,null,\"31091362\"]]],[6,null,null,null,6,null,\"31091362\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31091375,[[null,null,14,[null,null,\"31091375\"]]],[6,null,null,null,6,null,\"31091375\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[1000,[[31091376,[[null,null,14,[null,null,\"31091376\"]]],[6,null,null,null,6,null,\"31091376\"]]],[4,null,55],63,null,null,null,null,null,null,null,null,2],[10,[[31091383],[31091384,[[739956550,null,null,[1]]]]]],[1,[[42531513],[42531514,[[316,null,null,[1]]]]]],[1,[[42531644],[42531645,[[368,null,null,[1]]]],[42531646,[[369,null,null,[1]],[368,null,null,[1]]]]]],[50,[[42531705],[42531706]]],[1,[[42532242],[42532243,[[1256,null,null,[1]],[290,null,null,[1]]]]]],[50,[[42532523],[42532524,[[1300,null,null,[]]]]]],[null,[[42532525],[42532526]]],[1,[[44719338],[44719339,[[334,null,null,[1]],[null,54,null,[null,100]],[null,66,null,[null,10]],[null,65,null,[null,1000]]]]]],[1,[[44801778],[44801779,[[506914611,null,null,[1]]]]],[4,null,55]],[10,[[95330276],[95330278,[[null,1336,null,[null,1]]]],[95330279,[[null,1336,null,[null,0.8]]]],[95357008,[[null,1336,null,[null,0.9]]]],[95357009,[[null,1336,null,[null,0.7]]]]]],[50,[[95331832],[95331833,[[1342,null,null,[1]]]]]],[10,[[95332584],[95332585,[[null,1343,null,[null,600]]]],[95332586,[[null,1343,null,[null,900]]]],[95332587,[[null,1343,null,[null,1200]]]]]],[10,[[95332589],[95332590,[[1344,null,null,[1]]]]]],[10,[[95332923],[95332924,[[null,1338,null,[null,0.8]]]],[95332925,[[null,1339,null,[null,0.8]]]],[95332926,[[null,1340,null,[null,0.8]]]],[95332927,[[null,1340,null,[null,0.8]],[null,1338,null,[null,0.8]],[null,1339,null,[null,0.8]]]]]],[10,[[95333409],[95333410,[[null,1346,null,[null,-1]],[null,1347,null,[null,-1]]]],[95333411,[[null,1346,null,[null,3]],[null,1347,null,[null,1]]]],[95333412,[[null,1346,null,[null,8]],[null,1347,null,[null,5]]]]]],[360,[[95334516,[[null,null,null,[null,null,null,[\"95334518\"]],null,null,null,630330362]]],[95334517,[[626390500,null,null,[1]],[null,null,null,[null,null,null,[\"95334519\"]],null,null,null,630330362]]]],[2,[[4,null,55],[12,null,null,null,2,null,\"buzzfun\\\\.me\/|diggfun\\\\.co\/|indiaimagine\\\\.com\/\"]]]],[50,[[95344787,[[null,null,null,[null,null,null,[\"95344792\"]],null,null,null,630330362]]],[95344788,[[566279275,null,null,[1]],[622128248,null,null,[1]],[null,null,null,[null,null,null,[\"95344793\"]],null,null,null,630330362]]],[95344789,[[622128248,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344794\"]],null,null,null,630330362]]],[95344790,[[566279275,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344795\"]],null,null,null,630330362]]],[95344791,[[566279275,null,null,[1]],[622128248,null,null,[1]],[566279276,null,null,[1]],[null,null,null,[null,null,null,[\"95344796\"]],null,null,null,630330362]]]],[4,null,55]],[1,[[95345037],[95345038,[[1377,null,null,[1]]]]],[4,null,55]],[null,[[95348881],[95348882,[[45650867,null,null,[1]]]]],null,130,null,null,null,null,null,null,null,null,null,7],[10,[[95352051,[[null,null,null,[null,null,null,[\"95352054\"]],null,null,null,630330362]]],[95352052,[[null,643258048,null,[]],[null,null,null,[null,null,null,[\"95352055\"]],null,null,null,630330362]]],[95352053,[[null,643258048,null,[]],[null,643258049,null,[]],[null,null,null,[null,null,null,[\"95352056\"]],null,null,null,630330362]]]],[4,null,55]],[50,[[95353386],[95353387,[[675298507,null,null,[1]]]]]],[30,[[95353420],[95353421,[[10018,null,null,[1]]]],[95355501,[[10018,null,null,[1]],[null,10021,null,[null,1.5]]]],[95355905,[[10018,null,null,[1]],[null,10023,null,[null,15]],[10022,null,null,[1]]]]]],[50,[[95353450],[95353451,[[10017,null,null,[1]]]]]],[50,[[95353929],[95353930,[[10020,null,null,[1]]]]]],[50,[[95354562],[95354563,[[1382,null,null,[1]]]]],[4,null,55]],[50,[[95354564],[95354565]],[4,null,55]],[50,[[95355310],[95355311,[[1134,null,null,[1]]]]]],[null,[[95355431,[[null,null,null,[null,null,null,[\"95355433\"]],null,null,null,630330362]]],[95355432,[[45683445,null,null,[1]],[null,null,null,[null,null,null,[\"95355434\"]],null,null,null,630330362]]]],[4,null,55]],[333,[[95356498,[[null,null,null,[null,null,null,[\"95356501\"]],null,null,null,630330362]]],[95356499,[[null,717888910,null,[null,0.6]],[null,null,null,[null,null,null,[\"95356502\"]],null,null,null,630330362]]],[95356500,[[null,717888910,null,[null,0.7]],[null,null,null,[null,null,null,[\"95356503\"]],null,null,null,630330362]]]],[4,null,55]],[333,[[95356504,[[null,null,null,[null,null,null,[\"95356507\"]],null,null,null,630330362]]],[95356505,[[null,717888912,null,[null,0.65]],[null,null,null,[null,null,null,[\"95356508\"]],null,null,null,630330362]]],[95356506,[[null,717888912,null,[null,0.7]],[null,null,null,[null,null,null,[\"95356509\"]],null,null,null,630330362]]]],[4,null,55]],[10,[[95356626,[[null,null,null,[null,null,null,[\"95356670\"]],null,null,null,630330362]]],[95356654,[[728618449,null,null,[1]],[null,null,null,[null,null,null,[\"95356671\"]],null,null,null,630330362]]]],[4,null,55]],[50,[[95357032,[[null,null,null,[null,null,null,[\"95357032\"]],null,null,null,630330362]]],[95357033,[[null,650548032,null,[null,53]],[null,null,null,[null,null,null,[\"95357033\"]],null,null,null,630330362]]]],[4,null,55],142],[50,[[95357042,[[null,null,null,[null,null,null,[\"95357042\"]],null,null,null,630330362]]],[95357043,[[null,650548032,null,[null,75]],[null,null,null,[null,null,null,[\"95357043\"]],null,null,null,630330362]]]],[4,null,55],142]]],[17,[[10,[[31084487],[31084488]],null,null,null,null,32,null,null,142,1],[10,[[31089209],[31089210]],null,null,null,null,39,null,null,189,1],[96,[[31090357]],[2,[[4,null,55],[7,null,null,15,null,20250512]]],null,null,null,null,null,null,194,1],[896,[[31090358,null,[4,null,71,null,null,null,null,[\"194\",\"14\"]]]],[2,[[4,null,55],[7,null,null,15,null,20250512]]],null,null,null,null,96,null,194,1],[null,[[31090922],[31090923,[[730909244,null,null,[1]]]],[31090924,[[730909245,null,null,[1]],[730909244,null,null,[1]]]],[31090925,[[730909244,null,null,[1]],[730909246,null,null,[1]]]],[31090926,[[730909244,null,null,[1]],[730909247,null,null,[1]]]]],null,null,null,null,null,200,null,200,1],[10,[[31090956],[31090957,[[733329085,null,null,[1]]]]],null,null,null,null,null,500,null,198,1],[10,[[31091205],[31091206,[[732217385,null,null,[1]]]]],null,null,null,null,null,700,null,198,1],[10,[[31091243],[31091244,[[736623794,null,null,[1]]]]],null,null,null,null,null,800,null,198,1],[500,[[95355300,[[null,null,null,[null,null,null,[\"95355300\",\"95355302\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95355302\"]],null,null,null,630330362]]],[95355301,[[732113159,null,null,[1]],[null,null,null,[null,null,null,[\"95355301\",\"95355303\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95355303\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,199],[100,[[95355964,[[null,null,null,[null,null,null,[\"95355964\",\"95355966\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95355966\"]],null,null,null,630330362]]],[95355965,[[720093567,null,null,[1]],[713244099,null,null,[]],[null,null,null,[null,null,null,[\"95355965\",\"95355967\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95355967\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,201],[1,[[95356661,[[null,null,null,[null,null,null,[\"95356661\",\"95356663\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95356663\"]],null,null,null,630330362]]],[95356662,[[655991266,null,null,[]],[null,null,null,[null,null,null,[\"95356662\",\"95356664\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95356664\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,203],[249,[[95356787,[[null,null,null,[null,null,null,[\"95356787\",\"95356789\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95356789\"]],null,null,null,630330362]]],[95356788,[[713244099,null,null,[1]],[null,null,null,[null,null,null,[\"95356788\",\"95356790\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95356790\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,502,null,201],[50,[[95356797,[[null,652486359,null,[null,9]],[null,null,null,[null,null,null,[\"95356799\"]],null,null,null,630330362]]],[95356798,[[null,652486359,null,[null,11]],[null,null,null,[null,null,null,[\"95356800\"]],null,null,null,630330362]]],[95356807,[[null,null,null,[null,null,null,[\"95356810\"]],null,null,null,630330362]]],[95356808,[[null,652486359,null,[null,5]],[null,null,null,[null,null,null,[\"95356811\"]],null,null,null,630330362]]],[95356809,[[null,652486359,null,[null,7]],[null,null,null,[null,null,null,[\"95356812\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,204,1],[333,[[95356927,[[null,null,null,[null,null,null,[\"95356927\",\"95356930\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95356930\"]],null,null,null,630330362]]],[95356928,[[null,null,716722045,[null,null,\"520px\"]],[null,null,null,[null,null,null,[\"95356928\",\"95356931\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95356931\"]],null,null,null,630330362]]],[95356929,[[null,null,716722045,[null,null,\"600px\"]],[null,null,null,[null,null,null,[\"95356929\",\"95356932\"]],null,null,null,631402549],[null,null,null,[null,null,null,[\"95356932\"]],null,null,null,630330362]]]],[4,null,55],null,null,null,null,null,null,205]]],[11,[[50,[[31088249],[31088250]],null,122,null,null,null,null,null,null,null,null,null,4]]]],null,null,[null,1000,1,1000]],[1,[null,[null,null,[[[null,2,null,null,null,null,\"DIV#content_views\\u003eP\"],[]],[[null,0,null,null,null,null,\"DIV#content_views\\u003eUL\"],[null,null,null,null,null,null,\"LI\"]],[[null,1,null,null,null,null,\"DIV#content_views\\u003eUL\"],[null,null,null,null,null,null,\"LI\"]]]],[]],1,null,null,null,null,null,\"ca-pub-1076724771190722\"],null,\"31091324\",1,\"blog.csdn.net\",2080902624,null,null,null,null,null,null,null,[0,0,0],[\"ca-pub-1076724771190722\",null,1,1,[],[]],null,\"m202503270101\"]");
