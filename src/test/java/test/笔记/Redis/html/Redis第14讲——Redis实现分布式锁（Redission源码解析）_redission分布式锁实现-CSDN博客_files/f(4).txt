(function(){'use strict';/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var m=this||self;function r(a){var b;a:{if(b=m.navigator)if(b=b.userAgent)break a;b=""}return b.indexOf(a)!=-1};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var t=class{constructor(a){this.g=a}toString(){return this.g}},aa=new t("about:invalid#zClosurez");class ba{constructor(a){this.da=a}}function v(a){return new ba(b=>b.substr(0,a.length+1).toLowerCase()===a+":")}const ca=new ba(a=>/^[^:]*([/?#]|$)/.test(a));var da=v("http"),ea=v("https"),fa=v("ftp"),ha=v("mailto"),ia=v("intent"),ka=v("market"),la=v("itms"),ma=v("itms-appss");const oa=[v("data"),da,ea,ha,fa,ca];function pa(a,b=oa){if(a instanceof t)return a;for(let c=0;c<b.length;++c){const d=b[c];if(d instanceof ba&&d.da(a))return new t(a)}}function qa(a,b=oa){return pa(a,b)||aa}var ra=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function w(a,b){if(b instanceof t)if(b instanceof t)b=b.g;else throw Error("");else b=ra.test(b)?b:void 0;b!==void 0&&(a.href=b)};function sa(a,b){a:{const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)if(e in d&&b.call(void 0,d[e],e,a)){b=e;break a}b=-1}return b<0?null:typeof a==="string"?a.charAt(b):a[b]};function ta(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};var ua=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function va(a,b,c){if(Array.isArray(b))for(let d=0;d<b.length;d++)va(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))}function wa(a,b,c,d){const e=c.length;for(;(b=a.indexOf(c,b))>=0&&b<d;){var g=a.charCodeAt(b-1);if(g==38||g==63)if(g=a.charCodeAt(b+e),!g||g==61||g==38||g==35)return b;b+=e+1}return-1}var xa=/#|$/; 
function ya(a,b){const c=a.search(xa);let d=wa(a,0,b,c);if(d<0)return null;let e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,e!==-1?e:0).replace(/\+/g," "))}var za=/[?&]($|#)/;const Aa=(new Date("2024-01-01T00:00:00Z")).getTime();function x(a,...b){b=b.filter(d=>d).join("&");if(!b)return a;const c=a.match(/[?&]adurl=/);return c?a.slice(0,c.index+1)+b+"&"+a.slice(c.index+1):a+(a.indexOf("?")===-1?"?":"&")+b}function Ba(a,b){switch(a){case 0:return b?"ri=1":"";case 1:return b?"ri=24":"ri=27";case 2:return b?"ri=25":"ri=26";default:return""}} 
function Ca(a){a=a.B;if(!a)return"";let b="";a.platform&&(b+="&uap="+encodeURIComponent(a.platform));a.platformVersion&&(b+="&uapv="+encodeURIComponent(a.platformVersion));a.uaFullVersion&&(b+="&uafv="+encodeURIComponent(a.uaFullVersion));a.architecture&&(b+="&uaa="+encodeURIComponent(a.architecture));a.model&&(b+="&uam="+encodeURIComponent(a.model));a.bitness&&(b+="&uab="+encodeURIComponent(a.bitness));a.fullVersionList&&(b+="&uafvl="+encodeURIComponent(a.fullVersionList.map(c=>encodeURIComponent(c.brand)+ 
";"+encodeURIComponent(c.version)).join("|")));typeof a.wow64!=="undefined"&&(b+="&uaw="+Number(a.wow64));return b.substring(1)}function y(a,b){return a.m?typeof a.i!=="string"||a.i.length===0?null:{version:3,D:a.i,v:x(a.g,"act=1",Ba(b,!0),Ca(a))}:a.s?{version:4,D:x(a.g,"dct=1","suid="+a.l,Ba(b,!1)),v:x(a.g,"act=1",Ba(b,!0),"suid="+a.l)}:null}function Da(a,b){return b===2?x(a.g,"ri=2"):b===0?x(a.g,"ri=16"):a.g} 
var Ea=class{constructor({url:a,V:b}){this.g=a;this.B=b;b=/[?&]dsh=1(&|$)/.test(a);this.m=!b&&/[?&]ae=1(&|$)/.test(a);this.s=!b&&/[?&]ae=2(&|$)/.test(a);this.h=/[?&]adurl=([^&]*)/.exec(a);this.C=!b&&/[?&]aspm=1(&|$)/.test(a);if(this.h&&this.h[1]){let c;try{c=decodeURIComponent(this.h[1])}catch(d){c=null}this.i=c}this.l=(new Date).getTime()-Aa}};function Fa(a){m.setTimeout(()=>{throw a;},0)};function Ga(){return r("iPhone")&&!r("iPod")&&!r("iPad")};function Ha(a){Ha[" "](a);return a}Ha[" "]=function(){};var Ja=Ga(),Ka=r("iPad");var La=Ga()||r("iPod"),Ma=r("iPad");var Na={},Oa=null;let Pa=void 0;function Qa(a){a=Error(a);ta(a,"warning");return a}function Ra(a,b){if(a!=null){var c=Pa??(Pa={});var d=c[a]||0;d>=b||(c[a]=d+1,a=Error(),ta(a,"incident"),Fa(a))}};function Sa(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var Ta=Sa(),Ua=Sa(),Va=Sa("m_m",!0),Wa=Sa();const A=Sa("jas",!0);var Xa;const Ya=[];Ya[A]=55;Xa=Object.freeze(Ya);function Za(a){a[A]|=34;return a};const $a=typeof Va==="symbol";var ab={};function bb(a){a=a[Va];const b=a===ab;$a&&a&&!b&&Ra(Wa,3);return b}function eb(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object}function fb(a){if(a&2)throw Error();}class gb{constructor(a,b,c){this.g=a;this.h=b;this.i=c}next(){const a=this.g.next();a.done||(a.value=this.h.call(this.i,a.value));return a}[Symbol.iterator](){return this}}var ib=Object.freeze({});const jb=BigInt(Number.MIN_SAFE_INTEGER),kb=BigInt(Number.MAX_SAFE_INTEGER);const lb=Number.isFinite;function nb(a){if(!lb(a))throw Qa("enum");return a|0}function ob(a){return a==null?a:lb(a)?a|0:void 0}function pb(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return lb(a)?a|0:void 0}function B(a){return a==null||typeof a==="string"?a:void 0} 
function qb(a,b,c,d){if(a!=null&&typeof a==="object"&&bb(a))return a;if(!Array.isArray(a))return c?d&2?((a=b[Ta])||(a=new b,Za(a.j),a=b[Ta]=a),b=a):b=new b:b=void 0,b;let e=c=a[A]|0;e===0&&(e|=d&32);e|=d&2;e!==c&&(a[A]=e);return new b(a)}function rb(a,b,c){if(b){if(typeof a!=="string")throw Error();}else a=B(a)??(c?"":void 0);return a};function sb(a){return a};const tb={},ub=(()=>class extends Map{constructor(){super()}})();function vb(a){return a}function wb(a){if(a.u&2)throw Error("Cannot mutate an immutable Map");} 
var C=class extends ub{constructor(a,b,c=vb,d=vb){super();let e=a[A]|0;e|=64;this.u=a[A]=e;this.o=b;this.F=c;this.W=this.o?xb:d;for(let g=0;g<a.length;g++){const h=a[g],f=c(h[0],!1,!0);let k=h[1];b?k===void 0&&(k=null):k=d(h[1],!1,!0,void 0,void 0,e);super.set(f,k)}}ea(){var a=yb;if(this.size!==0)return Array.from(super.entries(),a)}U(){return Array.from(super.entries())}clear(){wb(this);super.clear()}delete(a){wb(this);return super.delete(this.F(a,!0,!1))}entries(){if(this.o){var a=super.keys(); 
a=new gb(a,zb,this)}else a=super.entries();return a}values(){if(this.o){var a=super.keys();a=new gb(a,C.prototype.get,this)}else a=super.values();return a}forEach(a,b){this.o?super.forEach((c,d,e)=>{a.call(b,e.get(d),d,e)}):super.forEach(a,b)}set(a,b){wb(this);a=this.F(a,!0,!1);return a==null?this:b==null?(super.delete(a),this):super.set(a,this.W(b,!0,!0,this.o,!1,this.u))}has(a){return super.has(this.F(a,!1,!1))}get(a){a=this.F(a,!1,!1);const b=super.get(a);if(b!==void 0){var c=this.o;return c?(c= 
this.W(b,!1,!0,c,this.ca,this.u),c!==b&&super.set(a,c),c):b}}[Symbol.iterator](){return this.entries()}};C.prototype.toJSON=void 0;function xb(a,b,c,d,e,g){a=qb(a,d,c,g);e&&(a=Ab(a));return a}function zb(a){return[a,this.get(a)]}let Bb;function Cb(){return Bb||(Bb=new C(Za([]),void 0,void 0,void 0,tb))};function D(a,b,c,d,e){d=d?!!(b&32):void 0;const g=[];var h=a.length;let f,k,l,n=!1;b&64?(b&256?(h--,f=a[h],k=h):(k=4294967295,f=void 0),e||b&512||(n=!0,l=(Db??sb)(f?k- -1:b>>15&1023||536870912,-1,a,f),k=l+-1)):(k=4294967295,b&1||(f=h&&a[h-1],eb(f)?(h--,k=h,l=0):f=void 0));let p=void 0;for(let q=0;q<h;q++){let u=a[q];u!=null&&(u=c(u,d))!=null&&(q>=k?(p??(p={}))[q- -1]=u:g[q]=u)}if(f)for(let q in f)a=f[q],a!=null&&(a=c(a,d))!=null&&(h=+q,h<l?g[h+-1]=a:(p??(p={}))[q]=a);p&&(n?g.push(p):g[k]=p);e&&(g[A]= 
b&33522241|(p!=null?290:34));return g}function yb(a){a[0]=Eb(a[0]);a[1]=Eb(a[1]);return a}function Eb(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=jb&&a<=kb?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[A]|0;return a.length===0&&b&1?void 0:D(a,b,Eb,!1,!1)}if(bb(a))return E(a);if(a instanceof C)return a.ea();return}return a}let Db;function E(a){a=a.j;return D(a,a[A]|0,Eb,void 0,!1)};function Fb(){Ra(Ua,5)};function Gb(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[A]|0;return a.length===0&&c&1?void 0:c&2?a:b&&(c===0||c&32&&!(c&64)&&c&16)?(a[A]|=34,c&4&&Object.freeze(a),a):D(a,c,Gb,b!==void 0,!0)}if(bb(a))return Hb(a);if(a instanceof C){c=a.u;if(c&2)return a;if(a.size){b=Za(a.U());if(a.o)for(a=0;a<b.length;a++){const d=b[a];let e=d[1];if(e==null||typeof e!=="object")e=void 0;else if(bb(e))e=Hb(e);else if(Array.isArray(e)){const g=e[A]|0;c&32&&(g===0||g&32&&!(g&64)&&g&16)?e[A]|=34: 
e=D(e,g,Gb,!0,!0)}else e=void 0;d[1]=e}return b}}}function Hb(a){const b=a.j,c=b[A]|0;return c&2?a:D(b,c,Gb,!0,!0)}function Ib(a){var b=a.j;a=new a.constructor(D(b,b[A]|0,Gb,!0,!0));b=a.j;b[A]&=-3;return a}function Ab(a){var b=a.j;if(!((b[A]|0)&2))return a;a=new a.constructor(D(b,b[A]|0,Gb,!0,!0));b=a.j;b[A]&=-3;return a};function H(a,b){a=a.j;return Jb(a,a[A]|0,b)}function Jb(a,b,c){if(c===-1)return null;const d=c+(b&512?0:-1),e=a.length-1;if(d>=e&&b&256)a=a[e][c];else if(d<=e)a=a[d];else return;return a}function Kb(a,b,c){const d=a.j;let e=d[A]|0;fb(e);J(d,e,b,c);return a}function J(a,b,c,d){const e=b&512?0:-1,g=c+e;var h=a.length-1;if(g>=h&&b&256)return a[h][c]=d,b;if(g<=h)return a[g]=d,b;d!==void 0&&(h=b>>15&1023||536870912,c>=h?d!=null&&(a[h+e]={[c]:d},b|=256,a[A]=b):a[g]=d);return b} 
function L(a,b,c){return Lb(a,b,c)!==void 0} 
function Mb(a,b,c,d,e){const g=a.j;a=g[A]|0;const h=2&a?1:d;e=!!e;d=Nb(g,a,b);let f=d[A]|0;if(!(4&f)){4&f&&(d=[...d],f=M(f,a),a=J(g,a,b,d));let k=0,l=0;for(;k<d.length;k++){const n=c(d[k]);n!=null&&(d[l++]=n)}l<k&&(d.length=l);f=Ob(f,a);c=(f|20)&-2049;f=c&=-4097;d[A]=f;2&f&&Object.freeze(d)}h===1||h===4&&32&f?N(f)||(e=f,f|=2,f!==e&&(d[A]=f),Object.freeze(d)):(h===2&&N(f)&&(d=[...d],f=M(f,a),f=Pb(f,a,e),d[A]=f,a=J(g,a,b,d)),N(f)||(b=f,f=Pb(f,a,e),f!==b&&(d[A]=f)));return d} 
function Nb(a,b,c){a=Jb(a,b,c);return Array.isArray(a)?a:Xa}function Ob(a,b){a===0&&(a=M(a,b),a|=16);return a|1}function N(a){return!!(2&a)&&!!(4&a)||!!(1024&a)} 
function Qb(a,b,c,d,e,g){const h=b&2;a:{var f=c,k=b&2;c=!1;if(f==null){if(k){a=Cb();break a}f=[]}else if(f.constructor===C){if((f.u&2)==0||k){a=f;break a}f=f.U()}else Array.isArray(f)?c=!!((f[A]|0)&2):f=[];if(k){if(!f.length){a=Cb();break a}c||(c=!0,Za(f))}else if(c){c=!1;k=[...f];for(f=0;f<k.length;f++){const l=k[f]=[...k[f]];Array.isArray(l[1])&&(l[1]=Za(l[1]))}f=k}c||((f[A]|0)&64?f[A]&=-33:32&b&&(f[A]|=32));g=new C(f,e,rb,g);J(a,b,d,g);a=g}!h&&e&&(a.ca=!0);return a} 
function Rb(a,b){a=a.j;const c=a[A]|0;return Qb(a,c,Jb(a,c,b),b,void 0,rb)}function Sb(a){var b=Tb;a=a.j;const c=a[A]|0;return Qb(a,c,Jb(a,c,1),1,b)}function Lb(a,b,c){a=a.j;let d=a[A]|0;const e=Jb(a,d,c);b=qb(e,b,!1,d);b!==e&&b!=null&&J(a,d,c,b);return b}function O(a,b,c){b=Lb(a,b,c);if(b==null)return b;a=a.j;let d=a[A]|0;if(!(d&2)){const e=Ab(b);e!==b&&(b=e,J(a,d,c,b))}return b}function M(a,b){2&a&&(a|=16);a=(2&b?a|2:a&-3)|32;return a&=-1025}function Pb(a,b,c){32&b&&c||(a&=-33);return a} 
function P(a,b){a=H(a,b);return(a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0)??!1}function Q(a,b){return B(H(a,b))??""}function R(a,b,c=0){return ob(H(a,b))??c}function Ub(a,b,c){if(c!=null&&typeof c!=="boolean")throw a=typeof c,Error(`Expected boolean but got ${a!="object"?a:c?Array.isArray(c)?"array":a:"null"}: ${c}`);return Kb(a,b,c)}function Vb(a,b,c){if(c!=null){if(typeof c!=="number")throw Qa("int32");if(!lb(c))throw Qa("int32");c|=0}Kb(a,b,c)} 
function S(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return Kb(a,b,c)}function Wb(a,b,c){Kb(a,b,c==null?c:nb(c))};var T=class{constructor(a){a:{if(a==null){var b=96;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[A]|0;8192&b||!(64&b)||2&b||Fb();if(b&1024)throw Error("farr");if(b&64){b&16384||(a[A]=b|16384);var c=a;break a}var d=a;b|=64;var e=d.length;if(e){var g=e-1;e=d[g];if(eb(e)){b|=256;const h=b&512?0:-1;g-=h;if(g>=1024)throw Error("pvtlmt");for(c in e){const f=+c;if(f<g)d[f+h]=e[c],delete e[c];else break}b=b&-33521665|(g&1023)<<15}}}a[A]=b|16384;c=a}this.j=c}toJSON(){return E(this)}}; 
T.prototype[Va]=ab;T.prototype.toString=function(){return this.j.toString()};var Xb=class extends T{};var Yb=class extends T{};var Zb=class extends T{};var $b=class extends T{};var ac=class extends T{g(){return P(this,1)}h(){return P(this,2)}};var bc=class extends T{A(){return Q(this,3)}T(a){Ub(this,5,a)}};var U=class extends T{A(){return Q(this,1)}T(a){Ub(this,2,a)}};var cc=class extends T{};var dc=class extends T{g(){return B(H(this,1))!=null}};function ec(a){var b=a.j[A]|0,c=b,d=!(2&b);b=void 0===ib?2:4;a=a.j;var e=!!(2&c);const g=e?1:b;d&&(d=!e);b=Nb(a,c,7);var h=b[A]|0;e=!!(4&h);if(!e){h=Ob(h,c);var f=b,k=c,l=!!(2&h);l&&(k|=2);let n=!l,p=!0,q=0,u=0;for(;q<f.length;q++){const z=qb(f[q],bc,!1,k);if(z instanceof bc){if(!l){const F=!!((z.j[A]|0)&2);n&&(n=!F);p&&(p=F)}f[u++]=z}}u<q&&(f.length=u);h|=4;h=p?h|16:h&-17;h=n?h|8:h&-9;f[A]=h;l&&Object.freeze(f)}if(d&&!(8&h||!b.length&&(g===1||g===4&&32&h))){N(h)&&(b=[...b],h=M(h,c),c=J(a,c,7,b)); 
d=b;f=h;for(h=0;h<d.length;h++)k=d[h],l=Ab(k),k!==l&&(d[h]=l);f|=8;f=d.length?f&-17:f|16;h=d[A]=f}g===1||g===4&&32&h?N(h)||(c=h,h|=!b.length||16&h&&(!e||32&h)?2:1024,h!==c&&(b[A]=h),Object.freeze(b)):(g===2&&N(h)&&(b=[...b],h=M(h,c),h=Pb(h,c,!1),b[A]=h,c=J(a,c,7,b)),N(h)||(a=h,h=Pb(h,c,!1),h!==a&&(b[A]=h)));return b}var Tb=class extends T{g(){return O(this,dc,26)}h(){return P(this,21)}};var fc=class extends T{};var gc=class extends T{};function hc(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}};var ic={capture:!0},jc={passive:!0},kc=hc(function(){let a=!1;try{const b=Object.defineProperty({},"passive",{get:function(){a=!0}});m.addEventListener("test",null,b)}catch(b){}return a});function lc(a){return a?a.passive&&kc()?a:a.capture||!1:!1}function V(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,lc(d))};function mc(a){let b=0;for(const c in a)b++};function nc(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}var oc=a=>{a.preventDefault?a.preventDefault():a.returnValue=!1};let tc=[];const uc=()=>{const a=tc;tc=[];for(const b of a)try{b()}catch{}}; 
var vc=a=>{tc.push(a);tc.length==1&&(window.Promise?Promise.resolve().then(uc):window.setImmediate?setImmediate(uc):setTimeout(uc,0))},wc=a=>{var b=W;b.readyState==="complete"||b.readyState==="interactive"?vc(a):b.addEventListener("DOMContentLoaded",a)},xc=a=>{var b=window;b.document.readyState==="complete"?vc(a):b.addEventListener("load",a)};function yc(a=document){return a.createElement("img")};function zc(a,b,c=null,d=!1){Ac(a,b,c,d)}function Ac(a,b,c,d){a.google_image_requests||(a.google_image_requests=[]);const e=yc(a.document);if(c||d){const g=h=>{c&&c(h);if(d){h=a.google_image_requests;const f=Array.prototype.indexOf.call(h,e,void 0);f>=0&&Array.prototype.splice.call(h,f,1)}e.removeEventListener&&e.removeEventListener("load",g,lc());e.removeEventListener&&e.removeEventListener("error",g,lc())};V(e,"load",g);V(e,"error",g)}e.src=b;a.google_image_requests.push(e)};let Bc=0;function Cc(){const a=Dc(Bc,document.currentScript);a&&(a.dataset.initialized="true")}function Ec(a){return(a=Dc(a,document.currentScript))&&a.getAttribute("data-jc-version")||"unknown"}function Dc(a,b=null){return b&&b.getAttribute("data-jc")===String(a)?b:document.querySelector(`[${"data-jc"}="${a}"]`)} 
function Fc(a){if(!(Math.random()>.01)){var b=Dc(a,document.currentScript);a=`https://${b&&b.getAttribute("data-jc-rcd")==="true"?"pagead2.googlesyndication-cn.com":"pagead2.googlesyndication.com"}/pagead/gen_204?id=jca&jc=${a}&version=${Ec(a)}&sample=${.01}`;b=window;var c;if(c=b.navigator)c=b.navigator.userAgent,c=/Chrome/.test(c)&&!/Edge/.test(c)?!0:!1;c&&typeof b.navigator.sendBeacon==="function"?b.navigator.sendBeacon(a):zc(b,a,void 0,!1)}};var W=document,Gc=window;var Hc=a=>{var b=W;try{return b.querySelectorAll("*["+a+"]")}catch(c){return[]}};var Ic=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function Jc(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(d){b=c;break a}b=void 0}return b};const Kc=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var Lc=class{constructor(a,b){this.g=a;this.h=b}},Mc=class{constructor(a,b){this.url=a;this.g=!!b;this.depth=null}};let Nc=null;function Oc(){const a=m.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function Pc(){const a=m.performance;return a&&a.now?a.now():null};var Qc=class{constructor(a,b){var c=Pc()||Oc();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const X=m.performance,Rc=!!(X&&X.mark&&X.measure&&X.clearMarks),Sc=hc(()=>{var a;if(a=Rc){var b;a=window;if(Nc===null){Nc="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(Nc=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=Nc;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function Tc(a){a&&X&&Sc()&&(X.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),X.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};function Uc(a,b){const c={};c[a]=b;return[c]}function Vc(a,b,c,d,e){const g=[];nc(a,(h,f)=>{(h=Wc(h,b,c,d,e))&&g.push(`${f}=${h}`)});return g.join(b)} 
function Wc(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const g=[];for(let h=0;h<a.length;h++)g.push(Wc(a[h],b,c,d+1,e));return g.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Vc(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function Xc(a){let b=1;for(const c in a.h)c.length>b&&(b=c.length);return 3997-b-a.i.length-1} 
function Yc(a,b){let c="https://pagead2.googlesyndication.com"+b,d=Xc(a)-b.length;if(d<0)return"";a.g.sort((g,h)=>g-h);b=null;let e="";for(let g=0;g<a.g.length;g++){const h=a.g[g],f=a.h[h];for(let k=0;k<f.length;k++){if(!d){b=b==null?h:b;break}let l=Vc(f[k],a.i,",$");if(l){l=e+l;if(d>=l.length){d-=l.length;c+=l;e=a.i;break}b=b==null?h:b}}}a="";b!=null&&(a=`${e}${"trn"}=${b}`);return c+a}var Zc=class{constructor(){this.i="&";this.h={};this.l=0;this.g=[]}};function $c(a,b,c){let d,e;try{a.g&&a.g.g?(e=a.g.start(b.toString(),3),d=c(),a.g.end(e)):d=c()}catch(g){c=!0;try{Tc(e),c=a.m(b,new Ic(g,{message:Jc(g)}),void 0,void 0)}catch(h){a.l(217,h)}if(c)window.console?.error?.(g);else throw g;}return d}function ad(a,b){var c=bd;return(...d)=>$c(c,a,()=>b.apply(void 0,d))} 
var ed=class{constructor(a=null){this.pinger=cd;this.g=a;this.h=null;this.i=!1;this.m=this.l}l(a,b,c,d,e){e=e||"jserror";let g=void 0;try{const K=new Zc;var h=K;h.g.push(1);h.h[1]=Uc("context",a);b.error&&b.meta&&b.id||(b=new Ic(b,{message:Jc(b)}));h=b;if(h.msg){b=K;var f=h.msg.substring(0,512);b.g.push(2);b.h[2]=Uc("msg",f)}var k=h.meta||{};f=k;if(this.h)try{this.h(f)}catch(I){}if(d)try{d(f)}catch(I){}d=K;k=[k];d.g.push(3);d.h[3]=k;var l;if(!(l=u)){d=m;k=[];let I;f=null;do{var n=d;try{var p;if(p= 
!!n&&n.location.href!=null)b:{try{Ha(n.foo);p=!0;break b}catch(G){}p=!1}var q=p}catch{q=!1}q?(I=n.location.href,f=n.document&&n.document.referrer||null):(I=f,f=null);k.push(new Mc(I||""));try{d=n.parent}catch(G){d=null}}while(d&&n!==d);for(let G=0,pc=k.length-1;G<=pc;++G)k[G].depth=pc-G;n=m;if(n.location&&n.location.ancestorOrigins&&n.location.ancestorOrigins.length===k.length-1)for(q=1;q<k.length;++q){const G=k[q];G.url||(G.url=n.location.ancestorOrigins[q-1]||"",G.g=!0)}l=k}var u=l;let Ia=new Mc(m.location.href, 
!1);l=null;const cb=u.length-1;for(n=cb;n>=0;--n){var z=u[n];!l&&Kc.test(z.url)&&(l=z);if(z.url&&!z.g){Ia=z;break}}z=null;const Cd=u.length&&u[cb].url;Ia.depth!==0&&Cd&&(z=u[cb]);g=new Lc(Ia,z);if(g.h){u=K;var F=g.h.url||"";u.g.push(4);u.h[4]=Uc("top",F)}var db={url:g.g.url||""};if(g.g.url){const I=g.g.url.match(ua);var na=I[1],qc=I[3],rc=I[4];F="";na&&(F+=na+":");qc&&(F+="//",F+=qc,rc&&(F+=":"+rc));var sc=F}else sc="";na=K;db=[db,{url:sc}];na.g.push(5);na.h[5]=db;dd(this.pinger,e,K,this.i,c)}catch(K){try{dd(this.pinger, 
e,{context:"ecmserr",rctx:a,msg:Jc(K),url:g?.g.url??""},this.i,c)}catch(Ia){}}return!0}};class fd{};function dd(a,b,c,d=!1,e){if((d?a.g:Math.random())<(e||.01))try{let g;c instanceof Zc?g=c:(g=new Zc,nc(c,(f,k)=>{var l=g;const n=l.l++;f=Uc(k,f);l.g.push(n);l.h[n]=f}));const h=Yc(g,"/pagead/gen_204?id="+b+"&");h&&zc(m,h)}catch(g){}}function gd(){var a=cd,b=window.google_srt;b>=0&&b<=1&&(a.g=b)}var hd=class{constructor(){this.g=Math.random()}};const id=[da,ea,ha,fa,ca,ka,la,ia,ma];function jd(a,b){if(a instanceof t)return a;const c=qa(a,id);c===aa&&b(a);return c} 
function kd(a){var b=`${Gc.location.protocol==="http:"?"http:":"https:"}//${"pagead2.googlesyndication.com"}/pagead/gen_204`;return c=>{c={id:"unsafeurl",ctx:a,url:c};var d=[];for(e in c)va(e,c[e],d);var e=d.join("&");if(e){c=b.indexOf("#");c<0&&(c=b.length);d=b.indexOf("?");let g;d<0||d>c?(d=c,g=""):g=b.substring(d+1,c);c=[b.slice(0,d),g,b.slice(c)];d=c[1];c[1]=e?d?d+"&"+e:e:d;e=c[0]+(c[1]?"?"+c[1]:"")+c[2]}else e=b;navigator.sendBeacon&&navigator.sendBeacon(e,"")}};let cd,bd; 
const Y=new class{constructor(a,b){this.h=[];this.i=b||m;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.h=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.g=Sc()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.g)return null;a=new Qc(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;X&&Sc()&&X.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(Pc()||Oc())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;X&&Sc()&&X.mark(b);!this.g|| 
this.h.length>2048||this.h.push(a)}}}(1,window);function ld(){window.google_measure_js_timing||(Y.g=!1,Y.h!==Y.i.google_js_reporting_queue&&(Sc()&&Array.prototype.forEach.call(Y.h,Tc,void 0),Y.h.length=0))}(function(a){cd=a??new hd;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());gd();bd=new ed(Y);bd.h=b=>{const c=Bc;c!==0&&(b.jc=String(c),b.shv=Ec(c))};bd.i=!0;window.document.readyState==="complete"?ld():Y.g&&V(window,"load",()=>{ld()})})();function md(a,b){return ad(a,b)} 
function nd(a,b){var c=fd;var d="S";c.S&&c.hasOwnProperty(d)||(d=new c,c.S=d);c=[];!b.eid&&c.length&&(b.eid=c.toString());dd(cd,a,b,!0)};function od(a=window){return a};mc({qa:0,pa:1,ma:2,ga:3,na:4,ha:5,oa:6,ka:7,la:8,fa:9,ia:10,ra:11});mc({ta:0,ua:1,sa:2});function pd(a){var b=new qd;fb(b.j[A]|0);b=Mb(b,1,ob,2,!0);if(Array.isArray(a)){var c=a.length;for(let d=0;d<c;d++)b.push(nb(a[d]))}else for(c of a)b.push(nb(c))}var qd=class extends T{};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);pd([1,8,9,10,11,12,2,3,4,5,15,16,19,20,21,23]);pd([1,6,7,9,10,11,12,2,3,4,5,13,14,18,19,20,21,23]);pd([1,6,7,9,10,11,12,22,2,3,4,5,13,14,17,18,19,20,21,23]);new qd;var rd=(a,b)=>{b=Q(a,2)||b;if(!b)return"";if(P(a,13))return b;const c=/[?&]adurl=([^&]+)/.exec(b);if(!c)return b;const d=[b.slice(0,c.index+1)];Rb(a,4).forEach((e,g)=>{d.push(encodeURIComponent(g)+"="+encodeURIComponent(e)+"&")});d.push(b.slice(c.index+1));return d.join("")},sd=(a,b=[])=>{b=b.length>0?b:Hc("data-asoch-targets");a=Sb(a);const c=[];for(let f=0;f<b.length;++f){var d=b[f].getAttribute("data-asoch-targets"),e=d.split(","),g=!0;for(let k of e)if(!a.has(k)){g=!1;break}if(g){g=a.get(e[0]); 
for(d=1;d<e.length;++d){var h=a.get(e[d]);g=E(Ib(g));h=E(h);const k=Math.max(g.length,h.length);for(let l=0;l<k;++l)g[l]==null&&(g[l]=h[l]);g=new Tb(g)}e=Rb(g,4);ob(H(g,5))!=null&&e.set("nb",R(g,5).toString());c.push({element:b[f],data:g})}else nd("gdn-asoch",{type:1,data:d})}return c},vd=(a,b,c,d)=>{c=rd(b,c);if(c.length!==0){var e=d===609;if(ya(c,"ase")==="2"&&d!==1087){let g;const h=!!W.featurePolicy?.allowedFeatures().includes("attribution-reporting");g=e?4:h?6:5;let f="";const k=y(new Ea({url:c}), 
0);e||h&&(!k||k.version!==3)?(c=td(c,"nis",g.toString()),a.setAttribute("attributionsrc",f)):h&&k&&k.version===3&&(f=td(k.v,"nis",g.toString()),a.setAttribute("attributionsrc",f))}e&&L(b,Xb,24)&&(e=O(b,Xb,24),a.setAttribute("attributionDestination",Q(e,2)),a.setAttribute("attributionSourceNonce",Q(e,1)));(e=(e=Dc(35))&&e.dataset.expanded?e.dataset.expanded:null)&&d===557&&(c=ud(c,"expanded",e));w(a,jd(c,kd(d)));a.target||(a.target=B(H(b,11))!=null?Q(b,11):"_top")}},wd=a=>{for(const b of a)if(a=b.data, 
b.element.tagName=="A"&&!P(a,1)){const c=b.element;vd(c,a,c.href,609)}},ud=(a,b,c)=>{b=encodeURIComponent(b);const d=encodeURIComponent(c);c=new RegExp("[?&]"+b+"=([^&]+)");const e=c.exec(a);b=b+"="+d;return e?a.replace(c,e[0].charAt(0)+b):a.replace("?","?"+b+"&")},xd=a=>{const b=m.oneAfmaInstance;if(b)for(const c of a)if((a=c.data)&&L(a,cc,8)){const d=Q(O(a,cc,8),4);if(d){b.fetchAppStoreOverlay(d,void 0,Q(O(a,cc,8),6));break}}},yd=(a,b=500)=>{const c=[],d=[];for(var e of a)(a=e.data)&&L(a,U,12)&& 
(d.push(O(a,U,12)),c.push(O(a,U,12).A()));e=(g,h)=>{if(h)for(const f of d)h[f.A()]&&f.T(!0)};a=m.oneAfmaInstance;for(const g of c)a?.canOpenAndroidApp(g,e,()=>{},b)},Ad=(a,b,c,d)=>{if(!b||!L(b,cc,8))return!1;const e=O(b,cc,8);let g=Q(e,2);Rb(b,10).forEach((f,k)=>{g=ud(g,k,f)});zd(b)&&P(b,15)&&!/[?&]label=/.test(c)&&(c=td(c,"label","deep_link_fallback"));a?.g()&&nd("skspvc",{});const h=f=>d.openStoreOverlay(f,void 0,Q(e,6));return d.redirectForStoreU2({clickUrl:c,trackingUrl:Q(e,3),finalUrl:g,pingFunc:P(b, 
13)?d.httpTrack:d.click,openFunc:a?.g()?h:d.openIntentOrNativeApp,isExternalClickUrl:P(b,13)})},Bd=(a,b,c,d)=>{c&&c.startsWith("intent:")?d.openIntentOrNativeApp(c):a?b?d.openBrowser(c):d.openChromeCustomTab(c):d.openSystemBrowser(c,{useFirstPackage:!0,useRunningProcess:!0})},Ed=(a,b,c,d,e,g,h,f=!1,k=!1)=>{const l=P(e,15);if(!k&&l&&B(H(e,22))!=null)Bd(c,d,Q(e,22),h);else{var n=y(new Ea({url:g}),0);k=l&&(n&&n.version===3||k);if(a&&b&&!k&&(g=Dd(g,h.click,!0),f&&e?.h()))return;Bd(c,d,g,h)}},Dd=(a,b= 
null,c=!1)=>{if(b===null){({V:b}={});var d=new Ea({url:a,V:b});d.C?(b=y(d,1))?(fetch(b.v,{method:"GET",keepalive:!0,mode:"no-cors",redirect:"follow"}),b=b.D):b=a:b=(b=y(d,0))?navigator.sendBeacon?navigator.sendBeacon(b.v,"")?b.D:Da(d,2):Da(d,0):a;return b}d=y(new Ea({url:a}),0);if(!d)return a;a=d.v;if(d.version===4&&c){var e=encodeURIComponent("ase");c=encodeURIComponent("3");e=new RegExp("[?&]"+e+"=([^&]+)","g");let g=0;const h=[];for(let f=e.exec(a);f!==null;){if(f[1]==c){let k=f[0].charAt(0)== 
"?"?1:0;h.push(a.slice(g,f.index+k));g=f.index+f[0].length+k}f=e.exec(a)}h.push(a.slice(g));a=h.join("")}b(a);return d.D},Fd=(a,b=!0)=>{b&&Gc.fetch?Gc.fetch(a,{method:"GET",keepalive:!0,mode:"no-cors"}).then(c=>{c.ok||zc(Gc,a)}):zc(Gc,a)},td=(a,b,c)=>{b=encodeURIComponent(String(b));c=encodeURIComponent(String(c));return a.replace("?","?"+b+"="+c+"&")},zd=a=>{for(const b of ec(a))if(R(b,1)===3&&Q(b,2))return!0;return!1};var Z=(a,b)=>a&&(a=a.match(b+"=([^&]+)"))&&a.length==2?a[1]:"";var Gd=class extends T{};function Hd(a,b){return S(a,2,b)}function Id(a,b){return S(a,3,b)}function Jd(a,b){return S(a,4,b)}function Kd(a,b){return S(a,5,b)}function Ld(a,b){return S(a,9,b)}function Md(a,b){{const l=a.j;let n=l[A]|0;fb(n);if(b==null)J(l,n,10);else{var c=b[A]|0,d=c,e=N(c),g=e||Object.isFrozen(b),h=!0,f=!0;for(let p=0;p<b.length;p++){var k=b[p];e||(k=!!((k.j[A]|0)&2),h&&(h=!k),f&&(f=k))}e||(c=h?13:5,c=f?c|16:c&-17);g&&c===d||(b=[...b],d=0,c=M(c,n),c=Pb(c,n,!0));c!==d&&(b[A]=c);J(l,n,10,b)}}return a} 
function Nd(a,b){return Ub(a,11,b)}function Od(a,b){return S(a,1,b)}function Pd(a,b){return Ub(a,7,b)}var Qd=class extends T{};const Rd="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Sd(){var a=window;if(typeof a.navigator?.userAgentData?.getHighEntropyValues!=="function")return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Rd).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} 
function Td(a){return Nd(Md(Kd(Hd(Od(Jd(Pd(Ld(Id(new Qd,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),a.fullVersionList?.map(b=>{var c=new Gd;c=S(c,1,b.brand);return S(c,2,b.version)})||[]),a.wow64||!1)}function Ud(){return Sd()?.then(a=>Td(a))??null};function Vd(a){for(const b of a)if(b.element.tagName=="A"){a=b.element;const c=b.data;B(H(c,2))==null&&S(c,2,a.href)}}function Wd(a,b){return sa(a,c=>c.element===b)}function Xd(a){wc(md(556,()=>{new Yd(a||{})}))} 
function Zd(a,b){if(!b.defaultPrevented||a.H===b){for(var c,d,e=b.target;(!c||!d)&&e;){d||e.tagName!="A"||(d=e);var g=e.hasAttribute("data-asoch-targets"),h=e.hasAttribute("data-asoch-fixed-value");if(!c)if(h)c=new Tb(JSON.parse(e.getAttribute("data-asoch-fixed-value"))||[]);else if(e.tagName=="A"||g)if(g=g&&e.getAttribute("data-asoch-is-dynamic")==="true"?sd(a.h,[e]):a.g,g=Wd(g,e))c=g.data;e=e.parentElement}e=c&&!P(c,1);if(a.G&&a.l&&b.defaultPrevented)$d(a,b,d,e?c:a.l);else{if(e){if(!a.G&&b.defaultPrevented){$d(a, 
b,d,c);return}g=c;for(var f of Mb(g,6,B,void 0===ib?2:4))Fd(f)}a.J&&e&&c.h()&&(oc(b),(f=c)&&Q(f,2)&&(g=ud(Q(f,2),"ae","1"),S(f,2,g)));if(d&&e){c=e?c:null;(f=Wd(a.g,d))?f=f.data:(f=new Tb,S(f,2,d.href),S(f,11,d.target||"_top"),a.g.push({element:d,data:f}));vd(d,c||f,Q(f,2),557);ae(a,b,d,c);for(var k of Mb(a.h,17,B,void 0===ib?2:4))f=W.body,e={},typeof window.CustomEvent==="function"?g=new CustomEvent(k,e):(g=document.createEvent("CustomEvent"),g.initCustomEvent(k,!!e.bubbles,!!e.cancelable,e.detail)), 
f.dispatchEvent(g);if(c?.g()?.g()){k=new Yb;ob(H(c,5))!=null?(f=k,e=R(c,5),Wb(f,1,e)):(f=Z(d.href,"nb"),f!=""&&Wb(k,1,+f));f=Z(d.href,"nx");f!=""&&Vb(k,2,+f);f=Z(d.href,"ny");f!=""&&Vb(k,3,+f);f=Z(d.href,"bg");f!=""&&S(k,4,f);f=Z(d.href,"nm");f!=""&&Vb(k,5,+f);f=Z(d.href,"mb");f!=""&&Vb(k,6,+f);f=d.href;e=f.search(xa);g=0;for(var l=[];(h=wa(f,g,"bg",e))>=0;)l.push(f.substring(g,h)),g=Math.min(f.indexOf("&",h)+1||e,e);l.push(f.slice(g));f=l.join("").replace(za,"$1");w(d,jd(f,kd(1211)));h=Q(c.g(),1); 
f=Q(c.g(),2);e=pb(H(c,20))!=null?pb(H(c,20))??0:null;l=JSON.stringify(E(k));k=a.s;g=od(m);var n=new gc;h=S(n,1,h);h=S(h,4,l);h=S(h,10,Date.now().toString());e!==null&&Vb(h,2,e);k!==null&&S(h,3,k);Wb(h,9,7);g?.fence?.setReportEventDataForAutomaticBeacons({eventType:"reserved.top_navigation_start",eventData:JSON.stringify(h),destination:["buyer"],once:!0});g?.fence?.reportEvent({eventType:"click",eventData:f,destination:["component-seller"]})}P(a.h,16)||a.M?be(a,b,d,c):(k="",(f=m.oneAfmaInstance)&& 
(k=f.appendClickSignals(d.href)),ce(a,b,d,c,k))}}}} 
function $d(a,b,c,d){if(a.H===b&&a.I){const g=new $b(a.I),h=Q(d,9);var e="";switch(R(g,4,1)){case 2:if(pb(H(g,2))??0)e="blocked_fast_click";else if(Q(g,1)||Q(g,7))e="blocked_border_click";break;case 3:e=W;e=e.getElementById?e.getElementById("common_15click_anchor"):null;const f=window;if(typeof f.copfcChm==="function"&&e){d=Ib(d);Wb(d,5,12);Rb(d,4).set("nb",(12).toString());const k=Wd(a.g,e);k?k.data=d:a.g.push({element:e,data:d});!a.R&&c&&(ae(a,b,c,d),S(d,2,c.href));f.copfcChm(b,rd(d,e.href),null, 
L(g,Zb,10)?JSON.stringify(E(O(g,Zb,10))):null);a.R&&ae(a,b,e,d)}e="onepointfiveclick_first_click"}h&&e&&(c=h+"&label="+e,e==="onepointfiveclick_first_click"&&(c+="&ccx="+b.clientX+"&ccy="+b.clientY),Fd(c,!1));Fc(a.N)}} 
function ae(a,b,c,d){if(!P(d,13)){var e=c.href;var g=/[?&]adurl=([^&]+)/.exec(e);e=g?[e.slice(0,g.index),e.slice(g.index)]:[e,""];for(w(c,jd(e[0],kd(557)));!c.id;)if(g="asoch-id-"+(Math.floor(Math.random()*2147483648).toString(36)+Math.abs(Math.floor(Math.random()*2147483648)^Date.now()).toString(36)),!W.getElementById(g)){c.id=g;break}g=c.id;typeof window.xy==="function"&&window.xy(b,c,W.body);typeof window.mb==="function"&&window.mb(c);typeof window.bgz==="function"&&window.bgz(g);typeof window.ja=== 
"function"&&window.ja(g,d?R(d,5):0);typeof window.vti==="function"&&window.vti(c);a.i&&typeof window.ss==="function"&&(a.P?window.ss(g,1,a.i):window.ss(a.i,1));e.length>0&&(a=a.s.length>0&&!d?.g()?.g()?c.href+"&uach="+encodeURIComponent(a.s)+e[1]:c.href+e[1],w(c,jd(a,kd(557))))}} 
async function be(a,b,c,d){let e="";var g=m.oneAfmaInstance;if(g&&(b.preventDefault(),e=await g.appendClickSignalsAsync(c.href)||"",a.M)){if(a.Y)return;if(g=await g.getNativeClickMeta()){if(g.customClickGestureEligible)return;e=td(e,"nas",g.encodedNas)}}ce(a,b,c,d,e)} 
function ce(a,b,c,d,e){a.L++;a.B<0&&(a.B=Date.now());const g=P(a.h,2),h=g&&Date.now()-a.O>300,f=m.oneAfmaInstance;f?(oc(b),(()=>{var k=P(d,13)?e:f.logScionEventAndAddParam(e);if(!a.C&&d&&L(d,U,12)){var l=O(d,U,12).A();var n="";if(ec(d).length>0)for(const p of ec(d))n+=Q(p,2)+" "+p.A()+" ";P(O(d,U,12),2)?(f.click(k),f.openAndroidApp(l),l=!0):l=!1}else l=!1;l||Ad(a.m,d,k,f)||Ed(g,h,a.aa,a.C,d,k,f,a.J,a.Z)})()):(b=window,a.X&&b.pawsig&&typeof b.pawsig.clk==="function"?(nd("paw_sigs",{msg:"click",count:a.L.toString(), 
elapsed:(Date.now()-a.B).toString()}),b.pawsig.clk(c)):h&&(b=c.getAttribute("attributionsrc")!=null&&ya(c.getAttribute("attributionsrc"),"nis")==="6"?Dd(c.href,()=>{}):Dd(c.href),b!==c.href&&w(c,jd(b,kd(599)))));h&&(a.O=Date.now());Fc(a.N)} 
var Yd=class{constructor(a){this.C=La||Ja||Ma||Ka;var b=Hc("data-asoch-meta");if(b.length!==1)nd("gdn-asoch",{type:2,data:b.length});else{this.N=70;this.h=new fc(JSON.parse(b[0].getAttribute("data-asoch-meta"))||[]);this.K=a["extra-meta"]?new fc(JSON.parse(a["extra-meta"])):null;this.M=a["is-fsn"]==="true";this.Y=a["is-tap-disabled-for-fsn"]==="true";this.m=a["ios-store-overlay-config"]?new ac(JSON.parse(a["ios-store-overlay-config"])):null;this.aa=a["use-cct-over-browser"]==="true";this.R=a["correct-redirect-url-for-och-15-click"]=== 
"true";this.Z=a["spitzer-use-click-url-for-fallback"]==="true";this.G=a["default-msg-in-och"]==="true";this.X=a["enable-paw"]==="true";this.J=a["allow-redirection-muted-in-och"]==="true";this.s="";b=Ud();b!=null&&b.then(c=>{var d=JSON.stringify(E(c));c=[];var e=0;for(var g=0;g<d.length;g++){var h=d.charCodeAt(g);h>255&&(c[e++]=h&255,h>>=8);c[e++]=h}d=3;d===void 0&&(d=0);if(!Oa)for(Oa={},e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),g=["+/=","+/","-_=","-_.","-_"],h= 
0;h<5;h++){var f=e.concat(g[h].split(""));Na[h]=f;for(var k=0;k<f.length;k++){var l=f[k];Oa[l]===void 0&&(Oa[l]=k)}}d=Na[d];e=Array(Math.floor(c.length/3));g=d[64]||"";for(h=f=0;f<c.length-2;f+=3){var n=c[f],p=c[f+1];l=c[f+2];k=d[n>>2];n=d[(n&3)<<4|p>>4];p=d[(p&15)<<2|l>>6];l=d[l&63];e[h++]=k+n+p+l}k=0;l=g;switch(c.length-f){case 2:k=c[f+1],l=d[(k&15)<<2]||g;case 1:c=c[f],e[h]=d[c>>2]+d[(c&3)<<4|k>>4]+l+g}this.s=e.join("")});this.g=sd(this.h);this.G&&(this.l=null,Sb(this.h).forEach(c=>{this.l!=null|| 
B(H(c,2))==null||B(H(c,9))==null||P(c,13)||(this.l=c)}));this.ba=Number(a["deeplink-and-android-app-validation-timeout"])||500;this.O=-Infinity;this.B=this.L=0;this.i=Q(this.h,5)||"";this.P=P(this.h,11);this.K&&(this.P=P(this.K,11));this.I=this.H=null;P(this.h,3)||(wd(this.g),Ub(this.h,3,!0));Vd(this.g);a=m.oneAfmaInstance;!this.C&&a&&yd(this.g,this.ba);if(a&&this.m?.h())switch(b=()=>{const c=pb(H(this.m,4))??0;c>0?m.setTimeout(()=>{xd(this.g)},c):xd(this.g)},R(this.m,3)){case 1:a.runOnOnShowEvent(b); 
break;case 2:xc(b);break;default:xd(this.g)}V(W,"click",md(557,c=>{Zd(this,c)}),ic);V(W,"auxclick",md(557,c=>{c.button===1&&Zd(this,c)}),ic);this.i&&typeof window.ss==="function"&&V(W.body,"mouseover",md(626,()=>{window.ss(this.i,0)}),jc);typeof window.ivti==="function"&&window.ivti(W.body);a=window;a.googqscp&&typeof a.googqscp.registerCallback==="function"&&a.googqscp.registerCallback((c,d)=>{this.H=c;this.I=d});Cc()}}};var de=md(555,a=>Xd(a));Bc=70;const ee=Dc(70,document.currentScript);if(ee==null)throw Error("JSC not found 70");const fe={},ge=ee.attributes;for(let a=ge.length-1;a>=0;a--){const b=ge[a].name;b.indexOf("data-jcp-")===0&&(fe[b.substring(9)]=ge[a].value)}de(fe);}).call(this);
