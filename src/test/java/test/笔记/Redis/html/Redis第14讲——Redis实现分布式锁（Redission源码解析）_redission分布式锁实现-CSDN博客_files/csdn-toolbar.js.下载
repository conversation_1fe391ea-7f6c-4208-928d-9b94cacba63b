"use strict";function _toConsumableArray(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var _createClass=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}(),_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_slicedToArray=function(){function e(e,t){var n=[],o=!0,r=!1,a=void 0;try{for(var i,s=e[Symbol.iterator]();!(o=(i=s.next()).done)&&(n.push(i.value),!t||n.length!==t);o=!0);}catch(e){r=!0,a=e}finally{try{!o&&s.return&&s.return()}finally{if(r)throw a}}return n}return function(t,n){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,n);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}();"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(e,t){if(null===e||void 0===e)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(e),o=1;o<arguments.length;o++){var r=arguments[o];if(null!==r&&void 0!==r)for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(n[a]=r[a])}return n},writable:!0,configurable:!0,enumerable:!1}),window._hmt=window._hmt||[],function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e&&"UD"==e)return decodeURIComponent(o[1]);if(o[0]==e)return decodeURI(o[1])}}function t(){var e=window.location.host;return 0===e.indexOf("blog")?"UA-*********-2":0===e.indexOf("download")?"UA-*********-8":0===e.indexOf("edu")?"UA-*********-9":0===e.indexOf("bbs")?"UA-*********-4":0===e.indexOf("ask")?"UA-*********-5":0===e.indexOf("gitbook")?"UA-*********-10":0===e.indexOf("iteye")?"UA-*********-6":0===e.indexOf("passport")?"UA-*********-7":0===e.indexOf("so")?"UA-*********-3":0===e.indexOf("www")?e.indexOf("iteye")>0?"UA-*********-6":"UA-*********-1":""}var n="6bcd52f51e9b3dce32bec4a3997715ac",o=function(){var e="6bcd52f51e9b3dce32bec4a3997715ac",t=$('meta[name="toolbar"]');if(t.length){var n=t.attr("content")||{};n=JSON.parse(n),e=n.hmId||e}return e}();if(function(e){for(var t=document.cookie.split("; "),o=0;o<t.length;o++){var r=t[o].split("=");/^Hm\_.+\_.+$/.test(r[0])&&-1===r[0].indexOf(e)&&-1===r[0].indexOf(n)&&(document.cookie=r[0]+"="+escape("")+";max-age=0;domain=.csdn.net;path=/")}}(o),function(e){_hmt.push(["_setAccount",e])}(o),function(){try{var t=!!e("UN"),n=!!e("UserName"),o=e("p_uid");o=o?o.substr(1,1):0,_hmt.push(["_setUserProperty",{islogin:+t,isonline:+n,isvip:+(1==o)}]),n&&_hmt.push(["_setUserId",e("UserName")]),_hmt.push(["_setUserTag","6525",e("uuid_tt_dd")])}catch(e){void 0}}(),window.self==window.top||function(){var e=null;if(parent!==window)try{e=parent.location.href}catch(t){e=document.referrer}return void 0,e}().indexOf("csdn.net")<0){void 0;var r=document.createElement("script");r.src="https://hm.baidu.com/hm.js?"+o;var a=document.getElementsByTagName("script")[0];a.parentNode.insertBefore(r,a)}else void 0;if(document.referrer.indexOf("google.com")>-1){var i=function(){dataLayer.push(arguments)},s=document.createElement("script");s.src="https://www.googletagmanager.com/gtag/js?id="+t(),a.parentNode.insertBefore(s,a),window.dataLayer=window.dataLayer||[];var c=e("UserName")||"";i("js",new Date),i("config",t()),c&&i("set",{user_id:c})}}(),function(){var e=document.createElement("script");e.type="text/javascript",e.async=!0,e.src="https://g.csdnimg.cn/??asdf/1.1.3/trackad.js,iconfont/nav/iconfont-1.0.1.js";var t=document.getElementsByTagName("script")[0];t.parentNode.insertBefore(e,t)}(),function(){function e(e){if(!e.includes("?"))return e;var n=e.split("?"),o=_slicedToArray(n,2),r=o[0],a=o[1],i=a.split("&").filter(function(e){return-1===e.indexOf(t)});return i.length?r+"?"+i.join("&"):r}var t="c_ab_test=1";if(("www.csdn.net"===location.hostname||"blog.csdn.net"===location.hostname)&&"/"===location.pathname){var n=location.href.indexOf(t)>-1;$.ajax({url:"https://blog.csdn.net/phoenix/web/get-www-ab-test"+(n?"?cAbTest=1":""),type:"get",xhrFields:{withCredentials:!0},success:function(){if(n){var o=e(location.href);o.indexOf(t)>-1&&(o=o.replace(t,"")),location.href=o}},complete:function(){}})}}(),window._hmt=window._hmt||[],function(e,t){function n(e){var t=document.createElement("style");t.innerText="#csdn-toolbar .toolbar-inside{display: none;}",document.getElementsByTagName("head")[0].appendChild(t);var n=document.createElement("link");n.rel="stylesheet",n.type="text/css",n.href=e,n.addEventListener("load",function(){t.remove()}),document.getElementsByTagName("head")[0].appendChild(n)}function o(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e)return decodeURIComponent(o[1])}}function r(e,t,n){var o=new Date;if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=e+"="+encodeURIComponent(t)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function a(e){var t=new Date;document.cookie=e+"="+escape("1")+";max-age=0;expires="+t.toGMTString()+";domain=.csdn.net;path=/"}function i(e,t){var n,o=null;return function(){var r=this,a=new Date;a-o-t>0?(n&&(clearTimeout(n),n=null),e.apply(r,arguments),o=a):n||(n=setTimeout(function(){e.apply(r,arguments)},t))}}function s(e,t){var n;return function(){var o=this,r=arguments;n&&clearTimeout(n),n=setTimeout(function(){e.apply(o,r)},t)}}function c(t){e.csdn&&e.csdn.loginBox&&e.csdn.loginBox.show?e.csdn.loginBox.show(t):e.location.href="https://passport.csdn.net/account/login"+(t?"?spm="+t.spm:"")}function l(e){e=e.replace("https://","");var t=e.split("/")[0];return~location.host.indexOf(t)}function d(){return location.origin+location.pathname==="https://www.csdn.net/c/"}function p(e){return"string"==typeof e||e instanceof String}function u(){var e=navigator.userAgent;return e.indexOf("Opera")>-1||e.indexOf("OPR/")>-1?"Opera":e.indexOf("Edg")>-1?"Edge":e.indexOf("Chrome")>-1?"Chrome":e.indexOf("Safari")>-1?"Safari":e.indexOf("Firefox")>-1?"Firefox":e.indexOf("Trident")>-1?"IE":"Unknown"}function h(t){var n="/"===e.location.pathname?"":e.location.pathname,o=e.location.origin+n;return t.indexOf(o)>-1}function g(e){return 0==e.length?"":e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\'/g,"&#39;").replace(/\"/g,"&quot;")}function m(n){if("[object Object]"==Object.prototype.toString.call(n)){var o=t("#csdn-toolbar"),r=n.need_first_suspend||!1,a=n.need_little_suspend||!1,s=n.little_tool_id||"",c=n.little_need_insert_type||"",l=n.need_change_function||"",d="",p="";if(1!=r||1!=a){if(a&&""!=s&&(o=t(s)),a&&s&&""!=c&&(d="."+c,p=t(d)),0===o.length)return void T.push(m.bind(this,n));var u=i(function(){if((document.documentElement.scrollTop||document.body.scrollTop)>=50)o.css({position:"fixed",top:"0",left:"0","z-index":"1993","min-width":"100%",width:"max-content"}),a&&t(".secodn_level_csdn_logo").length&&t(".secodn_level_csdn_logo").css({display:"block"}),a&&""!=c&&(t("body").addClass("toolbar-second-drop"),"onlySearch"==c?t("#csdn_tool_otherPlace").append(p):"onlyUser"==c?t("#csdn_tool_otherPlace").append(p):"searchUser"==c&&(t("#csdn_tool_otherPlace").append(t(".onlySearch")),t("#csdn_tool_otherPlace").append(t(".onlyUser")))),"function"==typeof l&&l("fixed");else{if(o.css({position:"relative","z-index":"","min-width":"100%",width:"max-content"}),a&&t(".secodn_level_csdn_logo").length&&t(".secodn_level_csdn_logo").css({display:"none"}),a&&""!=c){t("body").removeClass("toolbar-second-drop");var e=t("#csdn-toolbar .toolbar-container");"onlySearch"==c?e.find(".toolbar-menus").after(p):"onlyUser"==c?e.find(".toolbar-container-right").append(p):"searchUser"==c&&(e.find(".toolbar-container-middle").append(t(".onlySearch")),e.find(".toolbar-container-right").append(t(".onlyUser")))}"function"==typeof l&&l("noFixed")}},80);"so"!==C?((document.documentElement.scrollTop||document.body.scrollTop)>50&&u(),t(e).on("scroll",u)):void 0}}}function f(){var t=e.location.host,n=t.split(".")[0],o="";switch(n){case"www":case"blog":case"bbs":o="";break;case"download":o="doc";break;case"ask":o="ask";break;case"gitchat":case"geek":o="";break;case"edu":o="course";break;default:o=""}return t.indexOf(".blog.csdn.net")>-1&&(o="blog"),o}function b(e){var t={};return location.href.replace(/([^*#&=?]+)=([^*#&=?]+)/g,function(){for(var e=arguments.length,n=Array(e),o=0;o<e;o++)n[o]=arguments[o];var r=n[1],a=n[2];t[r]=a}),e?t[e]:t}function v(e){void 0;try{e&&t(document).trigger(e)}catch(e){void 0}}function w(){return!!+(o("p_uid")||"").substr(1,1)}function y(){try{var e=navigator.userAgent,t=/(?:Android)/.test(ua)||/Linux/i.test(e)||/(?:iPhone)/.test(ua);return/Tablet/i.test(e)||/iPad/i.test(e)||t}catch(e){return!0}}function x(e){try{e>1e4&&(e=parseFloat((e/1e4).toFixed(1))+"w")}catch(e){}return e}function k(t){try{return e.csdn.report.getFullSpm(t)}catch(e){return t}}function _(){this.currentUser={userName:"",userNick:'<a class="set-nick" href="https://passport.csdn.net/account/profile">设置昵称<span class="write-icon"></span></a>',desc:'<a class="fill-dec" href="//my.csdn.net" target="_blank">编辑自我介绍，让更多人了解你<span class="write-icon"></span></a>',avatar:""},this.hasLogin=!1,this.$container="",this.announcement=0,this.logoData={title:"CSDN首页",url:{default:"https://csdnimg.cn/cdn/content-toolbar/csdn-logo.png?v=********.1",dark:"https://g.csdnimg.cn/common/csdn-toolbar/images/csdn-logo.png",home:"https://g.csdnimg.cn/common/csdn-toolbar/images/csdn-logo.png"}},this.navData=[],this.mpAb=null,this.mpUrl="mp.csdn.net",this.advertData={date:{start:"2022/07/03 08:50:00",end:"2022/07/04 17:30:00"},background:{large:"https://csdnimg.cn/public/publick_img/ad_20200703_toolbar325.jpg",default:"https://csdnimg.cn/public/publick_img/ad_20200703_toolbar80_2.jpg"},color:"#027ef2",url:"https://aiprocon.csdn.net/p/live?utm_source=live_703"},this.mpMenuData={isShow:!1},this.searchLoginAb=null,this.searchLoginAbSpm=null,this.demoSpm="",this.absegment=-1,this.cknowData=null,this.searchToolbarData=null,this.init()}e.csdn=e.csdn||{},e.csdn.configuration_tool_parameterv=m;var S=["csdn-toolbar-default","csdn-toolbar-dark","csdn-toolbar-home"],T=[],D=0,C="normal",$=0,I="https://so.csdn.net/so/search";_.prototype={constructor:_,init:function(){var e=this;e.checkLogin(function(t){t.userName&&(e.hasLogin=!0,_hmt.push(["_setUserTag","5744",t.userName]))}),e.getToolbarData(e.render)},render:function(t){var n=this;if(n.isMiniRenderSearch=!1,"sidebar-www"!==C){this.abWithWhiteHostSearchToolbar()&&(C="so")}"mini"===C?(n.renderMiniToolbar(),n.renderLogo(),n.renderMiniMenu(),n.renderBtnsJudgement(),n.chain(),e.csdn&&e.csdn.toolbarFinishCallback&&e.csdn.toolbarFinishCallback()):"sidebar"===C||"sidebar-www"===C?(n.renderMiniToolbar(),n.renderLogo(),n.renderMiniMenuSidebar(),n.renderSearch("#csdn-toolbar .toolbar-container .toolbar-container-mini-middle"),n.renderBtnsJudgement(),n.getHotSearchWordData(),n.chain(),e.csdn&&e.csdn.toolbarFinishCallback&&e.csdn.toolbarFinishCallback()):"so"===C?(n.renderSoToolbar(),n.renderLogo(),n.abTestFn(),n.renderNav(),this.renderSearch("#csdn-toolbar .toolbar-so .toolbar-so-search-container"),n.soToolbarOnScroll(),n.renderBtnsJudgement(),n.getHotSearchWordData(),n.chain()):(n.renderToolbar(),n.renderLogo(),n.abTestFn(),n.renderNav(),n.renderSearch(),n.renderBtnsJudgement(),n.getHotSearchWordData(),n.chain()),e.csdn&&e.csdn.report&&e.csdn.report.viewCheck&&e.csdn.report.viewCheck()},getAvatarByAu:function(e){return"https://profile-avatar.csdnimg.cn/default.jpg!3"},checkLogin:function(e){var t=o("UserNick"),n=o("UserName");this.currentUser.userNick=t,this.currentUser.userName=n,this.currentUser.avatar=this.getAvatarByAu(),e&&e(this.currentUser)},renderToolbar:function(){var e=this,n=e.getToolbarStyle(),o=t('<div id="csdn-toolbar">\n                    <div class="toolbar-inside exp3" '+(n?'style="'+n+'"':"")+'>\n                      <div class="toolbar-container">\n                        <div class="toolbar-container-left">\n                          <div class="toolbar-logo toolbar-subMenu-box csdn-toolbar-fl"></div>\n                          <ul class="toolbar-menus csdn-toolbar-fl"></ul>\n                        </div>\n                        <div class="toolbar-container-middle">\n                        </div>\n                        <div class="toolbar-container-right">\n                          <div class="toolbar-btns onlyUser"></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>');return 1==$?t("#toolbarBox").prepend(o):t("body").prepend(o),e.$container=o.find(".toolbar-container"),e.$logoBox=o.find(".toolbar-logo"),e.$NavBox=o.find(".toolbar-menus"),e.$btnsBox=o.find(".toolbar-btns"),t(document).on("click",function(n){0!==t(n.target).closest(".toolbar-search").length||e.searchInputFocus||(e.clearSeachDropMenu(),e.toggleSearchBarInput())}),this},renderSoToolbar:function(){var e=["default","dark","home"],n=e[D],o=this.searchToolbarData||{},r=o.logo&&o.logo.url?o.logo.url[n]:"",a=o.sideLogo&&o.sideLogo.url?o.sideLogo.url[n]:"",i="dark"===n?"dark":"light",s=o.background&&o.background[i]?o.background[i]:{},c=s.fold&&s.normal,l=this,d=l.getToolbarStyle(),p=t('<div id="csdn-toolbar" class="search-model">\n                    <div class="toolbar-so-height"></div>\n                    <div class="toolbar-inside exp3" '+(d?'style="'+d+'"':"")+'>\n                      <div class="toolbar-container">\n                        <div class="toolbar-container-left">\n                          <div class="toolbar-logo toolbar-subMenu-box csdn-toolbar-fl"></div>\n                          <ul class="toolbar-menus csdn-toolbar-fl"></ul>\n                        </div>\n                        <div class="toolbar-container-middle">\n                        </div>\n                        <div class="toolbar-container-right">\n                          <div class="toolbar-btns onlyUser"></div>\n                        </div>\n                      </div>\n                    </div>\n                    <div class="toolbar-so '+("dark"===n?"dark":"")+" "+(c?"has-bg-image":"")+'" '+(c?'style="background: '+s.normal+'"':"")+'>\n                      <div class="toolbar-so-container">\n                        <div class="toolbar-so-gradient"></div>\n                        <div class="toolbar-so-linear_gradient"></div>\n                        '+(r?'<img src="'+r+'" alt="" />':"")+'\n                        <div class="toolbar-so-inner">\n                          <div class="toolbar-so-search">\n                            <div class="toolbar-so-search-container"></div>\n                            <div class="toolbar-so-logo">\n                               '+(a?'<img src="'+a+'" alt="" />':"")+"\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>");return 1==$?t("#toolbarBox").prepend(p):t("body").prepend(p),l.$container=p.find(".toolbar-container"),l.$logoBox=p.find(".toolbar-logo"),l.$NavBox=p.find(".toolbar-menus"),l.$btnsBox=p.find(".toolbar-btns"),l.$toolbarSo=p.find(".toolbar-so"),l.$toolbarInside=p.find(".toolbar-inside"),l.$fakeHeight=p.find(".toolbar-so-height"),t(document).on("click",function(e){0!==t(e.target).closest(".toolbar-search").length||l.searchInputFocus||(l.clearSeachDropMenu(),l.toggleSearchBarInput())}),this},soToolbarOnScroll:function(){var n=this,r=n.$toolbarSo.find(".toolbar-so-container > img"),a=!!o("is_advert"),s=["default","dark","home"],c=s[D],l=this.searchToolbarData||{},d="dark"===c?"dark":"light",p=l.background&&l.background[d]?l.background[d]:{},u=p.fold&&p.normal,h=function(e){var o=document.documentElement.scrollTop||document.body.scrollTop,i=t("#csdn-toolbar .toolbar-advert"),s=i.length?i.height():0;if(!(o>t("#csdn-toolbar").height())||e){var c=n.$toolbarInside.height(),l=0;if(r.length){var d=getComputedStyle(r[0]);l=r.height()+Number(d.marginBottom.replace("px",""))+Number(d.marginTop.replace("px",""))+10}var h=s,g=0;if(o>0||a){n.$toolbarInside.css("top",h+"px"),i.addClass("fixed"),n.$toolbarInside.addClass("fixed"),g=c+h;var m=n.$toolbarSo.hasClass("fold");if(o>=0){var f=!!(o>=l+0);g=f?68+c+l:48,g+=h,n.$fakeHeight.css("height",g+"px");var b={top:f?h+c+"px":0};u&&(b.background=f?p.fold:p.normal),f?n.$toolbarSo.addClass("fold").css(b):m&&n.$toolbarSo.css(b).removeClass("fold")}else m&&n.$toolbarSo.css("top",0).removeClass("fold");if(r.length&&o<t("#csdn-toolbar").height()){var v=l+0;if(o>0&&o<v){var w=1-~~(100/l*(o-0)*1)/100*1.5;r.css("opacity",Math.max(w,0))}else r.css("opacity",1)}}else n.$toolbarInside.removeClass("fixed"),n.$toolbarSo.css("top",0).removeClass("fold"),i.removeClass("fixed"),r.css("opacity",1);n.$fakeHeight.css("height",g+"px")}};(document.documentElement.scrollTop||document.body.scrollTop)>50&&h(!0),t(document).on("toolbarHeightChange",function(){h(!0)});var g=i(h,20);t(e).on("scroll",g)},renderMiniToolbar:function(){var e=this,n=e.getToolbarStyle(),o=t('<div id="csdn-toolbar">\n                    <div class="toolbar-inside exp3" '+(n?'style="'+n+'"':"")+'>\n                      <div class="toolbar-container toolbar-container-'+C+'">\n                        <div class="toolbar-container-left">\n                          <div class="toolbar-logo toolbar-subMenu-box csdn-toolbar-fl"></div>\n                        </div>\n                        <div class="toolbar-container-mini-middle">\n                        </div>\n                        <div class="toolbar-container-right">\n                          <div class="toolbar-btns onlyUser"></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>');return t("body").prepend(o),e.$miniMenu=o.find(".toolbar-container-left"),e.$logoBox=o.find(".toolbar-logo"),e.$btnsBox=o.find(".toolbar-btns"),this},abWithWhiteHostSearchToolbar:function(){try{var e=this.searchToolbarData||{};if(!e.whiteList||!e.whiteList.length)return!1;if(e.whiteList.some(function(e){return"string"==typeof e?"*"===e||!!~location.href.indexOf(e):!("object"!==(void 0===e?"undefined":_typeof(e))||!e.host)&&(e.path=e.path||["*"],!!~location.href.indexOf(e.host)&&e.path.some(function(e){return"*"===e||location.pathname===e}))})){var t=o("c_segment")?parseInt(o("c_segment")):-1;return!(!e.abTest||!e.abTest.exp1)&&e.abTest.exp1.indexOf(t)>-1}return!1}catch(e){return!1}},abTestFn:function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e)return decodeURIComponent(o[1])}}var t=e("c_segment")?parseInt(e("c_segment")):-1;this.absegment=this.cknowData&&this.cknowData.exp1?this.cknowData.exp1.indexOf(t):-1,void 0},renderNav:function(){var e=this,t=""+this.navData.map(function(t){if(t.hasOwnProperty("whiteUrl")&&t.whiteUrl.length>0&&!h(t.whiteUrl))return"";if(t.hasOwnProperty("browser")){var n=t.browser,o=u();if(o=o.toLowerCase(),Array.isArray(n)?n=n.map(function(e){return e.toLowerCase()}):p(n)&&(n=n.toLowerCase()),-1===n.indexOf(o))return""}if(t.active=l(t.url),"index"===t.id&&d()?t.active=!1:"community"!==t.id||d()||(t.active=!1),e.absegment>-1&&"plugin"==t.id)return"";if(e.absegment<=-1&&"cknow"===t.id)return"";e.absegment>t.abtest&&(t.iconTitle=null);var r=((new Date).getTime(),e.isEffectiveTime(t.icon));return'<li class="'+(t.active?"active ":"")+(t.children.length?"toolbar-subMenu-box":"")+(t.slider&&t.slider.list&&t.slider.list.length?" toolbar-subSlider-box":"")+"\" title='"+(t.title?t.title:"")+"'>\n                                <a "+(t.report?"data-report-click='"+JSON.stringify(t.report)+"' data-report-view='"+JSON.stringify(t.report)+"'":"")+" "+(t.report&&t.report.spm?'data-report-query="spm='+t.report.spm+'"':"")+" href='"+t.url+"'>\n                                  "+(t.iconTitle?"<img style='"+t.iconTitle.style+'\'" src="'+t.iconTitle.url+'">':"")+t.name+"\n                                  "+(r?"<img style='"+t.icon.style+'\'" src="'+t.icon.url+'">':"")+"\n                                  "+(t.children.length?'<i class="toolbar-subMenu-arrowHead"></i>':"")+"\n                                </a>\n                                "+(t.children.length?'<div class="toolbar-subMenu">\n                                    '+t.children.map(function(e){return'<a rel="nofollow" data-report-click=\''+JSON.stringify(e.report)+"' data-report-query=\"spm="+e.report.spm+'" target="_blank" href=\''+e.url+"'>"+e.name+"</a>"}).join("")+"\n                                  </div>":"")+"\n                                "+(t.slider&&t.slider.list&&t.slider.list.length?'<div class="toolbar-subSlider">\n                                  <div class=toolbar-subSlider-'+t.id+">\n                                    "+t.slider.list.map(function(e){return(e.name&&e.name.length<=2||e.imgUrl)&&'<a rel="nofollow" '+(e.report?"data-report-click='"+JSON.stringify(e.report)+"'":"")+" "+(e.report&&e.report.spm?'data-report-query="spm='+e.report.spm+'"':"")+' target="_blank" href='+(e.url?e.url:"javascript:void(0);")+">"+(e.name?e.name:"")+(e.imgUrl?"<img src="+e.imgUrl+" style="+(e.imgStyle?e.imgStyle:"")+" />":"")+"</a>"}).join("")+"\n                                    </div>\n                                  </div>":"")+"\n                              </li>"}).join("");return this.$NavBox.append(t),this},renderLogo:function(){var e=["default","dark","home"],t=e[D],n='<a data-report-click=\'{"spm":"3001.4476"}\' data-report-query="spm=3001.4476" href="'+(this.logoData.link||"https://csdn.net")+'"><img title="'+(this.logoData.title||"CSDN首页")+'" src="'+this.logoData.url[t]+'"></a>\n                    '+(this.logoData.qrcode?'<div class="toolbar-subMenu">\n                    <img width="96" height="96" src="'+this.logoData.qrcode+'">\n                  </div>':"");return void 0,this.$logoBox.append(n),this},toggleClearIcon:function(n){var o=t("#csdn-toolbar .toolbar-search-container");if(o){var r=o.find(".clear-icon"),a=o.find("#toolbar-search-input"),i=o.find("#toolbar-search-button");n?r&&r.length||!a.val()||(e.csdn&&e.csdn.report&&e.csdn.report.reportView&&e.csdn.report.reportView({spm:"3001.10183"}),i.before('<span class="clear-icon"><i></i></span>')):r.remove()}},renderSearch:function(n){var r=this,a="",i="",l=0,d=!1,p=0;if(!this.hasLogin&&this.searchLoginAb){var u=Object.keys(this.searchLoginAb);l=parseInt(e.localStorage.getItem("searchShowLoginCount")),l=isNaN(l)?0:l;var h=l>0?parseInt(e.localStorage.getItem("searchShowLoginShowTime")):0;h=isNaN(h)?0:h;if((new Date).getTime()-h>=864e5&&(l=0,e.localStorage.setItem("searchShowLoginCount",0),e.localStorage.setItem("searchShowLoginShowTime",0)),u.length>0){var g=parseInt(o("c_segment"));a=u.find(function(e){return r.searchLoginAb[e].indexOf(g)>-1}),a=a||"",i=a?this.searchLoginAbSpm[a]:""}}var m=function(){var n=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!n||"exp2"===a){l=parseInt(e.localStorage.getItem("searchShowLoginCount")),l=isNaN(l)?0:l;var o=l>0?parseInt(e.localStorage.getItem("searchShowLoginShowTime")):0;o=isNaN(o)?0:o;var r=(new Date).getTime()-o;void 0,r>=864e5&&(l=0,o=0),l<2&&(0===o||r>6e4)?(d=!1,l+=1,e.localStorage.setItem("searchShowLoginCount",l),e.localStorage.setItem("searchShowLoginShowTime",(new Date).getTime()),c({spm:i,guideText:"请登录后，再进行搜索哦～",cb:function(){e.csdn.loginBox.close(),n?e.location.reload():(setTimeout(function(){e.location.reload()},500),d=!0,t("#toolbar-search-button").trigger("click")),e.csdn&&e.csdn.report&&e.csdn.report.reportClick&&e.csdn.report.reportClick({spm:i,extra:{searchShowLoginCount:l,hasLoggedIn:1,is_loginbox:0}})}})):(d=!0,n||t("#toolbar-search-button").trigger("click"))}},f=this,b=t('<div class="toolbar-search '+("so"===C?"toolbar-search-so":"")+" onlySearch "+a+'" '+(i?'data-report-view=\'{"spm":"'+i+'","extra":{"is_loginbox":0}}\'':"")+'><div class="toolbar-search-container">\n                    <span class="icon-fire"></span>\n                    <input id="toolbar-search-input" maxlength="2000" autocomplete="off" type="text" value="" placeholder="'+this.getSearchInputPlaceholder()+'"><div class="gradient"></div>\n                    <button id="toolbar-search-button"><i></i><span>搜索</span></button>\n                    <input type="password" autocomplete="new-password" readonly disabled="true" style="display: none; position:absolute;left:-9999999px;width:0;height:0;"/>\n                  </div></div>'),v=!1,w=s(this.getSearchAssociateWord,300).bind(this);b.on("focus","#toolbar-search-input",function(e){a&&"control"!==a&&m(!0);var o=t(this),r=o.val().trim();""===r?f.getSearchHistoryArray(f.renderSearchHistoryDropMenu):w(r,f.renderAssociateWordDropMenu.bind(f)),n||f.toggleSearchBarInput("focus"),f.searchInputFocus=!0,v=!0,f.refreshPlaceholder(),f.buriedPoint({spm:"3001.8516"}),b.find(".toolbar-search-container").addClass("is-focus"),p=document.documentElement.scrollTop||document.body.scrollTop}).on("blur","#toolbar-search-input",function(e){f.searchInputFocus=!1,setTimeout(function(){f.refreshPlaceholder()},500),b.find(".toolbar-search-container").removeClass("is-focus")}).on("input","#toolbar-search-input",function(e){var n=t(this),o=n.val().trim();f.searchInputValue=o,void 0,""===o&&v?f.getSearchHistoryArray(f.renderSearchHistoryDropMenu):w(o,f.renderAssociateWordDropMenu.bind(f))}).on("click","button",function(e){if(a&&"control"!==a&&!d)return void m();d=!1;var o=t("#toolbar-search-input").val(),r=f.placeholder&&f.placeholder.productId||f.placeholderRight&&f.placeholderRight.productId,i=o?"3001.4498":"3001.7499",s=o||r,c=f.clickSearchBtnHandler.call(f,o,i),l={utm_medium:f.utm_medium,searchword:s},p=f.placeholder.reportData&&f.placeholder.reportData.data&&f.placeholder.reportData.data.index;return o||(void 0,l.recommendType=f.placeholder.recommendType,location.href.includes("/article/details")&&(l.pageType="blogDetail")),f.buriedPoint({spm:i,dest:c,index:p,extend1:o?"pc_search_uc_word":"pc_search_default_word",extra:JSON.stringify(l)}),f.clearSeachDropMenu(),b.find("#toolbar-search-input").blur(),n||f.toggleSearchBarInput(),!1}).on("keydown","#toolbar-search-input",function(e){229!==e.keyCode&&13===e.keyCode&&t("#toolbar-search-button").trigger("click")}),b.on("input","#toolbar-search-input",function(e){f.toggleClearIcon(!!t(this).val())}).on("click",".clear-icon",function(e){f.toggleClearIcon(!1),b.find("#toolbar-search-input").val("").focus(),f.getSearchHistoryArray(f.renderSearchHistoryDropMenu),f.buriedPoint({spm:"3001.10183"})}).on("mouseenter",function(){b.find("#toolbar-search-input").val()&&e.csdn&&e.csdn.report&&e.csdn.report.reportView&&e.csdn.report.reportView({spm:"3001.10183"})});var x=!1;return b.on("compositionstart","#toolbar-search-input",function(e){x=!0}).on("compositionupdate","#toolbar-search-input",function(e){x=!0}).on("compositionend","#toolbar-search-input",function(e){x=!1}),n?(t(n).append(b),f.isMiniRenderSearch=!0,t(document).on("click",function(e){0!==t(e.target).closest(".toolbar-search").length||f.searchInputFocus||f.clearSeachDropMenu()})):t(".toolbar-container-middle").append(b),setTimeout(function(){f.reportViewCheck()}),f.$searchBox=t(".toolbar-search"),t(e).on("keydown",function(e){if(!x){var n=t(".associate-word-drop-menu");if(n.length&&v){var o=n.find(".toolbar-search-item"),r=n.find(".toolbar-search-item.active"),a=r.length?r.index():-1;"ArrowUp"===e.key?(a--,a<0&&(a=o.length-1),o.eq(a).addClass("active").siblings().removeClass("active"),b.find("#toolbar-search-input").val(o.eq(a).find(".search-key-data").text()),e.preventDefault()):"ArrowDown"===e.key&&(a++,a>o.length-1&&(a=0),o.eq(a).addClass("active").siblings().removeClass("active"),void 0,b.find("#toolbar-search-input").val(o.eq(a).find(".search-key-data").text()))}}}),t(e).on("resize",function(){y()||v&&(f.clearSeachDropMenu(),b.find("#toolbar-search-input").blur(),n||f.toggleSearchBarInput())}),t(e).on("scroll",function(){if(f.searchInputFocus){var e=document.documentElement.scrollTop||document.body.scrollTop;if(!(Math.abs(e-p)>30))return;f.clearSeachDropMenu(),b.find("#toolbar-search-input").blur(),n||f.toggleSearchBarInput()}}),this},outPutSearchMethod:function(e){this.renderSearch(e),this.getHotSearchWordData()},renderBtnsJudgement:function(){this.renderBtnsExp3()},renderBtnsExp3:function(){var e=this,n=w(),a=n&&this.hasLogin?this.vipData.iconVip:this.vipData.iconNormal,i=a.showIcon?"inline-block":"none",s="sidebar-www"===C,l=t('<div class="toolbar-btn toolbar-btn-login toolbar-btn-login-new csdn-toolbar-fl '+(this.hasLogin?"toolbar-subMenu-box":"")+'">\n          '+(this.hasLogin?'<a class="hasAvatar" data-report-click=\'{"spm": "3001.5343"}\' data-report-query="spm=3001.5343" href="https://blog.csdn.net/'+e.currentUser.userName+'"><img src="'+this.currentUser.avatar+'"></a>':' <a class="toolbar-btn-loginfun" data-report-click=\'{"spm":"3001.5105"}\'>登录</a>')+'\n          </div>\n          <div class="toolbar-btn toolbar-btn-vip csdn-toolbar-fl">\n            <a rel="nofollow" style="'+(e.vipData.textColor?"color:"+e.vipData.textColor+";":"")+"\" data-report-click='"+JSON.stringify(e.vipData.report)+"' "+(e.vipData.report.spm?'data-report-query="spm='+e.vipData.report.spm+'"':"")+" href='"+e.vipData.url+"'>\n              "+e.vipData.name+" "+(e.isEffectiveTime(e.vipData.icon)?"<img style='"+e.vipData.icon.style+'\'" src="'+e.vipData.icon.url+'">':"<img style='"+a.style+";display:"+i+'\'" src="'+a.url+'">')+"\n            </a>\n          </div>\n          "+(this.hasLogin?'<div class="toolbar-btn toolbar-btn-msg csdn-toolbar-fl">\n              <div class="toolbar-subMenu-box">\n                <a rel="nofollow" data-report-click=\'{"spm":"3001.4508"}\' data-report-query="spm=3001.4508" id="toolbar-remind" href="https://i.csdn.net/#/msg/index">消息</a>\n              </div>\n              <div class="toolbar-msg-box"></div>\n            </div>':'<div class="toolbar-btn toolbar-btn-msg csdn-toolbar-fl">\n            <div class="toolbar-subMenu-box">\n              <a rel="nofollow" data-report-click=\'{"spm":"3001.9699"}\' data-report-view=\'{"spm":"3001.9699"}\' data-report-query="spm=3001.9699" id="toolbar-remind" href="https://i.csdn.net/#/msg/index">消息</a>\n            </div>\n          </div>')+"\n          "+(s?"":'<div class="toolbar-btn toolbar-btn-collect csdn-toolbar-fl">\n            <a rel="nofollow" data-report-click=\'{"spm":"3001.7480"}\' data-report-query="spm=3001.7480" href="https://i.csdn.net/#/user-center/history">历史</a>\n          </div>')+"\n          "+(s?"":'<div class="toolbar-btn toolbar-btn-mp csdn-toolbar-fl">\n            <a rel="nofollow" title="创作中心" data-report-click=\'{"dest":"https://'+e.mpUrl+'","spm":"3001.8539"}\' data-report-view=\'{"dest":"https://'+e.mpUrl+'","spm":"3001.8539"}\' data-report-query="spm=3001.8539" href="https://'+e.mpUrl+'">\n              创作中心'+this.getMpMenuIcon()+"\n            </a>\n          </div>")+'\n          <div class="toolbar-btn toolbar-btn-write toolbar-btn-write-new csdn-toolbar-fl '+(this.hasLogin?"toolbar-subMenu-box":"")+'"></div>\n        </div>');this.$btnsBox.append(l),this.renderNewBtnWrite(),t("#toolbar-remind").on("click",function(){
var e=o("toolbar_remind_num");e?e<3&&(e=parseInt(e)+1,r("toolbar_remind_num",e,864e5)):(e=1,r("toolbar_remind_num",e,864e5))}),l.on("click",".toolbar-btn-loginfun",function(){var e=t(this).data("report-click");e?c(e):c()}),l.on("click",".toolbar-btn-logout",function(){e.clickLogoutBtnHandler()})},renderNewBtnWrite:function(){var n="https://"+this.mpUrl+"/edit",o='<a rel="nofollow" data-report-click=\'{"spm":"3001.4503","extra":{"dataType":"'+this.demoSpm+'"}}\' data-report-query="spm=3001.4503" href="'+n+'">创作</a>';if(e.csdn.toolbarData&&e.csdn.toolbarData.writeBtnData&&e.csdn.toolbarData.writeBtnData.btnShow)if(!this.hasLogin&&e.csdn.toolbarData.writeBtnData&&e.csdn.toolbarData.writeBtnData.btnBgUnLogin)o='<a class="has-img" data-report-click=\'{"spm":"3001.7765"}\' data-report-query="spm=3001.7765" href="'+n+'">\n            <img src="'+e.csdn.toolbarData.writeBtnData.btnBgUnLogin+'">\n          </a>',t(".toolbar-btn-write").append(o),e.csdn&&e.csdn.report&&e.csdn.report.reportView&&e.csdn.report.reportView({spm:"3001.7765"});else{t.ajax({url:"https://blog.csdn.net/phoenix/web/v1/is-zero-article-user",type:"get",xhrFields:{withCredentials:!0},success:function(t){200===t.code&&(t.data?e.csdn.toolbarData.writeBtnData.btnBgNewWriter&&(o='<a class="has-img" data-report-click=\'{"spm":"3001.7765"}\' data-report-query="spm=3001.7765" href="'+n+'">\n                      <img src="'+e.csdn.toolbarData.writeBtnData.btnBgNewWriter+'">\n                    </a>'):e.csdn.toolbarData.writeBtnData.btnBgWriter&&(o='<a class="has-img" data-report-click=\'{"spm":"3001.7765"}\' data-report-query="spm=3001.7765" href="'+n+'">\n                      <img src="'+e.csdn.toolbarData.writeBtnData.btnBgWriter+'">\n                    </a>'))},complete:function(){t(".toolbar-btn-write").append(o),e.csdn&&e.csdn.report&&e.csdn.report.reportView&&e.csdn.report.reportView({spm:"3001.7765"})}})}else t(".toolbar-btn-write").append(o),e.csdn&&e.csdn.report&&e.csdn.report.reportView&&e.csdn.report.reportView({spm:"3001.7765"})},renderMiniMenu:function(){var e=t('<div class="csdn-toolbar-mini csdn-toolbar-fl">\n        <div class="toolbar-mini-meun-logo">\n          <div class="toolbar-mini-icon"></div>\n          <span>导航</span>\n          <div class="toolbar-mini-partition"></div>\n        </div>\n      </div>');return this.$miniMenu.prepend(e),this},renderMiniMenuSidebar:function(){var e=t('<div class="csdn-toolbar-mini csdn-toolbar-fl">\n        <div class="toolbar-mini-meuns">\n          <div class="toolbar-mini-meuns-icon"></div> \n        </div>\n      </div>');return this.$miniMenu.prepend(e),this},renderMsgMenu:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.hasLogin){var t='<div class="toolbar-subMenu">\n                          <a rel="nofollow" data-type="chat" href="https://im.csdn.net/im/main.html"><span class="pos-rel">我的消息'+(e.im?"<i></i>":"")+'</span></a>\n                          <a rel="nofollow" data-type="comment" href="https://i.csdn.net/#/msg/index"><span \n                          class="pos-rel">评论和@'+(e.comment?"<i></i>":"")+'</span></a>\n                          <a rel="nofollow" data-type="purchased" href="https://i.csdn.net/#/msg/new-content">\n                            <span class="pos-rel">已购上新</span>\n                          </a>\n                          <a rel="nofollow" data-type="attention" href="https://i.csdn.net/#/msg/attention"><span class="pos-rel">新增粉丝'+(e.follow?"<i></i>":"")+'</span></a>         \n                          <a rel="nofollow" data-type="like" href="https://i.csdn.net/#/msg/like"><span class="pos-rel">赞和收藏'+(e.thumb_up?"<i></i>":"")+'</span></a>\n                          <a rel="nofollow" data-type="notice" href="https://task.csdn.net/">有奖任务</a>\n                          <a rel="nofollow" href="https://i.csdn.net/#/msg/setting">消息设置</a>\n                     </div>';this.$btnsBox.find(".toolbar-btn-msg .toolbar-subMenu-box").append(t)}else this.setRemind("https://passport.csdn.net/account/login",0);T.length&&T.forEach(function(e){return e()})},renderMsgMenuNewVersion:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t="",n=!0,o=!1,r=void 0;try{for(var a,i=e[Symbol.iterator]();!(n=(a=i.next()).done);n=!0)!function(){var e=a.value,n={0:"评论通知",1:"用户关注",2:"互动通知",5:g(e.targetNickname||"")},o=n[e.previewType]||g(e.targetNickname||""),r="";try{if(["0","1","2"].includes(e.previewType))r=e.content.tt.replace(/{%\w+%}/g,function(t){return"{%url%}"===t?" ":"{%nickname%}"!==t||e.content.nickname?e.content[t.slice(2,-2)]:e.content.username||"csdn用户"});else{r=e.content.messageBody||"";var i=/\[face\]([^\]]+):([^\]]+)\[\/face\]/gi;i.test(r)&&(r=r.replace(i,'<img src="//g.csdnimg.cn/static/face/$1/$2" style="width:20px;height:20px;display:inline-block;float:none;" alt="表情包" />'))}}catch(e){r="新消息"}var s="https://i.csdn.net/#/msg/chat/"+(5==e.previewType?e.targetUsername:"notice/"+e.previewType),c='\n          <a href="'+s+'" data-report-view=\'{"spm":"3001.9945"}\' data-report-click=\'{"spm": "3001.9945"}\' target="_blank" class="msg-item msg-item_inner">\n            <div class="headimg">\n              <img src="'+e.targetAvatarUrl+'">\n            </div>\n            <div class="right">\n              <div class="r-left">\n                <div class="who-name">\n                  '+o+'\n                </div>\n              </div>\n              <div class="r-right">\n                <div class="last-msg">'+r+"</div>\n              </div>\n            </div>\n          </a>\n        ";t+=c}()}catch(e){o=!0,r=e}finally{try{!n&&i.return&&i.return()}finally{if(o)throw r}}if(this.hasLogin){var s='<div class="toolbar-subMenu toolbar-subMenu-msg-detail">\n                      <div class="toolbar-subMenu-title">\n                        <div class="the-title">我的私信</div>\n                        <a href="https://i.csdn.net/#/msg/index" data-report-click=\'{"spm": "3001.9946"}\' class="view-all">查看全部</a>\n                      </div>\n                      <div class="toolbar-subMenu-contentList">\n                        '+t+"\n                      </div>\n                    ";this.$btnsBox.find(".toolbar-btn-msg .toolbar-subMenu-box").append(s)}else this.setRemind("https://passport.csdn.net/account/login",0);T.length&&T.forEach(function(e){return e()})},getSessionList:function(){return new Promise(function(e,n){t.ajax({url:"https://msg.csdn.net/v1/im/preview",type:"get",data:{},xhrFields:{withCredentials:!0},success:function(t){if(!0===t.status){var n=t.data||[];e(n)}},error:function(t){e([])}})})},getAbTestData:function(){return new Promise(function(e,n){t.ajax({url:"https://msg.csdn.net/v1/im/abTest/isNewVersion",type:"get",xhrFields:{withCredentials:!0},success:function(t){!0===t.isNewVersion?e(!0):n()},error:function(){n()}})})},renderCoupon:function(n){var o=t('<a href="https://i.csdn.net/#/msg/notice" class="toolbar-msg-coupon">你有一张VIP限时优惠券哦</a>');n&&n>0&&-1===e.location.href.indexOf("assign_skin_id")&&(this.$msgBox.append(o),setTimeout(function(){o.remove()},5e3))},renderGuide:function(e){var n=this,a=o("c-toolbar-loginguide"),i=t('<span class="toolbar-msg-guide"><a href="https://i.csdn.net/#/msg/index">登录查看未读消息</a><i></i></span>');!a&&e&&(i.find("i").click(function(e){r("c-toolbar-loginguide",1,864e5),i.remove()}),!n.hasLogin&&e>0&&this.$msgBox.append(i))},renderLiveMsg:function(e){var n=this;if(e&&1===e.status){var o=t('<div class="toolbar-msg-live">\n                      <a class="toolbar-msg-live-title" target="_blank" href="'+e.url+'">'+e.title+'</a>\n                      <p class="toolbar-msg-live-count"><i></i>'+e.count+'人在看</p>\n                      <div class="toolbar-msg-live-avatar"><span><img src="'+e.avatar+'" alt=""></span></div>\n                      <span class="toolbar-msg-live-close"></span>\n                    </div>');o.on("click",".toolbar-msg-live-close",function(e){o.remove()}),o.on("click",".toolbar-msg-live-title",function(t){n.reportLiveId(+e.messageId)}),n.$msgBox.append(o),setTimeout(function(){o.remove()},15e3)}},reportLiveId:function(e){if(e===e){var n={messageId:e};n=JSON.stringify?JSON.stringify(n):'{"messageId":'+e+"}",t.ajax({url:"https://msg.csdn.net/v1/web/message/view/live",type:"post",data:n,contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){}})}},renderSearchHistoryDropMenu:function(){var e=this,n=this.getSearchHistoryData(),o=JSON.stringify({spm:"3001.7957"}),r=function(t,n){var o=k("3001.4501");return JSON.stringify({spm:o,index:""+n,dest:I+"?spm="+o+"&q="+encodeURIComponent(t),extra:{searchword:t,isDoublePlaceholder:e.isDoublePlaceholder?1:0}})},a=t('<div class="toolbar-search-drop-menu" data-report-view=\''+o+"'>\n        "+(n.length?'<div class="toolbar-search-history">\n          <p class="toolbar-search-title">搜索历史<span class="toolbar-search-clear"><i></i>清空</span></p>\n          <ul>\n            '+n.map(function(e,t){return'<li class="toolbar-search-item" data-type="history" data-index="'+t+"\" data-report-view='"+r(e,t)+"'><span>"+e+'</span><span class="del"></span></li>'}).join("")+"\n          </ul>\n        </div>":"")+"\n      </div>");a.on("click",".toolbar-search-clear",function(t){e.clearSearchHistory(),a.find(".toolbar-search-history").remove(),a.removeClass("toolbar-search-half")}).on("click",".del",function(n){var o=t(this).parent().text();e.clearSingleSearchHistory(o),t(this).parent().remove(),n.stopPropagation()}).on("click","li",function(n){e.clickSearchItemHandler.call(e,t(this)),e.isMiniRenderSearch||e.setAnimate(e.$NavBox,{width:"auto"})}).on("mouseover","li.toolbar-search-item",function(e){t(this).addClass("active").siblings().removeClass("active"),t(this).find(".del").show()}).on("mouseleave","li.toolbar-search-item",function(){t(this).removeClass("active"),t(this).find(".del").hide()}),e.clearSeachDropMenu(),e.$searchHotAndHistoryDropMenu=a,setTimeout(function(){e.reportViewCheck()}),n.length?(e.$searchBox.append(a),e.getHotSearchData(function(){a.addClass("toolbar-search-half")},n)):e.getHotSearchData(function(){e.$searchBox.append(a)},n)},renderSearchHotDropMenu:function(e,n){var o=this,r=Array.isArray(e)?e.slice():[];if(this.isDoublePlaceholder&&this.placeholderRight&&this.placeholderRight.productId){r=r.filter(function(e){return e.productId!==o.placeholderRight.productId});var a=Object.assign({},this.placeholderRight,{productId:g(this.placeholderRight.productId)});r.unshift(a)}if(r.length){"function"==typeof n&&n();var i=function(e,t){var n=e.productId||e.word,r=k("3001.4502"),a=Object.assign({},e.reportData.data,{spm:r,index:""+t,dest:I+"?spm="+r+"&q="+encodeURIComponent(n),extra:{searchword:n,isDoublePlaceholder:o.isDoublePlaceholder?1:0}});try{var i=JSON.parse(e.reportData.data.extra);a.extra=Object.assign({},i,a.extra)}catch(e){}return JSON.stringify(a)},s=t('<div class="toolbar-search-hot guess">\n        <p class="toolbar-search-title">搜索发现</p>\n        <ul>\n          '+r.map(function(e,t){return'<li class="toolbar-search-item '+(t<=2||e.hot?"hot":"")+'" data-type="hot" data-index="'+t+"\" data-report-view='"+i(e,t)+"'><span>"+(e.productId||e.word)+"</span></li>"}).join("")+"\n        </ul>\n      </div>");s.on("mouseover",".toolbar-search-item",function(e){t(this).addClass("active").siblings().removeClass("active")}).on("mouseleave",".toolbar-search-item",function(){t(this).removeClass("active")}),this.$searchHotAndHistoryDropMenu.find(".toolbar-search-hot").remove(),this.$searchHotAndHistoryDropMenu&&this.$searchHotAndHistoryDropMenu.append(s),setTimeout(function(){o.reportViewCheck()})}},renderAssociateWordDropMenu:function(e){var n=t("#toolbar-search-input").val();if(this.searchInputFocus&&""!==n){var o=this,r=t(t.parseHTML('\n        <div id="dropDownList" class="toolbar-search-drop-menu associate-word-drop-menu" style="height: auto;">\n          <ul class="drop-menu-list">\n            '+e.map(function(e,t){var n=k("3001.7498"),r=e.ext&&e.ext.query||e.productId,a=I+"?spm="+n+"&q="+encodeURIComponent(r),i=e.reportData&&e.reportData.data||{};try{"string"==typeof i.extra&&(i.extra=JSON.parse(i.extra)||{})}catch(e){i.extra={}}return i.extra.searchword=r,Object.assign(i,{spm:n,dest:a}),'<li class="toolbar-search-item" data-index=\''+t+"' data-report-view='"+JSON.stringify(i)+'\'>\n                <span class="search-key-data" data-type="suggest" data-index=\''+t+"'>"+e.productId+'</span>\n                <span class="search-key-tip">\n                  '+("1"===o.searchAssociateType&&e.ext.pv>0?x(e.ext.pv)+" 人搜过":"")+"\n                  "+("2"===o.searchAssociateType&&e.ext.result_num>0?x(e.ext.result_num)+" 篇精华内容":"")+"\n                </span>\n              </li>"}).join("")+"\n          </ul>\n        </div>\n      "));r.on("click","li",function(e){o.clickSearchItemHandler.call(o,t(this).find(".search-key-data")),o.isMiniRenderSearch||o.setAnimate(o.$NavBox,{width:"auto"})}),r.on("mouseover","li.toolbar-search-item",function(e){t(this).addClass("active").siblings().removeClass("active")}),this.$searchBox.append(r),this.reportViewCheck()}},clickSearchBtnHandler:function(n,o){var r=this.placeholder&&this.placeholder.productId||this.placeholderRight&&this.placeholderRight.productId,a=n||r;if(!n&&this.placeholder&&"csdn_ad"===this.placeholder.recommendType&&this.placeholder.ext&&this.placeholder.ext.url)return void e.open(this.placeholder.ext.url);if(void 0===a||null===a)return t("#toolbar-search-input").focus(),!1;var i=encodeURIComponent(a),s=!n&&r,c="",l=I+"?spm="+k(o)+"&q="+i+"&t="+f()+"&u=",d=s?this.urlParamsPlaceholder:this.urlParams;if(d){for(var p in d)if(d.hasOwnProperty(p)){var u=d[p];c+="&"+p+"="+u}l+=c}return this.urlParams="",e.location.href.indexOf("so.csdn.net")>-1?e.csdn&&e.csdn.toolbarSearchUrl?e.location.href=e.csdn.toolbarSearchUrl+i+c:e.location.href=l:e.open(l),l},clickSearchItemHandler:function(n){var o=this,r=[n.text(),n.data("type"),n.data("index")],a=r[0],i=r[1],s=r[2],c={},l="";if("hot"===i){var d=o.hotSearchData[s],p=d&&d.reportData;c=p?o.getHotSearchPointData(p,a):Object.assign({},c,{spm:"3001.4502"}),d&&"csdn_ad"===d.recommendType&&d.ext&&d.ext.url&&(l=d.ext.url)}else if("history"===i)c={spm:"3001.4501"};else{var u=this.searchAssociateWord[s],h=u&&u.reportData&&u.reportData.data;this.urlParams=u&&u.reportData&&u.reportData.urlParams,c=Object.assign({},h,{spm:"3001.7498"})}l?e.open(l):(t("#toolbar-search-input").val(a).blur(),l=o.clickSearchBtnHandler.call(o,a,c.spm));try{"string"==typeof c.extra&&(c.extra=JSON.parse(c.extra))}catch(e){}c.extra=Object.assign({},c.extra,{searchword:a,isDoublePlaceholder:this.isDoublePlaceholder?1:0}),Object.assign(c,{dest:l,index:""+s}),o.buriedPoint(c),o.clearSeachDropMenu(),o.isMiniRenderSearch||o.toggleSearchBarInput(),a&&o.toggleClearIcon(!0)},clickAnnouncementHandler:function(){this.hasLogin&&(t("#toolbar-announcement").find(">i").remove(),this.announcement&&(this.announcement.announcementCount=0),r("announcement-new",JSON.stringify(this.announcement),this.announcement.announcementExpire||3e5),this.clearReadAnnouncement())},clickLogoutBtnHandler:function(){var t={mod:"popu_789"},n="https://passport.csdn.net/account/logout?from="+encodeURIComponent(e.location.href);t.dest=n,t.extend1="退出",e.location.href=n,csdn&&csdn.report&&csdn.report.reportClick(t)},clearSeachDropMenu:function(){t(".toolbar-search-drop-menu").remove()},clearReadAnnouncement:function(){t.ajax({url:"https://msg.csdn.net/v1/web/message/read_announcement",type:"post",contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){},error:function(e){void 0}})},setUserAvatar:function(e){var n=e.avatarUrl;e.totalCount;n&&(this.currentUser.avatar=n,t(".toolbar-btn-login").find(".hasAvatar img").attr("src",n),t("#csdn-toolbar-profile").find(".csdn-profile-avatar > img").attr("src",n))},getHotSearchPointData:function(e,t){this.urlParams=e.urlParams;var n=Object.assign({},e.data,{spm:"3001.4502",extra:{searchword:t,isDoublePlaceholder:this.isDoublePlaceholder?1:0}});try{Object.assign(n.extra,JSON.parse(e.data.extra))}catch(e){}return n},getReadAnnouncement:function(){var e=this;t.ajax({url:"https://msg.csdn.net/v1/web/message/view/announcement",type:"post",contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){if(t.status){var n=t.data;e.announcement=n,r("announcement-new",JSON.stringify(n),n.announcementExpire||3e5),e.hasLogin?e.getUnreadMsg():e.renderMsgMenu({announcement:n})}},error:function(e){void 0}})},getUnreadMsg:function(){if(this.hasLogin||o("UN")){var e=this,n=JSON.stringify?JSON.stringify({coupon:!0}):'{"coupon":true}';t.ajax({url:"https://msg.csdn.net/v1/web/message/view/unread",type:"post",data:n,contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){if(t.status){var n=t.data;e.setRemind("",n.totalCount),e.renderCoupon(n.coupon_order),e.renderGuide(n.totalCount),e.renderLiveMsg(n.live_info),e.setUserAvatar(n),e.getAbTestData().then(function(t){return e.getSessionList()},function(t){return e.renderMsgMenu(n),Promise.reject()}).then(function(t){e.renderMsgMenuNewVersion(t)}).catch(function(){})}}})}},getSearchHistoryData:function(){var e=this.searchHistoryArray.splice(0,10);return this.isDoublePlaceholder&&this.placeholder&&this.placeholder.productId&&(e.unshift(g(this.placeholder.productId)),e=Array.from(new Set(e))),e},getSearchHistoryArray:function(e){var n=this;t.ajax({url:"https://so.csdn.net/api/v1/get_search_his",type:"get",contentType:"application/json",dataType:"json",xhrFields:{withCredentials:!0},success:function(t){200==t.code&&(n.searchHistoryArray=t.data.map(function(e){return g(e)}),e&&e.call(n))},error:function(e){void 0}})},getHotSearchData:function(e,n){var r=this;if((new Date).valueOf()-r.copyHotSearchDataTime<=2e3&&r.hotSearchData)return void r.renderSearchHotDropMenu(r.hotSearchData,e);t.ajax({url:"https://silkroad.csdn.net/api/v2/assemble/list/channel/pc_hot_word",type:"get",data:{user_foormark:1,channel_name:"pc_hot_word",size:20,user_name:r.currentUser.userName,platform:"pc",imei:o("uuid_tt_dd")},xhrFields:{withCredentials:!0},contentType:"application/json",dataType:"json",success:function(t){if(200===t.code){var o=t.data&&t.data.items||[];Array.isArray(o)&&Array.isArray(n)&&(o=o.filter(function(e){return!n.includes(e.productId)})),o=o.slice(0,10),o.forEach(function(e){e.productId=g(e.productId)}),r.copyHotSearchData(o),r.renderSearchHotDropMenu(o,e)}},error:function(t){r.hotSearchData&&r.renderSearchHotDropMenu(r.hotSearchData,e)}})},getSearchAssociateWord:function(e,n){if(this.searchInputValue){if(e===this.historySearchInputValue)return this.clearSeachDropMenu(),void n(this.searchAssociateWord);var r=this;t.ajax({url:"https://silkroad.csdn.net/api/v2/rcmd/list/channel/pc_toolbar_associateword",type:"POST",data:JSON.stringify({channel:"pc_toolbar_associateword",ext:{isAcceptDownGrade:!0,summary:!0,query:e,pageSize:10,page:0,type:"suggest",deviceid:"-",platform:"pc",user_name:r.currentUser.userName,imei:o("uuid_tt_dd")},size:10}),contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){if(200===t.code){if(r.clearSeachDropMenu(),!t.data.items.length)return;var o=t.data.items.map(function(e){return{productId:e.product_id,reportData:e.report_data,ext:e.ext}});r.searchAssociateType=t.data.ext.num_show,r.searchAssociateWord=o,r.historySearchInputValue=e,n&&n(o)}}})}},getHotSearchWordData:function(n){var r={new_hot_flag:1,channel_name:"pc_hot_word",size:20,user_name:this.currentUser.userName,platform:"pc",imei:o("uuid_tt_dd")};if(e.toolbarSearchExt)try{var a=_typeof(e.toolbarSearchExt);"object"===a?r.toolbarSearchExt=JSON.stringify(e.toolbarSearchExt):"string"===a&&(r.toolbarSearchExt=e.toolbarSearchExt)}catch(e){void 0}var i=this;t.ajax({url:"https://silkroad.csdn.net/api/v2/assemble/list/channel/search_hot_word",type:"get",data:r,contentType:"application/json",dataType:"json",xhrFields:{withCredentials:!0},success:function(e){if(200===e.code){var t=e.data&&e.data.items||[];r.toolbarSearchExt?this.isDoublePlaceholder=!1:i.isDoublePlaceholder=e.data&&e.data.ext&&e.data.ext.ab_test_ext&&"1"===e.data.ext.ab_test_ext.his_foot_flag,t.length&&(i.utm_medium=e.data.ext.utm_medium||"",i.isDoublePlaceholder?(i.placeholderList=t.filter(function(e){return!!e.productId&&"alirecmd"===e.strategyId}),i.placeholderListRight=t.filter(function(e){return!!e.productId&&"alirecmd"!==e.strategyId})):(i.placeholderList=t.filter(function(e){return!!e.productId}),i.placeholderListRight=null),i.setPlaceholderInterval(i.placeholderList,i.placeholderListRight)),n&&n()}},error:function(e){void 0}})},refreshPlaceholder:function(){this.setPlaceholderInterval(this.placeholderList,this.placeholderListRight,this.placeholderIndex)},setPlaceholderInterval:function(e){var n=this,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=e&&e.length?r%e.length:0,i=o&&o.length?r%o.length:0;this.placeholder=e&&e[a]||null,this.placeholderRight=this.isDoublePlaceholder&&o&&o[i]||null,this.placeholderIndex=r,setTimeout(function(){n.setSearchInputPlaceholder(n.placeholder,n.placeholderRight),!n.isDoublePlaceholder&&n.placeholder&&a<3||n.isDoublePlaceholder&&n.placeholderRight&&i<3?(t(".icon-fire").show(),t("#toolbar-search-input").css("textIndent","32px")):(t(".icon-fire").hide(),t("#toolbar-search-input").css("textIndent","12px"))},200),clearTimeout(this.placeholderTimeout),this.placeholderTimeout=setTimeout(function(){n.setPlaceholderInterval(e,o,r+1)},5e3)},copyHotSearchData:function(e){if(e){var t=this;t.hotSearchData=e,t.copyHotSearchDataTime=(new Date).getTime()}},clearSingleSearchHistory:function(e){t.ajax({url:"https://so.csdn.net/api/v1/del_one_search_his?del_query="+e,type:"get",contentType:"application/json",dataType:"json",xhrFields:{withCredentials:!0},error:function(e){void 0}})},clearSearchHistory:function(){this.isDoublePlaceholder&&(this.placeholderList=[]),t.ajax({url:"https://so.csdn.net/api/v1/del_search_his",type:"get",contentType:"application/json",dataType:"json",xhrFields:{withCredentials:!0},error:function(e){void 0}})},getSearchInputPlaceholder:function(){return this.placeholder?this.placeholder&&this.placeholder.productId:~location.host.indexOf("edu")?"搜学院课程":"搜CSDN"},setSearchInputPlaceholder:function(e,n){this.urlParamsPlaceholder=e&&e.reportData&&e.reportData.urlParams;var o=e&&e.productId||"",r=n&&n.productId||"",a="";this.searchInputFocus?a=o||r||"":o&&r?(o=o.slice(0,12),r=r.slice(0,12),a=o+" | "+r):a=o||r||"",this.reportPlaceholderView(e),this.reportPlaceholderView(n),a&&t("#toolbar-search-input").attr("placeholder",a)},reportPlaceholderView:function(n){try{if(!n||n.isViewReported||t("#toolbar-search-input").val())return;if("csdn_ad"===n.recommendType||location.href.includes("/article/details")){var o=n.reportData.data;o.spm="3001.7499";var r=JSON.parse(o.extra);r.recommendType=n.recommendType,o.extra=JSON.stringify(r),e.csdn.report.reportView(o),n.isViewReported=!0}}catch(e){}},isEffectiveTime:function(e){if(!e)return!1;if(e.always)return!0;var t=(new Date).valueOf(),n=new Date(e.start).valueOf();return t<=new Date(e.end).valueOf()&&t>=n},getCurrentLogoData:function(e){var t=this;return Array.isArray(e)?e.reduce(function(e,n){return e="default"!==n.type||e?e:n,e=t.isEffectiveTime(n.time)?n:e},void 0):"object"===(void 0===e?"undefined":_typeof(e))?e:t.logoData},getToolbarData:function(n){var o=this;t.ajax({url:"https://img-home.csdnimg.cn/data_json/toolbar/toolbar1105.json",type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",success:function(t){if(void 0,o.navData=t.menus.slice(0,t.menus.length-1),o.vipData=t.menus.pop(),void 0,void 0,o.logoData=o.getCurrentLogoData(t.logo),void 0,o.background=t.background,o.redpackData=t.redpackData,o.mpMenuData=t.mpMenuData?t.mpMenuData:o.mpMenuData,o.searchLoginAb=t.searchLoginAb?t.searchLoginAb:null,o.searchLoginAbSpm=t.searchLoginAbSpm?t.searchLoginAbSpm:null,o.cknowData=t.cknowAb?t.cknowAb:null,o.mpAb=t.mpAb?t.mpAb:null,o.searchToolbarData=t.searchToolbar?t.searchToolbar:null,e.csdn.toolbarData=t,o.mpAb){var r=(null!=(_ref1=/(; )?(uuid_tt_dd|_javaeye_cookie_id_)=([^;]+)/.exec(e.document.cookie))?_ref1[3]:void 0)||"";(r?r.substring(r.length-6)%16:0)<o.mpAb.abMax&&(o.mpUrl=o.mpAb.url,e.csdn.toolbarData.mpUrl=o.mpUrl)}n&&n.call(o)},error:function(e){n&&n.call(o)}})},hasShowMpMenu:function(){return!("normal"!==C||!this.mpMenuData.isShow||void 0===this.mpMenuData.maxSegment)&&o("c_segment")<=parseInt(this.mpMenuData.maxSegment)},getMpMenuIcon:function(){if(!this.mpMenuData.isShow)return"";if(this.mpMenuData.icon&&this.mpMenuData.icon.length>0){return'<img src="'+(this.mpMenuData.icon[D]?this.mpMenuData.icon[D]:this.mpMenuData.icon[0])+'" class="icon-mp-menu">'}return""},buriedPoint:function(e){try{csdn&&csdn.report&&csdn.report.reportClick(e)}catch(e){void 0}},reportViewCheck:function(){try{csdn&&csdn.report&&csdn.report.viewCheck()}catch(e){void 0}},setDocumentTitle:function(e){var t=document.title;e>0&&(document.title="("+e+"条消息) "+t)},setRemind:function(e,n){var r=t("#toolbar-remind"),a=t(".toolbar-inside").hasClass("exp2");this.hasLogin;var i=o("toolbar_remind_num")?o("toolbar_remind_num"):0;r.html('<span class="pos-rel">'+(a?"":"消息")+(!this.hasLogin&&i<3||n>0?'<i class="toolbar-msg-count"></i>':"")+"</span>")},setAnimate:function(e,t,n){n&&e.animate(t,n)||e.css(t)},toggleNavItems:function(e,t){var n=this.$NavBox.find(">li").eq(e).nextAll();t&&n.show()||n.hide()},getNavItemsWidthByCount:function(e){var n=0;return this.$NavBox.find(">li").eq(e).prevAll().each(function(e,o){n+=t(this).width()}),n+=4},toggleSearchBarInput:function(e){var n=t(document).width();if(!(n>1440)){var o=5;1366<n&&n<=1440?o=5:1280<n&&n<=1366?o=5:n<=1280&&(o=4);var r=this.getNavItemsWidthByCount(o);"focus"===e?this.setAnimate(this.$NavBox,{width:r},200):this.setAnimate(this.$NavBox,{width:"auto"},0),this.toggleNavItems(o-1,"focus"!==e)}},getToolbarStyle:function(){var e=this,t=e.background,n=["default","dark","home"],o=n[D];if(t){var r=t[o];if(void 0,r.indexOf("http")>=0)return"background: url("+r+") no-repeat center center;background-size: cover;";if(r.indexOf("#")>=0)return"background: "+r}},setToolbarMsgCountByType:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(e&&this.hasLogin){var o=t(".toolbar-btn-msg").find('a[data-type="'+e+'"]');if(0!==o.length){o.find("i").remove(),o.html('<span class="pos-rel">'+o.text()+(n>0?"<i></i>":"")+"</span>");var r=t(".toolbar-btn-msg").find(".toolbar-subMenu a i").length;void 0,this.setRemind("",r)}}},chain:function(){var e=o("UserName");return v("toolbarReady"),e?this.getUnreadMsg():this.renderMsgMenu(),a("announcement"),a("announcement_new"),a("searchHistoryArray"),a("searchHistoryArray-new"),this}},function(){var e=t('meta[name="toolbar"]'),o="",r=0,a=0;if(e.length){var i=e.attr("content")||{};i=JSON.parse(i),r=i.type||r,C=i.model||C,a=i.fixModel||a}else r=b("toolbarSkinType")||r;o="https://g.csdnimg.cn/common/csdn-toolbar/"+S[r]+".css",D=r,$=a,-1===location.host.indexOf("loc-toolbar")&&n(o)}();var U=new _;e.csdn.toolbar={setToolbarMsgCountByType:U.setToolbarMsgCountByType.bind(U),configuration_tool_parameterv:m,renderSeasrchBox:U.outPutSearchMethod.bind(U)}}(window,jQuery),function(){function e(e){return e.some(function(e){return"*"===e||!!~location.href.indexOf(e)})}function t(e){return e.some(function(e){return new RegExp("^http(s)?:\\/\\/("+e+")(\\/|\\/\\?)?(spm=\\S*)?$").test(location.href)})}var n=function(){function e(){_classCallCheck(this,e),this.active={},this.loginUrl="https://passport.csdn.net/account/login?from="+encodeURIComponent(window.location.href)}return _createClass(e,[{key:"init",value:function(){this.getActiveData()}},{key:"getActiveData",value:function(){var e=this,t=this;$.ajax({url:"https://mp-activity.csdn.net/activity/report",type:"post",contentType:"application/json; charset=utf-8",xhrFields:{withCredentials:!0},data:JSON.stringify({pageUrl:window.location.href,action:"pageView",platform:"pc"}),dataType:"json",success:function(n){void 0;var o=n.data;return t.active=o,!(!o.matched||"popWindow"!==o.operationCommand)&&(o.ext.report&&-1!==o.ext.report.indexOf("exposure")&&window.csdn&&window.csdn.report&&window.csdn.report.reportView({spm:e.active.ext.spm}),"center"===o.ext.position?(t.renderCenterDom(),t.renderCenterCss()):"right"===o.ext.position?(t.renderRightDom(),t.renderRightCss()):(t.renderCenterDom(),t.renderCenterCss()),!1)},error:function(e){void 0}})}},{key:"handleClose",value:function(){var e=$("#csdn-active-dialog .closeBtn"),t=$("#csdn-active-mask");void 0,e.click(function(){void 0,$("#csdn-active-mask").remove(),$("#csdn-active-dialog").remove()}),t.click(function(){void 0,$("#csdn-active-mask").remove(),$("#csdn-active-dialog").remove()})}},{key:"handleJump",value:function(){var e=$("#csdn-active-dialog .active_main");void 0,e.click(function(){void 0,$("#csdn-active-mask").remove(),$("#csdn-active-dialog").remove()})}},{key:"renderCenterDom",value:function(){void 0;var e='\n        <div id="csdn-active-mask" class="'+(this.active.ext.hasMask?"":"hide")+'"></div>\n        <div id="csdn-active-dialog">\n        <div class="dialog__wrapper">\n            <a \n              '+("close"!==this.active.ext.clickAction?"href='"+(this.active.ext.clickUrl||this.loginUrl)+"'":"")+"\n              "+(this.active.ext.report&&-1!==this.active.ext.report.indexOf("click")?'data-report-click=\'{"spm":'+JSON.stringify(this.active.ext.spm)+"}'":"")+'\n              data-report-query="spm='+this.active.ext.spm+'"\n              class="active_main"><img src="'+this.active.ext.backgroundPicUrl+'" alt=""></a>\n            <span class="closeBtn close_icon1"></span>\n          </div>\n        </div>\n      ';if($("body").append(e),this.handleClose(),this.handleJump(),-1!=this.active.ext.popUpDuration)var t=setTimeout(function(){$("#csdn-active-mask").remove(),$("#csdn-active-dialog").remove(),clearTimeout(t)},1e3*this.active.ext.popUpDuration)}},{key:"renderRightDom",value:function(){void 0;var e='\n        <div id="csdn-active-mask" class="'+(this.active.ext.hasMask?"":"hide")+'"></div>\n        <div id="csdn-active-dialog">\n        <div class="dialog__wrapper">\n            <span class="closeBtn close_icon2"></span>\n            <a \n            '+("close"!==this.active.ext.clickAction?"href='"+(this.active.ext.clickUrl||this.loginUrl)+"'":"")+"\n            "+(this.active.ext.report&&-1!==this.active.ext.report.indexOf("click")?'data-report-click=\'{"spm":'+JSON.stringify(this.active.ext.spm)+"}'":"")+'\n            data-report-query="spm='+this.active.ext.spm+'"\n            class="active_main"><img src="'+this.active.ext.backgroundPicUrl+'" alt=""></a>\n          </div>\n        </div>\n      ';if($("body").append(e),this.handleClose(),this.handleJump(),-1!=this.active.ext.popUpDuration)var t=setTimeout(function(){$("#csdn-active-mask").remove(),$("#csdn-active-dialog").remove(),clearTimeout(t)},1e3*this.active.ext.popUpDuration)}},{key:"renderCenterCss",value:function(){void 0
;var e=$('<style type="text/css">\n                    #csdn-active-mask {\n                      position: fixed;\n                      width: 100%;\n                      height: 100%;\n                      background: rgba(0, 0, 0, 0.5);\n                      overflow: hidden;\n                      top: 0;\n                      left: 0;\n                      z-index: 2147483645;\n                    }\n                    .hide {\n                      display: none;\n                    }\n                    #csdn-active-dialog {\n                      position: fixed;\n                      overflow: hidden;\n                      top: 0;\n                      left: 0;\n                      z-index: 2147483646;\n                    }\n                    #csdn-active-dialog .dialog__wrapper {\n                      \n                      width: 400px;\n                      position: fixed;;\n                      top: 50%;\n                      left: 50%;\n                      margin-left: -200px;\n                      margin-top: -200px;\n                      box-sizing: border-box;\n                    }\n                    #csdn-active-dialog .dialog__wrapper .active_main {\n                      width: 400px;\n                      height: 400px;\n                      cursor: pointer;\n                    }\n                    #csdn-active-dialog .dialog__wrapper .active_main img{\n                      width: 400px;\n                      height: 400px;\n                    }\n                    #csdn-active-dialog .dialog__wrapper .closeBtn{\n                      display: block;\n                      width: 32px;\n                      height: 32px;\n                      margin: 24px auto;\n                      cursor: pointer;\n                    }\n                    #csdn-active-dialog .dialog__wrapper .close_icon1 {\n                      background: url("https://img-home.csdnimg.cn/images/20211202112243.png") no-repeat center center;\n                      background-size: contain;\n                    }\n                    #csdn-active-dialog .dialog__wrapper .close_icon2 {\n                      background: url("https://img-home.csdnimg.cn/images/20211208022627.png") no-repeat center center;\n                      background-size: contain;\n                    }\n                  </style>');document.head.insertBefore($(e)[0],document.head.getElementsByTagName("title")[0])}},{key:"renderRightCss",value:function(){void 0;var e=$('<style type="text/css">\n                      #csdn-active-mask {\n                        position: fixed;\n                        width: 100%;\n                        height: 100%;\n                        background: rgba(0, 0, 0, 0.5);\n                        overflow: hidden;\n                        top: 0;\n                        left: 0;\n                        z-index: 2147483645;\n                      }\n                      .hide {\n                        display: none;\n                      }\n                      #csdn-active-dialog {\n                        position: fixed;\n                        overflow: hidden;\n                        top: 0;\n                        left: 0;\n                        z-index: 2147483646;\n                      }\n                      #csdn-active-dialog .dialog__wrapper {\n                        \n                        width: 343px;\n                        position: fixed;\n                        top: 48px;\n                        right: 270px;\n                        box-sizing: border-box;\n                      }\n                      #csdn-active-dialog .dialog__wrapper .active_main {\n                        display: block;\n                        overflow: hidden;\n                        width: 343px;\n                        height: 80px;\n                        cursor: pointer;\n                      }\n                      #csdn-active-dialog .dialog__wrapper .active_main img{\n                        width: 343px;\n                        height: 80px;\n                      }\n                      #csdn-active-dialog .dialog__wrapper .closeBtn{\n                        display: block;\n                        width: 28px;\n                        height: 28px;\n                        cursor: pointer;\n                        float:right\n                      }\n                      #csdn-active-dialog .dialog__wrapper .close_icon1 {\n                        background: url("https://img-home.csdnimg.cn/images/20211202112243.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                      #csdn-active-dialog .dialog__wrapper .close_icon2 {\n                        background: url("https://img-home.csdnimg.cn/images/20211208022627.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                  </style>');document.head.insertBefore($(e)[0],document.head.getElementsByTagName("title")[0])}}]),e}();$(document).on("toolbarReady",function(o){var r=window.csdn.toolbarData||{},a=r.activeData;void 0,a&&a.whiteList&&e(a.whiteList)&&(void 0,(new n).init()),a&&a.whiteRegexList&&t(a.whiteRegexList)&&(void 0,(new n).init())})}(),function(){!function(){function e(){_classCallCheck(this,e)}_createClass(e,[{key:"init",value:function(){"www.csdn.net"===window.location.host&&"/"===window.location.pathname&&this.renderStyle()}},{key:"renderStyle",value:function(){var e=document.createElement("style");e.innerHTML="\n        html {\n          -webkit-filter: grayscale(100%); /* webkit */\n          -moz-filter: grayscale(100%); /*firefox*/\n          -ms-filter: grayscale(100%); /*ie9*/\n          -o-filter: grayscale(100%); /*opera*/\n          filter: grayscale(100%);\n          filter:progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);\n        }\n        body{\n          filter:gray; /*ie9- */\n          background : none!important;\n        }\n      ",document.querySelector("head").appendChild(e)}}])}()}(),function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e&&"UD"==e)return decodeURIComponent(o[1]);if(o[0]==e)return decodeURI(o[1])}}function t(e,t,n){var o=new Date;if(-1==n)return void(document.cookie=e+"="+escape(t)+";domain=.csdn.net;path=/");if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=e+"="+escape(t)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function n(e){var t=new Date;document.cookie=e+"="+escape("1")+";max-age=0;expires="+t.toGMTString()+";domain=.csdn.net;path=/"}function o(e){try{e&&$(document).trigger(e)}catch(e){void 0}}function r(e,t){var n=[603,604,605,606,607];void 0===t&&(t=(d?539:536)+","+n.toString());var o="https://kunpeng.csdn.net/ad/json/list?positions="+t;$.ajax({url:o,type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},success:function(n){if(200===n.code&&n.data){if(n.data.length>0){var o=n.data[0];if(-1===[536,539,541].indexOf(o.adPositionId))return;switch(o.adType){case"baodeng":var d=n.data.reduce(function(e,t,n){return 0===n?e=Object.assign({},t,{children:[]}):e.children.push(t),e},{});void 0,a(d);break;case"code":i(o)}}}else csdn&&csdn.toolbarData&&csdn.toolbarData.advert&&csdn.toolbarData.advert.checkPlugin&&541!==t&&setTimeout(function(){!s()&&!c()||l()||r(e,541)},5e3)}})}function a(r){var a=!!e("is_advert");if(!r)return void n("is_advert");var i="width:100%; height:100%; background-image: url("+r.imgUrl+"); background-size: auto 80px;background-repeat: no-repeat; background-position: center center;",s="width:100%; height:100%; background-image: url("+r.bigImgUrl+"); background-size: auto 320px;background-repeat: no-repeat; background-position: center center;",c=$('<div class="toolbar-advert">\n            <a href="'+r.clickUrl+'" target="_blank" style="background: '+r.backgroundColor+';" class="toolbar-advert-default '+(a?"":"toolbar-advert-lg")+'"><div style="'+(a?i:s)+'"></div>\n              '+(r.children.length?'<span class="toolbar-advert-more">'+r.children.map(function(e){return'<span class="toolbar-advert-more-item" data-adress="'+e.clickUrl+'" style="background-image: url('+e.imgUrl+'); background-size: contain;background-repeat: no-repeat; background-position: center center;"><img style="width:0;height:0;display:none;" src="'+e.exposureUrl+'"/></span>'}).join("")+"</span>":"")+'\n            </a>\n            <span class="toolbar-adver-btn">关闭</span>\n            <img style="width:0;height:0;display:none;" src="'+r.exposureUrl+'"/>\n          </div>');c.find(".toolbar-adver-btn").click(function(e){return c.remove(),o("toolbarHeightChange"),r.closeAdClickUrl&&$.ajax({url:r.closeAdClickUrl,type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},success:function(e){}}),!1}),c.on("click",".toolbar-advert-more-item",function(e){var t=$(this).data("adress")?$(this).data("adress"):"";return t&&window.open(t,"_blank"),e.stopPropagation(),!1}),$("#csdn-toolbar").prepend(c),o("toolbarHeightChange"),!a&&t("is_advert","1",864e5),c.on("transitionend",function(e){o("toolbarHeightChange")}),setTimeout(function(){!a&&$(".toolbar-advert-default").removeClass("toolbar-advert-lg").find("div").attr("style",i),o("toolbarHeightChange")},1e3*(r.showSeconds||5))}function i(e){var t=$('<div class="toolbar-advert"></div>');if(e.content&&t.append(e.content),e.exposureUrl){var n=$('<img width="0" height="0" style="display:none;" src="'+e.exposureUrl+'">');t.append(n)}o("toolbarHeightChange"),$("#csdn-toolbar").prepend(t),setTimeout(function(){o("toolbarHeightChange")},200)}function s(){var e=$('<div class="adsbox ad_box ads_box"></div>');if($("#csdn-toolbar").append(e),e.is(":hidden"))return e.remove(),!0;e.remove()}function c(){return $("#greenerSettings").length}function l(){return $("#open_chromePlugin_tab").length}var d=!1;$(document).on("toolbarReady",function(e){void 0,void 0,void 0,(window.csdn.toolbarData.advert.blacklist||[]).indexOf(location.host)>=0||r()})}(),function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e&&"UD"==e)return decodeURIComponent(o[1]);if(o[0]==e)return decodeURI(o[1])}}function t(e,t,n){var o=new Date;if(-1==n)return void(document.cookie=e+"="+t+";domain=.csdn.net;path=/");if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=e+"="+t+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function n(e){return e.some(function(e){return"*"===e||!!~location.href.indexOf(e)})}var o=function(){function n(){_classCallCheck(this,n),this.allData=[],this.barrageList=[],this.barrageDomArr=[],this.fnArr=[],this.barrageBoxWidth=400,this.pollDelay=30,this.timer=null,this.errorCount=0,this.toolbarHeight=44}return _createClass(n,[{key:"init",value:function(){"1"!==e("hideBarrage")&&(this.getToolbarHeight(),this.insertCss(),this.queryBarrage(),this.bindToolbarHeightChange(),this.handlePageVisibilityChange())}},{key:"queryBarrage",value:function(){var e=this;$.ajax({url:"https://barrage-kunpeng.csdn.net/api/barrage/list",type:"get",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){if(200===t.code){if(e.pollDelay=t.pollDelay,!t.data.length)return void e.setTimer();e.allData=t.data,e.barrageList=e.getBarrageList(),e.renderBarrage(),e.start(),e.insertAdCookie(),e.resetErrorCount()}else 201!==t.code&&e.handleQueryError()},error:function(t){e.handleQueryError()}})}},{key:"handleQueryError",value:function(){this.errorCount++,this.setTimer(),this.errorCount>10&&clearTimeout(this.timer)}},{key:"getBarrageList",value:function(){var e=this.allData.slice(0,3);return this.allData=this.allData.slice(3),e.map(function(e){return Object.assign({},e,{x:0,speed:2+Number(Math.random().toFixed(1)),stop:!1})})}},{key:"start",value:function(){for(var e=this,n=document.documentElement.clientWidth,o=0,r=0;r<this.barrageDomArr.length;r++)!function(r){e.fnArr.push(function(){if(e.barrageList[r].x-=e.barrageList[r].speed,e.barrageDomArr[r].style.transform="translateX("+e.barrageList[r].x+"px)",n+e.barrageList[r].x>=-e.barrageBoxWidth&&!e.barrageList[r].stop)requestAnimationFrame(e.fnArr[r]);else if(n+e.barrageList[r].x<-e.barrageBoxWidth&&!e.barrageList[r].stop&&++o===e.barrageDomArr.length){if(e.fnArr=[],$("#barrageBox").remove(),!e.checkIsPassport()){var a=e.getTomorrowTimeRemaining();t("ad_last_time",Date.now(),a)}e.barrageList=e.getBarrageList(),e.barrageList.length?(e.renderBarrage(),e.start(),e.insertAdCookie()):e.setTimer()}})}(r);for(var a=0;a<this.barrageList.length;a++)!function(t){requestAnimationFrame(e.fnArr[t]),setTimeout(function(){window.csdn.report.reportView({mod:"popu_894",extend1:e.barrageList[t].barrageId,dest:e.barrageList[t].clickUrl})},1e3)}(a)}},{key:"insertCss",value:function(){var e=document.getElementsByTagName("head")[0],t=document.createElement("style");t.type="text/css",t.appendChild(document.createTextNode("\n        #barrageBox .barrage-item {\n          position:relative;\n          display: flex;\n          align-items: center;\n          height: 40px;\n          margin-bottom: 62px;\n          border-radius: 0 100px 100px 0;\n          width: fit-content;\n        }\n        #barrageBox .barrage-item.barrage-link-hide {\n          visibility: hidden;\n        }\n        #barrageBox .barrage-item .barrage-follow {\n          cursor: pointer;\n          position: absolute;\n          left: 8px;\n          bottom: -9px;\n          z-index: 999;\n          border: none;\n          outline: none;\n          font-size: 12px;\n          font-weight: 500;\n          line-height: 17px;\n          color: #FFFFFF;\n          -webkit-background-clip: text;\n          -webkit-text-fill-color: transparent;\n        }\n        #barrageBox .barrage-item .barrage-follow.is-follow {\n          left: 2px\n        }\n        #barrageBox .barrage-item .barrage-link {\n          position: relative;\n          display: flex;\n          align-items: center;\n          color: #fff;\n          font-size: 14px;\n          font-weight: 500;\n          height: 100%;\n          padding-right: 24px;\n          min-width: 294px;\n        }\n        #barrageBox .barrage-item .barrage-link img {\n          width: 38px;\n          height: 38px;\n          display: block;\n          border-radius: 50%;\n          margin-right: 27px;\n        }\n        #barrageBox .barrage-item .barrage-link .barrage-content {\n          max-width: 592px;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n        #barrageBox .barrage-item .barrage-link .barrage-bg {\n          width: 320px;\n          height: 80px;\n          position: absolute;\n          left: -26px;\n          top: 50%;\n          transform: translateY(-50%);\n        }\n        #barrageBox .barrage-item .barrage-right {\n          position: relative;\n          height: 100%;\n          width: 44px;\n          display: flex;\n          align-items: center;\n        }\n        #barrageBox .barrage-item .barrage-right .barrage-close {\n          width: 100%;\n          height: 100%;\n          position: absolute;\n          background: url('https://img-home.csdnimg.cn/images/20201104030927.png') no-repeat center;\n          background-size: 16px 16px;\n          cursor: pointer;\n        }\n        #barrageBox .barrage-item .barrage-right .barrage-right-border {\n          width: 1px;\n          height: 20px;\n          background: rgba(255, 255, 255, 0.3);\n        }\n      ")),e.appendChild(t)}},{key:"renderBarrage",value:function(){for(var n="",o=0;o<this.barrageList.length;o++){var r=this.barrageList[o].barrageId,a=this.barrageList[o].clickUrl;n+='\n          <div class="barrage-item" style="background-color: '+this.barrageList[o].bgColor+"; box-shadow: -12px 4px 14px 0 "+this.barrageList[o].shadowColor+';">\n          '+(this.barrageList[o].type?"":'<button class="barrage-follow" data-username="'+this.barrageList[o].username+'">关注</button>')+'\n            <a class="'+(this.barrageList[o].type?"barrage-link barrage-link-redpack":"barrage-link")+'" href="'+(this.barrageList[o].type?"javascript:;":a)+'" target="'+(this.barrageList[o].type?"":"_blank")+'" data-report-click=\'{"mod":"popu_894","extend1": "'+r+'", "dest": "'+a+'"}\'>\n              <img src="'+this.barrageList[o].headImage+'" alt="">\n              <div class="barrage-content">'+this.barrageList[o].nickname.slice(0,10)+"："+this.barrageList[o].content+'</div>\n              <div class="barrage-bg" style="background: url('+this.barrageList[o].bgImage+') no-repeat center; background-size: cover;"></div>\n            </a>\n            <div class="barrage-right">\n              <div class="barrage-close"></div>\n              <div class="barrage-right-border"></div>\n            </div>\n          </div>\n        '}$("#csdn-toolbar").append('<div id="barrageBox" style="position: fixed; z-index: 99999; top: '+(this.toolbarHeight+32)+'px;">'+n+"</div>"),this.barrageDomArr=[].concat(_toConsumableArray(document.querySelectorAll("#barrageBox .barrage-item")));var i=$("#barrageBox");this.barrageBoxWidth=i.width()+26,i.css({right:-this.barrageBoxWidth+"px"});var s=this;i.on("mouseenter",".barrage-item",function(){s.barrageList[$(this).index()].stop=!0}),i.on("mouseleave",".barrage-item",function(){s.barrageList[$(this).index()].stop=!1,requestAnimationFrame(s.fnArr[$(this).index()])}),i.on("click",".barrage-close",function(){$("#barrageBox").remove(),clearTimeout(s.timer),t("hideBarrage","1",s.getTomorrowTimeRemaining())}),i.on("click",".barrage-follow",function(){$(this).hasClass("is-follow")||s.follow(s.barrageList[$(this).parent().index()].username)}),i.on("click",".barrage-link.barrage-link-redpack",function(){if(e("UserName")){if(!$(this).hasClass("already")){var t=$(this).parent().index();window.csdn.barrageRedpack&&window.csdn.barrageRedpack.open(s.barrageList[t].typeId),$(this).addClass("already").parent().addClass("barrage-link-hide")}}else window.location.href="https://passport.csdn.net/account/login?from="+encodeURIComponent(window.location.href)})}},{key:"insertAdCookie",value:function(){var n=e("ad_barrage_ids")||"",o=n?n.split(","):[];for(o=[].concat(_toConsumableArray(new Set(o.concat(this.barrageList.filter(function(e){return e.isAddCookie}).map(function(e){return e.barrageId})))));o.length>500;)o.shift();t("ad_barrage_ids",o.join(","),864e5)}},{key:"setTimer",value:function(){var e=this;clearTimeout(this.timer),this.timer=setTimeout(function(){e.queryBarrage()},1e3*this.pollDelay)}},{key:"resetErrorCount",value:function(){this.errorCount=0}},{key:"bindToolbarHeightChange",value:function(){var e=this;$(document).on("toolbarHeightChange",function(){e.getToolbarHeight();var t=$("#barrageBox");t.length&&t.css("top",e.toolbarHeight+32+"px")})}},{key:"getToolbarHeight",value:function(){var e=$("#csdn-toolbar");this.toolbarHeight=e.length?e.height():44}},{key:"handlePageVisibilityChange",value:function(){var e,t,n=this;void 0!==document.hidden?(e="hidden",t="visibilitychange"):void 0!==document.msHidden?(e="msHidden",t="msvisibilitychange"):void 0!==document.webkitHidden&&(e="webkitHidden",t="webkitvisibilitychange");var o=function(){document[e]?clearTimeout(n.timer):!$("#barrageBox").length&&n.setTimer()};document.addEventListener(t,o,!1)}},{key:"getTomorrowTimeRemaining",value:function(){var e=(new Date).getFullYear()+"/"+((new Date).getMonth()+1)+"/"+((new Date).getDate()+1);return new Date(e)-Date.now()}},{key:"follow",value:function(t){var n=this;e("UserName")?e("UserName")!==t&&$.ajax({url:"https://me.csdn.net/api/relation/create",type:"post",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},data:JSON.stringify({username:t}),dataType:"json",success:function(e){n.changeFollowStatus(t)},error:function(e){n.changeFollowStatus(t)}}):window.location.href="https://passport.csdn.net/account/login?from="+encodeURIComponent(window.location.href)}},{key:"changeFollowStatus",value:function(e){$("#barrageBox").find(".barrage-item").each(function(){$(this).find(".barrage-follow").attr("data-username")===e&&$(this).find(".barrage-follow").addClass("is-follow").text("已关注")})}},{key:"checkIsPassport",value:function(){return!!~window.location.href.indexOf("passport.csdn.net")}}]),n}();$(document).on("toolbarReady",function(){var e=window.csdn.toolbarData.barrageData.whiteList;e.length&&n(e)&&(new o).init()})}(),function(){function e(){return n("UserName")}function t(e){window.csdn&&window.csdn.loginBox&&window.csdn.loginBox.show?window.csdn.loginBox.show(e):window.location.href="https://passport.csdn.net/account/login"+(e?"?spm="+e.spm:"")}function n(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e&&"UD"==e)return decodeURIComponent(o[1]);if(o[0]==e)return decodeURI(o[1])}}var o=!1,r=function(){function e(){_classCallCheck(this,e),this.container=$(".toolbar-btn.toolbar-btn-collect"),this.data=[],this.history={},this.currentIndex=0,this.isHideOnce=!1,this.finishInit=!1,this.timer=null,this.init()}return _createClass(e,[{key:"init",value:function(){this.getCollectionFolder()}},{key:"getCollectionFolder",value:function(){var e=this;$.ajax({url:"https://g-api.csdn.net/community/toolbar-api/v2/favorites-list",type:"get",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){if(200===t.code){if(e.data=t.data.favoritesList,!e.data.length)return;e.data[0].contentList=t.data.contentList,e.render(),e.finishInit=!0}},error:function(e){void 0}})}},{key:"getCollectionContent",value:function(e){var t=this;$.ajax({url:"https://g-api.csdn.net/community/toolbar-api/v1/get-favorite-content?id="+e,type:"get",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){200===e.code&&(t.data[t.currentIndex].contentList=e.data,t.render())},error:function(e){void 0}})}},{key:"bindToggleFolder",value:function(){var e=this;$("#csdn-toolbar-collection").on("click",".toolbar-collection-left ul li",function(){$(this).index()!==e.currentIndex&&(e.currentIndex=$(this).index(),$(this).addClass("collection-folder-active").siblings().removeClass("collection-folder-active"),e.data[e.currentIndex].contentList?e.render():e.getCollectionContent(e.data[e.currentIndex].id))})}},{key:"bindToMore",value:function(){var e=this;$("#csdn-toolbar-collection").on("click",".toolbar-collection-more",function(){var t=0==e.currentIndex?"https://i.csdn.net/#/user-center/history":"https://i.csdn.net/#/user-center/collection-list?type=1&folder="+e.data[e.currentIndex].id+"&key="+(e.currentIndex-1);window.open(t,"_blank")})}},{key:"render",value:function(){if(this.finishInit)$("#csdn-toolbar-collection .toolbar-collection-right").empty().append(this.renderRight());else{var e='\n          <div id="csdn-toolbar-collection" class="csdn-toolbar-plugin">\n          '+(this.data.length<=1?"":'<div class="toolbar-collection-left csdn-toolbar-scroll-box">\n          <ul>'+this.renderLeft()+"</ul>\n        </div>")+'\n            \n            <div class="toolbar-collection-right">\n              '+this.renderRight()+'\n            </div>\n            <div class="csdn-toolbar-plugin-triangle"></div>\n          </div>\n        ';this.container.append(e),1==this.data.length&&$(".toolbar-collection-left").hide(),this.isHideOnce&&$("#csdn-toolbar-collection").hide(),this.bindToggleFolder(),this.bindToMore()}}},{key:"renderLeft",value:function(){for(var e="",t=0;t<this.data.length;t++){var n=this.data[t].name.replace(/</g,"&lt;").replace(/>/g,"&gt;");e+="\n          <li"+(t===this.currentIndex?' class="collection-folder-active"':"")+'>\n            <div class="toolbar-collection-folder-name">'+n+"</div>\n            "+(0==t?"":' <div class="toolbar-collection-folder-count">'+this.data[t].num+"</div>")+"\n          </li>\n        "}return e}},{key:"renderRight",value:function(){if(this.data[this.currentIndex].contentList.length){for(var e="",t=0;t<this.data[this.currentIndex].contentList.length;t++){var n=this.data[this.currentIndex].contentList[t];e+='\n          <li>\n            <a rel="nofollow" href="'+n.url+'" target="_blank">\n              <span '+("其他"===n.source?'class="toolbar-collection-type toolbar-collection-otherType"':'class="toolbar-collection-type"')+">"+n.source+'</span>\n              <span class="toolbar-collection-title">'+n.title+"</span>\n            </a>\n          </li>\n        "}return'<ul class="csdn-toolbar-scroll-box">'+e+"</ul>"+(this.data[this.currentIndex].num>15?'<a rel="nofollow" class="toolbar-collection-more">查看更多<i></i></a>':"")}return'\n          <div class="toolbar-collection-empty">\n            <div>空空如也</div>\n          </div>\n        '}},{key:"show",value:function(){clearTimeout(this.timer),this.timer=setTimeout(function(){o&&$("#csdn-toolbar-collection").stop().fadeIn(100)},150)}},{key:"hide",value:function(){this.isHideOnce=!0,clearTimeout(this.timer),o||$("#csdn-toolbar-collection").stop().fadeOut(100)}}]),e}(),a=function(){function e(){_classCallCheck(this,e),this.container=$(".toolbar-btn.toolbar-btn-collect"),this.init()}return _createClass(e,[{key:"init",value:function(){var e=$('<div id="csdn-toolbar-collection-nologin" class="csdn-toolbar-plugin">\n      <div class="csdn-toolbar-plugin-triangle"></div>\n      <div class="csdn-toolbar-collection-top">登录即可查看浏览历史和收藏</div>\n      <a rel="nofollow" class="csdn-toolbar-loginbtn" data-report-click=\'{"spm":"3001.8845"}\'>立即登录</a>\n</div>');this.container.append(e),e.find("a.csdn-toolbar-loginbtn").on("click",function(){var e=$(this).data("report-click");$("#csdn-toolbar-collection-nologin").hide(),e?t(e):t()})}},{key:"show",value:function(){clearTimeout(this.timer),this.timer=setTimeout(function(){o&&$("#csdn-toolbar-collection-nologin").stop().fadeIn(100)},150)}},{key:"hide",value:function(){this.isHideOnce=!0,clearTimeout(this.timer),o||$("#csdn-toolbar-collection-nologin").stop().fadeOut(100)}}]),e}();$(document).on("toolbarReady",function(){if(e()){var t=null,n=$(".toolbar-btn.toolbar-btn-collect");n.on("mouseenter",function(){o=!0,t?t.show():t=new r}),n.on("mouseleave",function(){o=!1,t.hide()})}else{void 0;var i=null,s=$(".toolbar-btn.toolbar-btn-collect");s.on("mouseenter",function(){o=!0,i?i.show():i=new a}),s.on("mouseleave",function(){o=!1,i.hide()})}})}(),function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e)return decodeURIComponent(o[1])}}function t(e,t,n){var o=new Date;if(-1==n)return void(document.cookie=e+"="+escape(t)+";domain=.csdn.net;path=/");if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=e+"="+escape(t)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}var n=(e("c_segment")&&parseInt(e("c_segment")),e("creative_btn_mp")?parseInt(e("creative_btn_mp")):0),o="",r=0,a=$('meta[name="toolbar"]'),i=0;if(a.length){var s=a.attr("content")||{};s=JSON.parse(s),i=s.type||i}else i=function(e){var t={};return location.href.replace(/([^*#&=?]+)=([^*#&=?]+)/g,function(){for(var e=arguments.length,n=Array(e),o=0;o<e;o++)n[o]=arguments[o];var r=n[1],a=n[2];t[r]=a}),e?t[e]:t}("toolbarSkinType")||i;r=i;var c=function(){function a(){_classCallCheck(this,a),this.container=$(".toolbar-btn.toolbar-btn-mp"),this.init()}return _createClass(a,[{key:"init",value:function(){this.render()}},{key:"render",value:function(){var n=e("UserName"),r=e("creativeSetApiNew");if(r=r?JSON.parse(r):null,r=r&&r.userName===n?r:null,n&&r){var a=0;0===r.articleNum?r.toolbarImg?(o.newuser={def:r.toolbarImg,dark:r.toolbarImg,left:3===r.type?"-130":"-148"},a=0===r.type?3:2):a=1:r.articleNum>=0&&r.toolbarImg&&(o.olduser={def:r.toolbarImg,dark:r.toolbarImg,left:3===r.type?"-130":"-148"}),this.creativeSet(a,o,r)}else{var i=this;n?$.ajax({url:"https://blog.csdn.net/phoenix/web/v1/get-novice-period-info",type:"get",xhrFields:{withCredentials:!0},success:function(e){var r=0;if(200===e.code&&e.data){0===e.data.articleNum?e.data.toolbarImg?(r=0===e.data.type?3:2,o.newuser={def:e.data.toolbarImg,dark:e.data.toolbarImg,left:3===r?"-130":"-148"}):r=1:e.data.toolbarImg&&(o.olduser={def:e.data.toolbarImg,dark:e.data.toolbarImg,left:3===r?"-130":"-148"});var a=e.data;a.userName=n,t("creativeSetApiNew",JSON.stringify(a),3e5)}i.creativeSet(r,o,a)}}):this.creativeSet(0,o,null)}}},{key:"creativeSet",value:function(e,o,a){if(null!==a&&(a.useSeven||a.oldUser)&&$(".toolbar-btn.toolbar-btn-write").addClass("is-traffic"),this.container.append('\n        <div class="csdn-toolbar-creative-mp" style="display:none">\n          <a href="https://mp.csdn.net/edit" data-report-query="spm=3001.9762" data-report-click=\'{"spm":"3001.9762","extra":'+JSON.stringify({dataType:e})+'}\'><img class="csdn-toolbar-creative-mp-bg" src="https://img-home.csdnimg.cn/images/20230815023727.png" alt=""></a> \n          <img class="csdn-toolbar-creative-mp-close" src="'+(1==r?"https://img-home.csdnimg.cn/images/20230815023232.png":"https://img-home.csdnimg.cn/images/20230815023238.png")+'" alt="">\n        </div>\n      '),window.csdn&&window.csdn.report&&window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.9762",extra:{dataType:e}}),o){var i="",s="",c="olduser";e>0&&(c="newuser"),i=1==r?o[c].dark:o[c].def,s=o[c].left+"px",$(".csdn-toolbar-creative-mp-bg").attr("src",i),$(".csdn-toolbar-creative-mp").css("left",s);var l="old";e>0&&n<3?($(".csdn-toolbar-creative-mp").show(),l="new",setTimeout(function(){$(".csdn-toolbar-creative-mp").hide()},5e3),window.csdn&&window.csdn.report&&window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.9743",extra:{dataType:"new"}})):n<3&&(n=parseInt(n)+1,t("creative_btn_mp",n,864e5),$(".csdn-toolbar-creative-mp").show(),setTimeout(function(){$(".csdn-toolbar-creative-mp").hide()},5e3),window.csdn&&window.csdn.report&&window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.9743",extra:{dataType:"old"}})),$(".csdn-toolbar-creative-mp-close").on("click",function(){$(".csdn-toolbar-creative-mp").hide(),window.csdn&&window.csdn.report&&window.csdn.report.reportView&&window.csdn.report.reportClick({spm:"3001.9744",extra:{dataType:l}}),t("creative_btn_mp",3,864e5)})}}},{key:"hide",value:function(){$("#csdn-toolbar-write").stop().fadeOut(100)}}]),a}();$(document).on("toolbarReady",function(){var e=null;o=window.csdn.toolbarData.toolbarBtnMpAsk||"",e||(e=new c)})}(),function(){function e(e,t){var n=document.createElement("script");n.type="text/javascript",n.readyState?n.onreadystatechange=function(){"loaded"!=n.readyState&&"complete"!=n.readyState||(n.onreadystatechange=null,t&&t())}:n.onload=function(){t&&t()},n.src=e,document.getElementsByTagName("head")[0].appendChild(n)}!function(){var t=window.location.href,n=["https://download.csdn.net/*","https://wenku.csdn.net/*","https://mall.csdn.net/*","https://qualcomm.csdn.net/*","https://vip.csdn.net/*","https://editor.csdn.net/*","https://mp.csdn.net/mp_blog/creation/editor","https://www.csdn.net/vip","https://www.csdn.net/apps/download"],o=/mobile/i.test(navigator.userAgent);n.some(function(e){return new RegExp("^"+e.replace(/\*/g,".*").replace(/\./g,"\\.")).test(t)
})||o||e("https://g.csdnimg.cn/common/csdn-cert-new/csdn-cert-new.js",function(){})}()}(),function(){function e(){return window.csdn.toolbarData.menus.filter(function(e){return e.slider&&e.slider.list&&e.slider.list.length>0})}var t=function(){function e(t){_classCallCheck(this,e),this.sliderArray=t,this.init()}return _createClass(e,[{key:"init",value:function(){this.initSliderScroll()}},{key:"initSliderScroll",value:function(){var e=this;this.sliderArray.forEach(function(t){$(".toolbar-subSlider-"+t.id).children().length&&e.sliderScroll(".toolbar-subSlider-"+t.id,$(".toolbar-subSlider-"+t.id).children().length,t.slider.time||4)})}},{key:"sliderScroll",value:function(e,t,n){var o=0,r=$(e);setInterval(function(){o===t-1?(o+=1,r.append(r.children().first().clone()),r.css({"margin-top":-48*o+"px",transition:"all 0.8s"})):o>=t?(o=0,r.css({"margin-top":"0px",transition:"none"}),r.children("a").last().remove()):(o+=1,r.css({"margin-top":-48*o+"px",transition:"all 0.8s"}))},1e3*n),r.on("transitionend",function(){o>=t&&(o=0,r.css({"margin-top":"0px",transition:"none"}),r.children("a").last().remove())})}}]),e}();$(document).on("toolbarReady",function(){var n=e();n.length&&new t(n)})}(),function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e&&"UD"==e)return decodeURIComponent(o[1]);if(o[0]==e)return decodeURI(o[1])}}function t(e,t,n){var o=new Date;if(-1==n)return void(document.cookie=e+"="+escape(t)+";domain=.csdn.net;path=/");if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=e+"="+escape(t)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function n(e){return e.some(function(e){return"*"===e||!!~location.href.indexOf(e)})}function o(e){window.csdn&&window.csdn.report&&window.csdn.report.reportClick(e)}function r(e){window.csdn&&window.csdn.report&&window.csdn.report.reportView(e)}function a(){this.closeTime=6,this.isLogin=e("UserName"),this.countDown="",this.radioValue="",this.reportUV="3001.6429",this.IPurl=i?"https://position.csdnimg.cn/oapi/get?ipAddr=***********":"https://position.csdnimg.cn/oapi/get",this.certUrl="https://i.csdn.net/#/user-center/profile?floor=edu&highSchool=true",this.closeValue=e("UserName")?"close":"nologin_close",this.isBlogDetail=window.location.href.indexOf("blog.csdn.net")>-1&&window.location.href.indexOf("/article/details")>-1,~location.href.indexOf("blog.csdn.net")&&!this.isBlogDetail||this.init()}var i=!1;a.prototype.init=function(){this.isHighSchoolIp()},a.prototype.render=function(){var e=this;this.$main=$('<div id="csdn-highschool-window" class="csdn-highschool-window '+(this.isBlogDetail?"csdn-highschool-blog-window":"")+'">\n      <img class="csdn-highschool-close" src="https://g.csdnimg.cn/common/csdn-toolbar/images/high-school-close.png"/>\n      <img class="csdn-highschool-monkey" src="https://g.csdnimg.cn/common/csdn-toolbar/images/high-school-monkey.png" />\n      <span class="csdn-highschool-countdown"></span>\n    </div>'),this.isLogin?this.hasCert||this.renderSelect():this.renderSelect(),this.$main.find(".csdn-highschool-close").on("click",function(){e.close(e.closeValue)}),$("body").append(this.$main),r({spm:this.reportUV})},a.prototype.renderSelect=function(){var e=this,t=$('<div class="csdn-highschool-select">\n      <p>认证学生身份,立享VIP折扣</p>\n      <div class="highschool-container">\n        <p>您是否为学生:</p>\n        <div class="highschool-select">\n          <label class="highschool-radio">\n            <input type="radio" name="type" id="highschool-radio-isStudent" value="cert" hidden/>\n            <label for="highschool-radio-isStudent" ></label>\n            <span class="radio-name">是,立即认证</span>\n          </label>\n          <label class="highschool-radio">\n            <input type="radio" name="type" id="highschool-radio-notStudent" value="notcert" hidden/>\n            <label for="highschool-radio-notStudent" ></label>\n            <span class="radio-name">否,立即关闭</span>\n          </label>\n        </div>\n      </div>\n      <div class="highschool-submit cannot-select">确定</div>\n    </div>');t.find(".highschool-radio").on("click",function(t){e.$main.find(".csdn-highschool-countdown").remove(),$(".highschool-submit").removeClass("cannot-select"),clearInterval(e.countDown),e.countDown=""}).on("click","input",function(t){e.radioValue=t.target.value}),t.find(".highschool-submit").on("click",function(){e.radioValue&&("cert"===e.radioValue?(window.open(e.certUrl,"_blank"),o({spm:e.reportUV,extend1:"是"})):o({spm:e.reportUV,extend1:"否"}),e.close(e.closeValue))}),this.$main.append(t)},a.prototype.renderAccount=function(){var e=this,t=$('<div class="csdn-highschool-account">\n      <p>高校社区扬帆起航 虚位以待静候卿来</p>\n      <div class="highschool-container">\n        <p class="highschool-name">'+this.schoolName+'</p>\n        <p class="highschool-desc">加入社区立即体验,参与校友互动</p>\n      </div>\n      <div class="highschool-submit">立即前往</div>\n    </div>');t.find(".highschool-submit").on("click",function(){window.open(e.schoolCommunityUrl,"_blank"),e.close(e.closeValue)}),this.$main.append(t)},a.prototype.isHighSchoolIp=function(){var e=this;$.ajax({url:this.IPurl,type:"get",contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){void 0,t&&(e.isLogin?e.getAccountInfo():(e.render(),e.count()))},error:function(e){void 0}})},a.prototype.getAccountInfo=function(){var e=this;$.ajax({url:"https://g-api.csdn.net/community/personal-api/v1/get-school-community",type:"get",contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){void 0,200===t.code&&t.data&&t.data.studentCertification?(e.hasCert=!0,e.reportUV="3001.6434",e.schoolName=t.data.schoolName,e.schoolCommunityUrl=t.data.schoolCommunityUrl):(e.render(),e.count())},error:function(t){e.render(),e.count(),void 0}})},a.prototype.close=function(e){this.$main.remove(),clearInterval(this.countDown),this.countDown="",t("csdn_highschool_close",e||"close",864e5)},a.prototype.count=function(){var e=this,t=this.closeTime;this.countDown=setInterval(function(){0===t?e.close(e.closeValue):(e.$main.find(".csdn-highschool-countdown").html(t+"秒"),t--)},1e3)},$(document).on("toolbarReady",function(t){var o=window.csdn.toolbarData||{},r=o.highSchoolData;r&&r.whiteList&&n(r.whiteList)&&(!e("csdn_highschool_close")||e("UserName")&&"nologin_close"===e("csdn_highschool_close"))&&(window.csdn.highSchool=new a)})}(),function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e&&"UD"==e)return decodeURIComponent(o[1]);if(o[0]==e)return decodeURI(o[1])}}try{if(document.referrer){var t=new URL(document.referrer);if(t&&["baidu.com","www.baidu.com","m.baidu.com"].indexOf(t.host)>-1&&"/"!==document.location.pathname){window._hmt=window._hmt||[],window._hmt.push(["_setAccount","ec8a58cd84a81850bcbd95ef89524721"]),window._hmt.push(["_setPageviewProperty",{uid:e("UserName")||e("UN")||"",cid:e("uuid_tt_dd")||""}]);var n=document.createElement("script");n.src="https://hm.baidu.com/hm.js?ec8a58cd84a81850bcbd95ef89524721";var o=document.getElementsByTagName("script")[0];o.parentNode.insertBefore(n,o)}}}catch(e){}}(),function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e)return decodeURIComponent(o[1])}}function t(e,t,n){var o=new Date;if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=e+"="+encodeURIComponent(t)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function n(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window;return e&&$(e).height()}function o(){return!!e("needShowLoginBoxAuto")}function r(){return e("UserName")}function a(){var e=!window.csdn||void 0===window.csdn.needShowLoginBoxAuto||!!window.csdn.needShowLoginBoxAuto;window.csdn&&window.csdn.loginBox&&e&&!o()&&!r()&&(setTimeout(function(){try{window.csdn.loginBox.show()}catch(e){void 0}},800),t("needShowLoginBoxAuto","1",60*c.hours*60*1e3))}function i(){$(window).on("scroll",function(e){var t=$(this).scrollTop(),o=n(document),r=n(window);void 0,t>=(o-r)*c.ratio&&a()})}function s(e){return e&&e.some(function(e){return"*"===e||!!~location.href.indexOf(e)})}var c={whiteList:["loc-toolbar.csdn.net"],blackList:[],ratio:.5,hours:6};try{window.csdn=window.csdn||{},csdn.loginBox=csdn.loginBox||{},csdn.loginBox.loginBoxParams={isClosedBtn:!0}}catch(e){void 0}$(document).on("toolbarReady",function(e){var t=window.csdn.toolbarData||{};if(c=Object.assign({},c,t.loginBoxData),void 0,c&&s(c.whiteList)&&!s(c.blackList)){var o=n(document),r=n(window);void 0,(o-r)*c.ratio<100?(void 0,a()):(void 0,i())}})}(),function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e&&"UD"==e)return decodeURIComponent(o[1]);if(o[0]==e)return decodeURI(o[1])}}function t(e){window.csdn&&window.csdn.report&&window.csdn.report.reportClick(e)}function n(){var e=new Date;return e.setDate(e.getDate()+1),e.setHours(0),e.setMinutes(0),e.setSeconds(0),e}function o(){var e=window.location.href;return s.some(function(t){return e.startsWith(t)})}function r(e){$.ajax({url:"https://kunpeng.csdn.net/ad/json/integrate/list?positions=932",type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},success:function(t){if(200===t.code&&t.data){if(t.data.length>0){var n=t.data[0];e(n)}}else;}})}function a(e){if(e&&e.file2){var o=$('<div class="csdn-common-logo-advert">\n      <img class="logo-advert-close" src="https://img-operation.csdnimg.cn/csdn/silkroad/img/1666168836091.png" />\n      <img class="logo-advert-back link-img" src='+e.file2+" />\n      "+(e.file1?'<div class="logo-advert-bubble"><img class="link-img" src='+e.file1+" /></div>":"")+"\n    </div>"),r=$(e.con).find("img.pre-img-lasy");r.attr("data-src",r.attr("data-src")+"&timestamp="+Date.now()),o.append(r),o.on("click",".link-img",function(){e.clickUrl&&window.open(e.clickUrl)}).on("click",".logo-advert-close",function(){e.spm&&t({spm:e.spm}),document.cookie="logo_advert_close="+escape(1)+";expires="+n()+";domain=.csdn.net;path=/",o.remove()}),i(o,[".csdn-side-toolbar",".so-fixed-menus"])}}function i(e,t){var n=0,o=setInterval(function(){n++,t.forEach(function(t){var o=$(t);o.length&&(n=15,setTimeout(function(){o.prepend(e),"live.csdn.net"===window.location.host&&$(o).attr("style","z-index: 20000"),setTimeout(function(){window.csdn.trackad.checkImgs()},100)},1e3))}),n>=15&&(clearInterval(o),o=null)},1e3)}var s=["https://download.csdn.net","https://mall.csdn.net/vip","https://www.csdn.net/vip","https://vip.csdn.net"];!function(){e("logo_advert_close")||o()||$(document).on("toolbarReady",function(e){r(a)})}()}(),function(){function e(e){return e&&e.el&&e.url}function t(e){return e=e.split("?")[0],e.indexOf(".csdn.net")>-1}function n(e){return/^#/g.test(e)}function o(o){e(o)?(void 0,$(o.el).on("click","a",function(e){var r=$(this).attr("href")||"",a=t(r)?r:o.url+(o.url.indexOf("?")>-1?"&":"?")+"target="+encodeURIComponent(r);e.preventDefault(),void 0,r&&!n(r)&&window.open(a,"_blank")})):void 0}window.csdn=window.csdn||{},window.csdn.middleJump=o}(),function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e&&"UD"==e)return decodeURIComponent(o[1]);if(o[0]==e)return decodeURI(o[1])}}function t(e,t,n){var o=new Date;if(-1==n)return void(document.cookie=e+"="+escape(t)+";domain=.csdn.net;path=/");if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=e+"="+escape(t)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function n(){return"https://profile-avatar.csdnimg.cn/default.jpg!3"}function o(e){var t={};return location.href.replace(/([^*#&=?]+)=([^*#&=?]+)/g,function(){for(var e=arguments.length,n=Array(e),o=0;o<e;o++)n[o]=arguments[o];var r=n[1],a=n[2];t[r]=a}),e?t[e]:t}function r(){return e("UserName")}function a(e){window.csdn&&window.csdn.loginBox&&window.csdn.loginBox.show?window.csdn.loginBox.show(e):window.location.href="https://passport.csdn.net/account/login"+(e?"?spm="+e.spm:"")}function i(){return!!+(e("p_uid")||"").substr(1,1)}function s(){return"--"}function c(){var e=0,t=$('meta[name="toolbar"]');if(t.length){var n=t.attr("content")||{};n=JSON.parse(n),e=n.type||e}else e=o("toolbarSkinType")||e;return e}function l(){this.data={},this.isVip=i(),this.isRender=!1,this.$box=$(".toolbar-btn.toolbar-btn-login"),this.nickName=s(),this.userName=e("UserName"),this.avatar=n(),this.mpUrl="mp.csdn.net";var t=window.csdn.toolbarData||null;t&&(this.mpUrl=t.mpUrl||"mp.csdn.net"),this.list=[{name:"我的主页",url:"https://blog.csdn.net/"+this.userName,report:{dest:"https://blog.csdn.net/"+this.userName,spm:"3001.10640"},icon:"csdn-profile-icon-bloguser",class:""},{name:"个人中心",url:"https://i.csdn.net/#/user-center/profile",report:{dest:"https://i.csdn.net/#/user-center/profile",spm:"3001.5111"},icon:"csdn-profile-icon-person",class:""},{name:"会员中心",url:"https://www.csdn.net/vip",report:{dest:"https://www.csdn.net/vip",spm:"3001.6256"},icon:"csdn-profile-icon-vipc",class:""},{name:"内容管理",url:"https://"+this.mpUrl+"/mp_blog/manage/article?spm=1011.2124.3001.5298",report:{dest:"https://"+this.mpUrl+"/mp_blog/manage/article",spm:"3001.5448"},icon:"csdn-profile-icon-pages",class:""},{name:"已购内容",url:"https://www.csdn.net/i/purchased",report:{dest:"https://www.csdn.net/i/purchased",spm:"3001.10565"},icon:"csdn-profile-icon-purchased",class:""},{name:"我的学习",url:"https://edu.csdn.net?utm_source=edu_txxl_mh",report:{dest:"https://edu.csdn.net?utm_source=edu_txxl_mh",spm:"3001.5350"},icon:"csdn-profile-icon-study",class:""},{name:"我的订单",url:"https://mall.csdn.net/myorder",report:{dest:"https://mall.csdn.net/myorder",spm:"3001.5137"},icon:"csdn-profile-icon-order",class:""},{name:"我的钱包",url:"https://i.csdn.net/#/wallet/index",report:{dest:"https://i.csdn.net/#/wallet/index",spm:"3001.5136"},icon:"csdn-profile-icon-wallet",class:""},{name:"我的云服务",url:"https://dev-portal.csdn.net/welcome?utm_source=toolbar_user_profile",report:{dest:"https://dev-portal.csdn.net/welcome?utm_source=toolbar_user_profile",spm:"3001.7345"},icon:"csdn-profile-icon-API",class:""},{name:"我的等级",url:"https://upload.csdn.net/level?utm_source=xz_pc_txxl",report:{dest:"https://upload.csdn.net/level?utm_source=xz_pc_txxl",spm:"3001.7346"},icon:"csdn-profile-icon-ac",class:"pb-8 csdn-border-bottom"},{name:"签到抽奖",url:"https://i.csdn.net/#/uc/reward",report:{dest:"https://i.csdn.net/#/uc/reward",spm:"3001.5351"},icon:"csdn-profile-icon-draw",class:"pt-8 pb-8 csdn-border-bottom"},{name:"退出",url:"javascript:;",report:{spm:"3001.5139"},icon:"csdn-profile-icon-logout",class:"pt-8 csdn-profile-logout"}],this.render()}function d(){this.cookieTime=864e5,this.cookieKey="hide_login",this.init=!!e(this.cookieKey),this.type=c(),this.isRender=!1,this.$box=$(".toolbar-btn.toolbar-btn-login"),void 0,this.list=[[{icon:"https://img-home.csdnimg.cn/images/20220208105133.png",text:"免费复制代码"},{icon:"https://img-home.csdnimg.cn/images/20220208105144.png",text:"关注/点赞/评论/收藏"},{icon:"https://img-home.csdnimg.cn/images/20220208105156.png",text:"下载海量资源"},{icon:"https://img-home.csdnimg.cn/images/20220208105204.png",text:"写文章/发动态/加入社区"}],[{icon:"https://img-home.csdnimg.cn/images/20220208020025.png",text:"免费复制代码"},{icon:"https://img-home.csdnimg.cn/images/20220208020036.png",text:"关注/点赞/评论/收藏"},{icon:"https://img-home.csdnimg.cn/images/20220208020055.png",text:"下载海量资源"},{icon:"https://img-home.csdnimg.cn/images/20220208020057.png",text:"写文章/发动态/加入社区"}]],!this.init&&this.render()}l.prototype.render=function(){var e=this,t=$('<div id="csdn-toolbar-profile" class="csdn-toolbar-plugin">\n            <div class="csdn-profile-top">\n              <a class="csdn-profile-avatar" data-report-click=\'{"spm": "3001.5343"}\' data-report-query="spm=3001.5343"  href="https://blog.csdn.net/'+e.userName+'"><img src="'+this.avatar+'"></a>\n              <p class="csdn-profile-nickName">'+e.nickName+'</p>\n              <a data-report-click=\'{"spm": "3001.5344"}\' data-report-query="spm=3001.5344" href="https://mall.csdn.net/vip" class="csdn-profile-no-vip"></a>\n            </div>\n            <div class="csdn-profile-mid">\n              <a data-report-click=\'{"spm": "3001.5347"}\' data-report-query="spm=3001.5347" href="https://blog.csdn.net/'+e.userName+'?type=sub&subType=fans"><i class="csdn-profile-fansCount">--</i>粉丝</a>\n              <a data-report-click=\'{"spm": "3001.5348"}\' data-report-query="spm=3001.5348" href="https://blog.csdn.net/'+e.userName+'?type=sub"><i class="csdn-profile-followCount">--</i>关注</a>\n              <a data-report-click=\'{"spm": "3001.5349"}\' data-report-query="spm=3001.5349" href="https://blog.csdn.net/'+e.userName+'"><i class="csdn-profile-likeCount">--</i>获赞</a>\n            </div>\n            <div class="csdn-profile-bottom">\n              <ul class="csdn-border-bottom">\n                '+e.list.map(function(e){return'<li class="'+e.class+'"><a href="'+e.url+'" '+(e.report?"data-report-click='"+JSON.stringify(e.report)+"'":"")+" "+(e.report?"data-report-query='spm="+e.report.spm+"'":"")+'><i class="csdn-profile-icon '+e.icon+'"></i>'+e.name+"</a></li>"}).join("")+"\n              </ul>\n            </div>\n          </div>");return this.$box.append(t),this.$tpl=t,this.$box.on("mouseenter",function(t){void 0,e.isEenter=!0,e.isRender&&e.showProfile()||e.getData()}).on("mouseleave",function(t){void 0,e.isEenter=!1,e.hideProfile()}),this.$tpl.find(".csdn-profile-logout").on("click",function(e){$.ajax({type:"post",url:"https://passport.csdn.net/account/logout",data:JSON.stringify({}),crossDomain:!0,xhrFields:{withCredentials:!0},success:function(e){var t={mod:"popu_789"},n="https://passport.csdn.net/account/logout?from="+encodeURIComponent(window.location.href);t.dest=n,t.extend1="退出",csdn&&csdn.report&&csdn.report.reportClick(t),window.location.reload()},error:function(e){}})}),this},l.prototype.update=function(e){if(e){var t=e.fansCount,n=e.likeCount,o=e.favoritesCount,r=e.nickName,a=e.followCount;this.avatar=e.avatar,this.toggleVip(e),this.isRender=!0,$(".toolbar-btn-login").find(".hasAvatar img").attr("src",e.avatar),$(".csdn-profile-fansCount").text(t||"--"),$(".csdn-profile-likeCount").text(n||"--"),$(".csdn-profile-favoritesCount").text(o||"--"),$(".csdn-profile-nickName").text(r||"--"),$(".csdn-profile-followCount").text(a||"--")}},l.prototype.toggleVip=function(e){this.isVip=1===e.vip,this.isVip&&$(".csdn-profile-no-vip").addClass("csdn-profile-vip").removeClass("csdn-profile-no-vip").attr("href","https://www.csdn.net/vip")},l.prototype.showProfile=function(){var e=this;this.timer&&clearTimeout(this.timer),$("#csdn-toolbar-profile").find(".csdn-profile-avatar > img").attr("src")!==$(".toolbar-btn-login").find(".hasAvatar img").attr("src")&&$("#csdn-toolbar-profile").find(".csdn-profile-avatar > img").attr("src",$(".toolbar-btn-login").find(".hasAvatar img").attr("src")),this.timer=setTimeout(function(){e.isEenter&&($(".toolbar-btn-login").find(".hasAvatar img").attr("src",e.avatar),void 0,$(".csdn-toolbar-plugin").hide(),e.$box.addClass("toolbar-btn-login-action"),e.$tpl.stop().fadeIn(200),window.csdn&&window.csdn.report&&window.csdn.report.reportView({spm:"3001.10565"}))},150)},l.prototype.hideProfile=function(){if(this.timer&&clearTimeout(this.timer),!this.isEenter){void 0;this.$box.removeClass("toolbar-btn-login-action"),this.$tpl.stop().fadeOut(100)}},l.prototype.getData=function(){void 0;var e=this;if(!e.isRender){var t=window.csdn.toolbar.profileData;if(t)return e.update(t),void e.showProfile();$.ajax({url:"https://g-api.csdn.net/community/toolbar-api/v1/get-user-info",type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},success:function(t){200===t.code&&(e.update(t.data),window.csdn.toolbar.profileData=t.data),e.showProfile()},error:function(e){}})}},d.prototype.render=function(){var e=this;!this.init&&t(this.cookieKey,"1",this.cookieTime);var n=this,o=$('<div id="csdn-toolbar-profile-nologin" class="csdn-toolbar-plugin">\n                <div class="csdn-toolbar-plugin-triangle"></div>\n                <div class="csdn-toolbar-profile-title">登录后您可以：</div>\n                <ul class="csdn-profile-top">\n                '+n.list[n.type].map(function(e){return'<li class="csdn-profile-a"><i class="csdn-profile-icon" style="background-image: url('+e.icon+'); "></i>'+e.text+"</li>"}).join("")+'\n                </ul>\n                <a class="csdn-toolbar-loginbtn" data-report-click=\'{"spm":"3001.8844"}\'>立即登录</a>\n          </div>');if(this.$box.append(o),this.$tpl=o,this.$box.find("a.csdn-toolbar-loginbtn").on("click",function(){var e=$(this).data("report-click");$(".csdn-toolbar-plugin").hide(),e?a(e):a()}),window.location.href.indexOf("passport.csdn.net")>-1)this.$tpl.hide(),n.isRender=!0;else var r=setTimeout(function(){n.isRender=!0,$(".csdn-toolbar-plugin").hide(),e.$tpl.stop().fadeOut(200),clearTimeout(r)},3e3);this.$box.on("mouseenter",function(e){void 0,n.isEenter=!0,n.isRender&&n.showProfile(),n.isRender=!0}).on("mouseleave",function(e){void 0,n.isEenter=!1,n.hideProfile()}),$(document).on("scroll",function(e){void 0,n.isEenter=!1,n.isRender=!0,n.hideProfile()})},d.prototype.showProfile=function(){var e=this;this.timer&&clearTimeout(this.timer),this.timer=setTimeout(function(){e.isEenter&&(void 0,$(".csdn-toolbar-plugin").hide(),e.$tpl.stop().fadeIn(200))},150)},d.prototype.hideProfile=function(){if(this.timer&&clearTimeout(this.timer),!this.isEenter){void 0;this.$tpl.stop().fadeOut(100)}},$(document).on("toolbarReady",function(e){void 0,setTimeout(function(){r()?new l:new d},200)})}(),function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e&&"UD"==e)return decodeURIComponent(o[1]);if(o[0]==e)return decodeURI(o[1])}}function t(e){return e.some(function(e){return"*"===e||!!~location.href.indexOf(e)})}function n(){this.el=null,this.host="https://mp-luckydraw.csdn.net/",this.dev=!1,this.type=["redPacket","coupon","common-coupon"],this.renderCss()}n.prototype.open=function(t){if(t){var n=this;$.ajax({url:n.host+"/luckydraw/api/timesWin",type:"post",data:JSON.stringify({lotteryId:t,username:e("UserName")}),contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){n.dev&&(e={code:200,data:{prizeId:"123",name:n.type[parseInt(99*Math.random())%3],url:"",money:(10*Math.random()).toFixed(2)}}),200===e.code&&e.data?n.clear().render(e.data):n.clear().render()}})}},n.prototype.render=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};void 0;var t=this,n="csdn-redpack-sorry";e.prizeId&&"common-coupon"===e.name?n="csdn-redpack-common-coupon":e.prizeId&&"coupon"===e.name?n="csdn-redpack-coupon":!e.prizeId||"redPacket"!==e.name&&"randomRedPacket"!==e.name||(n="csdn-redpack-cash"),t.el=$('<div id="csdn-redpack-barrage" class="csdn-redpack-barrage csdn-redpack-container">\n                  <div class="csdn-redpack-result '+n+'">\n                    <a class="csdn-redpack-result-link" href="'+(e.url?e.url:"javascript:;")+'" '+(e.url?'target="_blank"':"")+'></a>\n                    <em class="csdn-redpack-result-close"></em>\n                    '+("csdn-redpack-sorry"!==n?"<span>"+e.money+"</span>":"")+"\n                    "+("csdn-redpack-common-coupon"===n||"csdn-redpack-coupon"===n?"<i>"+e.money+"</i>":"")+"\n                  </div>\n              </div>"),t.el.find(".csdn-redpack-result-close").on("click",function(e){t.clear()}),t.el.find(".csdn-redpack-result-link").on("click",function(e){t.clear()}),$("body").append(t.el)},n.prototype.renderCss=function(){void 0;var e=$('<style type="text/css">\n                      #csdn-redpack-barrage {\n                        position: fixed;\n                        width: 100%;\n                        height: 100%;\n                        background: rgba(0, 0, 0, 0.74);\n                        overflow: hidden;\n                        top: 0;\n                        left: 0;\n                        z-index: 2147483647;\n                      }\n                      #csdn-redpack-barrage * {\n                        margin: 0;\n                        padding: 0;\n                        -webkit-box-sizing: border-box;\n                        box-sizing: border-box;\n                        -webkit-user-select: none;\n                        -moz-user-select: none;\n                        -ms-user-select: none;\n                        user-select: none;\n                      }\n                      #csdn-redpack-barrage a {\n                        text-decoration: none;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result {\n                        width: 950px;\n                        height: 720px;\n                        position: absolute;\n                        top: 50%;\n                        left: 50%;\n                        z-index: 999;\n                        -webkit-transform: translate(-50%, -45%);\n                        transform: translate(-50%, -45%);\n                        background-size: contain;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result .csdn-redpack-result-close {\n                        display: block;\n                        position: absolute;\n                        width: 50px;\n                        height: 50px;\n                        z-index: 99999;\n                        top: 18px;\n                        right: 300px;\n                        cursor: pointer;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result .csdn-redpack-result-link {\n                        display: block;\n                        position: absolute;\n                        width: 188px;\n                        height: 50px;\n                        z-index: 99999;\n                        left: 50%;\n                        bottom: 276px;\n                        cursor: pointer;\n                        margin-left: -84px;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-sorry {\n                        background: url("https://img-home.csdnimg.cn/images/20201110020632.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-cash {\n                        background: url("https://img-home.csdnimg.cn/images/20201110020622.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-cash span {\n                        position: absolute;\n                        display: block;\n                        text-align: center;\n                        min-width: 100px;\n                        font-size: 40px;\n                        font-weight: 900;\n                        height: 56px;\n                        line-height: 56px;\n                        font-family: Arial-Black, Arial;\n                        color: #FE1826;\n                        text-shadow: 0px 2px 12px rgba(234, 202, 105, 0.39);\n                        left: 50%;\n                        -webkit-transform: translate(-40%, 0);\n                        transform: translate(-40%, 0);\n                        top: 135px;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-cash .csdn-redpack-result-link {\n                        bottom: 316px;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-coupon {\n                        background: url("https://img-home.csdnimg.cn/images/20201110020628.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-common-coupon {\n                        background: url("https://img-home.csdnimg.cn/images/20201110020625.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-coupon span,\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-common-coupon span,\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-coupon i,\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-common-coupon i {\n                        min-width: 90px;\n                        font-size: 24px;\n                        font-weight: 900;\n                        font-family: Arial-Black, Arial;\n                        color: #FE1826;\n                        text-shadow: 0px 2px 12px rgba(234, 202, 105, 0.39);\n                        position: absolute;\n                        top: 318px;\n                        left: 346px;\n                        text-align: center;\n                        line-height: 34px;\n                        height: 34px;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-coupon i,\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-common-coupon i {\n                        top: 303px;\n                        left: 516px;\n                        text-align: center;\n                        font-style: normal;\n                      }\n                  </style>');document.head.insertBefore($(e)[0],document.head.getElementsByTagName("title")[0])},n.prototype.clear=function(){return void 0,this.el=null,this.dev=!1,$(".csdn-redpack-barrage").remove(),this},n.prototype.test=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:243,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.dev=t,this.host="https://test-luckydraw.csdn.net/",this.open(e)},$(document).on("toolbarReady",function(e){var o=window.csdn.toolbarData||{},r=o.barrageRedpackData;void 0,r&&r.whiteList&&t(r.whiteList)&&(void 0,window.csdn.barrageRedpack=new n)})}(),function(){function e(e){
var t="width:100%; height:100%; background-image: url("+e.imgUrl+"); background-size: auto 80px;background-repeat: no-repeat; background-position: center center;",n=$('<div class="toolbar-redpack-advert">\n            <a href="'+e.clickUrl+'" style="background: '+e.backgroundColor+';" class="toolbar-redpack-advert-default"><div style="'+t+'"></div></a>\n            <span class="toolbar-redpack-adver-btn" '+(e.closeSpm?'data-report-click=\'{"spm":'+JSON.stringify(e.closeSpm)+"}'":"")+" ></span>\n            "+(e.exposureUrl?'<img style="width:0;height:0;display:none;" src="'+e.exposureUrl+'"/>':"")+"\n          </div>");n.find(".toolbar-redpack-adver-btn").click(function(e){n.remove()}),n.find(".toolbar-redpack-advert-default").click(function(t){e.clickCallback&&"function"==typeof e.clickCallback&&e.clickCallback()}),$("#csdn-toolbar").prepend(n)}function t(){$("body").find("#csdn-toolbar .toolbar-redpack-advert").remove()}var n={clickUrl:"javascript:void(0);",imgUrl:"https://g.csdnimg.cn/common/redpack/images/redpaceAdvert.png",backgroundColor:"#FFCEA6"};window.csdn=window.csdn||{},window.csdn.bannerAdvert={show:function(o){t(),e(Object.assign({},n,o))},close:function(){t()}}}(),function(){function e(e){var t=document.createElement("link");t.rel="stylesheet",t.type="text/css",t.href=e,document.getElementsByTagName("head")[0].appendChild(t)}function t(e,t){var n=document.createElement("script");n.type="text/javascript",n.readyState?n.onreadystatechange=function(){"loaded"!=n.readyState&&"complete"!=n.readyState||(n.onreadystatechange=null,t&&t())}:n.onload=function(){t&&t()},n.src=e,document.getElementsByTagName("head")[0].appendChild(n)}function n(){e("https://g.csdnimg.cn/common/redpack/redpack.css"),t("https://g.csdnimg.cn/common/redpack/redpack.js")}function o(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return!!Array.isArray(e)&&e.some(function(e){return"*"===e||e&&location.href.includes(e)})}$(document).on("toolbarReady",function(e){var t=window.csdn.toolbarData||{},r=t.redpackData;void 0,r&&r.whiteList&&o(r.whiteList)&&(void 0,n())})}(),function(){function e(e){return new Promise(function(t){var n=document.createElement("script");n.src=e,(document.body||document.head).append(n),n.onload=n.onreadystatechange=t})}function t(){return r?Promise.resolve():new Promise(function(t){return a?void a.then(t):window.SidebarDrawer?void(r=!0):(a=e(o),void a.then(function(){i=new CsdnSidebarDrawer({zIndex:2100,defaultActive:"",lockScroll:!1,onMenuChange:function(e){window.csdn.toolbar&&window.csdn.toolbar.onSideMenuChange&&window.csdn.toolbar.onSideMenuChange(e)},beforeSelectMenu:function(e){if(window.csdn.toolbar&&window.csdn.toolbar.beforeSideMenuItemClick)return window.csdn.toolbar.beforeSideMenuItemClick(e)}}),r=!0,t()}))})}function n(){i?i.toggleVisable():t().then(function(){i.toggleVisable()})}var o="https://g.csdnimg.cn/lib/csdn-sidebar/drawer/1.0.0/drawer.js",r=!1,a=null,i=null,s=$('meta[name="toolbar"]'),c=void 0;if(s.length){var l=s.attr("content")||{};l=JSON.parse(l),c=l.model||c}"mini"!==c&&"sidebar"!==c||t(),$(document).on("toolbarReady",function(e){var t="";switch(c){case"mini":t=".toolbar-mini-meun-logo";break;case"sidebar":case"sidebar-www":t=".toolbar-mini-meuns"}t&&$("#csdn-toolbar .csdn-toolbar-mini "+t).click(function(e){if(window.csdn.report&&window.csdn.report.reportClick({spm:"3001.10427"}),window.csdn.toolbar&&window.csdn.toolbar.beforeMiniMenuClick){var t=window.csdn.toolbar.beforeMiniMenuClick();if("boolean"==typeof t&&!t)return}n()})})}(),function(){function e(e){var t={};return location.href.replace(/([^*#&=?]+)=([^*#&=?]+)/g,function(){for(var e=arguments.length,n=Array(e),o=0;o<e;o++)n[o]=arguments[o];var r=n[1],a=n[2];t[r]=a}),e?t[e]:t}function t(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e&&"UD"==e)return decodeURIComponent(o[1]);if(o[0]==e)return decodeURI(o[1])}}function n(){return t("UserName")}function o(e){var t="https://mall.csdn.net/mp/mallorder/ab/getExperiment?testId="+e;return new Promise(function(e,n){$.ajax({url:t,type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},success:function(t){e(200===t.code?t.data.treatment:"control")},error:function(t){e("control")}})})}function r(){this.isVip=!1,this.timer=null,this.box=null,this.isRender=!1,this.bindEvent()}var a=0;!function(){var t=$('meta[name="toolbar"]'),n=0;if(t.length){var o=t.attr("content")||{};o=JSON.parse(o),n=o.type||n}else n=e("toolbarSkinType")||n;a=n}(),r.prototype.getProfileData=function(e){var t=this;$.ajax({url:"https://g-api.csdn.net/community/toolbar-api/v1/get-user-info",type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},success:function(n){200===n.code&&(e&&e.call(t,n.data),window.csdn.toolbar.profileData=n.data)},error:function(e){}})},r.prototype.renderVip=function(e){void 0,void 0;var t=window.csdn.toolbarData.vipItemPath||{normal:[{icon:"https://img-home.csdnimg.cn/images/20210826043933.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytbcj#banner",title:"限时抽奖"},{icon:"https://img-home.csdnimg.cn/images/20210826043936.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytblq#discount_center",title:"领券中心"},{icon:"https://img-home.csdnimg.cn/images/20210826043937.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytbhyg#vip_shop",title:"会员购"},{icon:"https://img-home.csdnimg.cn/images/20210826043940.png",url:"https://www.csdn.net/vip?utm_source=vip_hyzx_hytb",title:"更多特权"}],dark:[{icon:"https://img-home.csdnimg.cn/images/20210826043735.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytbcj#banner",title:"限时抽奖"},{icon:"https://img-home.csdnimg.cn/images/20210826043738.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytblq#discount_center",title:"领券中心"},{icon:"https://img-home.csdnimg.cn/images/20210826043740.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytbhyg#vip_shop",title:"会员购"},{icon:"https://img-home.csdnimg.cn/images/20210826043742.png",url:"https://www.csdn.net/vip?utm_source=vip_hyzx_hytb",title:"更多特权"}]},n=this,r=1===e.vip,i="https://mall.csdn.net/vip",s=1===e.vip?"3001.6441":"3001.6439",c=1===e.vip?"3001.6442":"3001.6440",l=void 0;1!==a?t.normal&&(l=t.normal.map(function(e){return'<a rel="nofollow" href="'+e.url+'"><i class="csdn-plugin-vip-icon" style="background:url('+e.icon+');background-size: contain;"></i><div class="csdn-plugin-vip-rights">'+e.title+"</div></a>"}).join("")):t.dark&&(l=t.dark.map(function(e){return'<a rel="nofollow" href="'+e.url+'"><i class="csdn-plugin-vip-icon" style="background:url('+e.icon+');background-size: contain;"></i><div class="csdn-plugin-vip-rights">'+e.title+"</div></a>"}).join(""));var d=window.csdn.toolbarData.vipBg||{darkBg:"https://img-home.csdnimg.cn/images/20210826055052.png",defaultBg:"https://img-home.csdnimg.cn/images/20210826055049.png"},p=1==a?d.darkBg:d.defaultBg,u="https://g.csdnimg.cn/common/csdn-toolbar/images",h=$('<div id="csdn-plugin-vip-v2" style=\'background-size: cover;\'}>\n        <div class="csdn-plugin-vip-header">\n          <span class="csdn-plugin-vip-header-text">会员特权<span>\n        </div>\n        <div class="csdn-plugin-vip-body">\n          <div style="flex: 1;min-width:0;">'+l+'</div>\n          <div class="csdn-plugin-vip-QRCode" style="">\n            <img src="'+u+'/vip-purchase-page-qrcode.png" class="qr-code-paying" />\n            <div class="paying-icon-list">\n              <img src="'+u+"/scan"+(1==a?"":"-dark")+'.svg" style="width:20px;height:20px" />\n              <img src="'+u+'/wx-pay.svg" style="width:20px;height:20px" />\n              <img src="'+u+'/ali-pay.svg" style="width:20px;height:20px" />\n              <img src="'+u+'/jd-pay.svg" style="width:20px;height:20px" />\n            </div>\n          </div>\n        </div>\n        <div class="csdn-plugin-vip-footer">\n          <a rel="nofollow" data-report-click=\'{"spm": "'+c+'"}\' data-report-query="spm='+c+'" class="csdn-plugin-vip-footer-link" href="'+i+'">\n            领取限时优惠券，低至 <span style="color:#DB8152;">0.5元/天</span><i></i>\n          </a>\n          <a rel="nofollow" data-report-click=\'{"spm": "'+s+'"}\' data-report-query="spm='+s+'" class="csdn-plugin-vip-footer-btn" href="'+i+'">\n            '+(r?"立即续费":"立即开通")+"\n          </a>\n        </div>\n      </div>"),g=$('<div id="csdn-plugin-vip" style=\'background:url('+p+') no-repeat center center; background-size: cover;\'}>\n                        <div class="csdn-plugin-vip-header">\n                            会员特权\n                        </div>\n                        <div class="csdn-plugin-vip-body">\n                            '+l+'\n                        </div>\n                        <div class="csdn-plugin-vip-footer">                \n                            <a rel="nofollow" data-report-click=\'{"spm": "'+c+'"}\' data-report-query="spm='+c+'" class="csdn-plugin-vip-footer-link" href="'+i+'">\n                              领取限时优惠券，低至 <span style="color:#DB8152;">0.5元/天</span><i></i>\n                            </a>\n                            <a rel="nofollow" data-report-click=\'{"spm": "'+s+'"}\' data-report-query="spm='+s+'" class="csdn-plugin-vip-footer-btn" href="'+i+'">\n                              '+(r?"立即续费":"立即开通")+"\n                            </a>\n                        </div>\n                    </div>"),m=$(".toolbar-btn-vip").find(">a"),f=g;o(348).then(function(e){"exp"===e&&(f=h)}).catch(function(e){}).finally(function(){$(".toolbar-btn-vip").append(f),n.box=f,n.showVip()}),r&&(m.attr("href","https://www.csdn.net/vip"),m.attr("data-report-click",'{"spm": "3001.5399"}'),m.attr("data-report-query","spm=3001.5399")),n.isRender=!0},r.prototype.showVip=function(){var e=this;clearTimeout(e.timer),e.timer=setTimeout(function(){if(e.isEnter){e.box&&e.box.stop().fadeIn(100);var t=window.csdn.toolbar&&window.csdn.toolbar.profileData||{vip:0},n=1===t.vip?"3001.6441":"3001.6439";window.csdn&&window.csdn.report&&window.csdn.report.reportView&&window.csdn.report.reportView({spm:n})}},150)},r.prototype.hideVip=function(){var e=this;clearTimeout(e.timer),e.isEnter||e.box&&e.box.stop().fadeOut(100)},r.prototype.init=function(){var e=window.csdn.toolbar&&window.csdn.toolbar.profileData;if(e)this.renderVip(e);else{n()?this.getProfileData(this.renderVip):(this.renderVip(this,{vip:0}),window.csdn.toolbar.profileData={vip:0})}},r.prototype.bindEvent=function(){var e=this;$(".toolbar-btn-vip").on("mouseenter",function(t){e.isEnter=!0,e.isRender?e.showVip():e.init()}).on("mouseleave",function(t){e.isEnter=!1,e.hideVip()})},$(document).on("toolbarReady",function(e){void 0,setTimeout(function(){new r},200)})}(),function(){function e(e,t){var n=document.createElement("script");n.type="text/javascript",n.readyState?n.onreadystatechange=function(){"loaded"!=n.readyState&&"complete"!=n.readyState||(n.onreadystatechange=null,t&&t())}:n.onload=function(){t&&t()},n.src=e,document.getElementsByTagName("head")[0].appendChild(n)}function t(){e("https://g.csdnimg.cn/common/vip-buyside/vip-buyside.js")}function n(e){return e.some(function(e){return"*"===e||!!~location.href.indexOf(e)})}$(document).on("toolbarReady",function(e){var o=window.csdn.toolbarData||{},r=o.vipBuySideDate;void 0,r&&r.whiteList&&n(r.whiteList)&&(void 0,t())})}(),function(){function e(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(o[0]==e)return decodeURIComponent(o[1])}}function t(e,t,n){var o=new Date;if(-1==n)return void(document.cookie=e+"="+escape(t)+";domain=.csdn.net;path=/");if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=e+"="+escape(t)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}var n=!1,o=(e("c_segment")&&parseInt(e("c_segment")),function(){function e(){_classCallCheck(this,e),this.container=$(".toolbar-btn.toolbar-btn-write"),this.activityList=[],this.spmextra="control1",this.mpUrl="mp.csdn.net";var t=window.csdn.toolbarData||null;t&&(this.mpUrl=t.mpUrl||"mp.csdn.net"),"passport.csdn.net"!==window.location.host&&this.init()}return _createClass(e,[{key:"init",value:function(){this.render(),this.renderWriteCenter(),this.show()}},{key:"renderWriteCenter",value:function(){$("#csdn-toolbar-write").find(".csdn-toolbar-plugin-container").append('<a class="write-center-box" href="https://'+this.mpUrl+'/"\n          data-report-click=\'{"spm":"3001.10461","dest":"https://'+this.mpUrl+'/"}\' \n          data-report-query="spm=3001.10461"><i class="icon-creationcenter"></i><span>进入创作者中心</span><i class="icon-arrowright"></i>\n        </a>')}},{key:"render",value:function(e){this.container.append('\n        <div id="csdn-toolbar-write" class="csdn-toolbar-plugin" data-report-view=\'{"spm":"3001.9643","extra":'+JSON.stringify({dataType:this.spmextra})+'}\'>\n          <div class="csdn-toolbar-plugin-container">\n            <ul class="csdn-toolbar-write-box">\n              <li class="csdn-toolbar-write-box-blog">\n                <a rel="nofollow" href="https://'+this.mpUrl+'/edit" target="_blank" data-report-click=\'{"spm":"3001.5352","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-view=\'{"spm":"3001.5352","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.5352">\n                  <i class="csdn-toolbar-write-icon"></i>\n                  <span>写文章</span>\n                </a>\n              </li>\n              <li class="csdn-toolbar-write-box-inscode">\n                <a rel="nofollow" href="https://inscode.csdn.net/?utm_source=109355915" target="_blank" data-report-click=\'{"spm":"3001.9241","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.9241">\n                  <i class="csdn-toolbar-write-icon"></i>\n                  <span>写代码</span>\n                </a>\n              </li>\n              <li class="csdn-toolbar-write-box-blink">\n                <a rel="nofollow" href="https://blink.csdn.net" target="_blank" data-report-click=\'{"spm":"3001.5353","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.5353">\n                  <i class="csdn-toolbar-write-icon"></i>\n                  <span>发动态</span>\n                </a>\n              </li>\n              <li class="csdn-toolbar-write-box-ask">\n                <a rel="nofollow" href="https://ask.csdn.net/new?utm_source=p_toolbar" target="_blank" data-report-click=\'{"spm":"3001.5354","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.5354">\n                  <i class="csdn-toolbar-write-icon"></i>\n                  <span>提问题</span>\n                </a>\n              </li>\n              <li class="csdn-toolbar-write-box-upload">\n                <a rel="nofollow" href="https://'+this.mpUrl+'/mp_download/creation/uploadResources" target="_blank" data-report-click=\'{"spm":"3001.5355","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.5355">\n                  <i class="csdn-toolbar-write-icon"></i>\n                  <span>传资源</span>\n                </a>\n              </li>\n              <li class="csdn-toolbar-write-box-code">\n                <a rel="nofollow" href="https://gitcode.com/create?utm_source=csdn_tool&isLogin=1" target="_blank" data-report-click=\'{"spm":"3001.5356","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.5356">\n                  <i class="csdn-toolbar-write-icon"></i>\n                  <span>建项目</span>\n                </a>\n              </li>\n            </ul>\n          </div>\n        </div>\n      ')}},{key:"isEffectiveTime",value:function(e){if(!e)return!1;if(e.always)return!0;var t=(new Date).valueOf(),n=new Date(e.start).valueOf();return t<=new Date(e.end).valueOf()&&t>=n}},{key:"show",value:function(){clearTimeout(this.timer),clearTimeout(this.closeTimer),this.timer=setTimeout(function(){n&&(window.csdn&&window.csdn.report&&window.csdn.report.reportView({spm:"3001.4503",extra:{dataType:"",isPopover:"1"}}),$("#csdn-toolbar-write").stop().fadeIn(100),$(".toolbar-btn.toolbar-btn-write").addClass("toolbar-btn-write_is-focus"))},150)}},{key:"hide",value:function(){clearTimeout(this.timer),clearTimeout(this.closeTimer),this.closeTimer=setTimeout(function(){n||($("#csdn-toolbar-write").stop().fadeOut(100),$(".toolbar-btn.toolbar-btn-write").removeClass("toolbar-btn-write_is-focus"))},300)}}]),e}()),r=e("UserName")||"";$(document).on("toolbarReady",function(){var e=null,a=$(".toolbar-btn.toolbar-btn-write");a.on("mouseenter",function(){n=!0,$(".toolbar-btn-write-new-comment").length>0&&($(".toolbar-btn-write-new-comment").data("time")&&t(r+"comment_new",$(".toolbar-btn-write-new-comment").data("time"),2592e6),$(".toolbar-btn-write-new-comment").remove()),e?e.show():e=new o,window.csdn&&window.csdn.report&&window.csdn.report.viewCheck&&window.csdn.report.viewCheck()}),a.on("mouseleave",function(){n=!1,e.hide()})})}();