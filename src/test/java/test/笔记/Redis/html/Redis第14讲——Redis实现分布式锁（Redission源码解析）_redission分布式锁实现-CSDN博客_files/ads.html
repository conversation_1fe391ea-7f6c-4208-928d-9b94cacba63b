<!DOCTYPE html>
<!-- saved from url=(1771)https://googleads.g.doubleclick.net/pagead/ads?client=ca-pub-1076724771190722&output=html&h=600&slotname=4787882818&adk=944469691&adf=1401350160&pi=t.ma~as.4787882818&w=300&abgtt=6&fwrn=4&fwrnh=100&lmt=1743258566&rafmt=1&format=300x600&url=https%3A%2F%2Fblog.csdn.net%2Fweixin_45433817%2Farticle%2Fdetails%2F138043455&fwr=0&fwrattr=true&rpe=1&resp_fmts=4&wgl=1&uach=********************************************************************************************************************************************************************************************************************&dt=1743258566088&bpp=4&bdt=1354&idt=462&shv=r20250327&mjsv=m202503250101&ptt=9&saldr=aa&abxe=1&cookie=ID%3Df0dde626079003d6%3AT%3D1741286283%3ART%3D1743258383%3AS%3DALNI_MZuvdz-Rr4oaINTs1sYdqEKK9Lusg&gpic=UID%3D00001057a6020297%3AT%3D1741286283%3ART%3D1743258383%3AS%3DALNI_MYLjzy157VqG8I5BXX2ajRDcMVMpA&eo_id_str=ID%3Dc5da87790f8b0361%3AT%3D1741286283%3ART%3D1743258383%3AS%3DAA-AfjZFqiO7ONQ6lXzzkSO-v4ym&prev_fmts=0x0&nras=1&correlator=8631188229818&frm=20&pv=1&u_tz=480&u_his=1&u_h=1152&u_w=2048&u_ah=1112&u_aw=2048&u_cd=24&u_sd=1.25&dmc=8&adx=203&ady=1828&biw=2033&bih=991&scr_x=0&scr_y=100&eid=95344787%2C95354565%2C95356500%2C95356504%2C31090358%2C95355301%2C95355965%2C95356928&oid=2&pvsid=1799801240911552&tmod=1028903326&uas=0&nvt=1&ref=https%3A%2F%2Fwww.baidu.com%2Flink%3Furl%3D-qL3IANpTt93EYjJagDsTSTnov5atGva3RcWAWF6JiIS8DA4uxmTFwQx0b-tPOesWphE0mwfuiOkP0hEhyehTGrnGirr3Ypx6E2-RIc-LjC%26wd%3D%26eqid%3Db0b094d80384f38e0000000667e80308&fc=1920&brdim=0%2C0%2C0%2C0%2C2048%2C0%2C2048%2C1112%2C2048%2C991&vis=1&rsz=%7C%7CpeEbr%7C&abl=CS&pfx=0&fu=128&bc=31&bz=1&td=1&tdf=2&psd=W251bGwsW251bGwsbnVsbCxudWxsLCJkZXByZWNhdGVkX2thbm9uIl0sbnVsbCwzXQ..&nt=1&ifi=2&uci=a!2&btvi=1&fsb=1&dtd=478 -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><script>var jscVersion = 'r20250327';</script><script>var google_casm=[];</script><style>a { color: #000000 }.img_ad:hover {-webkit-filter: brightness(120%)}</style><script></script><script>window.dicnf = {imprtype: 2,};</script><script data-jc="40" data-jc-version="r20250327" data-jc-flags="[&quot;x%278446&#39;9efotm(&amp;20067;&gt;8&amp;&gt;`dopb/%&lt;1732261!=|vqc)!7201061?&#39;9efotm(&amp;20723;&gt;:&amp;&gt;`dopb/%&lt;1245;05!=nehu`/!361:&lt;320!9sqrm(&amp;2057?61&lt;&amp;&gt;`dopb~&quot;]">(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var q=this||self;function aa(a){q.setTimeout(()=>{throw a;},0)};function ba(a){ba[" "](a);return a}ba[" "]=function(){};var ca={},t=null;function da(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};let ea=void 0;function fa(a,b){if(a!=null){var c=ea??(ea={});var d=c[a]||0;d>=b||(c[a]=d+1,a=Error(),da(a,"incident"),aa(a))}};function u(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var ha=u(),ia=u("m_m",!0),ja=u();const v=u("jas",!0);const ka=typeof ia==="symbol";var la={};function ma(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object};const na=BigInt(Number.MIN_SAFE_INTEGER),oa=BigInt(Number.MAX_SAFE_INTEGER);const pa=Number.isFinite;function ra(a){if(typeof a!=="boolean"){var b=typeof a;throw Error(`Expected boolean but got ${b!="object"?b:a?Array.isArray(a)?"array":b:"null"}: ${a}`);}return a};function sa(a){return a};function ta(a,b,c){var d=ua;c=c?!!(b&32):void 0;const f=[];var e=a.length;let g,k,l,m=!1;b&64?(b&256?(e--,g=a[e],k=e):(k=**********,g=void 0),b&512||(m=!0,l=(va??sa)(g?k- -1:b>>15&1023||536870912,-1,a,g),k=l+-1)):(k=**********,b&1||(g=e&&a[e-1],ma(g)?(e--,k=e,l=0):g=void 0));b=void 0;for(let h=0;h<e;h++){let n=a[h];n!=null&&(n=d(n,c))!=null&&(h>=k?(b??(b={}))[h- -1]=n:f[h]=n)}if(g)for(let h in g)a=g[h],a!=null&&(a=d(a,c))!=null&&(e=+h,e<l?f[e+-1]=a:(b??(b={}))[h]=a);b&&(m?f.push(b):f[k]=b);return f} function ua(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=na&&a<=oa?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[v]|0;return a.length===0&&b&1?void 0:ta(a,b,!1)}b=a[ia];const c=b===la;ka&&b&&!c&&fa(ja,3);if(c)return x(a);return}return a}let va;function x(a){a=a.o;return ta(a,a[v]|0)};function wa(){fa(ha,5)};function y(a,b,c){const d=a.o;let f=d[v]|0;if(f&2)throw Error();xa(d,f,b,c);return a}function xa(a,b,c,d){const f=b&512?0:-1,e=c+f;var g=a.length-1;e>=g&&b&256?a[g][c]=d:e<=g?a[e]=d:d!==void 0&&(g=b>>15&1023||536870912,c>=g?d!=null&&(a[g+f]={[c]:d},a[v]=b|256):a[e]=d)}function A(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return y(a,b,c)};var E=class{constructor(a){a:{if(a==null){var b=96;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[v]|0;8192&b||!(64&b)||2&b||wa();if(b&1024)throw Error("farr");if(b&64){b&16384||(a[v]=b|16384);var c=a;break a}var d=a;b|=64;var f=d.length;if(f){var e=f-1;f=d[e];if(ma(f)){b|=256;const g=b&512?0:-1;e-=g;if(e>=1024)throw Error("pvtlmt");for(c in f){const k=+c;if(k<e)d[k+g]=f[c],delete f[c];else break}b=b&-33521665|(e&1023)<<15}}}a[v]=b|16384;c=a}this.o=c}toJSON(){return x(this)}}; E.prototype[ia]=la;E.prototype.toString=function(){return this.o.toString()};var ya=class extends E{};var F=class{constructor(a,b=!1){this.key=a;this.defaultValue=b;this.valueType="boolean"}};var za=new F("100000"),Aa=new F("45368259"),Ba=new F("45357156",!0),Ca=new F("45350890"),Da=new F("45628745",!0),Ea=new F("45414892"),Fa=new F("45620832");const Ga=RegExp("ad\\.doubleclick\\.net/(ddm/trackimp|pcs/view)");var G=(a,b)=>a.substring(a.length-7)=="&adurl="?a.substring(0,a.length-7)+b+"&adurl=":a+b;function H(a=window){return a};let I=q.dicnf||{};var J=()=>I.imprtype=="1"?1:I.imprtype=="2"?2:0;function Ha(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}};function K(a,b,c){a.addEventListener&&a.addEventListener(b,c,!1)}function Ia(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)};var Ja=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function Ka(a,b,c,d){const f=c.length;for(;(b=a.indexOf(c,b))>=0&&b<d;){var e=a.charCodeAt(b-1);if(e==38||e==63)if(e=a.charCodeAt(b+f),!e||e==61||e==38||e==35)return b;b+=f+1}return-1}var La=/#|$/,Ma=/[?&]($|#)/;function Na(a){let b=q,c=0;for(;b&&c++<40&&!a(b);)a:{try{const d=b.parent;if(d&&d!=b){b=d;break a}}catch{}b=null}}function Oa(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function Pa(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}let L=[];const Ua=()=>{const a=L;L=[];for(const b of a)try{b()}catch{}}; var Va=(a,b)=>{a.readyState==="complete"||a.readyState==="interactive"?(L.push(b),L.length==1&&(window.Promise?Promise.resolve().then(Ua):window.setImmediate?setImmediate(Ua):setTimeout(Ua,0))):a.addEventListener("DOMContentLoaded",b)};function Wa(a,b=document){return b.createElement(String(a).toLowerCase())};function N(a,b,c=null,d=!1,f=!1){Xa(a,b,c,!1,d,f)}function Xa(a,b,c,d,f,e=!1){a.google_image_requests||(a.google_image_requests=[]);const g=Wa("IMG",a.document);if(c||f){const k=l=>{c&&c(l);if(f){l=a.google_image_requests;const m=Array.prototype.indexOf.call(l,g,void 0);m>=0&&Array.prototype.splice.call(l,m,1)}Ia(g,"load",k);Ia(g,"error",k)};K(g,"load",k);K(g,"error",k)}d&&(g.referrerPolicy="no-referrer");e&&(g.attributionSrc="");g.src=b;a.google_image_requests.push(g)} function Ya(a,b,c=!1){if(a.fetch){const d={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};c&&(d.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?d.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:d.headers={"Attribution-Reporting-Eligible":"event-source"});a.fetch(b,d)}else N(a,b,void 0,!1,c)}var Za=Ha(()=>"referrerPolicy"in Wa("IMG"));let $a=0;function ab(a,b=null){return b&&b.getAttribute("data-jc")===String(a)?b:document.querySelector(`[${"data-jc"}="${a}"]`)};function O(a){bb||(bb=new cb);const b=bb.g[a.key];if(a.valueType==="proto"){try{const c=JSON.parse(b);if(Array.isArray(c))return c}catch(c){}return a.defaultValue}return typeof b===typeof a.defaultValue?b:a.defaultValue}var db=class{constructor(){this.g={}}};var cb=class extends db{constructor(){super();var a=ab($a,document.currentScript);a=a&&a.getAttribute("data-jc-flags")||"";try{const b=JSON.parse(a)[0];a="";for(let c=0;c<b.length;c++)a+=String.fromCharCode(b.charCodeAt(c)^"\u0003\u0007\u0003\u0007\b\u0004\u0004\u0006\u0005\u0003".charCodeAt(c%10));this.g=JSON.parse(a)}catch(b){}}},bb;var eb=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function fb(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(d){b=c;break a}b=void 0}return b};const gb=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var hb=class{constructor(a,b){this.g=a;this.i=b}},ib=class{constructor(a,b){this.url=a;this.g=!!b;this.depth=null}};let P=null;function jb(){const a=q.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function kb(){const a=q.performance;return a&&a.now?a.now():null};var lb=class{constructor(a,b){var c=kb()||jb();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const Q=q.performance,mb=!!(Q&&Q.mark&&Q.measure&&Q.clearMarks),R=Ha(()=>{var a;if(a=mb){var b;a=window;if(P===null){P="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(P=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=P;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function nb(a){a&&Q&&R()&&(Q.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),Q.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};function S(a,b){const c={};c[a]=b;return[c]}function ob(a,b,c,d,f){const e=[];Pa(a,(g,k)=>{(g=pb(g,b,c,d,f))&&e.push(`${k}=${g}`)});return e.join(b)} function pb(a,b,c,d,f){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const e=[];for(let g=0;g<a.length;g++)e.push(pb(a[g],b,c,d+1,f));return e.join(c[d])}}else if(typeof a==="object")return f||(f=0),f<2?encodeURIComponent(ob(a,b,c,d,f+1)):"...";return encodeURIComponent(String(a))}function qb(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.j.length-1} function rb(a,b){let c="https://pagead2.googlesyndication.com"+b,d=qb(a)-b.length;if(d<0)return"";a.g.sort((e,g)=>e-g);b=null;let f="";for(let e=0;e<a.g.length;e++){const g=a.g[e],k=a.i[g];for(let l=0;l<k.length;l++){if(!d){b=b==null?g:b;break}let m=ob(k[l],a.j,",$");if(m){m=f+m;if(d>=m.length){d-=m.length;c+=m;f=a.j;break}b=b==null?g:b}}}a="";b!=null&&(a=`${f}${"trn"}=${b}`);return c+a}var sb=class{constructor(){this.j="&";this.i={};this.l=0;this.g=[]}};function tb(a,b,c,d){let f,e;try{a.g&&a.g.g?(e=a.g.start(b.toString(),3),f=c(),a.g.end(e)):f=c()}catch(g){c=!0;try{nb(e),c=a.u(b,new eb(g,{message:fb(g)}),void 0,d)}catch(k){a.l(217,k)}if(c)window.console?.error?.(g);else throw g;}return f}function T(a,b,c,d){var f=U;return(...e)=>tb(f,a,()=>b.apply(c,e),d)} var vb=class{constructor(a=null){this.m=V;this.g=a;this.i=null;this.j=!1;this.u=this.l}l(a,b,c,d,f){f=f||"jserror";let e=void 0;try{const C=new sb;var g=C;g.g.push(1);g.i[1]=S("context",a);b.error&&b.meta&&b.id||(b=new eb(b,{message:fb(b)}));g=b;if(g.msg){b=C;var k=g.msg.substring(0,512);b.g.push(2);b.i[2]=S("msg",k)}var l=g.meta||{};k=l;if(this.i)try{this.i(k)}catch(B){}if(d)try{d(k)}catch(B){}d=C;l=[l];d.g.push(3);d.i[3]=l;var m;if(!(m=p)){k=q;l=[];let B;b=null;do{var h=k;a:{d=void 0;try{if(d=!!h&& h.location.href!=null)b:{try{ba(h.foo);d=!0;break b}catch(z){}d=!1}var n=d;break a}catch{n=!1;break a}n=void 0}n?(B=h.location.href,b=h.document&&h.document.referrer||null):(B=b,b=null);l.push(new ib(B||""));try{k=h.parent}catch(z){k=null}}while(k&&h!==k);for(let z=0,Qa=l.length-1;z<=Qa;++z)l[z].depth=Qa-z;h=q;if(h.location&&h.location.ancestorOrigins&&h.location.ancestorOrigins.length===l.length-1)for(n=1;n<l.length;++n){const z=l[n];z.url||(z.url=h.location.ancestorOrigins[n-1]||"",z.g=!0)}m=l}var p= m;let Z=new ib(q.location.href,!1);m=null;const qa=p.length-1;for(h=qa;h>=0;--h){var r=p[h];!m&&gb.test(r.url)&&(m=r);if(r.url&&!r.g){Z=r;break}}r=null;const Kb=p.length&&p[qa].url;Z.depth!==0&&Kb&&(r=p[qa]);e=new hb(Z,r);if(e.i){p=C;var w=e.i.url||"";p.g.push(4);p.i[4]=S("top",w)}var D={url:e.g.url||""};if(e.g.url){const B=e.g.url.match(Ja);var M=B[1],Ra=B[3],Sa=B[4];w="";M&&(w+=M+":");Ra&&(w+="//",w+=Ra,Sa&&(w+=":"+Sa));var Ta=w}else Ta="";M=C;D=[D,{url:Ta}];M.g.push(5);M.i[5]=D;ub(this.m,f,C,this.j, c)}catch(C){try{ub(this.m,f,{context:"ecmserr",rctx:a,msg:fb(C),url:e?.g.url??""},this.j,c)}catch(Z){}}return!0}};var W=a=>{var b="v";if(a.v&&a.hasOwnProperty(b))return a.v;b=new a;return a.v=b};class wb{};function ub(a,b,c,d=!1,f,e){if((d?a.g:Math.random())<(f||.01))try{let g;c instanceof sb?g=c:(g=new sb,Pa(c,(l,m)=>{var h=g;const n=h.l++;l=S(m,l);h.g.push(n);h.i[n]=l}));const k=rb(g,a.path+b+"&");k&&(typeof e!=="undefined"?N(q,k,e):N(q,k))}catch(g){}}function xb(){var a=V,b=window.google_srt;b>=0&&b<=1&&(a.g=b)}var yb=class{constructor(){this.path="/pagead/gen_204?id=";this.g=Math.random()}};let V,U; const X=new class{constructor(a,b){this.i=[];this.j=b||q;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.i=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.g=R()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.g)return null;a=new lb(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;Q&&R()&&Q.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(kb()||jb())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;Q&&R()&&Q.mark(b);!this.g||this.i.length> 2048||this.i.push(a)}}}(1,window);function zb(){window.google_measure_js_timing||(X.g=!1,X.i!==X.j.google_js_reporting_queue&&(R()&&Array.prototype.forEach.call(X.i,nb,void 0),X.i.length=0))} (function(a){V=a??new yb;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());xb();U=new vb(X);U.i=b=>{var c=$a;c!==0&&(b.jc=String(c),c=(c=ab(c,document.currentScript))&&c.getAttribute("data-jc-version")||"unknown",b.shv=c)};U.j=!0;window.document.readyState==="complete"?zb():X.g&&K(window,"load",()=>{zb()})})();function Ab(a,b,c,d){return T(a,b,c,d)}function Bb(a,b,c,d){W(wb);var f=[];!b.eid&&f.length&&(b.eid=f.toString());ub(V,a,b,!0,c,d)};const Cb=["FRAME","IMG","IFRAME"],Db=/^[01](px)?$/;function Eb(a){return typeof a==="string"?document.getElementById(a):a}function Fb(a){a||(a=(b,c,d)=>{b.addEventListener(c,d)});return a} function Gb(a,b){if(a=Eb(a)){var c=Fb(c);var d=!1,f=p=>{d||(d=!0,b(p))},e=2;for(var g=0;g<Cb.length;++g)if(Cb[g]===a.tagName){e=3;var k=[a];break}k||(k=a.querySelectorAll(Cb.join(",")));var l=0,m=0,h=!0;a=!1;for(g=0;g<k.length;g++){const p=k[g];if(p.tagName!=="IMG"||!p.complete||p.naturalWidth&&p.naturalHeight?Db.test(p.getAttribute("width")??"")&&Db.test(p.getAttribute("height")??""):1)continue;const r=p.tagName==="IMG";if(p.tagName==="IMG")var n=p.naturalWidth&&p.naturalHeight?!0:!1;else try{n= (p.readyState?p.readyState:p.contentWindow?.document?.readyState)==="complete"}catch(w){n=!1}if(n)a=!0,r&&(h=!0);else{l++;const w=D=>{l--;!l&&h&&f(e);r&&(D=D&&D.type==="error",m--,D||(h=!0))};c(p,"load",w);r&&(m++,c(p,"error",w))}}m===0&&(h=!0);k=null;k=q.document.readyState==="complete";if(l===0&&!a&&k)e=5;else if(l||!a){c(q,"load",()=>{f(4)});return}f(e)}};function Hb(a){const b=a.length;let c=0;return new Y(d=>{if(b==0)d([]);else{const f=[];for(let e=0;e<b;++e)a[e].then(g=>{f[e]=g;++c==b&&d(f)})}})}function Ib(){let a;const b=new Y(c=>{a=c});return new Jb(b,a)}function Lb(a,b){if(!a.i)if(b instanceof Y)b.then(c=>{Lb(a,c)});else{a.i=!0;a.j=b;for(b=0;b<a.g.length;++b)Mb(a,a.g[b]);a.g=[]}}function Mb(a,b){a.i?b(a.j):a.g.push(b)}class Y{constructor(a){this.i=!1;this.g=[];a(b=>{Lb(this,b)})}then(a){return new Y(b=>{Mb(this,c=>{b(a(c))})})}} var Jb=class{constructor(a,b){this.promise=a;this.resolve=b}};function Nb(a){return a.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||""]??0}function Ob(a){let b;a.visibilityState?b="visibilitychange":a.mozVisibilityState?b="mozvisibilitychange":a.webkitVisibilityState&&(b="webkitvisibilitychange");return b};function Pb(){const a=window;if(a.gmaSdk||a.webkit?.messageHandlers?.getGmaViewSignals)return a;try{const b=window.parent;if(b.gmaSdk||b.webkit?.messageHandlers?.getGmaViewSignals)return b}catch(b){}return null} function Qb(a,b={},c=()=>{},d=()=>{},f=200,e,g){const k=String(Math.floor(Oa()*2147483647));let l=0;const m=h=>{try{const n=typeof h.data==="object"?h.data:JSON.parse(h.data);k===n.paw_id&&(window.clearTimeout(l),window.removeEventListener("message",m),n.signal?c(n.signal):n.error&&d(n.error))}catch(n){g("paw_sigs",{msg:"postmessageError",err:n instanceof Error?n.message:"nonError",data:h.data==null?"null":h.data.length>500?h.data.substring(0,500):h.data})}};window.addEventListener("message",h=>{e(903, ()=>{m(h)})()});a.postMessage({paw_id:k,...b});l=window.setTimeout(()=>{window.removeEventListener("message",m);d("PAW GMA postmessage timed out.")},f)};function Rb(a=document){return!!a.featurePolicy?.allowedFeatures().includes("attribution-reporting")};var Sb=class extends E{};function Tb(a,b){return A(a,2,b)}function Ub(a,b){return A(a,3,b)}function Vb(a,b){return A(a,4,b)}function Wb(a,b){return A(a,5,b)}function Xb(a,b){return A(a,9,b)} function Yb(a,b){{const m=a.o;let h=m[v]|0;if(h&2)throw Error();if(b==null)xa(m,h,10);else{var c=b[v]|0,d=c,f=!!(2&c)&&!!(4&c)||!!(1024&c),e=f||Object.isFrozen(b),g=!0,k=!0;for(let n=0;n<b.length;n++){var l=b[n];f||(l=!!((l.o[v]|0)&2),g&&(g=!l),k&&(k=l))}f||(c=g?13:5,c=k?c|16:c&-17);e&&c===d||(b=[...b],d=0,2&c&&(c|=16),c=2&h?c|2:c&-3,c=(c|32)&-1025,32&h||(c&=-33));c!==d&&(b[v]=c);xa(m,h,10,b)}}return a}function Zb(a,b){return y(a,11,b==null?b:ra(b))}function $b(a,b){return A(a,1,b)} function ac(a,b){return y(a,7,b==null?b:ra(b))}var bc=class extends E{};const cc="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function dc(a){if(typeof a.navigator?.userAgentData?.getHighEntropyValues!=="function")return null;const b=a.google_tag_data??(a.google_tag_data={});if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(cc).then(c=>{b.uach??(b.uach=c);return c});return b.uach_promise=a} function ec(a){return Zb(Yb(Wb(Tb($b(Vb(ac(Xb(Ub(new bc,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),a.fullVersionList?.map(b=>{var c=new Sb;c=A(c,1,b.brand);return A(c,2,b.version)})||[]),a.wow64||!1)}function fc(a){return dc(a)?.then(b=>ec(b))??null};function gc(a){return["omid_v1_present","omid_v1_present_web","omid_v1_present_app"].some(b=>{try{var c=a.frames&&!!a.frames[b]}catch(d){c=!1}return c})};let hc=null;function ic(){const a=H(q).omid3p;let b=!!a&&typeof a.registerSessionObserver==="function"&&typeof a.addEventListener==="function";b||Na(c=>{gc(c)&&(b=!0);return b});return b}function jc(){const a=W(kc);if(!a.m)throw Error("aiv::err");a.m()}function lc(a){if(I.ebrpfa||O(za))a=G(a,"&cbvp=2");O(Fa)&&(a=G(a,"&sbtr=1"));I.opxdv&&(a=ic()?G(a,"&rm=3"):G(a,"&rm=1"));a=a.replace("&amp;","&");mc(W(kc),a)} function nc(a,b){W(kc).i.then(()=>{var c=new ya;c=A(c,1,a);var d=b?2:3;if(d!=null){if(!pa(d))throw c=Error("enum"),da(c,"warning"),c;d|=0}c=y(c,9,d);H(q)?.fence?.reportEvent({eventType:"impression",eventData:JSON.stringify(x(c)),destination:["buyer"]})})} function oc(a){W(kc).i.then(()=>{if(Za())Xa(window,a,null,!0,!1,!1);else{{const c=q.document;if(c.body){var b=c.getElementById("goog-srcless-iframe");b||(b=Wa("IFRAME"),b.style.display="none",b.id="goog-srcless-iframe",c.body.appendChild(b))}else b=null}b&&b.contentWindow&&Xa(b.contentWindow,a,null,!0,!1,!1)}})} function pc(a,b){var c=b;var d=c.search(La),f=Ka(c,0,"ase",d);if(f<0)c=null;else{var e=c.indexOf("&",f);if(e<0||e>d)e=d;c=decodeURIComponent(c.slice(f+4,e!==-1?e:0).replace(/\+/g," "))}if(c===(2).toString()||Ga.test(b)){c=Rb(a.document);e=b.search(La);d=0;for(var g=[];(f=Ka(b,d,"nis",e))>=0;)g.push(b.substring(d,f)),d=Math.min(b.indexOf("&",f)+1||e,e);g.push(b.slice(d));e=g.join("").replace(Ma,"$1");b=c?6:5;(b="nis"+(b!=null?"="+encodeURIComponent(String(b)):""))?(d=e.indexOf("#"),d<0&&(d=e.length), f=e.indexOf("?"),f<0||f>d?(f=d,g=""):g=e.substring(f+1,d),e=[e.slice(0,f),g,e.slice(d)],d=e[1],e[1]=b?d?d+"&"+b:b:d,b=e[0]+(e[1]?"?"+e[1]:"")+e[2]):b=e;Ya(a,b,O(Da)?c:!0)}else if(I.atsb){c=b;if(b=a.navigator)b=a.navigator.userAgent,b=/Chrome/.test(b)&&!/Edge/.test(b)?!0:!1;b&&typeof a.navigator.sendBeacon==="function"?a.navigator.sendBeacon(c):N(a,c,void 0,!1)}else N(a,b)} function mc(a,b){/(google|doubleclick).*\/pagead\/adview/.test(b)&&(b=G(b,`&vis=${Nb(a.g)}`));O(Aa)&&"__google_lidar_radf_"in a.l&&(b=G(b,"&avradf=1"));a.i.then(()=>{let c=b;a.u.length>0&&(c=G(c,"&uach="+a.u));a.j.length>0&&(c=G(c,a.j));J()===1?c=G(c,"&ibtr=1"):J()===2&&(c=G(c,"&ebtr=1"));pc(a.l,c)});J()===2&&Hb([a.i,a.A]).then(()=>{let c=b;c=G(c,"&ibtr=1");c=c.replace("/pagead/adview","/btr/view").replace("/pcs/view","/btr/view");pc(a.l,c)})} function qc(a){const b=[];var c=O(Ca)||!!I.aub;if(c||I.aunb){let d=fc(a.l);d&&(d=d.then(f=>{var e=JSON.stringify(x(f));f=[];var g=0;for(var k=0;k<e.length;k++){var l=e.charCodeAt(k);l>255&&(f[g++]=l&255,l>>=8);f[g++]=l}e=3;e===void 0&&(e=0);if(!t)for(t={},g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),k=["+/=","+/","-_=","-_.","-_"],l=0;l<5;l++){var m=g.concat(k[l].split(""));ca[l]=m;for(var h=0;h<m.length;h++){var n=m[h];t[n]===void 0&&(t[n]=h)}}e=ca[e];g=Array(Math.floor(f.length/ 3));k=e[64]||"";for(l=m=0;m<f.length-2;m+=3){var p=f[m],r=f[m+1];n=f[m+2];h=e[p>>2];p=e[(p&3)<<4|r>>4];r=e[(r&15)<<2|n>>6];n=e[n&63];g[l++]=h+p+r+n}h=0;n=k;switch(f.length-m){case 2:h=f[m+1],n=e[(h&15)<<2]||k;case 1:f=f[m],g[l]=e[f>>2]+e[(f&3)<<4|h>>4]+n+k}a.u=g.join("")}),c&&b.push(d))}if(O(Ba)||O(Ea))c=Pb(),c?.gmaSdk?.getViewSignals?(c=c.gmaSdk.getViewSignals())&&!O(Ea)&&(a.j="&ms="+c):c?.webkit?.messageHandlers?.getGmaViewSignals&&Qb(c?.webkit?.messageHandlers?.getGmaViewSignals,{},d=>{O(Ea)|| (a.j="&"+d)},()=>{},200,Ab,Bb);I.umi&&(c=new Y(d=>{a.m=d}),b.push(c));if(I.ebrpfa||O(za)||J()){const d=Ib();J()===2?a.A=d.promise:b.push(d.promise);Va(a.g,()=>{Gb(a.g.body,d.resolve)})}Nb(a.g)==3&&Nb(a.g)==3&&b.push(rc(a));if(I.opxdv&&a.B){const d=Ib();b.push(d.promise);c=q.omrhp;typeof c==="function"?c(d.resolve):(c=q.document.querySelector("script[data-jc='86']"))&&c.addEventListener("load",()=>{q.omrhp(d.resolve)})}a.i=Hb(b)} function rc(a){return new Y(b=>{const c=Ob(a.g);if(c){var d=()=>{Nb(a.g)!=3&&(Ia(a.g,c,d),b())};hc&&(d=hc(521,d));K(a.g,c,d)}})}class kc{constructor(){this.g=q.document;this.l=q;this.m=null;this.j=this.u="";this.B=ic();qc(this)}};$a=40;const sc=ab(40,document.currentScript);var tc;if(sc){const a={},b=sc.attributes;for(let c=b.length-1;c>=0;c--){const d=b[c].name;d.indexOf("data-jcp-")===0&&(a[d.substring(9)]=b[c].value)}tc=a}else tc={};(a=>{hc=Ab;H().vu=T(492,lc);H().vv=T(494,jc);a?.istd&&(H().tdvu=T(1197,nc));a?.extrk&&(H().nrtp=Ab(1169,function(b,c){oc(c)}))})(tc);}).call(this);</script><script data-jc="55" data-jc-version="r20250327">(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var e=this||self;/*  Copyright Google LLC SPDX-License-Identifier: Apache-2.0 */ var f=class{constructor(a){this.g=a}toString(){return this.g}},g=new f("about:invalid#zClosurez");class l{constructor(a){this.j=a}}function n(a){return new l(b=>b.substr(0,a.length+1).toLowerCase()===a+":")}const p=new l(a=>/^[^:]*([/?#]|$)/.test(a));var q=n("http"),r=n("https"),t=n("ftp"),u=n("mailto");const v=[n("data"),q,r,u,t,p];function w(a,b=v){if(a instanceof f)return a;for(let c=0;c<b.length;++c){const d=b[c];if(d instanceof l&&d.j(a))return new f(a)}}function x(a,b=v){return w(a,b)||g}var y=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function z(a,b,c){if(Array.isArray(b))for(let d=0;d<b.length;d++)z(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};var A=(a,b,c,d,h)=>{if(h)c=a+("&"+b+"="+c);else{var k="&"+b+"=";let m=a.indexOf(k);m<0?c=a+k+c:(m+=k.length,k=a.indexOf("&",m),c=k>=0?a.substring(0,m)+c+a.substring(k):a.substring(0,m)+c)}return c.length>6E4?d!==void 0?A(a,b,d,void 0,h):a:c};var B=window;let C=null;function D(){const a=e.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function E(){const a=e.performance;return a&&a.now?a.now():null};var F=class{constructor(a,b){var c=E()||D();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const G=e.performance,H=!!(G&&G.mark&&G.measure&&G.clearMarks),I=function(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}(()=>{var a;if(a=H){var b;a=window;if(C===null){C="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(C=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=C;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function J(a){a&&G&&I()&&(G.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),G.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};const K=[q,r,u,t,p,n("market"),n("itms"),n("intent"),n("itms-appss")]; function L(){var a=`${B.location.protocol==="http:"?"http:":"https:"}//${"pagead2.googlesyndication.com"}/pagead/gen_204`;return b=>{b={id:"unsafeurl",ctx:600,url:b};var c=[];for(d in b)z(d,b[d],c);var d=c.join("&");if(d){b=a.indexOf("#");b<0&&(b=a.length);c=a.indexOf("?");let h;c<0||c>b?(c=b,h=""):h=a.substring(c+1,b);b=[a.slice(0,c),h,a.slice(b)];c=b[1];b[1]=d?c?c+"&"+d:d:c;d=b[0]+(b[1]?"?"+b[1]:"")+b[2]}else d=a;navigator.sendBeacon&&navigator.sendBeacon(d,"")}};const M=new class{constructor(a,b){this.g=[];this.l=b||e;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.g=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.i=I()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.i)return null;a=new F(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;G&&I()&&G.mark(b);return a}end(a){if(this.i&&typeof a.value==="number"){a.duration=(E()||D())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;G&&I()&&G.mark(b);!this.i||this.g.length> 2048||this.g.push(a)}}}(1,window);function N(){window.google_measure_js_timing||(M.i=!1,M.g!==M.l.google_js_reporting_queue&&(I()&&Array.prototype.forEach.call(M.g,J,void 0),M.g.length=0))}typeof window.google_srt!=="number"&&(window.google_srt=Math.random());if(window.document.readyState==="complete")N();else if(M.i){var O=()=>{N()},P=window;P.addEventListener&&P.addEventListener.call(P,"load",O,!1)};window.bgz=a=>{if(a=document.getElementById(a)){var b=A(a.href,"bg","10");var c=L();if(!(b instanceof f)){var d=x(b,K);d===g&&c(b);b=d}if(b instanceof f)if(b instanceof f)b=b.g;else throw Error("");else b=y.test(b)?b:void 0;b!==void 0&&(a.href=b)}};}).call(this);</script><script data-jc="53" data-jc-version="r20250327">(function(){'use strict';var aa=Object.defineProperty,ba=globalThis;function ca(a,b){if(b)a:{var c=ba;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&aa(c,a,{configurable:!0,writable:!0,value:b})}}ca("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var n=this||self;function q(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function da(a,b){function c(){}c.prototype=b.prototype;a.B=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Z=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};function r(){this.o=this.o;this.s=this.s}r.prototype.o=!1;r.prototype.dispose=function(){this.o||(this.o=!0,this.u())};r.prototype[Symbol.dispose]=function(){this.dispose()};r.prototype.u=function(){if(this.s)for(;this.s.length;)this.s.shift()()};function u(a,b){this.type=a;this.g=this.target=b;this.defaultPrevented=!1}u.prototype.h=function(){this.defaultPrevented=!0};function v(a,b){u.call(this,a?a.type:"");this.relatedTarget=this.g=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.i=null;if(a){const c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.g=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement)); this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType= a.pointerType;this.state=a.state;this.i=a;a.defaultPrevented&&v.B.h.call(this)}}da(v,u);v.prototype.h=function(){v.B.h.call(this);const a=this.i;a.preventDefault?a.preventDefault():a.returnValue=!1};var w="closure_listenable_"+(Math.random()*1E6|0);var ea=0;function fa(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.m=e;this.key=++ea;this.j=this.l=!1}function x(a){a.j=!0;a.listener=null;a.proxy=null;a.src=null;a.m=null};function ha(a){let b=0;for(const c in a)b++}const ia="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function ja(a,b){let c,d;for(let e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(let f=0;f<ia.length;f++)c=ia[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};function y(a){this.src=a;this.g={};this.h=0}y.prototype.add=function(a,b,c,d,e){const f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);const g=z(a,b,d,e);g>-1?(b=a[g],c||(b.l=!1)):(b=new fa(b,this.src,f,!!d,e),b.l=c,a.push(b));return b};function A(a,b){const c=b.type;if(c in a.g){var d=a.g[c],e=Array.prototype.indexOf.call(d,b,void 0),f;(f=e>=0)&&Array.prototype.splice.call(d,e,1);f&&(x(b),a.g[c].length==0&&(delete a.g[c],a.h--))}} function z(a,b,c,d){for(let e=0;e<a.length;++e){const f=a[e];if(!f.j&&f.listener==b&&f.capture==!!c&&f.m==d)return e}return-1};var B="closure_lm_"+(Math.random()*1E6|0),C={},ka=0;function la(a,b,c,d,e){if(d&&d.once)ma(a,b,c,d,e);else if(Array.isArray(b))for(let f=0;f<b.length;f++)la(a,b[f],c,d,e);else c=D(c),a&&a[w]?a.listen(b,c,q(d)?!!d.capture:!!d,e):na(a,b,c,!1,d,e)} function na(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");const g=q(e)?!!e.capture:!!e;let h=E(a);h||(a[B]=h=new y(a));c=h.add(b,c,d,g,f);if(!c.proxy){d=oa();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(pa(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");ka++}} function oa(){function a(c){return b.call(a.src,a.listener,c)}const b=qa;return a}function ma(a,b,c,d,e){if(Array.isArray(b))for(let f=0;f<b.length;f++)ma(a,b[f],c,d,e);else c=D(c),a&&a[w]?ra(a,b,c,q(d)?!!d.capture:!!d,e):na(a,b,c,!0,d,e)} function sa(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)sa(a,b[f],c,d,e);else(d=q(d)?!!d.capture:!!d,c=D(c),a&&a[w])?(a=a.g,f=String(b).toString(),f in a.g&&(b=a.g[f],c=z(b,c,d,e),c>-1&&(x(b[c]),Array.prototype.splice.call(b,c,1),b.length==0&&(delete a.g[f],a.h--)))):a&&(a=E(a))&&(b=a.g[b.toString()],a=-1,b&&(a=z(b,c,d,e)),(c=a>-1?b[a]:null)&&ta(c))} function ta(a){if(typeof a!=="number"&&a&&!a.j){var b=a.src;if(b&&b[w])A(b.g,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(pa(c),d):b.addListener&&b.removeListener&&b.removeListener(d);ka--;(c=E(b))?(A(c,a),c.h==0&&(c.src=null,b[B]=null)):x(a)}}}function pa(a){return a in C?C[a]:C[a]="on"+a}function qa(a,b){if(a.j)a=!0;else{b=new v(b,this);const c=a.listener,d=a.m||a.src;a.l&&ta(a);a=c.call(d,b)}return a} function E(a){a=a[B];return a instanceof y?a:null}var F="__closure_events_fn_"+(Math.random()*1E9>>>0);function D(a){if(typeof a==="function")return a;a[F]||(a[F]=function(b){return a.handleEvent(b)});return a[F]};function H(){r.call(this);this.g=new y(this);this.D=this;this.A=null}da(H,r);H.prototype[w]=!0;H.prototype.addEventListener=function(a,b,c,d){la(this,a,b,c,d)};H.prototype.removeEventListener=function(a,b,c,d){sa(this,a,b,c,d)}; H.prototype.dispatchEvent=function(a){var b,c=this.A;if(c)for(b=[];c;c=c.A)b.push(c);c=this.D;const d=a.type||a;if(typeof a==="string")a=new u(a,c);else if(a instanceof u)a.target=a.target||c;else{var e=a;a=new u(d,c);ja(a,e)}e=!0;let f,g;if(b)for(g=b.length-1;g>=0;g--)f=a.g=b[g],e=I(f,d,!0,a)&&e;f=a.g=c;e=I(f,d,!0,a)&&e;e=I(f,d,!1,a)&&e;if(b)for(g=0;g<b.length;g++)f=a.g=b[g],e=I(f,d,!1,a)&&e;return e}; H.prototype.u=function(){H.B.u.call(this);if(this.g){var a=this.g;let b=0;for(const c in a.g){const d=a.g[c];for(let e=0;e<d.length;e++)++b,x(d[e]);delete a.g[c];a.h--}}this.A=null};H.prototype.listen=function(a,b,c,d){return this.g.add(String(a),b,!1,c,d)};function ra(a,b,c,d,e){a.g.add(String(b),c,!0,d,e)} function I(a,b,c,d){b=a.g.g[String(b)];if(!b)return!0;b=b.concat();let e=!0;for(let f=0;f<b.length;++f){const g=b[f];if(g&&!g.j&&g.capture==c){const h=g.listener,l=g.m||g.src;g.l&&A(a.g,g);e=h.call(l,d)!==!1&&e}}return e&&!d.defaultPrevented};function ua(a){a.dispatchEvent(new Event("became-visible"))}function va(a){a.dispatchEvent(new Event("became-invisible"))}var wa=class extends H{constructor(){super();this.C=!1}};function xa(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}};var ya={capture:!0},za=xa(function(){let a=!1;try{const b=Object.defineProperty({},"passive",{get:function(){a=!0}});n.addEventListener("test",null,b)}catch(b){}return a});function Aa(a){return a?a.passive&&za()?a:a.capture||!1:!1}function J(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,Aa(d))};function K(a){return a.prerendering?3:{visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[a.visibilityState||a.webkitVisibilityState||a.mozVisibilityState||""]??0}function Ba(a){let b;a.visibilityState?b="visibilitychange":a.mozVisibilityState?b="mozvisibilitychange":a.webkitVisibilityState&&(b="webkitvisibilitychange");return b}function Ca(a,b){if(K(b)===3)return!1;a();return!0} function Da(a){function b(){!d&&Ca(a,c)&&(d=!0,c.removeEventListener&&c.removeEventListener(e,b,Aa()))}var c=n.document;if(!Ca(a,c)){var d=!1,e=Ba(c);e&&J(c,e,b)}};function Ea(a){if("IntersectionObserver"in n&&"IntersectionObserverEntry"in n&&"isIntersecting"in n.IntersectionObserverEntry.prototype&&K(document)!==0){(new IntersectionObserver(c=>{for(const g of c){c=a;const h=g.intersectionRatio;var d=c,e=g.intersectionRect.top+"-"+g.intersectionRect.left+"-"+g.intersectionRect.bottom+"-"+g.intersectionRect.right,f=h;let l=new Event("visibility-update");l.visibleCoordinates=e;l.visibleFraction=f;d.dispatchEvent(l);d=h>=.1;Fa(c)?L(c,d):Ga(c,d)}},a.G)).observe(a.I); const b=Ba(n.document);b&&(Fa(a)?(J(n,b,()=>L(a)),Da(()=>L(a))):(a.h=!1,J(n,b,()=>Ha(a)),Da(()=>Ha(a))));Ia(a)}else Ja(a)}function Fa(a){return Array.prototype.indexOf.call(a.F,"combine_io_and_pv_functions",void 0)>=0}function L(a,b=a.v){a.v=b;b=K(n.document)===1;Ga(a,a.v&&b)}function Ha(a){const b=a.h;a.h=K(n.document)===1;!b&&a.h&&a.i?ua(a):b&&!a.h&&va(a)}function Ia(a){const b=new Event("monitoring-started");a.dispatchEvent(b)} function Ja(a){const b=new Event("monitoring-failed");a.dispatchEvent(b)}function Ga(a,b){const c=a.i;a.i=b;a.i&&!c&&a.h?ua(a):!a.i&&c&&va(a)}var Ka=class extends wa{constructor(){var a=document.body;super();this.I=a;this.G={threshold:[.1]};this.F=[];this.v=this.i=!1;this.h=!0}};function La(a){n.setTimeout(()=>{throw a;},0)};function Ma(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};let Na=void 0;function Oa(a,b){if(a!=null){var c=Na??(Na={});var d=c[a]||0;d>=b||(c[a]=d+1,a=Error(),Ma(a,"incident"),La(a))}};function M(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var Pa=M(),N=M("m_m",!0),Qa=M();const O=M("jas",!0);var Ra;const Sa=[];Sa[O]=55;Ra=Object.freeze(Sa);const Ta=typeof N==="symbol";var Ua={};function Va(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object};const Wa=BigInt(Number.MIN_SAFE_INTEGER),Xa=BigInt(Number.MAX_SAFE_INTEGER);const Ya=Number.isFinite;function Za(a){if(!Ya(a))throw a=Error("enum"),Ma(a,"warning"),a;return a|0};function $a(a){return a};function ab(a,b,c){var d=bb;c=c?!!(b&32):void 0;const e=[];var f=a.length;let g,h,l,k=!1;b&64?(b&256?(f--,g=a[f],h=f):(h=**********,g=void 0),b&512||(k=!0,l=(cb??$a)(g?h- -1:b>>15&1023||536870912,-1,a,g),h=l+-1)):(h=**********,b&1||(g=f&&a[f-1],Va(g)?(f--,h=f,l=0):g=void 0));b=void 0;for(let m=0;m<f;m++){let p=a[m];p!=null&&(p=d(p,c))!=null&&(m>=h?(b??(b={}))[m- -1]=p:e[m]=p)}if(g)for(let m in g)a=g[m],a!=null&&(a=d(a,c))!=null&&(f=+m,f<l?e[f+-1]=a:(b??(b={}))[m]=a);b&&(k?e.push(b):e[h]=b);return e} function bb(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=Wa&&a<=Xa?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[O]|0;return a.length===0&&b&1?void 0:ab(a,b,!1)}b=a[N];const c=b===Ua;Ta&&b&&!c&&Oa(Qa,3);if(c)return db(a);return}return a}let cb;function db(a){a=a.g;return ab(a,a[O]|0)};function eb(){Oa(Pa,5)};function fb(a,b,c){const d=b&512?0:-1,e=1+d;var f=a.length-1;if(e>=f&&b&256)return a[f][1]=c,b;if(e<=f)return a[e]=c,b;c!==void 0&&(f=b>>15&1023||536870912,1>=f?c!=null&&(a[f+d]={[1]:c},b|=256,a[O]=b):a[e]=c);return b}function gb(a){return!!(2&a)&&!!(4&a)||!!(1024&a)}function hb(a,b){2&a&&(a|=16);var c;2&b?c=a|2:c=a&-3;a=c;return(a|32)&-1025}function ib(a,b){32&b||(a&=-33);return a};var jb=class{constructor(a){a:{if(a==null){var b=96;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[O]|0;8192&b||!(64&b)||2&b||eb();if(b&1024)throw Error("farr");if(b&64){b&16384||(a[O]=b|16384);var c=a;break a}var d=a;b|=64;var e=d.length;if(e){var f=e-1;e=d[f];if(Va(e)){b|=256;const g=b&512?0:-1;f-=g;if(f>=1024)throw Error("pvtlmt");for(c in e){const h=+c;if(h<f)d[h+g]=e[c],delete e[c];else break}b=b&-33521665|(f&1023)<<15}}}a[O]=b|16384;c=a}this.g=c}toJSON(){var a=db(this);return a}}; jb.prototype[N]=Ua;jb.prototype.toString=function(){return this.g.toString()};/*  Copyright Google LLC SPDX-License-Identifier: Apache-2.0 */ var P=class{constructor(a){this.g=a}toString(){return this.g}};class kb{constructor(a){this.H=a}}function Q(a){return new kb(b=>b.substr(0,a.length+1).toLowerCase()===a+":")}const lb=[Q("data"),Q("http"),Q("https"),Q("mailto"),Q("ftp"),new kb(a=>/^[^:]*([/?#]|$)/.test(a))];var nb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;var R=document;function ob(a,b){return a&&b?a==b||a.contains(b):!1};let S=null;function pb(){const a=n.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function qb(){const a=n.performance;return a&&a.now?a.now():null};var rb=class{constructor(a,b){var c=qb()||pb();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const T=n.performance,sb=!!(T&&T.mark&&T.measure&&T.clearMarks),U=xa(()=>{var a;if(a=sb){var b;a=window;if(S===null){S="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(S=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=S;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function tb(a){a&&T&&U()&&(T.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),T.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};const V=new class{constructor(a,b){this.g=[];this.i=b||n;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.g=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.h=U()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.h)return null;a=new rb(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;T&&U()&&T.mark(b);return a}end(a){if(this.h&&typeof a.value==="number"){a.duration=(qb()||pb())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;T&&U()&&T.mark(b);!this.h||this.g.length> 2048||this.g.push(a)}}}(1,window);function ub(){window.google_measure_js_timing||(V.h=!1,V.g!==V.i.google_js_reporting_queue&&(U()&&Array.prototype.forEach.call(V.g,tb,void 0),V.g.length=0))}typeof window.google_srt!=="number"&&(window.google_srt=Math.random());window.document.readyState==="complete"?ub():V.h&&J(window,"load",()=>{ub()});ha({U:0,T:1,P:2,K:3,R:4,L:5,S:6,N:7,O:8,J:9,M:10,V:11});ha({X:0,Y:1,W:2});function vb(a){var b=new wb;if((b.g[O]|0)&2)throw Error();var c=b.g;b=c[O]|0;const d=2&b?1:2;var e=1+(b&512?0:-1);var f=c.length-1;e>=f&&b&256?e=c[f][1]:e<=f?e=c[e]:e=void 0;e=Array.isArray(e)?e:Ra;f=e[O]|0;var g=4&f?!1:!0;if(g){4&f&&(e=[...e],f=hb(f,b),b=fb(c,b,e));let k=g=0;for(;g<e.length;g++){var h=e[g];h=h==null?h:Ya(h)?h|0:void 0;h!=null&&(e[k++]=h)}k<g&&(e.length=k);f===0&&(f=hb(f,b),f|=16);f|=21;f&=-6145;e[O]=f;2&f&&Object.freeze(e)}d===1||d===4&&32&f?gb(f)||(b=f,f|=2,f!==b&&(e[O]=f),Object.freeze(e)): (d===2&&gb(f)&&(e=[...e],f=hb(f,b),f=ib(f,b),e[O]=f,b=fb(c,b,e)),gb(f)||(c=f,f=ib(f,b),f!==c&&(e[O]=f)));b=e;if(Array.isArray(a)){var l=a.length;for(c=0;c<l;c++)b.push(Za(a[c]))}else for(l of a)b.push(Za(l))}var wb=class extends jb{};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);vb([1,8,9,10,11,12,2,3,4,5,15,16,19,20,21,23]);vb([1,6,7,9,10,11,12,2,3,4,5,13,14,18,19,20,21,23]);vb([1,6,7,9,10,11,12,22,2,3,4,5,13,14,17,18,19,20,21,23]);new wb;function W(a,b,c,d,e){if(window.css)window.css(b,c,d,e,void 0);else if(a){a:if(b=a.href,e?d=b+("&"+c+"="+d):(e="&"+c+"=",c=b.indexOf(e),c<0?d=b+e+d:(c+=e.length,e=b.indexOf("&",c),d=e>=0?b.substring(0,c)+d+b.substring(e):b.substring(0,c)+d)),b=d.length>6E4?b:d,!(b instanceof P)){for(d=0;d<lb.length;++d)if(c=lb[d],c instanceof kb&&c.H(b)){b=new P(b);break a}b=void 0}if(b instanceof P)if(b instanceof P)b=b.g;else throw Error("");else b=nb.test(b)?b:void 0;b!==void 0&&(a.href=b)}};let xb=!1,yb=!1,zb=null;const Ab=()=>{J?.(document,"click",a=>{zb=a},ya)};var Bb=()=>{const a=zb;return a?xb||yb?!1:(a.preventDefault?a.preventDefault():a.returnValue=!1,!0):!1},Cb=()=>{J(document,"mousedown",()=>{xb=!0});J(document,"keydown",()=>{yb=!0});Ab()};var Db=(a,b=17)=>{if(b<0||Math.floor(b)!==b)b=17;X(a,b)},Eb=(a,b)=>{X(a,b||1)},Fb=a=>{X(a,2)},Gb=(a,b)=>{X(a,b||0)};let Y={},Hb=!0;const X=(a,b)=>{var c;if(c=Hb)Y[a]===void 0&&(Y[a]=[]),(c=Y[a][b])||(Y[a][b]=!0);c||W(document.getElementById(a),a,"nb",b,Hb)};function Ib(a,b,c){const d=a.id||"";W(a,d,"nx",b);W(a,d,"ny",c)}var Jb=class{constructor(){this.g=this.h=null;R.addEventListener&&R.addEventListener("mousedown",a=>{this.h=a},!0);J(R,"DOMContentLoaded",()=>{this.g=R.getElementById("common_15click_anchor")})}};const Kb=[0,2,1];let Lb=null;var Mb=a=>{if(a){var b;{const c=window.event||Lb;c?((b=c.which?1<<Kb[c.which-1]:c.button)&&c.shiftKey&&(b|=8),b&&c.altKey&&(b|=16),b&&c.ctrlKey&&(b|=32)):b=null}b&&W(a,a.id,"mb",b)}};document.addEventListener&&document.addEventListener("mousedown",a=>{Lb=a},!0);window.mb=Mb;var Z={},Nb=(a,b=2,c="")=>{c=c?c:a;b!==1&&(Z[c]===void 0?Z[c]=1:Z[c]++);b!==0&&Z[c]&&W(document.getElementById(a),a,"nm",Z[c])};function Ob(a,b){b&&(b=new Ka,ra(b,"monitoring-failed",()=>{a.g=-1}),b.listen("became-visible",()=>{a.g=(new Date).getTime()}),b.C||(b.C=!0,Ea(b)))}var Pb=class{constructor(){this.g=0}};window.init_ssb=(a,b,c,d,e,f,g)=>{const h=window;a&&(Cb(),h.accbk=Bb);b&&(f&&(Hb=!1),h.cla=Db,h.cll=Eb,h.clb=Fb,h.clh=Gb);if(c){const l=new Jb;h.xy=(k,m,p)=>{p=p||m;const G=k||l.h;if(G&&m&&p&&!ob(l.g,G.target)){k=R.querySelector("a.one-point-five-click.rhbutton");var t;(t=!k)||(k.classList?t=k.classList.contains("preexpanded"):(t=k.classList?k.classList:(typeof k.className=="string"?k.className:k.getAttribute&&k.getAttribute("class")||"").match(/\S+/g)||[],t=Array.prototype.indexOf.call(t,"preexpanded", void 0)>=0));if(t||!ob(k,G.target))t=Math.round(G.clientX-p.offsetLeft),p=Math.round(G.clientY-p.offsetTop),Ib(m,t,p),l.g&&Ib(l.g,t,p),k&&Ib(k,t,p)}}}d&&(h.mb=Mb);e&&(h.ss=Nb);if(g){const l=new Pb;h.vti=k=>{if(k){const m=(new Date).getTime();W(k,k.id,"vti",l.g>0&&l.g<=m?m-l.g:l.g<0?l.g:"")}};h.ivti=k=>{Ob(l,k)}}};}).call(this);</script><script>init_ssb(true,false,true,true,true,false,false);</script><script>if (typeof(ss) === "undefined") { ss = function(){}; }function st(id) {var a = document.getElementById(id);if (a) {xy(window.event, a, document.body);mb(a);}bgz(id);}function ha(a,x){  if (accbk()) return;bgz(a);}function hb(u) {return bgy(u);}function ia(a,e,x) {if (accbk()) return;bgz(a);}function ja(a,x) {if (accbk()) return;bgz(a);}function ga(o,e,x) {if (document.getElementById) {var a=o.id.substring(1),p="",r="",g=e.target,t,f,h;if (g) {t=g.id;f=g.parentNode;if (f) {p=f.id;h=f.parentNode;if (h)r=h.id;}} else {h=e.srcElement;f=h.parentNode;if (f)p=f.id;t=h.id;}if (t==a||p==a||r==a)return true;ia(a,e,x);top.location.href=document.getElementById(a).href;}}</script><style>html, body {width:100%;height:100%;}body {display:table;text-align:center;}#google-center-div {display:table-cell;font-size: 0;line-height: 0;}#google_image_div { display:inline-block; }</style><meta data-asoch-meta="[[[&quot;ad0&quot;,[null,&quot;https://www.googleadservices.com/pagead/aclk?sa=L\u0026ai=Cv6ItxwPoZ_vbJ5mIid4PiI7wmQz1uomMfrK6nsGEFNnZHhABILfIlAJgvwWgAZyv-6gCyAEDqAMByAPJBKoEoQJP0If4QdkF84gcR0CNLIONBW7uh8sKpdKs2hiIR-2SR8mnh2Obw3bTPl2JZz3csVT-q62wk30Ne26nwuxAVwFFQO59Flp0maM2LTGHZuGHSpf0Q3gBuvELrTxVgPsLgjFvvUz9AE9hNVh-AvbowGIMTSLlJ_27pBqBNHuurMzMqXA77U236hRjRo-s86auRJN5Q6NCHp-Y9BqOFoqbDjXT1K_hB8DwfVxEWNwm9d0iYouDz4vACmygigjVe8VRR87xLMU49VjtvOHy1XYjeMwqgqp_V-BH7xl8xmFc5jEzsZfspaA3hBYsGBPHUMD8HPir2Cqtalq3AL-unxhLFmWDGi8mpf5evBUbBLL6ateKDikKwox6NWQWDsu0nSlbAISQwATR7OiQlAWIBe3xg4RTkAYBoAYDgAfM0ITXAYgHAZAHAqgH1ckbqAfZtrECqAemvhuoB_PRG6gHltgbqAeqm7ECqAfgvbECqAeOzhuoB5PYG6gH8OAbqAfulrECqAf-nrECqAevvrECqAeaBqgH_56xAqgH35-xAqgHyqmxAqgH66WxAqgH6rGxAqgHmbWxAqgHvrexAqgH-MKxAqgH-8KxAqgHxdGxAtgHAdIIKggAEAIYGjIBADoRn9CAgICABIDAgICAoKiAAiBIvf3BOlj7sbGdwK-MA7EJxF-BwOKiGniACgGYCwHICwHaDBAKChCg8e2Mv6v0pm4SAgEDqg0CU0fIDQHqDRMIi8_NncCvjAMVGUTCBR0IBzzD8A0B2BMN0BUBmBYB-BYBgBcBshcCGAG6FwI4AbIYCRICsFMYAyIBAdAYAegYAQ\u0026ae=1\u0026ase=2\u0026gclid=Cj0KCQjwtJ6_BhDWARIsAGanmKdvSRbV9LbQGi5b86QR6rabf-Illdkz_ltPoVL26Vzichu7DJX88WIaAgSbEALw_wcB\u0026num=1\u0026cid=CAQSQwCjtLzMBvwuYnmo0pFL18OOYsSEBOvIuw6V1TYVcb8Zr1FGTNgP_liI3sXufbisu_Xy69JQqCqKh_AOHFDfigX8mHMYAQ\u0026sig=AOD64_1hTQf-9wLmpk0t3uocwrilI3iHgg\u0026client=ca-pub-1076724771190722\u0026rf=2\u0026adurl=https://www.hero-wars.com/%3Fdelayedsignup%3Dtrue%26nx_source%3Dadx_adwordsdisplay.hw_wb_uf_-.cc-ww_en.g-mix.a-mix.au-affin_gamers.opt-purchase2.cr-hw_st_multiplier1a_gif.cn-300_600.lp-delayed.dt-display.cid-22288595181.agid-177202673233.csd-210325.-%26aid-740461794407%26gad_source%3D5%26gclid%3DCj0KCQjwtJ6_BhDWARIsAGanmKdvSRbV9LbQGi5b86QR6rabf-Illdkz_ltPoVL26Vzichu7DJX88WIaAgSbEALw_wcB&quot;,null,null,2,null,null,[null,&quot;https://www.hero-wars.com/?delayedsignup=true\u0026nx_source=adx_adwordsdisplay.hw_wb_uf_-.cc-ww_en.g-mix.a-mix.au-affin_gamers.opt-purchase2.cr-hw_st_multiplier1a_gif.cn-300_600.lp-delayed.dt-display.cid-{campaignid}.agid-{adgroupid}.csd-210325.-{ignore}\u0026aid-{creative}&quot;,&quot;&quot;],&quot;https://googleads.g.doubleclick.net/pagead/interaction/?ai=Cv6ItxwPoZ_vbJ5mIid4PiI7wmQz1uomMfrK6nsGEFNnZHhABILfIlAJgvwWgAZyv-6gCyAEDqAMByAPJBKoEoQJP0If4QdkF84gcR0CNLIONBW7uh8sKpdKs2hiIR-2SR8mnh2Obw3bTPl2JZz3csVT-q62wk30Ne26nwuxAVwFFQO59Flp0maM2LTGHZuGHSpf0Q3gBuvELrTxVgPsLgjFvvUz9AE9hNVh-AvbowGIMTSLlJ_27pBqBNHuurMzMqXA77U236hRjRo-s86auRJN5Q6NCHp-Y9BqOFoqbDjXT1K_hB8DwfVxEWNwm9d0iYouDz4vACmygigjVe8VRR87xLMU49VjtvOHy1XYjeMwqgqp_V-BH7xl8xmFc5jEzsZfspaA3hBYsGBPHUMD8HPir2Cqtalq3AL-unxhLFmWDGi8mpf5evBUbBLL6ateKDikKwox6NWQWDsu0nSlbAISQwATR7OiQlAWIBe3xg4RTkAYBoAYDgAfM0ITXAYgHAZAHAqgH1ckbqAfZtrECqAemvhuoB_PRG6gHltgbqAeqm7ECqAfgvbECqAeOzhuoB5PYG6gH8OAbqAfulrECqAf-nrECqAevvrECqAeaBqgH_56xAqgH35-xAqgHyqmxAqgH66WxAqgH6rGxAqgHmbWxAqgHvrexAqgH-MKxAqgH-8KxAqgHxdGxAtgHAdIIKggAEAIYGjIBADoRn9CAgICABIDAgICAoKiAAiBIvf3BOlj7sbGdwK-MA7EJxF-BwOKiGniACgGYCwHICwHaDBAKChCg8e2Mv6v0pm4SAgEDqg0CU0fIDQHqDRMIi8_NncCvjAMVGUTCBR0IBzzD8A0B2BMN0BUBmBYB-BYBgBcBshcCGAG6FwI4AbIYCRICsFMYAyIBAdAYAegYAQ\u0026sigh=YIBzLRxRrxU\u0026cid=CAQSQwCjtLzMBvwuYnmo0pFL18OOYsSEBOvIuw6V1TYVcb8Zr1FGTNgP_liI3sXufbisu_Xy69JQqCqKh_AOHFDfigX8mHM&quot;,null,&quot;_top&quot;,null,null,null,null,1]],[&quot;btnClk&quot;,[null,null,null,null,8]]],1,null,null,&quot;aw0&quot;]"><script>(function(){const meta = document.createElement('meta');meta.httpEquiv = 'origin-trial';meta.content = "AqgsqKkFpW6rufE+US5aH70P+FAXd+rJo4/JhHS0tUc0EF+uOPe2QL7m4iGY8q+jPdirnGzoVc1gKg9Te+E3OgIAAAB7eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiQXR0cmlidXRpb25SZXBvcnRpbmdDcm9zc0FwcFdlYiIsImV4cGlyeSI6MTcxNDUyMTU5OSwiaXNTdWJkb21haW4iOnRydWV9";document.head.appendChild(meta);const meta2 = document.createElement('meta');meta2.httpEquiv = 'origin-trial';meta2.content = "ArYcxyJSLOkWhsN3xLIG+rNZJl3GEMEV1HJXMI7TfGoA4ffSCG6Ak9Pu5lL4/b8+uCihOf8DgZfPTeXKz98FkAYAAACBeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiQXR0cmlidXRpb25SZXBvcnRpbmdDcm9zc0FwcFdlYiIsImV4cGlyeSI6MTcxNDUyMTU5OSwiaXNTdWJkb21haW4iOnRydWV9";document.head.appendChild(meta2);})();</script><meta http-equiv="origin-trial" content="AqgsqKkFpW6rufE+US5aH70P+FAXd+rJo4/JhHS0tUc0EF+uOPe2QL7m4iGY8q+jPdirnGzoVc1gKg9Te+E3OgIAAAB7eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiQXR0cmlidXRpb25SZXBvcnRpbmdDcm9zc0FwcFdlYiIsImV4cGlyeSI6MTcxNDUyMTU5OSwiaXNTdWJkb21haW4iOnRydWV9"><meta http-equiv="origin-trial" content="ArYcxyJSLOkWhsN3xLIG+rNZJl3GEMEV1HJXMI7TfGoA4ffSCG6Ak9Pu5lL4/b8+uCihOf8DgZfPTeXKz98FkAYAAACBeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiQXR0cmlidXRpb25SZXBvcnRpbmdDcm9zc0FwcFdlYiIsImV4cGlyeSI6MTcxNDUyMTU5OSwiaXNTdWJkb21haW4iOnRydWV9"><meta http-equiv="origin-trial" content="AxjhRadLCARYRJawRjMjq4U8V8okQvSnrBIJWdMajuEkN3/DfVAcLcFhMVrUWnOXagwlI8dQD84FwJDGj9ohqAYAAABveyJvcmlnaW4iOiJodHRwczovL2dvb2dsZWFkc2VydmljZXMuY29tOjQ0MyIsImZlYXR1cmUiOiJGZXRjaExhdGVyQVBJIiwiZXhwaXJ5IjoxNzI1NDA3OTk5LCJpc1RoaXJkUGFydHkiOnRydWV9"><meta http-equiv="origin-trial" content="AxjhRadLCARYRJawRjMjq4U8V8okQvSnrBIJWdMajuEkN3/DfVAcLcFhMVrUWnOXagwlI8dQD84FwJDGj9ohqAYAAABveyJvcmlnaW4iOiJodHRwczovL2dvb2dsZWFkc2VydmljZXMuY29tOjQ0MyIsImZlYXR1cmUiOiJGZXRjaExhdGVyQVBJIiwiZXhwaXJ5IjoxNzI1NDA3OTk5LCJpc1RoaXJkUGFydHkiOnRydWV9"></head><body leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" style="background-color:transparent" class="jar"><script data-jc="43" data-jc-version="r20250327" data-jcp-config="[300,600,300,600,1,1,&quot;CLvbtJ3Ar4wDFRlEwgUdCAc8ww&quot;,1,null,1]">(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var q=this||self;function aa(a){q.setTimeout(()=>{throw a;},0)};function r(a,b){return Array.prototype.map.call(a,b,void 0)};function t(a){t[" "](a);return a}t[" "]=function(){};let u=void 0;function ba(a,b){if(a!=null){var c=u??(u={});var d=c[a]||0;d>=b||(c[a]=d+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity="incident",aa(a))}};function z(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var ca=z(),A=z("m_m",!0),da=z();const C=z("jas",!0);const ea=typeof A==="symbol";var fa={};function ha(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object};const ia=BigInt(Number.MIN_SAFE_INTEGER),ja=BigInt(Number.MAX_SAFE_INTEGER);const ka=Number.isFinite;function la(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function D(a){if(a==null||typeof a==="boolean")return a;if(typeof a==="number")return!!a}function ma(a){return a==null?a:ka(a)?a|0:void 0}function E(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return ka(a)?a|0:void 0}function F(a){return a==null||typeof a==="string"?a:void 0};function na(a){return a};function oa(a,b,c){var d=pa;c=c?!!(b&32):void 0;const f=[];var e=a.length;let g,h,l,m=!1;b&64?(b&256?(e--,g=a[e],h=e):(h=**********,g=void 0),b&512||(m=!0,l=(qa??na)(g?h- -1:b>>15&1023||536870912,-1,a,g),h=l+-1)):(h=**********,b&1||(g=e&&a[e-1],ha(g)?(e--,h=e,l=0):g=void 0));b=void 0;for(let k=0;k<e;k++){let p=a[k];p!=null&&(p=d(p,c))!=null&&(k>=h?(b??(b={}))[k- -1]=p:f[k]=p)}if(g)for(let k in g)a=g[k],a!=null&&(a=d(a,c))!=null&&(e=+k,e<l?f[e+-1]=a:(b??(b={}))[k]=a);b&&(m?f.push(b):f[h]=b);return f} function pa(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=ia&&a<=ja?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[C]|0;return a.length===0&&b&1?void 0:oa(a,b,!1)}b=a[A];const c=b===fa;ea&&b&&!c&&ba(da,3);if(c)return ra(a);return}return a}let qa;function ra(a){a=a.g;return oa(a,a[C]|0)};function sa(){ba(ca,5)};function G(a,b){a=a.g;return ta(a,a[C]|0,b)}function ta(a,b,c,d){if(c===-1)return null;const f=c+(b&512?0:-1),e=a.length-1;let g;if(f>=e&&b&256)b=a[e][c],g=!0;else if(f<=e)b=a[f];else return;if(d&&b!=null){d=d(b);if(d==null)return d;if(d!==b)return g?a[e][c]=d:a[f]=d,d}return b}function ua(a){a=a.g;return ta(a,a[C]|0,12,la)};var I=class{constructor(a){a:{if(a==null){var b=96;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[C]|0;8192&b||!(64&b)||2&b||sa();if(b&1024)throw Error("farr");if(b&64){b&16384||(a[C]=b|16384);var c=a;break a}var d=a;b|=64;var f=d.length;if(f){var e=f-1;f=d[e];if(ha(f)){b|=256;const g=b&512?0:-1;e-=g;if(e>=1024)throw Error("pvtlmt");for(c in f){const h=+c;if(h<e)d[h+g]=f[c],delete f[c];else break}b=b&-33521665|(e&1023)<<15}}}a[C]=b|16384;c=a}this.g=c}toJSON(){var a=ra(this);return a}}; I.prototype[A]=fa;I.prototype.toString=function(){return this.g.toString()};var za=function(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b[C]|=32;b=new a(b)}return b}}(class extends I{});var J=window;function K(a,b,c){a.addEventListener&&a.addEventListener(b,c,!1)};var Aa=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function Ba(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{t(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch{return!1}}function Ca(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function Da(a=document){return a.createElement("img")};function Ea(a){q.google_image_requests||(q.google_image_requests=[]);const b=Da(q.document);b.src=a;q.google_image_requests.push(b)};let Fa=0;function Ga(a,b=null){return b&&b.getAttribute("data-jc")===String(a)?b:document.querySelector(`[${"data-jc"}="${a}"]`)};var Ha=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function L(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(d){b=c;break a}b=void 0}return b};const Ia=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var Ja=class{constructor(a,b){this.g=a;this.h=b}},Ka=class{constructor(a,b){this.url=a;this.g=!!b;this.depth=null}};let M=null;function La(){const a=q.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function Ma(){const a=q.performance;return a&&a.now?a.now():null};var Na=class{constructor(a,b){var c=Ma()||La();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const N=q.performance,Oa=!!(N&&N.mark&&N.measure&&N.clearMarks),O=function(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}(()=>{var a;if(a=Oa){var b;a=window;if(M===null){M="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(M=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=M;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function Pa(a){a&&N&&O()&&(N.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),N.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};function P(a,b){const c={};c[a]=b;return[c]}function Qa(a,b,c,d,f){const e=[];Ca(a,(g,h)=>{(g=Ra(g,b,c,d,f))&&e.push(`${h}=${g}`)});return e.join(b)} function Ra(a,b,c,d,f){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const e=[];for(let g=0;g<a.length;g++)e.push(Ra(a[g],b,c,d+1,f));return e.join(c[d])}}else if(typeof a==="object")return f||(f=0),f<2?encodeURIComponent(Qa(a,b,c,d,f+1)):"...";return encodeURIComponent(String(a))}function Sa(a){let b=1;for(const c in a.h)c.length>b&&(b=c.length);return 3997-b-a.i.length-1} function Ta(a,b){let c="https://pagead2.googlesyndication.com"+b,d=Sa(a)-b.length;if(d<0)return"";a.g.sort((e,g)=>e-g);b=null;let f="";for(let e=0;e<a.g.length;e++){const g=a.g[e],h=a.h[g];for(let l=0;l<h.length;l++){if(!d){b=b==null?g:b;break}let m=Qa(h[l],a.i,",$");if(m){m=f+m;if(d>=m.length){d-=m.length;c+=m;f=a.i;break}b=b==null?g:b}}}a="";b!=null&&(a=`${f}${"trn"}=${b}`);return c+a}var Q=class{constructor(){this.i="&";this.h={};this.j=0;this.g=[]}};function Ua(a,b,c){let d,f;try{a.g&&a.g.g?(f=a.g.start(b.toString(),3),d=c(),a.g.end(f)):d=c()}catch(e){c=!0;try{Pa(f),c=a.m(b,new Ha(e,{message:L(e)}),void 0,void 0)}catch(g){a.j(217,g)}if(c)window.console?.error?.(e);else throw e;}return d}function Va(a,b){var c=R;return(...d)=>Ua(c,a,()=>b.apply(void 0,d))} var Xa=class{constructor(a=null){this.l=S;this.g=a;this.h=null;this.i=!1;this.m=this.j}j(a,b,c,d,f){f=f||"jserror";let e=void 0;try{const w=new Q;var g=w;g.g.push(1);g.h[1]=P("context",a);b.error&&b.meta&&b.id||(b=new Ha(b,{message:L(b)}));g=b;if(g.msg){b=w;var h=g.msg.substring(0,512);b.g.push(2);b.h[2]=P("msg",h)}var l=g.meta||{};h=l;if(this.h)try{this.h(h)}catch(n){}if(d)try{d(h)}catch(n){}d=w;l=[l];d.g.push(3);d.h[3]=l;var m;if(!(m=v)){d=q;l=[];h=null;do{var k=d;if(Ba(k)){var p=k.location.href; h=k.document&&k.document.referrer||null}else p=h,h=null;l.push(new Ka(p||""));try{d=k.parent}catch(n){d=null}}while(d&&k!==d);for(let n=0,va=l.length-1;n<=va;++n)l[n].depth=va-n;k=q;if(k.location&&k.location.ancestorOrigins&&k.location.ancestorOrigins.length===l.length-1)for(p=1;p<l.length;++p){const n=l[p];n.url||(n.url=k.location.ancestorOrigins[p-1]||"",n.g=!0)}m=l}var v=m;let H=new Ka(q.location.href,!1);m=null;const T=v.length-1;for(k=T;k>=0;--k){var x=v[k];!m&&Ia.test(x.url)&&(m=x);if(x.url&& !x.g){H=x;break}}x=null;const cb=v.length&&v[T].url;H.depth!==0&&cb&&(x=v[T]);e=new Ja(H,x);if(e.h){v=w;var y=e.h.url||"";v.g.push(4);v.h[4]=P("top",y)}var U={url:e.g.url||""};if(e.g.url){const n=e.g.url.match(Aa);var B=n[1],wa=n[3],xa=n[4];y="";B&&(y+=B+":");wa&&(y+="//",y+=wa,xa&&(y+=":"+xa));var ya=y}else ya="";B=w;U=[U,{url:ya}];B.g.push(5);B.h[5]=U;Wa(this.l,f,w,this.i,c)}catch(w){try{Wa(this.l,f,{context:"ecmserr",rctx:a,msg:L(w),url:e?.g.url??""},this.i,c)}catch(H){}}return!0}};function Wa(a,b,c,d=!1,f){if((d?a.g:Math.random())<(f||.01))try{let e;c instanceof Q?e=c:(e=new Q,Ca(c,(h,l)=>{var m=e;const k=m.j++;h=P(l,h);m.g.push(k);m.h[k]=h}));const g=Ta(e,"/pagead/gen_204?id="+b+"&");g&&Ea(g)}catch(e){}}function Ya(){var a=S,b=window.google_srt;b>=0&&b<=1&&(a.g=b)}var Za=class{constructor(){this.g=Math.random()}};let S,R; const V=new class{constructor(a,b){this.h=[];this.i=b||q;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.h=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.g=O()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.g)return null;a=new Na(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;N&&O()&&N.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(Ma()||La())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;N&&O()&&N.mark(b);!this.g||this.h.length> 2048||this.h.push(a)}}}(1,window);function $a(){window.google_measure_js_timing||(V.g=!1,V.h!==V.i.google_js_reporting_queue&&(O()&&Array.prototype.forEach.call(V.h,Pa,void 0),V.h.length=0))} (function(a){S=a??new Za;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());Ya();R=new Xa(V);R.h=b=>{var c=Fa;c!==0&&(b.jc=String(c),c=(c=Ga(c,document.currentScript))&&c.getAttribute("data-jc-version")||"unknown",b.shv=c)};R.i=!0;window.document.readyState==="complete"?$a():V.g&&K(window,"load",()=>{$a()})})();function ab(a,b){return Va(a,b)};function bb(a){K(J,"message",b=>{let c;try{c=JSON.parse(b.data)}catch(d){return}!c||c.googMsgType!=="sth"||a(c,b)})}function W(a){var b=J.top;a.googMsgType="sth";b.postMessage(JSON.stringify(a),"*")};function db(a){switch(a){case 0:return"safe";case 1:return"force";case 2:return"animate";case 3:return"no_rsz";default:return"safe"}} function eb(a){const b=ab(421,c=>{fb(a,"force",!0,"dnsz_user");a.j=2;a.l===3&&W(X("expand-on-scroll-force-expand",[]));Y(a);c.preventDefault()});a.h&&K(a.h,"click",b);bb(ab(422,c=>{if(c)if(c.msg_type==="resize-result"){if(c.r_str!="no_rsz"){c=!!c.r_status;a.j=c?2:1;if(c)try{for(c=J;c!=c.top&&Ba(c.parent);c=c.parent)for(var d=c.frameElement;d;d=d.parentElement)d.tagName!="HTML"&&d.tagName!="BODY"&&(d.width=a.m,d.height=a.i,d.style.width=`${a.m}px`,d.style.height=`${a.i}px`)}catch(f){}Y(a);gb(a)}}else c.msg_type=== "expand-on-scroll-result"&&((d=c.eos_amount)?d==a.i?(a.j=2,Y(a)):a.h.style.height=a.h.offsetHeight+d+"px":(c.eos_success||(a.j=1,a.l=1),Y(a)))}))}function fb(a,b,c,d){a=r([["r_nw",a.m],["r_nh",a.i],["r_str",b],["r_ao",c],["expid",F(G(a.g,11))??""],["r_rqtr",d],["gen204_fraction",ua(a.g)??0],["qid",F(G(a.g,7))??""],["r_ifr",D(G(a.g,5))??!1],["r_cab",D(G(a.g,6))??!1],["r_cui",D(G(a.g,13))??!1]],f=>({key:f[0],value:f[1]}));W(X("resize-me",a))}function X(a,b){return{key_value:b,msg_type:a}} function Y(a){a.h&&(a.h.style.display=a.j!=1||a.o!=1||a.l!=1&&a.l!=3?"none":"block")}function gb(a){a.j===1&&a.o===1&&a.l===3&&(a=r([["r_nh",a.i],["i_expid",F(G(a.g,11))??""],["gen204_fraction",ua(a.g)??0],["qid",F(G(a.g,7))??""]],([b,c])=>({key:b,value:c})),W(X("expand-on-scroll",a)))} class hb{constructor(){var a=ib;this.h=document.getElementById("expander-glasspane");this.g=za(a.config);this.s=E(G(this.g,2))??0;this.m=E(G(this.g,3))??0;this.i=E(G(this.g,4))??0;this.o=this.s>=this.i?0:1;this.j=0;this.l=ma(G(this.g,10))??0;eb(this);fb(this,db(ma(G(this.g,9))??0),!1,"dnsz_init")}};Fa=43;const jb=Ga(43,document.currentScript);if(jb==null)throw Error("JSC not found 43");var ib;const kb={},Z=jb.attributes;for(let a=Z.length-1;a>=0;a--){const b=Z[a].name;b.indexOf("data-jcp-")===0&&(kb[b.substring(9)]=Z[a].value)}ib=kb;new hb;}).call(this);</script><div id="google-center-div"><div id="google_image_div" style="height: 600px; width: 300px; overflow:hidden; position:relative" class="GoogleActiveViewElement" data-google-av-cxn="https://pagead2.googlesyndication.com/pcs/activeview?xai=AKAOjstdxqMvrD3kiO_M3igf5QUJL4RdOTnHR29VkEEYkGh6XeshoHm2oF9R2TtUy7C3QK296k-DqHRfk94n5fEvI_szz9kr_D3-3Eiij8HyxzZqJOBxpp2ygzVJH27ORQ5yApQPCNOz1wAGQrCXIZDiW-e3_JrpZT_Ckh8RgMVPvHG7xZgodzJB_KYqcyu6D1Rt5tKdmFrcz8Y&amp;sai=AMfl-YS7tA4YUzZ3dZGlg1OPTFahT-vcOs2C5V6iRzaNJaCMvLZ0mcbWQahy1qNNP4igqxUu7ZVmZGE3gXyhmfpMT7lw-KIt3x-2p8yNFrfI_OgEeg23IXXWaOGEEX09N8m0UAk5ug&amp;sig=Cg0ArKJSzPp-H4ivh1ccEAE" data-google-av-adk="944469691" data-google-av-metadata="la=0&amp;xdi=0&amp;" data-google-av-ufs-integrator-metadata="CgIYABLAAwqzA2h0dHBzOi8vcGFnZWFkMi5nb29nbGVzeW5kaWNhdGlvbi5jb20vcGNzL2FjdGl2ZXZpZXc_eGFpPUFLQU9qc3RkeHFNdnJEM2tpT19NM2lnZjVRVUpMNFJkT1RuSFIyOVZrRUVZa0doNlhlc2hvSG0yb0Y5UjJUdFV5N0MzUUsyOTZrLURxSFJmazk0bjVmRXZJX3N6ejlrcl9EMy0zRWlpajhIeXh6WnFKT0J4cHAyeWd6VkpIMjdPUlE1eUFwUVBDTk96MXdBR1FyQ1hJWkRpVy1lM19KcnBaVF9Da2g4UmdNVlB2SEc3eFpnb2R6SkJfS1lxY3l1NkQxUnQ1dEtkbUZyY3o4WSZzYWk9QU1mbC1ZUzd0QTRZVXpaM2RaR2xnMU9QVEZhaFQtdmNPczJDNVY2aVJ6YU5KYUNNdkxaMG1jYldRYWh5MXFOTlA0aWdxeFV1N1pWbVpHRTNnWHlobWZwTVQ3bHctS0l0M3gtMnA4eU5GcmZJX09nRWVnMjNJWFhXYU9HRUVYMDlOOG0wVUFrNXVnJnNpZz1DZzBBcktKU3pQcC1INGl2aDFjY0VBRRIAGgAgASgAMAQaHgoaQ0x2YnRKM0FyNHdERlJsRXdnVWRDQWM4d3cQBQ" data-google-av-override="-1" data-google-av-dm="2" data-google-av-aid="0" data-google-av-naid="1" data-google-av-slift="" data-google-av-cpmav="" data-google-av-btr="" data-google-av-itpl="4" data-google-av-rs="2" data-google-av-flags="[&quot;x%278440&#39;9efotm(&amp;753374%2bejvf/%27844&gt;&#39;9wuvb$&amp;56533&gt;!=|vqc)!273794&amp;&lt;qqvb/%&lt;1735020!=nehu`/!364=5051!9abk{a($160210:3&amp;&lt;cbotf+*0150034:%2bejvf/%72;17613!=efdwa*&#39;76463;21$?ebkpb$&amp;0366717&gt;*&gt;bgipf+!3=712363%9aihwc)!7202&lt;217&#39;9efotm(&amp;20061;48&amp;&gt;`dopb/%&lt;1707200!=8(&amp;2005575?&amp;&gt;`dopb/%&lt;170642?!=|vqc)!7201;=50&#39;9wuvb$&amp;03641654*&gt;bgipf+!3=731103%9aihwc)!7200?073&#39;9efotm(&amp;2004?51;&amp;&gt;`dopb/%&lt;17&gt;474&gt;!=nehu`/!36406412!9abk{a($167745;=&amp;&lt;cbotf+*01254133%2pvs`/!36383624!9abk{a($167574&gt;7&amp;&lt;qqvb/%&lt;104=460!=nehu`/!363;42&gt;7!9abk{a($1656;3?&lt;&amp;&lt;cbotf+*01011776%2bejvf/%72&gt;17266!=efdwa*&#39;7616?=&lt;=$?ebkpb$&amp;0335225&gt;*&gt;bgipfz&quot;]" data-creative-load-listener=""><script>vu("https://googleads.g.doubleclick.net/pagead/adview?ai\x3dCOR8txwPoZ_vbJ5mIid4PiI7wmQz1uomMfrK6nsGEFNnZHhABILfIlAJgvwWgAZyv-6gCyAEDqAMByAPJBKoEkgJP0If4QdkF84gcR0CNLIONBW7uh8sKpdKs2hiIR-2SR8mnh2Obw3bTPl2JZz3csVT-q62wk30Ne26nwuxAVwFFQO59Flp0maM2LTGHZuGHSpf0Q3gBuvELrTxVgPsLgjFvvUz9AE9hNVh-AvbowGIMTSLlJ_27pBqBNHuurMzMqXA77U236hRjRo-s86auRJN5Q6NCHp-Y9BqOFoqbDjXT1K_hB8DwfVxEWNwm9d0iYouDz4vACmygigjVe8VRR87xLMU49VjtvOHy1XYjeMwqgqp_V-BH7xl8xmFc5jEzsZfspaA3hBYsGBPHUIL-PRJiKbPCyczHoFz9saLXNkdK6rqpfVBin8m0I5pqeTx6l7INwATR7OiQlAWIBe3xg4RTkgUECAQYAZIFBAgFGASSBQQIBRgYkgUFCAUYqAGQBgGgBgOAB8zQhNcBiAcBkAcCqAfVyRuoB9m2sQKoB6a-G6gH89EbqAeW2BuoB6qbsQKoB-C9sQKoB47OG6gHk9gbqAfw4BuoB-6WsQKoB_6esQKoB6--sQKoB_fCsQLYBwHyBwQQsuMR0ggqCAAQAhgaMgEAOhGf0ICAgIAEgMCAgICgqIACIEi9_cE6WPuxsZ3Ar4wDmgmQAmh0dHBzOi8vd3d3Lmhlcm8td2Fycy5jb20vP2RlbGF5ZWRzaWdudXA9dHJ1ZSZueF9zb3VyY2U9YWR4X2Fkd29yZHNkaXNwbGF5Lmh3X3diX3VmXy0uY2Mtd3dfZW4uZy1taXguYS1taXguYXUtYWZmaW5fZ2FtZXJzLm9wdC1wdXJjaGFzZTIuY3ItaHdfc3RfbXVsdGlwbGllcjFhX2dpZi5jbi0zMDBfNjAwLmxwLWRlbGF5ZWQuZHQtZGlzcGxheS5jaWQtMjIyODg1OTUxODEuYWdpZC0xNzcyMDI2NzMyMzMuY3NkLTIxMDMyNS4tJmFpZC03NDA0NjE3OTQ0MDcmZ2FkX3NvdXJjZT01gAoByAsB2gwQCgoQoPHtjL-r9KZuEgIBA-oNEwiLz82dwK-MAxUZRMIFHQgHPMPYEw3QFQGYFgH4FgGAFwGyFx4KGggAEhRwdWItMTA3NjcyNDc3MTE5MDcyMhgAGAG6FwI4AbIYCRICsFMYAyIBAdAYAegYAQ\x26sigh\x3dOww-rHNKvvQ\x26uach_m\x3d%5BUACH%5D\x26ase\x3d2\x26nis\x3d4\x26cid\x3dCAQSQwCjtLzMBvwuYnmo0pFL18OOYsSEBOvIuw6V1TYVcb8Zr1FGTNgP_liI3sXufbisu_Xy69JQqCqKh_AOHFDfigX8mHMYAQ")</script><a id="aw0" target="_top" href="https://www.googleadservices.com/pagead/aclk?nis=4&amp;sa=L&amp;ai=Cv6ItxwPoZ_vbJ5mIid4PiI7wmQz1uomMfrK6nsGEFNnZHhABILfIlAJgvwWgAZyv-6gCyAEDqAMByAPJBKoEoQJP0If4QdkF84gcR0CNLIONBW7uh8sKpdKs2hiIR-2SR8mnh2Obw3bTPl2JZz3csVT-q62wk30Ne26nwuxAVwFFQO59Flp0maM2LTGHZuGHSpf0Q3gBuvELrTxVgPsLgjFvvUz9AE9hNVh-AvbowGIMTSLlJ_27pBqBNHuurMzMqXA77U236hRjRo-s86auRJN5Q6NCHp-Y9BqOFoqbDjXT1K_hB8DwfVxEWNwm9d0iYouDz4vACmygigjVe8VRR87xLMU49VjtvOHy1XYjeMwqgqp_V-BH7xl8xmFc5jEzsZfspaA3hBYsGBPHUMD8HPir2Cqtalq3AL-unxhLFmWDGi8mpf5evBUbBLL6ateKDikKwox6NWQWDsu0nSlbAISQwATR7OiQlAWIBe3xg4RTkAYBoAYDgAfM0ITXAYgHAZAHAqgH1ckbqAfZtrECqAemvhuoB_PRG6gHltgbqAeqm7ECqAfgvbECqAeOzhuoB5PYG6gH8OAbqAfulrECqAf-nrECqAevvrECqAeaBqgH_56xAqgH35-xAqgHyqmxAqgH66WxAqgH6rGxAqgHmbWxAqgHvrexAqgH-MKxAqgH-8KxAqgHxdGxAtgHAdIIKggAEAIYGjIBADoRn9CAgICABIDAgICAoKiAAiBIvf3BOlj7sbGdwK-MA7EJxF-BwOKiGniACgGYCwHICwHaDBAKChCg8e2Mv6v0pm4SAgEDqg0CU0fIDQHqDRMIi8_NncCvjAMVGUTCBR0IBzzD8A0B2BMN0BUBmBYB-BYBgBcBshcCGAG6FwI4AbIYCRICsFMYAyIBAdAYAegYAQ&amp;ae=1&amp;ase=2&amp;gclid=Cj0KCQjwtJ6_BhDWARIsAGanmKdvSRbV9LbQGi5b86QR6rabf-Illdkz_ltPoVL26Vzichu7DJX88WIaAgSbEALw_wcB&amp;num=1&amp;cid=CAQSQwCjtLzMBvwuYnmo0pFL18OOYsSEBOvIuw6V1TYVcb8Zr1FGTNgP_liI3sXufbisu_Xy69JQqCqKh_AOHFDfigX8mHMYAQ&amp;sig=AOD64_1hTQf-9wLmpk0t3uocwrilI3iHgg&amp;client=ca-pub-1076724771190722&amp;rf=2&amp;nb=2&amp;adurl=https://www.hero-wars.com/%3Fdelayedsignup%3Dtrue%26nx_source%3Dadx_adwordsdisplay.hw_wb_uf_-.cc-ww_en.g-mix.a-mix.au-affin_gamers.opt-purchase2.cr-hw_st_multiplier1a_gif.cn-300_600.lp-delayed.dt-display.cid-22288595181.agid-177202673233.csd-210325.-%26aid-740461794407%26gad_source%3D5%26gclid%3DCj0KCQjwtJ6_BhDWARIsAGanmKdvSRbV9LbQGi5b86QR6rabf-Illdkz_ltPoVL26Vzichu7DJX88WIaAgSbEALw_wcB" data-asoch-targets="ad0" attributionsrc=""><img src="./10243675803437796522" border="0" width="300" height="600" alt="" class="img_ad"></a><style>div{margin:0;padding:0;}.abgc{display:block;height:15px;position:absolute;right:17px;top:1px;text-rendering:geometricPrecision;z-index:2147483646;}.abgb{display:inline-block;height:15px;}.abgc,.jar .abgc,.jar .cbb{opacity:1;}.abgc{cursor:pointer;}.cbb{cursor:pointer;height:15px;width:15px;z-index:2147483646;background-color:#ffffff;opacity:0;}.cbb svg{position:absolute;top:0;right:0;height:15px;width:15px;stroke:#00aecd;fill:#00aecd;stroke-width:1.25;}.cbb:hover{cursor:pointer;}.cbb:hover{background-color:#58585a;}.cbb:hover svg{stroke:#ffffff;}.abgb{position:absolute;right:0px;top:0px;}.cbb{position:absolute;right:1px;top:1px;}.abgs{display:none;height:100%;}.abgl{text-decoration:none;}.abgs svg,.abgb svg{display:inline-block;height:15px;width:auto;vertical-align:top;}.abgc .il-wrap{background-color:#ffffff;height:15px;white-space:nowrap;}.abgc .il-wrap.exp{border-bottom-left-radius:5px;}.abgc .il-text,.abgc .il-icon{display:inline-block;}.abgc .il-text{padding-right:1px;padding-left:5px;height:15px;width:96px;}.abgc .il-icon{height:15px;width:15px;}.abgc .il-text svg{fill:#000000;}.abgc .il-icon svg{fill:#00aecd}</style><div id="abgc" class="abgc" dir="ltr"><div id="abgb" class="abgb"><div class="il-wrap"><div class="il-icon"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 15 15"><path d="M7.5,1.5a6,6,0,1,0,0,12a6,6,0,1,0,0,-12m0,1a5,5,0,1,1,0,10a5,5,0,1,1,0,-10ZM6.625,11l1.75,0l0,-4.5l-1.75,0ZM7.5,3.75a1,1,0,1,0,0,2a1,1,0,1,0,0,-2Z"></path></svg></div></div></div><div id="abgs" class="abgs"><a id="abgl" class="abgl" href="https://adssettings.google.com/whythisad?source=display&amp;reasons=AZ-QMKGFDwfEXAdyK6H_I6VDIZEFjh3w9X-L_OV3592EEm46PUT4GW8aA9HSD8E2YJvKxz2VhrCZv6Gxwzgp6F8aGMJueaFdQniiRhxUJQMwfBFt0Rfh6s6LfHhJc2WpUv7tSrv-CVZuI2_g4SqCk2e-f-8UDvHUk4TAeEHlC_C3A-kc8g5ukFtKeeeD1z8cN6_b-cGy1lQFkUO76UKptobzxONP2J1PNsS0k81fKPw7cUjE5tdx-IqEGmO99Ym9UJes0uY0SyvQffirnRBD2kCAzNFq_koe23iq6brpC-fvmrM05sxczf52tx-YB5ORa_9CDdtAzR1S-2QIfCoT3Lh4-pOpDGXIk2lYDfOK1J3OaXJaYT9_n4rV0x0jYIHEwz5tqKz-MkByQZt0S4yys0jh7EScflzFi_laWnP_aqMZjYOJGLry8Xl3Xw7_YI7Z03GTZsoxJesCwCaPgRoY8f7tBHmPUOYsjjADlwlXuC3nAA_tWv77XLxBZXAXu1Oh2dnMlFirdJUsjXqKdg-4jVk_BmIigspuKjb3_rugDT6adFZ0ic8Vlrkd76Ys-WPKBIPE6qbVveEfRnbx5tTb9StE3OJpzMISZW5cvRZ_gKyPKMOY_eihGNchZoIpJgHDjQXbR9SsRuWRoej6czDNvV6ojk1qY6u2zZw9VD-Jn6Du2_9Aapmq4B1ZfmiY9xF7NFbo5kaQQ2G7A4Igd7WLTX84nnf67rk5pKJS4wg1CrRwClvd2pExvqZokMQOytRph3yZ3iXfDjj8yezrMip80DRd9ZKXw_Y6qi7iIAJ1iTpt5Mqz2PP6zl4yoXtVkdcP3etCjIi4jpa5RPVyp1Yk947Wgf2VRhqrgrsLME4mQCD5oFF3tLIvCj10yrX9VcGJSJnFhH67ROqgpSf_6iZvSX-XZvaJKftv5_Av4DhauocKfD1Zb0-LriouTryV6BVNLRxU0e1gv3A_m5RtoED7KEDm2-0JzQWKiGkShjjwhU2kXW14PfasohGuD3K3VW0laAj3gyFedAFEvKJCV00P_g5ca6NqWO_SY2nxxqY8GIIcui2FTFkzBJnzZsyPfqZXJFuLB3bR5s7kbg9vr2bvLYH9xGrJ6J_onwT87_L8JZCOSKJAgObCR4BM9rx14-z2pRTVp2X1UX3rSx7NY647uD-FgNIdiVl8sV-Z1qe-fYQmnPmHY9eBsZ2JilmwksxHhIsubQqcnaA72tiZCJPmbv3bEMuW_fhB5_5ZaHCUQ6MCu9YT6Okstw3DuIPOpJTAdDudRdh3qkhBAj-A_71kRrVPc2b5ZNgS5Nn9PKA3rO5WLXTKKn-_nQgr3EzbtFYOmGQ076sPP5s3PgF1hmQ30Q_cnOVxrrQzLoSmDzux1QMyjm6TYmF3WnFUN-_UWchbrMtTNg21QXAGMRw65h7-WwrBjO12b3LIZzCQk66kroXgBe0OMK8FqfgXzItHNzXAQdeQkbApoomCsdw-_g7QMufPLLjOtvuTH6NVDQ4q3Un2r6Ep2pwXeirBmZq9VIylMzNbeZ2WsHKKPUETYBss0LPAdvTGofriNmAnaBsSYogXUMjf7hV1edvW60amU-i_qVKt2yGGwgPqLYuOiLENzfTg9Y1zzduH_ngOlkdDxchgPu1m3GxSNBaMhDZTKeLAZEnfSfzNLA5T5OJvR7mcCv9YWBS37-yBMzc2_lSyDrqzKYlWofflUza1wiQEEUDTBJVAI_QsHwGH_cflgA3Vhkyywe_-FddAuhkv5x2Yrviy9fTGRXsuZM4GNNf9SGI_bkQyaCagxCQZPJBwH_XrtqVLsO84nRfmnoY7Q0CyLWGIsLyTjYhRj6jW7IKDY34C2dt8jyZHl0br57tutdkTQAS-sYGcy_WobwCGErd9XPCD9EIFD_4uzJGqdPjcHMTfCBZLQ6JHQyzDUo5inkczxDtCztyshsMomRe9Im_0864g8vZPSMlZW8BKHNKJQi9vpSAyeNBTiGgKZutGuDR_LL8zeJAL3mrR4kvLOCtG02Sb_vA7kLz_QHFXvsQIU0mupv-11j-ns6KKZO_VqYVZo-rzDPTRZwej5Gec1y9z-0zXgCOk5WgPEUhNooBPAaoqQ3LXVUJgeSFr2h78sng4X5Hlbh0PgfPmWExWKQ&amp;opi=122715837" target="_blank"><div class="il-wrap exp"><div class="il-text"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 103 16"><path d="M8.57 8.44L8.57 11.63L8.57 11.63Q6.94 12.97 5.02 12.97L5.02 12.97L5.02 12.97Q3.04 12.97 1.83 11.77L1.83 11.77L1.83 11.77Q0.62 10.56 0.62 8.57L0.62 8.57L0.62 8.57Q0.62 7.35 1.12 6.30L1.12 6.30L1.12 6.30Q1.61 5.24 2.58 4.67L2.58 4.67L2.58 4.67Q3.54 4.09 4.93 4.09L4.93 4.09L4.93 4.09Q6.38 4.09 7.26 4.73L7.26 4.73L7.26 4.73Q8.13 5.36 8.44 6.63L8.44 6.63L7.42 6.92L7.42 6.92Q7.18 5.99 6.56 5.53L6.56 5.53L6.56 5.53Q5.94 5.06 4.93 5.06L4.93 5.06L4.93 5.06Q3.38 5.06 2.59 6.01L2.59 6.01L2.59 6.01Q1.80 6.96 1.80 8.50L1.80 8.50L1.80 8.50Q1.80 9.64 2.18 10.41L2.18 10.41L2.18 10.41Q2.57 11.17 3.32 11.56L3.32 11.56L3.32 11.56Q4.07 11.95 4.96 11.95L4.96 11.95L4.96 11.95Q6.30 11.95 7.46 11.06L7.46 11.06L7.46 9.47L4.93 9.47L4.93 8.44L8.57 8.44ZM12.67 6.47L12.67 6.47L12.67 6.47Q13.93 6.47 14.76 7.29L14.76 7.29L14.76 7.29Q15.59 8.11 15.59 9.62L15.59 9.62L15.59 9.62Q15.59 11.46 14.69 12.21L14.69 12.21L14.69 12.21Q13.79 12.97 12.67 12.97L12.67 12.97L12.67 12.97Q11.49 12.97 10.62 12.19L10.62 12.19L10.62 12.19Q9.76 11.41 9.76 9.72L9.76 9.72L9.76 9.72Q9.76 8.08 10.59 7.27L10.59 7.27L10.59 7.27Q11.43 6.47 12.67 6.47ZM12.67 12.11L12.67 12.11L12.67 12.11Q13.57 12.11 14.04 11.44L14.04 11.44L14.04 11.44Q14.51 10.77 14.51 9.68L14.51 9.68L14.51 9.68Q14.51 8.51 13.98 7.92L13.98 7.92L13.98 7.92Q13.45 7.33 12.67 7.33L12.67 7.33L12.67 7.33Q11.87 7.33 11.35 7.93L11.35 7.93L11.35 7.93Q10.83 8.53 10.83 9.72L10.83 9.72L10.83 9.72Q10.83 10.90 11.36 11.50L11.36 11.50L11.36 11.50Q11.88 12.11 12.67 12.11ZM19.34 6.47L19.34 6.47L19.34 6.47Q20.60 6.47 21.43 7.29L21.43 7.29L21.43 7.29Q22.26 8.11 22.26 9.62L22.26 9.62L22.26 9.62Q22.26 11.46 21.36 12.21L21.36 12.21L21.36 12.21Q20.47 12.97 19.34 12.97L19.34 12.97L19.34 12.97Q18.16 12.97 17.30 12.19L17.30 12.19L17.30 12.19Q16.43 11.41 16.43 9.72L16.43 9.72L16.43 9.72Q16.43 8.08 17.27 7.27L17.27 7.27L17.27 7.27Q18.11 6.47 19.34 6.47ZM19.34 12.11L19.34 12.11L19.34 12.11Q20.24 12.11 20.71 11.44L20.71 11.44L20.71 11.44Q21.18 10.77 21.18 9.68L21.18 9.68L21.18 9.68Q21.18 8.51 20.65 7.92L20.65 7.92L20.65 7.92Q20.12 7.33 19.34 7.33L19.34 7.33L19.34 7.33Q18.54 7.33 18.02 7.93L18.02 7.93L18.02 7.93Q17.51 8.53 17.51 9.72L17.51 9.72L17.51 9.72Q17.51 10.90 18.03 11.50L18.03 11.50L18.03 11.50Q18.56 12.11 19.34 12.11ZM28.58 6.61L28.58 11.99L28.58 11.99Q28.58 13.31 28.31 13.97L28.31 13.97L28.31 13.97Q28.05 14.62 27.38 14.98L27.38 14.98L27.38 14.98Q26.70 15.35 25.76 15.35L25.76 15.35L25.76 15.35Q24.71 15.35 24.01 14.88L24.01 14.88L24.01 14.88Q23.30 14.41 23.30 13.34L23.30 13.34L24.33 13.50L24.33 13.50Q24.39 13.99 24.74 14.24L24.74 14.24L24.74 14.24Q25.10 14.48 25.75 14.48L25.75 14.48L25.75 14.48Q26.57 14.48 26.95 14.15L26.95 14.15L26.95 14.15Q27.33 13.82 27.42 13.34L27.42 13.34L27.42 13.34Q27.50 12.86 27.50 12.01L27.50 12.01L27.50 12.01Q26.82 12.83 25.78 12.83L25.78 12.83L25.78 12.83Q24.56 12.83 23.83 11.93L23.83 11.93L23.83 11.93Q23.09 11.03 23.09 9.67L23.09 9.67L23.09 9.67Q23.09 8.30 23.81 7.38L23.81 7.38L23.81 7.38Q24.52 6.47 25.79 6.47L25.79 6.47L25.79 6.47Q26.89 6.47 27.58 7.35L27.58 7.35L27.60 7.35L27.60 6.61L28.58 6.61ZM25.88 11.96L25.88 11.96L25.88 11.96Q26.55 11.96 27.07 11.43L27.07 11.43L27.07 11.43Q27.59 10.89 27.59 9.63L27.59 9.63L27.59 9.63Q27.59 8.52 27.09 7.92L27.09 7.92L27.09 7.92Q26.60 7.33 25.86 7.33L25.86 7.33L25.86 7.33Q25.11 7.33 24.64 7.94L24.64 7.94L24.64 7.94Q24.17 8.55 24.17 9.61L24.17 9.61L24.17 9.61Q24.17 10.83 24.67 11.40L24.67 11.40L24.67 11.40Q25.16 11.96 25.88 11.96ZM31.21 4.24L31.21 12.83L30.16 12.83L30.16 4.24L31.21 4.24ZM38.23 9.98L38.23 9.98L33.57 9.98L33.57 9.98Q33.63 11.02 34.16 11.56L34.16 11.56L34.16 11.56Q34.68 12.11 35.47 12.11L35.47 12.11L35.47 12.11Q36.07 12.11 36.48 11.79L36.48 11.79L36.48 11.79Q36.89 11.47 37.11 10.82L37.11 10.82L38.19 10.96L38.19 10.96Q37.93 11.93 37.22 12.45L37.22 12.45L37.22 12.45Q36.52 12.97 35.47 12.97L35.47 12.97L35.47 12.97Q34.07 12.97 33.28 12.11L33.28 12.11L33.28 12.11Q32.49 11.26 32.49 9.77L32.49 9.77L32.49 9.77Q32.49 8.30 33.25 7.38L33.25 7.38L33.25 7.38Q34.02 6.47 35.41 6.47L35.41 6.47L35.41 6.47Q36.09 6.47 36.72 6.77L36.72 6.77L36.72 6.77Q37.35 7.07 37.79 7.78L37.79 7.78L37.79 7.78Q38.23 8.50 38.23 9.98ZM33.63 9.12L37.14 9.12L37.14 9.12Q37.08 8.17 36.55 7.75L36.55 7.75L36.55 7.75Q36.03 7.33 35.41 7.33L35.41 7.33L35.41 7.33Q34.66 7.33 34.18 7.83L34.18 7.83L34.18 7.83Q33.70 8.33 33.63 9.12L33.63 9.12ZM46.60 5.19L46.60 6.07L45.30 6.07L45.30 8.22L46.44 7.72L46.41 8.68L45.30 9.18L45.30 12.66L45.30 12.66Q45.30 13.03 44.97 13.34L44.97 13.34L44.97 13.34Q44.64 13.65 43.25 13.58L43.25 13.58L42.97 12.71L43.88 12.76L43.88 12.76Q44.39 12.79 44.39 12.24L44.39 12.24L44.39 9.49L44.39 9.49Q43.48 9.88 43.21 9.96L43.21 9.96L42.60 9.19L42.60 9.19Q44.37 8.64 44.39 8.59L44.39 8.59L44.39 6.07L42.87 6.07L42.87 5.19L44.39 5.19L44.39 2.91L45.30 2.91L45.30 5.19L46.60 5.19ZM52.25 3.36L52.25 7.55L47.00 7.55L47.00 3.36L52.25 3.36ZM47.91 4.98L51.34 4.98L51.34 4.24L47.91 4.24L47.91 4.98ZM47.91 6.67L51.34 6.67L51.34 5.85L47.91 5.85L47.91 6.67ZM53.66 12.15L53.66 12.15L53.30 13.09L50.81 13.07L50.81 13.07Q48.80 13.07 47.51 11.95L47.51 11.95L47.51 11.95Q47.00 13.14 46.07 13.69L46.07 13.69L45.45 13.02L45.45 13.02Q46.28 12.49 46.66 11.70L46.66 11.70L46.66 11.70Q47.04 10.90 46.99 9.46L46.99 9.46L47.88 9.57L47.88 9.57Q47.88 10.37 47.78 11.00L47.78 11.00L47.78 11.00Q48.15 11.51 49.24 11.95L49.24 11.95L49.24 9L46.51 9L46.51 8.12L53.19 8.12L53.19 9L50.15 9L50.15 10.04L52.52 10.04L52.52 10.92L50.15 10.92L50.15 12.15L50.15 12.15Q50.70 12.23 51.44 12.23L51.44 12.23L51.44 12.23Q51.86 12.23 53.66 12.15ZM57.50 2.70L58.35 3.09L58.35 3.09Q58.14 3.77 57.65 4.85L57.65 4.85L57.16 5.91L57.16 13.65L56.24 13.65L56.24 7.40L56.24 7.40Q55.96 7.87 55.27 8.61L55.27 8.61L54.49 8.06L54.49 8.06Q56.44 6.04 57.50 2.70L57.50 2.70ZM65.25 8.72L65.25 9.60L57.76 9.60L57.76 8.72L59.46 8.72L59.46 6.09L58.11 6.09L58.11 5.18L59.46 5.18L59.46 2.84L60.36 2.84L60.36 5.18L62.58 5.18L62.58 2.84L63.53 2.84L63.53 5.18L64.92 5.18L64.92 6.09L63.53 6.09L63.53 8.72L65.25 8.72ZM60.36 8.72L62.58 8.72L62.58 6.09L60.36 6.09L60.36 8.72ZM60.19 10.04L60.97 10.55L60.97 10.55Q59.98 12.38 58.29 13.66L58.29 13.66L57.59 13.00L57.59 13.00Q59.07 11.89 60.19 10.04L60.19 10.04ZM65.42 13.09L65.42 13.09L64.66 13.63L64.66 13.63Q63.73 11.96 61.89 10.66L61.89 10.66L62.66 10.13L62.66 10.13Q64.49 11.47 65.42 13.09ZM73.37 4.80L76.61 4.80L76.61 4.80Q76.56 9.65 76.15 11.56L76.15 11.56L76.15 11.56Q75.73 13.48 74.67 13.48L74.67 13.48L74.67 13.48Q74.19 13.48 72.80 13.08L72.80 13.08L72.64 12.07L72.64 12.07Q73.99 12.57 74.53 12.57L74.53 12.57L74.53 12.57Q75.04 12.57 75.36 10.68L75.36 10.68L75.36 10.68Q75.68 8.79 75.70 5.63L75.70 5.63L73.14 5.63L73.14 5.63Q72.69 6.89 71.75 8.14L71.75 8.14L71.13 7.74L71.13 13.10L70.18 13.10L70.18 12.32L68.19 12.32L68.19 13.29L67.24 13.29L67.24 4.98L68.34 4.98L68.34 4.98Q68.83 4.12 69.08 2.87L69.08 2.87L69.94 3.22L69.94 3.22Q69.60 4.49 69.29 4.98L69.29 4.98L71.13 4.98L71.13 7.42L71.13 7.42Q72.46 5.54 72.93 2.77L72.93 2.77L73.89 2.99L73.89 2.99Q73.73 3.70 73.37 4.80L73.37 4.80ZM68.19 8.17L70.18 8.17L70.18 5.85L68.19 5.85L68.19 8.17ZM68.19 11.44L70.18 11.44L70.18 9.05L68.19 9.05L68.19 11.44ZM74.98 10.26L74.98 10.26L74.12 10.64L74.12 10.64Q73.59 9.22 72.49 7.82L72.49 7.82L73.23 7.33L73.23 7.33Q74.44 8.72 74.98 10.26ZM88.93 4.69L88.93 5.57L81.20 5.57L81.20 7.22L81.20 7.22Q81.20 9.15 81.09 10.11L81.09 10.11L81.09 10.11Q80.98 11.07 80.61 11.92L80.61 11.92L80.61 11.92Q80.24 12.77 79.30 13.56L79.30 13.56L78.62 12.93L78.62 12.93Q80.32 11.82 80.32 8.66L80.32 8.66L80.32 4.69L84.13 4.69L84.13 2.95L85.03 2.95L85.03 4.69L88.93 4.69ZM101.03 7.34L101.03 8.22L90.83 8.22L90.83 7.34L95.58 7.34L95.58 5.53L93.15 5.53L93.15 5.53Q92.58 6.53 91.89 7.07L91.89 7.07L91.13 6.46L91.13 6.46Q92.47 5.46 92.98 3.33L92.98 3.33L93.87 3.55L93.87 3.55Q93.80 3.86 93.52 4.65L93.52 4.65L95.58 4.65L95.58 2.84L96.50 2.84L96.50 4.65L99.89 4.65L99.89 5.53L96.50 5.53L96.50 7.34L101.03 7.34ZM99.47 8.88L99.47 13.63L98.56 13.63L98.56 12.78L93.35 12.78L93.35 13.63L92.44 13.63L92.44 8.88L99.47 8.88ZM93.35 11.90L98.56 11.90L98.56 9.76L93.35 9.76L93.35 11.90Z"></path></svg></div><div class="il-icon"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 15 15"><path d="M7.5,1.5a6,6,0,1,0,0,12a6,6,0,1,0,0,-12m0,1a5,5,0,1,1,0,10a5,5,0,1,1,0,-10ZM6.625,11l1.75,0l0,-4.5l-1.75,0ZM7.5,3.75a1,1,0,1,0,0,2a1,1,0,1,0,0,-2Z"></path></svg></div></div></a></div></div><div id="cbb" class="cbb" tabindex="0" role="button"><svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 15 15"><path d="M3.25,3.25l8.5,8.5M11.75,3.25l-8.5,8.5"></path></svg></div><style>.mute_panel{z-index:2147483646;}.abgac{position:absolute;left:0px;top:0px;z-index:2147483646;display:none;width:100%;height:100%;background-color:#FAFAFA;}.mlsc{height:100%;display:flex;justify-content:center;align-items:center;}.mls{animation:mlskf 2s linear infinite;height:50%;width:50%;}.mlsd{stroke-dasharray:1,189;stroke-dashoffset:0;animation:mlsdkf 1.4s ease-in-out infinite;}@keyframes mlskf{100%{transform:rotate(360deg);}}@keyframes mlsdkf{0%{stroke-dasharray:1,189;stroke-dashoffset:0;}50%{stroke-dasharray:134,189;stroke-dashoffset:-53px;}100%{stroke-dasharray:134,189;stroke-dashoffset:-188px;}}</style><div id="mute_panel" class="mute_panel" aria-hidden="true"><div id="abgac" class="abgac" aria-hidden="true"><div id="mlsc" class="mlsc"><svg class="mls" viewBox="50 50 100 100"><circle class="mlsd" cx="100" cy="100" r="30" fill="none" stroke="#9E9E9E" stroke-width="3"></circle></svg></div></div></div><script data-jc="60" src="./f.txt" async="" data-jc-version="r20250327" data-jcp-attribution-data="[[null,&quot;https://googleads.g.doubleclick.net/pagead/images/mtad/x_blue.png&quot;,null,&quot;https://googleads.g.doubleclick.net/pagead/images/mtad/x_blue.png&quot;,&quot;https://googleads.g.doubleclick.net/pagead/interaction/?ai=Cv6ItxwPoZ_vbJ5mIid4PiI7wmQz1uomMfrK6nsGEFNnZHhABILfIlAJgvwWgAZyv-6gCyAEDqAMByAPJBKoEoQJP0If4QdkF84gcR0CNLIONBW7uh8sKpdKs2hiIR-2SR8mnh2Obw3bTPl2JZz3csVT-q62wk30Ne26nwuxAVwFFQO59Flp0maM2LTGHZuGHSpf0Q3gBuvELrTxVgPsLgjFvvUz9AE9hNVh-AvbowGIMTSLlJ_27pBqBNHuurMzMqXA77U236hRjRo-s86auRJN5Q6NCHp-Y9BqOFoqbDjXT1K_hB8DwfVxEWNwm9d0iYouDz4vACmygigjVe8VRR87xLMU49VjtvOHy1XYjeMwqgqp_V-BH7xl8xmFc5jEzsZfspaA3hBYsGBPHUMD8HPir2Cqtalq3AL-unxhLFmWDGi8mpf5evBUbBLL6ateKDikKwox6NWQWDsu0nSlbAISQwATR7OiQlAWIBe3xg4RTkAYBoAYDgAfM0ITXAYgHAZAHAqgH1ckbqAfZtrECqAemvhuoB_PRG6gHltgbqAeqm7ECqAfgvbECqAeOzhuoB5PYG6gH8OAbqAfulrECqAf-nrECqAevvrECqAeaBqgH_56xAqgH35-xAqgHyqmxAqgH66WxAqgH6rGxAqgHmbWxAqgHvrexAqgH-MKxAqgH-8KxAqgHxdGxAtgHAdIIKggAEAIYGjIBADoRn9CAgICABIDAgICAoKiAAiBIvf3BOlj7sbGdwK-MA7EJxF-BwOKiGniACgGYCwHICwHaDBAKChCg8e2Mv6v0pm4SAgEDqg0CU0fIDQHqDRMIi8_NncCvjAMVGUTCBR0IBzzD8A0B2BMN0BUBmBYB-BYBgBcBshcCGAG6FwI4AbIYCRICsFMYAyIBAdAYAegYAQ\u0026sigh=YIBzLRxRrxU\u0026cid=CAQSQwCjtLzMBvwuYnmo0pFL18OOYsSEBOvIuw6V1TYVcb8Zr1FGTNgP_liI3sXufbisu_Xy69JQqCqKh_AOHFDfigX8mHM&quot;,&quot;JglLmO3pOu4IsrqewYQUEO3xg4RTGJ-OmPcBIg1oZXJvLXdhcnMuY29tMggIBRMYqogCFEIXY2EtcHViLTEwNzY3MjQ3NzExOTA3MjJIAFgDcAF6BQgEEgEHqAEB&quot;,[&quot;user_feedback_menu_interaction&quot;,&quot;&quot;,0],null,null,null,null,&quot;此广告有什么问题？&quot;,null,&quot;https://googleads.g.doubleclick.net/pagead/images/mtad/back_blue.png&quot;,&quot;感谢您的反馈！&quot;,&quot;我们将对此广告进行审核，以便改善您在今后的体验。&quot;,&quot;感谢您的反馈！&quot;,&quot;我们会根据您的反馈审核此网站上的广告。&quot;,&quot;CAQSQwCjtLzMBvwuYnmo0pFL18OOYsSEBOvIuw6V1TYVcb8Zr1FGTNgP_liI3sXufbisu_Xy69JQqCqKh_AOHFDfigX8mHMYAQ&quot;,null,&quot;{\&quot;msg_type\&quot;:\&quot;resize-me\&quot;,\&quot;key_value\&quot;:[{\&quot;key\&quot;:\&quot;r_nh\&quot;,\&quot;value\&quot;:\&quot;0\&quot;},{\&quot;key\&quot;:\&quot;r_ifr\&quot;,\&quot;value\&quot;:\&quot;true\&quot;},{\&quot;key\&quot;:\&quot;r_str\&quot;,\&quot;value\&quot;:\&quot;animate\&quot;}],\&quot;googMsgType\&quot;:\&quot;sth\&quot;}&quot;,&quot;即将关闭广告：%1$d 秒&quot;,null,&quot;https://googleads.g.doubleclick.net/pagead/images/mtad/abg_blue.png&quot;,&quot;https://www.google.com/url?ct=abg\u0026q=https://www.google.com/adsense/support/bin/request.py%3Fcontact%3Dabg_afc%26url%3Dhttps://blog.csdn.net/weixin_45433817/article/details/138043455%26gl%3DSG%26hl%3Dzh%26client%3Dca-pub-1076724771190722%26ai0%3DCv6ItxwPoZ_vbJ5mIid4PiI7wmQz1uomMfrK6nsGEFNnZHhABILfIlAJgvwWgAZyv-6gCyAEDqAMByAPJBKoEoQJP0If4QdkF84gcR0CNLIONBW7uh8sKpdKs2hiIR-2SR8mnh2Obw3bTPl2JZz3csVT-q62wk30Ne26nwuxAVwFFQO59Flp0maM2LTGHZuGHSpf0Q3gBuvELrTxVgPsLgjFvvUz9AE9hNVh-AvbowGIMTSLlJ_27pBqBNHuurMzMqXA77U236hRjRo-s86auRJN5Q6NCHp-Y9BqOFoqbDjXT1K_hB8DwfVxEWNwm9d0iYouDz4vACmygigjVe8VRR87xLMU49VjtvOHy1XYjeMwqgqp_V-BH7xl8xmFc5jEzsZfspaA3hBYsGBPHUMD8HPir2Cqtalq3AL-unxhLFmWDGi8mpf5evBUbBLL6ateKDikKwox6NWQWDsu0nSlbAISQwATR7OiQlAWIBe3xg4RTkAYBoAYDgAfM0ITXAYgHAZAHAqgH1ckbqAfZtrECqAemvhuoB_PRG6gHltgbqAeqm7ECqAfgvbECqAeOzhuoB5PYG6gH8OAbqAfulrECqAf-nrECqAevvrECqAeaBqgH_56xAqgH35-xAqgHyqmxAqgH66WxAqgH6rGxAqgHmbWxAqgHvrexAqgH-MKxAqgH-8KxAqgHxdGxAtgHAdIIKggAEAIYGjIBADoRn9CAgICABIDAgICAoKiAAiBIvf3BOlj7sbGdwK-MA7EJxF-BwOKiGniACgGYCwHICwHaDBAKChCg8e2Mv6v0pm4SAgEDqg0CU0fIDQHqDRMIi8_NncCvjAMVGUTCBR0IBzzD8A0B2BMN0BUBmBYB-BYBgBcBshcCGAG6FwI4AbIYCRICsFMYAyIBAdAYAegYAQ\u0026usg=AOvVaw1SHjiVBRvgyWNmSPhS2kza&quot;,&quot;https://googleads.g.doubleclick.net/pagead/images/mtad/x_blue.png&quot;,0,[[&quot;停止显示此广告&quot;,[&quot;user_feedback_menu_option&quot;,&quot;1&quot;,1],[&quot;此广告有什么问题？&quot;,[[&quot;广告内容不当&quot;,[&quot;mute_survey_option&quot;,&quot;8&quot;,1]],[&quot;广告遮挡内容&quot;,[&quot;mute_survey_option&quot;,&quot;3&quot;,1]],[&quot;已多次看到此广告&quot;,[&quot;mute_survey_option&quot;,&quot;2&quot;,1]],[&quot;对此广告不感兴趣&quot;,[&quot;mute_survey_option&quot;,&quot;7&quot;,1]]]],[&quot;user_feedback_undo&quot;,&quot;1&quot;,1]]],[&quot;https://googleads.g.doubleclick.net/pagead/images/adchoices/iconx2-000000.png&quot;,&quot;广告选择&quot;,&quot;%1$s 已关闭此广告&quot;,null,&quot;https://www.gstatic.com/images/branding/googlelogo/2x/googlelogo_dark_color_84x28dp.png&quot;,&quot;停止显示此广告&quot;,&quot;我们尽量不再显示该广告&quot;,null,null,null,&quot;https://googleads.g.doubleclick.net/pagead/images/abg/iconx2-000000.png&quot;,&quot;Google 提供的广告&quot;,null,&quot;查看我的 Google 广告设置&quot;,null,&quot;https://www.gstatic.com&quot;,&quot;&quot;,&quot;%1$s 提供的广告&quot;,&quot;广告设置&quot;,&quot;https://adssettings.google.com&quot;,null,null,null,0,null,null,null,0,1],&quot;APyvJYwAAAdYW1tbW251bGwsWzEsMiwyMSwyMiw2M10sbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxbMl0sWzJdLG51bGwsdHJ1ZSxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLG51bGwsbnVsbCxudWxsLFtbIk5leHRlcnMgR2xvYmFsIEx0ZC4iLCJDWSIsMSxudWxsLDFdLCI1MTgzOTE1ODMiXV0sW251bGwsImh0dHBzOi8vZ29vZ2xlYWRzLmcuZG91YmxlY2xpY2submV0L3BhZ2VhZC9pbnRlcmFjdGlvbi8_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-CzLSOFMqZQH8vLMqXl3TKbzMs7RWVLUrxWfo-AJkvYe4tP05F2x6YIZslHLxCvMywppyHf2Y6eJ59J-8WfQwdIA6FcFUILssW5y8r1VeQnZOEFQg7sMce-WNTRZGyb0EdlgTty3kPbWSA9OoM45WDy2qUw2Hy6AH5S0Sszwf7v9gXQxGtAeIDW_DLM5VTSCC86khvhC8UwUmHF_j1fU2hgMjOsttWtt84Gqn3UgV8_65RVCvDTztcnh1M7z5qz2WFwJzV8CDrjzqlZDBPAnua5q6PIuBrP1s81h8vOqT8EGUQpCg,gJD_31OaA1JUHuemtYgcGQ&quot;,&quot;https://adssettings.google.com/whythisad?source=display\u0026reasons=AZ-QMKGFDwfEXAdyK6H_I6VDIZEFjh3w9X-L_OV3592EEm46PUT4GW8aA9HSD8E2YJvKxz2VhrCZv6Gxwzgp6F8aGMJueaFdQniiRhxUJQMwfBFt0Rfh6s6LfHhJc2WpUv7tSrv-CVZuI2_g4SqCk2e-f-8UDvHUk4TAeEHlC_C3A-kc8g5ukFtKeeeD1z8cN6_b-cGy1lQFkUO76UKptobzxONP2J1PNsS0k81fKPw7cUjE5tdx-IqEGmO99Ym9UJes0uY0SyvQffirnRBD2kCAzNFq_koe23iq6brpC-fvmrM05sxczf52tx-YB5ORa_9CDdtAzR1S-2QIfCoT3Lh4-pOpDGXIk2lYDfOK1J3OaXJaYT9_n4rV0x0jYIHEwz5tqKz-MkByQZt0S4yys0jh7EScflzFi_laWnP_aqMZjYOJGLry8Xl3Xw7_YI7Z03GTZsoxJesCwCaPgRoY8f7tBHmPUOYsjjADlwlXuC3nAA_tWv77XLxBZXAXu1Oh2dnMlFirdJUsjXqKdg-4jVk_BmIigspuKjb3_rugDT6adFZ0ic8Vlrkd76Ys-WPKBIPE6qbVveEfRnbx5tTb9StE3OJpzMISZW5cvRZ_gKyPKMOY_eihGNchZoIpJgHDjQXbR9SsRuWRoej6czDNvV6ojk1qY6u2zZw9VD-Jn6Du2_9Aapmq4B1ZfmiY9xF7NFbo5kaQQ2G7A4Igd7WLTX84nnf67rk5pKJS4wg1CrRwClvd2pExvqZokMQOytRph3yZ3iXfDjj8yezrMip80DRd9ZKXw_Y6qi7iIAJ1iTpt5Mqz2PP6zl4yoXtVkdcP3etCjIi4jpa5RPVyp1Yk947Wgf2VRhqrgrsLME4mQCD5oFF3tLIvCj10yrX9VcGJSJnFhH67ROqgpSf_6iZvSX-XZvaJKftv5_Av4DhauocKfD1Zb0-LriouTryV6BVNLRxU0e1gv3A_m5RtoED7KEDm2-0JzQWKiGkShjjwhU2kXW14PfasohGuD3K3VW0laAj3gyFedAFEvKJCV00P_g5ca6NqWO_SY2nxxqY8GIIcui2FTFkzBJnzZsyPfqZXJFuLB3bR5s7kbg9vr2bvLYH9xGrJ6J_onwT87_L8JZCOSKJAgObCR4BM9rx14-z2pRTVp2X1UX3rSx7NY647uD-FgNIdiVl8sV-Z1qe-fYQmnPmHY9eBsZ2JilmwksxHhIsubQqcnaA72tiZCJPmbv3bEMuW_fhB5_5ZaHCUQ6MCu9YT6Okstw3DuIPOpJTAdDudRdh3qkhBAj-A_71kRrVPc2b5ZNgS5Nn9PKA3rO5WLXTKKn-_nQgr3EzbtFYOmGQ076sPP5s3PgF1hmQ30Q_cnOVxrrQzLoSmDzux1QMyjm6TYmF3WnFUN-_UWchbrMtTNg21QXAGMRw65h7-WwrBjO12b3LIZzCQk66kroXgBe0OMK8FqfgXzItHNzXAQdeQkbApoomCsdw-_g7QMufPLLjOtvuTH6NVDQ4q3Un2r6Ep2pwXeirBmZq9VIylMzNbeZ2WsHKKPUETYBss0LPAdvTGofriNmAnaBsSYogXUMjf7hV1edvW60amU-i_qVKt2yGGwgPqLYuOiLENzfTg9Y1zzduH_ngOlkdDxchgPu1m3GxSNBaMhDZTKeLAZEnfSfzNLA5T5OJvR7mcCv9YWBS37-yBMzc2_lSyDrqzKYlWofflUza1wiQEEUDTBJVAI_QsHwGH_cflgA3Vhkyywe_-FddAuhkv5x2Yrviy9fTGRXsuZM4GNNf9SGI_bkQyaCagxCQZPJBwH_XrtqVLsO84nRfmnoY7Q0CyLWGIsLyTjYhRj6jW7IKDY34C2dt8jyZHl0br57tutdkTQAS-sYGcy_WobwCGErd9XPCD9EIFD_4uzJGqdPjcHMTfCBZLQ6JHQyzDUo5inkczxDtCztyshsMomRe9Im_0864g8vZPSMlZW8BKHNKJQi9vpSAyeNBTiGgKZutGuDR_LL8zeJAL3mrR4kvLOCtG02Sb_vA7kLz_QHFXvsQIU0mupv-11j-ns6KKZO_VqYVZo-rzDPTRZwej5Gec1y9z-0zXgCOk5WgPEUhNooBPAaoqQ3LXVUJgeSFr2h78sng4X5Hlbh0PgfPmWExWKQ\u0026opi=122715837&quot;,&quot;为什么显示此广告？&quot;,1,0],null,null,0,null,1,0,1,0,0,0,0,0,0,0,null,0,1,0,null,[[&quot;post_click_menu_height_when_bottom_anchor_on_mobile&quot;,&quot;0&quot;],[&quot;show_ad_after_mute&quot;,&quot;false&quot;],[&quot;jake_ui_extension&quot;,&quot;jake_default_ui&quot;]],600,300,0,null,null,0,null,null,&quot;right&quot;,0,null,&quot;r20250327/r20110914&quot;,null,0]"></script></div></div><script data-jc="22" src="./f(1).txt" async="" data-jc-version="r20250327" data-jcp-url="https://googleads.g.doubleclick.net/pagead/interaction/?ai=Cv6ItxwPoZ_vbJ5mIid4PiI7wmQz1uomMfrK6nsGEFNnZHhABILfIlAJgvwWgAZyv-6gCyAEDqAMByAPJBKoEoQJP0If4QdkF84gcR0CNLIONBW7uh8sKpdKs2hiIR-2SR8mnh2Obw3bTPl2JZz3csVT-q62wk30Ne26nwuxAVwFFQO59Flp0maM2LTGHZuGHSpf0Q3gBuvELrTxVgPsLgjFvvUz9AE9hNVh-AvbowGIMTSLlJ_27pBqBNHuurMzMqXA77U236hRjRo-s86auRJN5Q6NCHp-Y9BqOFoqbDjXT1K_hB8DwfVxEWNwm9d0iYouDz4vACmygigjVe8VRR87xLMU49VjtvOHy1XYjeMwqgqp_V-BH7xl8xmFc5jEzsZfspaA3hBYsGBPHUMD8HPir2Cqtalq3AL-unxhLFmWDGi8mpf5evBUbBLL6ateKDikKwox6NWQWDsu0nSlbAISQwATR7OiQlAWIBe3xg4RTkAYBoAYDgAfM0ITXAYgHAZAHAqgH1ckbqAfZtrECqAemvhuoB_PRG6gHltgbqAeqm7ECqAfgvbECqAeOzhuoB5PYG6gH8OAbqAfulrECqAf-nrECqAevvrECqAeaBqgH_56xAqgH35-xAqgHyqmxAqgH66WxAqgH6rGxAqgHmbWxAqgHvrexAqgH-MKxAqgH-8KxAqgHxdGxAtgHAdIIKggAEAIYGjIBADoRn9CAgICABIDAgICAoKiAAiBIvf3BOlj7sbGdwK-MA7EJxF-BwOKiGniACgGYCwHICwHaDBAKChCg8e2Mv6v0pm4SAgEDqg0CU0fIDQHqDRMIi8_NncCvjAMVGUTCBR0IBzzD8A0B2BMN0BUBmBYB-BYBgBcBshcCGAG6FwI4AbIYCRICsFMYAyIBAdAYAegYAQ&amp;sigh=YIBzLRxRrxU&amp;cid=CAQSQwCjtLzMBvwuYnmo0pFL18OOYsSEBOvIuw6V1TYVcb8Zr1FGTNgP_liI3sXufbisu_Xy69JQqCqKh_AOHFDfigX8mHM" data-jcp-gws-id="xwPoZ_GYIu6ijMwPjp2w-Qk" data-jcp-qem-id="CLvbtJ3Ar4wDFRlEwgUdCAc8ww"></script><iframe title="Blank" scrolling="no" frameborder="0" height="0" width="0" src="./cookie_push_onload.html" style="position:absolute" aria-hidden="true"></iframe><script data-jc="23" src="./f(2).txt" data-jc-version="r20250327" data-jcp-init-data="[[[[null,500,99,2,9,null,null,null,1],[null,500,99,2,8,null,null,null,1]]]]"></script><div style="display: none; position: absolute; z-index: 2147483647; width: 100%; height: 100%; top: 0px; left: 0px;"></div><script>window.parent.postMessage('{"googMsgType":"pvt","token":"AOrYGsn6i3kKJ78vyLELOruBz1nWzzKVbnguhdQVPQddoRBpQatbkX510VpN28e_I235qPQSn20FqiFWSVIm-FPFCtxel2E1"}', '*');window.top.postMessage('{"msg_type":"adsense-labs","key_value":[{"key":"settings","value":"[\\\"ca-pub-1076724771190722\\\",[[1]]]"}],"googMsgType":"sth"}', '*');window.top.postMessage('{"key_value":[{"key":"qid","value":"CLvbtJ3Ar4wDFRlEwgUdCAc8ww"}],"googMsgType":"adpnt"}', '*');</script><img src="./l" style="display:none;" alt=""><div style="display:none" data-google-query-id="CLvbtJ3Ar4wDFRlEwgUdCAc8ww"></div><div style="bottom:0;right:0;width:86px;height:250px;background:initial !important;position:absolute !important;max-width:100% !important;max-height:100% !important;pointer-events:none !important;image-rendering:pixelated !important;z-index:2147483647;background-image:url(&#39;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAAWBAMAAACrl3iAAAAABlBMVEUAAAD+AciWmZzWAAAAAnRSTlMAApidrBQAAAB6SURBVBjTbZABDsAgCAPrD/r/1y4C7coyElFirQfAf5wO1HIFsG45SZV1rx6tJlkZszPVZZlqe0dlEmwSxzVd3EC4y/ulrRP7h1HcHCDXkqNls496eOvhnAJEfTInGG2uvnocwvLnRyj4TLA2eXuCfrC8KWruCaqDGw8ZYQXCK8piAwAAAABJRU5ErkJggg==&#39;) !important;"></div><script data-jc="103" data-jc-version="r20250327" data-jcp-base_url="https://googleads.g.doubleclick.net/pagead/conversion/?ai=Cv6ItxwPoZ_vbJ5mIid4PiI7wmQz1uomMfrK6nsGEFNnZHhABILfIlAJgvwWgAZyv-6gCyAEDqAMByAPJBKoEoQJP0If4QdkF84gcR0CNLIONBW7uh8sKpdKs2hiIR-2SR8mnh2Obw3bTPl2JZz3csVT-q62wk30Ne26nwuxAVwFFQO59Flp0maM2LTGHZuGHSpf0Q3gBuvELrTxVgPsLgjFvvUz9AE9hNVh-AvbowGIMTSLlJ_27pBqBNHuurMzMqXA77U236hRjRo-s86auRJN5Q6NCHp-Y9BqOFoqbDjXT1K_hB8DwfVxEWNwm9d0iYouDz4vACmygigjVe8VRR87xLMU49VjtvOHy1XYjeMwqgqp_V-BH7xl8xmFc5jEzsZfspaA3hBYsGBPHUMD8HPir2Cqtalq3AL-unxhLFmWDGi8mpf5evBUbBLL6ateKDikKwox6NWQWDsu0nSlbAISQwATR7OiQlAWIBe3xg4RTkAYBoAYDgAfM0ITXAYgHAZAHAqgH1ckbqAfZtrECqAemvhuoB_PRG6gHltgbqAeqm7ECqAfgvbECqAeOzhuoB5PYG6gH8OAbqAfulrECqAf-nrECqAevvrECqAeaBqgH_56xAqgH35-xAqgHyqmxAqgH66WxAqgH6rGxAqgHmbWxAqgHvrexAqgH-MKxAqgH-8KxAqgHxdGxAtgHAdIIKggAEAIYGjIBADoRn9CAgICABIDAgICAoKiAAiBIvf3BOlj7sbGdwK-MA7EJxF-BwOKiGniACgGYCwHICwHaDBAKChCg8e2Mv6v0pm4SAgEDqg0CU0fIDQHqDRMIi8_NncCvjAMVGUTCBR0IBzzD8A0B2BMN0BUBmBYB-BYBgBcBshcCGAG6FwI4AbIYCRICsFMYAyIBAdAYAegYAQ&amp;sigh=YIBzLRxRrxU" data-jcp-cpu_label="heavy_ad_intervention_cpu" data-jcp-net_label="heavy_ad_intervention_network">(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var m=this||self;function n(a){m.setTimeout(()=>{throw a;},0)};let q=void 0;function r(a,b){if(a!=null){var e=q??(q={});var h=e[a]||0;h>=b||(e[a]=h+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity="incident",n(a))}};function t(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var u=t(),v=t("m_m",!0),w=t();const x=t("jas",!0);const y=typeof v==="symbol";var z={};function A(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object};const B=BigInt(Number.MIN_SAFE_INTEGER),C=BigInt(Number.MAX_SAFE_INTEGER);function E(a){return a};function F(a,b,e){var h=G;e=e?!!(b&32):void 0;const c=[];var g=a.length;let d,f,k,D=!1;b&64?(b&256?(g--,d=a[g],f=g):(f=**********,d=void 0),b&512||(D=!0,k=(H??E)(d?f- -1:b>>15&1023||536870912,-1,a,d),f=k+-1)):(f=**********,b&1||(d=g&&a[g-1],A(d)?(g--,f=g,k=0):d=void 0));b=void 0;for(let l=0;l<g;l++){let p=a[l];p!=null&&(p=h(p,e))!=null&&(l>=f?(b??(b={}))[l- -1]=p:c[l]=p)}if(d)for(let l in d)a=d[l],a!=null&&(a=h(a,e))!=null&&(g=+l,g<k?c[g+-1]=a:(b??(b={}))[l]=a);b&&(D?c.push(b):c[f]=b);return c} function G(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=B&&a<=C?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[x]|0;return a.length===0&&b&1?void 0:F(a,b,!1)}b=a[v];const e=b===z;y&&b&&!e&&r(w,3);if(e)return I(a);return}return a}let H;function I(a){a=a.g;return F(a,a[x]|0)};function J(){r(u,5)};function K(a,b,e){if(e!=null&&typeof e!=="string")throw Error();a=a.g;let h=a[x]|0;if(h&2)throw Error();{const g=h&512?0:-1,d=b+g;var c=a.length-1;d>=c&&h&256?a[c][b]=e:d<=c?a[d]=e:e!==void 0&&(c=h>>15&1023||536870912,b>=c?e!=null&&(a[c+g]={[b]:e},a[x]=h|256):a[d]=e)}};var L=class{constructor(a){a:{if(a==null){var b=96;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[x]|0;8192&b||!(64&b)||2&b||J();if(b&1024)throw Error("farr");if(b&64){b&16384||(a[x]=b|16384);var e=a;break a}var h=a;b|=64;var c=h.length;if(c){var g=c-1;c=h[g];if(A(c)){b|=256;const d=b&512?0:-1;g-=d;if(g>=1024)throw Error("pvtlmt");for(e in c){const f=+e;if(f<g)h[f+d]=c[e],delete c[e];else break}b=b&-33521665|(g&1023)<<15}}}a[x]=b|16384;e=a}this.g=e}toJSON(){return I(this)}}; L.prototype[v]=z;L.prototype.toString=function(){return this.g.toString()};var M=class extends L{};function N(a=window){return a};var O=/#|$/;const P=function(a,b=null){return b&&b.getAttribute("data-jc")===String(a)?b:document.querySelector(`[${"data-jc"}="${a}"]`)}(103,document.currentScript);if(P==null)throw Error("JSC not found 103");const Q={},R=P.attributes;for(let a=R.length-1;a>=0;a--){const b=R[a].name;b.indexOf("data-jcp-")===0&&(Q[b.substring(9)]=R[a].value)} (function(a,b,e){var h=window;a&&b&&e&&h.ReportingObserver&&h.fetch&&(new h.ReportingObserver((c,g)=>{c=c[0];if(c?.body?.id==="HeavyAdIntervention"){c=(c.body.message?.indexOf("network")||0)>0?e:b;var d=a.search(O);var f;b:{for(f=0;(f=a.indexOf("ad_signals",f))>=0&&f<d;){var k=a.charCodeAt(f-1);if(k==38||k==63)if(k=a.charCodeAt(f+10),!k||k==61||k==38||k==35)break b;f+=11}f=-1}k=f;if(k<0)d=null;else{f=a.indexOf("&",k);if(f<0||f>d)f=d;d=decodeURIComponent(a.slice(k+11,f!==-1?f:0).replace(/\+/g," "))}d? (navigator.sendBeacon("https://pagead2.googlesyndication.com/pagead/gen_204/?id=fledge_interactions&label="+c),c={h:d,label:c},d=new M,c!=null&&(c.h!=null&&K(d,1,c.h),c.m!=null&&K(d,3,c.m),c.label!=null&&K(d,6,c.label),c.j!=null&&K(d,7,c.j),c.i!=null&&K(d,8,c.i),c.l!=null&&K(d,11,c.l)),N(m).fence?.reportEvent({eventType:"interaction",eventData:JSON.stringify(I(d)),destination:["buyer"]})):h.fetch(`${a}&label=${c}`,{keepalive:!0,method:"get",mode:"no-cors"});g.disconnect()}},{types:["intervention"], buffered:!0})).observe()})(Q.base_url,Q.cpu_label,Q.net_label);}).call(this);</script><script id="googleActiveViewDisplayScript" src="./f(3).txt"></script><script type="text/javascript">osdlfm();</script><script data-jc="70" src="./f(4).txt" async="" data-jc-version="r20250327" data-jcp-expt-ids="" data-jcp-extra-meta="[]" data-jcp-correct-redirect-url-for-och-15-click="false" data-initialized="true"></script> <script data-jc="56" data-jc-version="r20250327">(function(){'use strict';/*  Copyright The Closure Library Authors. SPDX-License-Identifier: Apache-2.0 */ var k=this||self;var l;function aa(a){k.setTimeout(()=>{throw a;},0)};function p(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};let q=void 0;function t(a,b){if(a!=null){var c=q??(q={});var f=c[a]||0;f>=b||(c[a]=f+1,a=Error(),p(a,"incident"),aa(a))}};function v(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var ba=v(),w=v("m_m",!0),ca=v();const x=v("jas",!0);var y;const z=[];z[x]=55;y=Object.freeze(z);const da=typeof w==="symbol";var A={};function B(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object};const ea=BigInt(Number.MIN_SAFE_INTEGER),fa=BigInt(Number.MAX_SAFE_INTEGER);const C=Number.isFinite;function D(a){if(!C(a))throw a=Error("enum"),p(a,"warning"),a;return a|0};function ha(a){return a};function E(a,b,c){var f=ia;c=c?!!(b&32):void 0;const e=[];var d=a.length;let g,h,n,r=!1;b&64?(b&256?(d--,g=a[d],h=d):(h=**********,g=void 0),b&512||(r=!0,n=(ja??ha)(g?h- -1:b>>15&1023||536870912,-1,a,g),h=n+-1)):(h=**********,b&1||(g=d&&a[d-1],B(g)?(d--,h=d,n=0):g=void 0));b=void 0;for(let m=0;m<d;m++){let u=a[m];u!=null&&(u=f(u,c))!=null&&(m>=h?(b??(b={}))[m- -1]=u:e[m]=u)}if(g)for(let m in g)a=g[m],a!=null&&(a=f(a,c))!=null&&(d=+m,d<n?e[d+-1]=a:(b??(b={}))[m]=a);b&&(r?e.push(b):e[h]=b);return e} function ia(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=ea&&a<=fa?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[x]|0;return a.length===0&&b&1?void 0:E(a,b,!1)}b=a[w];const c=b===A;da&&b&&!c&&t(ca,3);if(c)return F(a);return}return a}let ja;function F(a){a=a.g;return E(a,a[x]|0)};function ka(){t(ba,5)};function G(a,b,c){const f=b&512?0:-1,e=1+f;var d=a.length-1;if(e>=d&&b&256)return a[d][1]=c,b;if(e<=d)return a[e]=c,b;c!==void 0&&(d=b>>15&1023||536870912,1>=d?c!=null&&(a[d+f]={[1]:c},b|=256,a[x]=b):a[e]=c);return b}function H(a){return!!(2&a)&&!!(4&a)||!!(1024&a)}function I(a,b){2&a&&(a|=16);var c;2&b?c=a|2:c=a&-3;a=c;return(a|32)&-1025}function J(a,b){32&b||(a&=-33);return a};var K=class{constructor(a){a:{if(a==null){var b=96;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[x]|0;8192&b||!(64&b)||2&b||ka();if(b&1024)throw Error("farr");if(b&64){b&16384||(a[x]=b|16384);var c=a;break a}var f=a;b|=64;var e=f.length;if(e){var d=e-1;e=f[d];if(B(e)){b|=256;const g=b&512?0:-1;d-=g;if(d>=1024)throw Error("pvtlmt");for(c in e){const h=+c;if(h<d)f[h+g]=e[c],delete e[c];else break}b=b&-33521665|(d&1023)<<15}}}a[x]=b|16384;c=a}this.g=c}toJSON(){var a=F(this);return a}}; K.prototype[w]=A;K.prototype.toString=function(){return this.g.toString()};function L(a,b){var c=window;c.addEventListener&&c.addEventListener(a,b,!1)};function M(a){let b=0;for(const c in a)b++};/*  Copyright Google LLC SPDX-License-Identifier: Apache-2.0 */ let N=globalThis.trustedTypes,O;function la(){let a=null;if(!N)return a;try{const b=c=>c;a=N.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a};var ma=class{constructor(a){this.g=a}toString(){return this.g+""}};var P=class{constructor(a){this.g=a}toString(){return this.g}};class Q{constructor(a){this.j=a}}function R(a){return new Q(b=>b.substr(0,a.length+1).toLowerCase()===a+":")}const na=[R("data"),R("http"),R("https"),R("mailto"),R("ftp"),new Q(a=>/^[^:]*([/?#]|$)/.test(a))];var oa=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function pa(a=document){a=a.querySelector?.("script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};function qa(a,b){if(b instanceof ma)b=b.g;else throw Error("");a.src=b;(b=pa(a.ownerDocument))&&a.setAttribute("nonce",b)};function ra(a=document){return a.createElement("img")};function sa(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)}function ta(a){this.g=a||k.document||document};let S=null;function ua(){const a=k.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function va(){const a=k.performance;return a&&a.now?a.now():null};var wa=class{constructor(a,b){var c=va()||ua();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const T=k.performance,xa=!!(T&&T.mark&&T.measure&&T.clearMarks),U=function(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}(()=>{var a;if(a=xa){var b;a=window;if(S===null){S="";try{let c="";try{c=a.top.location.hash}catch(f){c=a.location.hash}c&&(S=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=S;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function ya(a){a&&T&&U()&&(T.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),T.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};const V=new class{constructor(a,b){this.g=[];this.l=b||k;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.g=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.i=U()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.i)return null;a=new wa(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;T&&U()&&T.mark(b);return a}end(a){if(this.i&&typeof a.value==="number"){a.duration=(va()||ua())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;T&&U()&&T.mark(b);!this.i||this.g.length> 2048||this.g.push(a)}}}(1,window);function za(){window.google_measure_js_timing||(V.i=!1,V.g!==V.l.google_js_reporting_queue&&(U()&&Array.prototype.forEach.call(V.g,ya,void 0),V.g.length=0))}typeof window.google_srt!=="number"&&(window.google_srt=Math.random());window.document.readyState==="complete"?za():V.i&&L("load",()=>{za()});var W=(a,b,c,f,e)=>{if(e)c=a+("&"+b+"="+c);else{var d="&"+b+"=";let g=a.indexOf(d);g<0?c=a+d+c:(g+=d.length,d=a.indexOf("&",g),c=d>=0?a.substring(0,g)+c+a.substring(d):a.substring(0,g)+c)}return c.length>6E4?f!==void 0?W(a,b,f,void 0,e):a:c};M({H:0,G:1,C:2,o:3,D:4,u:5,F:6,A:7,B:8,m:9,v:10,I:11});M({K:0,L:1,J:2});function X(a){var b=new Aa;if((b.g[x]|0)&2)throw Error();var c=b.g;b=c[x]|0;const f=2&b?1:2;var e=1+(b&512?0:-1);var d=c.length-1;e>=d&&b&256?e=c[d][1]:e<=d?e=c[e]:e=void 0;e=Array.isArray(e)?e:y;d=e[x]|0;var g=4&d?!1:!0;if(g){4&d&&(e=[...e],d=I(d,b),b=G(c,b,e));let r=g=0;for(;g<e.length;g++){var h=e[g];h=h==null?h:C(h)?h|0:void 0;h!=null&&(e[r++]=h)}r<g&&(e.length=r);d===0&&(d=I(d,b),d|=16);d|=21;d&=-6145;e[x]=d;2&d&&Object.freeze(e)}f===1||f===4&&32&d?H(d)||(b=d,d|=2,d!==b&&(e[x]=d),Object.freeze(e)): (f===2&&H(d)&&(e=[...e],d=I(d,b),d=J(d,b),e[x]=d,b=G(c,b,e)),H(d)||(c=d,d=J(d,b),d!==c&&(e[x]=d)));b=e;if(Array.isArray(a)){var n=a.length;for(c=0;c<n;c++)b.push(D(a[c]))}else for(n of a)b.push(D(n))}var Aa=class extends K{};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);X([1,8,9,10,11,12,2,3,4,5,15,16,19,20,21,23]);X([1,6,7,9,10,11,12,2,3,4,5,13,14,18,19,20,21,23]);X([1,6,7,9,10,11,12,22,2,3,4,5,13,14,17,18,19,20,21,23]);new Aa;function Ba(a,b,c,f,e,d){if(window.css)window.css(b,c,f,e,d);else if(a){a:if(b=W(a.href,c,f,d,e),!(b instanceof P)){for(c=0;c<na.length;++c)if(f=na[c],f instanceof Q&&f.j(b)){b=new P(b);break a}b=void 0}if(b instanceof P)if(b instanceof P)b=b.g;else throw Error("");else b=oa.test(b)?b:void 0;b!==void 0&&(a.href=b)}};function Ca(a){a=a===null?"null":a===void 0?"undefined":a;O===void 0&&(O=la());var b=O;return new ma(b?b.createScriptURL(a):a)};function Da(a=document){var b=Ea,c=Fa;const f=sa((a?new ta(a.nodeType==9?a:a.ownerDocument||a.document):l||(l=new ta)).g,"SCRIPT");f.type="text/javascript";c&&(f.onreadystatechange!==void 0?f.onreadystatechange=()=>{if(f.readyState==="complete"||f.readyState==="loaded")try{c&&c()}catch(d){}}:f.onload=c);qa(f,Ca(b));const e=a.getElementsByTagName("head")[0];if(e)try{k.setTimeout(()=>{e.appendChild(f)},0)}catch(d){}};let Ea,Ga,Y,Ha,Z; var Ia=()=>{var a=window,b=Ia;a.removeEventListener&&a.removeEventListener("load",b,!1);!Z&&(a=sa(document,"IFRAME"),a.frameBorder="0",a.style.height=0,a.style.width=0,a.style.position="absolute",Z=a,document.body&&(document.body.appendChild(a),a=Z))&&(a=a.contentWindow)&&(Y="1",a.document.open(),a.document.write("<!doctype html><html><head></head><body></body></html>"),a.document.close(),Da(a.document))},Fa=()=>{var a=Z;if(a&&(a=a.contentWindow))if(Y="",a.botguard){var b=a.botguard.bg;if(b)try{Ja(()=> {Ha=new b(Ga)})}catch(c){Y="5"}else Y="3"}else Y="2"},Ka=()=>{let a=null;try{a=window.rvdt,typeof a==="number"?a=a<36E5?""+a:"M":a=null}catch(b){}return a},Ja=a=>{window.wrpfc=a;window.wrpfc()},La=()=>{var a=Ha;if(Y)return Y;if(!a)return"5";if(!a.invoke)return"4";let b;try{Ja(()=>{a.invoke(c=>{b=c})})}catch(c){return"6"}return b&&b.length?b.length<3?"7":b.length>2550?(L("unload",()=>{var c=["bg",b],f=["id","bg"];if(f&&c&&f.length&&c.length&&f.length===c.length){var e=["https://","pagead2.googlesyndication.com", "/pagead/gen_204"],d="?";for(let g=0;g<f.length;g++)e.push(d+f[g]+"="+c[g]),d="&";c=e.join("");f=window;f.google_image_requests||(f.google_image_requests=[]);e=ra(f.document);e.src=c;f.google_image_requests.push(e)}}),"8"):b:"6"};window.bga=(a,b)=>{Ea=a;Ga=b;Y="0";L("load",Ia)};window.bgy=a=>{const b=Ka();b&&(a=W(a,"rvdt",b));return W(a,"bg",La(),"9")};window.bgz=a=>{const b=document.getElementById(a);if(b){var c=Ka();c&&Ba(b,a,"rvdt",c);Ba(b,a,"bg",La(),!1,"9")}};}).call(this);</script><script>bga('https://pagead2.googlesyndication.com/bg/x5qs4mqv7HuBQU_F7im1iDqvILL6GDzrDIuXHYsd5to.js','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');</script><script type="text/javascript">(function() {if (!window.GoogleUzGyiZ || typeof window.GoogleUzGyiZ.push !== 'function') {window.GoogleUzGyiZ = [];}window.GoogleUzGyiZ.push({'0': '0','1': '0a6fc7d4-10ac-486b-a017-1e42a30d7e88','2': '1'});var gfs = document.createElement('script');gfs.type = 'text/javascript';gfs.async = true;gfs.src = '';document.body.appendChild(gfs);})();</script><script type="text/javascript" async="" src="https://googleads.g.doubleclick.net/pagead/ads?client=ca-pub-1076724771190722&amp;output=html&amp;h=600&amp;slotname=4787882818&amp;adk=944469691&amp;adf=1401350160&amp;pi=t.ma~as.4787882818&amp;w=300&amp;abgtt=6&amp;fwrn=4&amp;fwrnh=100&amp;lmt=1743258566&amp;rafmt=1&amp;format=300x600&amp;url=https%3A%2F%2Fblog.csdn.net%2Fweixin_45433817%2Farticle%2Fdetails%2F138043455&amp;fwr=0&amp;fwrattr=true&amp;rpe=1&amp;resp_fmts=4&amp;wgl=1&amp;uach=********************************************************************************************************************************************************************************************************************&amp;dt=1743258566088&amp;bpp=4&amp;bdt=1354&amp;idt=462&amp;shv=r20250327&amp;mjsv=m202503250101&amp;ptt=9&amp;saldr=aa&amp;abxe=1&amp;cookie=ID%3Df0dde626079003d6%3AT%3D1741286283%3ART%3D1743258383%3AS%3DALNI_MZuvdz-Rr4oaINTs1sYdqEKK9Lusg&amp;gpic=UID%3D00001057a6020297%3AT%3D1741286283%3ART%3D1743258383%3AS%3DALNI_MYLjzy157VqG8I5BXX2ajRDcMVMpA&amp;eo_id_str=ID%3Dc5da87790f8b0361%3AT%3D1741286283%3ART%3D1743258383%3AS%3DAA-AfjZFqiO7ONQ6lXzzkSO-v4ym&amp;prev_fmts=0x0&amp;nras=1&amp;correlator=8631188229818&amp;frm=20&amp;pv=1&amp;u_tz=480&amp;u_his=1&amp;u_h=1152&amp;u_w=2048&amp;u_ah=1112&amp;u_aw=2048&amp;u_cd=24&amp;u_sd=1.25&amp;dmc=8&amp;adx=203&amp;ady=1828&amp;biw=2033&amp;bih=991&amp;scr_x=0&amp;scr_y=100&amp;eid=95344787%2C95354565%2C95356500%2C95356504%2C31090358%2C95355301%2C95355965%2C95356928&amp;oid=2&amp;pvsid=1799801240911552&amp;tmod=1028903326&amp;uas=0&amp;nvt=1&amp;ref=https%3A%2F%2Fwww.baidu.com%2Flink%3Furl%3D-qL3IANpTt93EYjJagDsTSTnov5atGva3RcWAWF6JiIS8DA4uxmTFwQx0b-tPOesWphE0mwfuiOkP0hEhyehTGrnGirr3Ypx6E2-RIc-LjC%26wd%3D%26eqid%3Db0b094d80384f38e0000000667e80308&amp;fc=1920&amp;brdim=0%2C0%2C0%2C0%2C2048%2C0%2C2048%2C1112%2C2048%2C991&amp;vis=1&amp;rsz=%7C%7CpeEbr%7C&amp;abl=CS&amp;pfx=0&amp;fu=128&amp;bc=31&amp;bz=1&amp;td=1&amp;tdf=2&amp;psd=W251bGwsW251bGwsbnVsbCxudWxsLCJkZXByZWNhdGVkX2thbm9uIl0sbnVsbCwzXQ..&amp;nt=1&amp;ifi=2&amp;uci=a!2&amp;btvi=1&amp;fsb=1&amp;dtd=478"></script><iframe frameborder="0" style="height: 0px; width: 0px; position: absolute;" src="./saved_resource.html"></iframe></body></html>