"use strict";!function(t,i,o){function s(t,i){if(!L||!S[t])return"";var o={};return o.spm=L+"."+S[t],o.extra=JSON.stringify(x.spmExt),void 0!==i&&(o.index=i+1),JSON.stringify(o).replace(/"/g,"&quot;")}function e(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}function r(){for(var t={},i=0;i<arguments.length;i++)o.extend(!0,t,arguments[i]);return t}function n(t){var o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1500,s=i.createElement("div");s.innerHTML=t,s.style.cssText="min-width:124px;padding:0 8px;opacity: 0.8;height: 40px;background:rgba(34,34,38,1);color: rgb(255, 255, 255);line-height: 40px;text-align: center;border-radius: 4px;position: fixed;top: 35%;left:50%;transform: translateX(-50%);z-index: 999999;font-size: 16px;",i.getElementById("user-ordertip").appendChild(s),setTimeout(function(){s.style.webkitTransition="-webkit-transform 0.5s ease-in, opacity 0.5s ease-in",s.style.opacity="0",setTimeout(function(){i.getElementById("user-ordertip")&&i.getElementById("user-ordertip").removeChild(s)},500)},o)}function a(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1500,i='\n      <div class="loading_warp" id="user-ordertip-loading">\n        <div class="icon_box">\n          <img class="rotating" src="'+w+'/icon-paying.png"/>\n        </div>\n        <div class="pay_msg">查询中...</div>\n      </div>\n    ';o(".ordertip_dialog").append(i).find(".ordertip_content").addClass("noScroll"),setTimeout(function(){o("#user-ordertip-loading").remove(),o(".ordertip_dialog .ordertip_content").removeClass("noScroll")},t)}function d(){var t=c("api_env")||"",i="https://mall.csdn.net/",o=/^beta|test|loc[a-z]*/;return t.match(o)?i="https://test-mall.csdn.net/":t.match(/^pre-|pre[a-z]*/)&&(i="https://pre-mall.csdn.net/"),i}function c(t){var i=new RegExp("(^|&)"+t+"=([^&]*)(&|$)","i"),o=window.location.search.substr(1).match(i);return null!=o?unescape(o[2]):""}function l(t,s){QRCode?s.pay_url&&(W.priceInfo=s,o("#ordertip_qr_code").html(""),o("#ordertip_notify").hide(),o("#pay_btn").attr("href",s.pay_url),new QRCode(i.getElementById("ordertip_qr_code"),{text:s.pay_url,width:120,height:120}),o("#user-ordertip .other_pay").removeClass("none").find("a").attr("href",s.pay_url)):void 0}function p(){f("pay_error","获取失败,点击重试","code_2")}function g(){f("pay_time_out","点击重新获取","")}function h(){f("pay_error","已扫码<br>请在手机端操作","")}function _(t){var i=!(!1===W.show_params.needLoading),o=W.show_params.successProcess||"reload";i&&a(),setTimeout(function(){n("支付成功","1000"),setTimeout(function(){k?(W.close(),k(t)):"reload"===o?window.location.reload():"jump"===o&&(window.location.href=u(t))},1e3)},i?1500:0)}function u(t){return"success"===t.errorMessage&&t.jumpUrl&&1===t.status?t.jumpUrl:!t.need_third_pay&&t.paySuccessUrl?t.paySuccessUrl:"https://mall.csdn.net/myorder"}function f(t,i,s){o("#ordertip_notify").show().html('<img class="pay_icon" src="https://csdnimg.cn/release/download/images/'+t+'.png"/><span class="pay_tip">'+i+"</span>"),o("#ordertip_qr_code").html('<img src="https://csdnimg.cn/public/static/img/csdn-userimg250.gif" width="145" height="145"/>'),"pay_time_out"==t||"pay_error"==t?o("#ordertip_notify .pay_icon,#ordertip_notify .pay_tip").on("click",function(){W.getPayCode()}):o("#ordertip_notify .pay_icon,#ordertip_notify .pay_tip").off("click")}function v(){o.ajax({url:"https://mp-activity.csdn.net/activity/report",type:"post",contentType:"application/json; charset=utf-8",xhrFields:{withCredentials:!0},data:JSON.stringify({pageUrl:"https://blog-send-vip-coupon/",action:"pageView",platform:"pc"}),dataType:"json",success:function(t){return void 0,t.data.matched,!1},error:function(t){void 0}})}function m(){this.userInfo={},this.tabList=[{icon:w+"/tab1-icon.png",title:"CSDN会员",desc:"下载",key:"vipForPopup",list:[]},{icon:w+"/tab2-icon.png",title:"超级会员",desc:"专栏、课程、下载",key:"superVipForPopup",list:[]}],this.activeTab="vipForPopup",this.goodsInfo={},this.goodsList=[],this.activeGoodsId="",this.isUseBalance=!0,this.priceInfo={},this.errType="",this.reportExt={},this.spmExt={},this.navList=[],this.payMethods=[],this.price=0,this.payUrl="",this.params={},this._cart=null,this.show_params={},this.goodsTransX=0,this.listBoxWidth=626,this.listScrollWidth=0,this.goodsTabWidth=176,this.rightsTransX=0,this.rightsBoxWidth=626,this.rightsScrollWidth=0,this.rightsTabWidth=136,this.vipRightsObj={},x=this}function b(){W.close()}var y="https://g.csdnimg.cn/user-ordertip/",w=y+"5.0.3/images",x=null,T=!1,k=null,I="",C=!0,L="",S={tab1:"3001.9858",tab2:"3001.9859",goods:"3001.9857",goods2:"3001.9938",closeBtn:"3001.9890",rule:"3001.9891",rights:"3001.9860"};try{var B=o("meta[name=report]");L=JSON.parse(B.attr("content")||"{}").spm}catch(t){void 0}var D=function(t){var o=i.cookie;return o&&function(){var i,s={};o=o.split("; ");for(var e=0,r=o.length;e<r&&(i=o[e].split("="),!(i.length>0&&(i[0]===t&&(s.key=i[0],s.value=i[1],s.status=!0),"key"in s)));e++);return"key"in s&&s}()};!function(t){var o=i.createElement("link");o.rel="stylesheet",o.type="text/css",o.href=t,i.getElementsByTagName("head")[0].appendChild(o)}("https://g.csdnimg.cn/user-ordertip/5.0.3/user-ordertip.css");m.prototype={constructor:m,close:function(){T=!1,this._cart&&this._cart.clearTimer(),o("#user-ordertip-box").remove()},show:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return D("UserName").value?window.csdn.cart?window.csdn.cartClass?(this.show_params=t,"function"==typeof t.get_pay_success_callback&&(k=t.get_pay_success_callback),this.sale=t.sale_source||c("sale_source"),this.reportExt=t.report_ext||{},this.spmExt=t.spmExt||{},t.tabList&&t.tabList.length>0&&(this.tabList=t.tabList),t.tabs&&(this.activeTab=t.tabs[0],this.tabList=this.tabList.filter(function(i){return-1!==t.tabs.indexOf(i.key)})),0===this.tabList.length?void void 0:(this.init(),void this.reportDialogView())):void void 0:void void 0:void void 0},reportDialogView:function(){try{window.csdn.report&&window.csdn.report.reportView({spm:"3001.10031",extra:JSON.stringify(this.spmExt||{})})}catch(t){void 0}},init:function(){this._cart=new window.csdn.cartClass,this.getVipGoodsList(function(t){x.getGoodsInfo(t,function(t){x.goodsInfo=t,x.goodsCodeData(t.new_price,t.available_amount),x.activeGoodsId=x.goodsInfo.goods_id,x.initDialog(),x.getPayCode(),x.getUserInfo()},!1)})},bindEvents:function(){o(i).off("click",".ordertip_dialog .user_balance").on("click",".ordertip_dialog .user_balance",function(){if(o(".ordertip_dialog .user_balance").hasClass("disable"))return void(x.isUseBalance=!1);o(".ordertip_dialog .user_balance").hasClass("active")?(o(".ordertip_dialog .user_balance").removeClass("active"),x.isUseBalance=!1):(o(".ordertip_dialog .user_balance").addClass("active"),x.isUseBalance=!0),x.getGoodsInfo(x.goodsInfo,function(t){x.goodsInfo=t,x.goodsCodeData(x.goodsInfo.new_price,x.goodsInfo.available_amount),x.changePriceHtml(x.price),x.getPayCode(x.goodsInfo)})}),o(i).off("click","#pay_btn").on("click","#pay_btn",function(t){var i=D("UserName").value;if(C){C=!1;var s={product_id:x.goodsInfo.product_id,goods_id:x.goodsInfo.goods_id,flag:x.goodsInfo.flag,goodsSource:x.goodsInfo.goodsSource||"",is_use_balance:2,sale_source:x.sale,request_id:i+"_"+I+"_"+x.goodsInfo.product_id+"_"+x.goodsInfo.goods_id+"_"+x.goodsInfo.flag};return void x._cart.quickBuy({params:s,get_pay_success_callback:function(t){C=!0,200==t.code&&!1===t.data.need_third_pay&&_(t.data)},error_function:function(t){C=!0,400103012==t.status&&o(".ordertip_item.active").trigger("click"),void 0}})}}),o(i).on("click",".ordertip_header_btn",function(t){x.close()}),o(i).off("click",".user-ordertip .list_btn.btn_left").on("click",".user-ordertip .list_btn.btn_left",function(t){x.moveTab("left")}),o(i).off("click",".user-ordertip .list_btn.btn_right").on("click",".user-ordertip .list_btn.btn_right",function(t){x.moveTab("right")}),o(i).off("click",".user-ordertip .rights_btn.rights_btn_left").on("click",".user-ordertip .rights_btn.rights_btn_left",function(t){x.moveRightsCard("left")}),o(i).off("click",".user-ordertip .rights_btn.rights_btn_right").on("click",".user-ordertip .rights_btn.rights_btn_right",function(t){x.moveRightsCard("right")}),o(i).off("click",".ordertip_tab_item").on("click",".ordertip_tab_item",function(t){var i=o(this).attr("data-key")||"";if(!o(this).hasClass("active")){x.activeTab=i,o(this).addClass("active").siblings().removeClass("active"),x.goodsList=x.tabList.find(function(t){return t.key===i}).list||[],o(".ordertip_dialog .ordertip_c_goodslist_scroll").html(x.setGoodsListHtml()),x.dealGoodsListJt();var s=x.findDefaultGood(x.goodsList).goodsId,e=o(".ordertip_item[data-id="+s+"]"),r=e.attr("data-report-click");e.removeAttr("data-report-click"),e.trigger("click"),setTimeout(function(){e.attr("data-report-click",r)},100)}}),o(i).off("click",".ordertip_item").on("click",".ordertip_item",function(t){var i=JSON.parse(o(this).attr("data-goods")||"{}");if(i=r(i,x.goodsList[i.index].ext),!o(this).hasClass("active"))return o(this).addClass("active").siblings().removeClass("active"),void x.getGoodsInfo(i,function(t){x.goodsInfo=t,1==x.goodsInfo.isContract&&t.available_amount>t.new_price?(x.isUseBalance=!1,x.goodsInfo.hideBalance=!0):t.available_amount>0&&(x.isUseBalance=!0),x.activeGoodsId=t.goods_id,x.goodsCodeData(t.new_price,t.available_amount),x.goodsInfo.contractDesc?o(".ordertip_dialog .ordertip_c_activity").html(decodeURIComponent(x.goodsInfo.contractDesc||"")).removeClass("none").show():o(".ordertip_dialog .ordertip_c_activity").hide(),o(".ordertip_dialog .commodity_box").html("").append(x.setPayPriceHtml()),o(".ordertip_dialog .scan_code").html("").append(x.setPaylistHtml())})})},initDialog:function(){this.bindEvents(),this.renderDialog()},setUserInfoHtml:function(){return x.userInfo.nickname?'<div class="ordertip_user">\n          <img class="ordertip_user_head" src="'+x.userInfo.avatar+'" />\n          <div class="ordertip_user_info">\n            <div class="ordertip_user_name '+(4==x.userInfo.userStatus?"vip":"")+'">'+x.userInfo.nickname+'<img src="'+w+'/vip-icon.png" /></div>\n            <div class="ordertip_user_vipdesc">'+x.userInfo.vipTips+"</div>\n          </div>\n        </div>":""},setVipTabHtml:function(){var t=this,i="";if(this.tabList.length>1){var o="";this.tabList.forEach(function(i,e){o+='<div data-report-click="'+s("tab"+(e+1))+'" class="ordertip_tab_item '+(t.activeTab===i.key?"active":"")+'" data-key="'+i.key+'">\n            <div class="ordertip_tab_title">'+(i.icon?'<img src="'+i.icon+'" />':"")+i.title+'</div>\n            <div class="ordertip_tab_desc">'+i.desc+"</div>\n          </div>"}),i='<div class="ordertip_tab_list">'+o+"</div>"}return i},setGoodsListHtml:function(){var t=this,i="superVipForPopup"===this.activeTab?"goods2":"goods",o="";return this.goodsList.forEach(function(e,r){o+='<div data-report-click="'+s(i,r)+'" class="ordertip_c_l_goodsitem ordertip_item '+(t.activeGoodsId==e.ext.goodsId?"active":"")+" \" data-goods='"+JSON.stringify({index:r,type:"other"})+"' data-id='"+e.ext.goodsId+"'>\n          <span class=\"ordertip_c_l_b_tips "+(e.ext.activityContent?"":"none")+'" >'+e.ext.activityContent+'</span>\n          <div class="ordertip_c_l_b_name">'+(e.name||"").split("：")[0]+'</div>\n          <div class="ordertip_c_l_b_price"><span>¥</span>'+e.ext.unitPrice+"<span>/"+e.ext.availableUnit+'</span></div>\n          <div class="ordertip_c_l_b_activity '+(e.title?"":"none")+'">'+e.title+"</div>\n        </div>"}),o},setPaylistHtml:function(){var t=x.goodsInfo,i=[];t.payTypeList&&t.payTypeList.forEach(function(t){"unionpay"!==t.name&&i.push(t)});for(var o="",s=0;s<i.length;s++)o+='<img class="icon_item '+i[s].name+'" src='+JSON.stringify(i[s].image)+' alt="img">';return o+'<span class="pay_intro">扫码支付</span>'},setPayPriceHtml:function(){var t=(x.priceInfo,x.goodsInfo);return'<ul class="commodity_desc">\n                    <li class="amount_actually">\n                      实付：<span class="num"><b>'+x.price+'</b>元</span><span class="num bg-box '+(t.totalDiscountPrice>0?"":"none")+'">已优惠¥'+t.totalDiscountPrice+'</span>\n                    </li>\n                    <li class="voucher '+(t.cashCouponVoList&&t.cashCouponVoList.length>0?"":"none")+'" id="useVoucherBtn"><div class="voucher-title ">有'+t.cashCouponVoList.length+'张代金券可用，选择代金券</div>\n                    </li>\n                    <li class="gift none '+(t.discount_msg?"block":"none")+'">\n                      <img src="'+w+'/enjoy.png" alt="">\n                      <span>'+t.discount_msg+'</span>\n                    </li>\n\n                    <li class="user_balance '+(t.hideBalance?"none":"")+" "+(t.available_amount>0?x.isUseBalance?"active":"":"disable")+" "+(19===t.flag?"none":"")+'" >\n                      <span class="unchecked"></span>\n                      <img src="'+w+'/checked.png" alt="" class="checked">\n                      钱包余额 <b class="num">'+t.available_amount+'</b>\n                    </li>\n                    <li class="user_balance tips '+(t.hideBalance?"":"none")+'">\n                      连续包月暂不支持余额抵扣，请使用微信或支付宝付款，请您谅解！\n                    </li>\n                    <li class="pay_count_time">支付倒计时：<span>04.59.9</span></li>\n                  </ul>'},setCountDown:function(){function t(){var t=e.countDown(i);s.find("span").text(t.minutes+":"+t.seconds+":"+t.mSeconds)}window._orderPayment_countDown_timer&&clearInterval(window._orderPayment_countDown_timer);var i=Date.now()+3e5,s=o("#user-ordertip-box .pay_count_time"),e=this;window._orderPayment_countDown_timer=setInterval(function(){t()},100)},countDown:function(t){function i(t){return t>=10?t+"":"0"+t}var o=new Date(t),s=o.getTime()-Date.now();if(s<=0)return{days:0,hours:0,minutes:0,seconds:0,mSeconds:0};var e=Math.floor(s%864e5/36e5),r=Math.floor(s%36e5/6e4),n=Math.floor(s%6e4/1e3),a=Math.floor(s%1e3/100);return{hours:i(e),minutes:i(r),seconds:i(n),mSeconds:a}},setVipRightsHtml:function(t){if(!x.vipRightsObj[t]||0===x.vipRightsObj[t].length)return"";var i="";return x.vipRightsObj[t].forEach(function(t,o){i+='<a data-report-click="'+s("rights",o)+'" href="'+t.permissionUrl+'" target="_blank" class="ordertip_rights_item">\n          <img src="'+t.permissionIcon+'" class="ordertip_r_item_img">\n          <div class="ordertip_r_item_info">\n            <div class="ordertip_r_item_title ellipis">'+t.permissionName+'</div>\n            <div class="ordertip_r_item_desc ellipis">'+t.permissionTips+"</div>\n          </div>\n        </a>"}),'<div class="ordertip_rights_head">CSDN会员特权</div>\n        <span class="rights_btn rights_btn_right">\n          <img src="'+w+'/icon-right.png">\n        </span>\n        <span class="rights_btn rights_btn_left">\n          <img src="'+w+'/icon-left.png">\n        </span>\n        <div class="ordertip_rights_scroll">\n          '+i+"\n        </div>"},renderDialog:function(){this.createMask();var i=this.setVipTabHtml(),r=this.setGoodsListHtml(),n=this.setPaylistHtml(),a=this.setPayPriceHtml(),d=this.show_params.learnNum||0,c='<div id="user-ordertip" class="user-ordertip noselect">\n          <div class="ordertip_dialog">\n              <div class="ordertip_header">\n                \n                <span data-report-click="'+s("closeBtn")+'" class="ordertip_header_btn"> + </span>\n              </div>\n              '+i+'\n              <div class="ordertip_content">\n                <div class="ordertip_c_goodslist ">\n                  <span class="list_btn btn_left">\n                    <img src="'+w+'/icon-left.png">\n                  </span>\n                  <span class="list_btn btn_right">\n                    <img src="'+w+'/icon-right.png">\n                  </span>\n                  <div class="ordertip_c_goodslist_scroll">\n                    '+r+'\n                  </div>\n                </div>\n                <div class="ordertip_c_activity '+(this.goodsInfo.contractDesc?"":"none")+'">'+(decodeURIComponent(this.goodsInfo.contractDesc)||"")+'</div>\n\n                <div class="intro-box">\n                  <div class="intro intro1 '+(d>=10?"":"none")+'">\n                  该篇文章</br>已有<span class="light-text">'+(d>1e5?"100,000+":e(d))+'</span>人学习\n                  </div>\n                  <div class="intro intro2">\n                  开通CSDN会员</br>更有<span class="light-text">200万＋优质VIP文章免费读</span>\n                  </div>\n                </div>\n\n                <div class="ordertip_paybox">\n                  <div class="recharge_mode '+(0!=x.price?"block":"none")+'">\n                    <div class="recharge_mode_qr_code" id="ordertip_qr_code">\n                      <img class="loading" src="'+w+'/loading.gif" width="50" height="50">\n                    </div>\n                    <div id="ordertip_notify" class="pay_notify"></div>\n                    <p class="scan_code">\n                      '+n+'\n                    </p>\n                  </div>\n                  <div class="recharge_mode_btn '+(0==x.price?"block":"none")+'">\n                    <div class="pay_btn" id="pay_btn">确定支付</div>\n                  </div>\n                  <div class="commodity_box">\n                    '+a+'\n                  </div>\n                </div>\n                <div class="ordertip_rights_box">\n                </div>\n                <div class="ordertip_agreement">\n                  购买即同意<a data-report-click="'+s("rule")+'" href="https://blog.csdn.net/blogdevteam/article/details/111173049" target="_blank">《CSDN会员服务协议》</a>\n                </div>\n              </div>\n            </div>\n          <div class="ordertip_mask"></div>\n      </div>\n      ',l=o(c);o("#user-ordertip-box").append(l),x.dealGoodsListJt(),x.dealRightsListJt(),t.report&&window.csdn.report.viewCheck()},dealGoodsListJt:function(){x.goodsTransX=0,x.listScrollWidth=o(".ordertip_c_goodslist_scroll").width()||0,x.listBoxWidth=o(".ordertip_c_goodslist").width()||626,x.listBoxWidth>=x.listScrollWidth?o("#user-ordertip-box .list_btn").hide():o("#user-ordertip-box .btn_left").hide().siblings(".btn_right").show(),o(".ordertip_c_goodslist_scroll").css("transform","translateX(0px)")},dealRightsListJt:function(){x.rightsTransX=0,x.rightsScrollWidth=o(".ordertip_rights_scroll").width()||0,x.rightsBoxWidth=o(".ordertip_rights_box").width()||626,x.rightsBoxWidth>=x.rightsScrollWidth?o("#user-ordertip-box .rights_btn").hide():o("#user-ordertip-box .rights_btn_left").hide().siblings(".rights_btn_right").show(),o(".ordertip_rights_scroll").css("transform","translateX(0px)")},createMask:function(){var t=i.createElement("div");t.id="user-ordertip-box",i.body.appendChild(t)},getVipGoodsList:function(t){o.ajax({url:d()+"mp/mallorder/api/internal/goods/showListV2?abTest=Y&showType=vipForPopup,superVipForPopup",type:"GET",contentType:"application/json",xhrFields:{withCredentials:!0},success:function(i){i.data?(x.tabList.forEach(function(t){t.list=i.data[t.key]||[]}),x.goodsList=x.tabList.find(function(t){return t.key===x.activeTab}).list||[],t&&t(x.findDefaultGood(x.goodsList))):void 0},error:function(t){void 0}})},getGoodsInfo:function(t,i){var s=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];t.ext&&delete t.ext,o.ajax({url:d()+"mp/mallorder/api/internal/goods/getGoodsInfo",type:"GET",dataType:"json",data:r(t,{goods_id:t.goodsId,product_id:t.productId}),contentType:"application/json",xhrFields:{withCredentials:!0},success:function(o){200==o.code&&o.data?(19===o.data.flag&&(x.isUseBalance=!1),i&&i(o.data),x.changePriceHtml(x.price),s&&x.getPayCode(o.data),x.getVipRights(t.rightsGoodsId)):void 0},error:function(t){void 0}})},getVipRights:function(t){if(t)return x.vipRightsObj[t]?(o("#user-ordertip .ordertip_rights_box").html(x.setVipRightsHtml(t)),void x.dealRightsListJt()):void o.ajax({url:d()+"mp/mallorder/vip_plugin/vip_card/get_permission?goodsId="+t,type:"GET",contentType:"application/json",xhrFields:{withCredentials:!0},success:function(i){i.data?(x.vipRightsObj[t]=i.data,o("#user-ordertip .ordertip_rights_box").html(x.setVipRightsHtml(t)),x.dealRightsListJt()):void 0},error:function(t){void 0}})},getUserInfo:function(){o.ajax({url:d()+"mp/mallorder/vip_plugin/vip_buy/get_user_info",type:"GET",contentType:"application/json",xhrFields:{withCredentials:!0},success:function(t){t.data?(x.userInfo=t.data,o("#user-ordertip .ordertip_header").prepend(x.setUserInfoHtml())):void 0},error:function(t){void 0}})},goodsCodeData:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=0;s=this.isUseBalance?t-o-i<0?0:(100*t-100*o-100*i)/100:t-o<0?0:(100*t-100*o)/100,this.price=Number(s).toFixed(2)},changePriceHtml:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;Number(t)<=0?(o(".ordertip_dialog .recharge_mode").addClass("none"),o(".ordertip_dialog .recharge_mode_btn").addClass("show").removeClass("none")):(o(".ordertip_dialog .recharge_mode").removeClass("none"),o(".ordertip_dialog .recharge_mode_btn").removeClass("show").addClass("none")),o(".ordertip_dialog .amount_actually .num b").html(t)},getPayCode:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:x.goodsInfo,i={product_id:t.product_id,goods_id:t.goods_id,goodsSource:t.goodsSource||"",flag:t.flag,sale_source:x.sale,report_ext:x.reportExt,is_use_balance:Number(x.isUseBalance),coupon_key:t.coupon_key,cash_coupon_keys:t.cash_coupon_keys,use_cache:!0,success_function:function(t,i){l(t,i),x.setCountDown()},error_function:p,timeout_function:g,payment_function:h,get_pay_success_callback:_};x._cart.qrPay(i)},findDefaultGood:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=t.find(function(t,i,o){return o[i].index=i,1===t.ext.default})||t[0];return r({goods_id:i.ext.goodsId||"",flag:i.ext.flag||"",product_id:i.ext.productId||""},i.ext)},moveTab:function(t){var i=o(".ordertip_c_goodslist_scroll");"left"==t?(x.goodsTransX>=0||Math.abs(x.goodsTransX)<2.5*x.goodsTabWidth?(x.goodsTransX=0,o("#user-ordertip .btn_left").hide()):x.goodsTransX+=x.goodsTabWidth,o("#user-ordertip .btn_right").show(),i.css("transform","translateX("+x.goodsTransX+"px)")):(Math.abs(x.goodsTransX)+x.listBoxWidth>=x.listScrollWidth||x.listScrollWidth-x.listBoxWidth-Math.abs(x.goodsTransX)<2.5*x.goodsTabWidth?(x.goodsTransX=-(x.listScrollWidth-x.listBoxWidth),o("#user-ordertip .btn_right").hide()):x.goodsTransX-=x.goodsTabWidth,o("#user-ordertip .btn_left").show(),x.goodsTransX-=10,i.css("transform","translateX("+x.goodsTransX+"px)"))},moveRightsCard:function(t){var i=o(".ordertip_rights_scroll");"left"==t?(x.rightsTransX>=0||Math.abs(x.rightsTransX)<2.5*x.rightsTabWidth?(x.rightsTransX=0,o("#user-ordertip .rights_btn_left").hide()):x.rightsTransX+=x.rightsTabWidth,o("#user-ordertip .rights_btn_right").show(),i.css("transform","translateX("+x.rightsTransX+"px)")):(Math.abs(x.rightsTransX)+x.rightsBoxWidth>=x.rightsScrollWidth||x.rightsScrollWidth-x.rightsBoxWidth-Math.abs(x.rightsTransX)<2.5*x.rightsTabWidth?(x.rightsTransX=-(x.rightsScrollWidth-x.rightsBoxWidth),o("#user-ordertip .rights_btn_right").hide()):x.rightsTransX-=x.rightsTabWidth,o("#user-ordertip .rights_btn_left").show(),x.rightsTransX-=10,i.css("transform","translateX("+x.rightsTransX+"px)"))}};var W=void 0,X=function(t,i){var o=null;return function(){var s=this,e=arguments;o&&(clearTimeout(o),o=null),o=setTimeout(function(){t.apply(s,e)},i)}}(function(t){void 0,T||(void 0==window._userOrderTip&&v(),T=!0,I=(new Date).getTime(),W=new m,W.show(t),window._userOrderTip=W)},200);window.csdn.userOrderTip={show:X,close:b}}(window.csdn=window.csdn||{},document,jQuery);