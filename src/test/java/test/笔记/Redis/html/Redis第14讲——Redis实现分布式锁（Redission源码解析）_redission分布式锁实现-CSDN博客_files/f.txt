(function(){'use strict';/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var l=this||self;function aa(a){l.setTimeout(()=>{throw a;},0)};function ba(a){ba[" "](a);return a}ba[" "]=function(){};let ca=void 0;function da(a,b){if(a!=null){var c=ca??(ca={});var d=c[a]||0;d>=b||(c[a]=d+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity="incident",aa(a))}};function q(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var ea=q(),fa=q("m_m",!0),ha=q();const r=q("jas",!0);const ia=typeof fa==="symbol";var ja={};function ka(a){a=a[fa];const b=a===ja;ia&&a&&!b&&da(ha,3);return b}function la(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object};const ma=BigInt(Number.MIN_SAFE_INTEGER),na=BigInt(Number.MAX_SAFE_INTEGER);const oa=Number.isFinite;function t(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return oa(a)?a|0:void 0}function w(a){return a==null||typeof a==="string"?a:void 0};function ra(a){return a};function y(a,b,c,d,e){d=d?!!(b&32):void 0;const f=[];var g=a.length;let h,k,n,m=!1;b&64?(b&256?(g--,h=a[g],k=g):(k=4294967295,h=void 0),e||b&512||(m=!0,n=(sa??ra)(h?k- -1:b>>15&1023||536870912,-1,a,h),k=n+-1)):(k=4294967295,b&1||(h=g&&a[g-1],la(h)?(g--,k=g,n=0):h=void 0));let v=void 0;for(let p=0;p<g;p++){let u=a[p];u!=null&&(u=c(u,d))!=null&&(p>=k?(v??(v={}))[p- -1]=u:f[p]=u)}if(h)for(let p in h)a=h[p],a!=null&&(a=c(a,d))!=null&&(g=+p,g<n?f[g+-1]=a:(v??(v={}))[p]=a);v&&(m?f.push(v):f[k]=v);e&&(f[r]= 
b&33522241|(v!=null?290:34));return f}function ta(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=ma&&a<=na?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[r]|0;return a.length===0&&b&1?void 0:y(a,b,ta,!1,!1)}if(ka(a))return A(a);return}return a}let sa;function A(a){a=a.l;return y(a,a[r]|0,ta,void 0,!1)};function ua(){da(ea,5)};function B(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){const d=a[r]|0;if(a.length===0&&d&1)return;if(d&2)return a;var c;if(c=b)c=d===0||!!(d&32)&&!(d&64||!(d&16));return c?(a[r]|=34,d&4&&Object.freeze(a),a):y(a,d,B,b!==void 0,!0)}if(ka(a))return b=a.l,c=b[r]|0,c&2?a:y(b,c,B,!0,!0)};function E(a,b){a=a.l;return va(a,a[r]|0,b)}function va(a,b,c){if(c===-1)return null;const d=c+(b&512?0:-1),e=a.length-1;if(d>=e&&b&256)a=a[e][c];else if(d<=e)a=a[d];else return;return a}function wa(a,b,c,d){const e=b&512?0:-1,f=c+e;var g=a.length-1;f>=g&&b&256?a[g][c]=d:f<=g?a[f]=d:d!==void 0&&(g=b>>15&1023||536870912,c>=g?d!=null&&(a[g+e]={[c]:d},a[r]=b|256):a[f]=d)} 
function xa(a){var b=ya;a=a.l;let c=a[r]|0;const d=va(a,c,1);if(d!=null&&typeof d==="object"&&ka(d))b=d;else if(Array.isArray(d)){const e=d[r]|0;let f=e;f===0&&(f|=c&32);f|=c&2;f!==e&&(d[r]=f);b=new b(d)}else b=void 0;b!==d&&b!=null&&wa(a,c,1,b);return b}function za(a){let b=xa(a);if(b==null)return b;a=a.l;let c=a[r]|0;if(!(c&2)){var d=b;var e=d.l;(e[r]|0)&2&&(d=new d.constructor(y(e,e[r]|0,B,!0,!0)),e=d.l,e[r]&=-3);d!==b&&(b=d,wa(a,c,1,b))}return b} 
function F(a,b){a=E(a,b);return(a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0)??!1}function H(a,b){return w(E(a,b))??""}function I(a,b,c){if(c!=null&&typeof c!=="string")throw Error();a=a.l;let d=a[r]|0;if(d&2)throw Error();wa(a,d,b,c)};var J=class{constructor(a){a:{if(a==null){var b=96;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[r]|0;8192&b||!(64&b)||2&b||ua();if(b&1024)throw Error("farr");if(b&64){b&16384||(a[r]=b|16384);var c=a;break a}var d=a;b|=64;var e=d.length;if(e){var f=e-1;e=d[f];if(la(e)){b|=256;const g=b&512?0:-1;f-=g;if(f>=1024)throw Error("pvtlmt");for(c in e){const h=+c;if(h<f)d[h+g]=e[c],delete e[c];else break}b=b&-33521665|(f&1023)<<15}}}a[r]=b|16384;c=a}this.l=c}toJSON(){return A(this)}}; 
J.prototype[fa]=ja;var ya=class extends J{};var Aa=function(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b[r]|=32;b=new a(b)}return b}}(class extends J{});var Ba=class extends J{};function Ca(a=window){return a};function Da(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}};var Ea={passive:!0},Fa=Da(function(){let a=!1;try{const b=Object.defineProperty({},"passive",{get:function(){a=!0}});l.addEventListener("test",null,b)}catch(b){}return a});function Ga(a){return a?a.passive&&Fa()?a:a.capture||!1:!1}function K(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,Ga(d))};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
let Ha=globalThis.trustedTypes,Ia;function Ja(){let a=null;if(!Ha)return a;try{const b=c=>c;a=Ha.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a};var Ka=class{constructor(a){this.g=a}toString(){return this.g+""}};function La(a=document){a=a.querySelector?.("script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};var Ma=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Na=/#|$/;function Oa(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function Pa(a,b=document){return b.createElement(String(a).toLowerCase())};function Qa(a,b,c=null,d=!1){Ra(a,b,c,d)}function Ra(a,b,c,d){a.google_image_requests||(a.google_image_requests=[]);const e=Pa("IMG",a.document);if(c||d){const f=g=>{c&&c(g);if(d){g=a.google_image_requests;const h=Array.prototype.indexOf.call(g,e,void 0);h>=0&&Array.prototype.splice.call(g,h,1)}e.removeEventListener&&e.removeEventListener("load",f,Ga());e.removeEventListener&&e.removeEventListener("error",f,Ga())};K(e,"load",f);K(e,"error",f)}e.src=b;a.google_image_requests.push(e)};let Sa=0;function Ta(a){return(a=Ua(a,document.currentScript))&&a.getAttribute("data-jc-version")||"unknown"}function Ua(a,b=null){return b&&b.getAttribute("data-jc")===String(a)?b:document.querySelector(`[${"data-jc"}="${a}"]`)} 
function Za(){if(!(Math.random()>.01)){var a=Ua(60,document.currentScript);a=`https://${a&&a.getAttribute("data-jc-rcd")==="true"?"pagead2.googlesyndication-cn.com":"pagead2.googlesyndication.com"}/pagead/gen_204?id=jca&jc=${60}&version=${Ta(60)}&sample=${.01}`;var b=window,c;if(c=b.navigator)c=b.navigator.userAgent,c=/Chrome/.test(c)&&!/Edge/.test(c)?!0:!1;c&&typeof b.navigator.sendBeacon==="function"?b.navigator.sendBeacon(a):Qa(b,a,void 0,!1)}};var $a=document,L=window;function ab(a){return typeof a.className=="string"?a.className:a.getAttribute&&a.getAttribute("class")||""}function bb(a,b){a.classList?b=a.classList.contains(b):(a=a.classList?a.classList:ab(a).match(/\S+/g)||[],b=Array.prototype.indexOf.call(a,b,void 0)>=0);return b}function M(a,b){if(a.classList)a.classList.add(b);else if(!bb(a,b)){const c=ab(a);b=c+(c.length>0?" "+b:b);typeof a.className=="string"?a.className=b:a.setAttribute&&a.setAttribute("class",b)}};function N(a){var b=document;return typeof a==="string"?b.getElementById(a):a}function cb(a){var b=document;if(b.getElementsByClassName)a=b.getElementsByClassName(a)[0];else{b=document;var c;a?c=b.querySelector(a?"."+a:""):c=(a?b.querySelectorAll(a?"."+a:""):b.getElementsByTagName("*"))[0]||null;a=c}return a||null}function db(a){a&&a.parentNode&&a.parentNode.removeChild(a)};var eb=class{constructor(a){this.serializedAttributionData=A(a);var b=a.l;a=new a.constructor(y(b,b[r]|0,B,!0,!0));b=a.l;b[r]&=-3;this.g=a;this.isMutableImpression=xa(this.g)!==void 0&&!!F(za(this.g),33);H(this.g,30);this.U=!!F(this.g,11);this.hasUserFeedbackData=!!this.g&&xa(this.g)!==void 0;this.N=!!F(this.g,4);this.R=!!F(this.g,6);this.M=!!F(this.g,13);t(E(this.g,8));this.creativeIndexSuffix=(t(E(this.g,8))??0)>1?(t(E(this.g,7))??0).toString():"";w(E(this.g,34))!=null&&(this.creativeIndexSuffix= 
H(this.g,34)+"_"+this.creativeIndexSuffix);this.V=!!F(this.g,17);this.T=!!F(this.g,18);this.L=!!F(this.g,14);this.D=!!F(this.g,15);this.W=!!F(this.g,31);this.S=F(this.g,9)==1;this.openAttributionInline=F(this.g,10)==1;this.isMobileDevice=!!F(this.g,12);this.u=null;this.P=(a=$a.querySelector("[data-slide]"))?a.getAttribute("data-slide")==="true":!1;(this.G=(t(E(this.g,8))??0)>1)&&L.goog_multislot_cache===void 0&&(L.goog_multislot_cache={});if(this.G&&!this.P){if(a=L.goog_multislot_cache.hd,a===void 0){a= 
!1;if(b=$a.querySelector("[data-dim]"))if(b=b.getBoundingClientRect(),b.right-b.left>=150&&b.bottom-b.top>=150)a=!1;else{var c=document.body.getBoundingClientRect();(Math.abs(c.left-b.left)<=1&&Math.abs(c.right-b.right)<=1?b.bottom-b.top:b.right-b.left)<150&&(a=!0)}else a=!1;window.goog_multislot_cache.hd=a}}else a=!1;this.F=a;this.A=N("abgcp"+this.creativeIndexSuffix);this.v=N("abgc"+this.creativeIndexSuffix);this.h=N("abgs"+this.creativeIndexSuffix);N("abgl"+this.creativeIndexSuffix);this.s=N("abgb"+ 
this.creativeIndexSuffix);this.C=N("abgac"+this.creativeIndexSuffix);N("mute_panel"+this.creativeIndexSuffix);this.B=cb("goog_delegate_attribution"+this.creativeIndexSuffix);this.isDelegateAttributionActive=!!this.B&&!!this.L&&!cb("goog_delegate_disabled")&&!this.D;if(this.h)a:for(a=this.h,b=a.childNodes,c=0;c<b.length;c++){const d=b.item(c);if(typeof d.tagName!="undefined"&&d.tagName.toUpperCase()=="A"){a=d;break a}}else a=null;this.m=a;this.j=this.isDelegateAttributionActive?this.B:N("cbb"+this.creativeIndexSuffix); 
this.O=this.F?this.creativeIndexSuffix==="0":!0;this.enableDelegateDismissableMenu=!!this.j&&bb(this.j,"goog_dismissable_menu");this.o=null;this.H=0;this.i=this.isDelegateAttributionActive?this.B:this.R&&this.A?this.A:this.v;this.autoExpandOnLoad=!!F(this.g,19);this.adbadgeEnabled=!!F(this.g,24);this.enableNativeJakeUi=!!F(this.g,27);H(this.g,33)}};var fb=class{constructor(a,b,c){if(!a)throw Error("bad conv util ctor args");this.g=a;this.h=c}};var O=(a,b)=>{a&&Oa(b,(c,d)=>{a.style[d]=c})};var gb=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function hb(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(d){b=c;break a}b=void 0}return b};const ib=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var jb=class{constructor(a,b){this.g=a;this.h=b}},kb=class{constructor(a,b){this.url=a;this.g=!!b;this.depth=null}};let P=null;function lb(){const a=l.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function mb(){const a=l.performance;return a&&a.now?a.now():null};var nb=class{constructor(a,b){var c=mb()||lb();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const R=l.performance,ob=!!(R&&R.mark&&R.measure&&R.clearMarks),S=Da(()=>{var a;if(a=ob){var b;a=window;if(P===null){P="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(P=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=P;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function pb(a){a&&R&&S()&&(R.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),R.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};function T(a,b){const c={};c[a]=b;return[c]}function qb(a,b,c,d,e){const f=[];Oa(a,(g,h)=>{(g=rb(g,b,c,d,e))&&f.push(`${h}=${g}`)});return f.join(b)} 
function rb(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push(rb(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(qb(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function sb(a){let b=1;for(const c in a.h)c.length>b&&(b=c.length);return 3997-b-a.i.length-1} 
function tb(a,b){let c="https://pagead2.googlesyndication.com"+b,d=sb(a)-b.length;if(d<0)return"";a.g.sort((f,g)=>f-g);b=null;let e="";for(let f=0;f<a.g.length;f++){const g=a.g[f],h=a.h[g];for(let k=0;k<h.length;k++){if(!d){b=b==null?g:b;break}let n=qb(h[k],a.i,",$");if(n){n=e+n;if(d>=n.length){d-=n.length;c+=n;e=a.i;break}b=b==null?g:b}}}a="";b!=null&&(a=`${e}${"trn"}=${b}`);return c+a}var ub=class{constructor(){this.i="&";this.h={};this.j=0;this.g=[]}};function vb(a,b,c){let d,e;try{a.g&&a.g.g?(e=a.g.start(b.toString(),3),d=c(),a.g.end(e)):d=c()}catch(f){c=!0;try{pb(e),c=a.m(b,new gb(f,{message:hb(f)}),void 0,void 0)}catch(g){a.j(217,g)}if(c)window.console?.error?.(f);else throw f;}return d}function wb(a,b){var c=U;return(...d)=>vb(c,a,()=>b.apply(void 0,d))} 
var zb=class{constructor(a=null){this.pinger=xb;this.g=a;this.h=null;this.i=!1;this.m=this.j}j(a,b,c,d,e){e=e||"jserror";let f=void 0;try{const C=new ub;var g=C;g.g.push(1);g.h[1]=T("context",a);b.error&&b.meta&&b.id||(b=new gb(b,{message:hb(b)}));g=b;if(g.msg){b=C;var h=g.msg.substring(0,512);b.g.push(2);b.h[2]=T("msg",h)}var k=g.meta||{};h=k;if(this.h)try{this.h(h)}catch(z){}if(d)try{d(h)}catch(z){}d=C;k=[k];d.g.push(3);d.h[3]=k;var n;if(!(n=u)){d=l;k=[];let z;h=null;do{var m=d;try{var v;if(v=!!m&& 
m.location.href!=null)b:{try{ba(m.foo);v=!0;break b}catch(x){}v=!1}var p=v}catch{p=!1}p?(z=m.location.href,h=m.document&&m.document.referrer||null):(z=h,h=null);k.push(new kb(z||""));try{d=m.parent}catch(x){d=null}}while(d&&m!==d);for(let x=0,Va=k.length-1;x<=Va;++x)k[x].depth=Va-x;m=l;if(m.location&&m.location.ancestorOrigins&&m.location.ancestorOrigins.length===k.length-1)for(p=1;p<k.length;++p){const x=k[p];x.url||(x.url=m.location.ancestorOrigins[p-1]||"",x.g=!0)}n=k}var u=n;let Z=new kb(l.location.href, 
!1);n=null;const pa=u.length-1;for(m=pa;m>=0;--m){var D=u[m];!n&&ib.test(D.url)&&(n=D);if(D.url&&!D.g){Z=D;break}}D=null;const Jb=u.length&&u[pa].url;Z.depth!==0&&Jb&&(D=u[pa]);f=new jb(Z,D);if(f.h){u=C;var G=f.h.url||"";u.g.push(4);u.h[4]=T("top",G)}var qa={url:f.g.url||""};if(f.g.url){const z=f.g.url.match(Ma);var Q=z[1],Wa=z[3],Xa=z[4];G="";Q&&(G+=Q+":");Wa&&(G+="//",G+=Wa,Xa&&(G+=":"+Xa));var Ya=G}else Ya="";Q=C;qa=[qa,{url:Ya}];Q.g.push(5);Q.h[5]=qa;yb(this.pinger,e,C,this.i,c)}catch(C){try{yb(this.pinger, 
e,{context:"ecmserr",rctx:a,msg:hb(C),url:f?.g.url??""},this.i,c)}catch(Z){}}return!0}};function yb(a,b,c,d=!1,e){if((d?a.g:Math.random())<(e||.01))try{let f;c instanceof ub?f=c:(f=new ub,Oa(c,(h,k)=>{var n=f;const m=n.j++;h=T(k,h);n.g.push(m);n.h[m]=h}));const g=tb(f,"/pagead/gen_204?id="+b+"&");g&&Qa(l,g)}catch(f){}}function Ab(){var a=xb,b=window.google_srt;b>=0&&b<=1&&(a.g=b)}var Bb=class{constructor(){this.g=Math.random()}};let xb,U; 
const V=new class{constructor(a,b){this.h=[];this.i=b||l;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.h=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.g=S()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.g)return null;a=new nb(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;R&&S()&&R.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(mb()||lb())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;R&&S()&&R.mark(b);!this.g||this.h.length> 
2048||this.h.push(a)}}}(1,window);function Cb(){window.google_measure_js_timing||(V.g=!1,V.h!==V.i.google_js_reporting_queue&&(S()&&Array.prototype.forEach.call(V.h,pb,void 0),V.h.length=0))}(function(a){xb=a??new Bb;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());Ab();U=new zb(V);U.h=b=>{const c=Sa;c!==0&&(b.jc=String(c),b.shv=Ta(c))};U.i=!0;window.document.readyState==="complete"?Cb():V.g&&K(window,"load",()=>{Cb()})})();function W(a,b){return wb(a,b)};function Db(a){if(a.g.m&&a.g.T){const b=za(a.g.g);b&&w(E(b,5))!=null&&w(E(b,6))!=null&&(a.i=new fb(H(b,5),H(b,6),H(b,19)));K(a.g.m,"click",W(452,()=>{if(!a.j&&(a.j=!0,a.i)){var c=a.i;{var d=c.g;const g=d.search(Na);var e;b:{for(e=0;(e=d.indexOf("ad_signals",e))>=0&&e<g;){var f=d.charCodeAt(e-1);if(f==38||f==63)if(f=d.charCodeAt(e+10),!f||f==61||f==38||f==35)break b;e+=11}e=-1}f=e;if(f<0)d=null;else{e=d.indexOf("&",f);if(e<0||e>g)e=g;d=decodeURIComponent(d.slice(f+11,e!==-1?e:0).replace(/\+/g," "))}}d? 
(c={I:d,label:"closebutton_whythisad_click",K:"1",J:""},d=new Ba,c!=null&&(c.I!=null&&I(d,1,c.I),c.Y!=null&&I(d,3,c.Y),c.label!=null&&I(d,6,c.label),c.K!=null&&I(d,7,c.K),c.J!=null&&I(d,8,c.J),c.X!=null&&I(d,11,c.X)),Ca(l).fence?.reportEvent({eventType:"interaction",eventData:JSON.stringify(A(d)),destination:["buyer"]})):(d=c.g+"&label=closebutton_whythisad_click",d+="&label_instance=1",c.h&&(d+="&cid="+c.h),Qa(window,d))}}))}} 
function Eb(a){if(a.g.U)K(a.g.i,"click",W(365,b=>{const c=L.goog_interstitial_display;c&&(c(b),b&&(b.stopPropagation(),b.preventDefault()))}));else if(a.g.isMutableImpression&&a.g.isMobileDevice)K(a.g.i,"click",()=>a.h());else if(a.g.isMutableImpression&&!a.g.isMobileDevice&&(a.g.j&&(K(a.g.j,"click",()=>a.h()),K(a.g.j,"keydown",b=>{b.code!=="Enter"&&b.code!=="Space"||a.h()})),a.g.W&&a.g.h&&K(a.g.h,"click",()=>a.h())),a.g.N)Fb(a);else{K(a.g.i,"mouseover",W(367,()=>Fb(a)));K(a.g.i,"mouseout",W(369, 
()=>Gb(a,500)));K(a.g.i,"touchstart",W(368,()=>Fb(a)),Ea);const b=W(370,()=>Gb(a,4E3));K(a.g.i,"mouseup",b);K(a.g.i,"touchend",b);K(a.g.i,"touchcancel",b);a.g.m&&K(a.g.m,"click",W(371,c=>a.preventDefault(c)))}}function Fb(a){window.clearTimeout(a.g.o);a.g.o=null;a.g.h&&a.g.h.style.display=="block"||(a.g.H=Date.now(),a.g.s&&a.g.h&&(a.g.s.style.display="none",a.g.h.style.display="block"))}function Gb(a,b){window.clearTimeout(a.g.o);a.g.o=window.setTimeout(()=>Hb(a),b)} 
function Ib(a){const b=a.g.C;b!==void 0&&(b.style.display="block",a.g.enableNativeJakeUi&&window.requestAnimationFrame(()=>{M(b,"abgacfo")}))}function Hb(a){window.clearTimeout(a.g.o);a.g.o=null;a.g.s&&a.g.h&&(a.g.s.style.display="block",a.g.h.style.display="none")} 
class Kb{constructor(a,b){this.g=a;this.h=b;this.g.V||(this.j=!1,this.i=null,!this.g.F||this.g.adbadgeEnabled||this.g.O?Db(this):(a={display:"none"},b={width:"15px",height:"15px"},this.g.isMobileDevice?(O(this.g.s,a),O(this.g.h,a),O(this.g.A,b),O(this.g.v,b)):O(this.g.v,a)),Eb(this),this.g.enableNativeJakeUi&&M(this.g.C,"abgnac"),this.g.isDelegateAttributionActive?(M(document.body,"goog_delegate_active"),M(document.body,"jaa")):(!this.g.isMutableImpression&&this.g.j&&db(this.g.j),setTimeout(()=>{M(document.body, 
"jar")},this.g.M?750:100)),this.g.D&&M(document.body,"goog_delegate_disabled"),this.g.autoExpandOnLoad&&L.addEventListener("load",()=>this.h()))}preventDefault(a){if(this.g.h&&this.g.h.style.display=="block"&&Date.now()-this.g.H<500)a.preventDefault?a.preventDefault():a.returnValue=!1;else if(this.g.openAttributionInline){var b=this.g.m.getAttribute("href");window.adSlot?window.adSlot.openAttribution(b)&&(a.preventDefault?a.preventDefault():a.returnValue=!1):window.openAttribution&&(window.openAttribution(b), 
a.preventDefault?a.preventDefault():a.returnValue=!1)}else this.g.S&&(b=this.g.m.getAttribute("href"),window.adSlot?window.adSlot.openSystemBrowser(b)&&(a.preventDefault?a.preventDefault():a.returnValue=!1):window.openSystemBrowser&&(window.openSystemBrowser(b),a.preventDefault?a.preventDefault():a.returnValue=!1))}};function Lb(a){if(!a.g&&(a.g=!0,L.goog_delegate_deferred_token=void 0,a.h)){var b=a.i;a=Aa(JSON.stringify(a.h));if(!a)throw Error("bad attrdata");a=new eb(a);new b(a)}}class Mb{constructor(a){var b=Nb;if(!b)throw Error("bad ctor");this.i=b;this.h=a;this.g=!1;cb("goog_delegate_deferred")?L.goog_delegate_deferred_token!==void 0?Lb(this):(a=()=>{Lb(this)},L.goog_delegate_deferred_token=a,setTimeout(a,5E3)):Lb(this)}};var Ob=(a=[])=>{l.google_logging_queue||(l.google_logging_queue=[]);l.google_logging_queue.push([11,a])};class Pb{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};function Qb(){const {promise:a,resolve:b}=new Pb;return{promise:a,resolve:b}};function Rb(a,b=()=>{}){a.google_llp||(a.google_llp={});a=a.google_llp;let c=a[5];if(c)return c;c=Qb();a[5]=c;b();return c}function Sb(a,b){return Rb(a,()=>{var c=a.document;const d=Pa("SCRIPT",c);if(b instanceof Ka)var e=b.g;else throw Error("");d.src=e;(e=La(d.ownerDocument))&&d.setAttribute("nonce",e);(c=c.getElementsByTagName("script")[0])&&c.parentNode&&c.parentNode.insertBefore(d,c)}).promise};function Tb(a){a=a===null?"null":a===void 0?"undefined":a;Ia===void 0&&(Ia=Ja());var b=Ia;return new Ka(b?b.createScriptURL(a):a)};function Ub(a){Ob([a]);new Mb(a)}function Vb(a){a.g.u?a.g.u.expandAttributionCard():(vb(U,373,()=>{Hb(a.h);Ib(a.h)}),Sb(window,Tb(`https://${"pagead2.googlesyndication.com"}${"/pagead/js/"+H(a.g.g,33)+"/abg_survey.js"}`)).then(b=>{b.createAttributionCard(a.g);a.g.u=b;b.expandAttributionCard()}),Za())}var Nb=class{constructor(a){this.g=a;this.h=new Kb(this.g,W(359,()=>Vb(this)))}};Sa=60;const Wb=Ua(60,document.currentScript);if(Wb==null)throw Error("JSC not found 60");const Xb={},Yb=Wb.attributes;for(let a=Yb.length-1;a>=0;a--){const b=Yb[a].name;b.indexOf("data-jcp-")===0&&(Xb[b.substring(9)]=Yb[a].value)}if(Xb["attribution-data"])Ub(JSON.parse(Xb["attribution-data"]));else for(var Zb=["buildAttribution"],X=l,Y;Zb.length&&(Y=Zb.shift());)Zb.length||Ub===void 0?X[Y]&&X[Y]!==Object.prototype[Y]?X=X[Y]:X=X[Y]={}:X[Y]=Ub;}).call(this);
