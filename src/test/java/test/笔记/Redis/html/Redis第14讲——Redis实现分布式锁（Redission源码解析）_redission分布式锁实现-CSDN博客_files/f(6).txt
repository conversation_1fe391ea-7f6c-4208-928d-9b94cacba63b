(function(sttc){'use strict';var l,aa=Object.defineProperty,ba=globalThis,ca=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",da={},ea={};function fa(a,b,c){if(!c||a!=null){c=ea[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}} 
function ha(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in da?f=da:f=ba;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=ca&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?aa(da,d,{configurable:!0,writable:!0,value:b}):b!==c&&(ea[d]===void 0&&(a=Math.random()*1E9>>>0,ea[d]=ca?ba.Symbol(d):"$jscp$"+a+"$"+d),aa(f,ea[d],{configurable:!0,writable:!0,value:b})))}}ha("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")},"es_next");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var p=this||self;function ia(a){var b=typeof a;return b=="object"&&a!=null||b=="function"}function ja(a,b,c){return a.call.apply(a.bind,arguments)}function ka(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}} 
function la(a,b,c){la=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?ja:ka;return la.apply(null,arguments)}function ma(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function na(a,b){a=a.split(".");for(var c=p||p,d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b} 
function oa(a,b){function c(){}c.prototype=b.prototype;a.Y=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.ub=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var pa;function qa(a){p.setTimeout(()=>{throw a;},0)};var ra,sa;a:{for(var ta=["CLOSURE_FLAGS"],ua=p,va=0;va<ta.length;va++)if(ua=ua[ta[va]],ua==null){sa=null;break a}sa=ua}var wa=sa&&sa[610401301];ra=wa!=null?wa:!1;function xa(){var a=p.navigator;return a&&(a=a.userAgent)?a:""}var t;const ya=p.navigator;t=ya?ya.userAgentData||null:null;function za(){if(!ra||!t)return!1;for(let a=0;a<t.brands.length;a++){const {brand:b}=t.brands[a];if(b&&b.indexOf("Chromium")!=-1)return!0}return!1}function u(a){return xa().indexOf(a)!=-1};function Aa(){return ra?!!t&&t.brands.length>0:!1}function Ba(){return Aa()?za():(u("Chrome")||u("CriOS"))&&!(Aa()?0:u("Edge"))||u("Silk")};function Ca(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(let c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1}function Da(a,b){const c=a.length,d=typeof a==="string"?a.split(""):a;for(let e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)}function Ea(a,b){var c=a.length;const d=typeof a==="string"?a.split(""):a;for(--c;c>=0;--c)c in d&&b.call(void 0,d[c],c,a)}function Fa(a,b){b=Ca(a,b);let c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c} 
function Ha(a,b){let c=0;Ea(a,function(d,e){b.call(void 0,d,e,a)&&Array.prototype.splice.call(a,e,1).length==1&&c++})};function Ia(a){Ia[" "](a);return a}Ia[" "]=function(){};var Ja=u("Gecko")&&!(xa().toLowerCase().indexOf("webkit")!=-1&&!u("Edge"))&&!(u("Trident")||u("MSIE"))&&!u("Edge"),Ka=xa().toLowerCase().indexOf("webkit")!=-1&&!u("Edge");function La(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};let Ma=void 0,Na;function Oa(a){if(Na)throw Error("");Na=b=>{p.setTimeout(()=>{a(b)},0)}}function Pa(a){if(Na)try{Na(a)}catch(b){throw b.cause=a,b;}}function Qa(a){a=Error(a);La(a,"warning");Pa(a);return a}function Ra(a,b){if(a!=null){var c=Ma??(Ma={});var d=c[a]||0;d>=b||(c[a]=d+1,a=Error(),La(a,"incident"),Na?Pa(a):qa(a))}};function Sa(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var Ta=Sa(),Ua=Sa(),Va=Sa(),Wa=Sa("m_m",!0),Xa=Sa();const v=Sa("jas",!0);var Ya;const Za=[];Za[v]=55;Ya=Object.freeze(Za);const $a=typeof Wa==="symbol";var ab={};function bb(a){a=a[Wa];const b=a===ab;$a&&a&&!b&&Ra(Xa,3);return b}function cb(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object}function db(a){if(a&2)throw Error();};function eb(a,b){const c=fb;if(!b(a))throw b=(typeof c==="function"?c():c)?.concat("\n")??"",Error(b+String(a));}function gb(a){a.xb=!0;return a}let fb=void 0;var hb=gb(a=>typeof a==="number"),w=gb(a=>typeof a==="string"),ib=gb(a=>a===void 0),jb=gb(a=>Array.isArray(a));function kb(){return gb(a=>jb(a)?a.every(b=>hb(b)):!1)};function x(a){if(w(a)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(a))throw Error(String(a));}else if(hb(a)&&!Number.isSafeInteger(a))throw Error(String(a));return BigInt(a)}var ob=gb(a=>a>=mb&&a<=nb);const mb=BigInt(Number.MIN_SAFE_INTEGER),nb=BigInt(Number.MAX_SAFE_INTEGER);let pb=0,qb=0;function rb(a){const b=a>>>0;pb=b;qb=(a-b)/4294967296>>>0}function sb(a){if(a<0){rb(-a);a=pb;var b=qb;b=~b;a?a=~a+1:b+=1;const [c,d]=[a,b];pb=c>>>0;qb=d>>>0}else rb(a)}function tb(a,b){b>>>=0;a>>>=0;var c;b<=2097151?c=""+(4294967296*b+a):c=""+(BigInt(b)<<BigInt(32)|BigInt(a));return c}function ub(){var a=pb,b=qb,c;b&2147483648?c=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):c=tb(a,b);return c};const vb=typeof BigInt==="function"?BigInt.asIntN:void 0,wb=Number.isSafeInteger,xb=Number.isFinite,yb=Math.trunc,zb=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function Ab(a){switch(typeof a){case "bigint":return!0;case "number":return xb(a);case "string":return zb.test(a);default:return!1}}function Bb(a){if(!xb(a))throw Qa("enum");return a|0}function Cb(a){return a==null?a:xb(a)?a|0:void 0}function Db(a){if(typeof a!=="number")throw Qa("int32");if(!xb(a))throw Qa("int32");return a|0} 
function Eb(a){if(a!=null)a:{if(!Ab(a))throw Qa("int64");switch(typeof a){case "string":var b=yb(Number(a));wb(b)?a=String(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),Fb(a)||(a.length<16?sb(Number(a)):(a=BigInt(a),pb=Number(a&BigInt(4294967295))>>>0,qb=Number(a>>BigInt(32)&BigInt(4294967295))),a=ub()));break a;case "bigint":a=x(vb(64,a));break a;default:a=Gb(a)}}return a} 
function Fb(a){const b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function Gb(a){a=yb(a);if(!wb(a)){sb(a);var b=pb,c=qb;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);const d=c*4294967296+(b>>>0);b=Number.isSafeInteger(d)?d:tb(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function Hb(a){if(typeof a!=="string")throw Error();return a} 
function Ib(a){if(a!=null&&typeof a!=="string")throw Error();return a}function Jb(a){return a==null||typeof a==="string"?a:void 0};function Kb(a){return a};function Lb(a,b,c,d,e){d=d?!!(b&32):void 0;const f=[];var g=a.length;let h,k,m,n=!1;b&64?(b&256?(g--,h=a[g],k=g):(k=4294967295,h=void 0),e||b&512||(n=!0,m=(Mb??Kb)(h?k- -1:b>>15&1023||536870912,-1,a,h),k=m+-1)):(k=4294967295,b&1||(h=g&&a[g-1],cb(h)?(g--,k=g,m=0):h=void 0));let q=void 0;for(let r=0;r<g;r++){let y=a[r];y!=null&&(y=c(y,d))!=null&&(r>=k?(q??(q={}))[r- -1]=y:f[r]=y)}if(h)for(let r in h)Object.prototype.hasOwnProperty.call(h,r)&&(a=h[r],a!=null&&(a=c(a,d))!=null&&(g=+r,g<m?f[g+-1]=a:(q?? 
(q={}))[r]=a));q&&(n?f.push(q):f[k]=q);e&&(f[v]=b&33522241|(q!=null?290:34));return f}function Nb(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return ob(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[v]|0;return a.length===0&&b&1?void 0:Lb(a,b,Nb,!1,!1)}if(bb(a))return Ob(a);return}return a}let Mb;function Ob(a){a=a.F;return Lb(a,a[v]|0,Nb,void 0,!1)};function Pb(){Ra(Va,5)};function Qb(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){const d=a[v]|0;if(a.length===0&&d&1)return;if(d&2)return a;var c;if(c=b)c=d===0||!!(d&32)&&!(d&64||!(d&16));return c?(a[v]|=34,d&4&&Object.freeze(a),a):Lb(a,d,Qb,b!==void 0,!0)}if(bb(a))return b=a.F,c=b[v]|0,c&2?a:Lb(b,c,Qb,!0,!0)}function Rb(a){var b=a.F;if(!((b[v]|0)&2))return a;a=new a.constructor(Lb(b,b[v]|0,Qb,!0,!0));b=a.F;b[v]&=-3;return a} 
function Sb(a){const b=a.F,c=b[v]|0;return c&2?a:new a.constructor(Lb(b,c,Qb,!0,!0))};const Tb=x(0);function z(a,b){a=a.F;return Ub(a,a[v]|0,b)}function Ub(a,b,c){if(c===-1)return null;const d=c+(b&512?0:-1),e=a.length-1;if(d>=e&&b&256)a=a[e][c];else if(d<=e)a=a[d];else return;return a}function Vb(a,b,c){const d=a.F;let e=d[v]|0;db(e);B(d,e,b,c);return a}function B(a,b,c,d){const e=b&512?0:-1,f=c+e;var g=a.length-1;if(f>=g&&b&256)return a[g][c]=d,b;if(f<=g)return a[f]=d,b;d!==void 0&&(g=b>>15&1023||536870912,c>=g?d!=null&&(a[g+e]={[c]:d},b|=256,a[v]=b):a[f]=d);return b} 
function Wb(a){return!!(2&a)&&!!(4&a)||!!(1024&a)}function Xb(a,b,c,d){const e=a.F;let f=e[v]|0;db(f);if(c==null)return B(e,f,b),a;let g=c[v]|0,h=g;var k=Wb(g);let m=k||Object.isFrozen(c);k||(g=0);m||(c=[...c],h=0,g=Yb(g,f),g=Zb(g,f),m=!1);g|=21;k=(4&g?2048&g?2048:4096&g?4096:0:void 0)??0;for(let n=0;n<c.length;n++){const q=c[n],r=d(q,k);Object.is(q,r)||(m&&(c=[...c],h=0,g=Yb(g,f),g=Zb(g,f),m=!1),c[n]=r)}g!==h&&(m&&(c=[...c],g=Yb(g,f),g=Zb(g,f)),c[v]=g);B(e,f,b,c);return a} 
function C(a,b,c,d){const e=a.F;let f=e[v]|0;db(f);B(e,f,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}function bc(a,b,c,d){let e=a.get(d);if(e!=null)return e;e=0;for(let f=0;f<d.length;f++){const g=d[f];Ub(b,c,g)!=null&&(e!==0&&(c=B(b,c,e)),e=g)}a.set(d,e);return e}function cc(a,b,c){c==null&&(c=void 0);return Vb(a,b,c)} 
function dc(a,b,c,d){d==null&&(d=void 0);a:{const g=a.F;var e=g[v]|0;db(e);if(d==null){var f=g[Ua]??(g[Ua]=new Map);if(bc(f,g,e,c)===b)f.set(c,0);else break a}else{f=g;const h=f[Ua]??(f[Ua]=new Map),k=bc(h,f,e,c);k!==b&&(k&&(e=B(f,e,k)),h.set(c,b))}B(g,e,b,d)}return a}function Yb(a,b){2&a&&(a|=16);var c;2&b?c=a|2:c=a&-3;a=c;return(a|32)&-1025}function Zb(a,b){32&b||(a&=-33);return a}function ec(a,b){a=z(a,b);return a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0} 
function D(a,b,c){return C(a,b,Ib(c),"")}function fc(a,b,c){return C(a,b,c==null?c:Bb(c),0)};var E=class{constructor(a){a:{if(a==null){var b=96;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[v]|0;8192&b||!(64&b)||2&b||Pb();if(b&1024)throw Error("farr");if(b&64){b&16384||(a[v]=b|16384);var c=a;break a}var d=a;b|=64;var e=d.length;if(e){var f=e-1;e=d[f];if(cb(e)){b|=256;const g=b&512?0:-1;f-=g;if(f>=1024)throw Error("pvtlmt");for(c in e){if(!Object.prototype.hasOwnProperty.call(e,c))continue;const h=+c;if(h<f)d[h+g]=e[c],delete e[c];else break}b=b&-33521665|(f&1023)<<15}}}a[v]=b|16384; 
c=a}this.F=c}toJSON(){return Ob(this)}};E.prototype[Wa]=ab;function gc(a){return()=>{var b;if(!(b=a[Ta])){const c=new a;b=c.F;b[v]|=34;b=a[Ta]=c}return b}};function hc(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}}function ic(a){let b=0;return function(c){p.clearTimeout(b);const d=arguments;b=p.setTimeout(function(){a.apply(void 0,d)},100)}};function F(a,b,c){a.addEventListener&&a.addEventListener(b,c,!1)}function jc(a,b,c){return a.removeEventListener?(a.removeEventListener(b,c,!1),!0):!1};function kc(){return ra&&t?t.mobile:!lc()&&(u("iPod")||u("iPhone")||u("Android")||u("IEMobile"))}function lc(){return ra&&t?!t.mobile&&(u("iPad")||u("Android")||u("Silk")):u("iPad")||u("Android")&&!u("Mobile")||u("Silk")};function mc(a,b,c){for(const d in a)b.call(c,a[d],d,a)}function nc(a){let b=0;for(const c in a)b++}function oc(a){const b={};for(const c in a)b[c]=a[c];return b}const pc="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function qc(a,b){let c,d;for(let e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(let f=0;f<pc.length;f++)c=pc[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
let rc=globalThis.trustedTypes,sc;function tc(){let a=null;if(!rc)return a;try{const b=c=>c;a=rc.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(b){}return a}function uc(){sc===void 0&&(sc=tc());return sc};var vc=class{constructor(a){this.g=a}toString(){return this.g+""}};function wc(a){const b=uc();return new vc(b?b.createScriptURL(a):a)}function xc(a){if(a instanceof vc)return a.g;throw Error("");};var yc=class{constructor(a){this.g=a}toString(){return this.g+""}};function zc(a){const b=uc();return new yc(b?b.createHTML(a):a)}function Ac(a){if(a instanceof yc)return a.g;throw Error("");};var Bc=class{constructor(a){this.g=a}toString(){return this.g}};function Cc(a){if(a instanceof Bc)return a.g;throw Error("");};function Dc(a){return String(a).replace(/\-([a-z])/g,function(b,c){return c.toUpperCase()})}function Ec(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};var Fc=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function Gc(a){return new Bc(a[0])};function Hc(a){return a instanceof yc?a:zc(String(a).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;"))};function Ic(a,...b){if(b.length===0)return wc(a[0]);let c=a[0];for(let d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return wc(c)}function Jc(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(k=>e(k,h)):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}let f=b.length?"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(g=>e(g[1],g[0])):d.forEach(e);return wc(a+b+c)};function Kc(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Ia(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch{return!1}}function Lc(){if(!globalThis.crypto)return Math.random();try{const a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch{return Math.random()}}function Mc(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function Nc(a){const b=[];Mc(a,function(c){b.push(c)});return b} 
var Oc=hc(()=>kc()?2:lc()?1:0),Pc=(a,b)=>{Mc(b,(c,d)=>{a.style.setProperty(d,c,"important")})},Rc=(a,b)=>{if("length"in a.style){a=a.style;const c=a.length;for(let d=0;d<c;d++){const e=a[d];b(a[e],e,a)}}else a=Qc(a.style.cssText),Mc(a,b)},Qc=a=>{const b={};if(a){const c=/\s*:\s*/;Da((a||"").split(/\s*;\s*/),d=>{if(d){var e=d.split(c);d=e[0];e=e[1];d&&e&&(b[d.toLowerCase()]=e)}})}return b},Sc=a=>{const b=/!\s*important/i;Rc(a,(c,d)=>{b.test(c)?b.test(c):a.style.setProperty(d,c,"important")})}; 
let Tc=[];const Uc=()=>{const a=Tc;Tc=[];for(const b of a)try{b()}catch{}};var Vc=a=>{Tc.push(a);Tc.length==1&&(window.Promise?Promise.resolve().then(Uc):window.setImmediate?setImmediate(Uc):setTimeout(Uc,0))},Wc=a=>{if(typeof a.goog_pvsid!=="number")try{var b=Object,c=b.defineProperty,d=Math.random;var e=Math.floor(d()*2**52);c.call(b,a,"goog_pvsid",{value:e,configurable:!1})}catch(f){}return Number(a.goog_pvsid)||-1},Xc=(a,b)=>new Promise(c=>{setTimeout(()=>void c(b),a)}); 
function Yc(a,b=document){return b.createElement(String(a).toLowerCase())}var Zc=a=>{var b=Kc(a.top)?a.top:null;if(!b)return 1;a=Oc()===0;const c=!!b.document.querySelector('meta[name=viewport][content*="width=device-width"]'),d=b.innerWidth;b=b.outerWidth;if(d===0)return 1;const e=Math.round((b/d+Number.EPSILON)*100)/100;return e===1?1:a||c?e:Math.round((b/d/.4+Number.EPSILON)*100)/100};function $c(a,b,c=null,d=!1,e=!1){ad(a,b,c,d,e)}function ad(a,b,c,d,e=!1){a.google_image_requests||(a.google_image_requests=[]);const f=Yc("IMG",a.document);if(c||d){const g=h=>{c&&c(h);d&&Fa(a.google_image_requests,f);jc(f,"load",g);jc(f,"error",g)};F(f,"load",g);F(f,"error",g)}e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)} 
function bd(a){let b="https://pagead2.googlesyndication.com/pagead/gen_204?id=rcs_internal";Mc(a,(c,d)=>{if(c||c===0)b+=`&${d}=${encodeURIComponent(String(c))}`});cd(b)}function cd(a){var b=window;b.fetch?b.fetch(a,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"}):$c(b,a,void 0,!1,!1)};function dd(){var a=window.innerHeight;this.width=window.innerWidth;this.height=a}l=dd.prototype;l.aspectRatio=function(){return this.width/this.height};l.isEmpty=function(){return!(this.width*this.height)};l.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};l.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};l.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this}; 
l.scale=function(a,b){this.width*=a;this.height*=typeof b==="number"?b:a;return this};function ed(a=p){let b=a.context||a.AMP_CONTEXT_DATA;if(!b)try{b=a.parent.context||a.parent.AMP_CONTEXT_DATA}catch{}return b?.pageViewId&&b?.canonicalUrl?b:null}function fd(){var a=ed();return a?Kc(a.master)?a.master:null:null};function gd(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)}function hd(a){return a&&a.parentNode?a.parentNode.removeChild(a):null}function id(){this.g=p.document||document}id.prototype.i=function(a){var b=this.g;return typeof a==="string"?b.getElementById(a):a};id.prototype.l=id.prototype.i;function G(a,b,c){if(typeof b==="string")(b=jd(a,b))&&(a.style[b]=c);else for(const e in b){c=a;var d=b[e];const f=jd(c,e);f&&(c.style[f]=d)}}var kd={};function jd(a,b){let c=kd[b];if(!c){var d=Dc(b);c=d;a.style[d]===void 0&&(d=(Ka?"Webkit":Ja?"Moz":null)+Ec(d),a.style[d]!==void 0&&(c=d));kd[b]=c}return c};var ld=class{constructor(a,b){this.error=a;this.meta={};this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"}};function md(a){return new ld(a,{message:nd(a)})}function nd(a){let b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);let d;for(;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(d){b=c;break a}b=void 0}return b};const od=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)");var pd=class{constructor(a,b){this.g=a;this.i=b}},qd=class{constructor(a,b){this.url=a;this.g=!!b;this.depth=null}};let rd=null;function sd(){const a=p.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function td(){const a=p.performance;return a&&a.now?a.now():null};var ud=class{constructor(a,b){var c=td()||sd();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const H=p.performance,vd=!!(H&&H.mark&&H.measure&&H.clearMarks),wd=hc(()=>{var a;if(a=vd){var b;a=window;if(rd===null){rd="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(rd=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=rd;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function xd(a){a&&H&&wd()&&(H.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),H.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))} 
function yd(a){a.g=!1;a.i!==a.l.google_js_reporting_queue&&(wd()&&Da(a.i,xd),a.i.length=0)} 
var zd=class{constructor(a){this.i=[];this.l=a||p;let b=null;a&&(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.i=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.g=wd()||(b!=null?b:Math.random()<1)}start(a,b){if(!this.g)return null;a=new ud(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;H&&wd()&&H.mark(b);return a}end(a){if(this.g&&typeof a.value==="number"){a.duration=(td()||sd())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;H&&wd()&&H.mark(b);!this.g||this.i.length> 
2048||this.i.push(a)}}};function Ad(a,b){const c={};c[a]=b;return[c]}function Bd(a,b,c,d,e){const f=[];Mc(a,(g,h)=>{(g=Cd(g,b,c,d,e))&&f.push(`${h}=${g}`)});return f.join(b)} 
function Cd(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const f=[];for(let g=0;g<a.length;g++)f.push(Cd(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Bd(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))}function Dd(a){let b=1;for(const c in a.i)c.length>b&&(b=c.length);return 3997-b-a.l.length-1} 
function Ed(a,b,c){b="https://"+b+c;let d=Dd(a)-c.length;if(d<0)return"";a.g.sort((f,g)=>f-g);c=null;let e="";for(let f=0;f<a.g.length;f++){const g=a.g[f],h=a.i[g];for(let k=0;k<h.length;k++){if(!d){c=c==null?g:c;break}let m=Bd(h[k],a.l,",$");if(m){m=e+m;if(d>=m.length){d-=m.length;b+=m;e=a.l;break}c=c==null?g:c}}}a="";c!=null&&(a=`${e}${"trn"}=${c}`);return b+a}var Fd=class{constructor(){this.l="&";this.i={};this.j=0;this.g=[]}};var Id=class{constructor(a=null){this.u=Gd;this.i=a;this.l=null;this.o=!1;this.C=this.P}j(a){this.l=a}G(a){this.o=a}g(a,b){let c,d;try{this.i&&this.i.g?(d=this.i.start(a.toString(),3),c=b(),this.i.end(d)):c=b()}catch(e){b=!0;try{xd(d),b=this.C(a,md(e),void 0,void 0)}catch(f){this.P(217,f)}if(b)window.console?.error?.(e);else throw e;}return c}B(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}P(a,b,c,d,e){e=e||"jserror";let f=void 0;try{const S=new Fd;var g=S;g.g.push(1);g.i[1]=Ad("context",a); 
b.error&&b.meta&&b.id||(b=md(b));g=b;if(g.msg){b=S;var h=g.msg.substring(0,512);b.g.push(2);b.i[2]=Ad("msg",h)}var k=g.meta||{};h=k;if(this.l)try{this.l(h)}catch(A){}if(d)try{d(h)}catch(A){}d=S;k=[k];d.g.push(3);d.i[3]=k;var m;if(!(m=r)){d=p;k=[];h=null;do{var n=d;if(Kc(n)){var q=n.location.href;h=n.document&&n.document.referrer||null}else q=h,h=null;k.push(new qd(q||""));try{d=n.parent}catch(A){d=null}}while(d&&n!==d);for(let A=0,Xd=k.length-1;A<=Xd;++A)k[A].depth=Xd-A;n=p;if(n.location&&n.location.ancestorOrigins&& 
n.location.ancestorOrigins.length===k.length-1)for(q=1;q<k.length;++q){const A=k[q];A.url||(A.url=n.location.ancestorOrigins[q-1]||"",A.g=!0)}m=k}var r=m;let lb=new qd(p.location.href,!1);m=null;const $b=r.length-1;for(n=$b;n>=0;--n){var y=r[n];!m&&od.test(y.url)&&(m=y);if(y.url&&!y.g){lb=y;break}}y=null;const jg=r.length&&r[$b].url;lb.depth!==0&&jg&&(y=r[$b]);f=new pd(lb,y);if(f.i){r=S;var Y=f.i.url||"";r.g.push(4);r.i[4]=Ad("top",Y)}var ac={url:f.g.url||""};if(f.g.url){const A=f.g.url.match(Fc); 
var Ga=A[1],Yd=A[3],Zd=A[4];Y="";Ga&&(Y+=Ga+":");Yd&&(Y+="//",Y+=Yd,Zd&&(Y+=":"+Zd));var $d=Y}else $d="";Ga=S;ac=[ac,{url:$d}];Ga.g.push(5);Ga.i[5]=ac;Hd(this.u,e,S,this.o,c)}catch(S){try{Hd(this.u,e,{context:"ecmserr",rctx:a,msg:nd(S),url:f?.g.url??""},this.o,c)}catch(lb){}}return!0}};var Jd=class extends E{};function Kd(a,b){try{const c=d=>[{[d.Ha]:d.Fa}];return JSON.stringify([a.filter(d=>d.va).map(c),Ob(b),a.filter(d=>!d.va).map(c)])}catch(c){return Ld(c,b),""}}function Ld(a,b){try{bd({m:nd(a instanceof Error?a:Error(String(a))),b:(Cb(z(b,1))??0)||null,v:(Jb(z(b,2))??"")||null})}catch(c){}}var Md=class{constructor(a,b){var c=new Jd;a=fc(c,1,a);b=D(a,2,b);this.l=Sb(b)}};var Nd=class extends E{L(a){return fc(this,2,a)}};var Od=class extends E{},Pd=[4,5,6,8,9,10,11,12,13,14,15,16,17];var Qd=class extends E{};function Rd(){var a=Rb(Sd());return D(a,1,Td())}var Ud=class extends E{};var Vd=class extends E{};var Wd=class extends E{getTagSessionCorrelator(){var a=z(this,1),b=typeof a;a!=null&&(b==="bigint"?a=x(vb(64,a)):Ab(a)?b==="string"?(b=yb(Number(a)),wb(b)?a=x(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=x(vb(64,BigInt(a))))):wb(a)?a=x(Gb(a)):(a=yb(a),wb(a)?a=String(a):(b=String(a),Fb(b)?a=b:(sb(a),a=ub())),a=x(a)):a=void 0);return a??Tb}};var ae=class extends E{},be=[1,7],ce=[4,6,8];class de extends Md{constructor(){super(...arguments)}}function ee(a,...b){fe(a,...b.map(c=>({va:!0,Ha:3,Fa:Ob(c)})))}function ge(a,...b){fe(a,...b.map(c=>({va:!0,Ha:7,Fa:Ob(c)})))}var he=class extends de{};var ie=(a,b)=>{globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(()=>{})};function fe(a,...b){try{a.B&&Kd(a.g.concat(b),a.l).length>=65536&&je(a),a.j&&!a.o&&(a.o=!0,ke(a.j,()=>{je(a)})),a.g.push(...b),a.g.length>=a.u&&je(a),a.g.length&&a.i===null&&(a.i=setTimeout(()=>{je(a)},a.C))}catch(c){Ld(c,a.l)}}function je(a){a.i!==null&&(clearTimeout(a.i),a.i=null);if(a.g.length){var b=Kd(a.g,a.l);a.G("https://pagead2.googlesyndication.com/pagead/ping?e=1",b);a.g=[]}} 
var le=class extends he{constructor(a,b,c,d){super(2,Td());this.G=ie;this.C=a;this.u=b;this.B=c;this.j=d;this.g=[];this.i=null;this.o=!1}},me=class extends le{constructor(a=1E3,b=100,c=!1,d){super(a,b,c&&!0,d)}};var I=a=>{var b="ta";if(a.ta&&a.hasOwnProperty(b))return a.ta;b=new a;return a.ta=b};function J(a,b,c){return b[a]||c};function ne(a,b){a.g=()=>J(3,b,()=>[])(1)}class oe{g(){return[]}};function Hd(a,b,c,d=!1,e){if((d?a.g:Math.random())<(e||.01))try{let f;c instanceof Fd?f=c:(f=new Fd,Mc(c,(h,k)=>{var m=f;const n=m.j++;h=Ad(k,h);m.g.push(n);m.i[n]=h}));const g=Ed(f,a.domain,a.path+b+"&");g&&$c(p,g)}catch(f){}}function pe(a,b){b>=0&&b<=1&&(a.g=b)}var qe=class{constructor(){this.domain="pagead2.googlesyndication.com";this.path="/pagead/gen_204?id=";this.g=Math.random()}};let Gd,re;const se=new zd(window);(function(a){Gd=a??new qe;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());pe(Gd,window.google_srt);re=new Id(se);re.j(()=>{});re.G(!0);window.document.readyState==="complete"?window.google_measure_js_timing||yd(se):se.g&&F(window,"load",()=>{window.google_measure_js_timing||yd(se)})})();function Td(){return"m202503250101"};var te=gc(Qd);var Sd=gc(Ud);function ue(a,b){return b(a)?a:void 0} 
function ve(a,b,c,d,e){c=c instanceof ld?c.error:c;var f=new ae;const g=new Wd;try{var h=Wc(window);C(g,1,Eb(h),"0")}catch(r){}try{var k=I(oe).g();Xb(g,2,k,Db)}catch(r){}try{D(g,3,window.document.URL)}catch(r){}h=cc(f,2,g);k=new Vd;b=fc(k,1,b);try{var m=w(c?.name)?c.name:"Unknown error";D(b,2,m)}catch(r){}try{var n=w(c?.message)?c.message:`Caught ${c}`;D(b,3,n)}catch(r){}try{var q=w(c?.stack)?c.stack:Error().stack;q&&Xb(b,4,q.split(/\n\s*/),Hb)}catch(r){}m=dc(h,1,be,b);if(e){n=0;switch(e.errSrc){case "LCC":n= 
1;break;case "PVC":n=2}q=Rd();b=ue(e.shv,w);q=D(q,2,b);n=fc(q,6,n);q=te();q=Rb(q);b=ue(e.es,kb());q=Xb(q,1,b,Db);q=Sb(q);n=cc(n,4,q);q=ue(e.client,w);n=Vb(n,3,Ib(q));q=ue(e.slotname,w);n=D(n,7,q);e=ue(e.tag_origin,w);e=D(n,8,e);e=Sb(e)}else e=Sb(Rd());e=dc(m,6,ce,e);d=C(e,5,Eb(d??1),"0");ee(a,d)};let we,xe=64;function ye(){try{return we??(we=new Uint32Array(64)),xe>=64&&(crypto.getRandomValues(we),xe=0),we[xe++]}catch(a){return Math.floor(Math.random()*2**32)}};var Ae=class{constructor(){this.g=ze}};function ze(){return{Na:ye()+(ye()&2**21-1)*2**32,Ka:Number.MAX_SAFE_INTEGER}};var De=class{constructor(a=!1){var b=Be;this.u=Ce;this.l=a;this.C=b;this.i=null;this.o=this.P}j(a){this.i=a}G(){}g(a,b){let c;try{c=b()}catch(d){b=this.l;try{b=this.o(a,md(d),void 0,void 0)}catch(e){this.P(217,e)}if(b)window.console?.error?.(d);else throw d;}return c}B(a,b){return(...c)=>this.g(a,()=>b.apply(void 0,c))}P(a,b,c,d){try{const g=c===void 0?1/this.C:c===0?0:1/c;var e=(new Ae).g();if(g>0&&e.Na*g<=e.Ka){var f=this.u;c={};if(this.i)try{this.i(c)}catch(h){}if(d)try{d(c)}catch(h){}ve(f,a,b, 
g,c)}}catch(g){}return this.l}};let Ce,Ee,Fe,Ge,Be;const He=new zd(p);(function(a,b,c=!0){({Ra:Be,La:Fe}=Ie());Ee=a||new qe;pe(Ee,Fe);Ce=b||new me(1E3);Ge=new De(c);p.document.readyState==="complete"?p.google_measure_js_timing||yd(He):He.g&&F(p,"load",()=>{p.google_measure_js_timing||yd(He)})})();function Je(a,b){Ge.g(a,b)}function K(a,b){return Ge.B(a,b)}function Ie(){let a,b;typeof p.google_srt==="number"?(b=p.google_srt,a=p.google_srt===0?1:.01):(b=Math.random(),a=.01);return{Ra:a,La:b}};function Ke(){var a=ed(window);if(a){if(a){var b=a.pageViewId;a=a.clientId;typeof a==="string"&&(b+=a.replace(/\D/g,"").substr(0,6))}else b=null;return+b}for(a=b=window;b&&b!=b.parent;)b=b.parent,Kc(b)&&(a=b);b=a;a=b.google_global_correlator;a||(b.google_global_correlator=a=1+Math.floor(Math.random()*8796093022208));return a} 
function Le(){if(Me)return Me;const a=fd()||window,b=a.google_persistent_state_async;return b!=null&&typeof b=="object"&&b.S!=null&&typeof b.S=="object"?Me=b:a.google_persistent_state_async=Me=new Ne}function Oe(a,b,c){b=Pe[b]||`google_ps_${b}`;a=a.S;const d=a[b];return d===void 0?(a[b]=c(),a[b]):d}function Qe(a){var b=Ke();return Oe(a,7,()=>b)}function Re(){var a=Le();return Qe(a)}var Ne=class{constructor(){this.S={}}},Me=null;const Pe={[8]:"google_prev_ad_formats_by_region",[9]:"google_prev_ad_slotnames_by_region"};function Se(a){a&&typeof a.dispose=="function"&&a.dispose()};function L(){this.l=this.l;this.G=this.G}L.prototype.l=!1;L.prototype.dispose=function(){this.l||(this.l=!0,this.D())};L.prototype[fa(Symbol,"dispose")]=function(){this.dispose()};function M(a,b){a.l?b():(a.G||(a.G=[]),a.G.push(b))}L.prototype.D=function(){if(this.G)for(;this.G.length;)this.G.shift()()};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce((a,b)=>a+b);nc({hb:0,gb:1,bb:2,Wa:3,eb:4,Xa:5,fb:6,Za:7,ab:8,Va:9,Ya:10,ib:11});nc({kb:0,lb:1,jb:2});var Te=class{constructor(a,b=!1){this.g=a;this.defaultValue=b}},N=class{constructor(a,b=0){this.g=a;this.defaultValue=b}};var Ue=new N(1130,100),Ve=new Te(10020),We=new Te(10018),Xe=new N(10023),Ye=new N(1085,5),Ze=new N(63,30),$e=new N(1080,5),af=new N(10019,5),bf=new Te(10016,!0),cf=new N(1027,10),df=new N(57,120),ef=new Te(10017),ff=new N(1050,30),gf=new N(58,120),hf=new N(10021,2),jf=new Te(10022),kf=new N(550718588,250);function lf(a){var b=new mf;db(b.F[v]|0);var c=b.F;b=c[v]|0;const d=2&b?1:2;var e=Ub(c,b,1);e=Array.isArray(e)?e:Ya;var f=e[v]|0;var g=4&f?!1:!0;if(g){4&f&&(e=[...e],f=Yb(f,b),b=B(c,b,1,e));let k=g=0;for(;g<e.length;g++){const m=Cb(e[g]);m!=null&&(e[k++]=m)}k<g&&(e.length=k);f===0&&(f=Yb(f,b),f|=16);f|=21;f&=-6145;e[v]=f;2&f&&Object.freeze(e)}d===1||d===4&&32&f?Wb(f)||(b=f,f|=2,f!==b&&(e[v]=f),Object.freeze(e)):(d===2&&Wb(f)&&(e=[...e],f=Yb(f,b),f=Zb(f,b),e[v]=f,b=B(c,b,1,e)),Wb(f)||(c=f,f=Zb(f,b), 
f!==c&&(e[v]=f)));b=e;if(Array.isArray(a)){var h=a.length;for(e=0;e<h;e++)b.push(Bb(a[e]))}else for(h of a)b.push(Bb(h))}var mf=class extends E{};var nf={},of={},pf={};function qf(){throw Error("Do not instantiate directly");}qf.prototype.Ca=null;qf.prototype.toString=function(){return this.content};qf.prototype.wa=function(){if(this.V!==nf)throw Error("Sanitized content was not of kind HTML.");return zc(this.toString())};function rf(){qf.call(this)}oa(rf,qf);rf.prototype.V=nf;function sf(a){if(a!=null)switch(a.Ca){case 1:return 1;case -1:return-1;case 0:return 0}return null}var O=function(a){function b(c){this.content=c}b.prototype=a.prototype;return function(c,d){c=new b(String(c));d!==void 0&&(c.Ca=d);return c}}(rf);function tf(a){return uf(String(a),()=>"").replace(vf,"&lt;")}const wf=RegExp.prototype.hasOwnProperty("sticky"),xf=new RegExp((wf?"":"^")+"(?:!|/?([a-zA-Z][a-zA-Z0-9:-]*))",wf?"gy":"g"); 
function uf(a,b){const c=[],d=a.length;let e=0,f=[],g,h,k=0;for(;k<d;){switch(e){case 0:var m=a.indexOf("<",k);if(m<0){if(c.length===0)return a;c.push(a.substring(k));k=d}else c.push(a.substring(k,m)),h=m,k=m+1,wf?(xf.lastIndex=k,m=xf.exec(a)):(xf.lastIndex=0,m=xf.exec(a.substring(k))),m?(f=["<",m[0]],g=m[1],e=1,k+=m[0].length):c.push("<");break;case 1:m=a.charAt(k++);switch(m){case "'":case '"':let n=a.indexOf(m,k);n<0?k=d:(f.push(m,a.substring(k,n+1)),k=n+1);break;case ">":f.push(m);c.push(b(f.join(""), 
g));e=0;f=[];h=g=null;break;default:f.push(m)}break;default:throw Error();}e===1&&k>=d&&(k=h+1,c.push("<"),e=0,f=[],h=g=null)}return c.join("")}function yf(a,b){a=a.replace(/<\//g,"<\\/").replace(/\]\]>/g,"]]\\>");return b?a.replace(/{/g," \\{").replace(/}/g," \\}").replace(/\/\*/g,"/ *").replace(/\\$/,"\\ "):a}function P(a){return a!=null&&a.V===nf?String(tf(a.content)).replace(zf,Af):String(a).replace(Bf,Af)} 
function Cf(a){a=String(a);const b=(d,e,f)=>{const g=Math.min(e.length-f,d.length);for(let k=0;k<g;k++){var h=e[f+k];if(d[k]!==("A"<=h&&h<="Z"?h.toLowerCase():h))return!1}return!0};for(var c=0;(c=a.indexOf("<",c))!=-1;){if(b("\x3c/script",a,c)||b("\x3c!--",a,c))return"zSoyz";c+=1}return a}function Df(a){if(a==null)return" null ";if(a!=null&&a.V===of)return a.content;switch(typeof a){case "boolean":case "number":return" "+a+" ";default:return"'"+String(String(a)).replace(Ef,Ff)+"'"}} 
function Q(a){return a!=null&&a.V===pf?yf(a.content,!1):a==null?"":a instanceof Bc?yf(Cc(a),!1):yf(String(a),!0)}const Gf={"\x00":"&#0;","\t":"&#9;","\n":"&#10;","\v":"&#11;","\f":"&#12;","\r":"&#13;"," ":"&#32;",'"':"&quot;","&":"&amp;","'":"&#39;","-":"&#45;","/":"&#47;","<":"&lt;","=":"&#61;",">":"&gt;","`":"&#96;","\u0085":"&#133;","\u00a0":"&#160;","\u2028":"&#8232;","\u2029":"&#8233;"};function Af(a){return Gf[a]} 
const Hf={"\x00":"\\x00","\b":"\\x08","\t":"\\t","\n":"\\n","\v":"\\x0b","\f":"\\f","\r":"\\r",'"':"\\x22",$:"\\x24","&":"\\x26","'":"\\x27","(":"\\x28",")":"\\x29","*":"\\x2a","+":"\\x2b",",":"\\x2c","-":"\\x2d",".":"\\x2e","/":"\\/",":":"\\x3a","<":"\\x3c","=":"\\x3d",">":"\\x3e","?":"\\x3f","[":"\\x5b","\\":"\\\\","]":"\\x5d","^":"\\x5e","{":"\\x7b","|":"\\x7c","}":"\\x7d","\u0085":"\\x85","\u2028":"\\u2028","\u2029":"\\u2029"};function Ff(a){return Hf[a]} 
const Bf=/[\x00\x22\x26\x27\x3c\x3e]/g,zf=/[\x00\x22\x27\x3c\x3e]/g,Ef=/[\x00\x08-\x0d\x22\x26\x27\/\x3c-\x3e\x5b-\x5d\x7b\x7d\x85\u2028\u2029]/g,If=/^[a-zA-Z0-9+\/_-]+={0,2}$/;function Jf(a){a=String(a);return If.test(a)?a:"zSoyz"}const vf=/</g;/* 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
const Kf={};/* 
 
Math.uuid.js (v1.4) 
http://www.broofa.com 
mailto:<EMAIL> 
Copyright (c) 2010 Robert Kieffer 
Dual licensed under the MIT and GPL licenses. 
*/ 
var Lf="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");class Mf{constructor(a){for(var b=Array(36),c=0,d,e=0;e<36;e++)e==8||e==13||e==18||e==23?b[e]="-":e==14?b[e]="4":(c<=2&&(c=33554432+Math.random()*16777216|0),d=c&15,c>>=4,b[e]=Lf[e==19?d&3|8:d]);this.uuid=b.join("");this.callback=a}} 
function Nf(a){const b=p.imalib_globalCallbacks||new Map,c=b.get("AFMA_updateActiveView")||[];if(c.length===0&&p.AFMA_updateActiveView){const d=new Mf(p.AFMA_updateActiveView);c.push(d);p.AFMA_updateActiveView=void 0}p.AFMA_updateActiveView||(p.AFMA_updateActiveView=function(){const d=b.get("AFMA_updateActiveView");for(const e of d)e.callback.apply(null,arguments)});a=new Mf(a);c.push(a);b.set("AFMA_updateActiveView",c);p.imalib_globalCallbacks=b;return a.uuid} 
function Of(a){if(p.AFMA_updateActiveView){var b=p.imalib_globalCallbacks;if(b){var c=b.get("AFMA_updateActiveView");if(c){var d=c.findIndex(e=>e.uuid===a);d!==-1&&(c.splice(d,1),c.length===0&&(p.AFMA_updateActiveView=void 0),b.set("AFMA_updateActiveView",c),p.imalib_globalCallbacks=b)}}}};lf([1,8,9,10,11,12,2,3,4,5,15,16,19,20,21,23]);lf([1,6,7,9,10,11,12,2,3,4,5,13,14,18,19,20,21,23]);lf([1,6,7,9,10,11,12,22,2,3,4,5,13,14,17,18,19,20,21,23]);new mf;var Pf=(p.navigator?p.navigator.userAgent:"").indexOf("Android")!=-1;function Qf(a,b){this.type=a;this.g=this.target=b;this.defaultPrevented=!1}Qf.prototype.i=function(){this.defaultPrevented=!0};var R=class{constructor(a,b){this.messageName=a;this.parameters=b||{}}},Rf=class extends Qf{constructor(a,b){super(a.messageName,b);this.params=a.parameters||{}}};function Sf(a,{data:b,source:c}){if(c&&b){var d=a.l,e=b.messageName;b=b.parameters;if(a.j)switch(e){case "mraid_loaded":e=b.is_top_win;e===!1&&(a.o=!0,a.i=Nf(f=>{a.j&&Tf(a,new R("update_activeview_action",f))}),d.indexOf(c)===-1&&(d.push(c),typeof c.postMessage!=="undefined"&&c.postMessage(new R("mraid_env_obj",window.MRAID_ENV),"*")));break;case "start_tracking_action":a.g==0&&window.AFMA_SendMessage("trackActiveViewUnit");a.g+=1;break;case "stop_tracking_action":--a.g;a.g==0&&(window.AFMA_SendMessage("untrackActiveViewUnit", 
{hashCode:b.hashCode}),a.i&&(Of(a.i),a.i=null));break;case "register_iframe_window_action":e=b.is_top_win;e===!1&&d.indexOf(c)===-1&&d.push(c);break;case "receive_message_action":b.messageName=="disableMraidOpen"&&window.AFMA_ReceiveMessage(b.messageName,b.parameters)}else switch(e){case "mraid_env_obj":window.MRAID_ENV=b;break;case "update_activeview_action":window.AFMA_updateActiveView&&window.AFMA_updateActiveView(b);break;case "receive_message_action":window.AFMA_ReceiveMessage(b.messageName, 
b.parameters)}}}function Tf(a,b){a.l.forEach(c=>c.postMessage(b,"*"))}class Uf{constructor(){this.l=[];this.j=window===window.top;this.o=!1;this.g=0;this.i=null;typeof window.addEventListener!=="undefined"&&window.addEventListener("message",a=>Sf(this,a))}};function Vf(a){var b=wc("gmsg://mobileads.google.com/"+a.messageName);a=new Map(Object.entries(a.parameters));b=xc(b).toString();const c=b.split(/[?#]/),d=/[?]/.test(b)?"?"+c[1]:"";return Jc(c[0],d,/[#]/.test(b)?"#"+(d?c[2]:c[1]):"",a)};function Wf(a,b){Qf.call(this,a?a.type:"");this.relatedTarget=this.g=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.l=null;a&&this.init(a,b)}oa(Wf,Qf); 
Wf.prototype.init=function(a,b){const c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.g=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!== 
void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.l=a;a.defaultPrevented&&Wf.Y.i.call(this)};Wf.prototype.i=function(){Wf.Y.i.call(this);const a=this.l;a.preventDefault?a.preventDefault():a.returnValue=!1};var Xf="closure_listenable_"+(Math.random()*1E6|0);var Yf=0;function Zf(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.ga=e;this.key=++Yf;this.W=this.ea=!1}function $f(a){a.W=!0;a.listener=null;a.proxy=null;a.src=null;a.ga=null};function ag(a){this.src=a;this.g={};this.i=0}ag.prototype.add=function(a,b,c,d,e){const f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.i++);const g=bg(a,b,d,e);g>-1?(b=a[g],c||(b.ea=!1)):(b=new Zf(b,this.src,f,!!d,e),b.ea=c,a.push(b));return b};function cg(a,b,c,d,e){b=b.toString();if(b in a.g){var f=a.g[b];c=bg(f,c,d,e);c>-1&&($f(f[c]),Array.prototype.splice.call(f,c,1),f.length==0&&(delete a.g[b],a.i--))}} 
function dg(a,b){const c=b.type;c in a.g&&Fa(a.g[c],b)&&($f(b),a.g[c].length==0&&(delete a.g[c],a.i--))}function bg(a,b,c,d){for(let e=0;e<a.length;++e){const f=a[e];if(!f.W&&f.listener==b&&f.capture==!!c&&f.ga==d)return e}return-1};var eg="closure_lm_"+(Math.random()*1E6|0),fg={},gg=0;function hg(a,b,c,d,e){if(d&&d.once)return ig(a,b,c,d,e);if(Array.isArray(b)){for(let f=0;f<b.length;f++)hg(a,b[f],c,d,e);return null}c=kg(c);return a&&a[Xf]?a.listen(b,c,ia(d)?!!d.capture:!!d,e):lg(a,b,c,!1,d,e)} 
function lg(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");const g=ia(e)?!!e.capture:!!e;let h=mg(a);h||(a[eg]=h=new ag(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=ng();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)e===void 0&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(og(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");gg++;return c} 
function ng(){function a(c){return b.call(a.src,a.listener,c)}const b=pg;return a}function ig(a,b,c,d,e){if(Array.isArray(b)){for(let f=0;f<b.length;f++)ig(a,b[f],c,d,e);return null}c=kg(c);return a&&a[Xf]?a.g.add(String(b),c,!0,ia(d)?!!d.capture:!!d,e):lg(a,b,c,!0,d,e)} 
function qg(a,b,c,d,e){if(Array.isArray(b))for(let f=0;f<b.length;f++)qg(a,b[f],c,d,e);else(d=ia(d)?!!d.capture:!!d,c=kg(c),a&&a[Xf])?cg(a.g,String(b),c,d,e):a&&(a=mg(a))&&(b=a.g[b.toString()],a=-1,b&&(a=bg(b,c,d,e)),(c=a>-1?b[a]:null)&&rg(c))} 
function rg(a){if(typeof a!=="number"&&a&&!a.W){var b=a.src;if(b&&b[Xf])dg(b.g,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(og(c),d):b.addListener&&b.removeListener&&b.removeListener(d);gg--;(c=mg(b))?(dg(c,a),c.i==0&&(c.src=null,b[eg]=null)):$f(a)}}}function og(a){return a in fg?fg[a]:fg[a]="on"+a}function pg(a,b){if(a.W)a=!0;else{b=new Wf(b,this);const c=a.listener,d=a.ga||a.src;a.ea&&rg(a);a=c.call(d,b)}return a} 
function mg(a){a=a[eg];return a instanceof ag?a:null}var sg="__closure_events_fn_"+(Math.random()*1E9>>>0);function kg(a){if(typeof a==="function")return a;a[sg]||(a[sg]=function(b){return a.handleEvent(b)});return a[sg]};function tg(a){L.call(this);this.i=a;this.g={}}oa(tg,L);var ug=[];tg.prototype.listen=function(a,b,c,d){Array.isArray(b)||(b&&(ug[0]=b.toString()),b=ug);for(let e=0;e<b.length;e++){const f=hg(a,b[e],c||this.handleEvent,d||!1,this.i||this);if(!f)break;this.g[f.key]=f}return this};function vg(a){mc(a.g,function(b,c){this.g.hasOwnProperty(c)&&rg(b)},a);a.g={}}tg.prototype.D=function(){tg.Y.D.call(this);vg(this)}; 
tg.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};function wg(){L.call(this);this.g=new ag(this);this.C=this;this.o=null}oa(wg,L);wg.prototype[Xf]=!0;l=wg.prototype;l.addEventListener=function(a,b,c,d){hg(this,a,b,c,d)};l.removeEventListener=function(a,b,c,d){qg(this,a,b,c,d)}; 
l.dispatchEvent=function(a){var b,c=this.o;if(c)for(b=[];c;c=c.o)b.push(c);c=this.C;const d=a.type||a;if(typeof a==="string")a=new Qf(a,c);else if(a instanceof Qf)a.target=a.target||c;else{var e=a;a=new Qf(d,c);qc(a,e)}e=!0;let f,g;if(b)for(g=b.length-1;g>=0;g--)f=a.g=b[g],e=xg(f,d,!0,a)&&e;f=a.g=c;e=xg(f,d,!0,a)&&e;e=xg(f,d,!1,a)&&e;if(b)for(g=0;g<b.length;g++)f=a.g=b[g],e=xg(f,d,!1,a)&&e;return e}; 
l.D=function(){wg.Y.D.call(this);if(this.g){var a=this.g;let b=0;for(const c in a.g){const d=a.g[c];for(let e=0;e<d.length;e++)++b,$f(d[e]);delete a.g[c];a.i--}}this.o=null};l.listen=function(a,b,c,d){return this.g.add(String(a),b,!1,c,d)};function xg(a,b,c,d){b=a.g.g[String(b)];if(!b)return!0;b=b.concat();let e=!0;for(let f=0;f<b.length;++f){const g=b[f];if(g&&!g.W&&g.capture==c){const h=g.listener,k=g.ga||g.src;g.ea&&dg(a.g,g);e=h.call(k,d)!==!1&&e}}return e&&!d.defaultPrevented};function yg(a,b){wg.call(this);this.j=a||1;this.i=b||p;this.u=la(this.Ua,this);this.B=Date.now()}oa(yg,wg);l=yg.prototype;l.enabled=!1;l.H=null;l.setInterval=function(a){this.j=a;this.H&&this.enabled?(this.stop(),this.start()):this.H&&this.stop()};l.Ua=function(){if(this.enabled){const a=Date.now()-this.B;a>0&&a<this.j*.8?this.H=this.i.setTimeout(this.u,this.j-a):(this.H&&(this.i.clearTimeout(this.H),this.H=null),this.dispatchEvent("tick"),this.enabled&&(this.stop(),this.start()))}}; 
l.start=function(){this.enabled=!0;this.H||(this.H=this.i.setTimeout(this.u,this.j),this.B=Date.now())};l.stop=function(){this.enabled=!1;this.H&&(this.i.clearTimeout(this.H),this.H=null)};l.D=function(){yg.Y.D.call(this);this.stop();delete this.i};function zg(){if(window.googleJsEnvironment&&(window.googleJsEnvironment.environment=="rhino"||window.googleJsEnvironment.environment=="jscore"))return new Ag;if(Pf&&window.googleAdsJsInterface&&"notify"in window.googleAdsJsInterface)try{return window.googleAdsJsInterface.notify("gmsg://mobileads.google.com/noop"),new Ag}catch(a){}else if(window.webkit&&window.webkit.messageHandlers&&window.webkit.messageHandlers.gadGMSGHandler)return new Bg;return new Cg}function Dg(){Eg||(Eg=zg());return Eg} 
var Eg=null,Fg=class extends L{};function Gg(a){const b=oc(a.parameters);b["google.afma.Notify_dt"]=(new Date).getTime();return Vf(new R(a.messageName,b)).toString()} 
var Hg=class extends Fg{constructor(a){super();this.u=a;this.o=[];this.j=new yg(1);this.B=new tg(this);this.B.listen(this.j,"tick",this.C)}sendMessage(a){this.o.push(a);this.j.enabled||(a=this.o.shift(),this.u(a),this.j.start())}C(){const a=this.o.shift();a?this.u(a):this.j.stop()}},Cg=class extends Hg{constructor(){super(a=>{var b=this.g[this.i];b||(b=gd(document,"IFRAME"),b.id="afma-notify-"+(new Date).getTime(),b.style.display="none",this.g[this.i]=b);this.i=(this.i+1)%25;const c=oc(a.parameters); 
c["google.afma.Notify_dt"]=(new Date).getTime();var d=b;a=Vf(new R(a.messageName,c));d.src=xc(a).toString();b.parentNode||document.body.appendChild(b)});this.g=[];this.i=0}D(){this.g.forEach(hd);this.g=[];super.D()}},Ag=class extends Fg{sendMessage(a){a=Gg(a);window.googleAdsJsInterface&&window.googleAdsJsInterface.notify&&(window.googleAdsJsInterface.notify(a),window.googleAdsJsInterface.DEBUG&&console.log(a))}},Bg=class extends Fg{sendMessage(a){a=Gg(a);window.webkit&&window.webkit.messageHandlers&& 
window.webkit.messageHandlers.gadGMSGHandler&&window.webkit.messageHandlers.gadGMSGHandler.postMessage(a)}};var Jg=class extends wg{constructor(){super();this.j=Dg();this.j=Dg();M(this,ma(Se,this.j));this.i={};this.u=new Uf}sendMessage(a,b){let c;typeof a==="string"?c=new R(a,b):a instanceof R&&(c=a);document.readyState=="loading"?ig(p,"DOMContentLoaded",()=>this.j.sendMessage(c),!1,this):this.j.sendMessage(c)}receiveMessage(a,b){if(this.shouldForwardMessageToIframe())this.forwardMessage(new R("receive_message_action",new R(a,b)));else{const c=document.getElementById("ad_iframe");c!=void 0&&c.contentWindow!= 
void 0&&c.contentWindow.AFMA_ReceiveMessage!=void 0&&c.contentWindow.AFMA_ReceiveMessage(a,b)}a=="onshow"&&document.readyState=="loading"?ig(p,"DOMContentLoaded",()=>Ig(a,b??void 0)):this.dispatchEvent(new Rf(new R(a,b),this))}addObserver(a,b,c){const d=e=>void c.call(b,e.type,e.params);this.listen(a,d);this.i[a]||(this.i[a]={});this.i[a][b]=d}removeObserver(a,b){this.i[a]&&this.i[a][b]&&(cg(this.g,String(a),this.i[a][b]),delete this.i[a][b])}shouldForwardMessageToIframe(){return this.u.o}forwardMessage(a){Tf(this.u, 
a)}};function T(a,b){p.AFMA_Communicator?p.AFMA_Communicator.sendMessage(a,b):Kg(a,b)}function Kg(a,b){document.readyState=="loading"?(a=la(Kg,null,a,b),ig(p,"DOMContentLoaded",a,!1)):(a=new R(a,b),Dg().sendMessage(a))}function Ig(a,b){p.AFMA_Communicator.receiveMessage(a,b)}function Lg(a,b,c,d){p.AFMA_Communicator.removeEventListener(a,b,c,d)}function Mg(a,b,c,d){p.AFMA_Communicator.addEventListener(a,b,c,d)}function Ng(a,b,c){p.AFMA_Communicator.addObserver(a,b,c)} 
function Og(a,b){p.AFMA_Communicator.removeObserver(a,b)}p.AFMA_Communicator||(na("AFMA_AddEventListener",Mg),na("AFMA_RemoveEventListener",Lg),na("AFMA_AddObserver",Ng),na("AFMA_RemoveObserver",Og),na("AFMA_ReceiveMessage",Ig),na("AFMA_SendMessage",T),p.AFMA_Communicator=new Jg);var Pg=class{constructor(a){this.g=a;Mg("h5adsEvent",b=>void this.g(b))}ra(a,b){T("h5ads",{obj_id:a,action:"create_interstitial_ad",ad_unit:b})}sa(a,b){T("h5ads",{obj_id:a,ad_unit:b,action:"create_rewarded_ad"})}dispose(a){T("h5ads",{obj_id:a,action:"dispose"})}};class U{constructor(){this.promise=new Promise((a,b)=>{this.resolve=a;this.reject=b})}};function Qg(a){a.extras===void 0&&(a.extras={});a.extras.highfive="1";return encodeURIComponent(JSON.stringify(a))}class Rg extends L{constructor(a,b){super();this.id=a;this.g=b}load(a,b){this.l||(this.listener=b,b=this.id,a=Qg(a),T("h5ads",{obj_id:b,action:"load_interstitial_ad",ad_request:a}))}show(){if(!this.l){if(this.listener==null)throw Error("load must be called before show");T("h5ads",{obj_id:this.id,action:"show_interstitial_ad"})}}D(){this.g.j.dispose(this.id);super.D()}} 
class Sg extends L{constructor(a,b){super();this.id=a;this.g=b}load(a,b){this.l||(this.listener=b,b=this.id,a=Qg(a),T("h5ads",{obj_id:b,action:"load_rewarded_ad",ad_request:a}))}show(){if(!this.l){if(this.listener==null)throw Error("load must be called before show");T("h5ads",{obj_id:this.id,action:"show_rewarded_ad"})}}D(){this.g.j.dispose(this.id);super.D()}}function Tg(a){const b=a.o;a.o+=1;return b} 
var Ug=class{constructor(){this.o=0;this.ads=new Map;this.g=new Map;this.l=new U;this.i=0;this.j=new Pg(a=>{a=a.params;switch(a.eventCategory){case "initialize":this.ads.clear();this.g.clear();this.i=3;this.l.resolve(this);break;case "creation":var b=a.objectId;switch(a.event){case "nativeObjectCreated":if(a=this.g.get(b))this.g.delete(b),this.ads.set(b,a.ad),a.O.resolve(a.ad);return;case "nativeObjectNotCreated":if(a=this.g.get(b))this.g.delete(b),a.ad.dispose(),a.O.reject(Error("Native object not created")); 
return;default:return}case "interstitial":if((b=this.ads.get(a.objectId))&&b instanceof Rg&&b.listener)switch(a.event){case "onAdLoaded":b.listener.T?.(b);break;case "onAdFailedToLoad":b.listener.R?.(b,a.errorCode);break;case "onAdOpened":b.listener.Pa?.(b);break;case "onAdClicked":b.listener.yb?.(b);break;case "onAdClosed":b.listener.K?.(b);break;case "onNativeAdObjectNotAvailable":b.listener.U?.(b)}break;case "rewarded":if((b=this.ads.get(a.objectId))&&b instanceof Sg&&b.listener)switch(a.event){case "onRewardedAdLoaded":b.listener.T?.(b); 
break;case "onRewardedAdFailedToLoad":b.listener.R?.(b,a.errorCode);break;case "onRewardedAdOpened":b.listener.Pa?.(b);break;case "onRewardedAdFailedToShow":b.listener.Oa?.(b,a.errorCode);break;case "onUserEarnedReward":b.listener.Qa?.(b);break;case "onRewardedAdClosed":b.listener.K?.(b);break;case "onNativeAdObjectNotAvailable":b.listener.U?.(b)}}})}connect(){switch(this.i){case 3:return Promise.resolve(this);case 1:return this.l.promise;default:return this.i=1,this.l=new U,T("h5ads",{action:"initialize"}), 
setTimeout(()=>{this.i!==3&&(this.i=2,this.l.reject(Error("GmaBridge could not connect to SDK after 10000 ms.")))},1E4),this.l.promise}}ra(a){if(this.i!==3)return Promise.reject(Error("GmaBridge is not connected"));const b=Tg(this),c=new U;this.g.set(b,{O:c,ad:new Rg(b,this)});this.j.ra(b,a);return c.promise}sa(a){if(this.i!==3)return Promise.reject(Error("GmaBridge is not connected"));const b=Tg(this),c=new U;this.g.set(b,{O:c,ad:new Sg(b,this)});this.j.sa(b,a);return c.promise}};let Vg=null;var Wg=class extends E{g(){return Jb(z(this,3))??""}i(){return ec(this,4)!=null}};var Xg=class extends E{g(){var a=this.F;var b=a[v]|0;const c=Ub(a,b,1);if(c!=null&&typeof c==="object"&&bb(c))var d=c;else if(Array.isArray(c)){let e=d=c[v]|0;e===0&&(e|=b&32);e|=b&2;e!==d&&(c[v]=e);d=new Wg(c)}else d=void 0;d!==c&&d!=null&&B(a,b,1,d);(a=d)||(a=Wg[Ta])||(b=new Wg,a=b.F,a[v]|=34,a=Wg[Ta]=b);return a}},Yg=function(a){return b=>{if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b[v]|=32;b=new a(b)}return b}}(Xg);var Zg=class extends E{};let $g=void 0;var ah=class{constructor(){const a={};this.i=(b,c)=>a[b]!=null?a[b]:c;this.l=(b,c)=>a[b]!=null?a[b]:c;this.o=(b,c)=>a[b]!=null?a[b]:c;this.u=(b,c)=>a[b]!=null?a[b]:c;this.j=(b,c)=>a[b]!=null?c.concat(a[b]):c;this.g=()=>{}}};function V(a){return I(ah).i(a.g,a.defaultValue)}function W(a){return I(ah).l(a.g,a.defaultValue)};var ch=class{constructor(){this.wasPlaTagProcessed=!1;this.wasReactiveAdConfigReceived={};this.adCount={};this.wasReactiveAdVisible={};this.stateForType={};this.reactiveTypeEnabledInAsfe={};this.wasReactiveTagRequestSent=!1;this.reactiveTypeDisabledByPublisher={};this.tagSpecificState={};this.messageValidationEnabled=!1;this.floatingAdsStacking=new bh;this.sideRailProcessedFixedElements=new Set;this.sideRailAvailableSpace=new Map;this.sideRailPlasParam=new Map;this.sideRailMutationCallbacks=[];this.clickTriggeredInterstitialMayBeDisplayed= 
!1}},bh=class{constructor(){this.maxZIndexRestrictions={};this.nextRestrictionId=0;this.maxZIndexListeners=[]}};function dh(a){const b=eh(a);Da(a.floatingAdsStacking.maxZIndexListeners,c=>c(b))}function eh(a){a=Nc(a.floatingAdsStacking.maxZIndexRestrictions);return a.length?Math.min.apply(null,a):null} 
var fh=class{constructor(a){a.google_reactive_ads_global_state?(a.google_reactive_ads_global_state.sideRailProcessedFixedElements==null&&(a.google_reactive_ads_global_state.sideRailProcessedFixedElements=new Set),a.google_reactive_ads_global_state.sideRailAvailableSpace==null&&(a.google_reactive_ads_global_state.sideRailAvailableSpace=new Map),a.google_reactive_ads_global_state.sideRailPlasParam==null&&(a.google_reactive_ads_global_state.sideRailPlasParam=new Map),a.google_reactive_ads_global_state.sideRailMutationCallbacks== 
null&&(a.google_reactive_ads_global_state.sideRailMutationCallbacks=[])):a.google_reactive_ads_global_state=new ch;this.floatingAdsStacking=a.google_reactive_ads_global_state.floatingAdsStacking}addListener(a){this.floatingAdsStacking.maxZIndexListeners.push(a);a(eh(this))}removeListener(a){Ha(this.floatingAdsStacking.maxZIndexListeners,b=>b===a)}},gh=class{constructor(a){this.controller=a;this.g=null}};function hh(a){const b={bottom:"auto",clear:"none",display:"inline","float":"none",height:"auto",left:"auto",margin:0,"margin-bottom":0,"margin-left":0,"margin-right":"0","margin-top":0,"max-height":"none","max-width":"none",opacity:1,overflow:"visible",padding:0,"padding-bottom":0,"padding-left":0,"padding-right":0,"padding-top":0,position:"static",right:"auto",top:"auto","vertical-align":"baseline",visibility:"visible",width:"auto","z-index":"auto"};Da(Object.keys(b),c=>{const d=a.style[Dc(c)]; 
(typeof d!=="undefined"?d:a.style[jd(a,c)])||G(a,c,b[c])});Sc(a)};function ih(a,b){const c=Yc("STYLE",a);c.textContent=Cc(Gc`* { pointer-events: none; }`);a?.head.appendChild(c);setTimeout(()=>{a?.head.removeChild(c)},b)}function jh(a,b,c){if(!a.body)return null;const d=new kh;d.apply(a,b);return()=>{var e=c||0;e>0&&ih(b.document,e);G(a.body,{filter:d.g,webkitFilter:d.g,overflow:d.l,position:d.j,top:d.o});b.scrollTo(0,d.i)}} 
class kh{constructor(){this.g=this.o=this.j=this.l=null;this.i=0}apply(a,b){this.l=a.body.style.overflow;this.j=a.body.style.position;this.o=a.body.style.top;this.g=a.body.style.filter?a.body.style.filter:a.body.style.webkitFilter;this.i=b.pageYOffset===void 0?(b.document.documentElement||b.document.body.parentNode||b.document.body).scrollTop:b.pageYOffset;G(a.body,"top",`${-this.i}px`)}};function lh(a,b){var c;if(!a.i)for(a.i=[],c=a.g.parentElement;c;){a.i.push(c);if(a.I(c))break;c=c.parentNode&&c.parentNode.nodeType===1?c.parentNode:null}c=a.i.slice();let d,e;for(d=0;d<c.length;++d)(e=c[d])&&b.call(a,e,d,c)}var mh=class extends L{constructor(a,b,c){super();this.g=a;this.N=b;this.B=c;this.i=null;M(this,()=>this.i=null)}I(a){return this.B===a}};function nh(a,b){const c=a.B;if(c)if(b){b=a.C;if(b.g==null){var d=b.controller;const e=d.floatingAdsStacking.nextRestrictionId++;d.floatingAdsStacking.maxZIndexRestrictions[e]=2147483646;dh(d);b.g=e}Pc(c,{display:"block"});a.u.body&&!a.j&&(a.j=jh(a.u,a.N,a.M));c.setAttribute("tabindex","0");c.setAttribute("aria-hidden","false");a.u.body.setAttribute("aria-hidden","true")}else b=a.C,b.g!=null&&(d=b.controller,delete d.floatingAdsStacking.maxZIndexRestrictions[b.g],dh(d),b.g=null),Pc(c,{display:"none"}), 
a.j&&(a.j(),a.j=null),a.u.body.setAttribute("aria-hidden","false"),c.setAttribute("aria-hidden","true")}function oh(a){nh(a,!1);const b=a.B;if(b){var c=ph(a.J);lh(a,d=>{Pc(d,c);hh(d)});a.g.setAttribute("width","");a.g.setAttribute("height","");G(a.g,c);G(a.g,qh);G(b,rh);G(b,{background:"transparent"});Pc(b,{display:"none",position:"fixed"});hh(b);hh(a.g);(Ba()&&kc()?Zc(a.J):1)<=1||(G(b,{overflow:"scroll","max-width":"100vw"}),Sc(b))}} 
class sh extends mh{constructor(a,b){var c=window,d=W(kf);super(a,c,b);this.j=null;this.u=c.document;this.M=d;a=new fh(c);this.C=new gh(a);this.J=c}o(){nh(this,!1)}}function ph(a){a=Ba()&&kc()?Zc(a):1;a=100*(a<1?1:a);return{width:`${a}vw`,height:`${a}vh`}}var rh={backgroundColor:"white",opacity:"1",position:"fixed",left:"0px",top:"0px",margin:"0px",padding:"0px",display:"none",zIndex:"2147483647"},qh={left:"0",position:"absolute",top:"0"};var th=class extends sh{constructor(a,b){super(a,b);oh(this)}I(a){a.classList?a=a.classList.contains("adsbygoogle"):(a=a.classList?a.classList:(typeof a.className=="string"?a.className:a.getAttribute&&a.getAttribute("class")||"").match(/\S+/g)||[],a=Ca(a,"adsbygoogle")>=0);return a}};function uh(){const a=window.google_ad_modifications=window.google_ad_modifications||{};a.afg_slotcar_vars||(a.afg_slotcar_vars={});return a.afg_slotcar_vars};function ke(a,b){a.i.size>0||vh(a);const c=a.i.get(0);c?c.push(b):a.i.set(0,[b])}function wh(a,b,c,d){F(b,c,d);M(a,()=>jc(b,c,d))}function xh(a,b){a.state!==1&&(a.state=1,a.i.size>0&&yh(a,b))} 
function vh(a){a.g.document.visibilityState?wh(a,a.g.document,"visibilitychange",b=>{a.g.document.visibilityState==="hidden"&&xh(a,b);a.g.document.visibilityState==="visible"&&(a.state=0)}):"onpagehide"in a.g?(wh(a,a.g,"pagehide",b=>{xh(a,b)}),wh(a,a.g,"pageshow",()=>{a.state=0})):wh(a,a.g,"beforeunload",b=>{xh(a,b)})}function yh(a,b){for(let c=9;c>=0;c--)a.i.get(c)?.forEach(d=>{d(b)})}var zh=class extends L{constructor(a){super();this.g=a;this.state=0;this.i=new Map}};async function Ah(a,b){var c=10;return c<=0?Promise.reject(Error(`wfc bad input ${c} ${200}`)):b()?Promise.resolve():new Promise((d,e)=>{const f=a.setInterval(()=>{--c?b()&&(a.clearInterval(f),d()):(a.clearInterval(f),e(Error(`wfc timed out ${c}`)))},200)})};function Bh(a){const b=a.state.pc;return b!==null&&b!==0?b:a.state.pc=Wc(a.g)}function Ch(a){var b=a.state.wpc;if(b===null||b==="")b=a.state,a=a.g,a=a.google_ad_client?String(a.google_ad_client):(a.google_ad_modifications=a.google_ad_modifications||{}).head_tag_slot_vars?.google_ad_client??a.document.querySelector(".adsbygoogle[data-ad-client]")?.getAttribute("data-ad-client")??"",b=b.wpc=a;return b}async function Dh(a){await Ah(a.g,()=>!(!Bh(a)||!Ch(a)))} 
async function Eh(a,b){await Dh(a);var c=a.l;var d=new Od;var e=Bh(a);d=C(d,1,Eb(e),"0");e=Ch(a);d=D(d,2,e);d=C(d,3,Eb(a.state.sd),"0");a=C(d,7,Eb(Math.round(a.g.performance.now())),"0");a=C(a,3,Eb(1),"0");b=dc(a,10,Pd,b);ge(c,b)}var Fh=class{constructor(a,b){this.g=fd()||window;this.i=b??new zh(this.g);this.l=a??new me(100,100,!0,this.i);this.state=Oe(Le(),33,()=>{const c=W(Ue);return{sd:c,ssp:c>0&&Lc()<1/c,pc:null,wpc:null,cu:null,le:[],lgdp:[],psi:null,tar:0,cc:null}})}};function Gh(a){var b=window;return a.google_adtest==="on"||a.google_adbreak_test==="on"||b.location.host.endsWith("h5games.usercontent.goog")||b.location.host==="gamesnacks.com"?b.document.querySelector('meta[name="h5-games-eids"]')?.getAttribute("content")?.split(",").map(c=>Math.floor(Number(c))).filter(c=>!isNaN(c)&&c>0)||[]:[]};class Hh{};function Ih(){var a=p.ggeac||(p.ggeac={});ne(I(oe),a);Jh(a);I(Hh);I(ah).g()}function Jh(a){const b=I(ah);b.i=(c,d)=>J(5,a,()=>!1)(c,d,1);b.l=(c,d)=>J(6,a,()=>0)(c,d,1);b.o=(c,d)=>J(7,a,()=>"")(c,d,1);b.u=(c,d)=>J(8,a,()=>[])(c,d,1);b.j=(c,d)=>J(17,a,()=>[])(c,d,1);b.g=()=>{J(15,a,()=>{})(1)}};function Kh(a){const b=I(oe).g();a=Gh(a);return b.concat(a).join(",")};function Lh({Ja:a,Sa:b}){return a||(b==="dev"?"dev":"")};function Mh(a){Ge.j(b=>{b.shv=String(a);b.mjsv=Lh({Ja:Td(),Sa:a});b.eid=Kh(p)})}function Nh(a,b){const c=b?.g();b=c?.g()||(Jb(z(a,2))??"");a=c?.i()?ec(c,4)??!1:ec(a,6)??!1;Mh(b);eb($g,ib);$g=a};function Oh(){var a=window.adsbygoogle;try{const b=a.pageState;eb(b,w);return Yg(b)}catch(b){return new Xg}};var Ph=typeof sttc==="undefined"?void 0:sttc;function Qh(){var a=Ge;try{if(eb(Ph,w),Ph.length>0)return new Zg(JSON.parse(Ph))}catch(b){a.P(838,b instanceof Error?b:Error(String(b)))}return new Zg};var Rh=class extends L{D(){this.disposeAd();super.D()}},Sh=class extends L{constructor(a){super();this.callback=a}},Th=class extends L{constructor(a){super();this.i=a;this.g=new Set}fetch(a,b){const c=new Sh(a.callback);this.g.add(c);this.i.fetch({...a,callback:d=>{c.l?d&&d.dispose():c.callback(d);this.g.delete(c)}},b)}D(){for(const a of this.g.values())a.dispose();this.g.clear();super.D()}};var Uh=class{constructor(a){var b=Td();this.o=a;this.u=b;this.l="unset"}ha(a){this.l=a}X(a){this.g=a.Da;this.i=a.Ga}L(a){this.j=a}A(a,b={}){b.event=a;b.client=this.l;b.bow_v=this.o;b.js_v=this.u;b.fetcher=this.j?.toString()??"unset";this.g&&(b.admb_iid=this.g);this.i&&(b.admb_rid=this.i);a=I(oe).g();!b.eid&&a.length&&(b.eid=a.toString());Hd(Ee,"slotcar",b,!0,1)}};var Vh=class extends Rh{constructor(a,b,c,d){super();this.ad=a;this.j=b;this.o=c;this.i=d;this.g=null;this.u=this.B=!1;this.C=!0}show(a){this.g=a;if(this.C&&this.u)this.ad.show();else if(this.u)this.K();else throw Error("Tried to show AdMobAd before it finished loading.");}disposeAd(){this.ad.dispose()}T(){this.u=!0;this.o(this)}R(){this.o(null);this.dispose()}U(){this.i.A("admb_na");this.g?this.K():this.C=!1}}; 
function Wh(a){return{T:K(849,()=>{a.T()}),R:K(850,()=>{a.R()}),K:K(851,()=>{a.K()}),U:K(854,()=>{a.U()})}}var Xh=class extends Vh{constructor(a,b,c,d){super(a,b,c,d);this.ad=a;this.j=b;this.o=c;this.i=d}request(){this.ad.load(this.j,Wh(this))}K(){(0,this.g)(1)}};function Yh(a){return{T:K(849,()=>{a.T()}),R:K(850,()=>{a.R()}),Oa:K(855,()=>{a.i.A("admb_rfs");(0,a.g)(2)}),Qa:K(852,()=>{a.B=!0}),K:K(853,()=>{a.K()}),U:K(854,()=>{a.U()})}} 
var Zh=class extends Vh{constructor(a,b,c,d){super(a,b,c,d);this.ad=a;this.j=b;this.o=c;this.i=d}request(){this.ad.load(this.j,Yh(this))}K(){this.B?(0,this.g)(3):(0,this.g)(2)}};function $h(a,b){const c=b.google_adbreak_test==="on";switch(a){case 1:return c?"ca-app-pub-3940256099942544/1033173712":b.google_admob_interstitial_slot;case 2:return c?"ca-app-pub-3940256099942544/5224354917":b.google_admob_rewarded_slot;default:throw Error(`Unknown ad type ${a}`);}}function ai(a,b,c){a.j.error(`Unable to fetch ad: '${b}' is missing from tag.`);c(null)}function bi(a){Je(850,()=>{a(null)})} 
var ci=class{constructor(a,b,c){this.i=a;this.j=b;this.g=c;this.l=Wc(window).toString()}fetch(a,b){const c={isTestDevice:!1,httpTimeoutMillis:W(Ze)*1E3};var d=b.google_tag_for_child_directed_treatment;if(d==="0"||d==="1")c.tagForChildDirectedTreatment=d==="1";d=b.google_tag_for_under_age_of_consent;if(d==="0"||d==="1")c.tagForUnderAgeOfConsent=d==="1";d=b.google_max_ad_content_rating;typeof d==="string"&&(c.maxAdContentRating=d);c.extras??(c.extras={});c.extras.muted=a.Ea||a.type===2?"0":"1";this.l&& 
(c.extras.pvsid=this.l);c.extras.correlator=Re().toString();d=Kh(b);d.length&&(c.extras.slotcar_eids=d);b=$h(a.type,b);a.type===1?typeof b!=="string"?ai(this,"data-admob-interstitial-slot",a.callback):this.i.ra(b).then(e=>{(new Xh(e,c,a.callback,this.g)).request()}).catch(()=>{bi(a.callback)}):typeof b!=="string"?ai(this,"data-admob-rewarded-slot",a.callback):this.i.sa(b).then(e=>{(new Zh(e,c,a.callback,this.g)).request()}).catch(()=>{bi(a.callback)})}};const di=new Set(["auto","on"]),ei=new Set(["on","off"]),fi=new Set("start pause next browse reward preroll".split(" ")),gi=new Map([["start","interstitial"],["pause","interstitial"],["next","interstitial"],["browse","interstitial"],["reward","reward"],["preroll","preroll"]]),hi=new Map([["interstitial",["type"]],["reward",["type","beforeReward","adDismissed","adViewed"]],["preroll",["type","adBreakDone"]]]),ii=new Map([["interstitial",["beforeReward","adDismissed","adViewed"]],["reward",[]],["preroll", 
["afterAd","beforeReward","adDismissed","adViewed"]]]),ji="beforeAd afterAd beforeReward adDismissed adViewed adBreakDone".split(" "),ki=new Map([["beforeBreak","beforeAd"],["afterBreak","afterAd"],["adComplete","adViewed"]]);var li=new Set("google_ad_client google_ad_host google_ad_channel google_ad_host_channel google_tag_for_under_age_of_consent google_tag_for_child_directed_treatment google_page_url".split(" ")); 
const mi=new Set([...li,"google_admob_interstitial_slot","google_admob_rewarded_slot","google_max_ad_content_rating"]); 
function ni(a,b){let c=!1;const d=f=>{c=!0;b.error(`Invalid ad config: ${f}.`)};if(a.preloadAdBreaks!=null&&!di.has(a.preloadAdBreaks)){var e=Array.from(di).map(f=>`'${f}'`).join(", ");d(`'preloadAdBreaks' must be one of [${e}]`)}a.sound==null||ei.has(a.sound)||(e=Array.from(ei).map(f=>`'${f}'`).join(", "),d(`'sound' must be one of [${e}]`));a.onReady!=null&&typeof a.onReady!=="function"&&d("'onReady' must be a function");if(a.h5AdsConfig!=null)if(typeof a.h5AdsConfig!=="object")d("'h5AdsConfig' must be an object"); 
else for(const [f,g]of Object.entries(a.h5AdsConfig))a=f,e=g,mi.has(a)?typeof e!=="string"&&d(`'h5AdsConfig.${a}' must be a string`):d(`'h5AdsConfig.${a}' is not a valid property`);return!c} 
function oi(a,b,c){for(const [d,e]of ki){const f=d,g=e;if(f in a){c.A("lgc_fld",{field:f});if(g in a)return b.error(`Invalid placement config: '${f}' has been renamed to ${g}. Cannot pass both fields. Please use ${g} only.`),!1;b.warn(`Placement config: '${f}' has been renamed to '${g}'. Please update your code.`);a[g]=a[f];delete a[f]}}return!0} 
function pi(a,b,c){let d=!1;const e=h=>{d=!0;b.error(`Invalid placement config: ${h}.`)};a=Object.assign({},a);if(!oi(a,b,c))return{ua:!1,xa:a};if(!fi.has(a.type)){var f=Array.from(fi).map(h=>`'${h}'`).join(", ");e(`'type' must be one of [${f}]`);return{ua:!d,xa:a}}c=gi.get(a.type);const g=hi.get(c).filter(h=>!(h in a));g.length>0&&e("missing required properties "+g.map(h=>`'${h}'`).join(", "));c=ii.get(c).filter(h=>h in a);c.length>0&&e("the following properties are not used for the given ad type: "+ 
c.map(h=>`'${h}'`).join(", "));for(f of ji)f in a&&typeof a[f]!=="function"&&e(`'${f}' must be a function`);return{ua:!d,xa:a}};const qi={[1]:10,[2]:11},ri={closed:1,viewed:3,dismissed:2,error:4};var ti=class{constructor(){this.g=window}fetch(a,b){const c={};for(const d in b)li.has(d)&&(c[d]=b[d]);c.google_reactive_ad_format=qi[a.type];c.google_wrap_fullscreen_ad=!0;c.google_video_play_muted=a.type!==2&&!a.Ea;c.google_acr=d=>{a.callback(d?new si(d):null)};c.google_tag_origin="gsc";this.g.adsbygoogle.push({params:c})}},si=class extends Rh{constructor(a){super();this.ad=a}show(a){this.ad.show(b=>{a(ri[b.status])})}disposeAd(){this.ad.disposeAd()}};function ui(){return O('<ins class="adsbygoogle" style="width:100% !important;height:100% !important;" id="fake-interstitial-ins"><iframe style="overflow:hidden;" width="100%" height="100%" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" scrolling="no" src="about:blank" id="aswift-fake"></iframe></ins>')} 
function vi(){return O('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path style="fill:#f5f5f5" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/><path fill="none" d="M0 0h24v24H0V0z"/></svg>')} 
function wi(a){const b=a.Ma;a=a.Ta;return O('<div class="dialog-wrapper" style="width: 100%; height: 100%; position: absolute; top: 0;"><div class="close-confirmation-dialog" id="close-confirmation-dialog" style="width: '+P(Q(Math.floor(a*.78)))+'px"><div class="confirmation-title" style="font-size: '+P(Q(Math.floor(b*.031)))+"px; margin-top: "+P(Q(Math.floor(b*.0375)))+"px; margin-left: "+P(Q(Math.floor(a*.066)))+"px; margin-right: "+P(Q(Math.floor(a*.066)))+'px;">Close Ad?</div><div class="confirmation-message" style="font-size: '+ 
P(Q(Math.floor(b*.025)))+"px; margin-bottom: "+P(Q(Math.floor(b*.0375)))+"px; margin-top: "+P(Q(Math.floor(b*.0375)))+"px; margin-left: "+P(Q(Math.floor(a*.066)))+"px; margin-right: "+P(Q(Math.floor(a*.066)))+'px;">You will lose your reward</div><div class="confirmation-buttons" style="font-size: '+P(Q(Math.floor(b*.0218)))+"px; line-height: "+P(Q(Math.floor(b*.05625)))+"px; margin-right: "+P(Q(Math.floor(b*.0125)))+"px; margin-bottom: "+P(Q(Math.floor(b*.0125)))+'px;"><div class="close-ad-button" id="close-ad-button" style="padding-left: '+ 
P(Q(Math.floor(a*.044)))+"px; padding-right: "+P(Q(Math.floor(a*.044)))+'px;">CLOSE</div><div class="resume-ad-button" id="resume-ad-button" style="padding-left: '+P(Q(Math.floor(a*.044)))+"px; padding-right: "+P(Q(Math.floor(a*.044)))+'px;">RESUME</div></div></div></div>')};var xi=Ic`about:blank`;xc(xi);var yi=Ic`javascript:undefined`;xc(yi);function zi(a,b,c){a=a.g;c=b(c||Kf,{});b=a||pa||(pa=new id);if(c&&c.g)b=c.g();else{b=gd(b.g,"DIV");b:if(ia(c)){if(c.wa&&(c=c.wa(),c instanceof yc))break b;c=Hc("zSoyz")}else c=Hc(String(c));b.innerHTML=Ac(c)}b.childNodes.length==1&&(c=b.firstChild,c.nodeType==1&&(b=c));return b}class Ai{constructor(){this.g=pa||(pa=new id)}render(a,b){a=a(b||{},{});return String(a)}};function Bi(a,b){if(a.contentDocument||a.contentWindow)b(a);else{const c=()=>{b(a);jc(a,"load",c)};F(a,"load",c)}} 
async function Ci(a){if(a.g==null)throw Error("Tried to show ad before initialized.");const b=new U;var c=a.g.g,d=Math.min(Number(c.clientWidth),Number(c.clientHeight));let e=Math.max(Number(c.clientWidth),Number(c.clientHeight));Di(a)&&(d*=.5,e*=.5);c=c.contentDocument;a=c.body.appendChild(zi(a.B,wi,{Ta:d,Ma:e}));d=a.querySelector(".resume-ad-button");F(a.querySelector(".close-ad-button"),"click",()=>{b.resolve(0)});F(d,"click",()=>{b.resolve(1)});d=await b.promise;c.body.removeChild(a);return d=== 
0}function Di(a){if(a.g==null)throw Error("Tried to show ad before initialized.");a=a.g.g;return Number(a.clientWidth)>1E3||Number(a.clientHeight)>1E3} 
var Ei=class extends Rh{constructor(a,b){super();this.u=b;this.B=new Ai;this.i=10;this.o=!1;this.j=zi(this.B,ui);this.j.dataset["slotcar"+(b===1?"Interstitial":"Rewarded")]="true";document.documentElement.appendChild(this.j);Bi(this.j.firstChild,c=>{var d={};var e=this.u===2?"Rewarded ad example":"Interstitial ad example";var f=this.u,g=d??{},h=g.qb;const k=g.sb,m=g.mb,n=g.tb,q=g.pb,r=g.rb,y=g.nb;!g.ob&&(h instanceof qf?h.content:h)?(g=g&&g.vb,h=O((h instanceof qf?h.content:h)?"<script"+(g?' nonce="'+ 
P(Jf(g))+'"':"")+">window['ppConfig'] = {productName: "+Cf(Df(k??"unknown"))+", deleteIsEnforced: "+Cf(Df(!!m))+", sealIsEnforced: "+Cf(Df(!!n))+", heartbeatRate: "+Cf(Df(q??.5))+", periodicReportingRateMillis: "+Cf(Df(r??6E4))+", disableAllReporting: "+Cf(Df(y??!1))+"};"+Cf(Df(h??""))+"\x3c/script>":"")):h="";h="<!DOCTYPE html><html><head>"+O(h);d=(d=d??{})&&d.wb;d=O("\n  <style"+(d?' nonce="'+P(Jf(d))+'"':"")+'>\n    body {\n      padding: 0;\n      margin: 0;\n      background-color: #262626;\n    }\n    .container {\n      width: 100vw;\n      height: 92vh;\n      display: flex;\n      flex-direction: column;\n    }\n    .container .creative {\n      background-color: white;\n      border-style: solid;\n      border-width: thin;\n      border-color:#bdc1c6;\n      height: 250px;\n      margin: 20vh auto auto auto;\n      overflow: hidden;\n      padding: 0;\n      width: 300px;\n    }\n    .header-panel {\n      display: flex;\n      justify-content: center;\n      margin-bottom: 20px;\n      background-color: #424242;\n      border: 1px solid transparent;\n      border-radius: 4px;\n      height: 8vh;\n      color: #f5f5f5;\n      font-family: "Google Sans",Roboto,Arial,sans-serif;\n      font-size: 20px;\n      line-height: 8vh;\n    }\n    .dismiss-button {\n      display: flex;\n      flex-direction: row;\n      height: inherit;\n      align-items: center;\n      padding-right: 4%;\n      cursor: pointer;\n      position: absolute;\n      right: 0;\n    }\n    .count-down-container {\n      display: inline-flex;\n      flex: auto;\n    }\n    .adContainer {\n      display: flex;\n      flex-direction: row;\n      width: 100%;\n      height: 100%;\n      text-align: left;\n      margin: 0;\n    }\n    .adContainer .logo {\n      align-self: center;\n      width: 40px;\n      margin: 0 24px;\n      height: 40px;\n    }\n    .adContainer .logo IMG {\n      height: 40px;\n      width: 40px;\n    }\n    .adContainer .text {\n      margin: auto auto auto 0;\n    }\n    .adContainer .button {\n      align-self: center;\n      height: 100%;\n      max-height: 48px;\n      /* This gives a perceived margin of 32px, due to the margins within the button SVGs. */\n      margin-right: 30px;\n    }\n    .adContainer .button-inner {\n      max-height: 48px;\n      height: 100%;\n    }\n    .adContainer .button-inner SVG {\n      height: 100%;\n      width: auto;\n    }\n    .adText {\n      font-family: "Google Sans",Roboto,Arial,sans-serif;\n      font-size: 18px;\n      font-weight: normal;\n      line-height: 18px;\n      color: #202124;\n      margin-bottom: 4px;\n    }\n    .nativeIframeMessage .text {\n      padding: 0 10px;\n    }\n    .creative a {\n      text-decoration: none;\n    }\n\n    @media (max-height: 44px),\n        (max-height: 150px) and (max-width: 210px) {\n      .adContainer .logo {\n        display: none;\n      }\n      .adContainer .text {\n        margin-left: 5px;\n      }\n    }\n    @media (max-height: 110px) and (max-width: 330px) {\n      .adText {\n        font-size: 13px;\n        line-height: 13px;\n        margin-bottom: 2px;\n      }\n    }\n    @media (max-height: 38px) {\n      .adText {\n        font-size: 17px;\n        line-height: 17px;\n        margin-bottom: 0;\n      }\n    }\n    @media (max-height: 20px) {\n      .adText {\n        font-size: 12px;\n        line-height: 12px;\n        margin-bottom: 0;\n      }\n    }\n\n    /* Vertically stacked assets in cases where creative is not a distictly\n       horizontal rectangle shape */\n    @media (min-height: 240px),\n        (max-width: 65px) and (min-height: 50px),\n        (max-width: 130px) and (min-height: 100px),\n        (max-width: 195px) and (min-height: 150px),\n        (max-width: 260px) and (min-height: 200px) {\n      .adContainer .logo {\n        display: initial;\n      }\n      .adContainer .text {\n        margin-left: 0;\n      }\n      .adContainer {\n        text-align: center;\n        display: flex;\n        flex-direction: column;\n      }\n      .adContainer .logo {\n        margin: 40px auto 24px auto;\n      }\n      .adContainer .text {\n        margin: 0 auto auto auto;\n      }\n      .adContainer .text .adText{\n        margin-bottom: 8px;\n      }\n      .adContainer .button {\n        margin: auto auto 32px auto;\n      }\n      @media (max-height: 200px) {\n        .adContainer .logo {\n          display: none;\n        }\n        .adContainer .text {\n          margin: 10px auto auto auto;\n        }\n      }\n    }\n\n    .x-button {\n      display: flex;\n      align-items: center;\n    }\n\n    .dialog-wrapper {\n      background: rgba(0, 0, 0, .4);\n      height: 100%;\n      left: 0;\n      opacity: 1;\n      pointer-events: auto;\n      position: fixed;\n      top: 0;\n      transition: opacity .15s ease-out;\n      -webkit-transition: opacity .15s ease-out;\n      width: 100%;\n      will-change: opacity;\n      z-index: 2147483647;\n    }\n\n    .close-confirmation-dialog {\n      background: #fff;\n      box-shadow: 0 16px 24px 2px rgba(0, 0, 0, .14),\n        0 6px 30px 5px rgba(0, 0, 0, .12), 0 8px 10px -5px rgba(0, 0, 0, .2);\n      font-family: Roboto, sans-serif;\n      left: 50%;\n      position: fixed;\n      top: 50%;\n      transform: translate(-50%, -50%);\n      -webkit-transform: translate(-50%, -50%);\n    }\n\n    .confirmation-title {\n      color: #000;\n    }\n\n    .confirmation-message {\n      color: #757575;\n    }\n\n    .confirmation-buttons {\n      display: -webkit-box;\n      display: -webkit-flex;\n      display: flex;\n\n      -webkit-box-align: center;\n      -webkit-align-items: center;\n      align-items: center;\n\n      -webkit-box-pack: flex-end;\n      -webkit-justify-content: flex-end;\n      justify-content: flex-end;\n    }\n\n    .close-ad-button,\n    .resume-ad-button {\n      color: #fff;\n      cursor: pointer;\n      font-weight: 500;\n      text-align: center;\n\n      display: -webkit-box;\n      display: -webkit-flex;\n      display: flex;\n    }\n\n    .close-ad-button {\n      color: #3e82f7;\n    }\n\n    .resume-ad-button {\n      background: #3e82f7;\n      border-radius: 2px;\n      box-shadow: 0 2px 2px 0 rgba(0, 0, 0, .24);\n    }\n  </style>\n  '); 
d=h+d+'</head><body><div class="header-panel">';f!==2&&(d+="Ad");d+='<div class="dismiss-button" id="dismiss-button">'+(f===2?'<div class="count-down-container" id="count-down-container"><div id="count-down"><div class="count-down-text" id="count-down-text"></div></div><div class="x-button" id="close-button" style="padding-left: 5px;">'+vi()+"</div></div>":"")+'<div class="x-button" id="dismiss-button-element">'+vi()+'</div></div></div><div class="container"><div class="creative">'+O('<div style="position:relative;float:right;top:1px;right:1px;width:15px;height:15px;"><svg style="fill:#00aecd;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 15 15"><circle cx="6" cy="6" r="0.67"></circle><path d="M4.2,11.3Q3.3,11.8,3.3,10.75L3.3,4.1Q3.3,3.1,4.3,3.5L10.4,7.0Q12.0,7.5,10.4,8.0L6.65,10.0L6.65,7.75a0.65,0.65,0,1,0,-1.3,0L5.35,10.75a0.9,0.9,0,0,0,1.3,0.8L12.7,8.2Q13.7,7.5,12.7,6.7L3.3,1.6Q2.2,1.3,1.8,2.5L1.8,12.5Q2.2,13.9,3.3,13.3L4.8,12.5A0.3,0.3,0,1,0,4.2,11.3Z"></path></svg></div>')+ 
'<a target="_blank" href="https://developers.google.com/ad-placement"><div class="adContainer"><div class="logo">'+O('<img width="40" height="40" alt="" src="data:image/svg+xml;base64,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"/>')+ 
'</div><div class="text"><div class="adText">'+(e!=null&&e.V===nf?e:e instanceof yc?O(Ac(e).toString()):O(String(String(e)).replace(Bf,Af),sf(e)))+"</div></div></div></a></div></div></body></html>";e=O(d).wa();f=c.contentDocument||c.contentWindow.document;f.open();f.write(Ac(e));f.close();this.g=new th(c,this.j);a(this)})}show(a){if(this.g==null)throw Error("Tried to show ad before initialized.");const b=this.g.g.contentDocument,c=b.getElementById("dismiss-button");nh(this.g,!0);if(this.u===2){const d= 
c.querySelector("#dismiss-button-element");d.style.display="none";const e=async()=>{if(this.g==null)throw Error("Failure on rewarded example: Could not find ad frame.");this.o=!0;await Ci(this)?(this.g.o(),F(c,"click",e),a(2)):this.o=!1};F(c,"click",e);this.i=W(cf);const f=this.i<0;this.o=!1;const g=b.getElementById("count-down-container"),h=g.querySelector("#count-down-text");h.innerText=`Reward in ${this.i} seconds`;f||(this.C=setInterval(()=>{this.o||(--this.i,h.innerText=`Reward in ${this.i} seconds`); 
if(this.i===0){g.style.display="none";d.style.display="";clearInterval(this.C);const k=async()=>{if(this.g==null)throw Error("Failure on rewarded example: Could not find ad frame.");this.g.o();jc(c,"click",k);a(3)};F(c,"click",k);jc(c,"click",e)}},1E3))}else F(c,"click",()=>{if(this.g==null)throw Error("Failure on rewarded example: Could not find ad frame.");this.g.o();a(1)})}disposeAd(){this.g?.o();hd(this.j)}},Fi=class{fetch(a){new Ei(a.callback,a.type)}};var Gi=class{constructor(){this.j=I(Fh);this.o={inv_plcnf:1,inv_adcnf:2,adbr_cl:3,adbr_noad:4,adbr_nousitr:5,adbr_usrint:6,adbr_naf:7,adbr_pgad:8,adbr_pgaatd:9,adbr_tepgai:10,adcf_cl:11,adcf_afni:29,adcf_pgad:13,adcf_pgaatd:14,prf_suc:15,prf_fail:16,ad_sr:32,admb_na:17,admb_rfs:18,admb_fetfail:19,lgc_fld:20,pr_rr:21,pr_to:22,r_to:31,api_ld:23,admb_tm:24,adbr_dn:25,dbl_init:26,sess_m:27,ad_cls:28,ad_rdtr:30};this.u={admob:1,adsense:2,adbreaktest:0}}ha(){}X(a){this.g=a.Da;this.i=a.Ga}L(a){this.l=this.u[a]}async A(a){var b= 
new Nd;a=fc(b,1,this.o[a]).L(this.l);this.g&&D(a,3,this.g);this.i&&D(a,4,this.i);await Eh(this.j,a)}},Hi=class{constructor(a){this.qa=new Gi;this.fa=a}ha(a){this.fa.ha(a)}X(a){this.qa.X(a);this.fa.X(a)}L(a){this.qa.L(a);this.fa.L(a)}async A(a,b={}){await this.qa.A(a,b);this.fa.A(a,b)}};function Ii(a){let b=a.l;a.g!==null&&(b+=(Date.now()-a.g)/1E3);return Math.min(b,a.j)}function Ji(a){const b=Ii(a);if(b<a.i)throw Error("Current tokens in seconds cannot be less than frequency cap in seconds when ad is shown.");a.l=b-a.i}function Ki(a){return a.g!==null&&Date.now()-a.g<a.o*1E3?!1:Ii(a)>=a.i} 
var Li=class{constructor(a,b){var c=W(hf);this.i=a;this.o=b;this.g=null;if(a<=0)throw Error("Frequency cap cannot be less than or equal to 0.");if(c<1)throw Error("Bucket capacity cannot be less than 1.");if(1.5>c)throw Error("Initial tokens cannot be greater than the bucket capacity.");this.l=1.5*a;this.j=c*a}};const Mi="click mousedown mouseup touchstart touchend pointerdown pointerup keydown keyup scroll".split(" ");var Ni=class extends L{constructor(){var a=window;super();this.g=0;const b=()=>{this.g=Date.now()};for(const c of Mi)a.document.documentElement.addEventListener(c,b,{capture:!0});M(this,()=>{for(const c of Mi)a.document.documentElement.removeEventListener(c,b,{capture:!0})})}};class Oi extends L{constructor(a,b){super();this.O=new U;this.g=!1;this.timeout=setTimeout(K(726,()=>{b()}),a*1E3)}get promise(){return this.O.promise}resolve(a){this.l||(this.g=!0,this.O.resolve(a))}reject(a){this.l||(this.g=!0,this.O.reject(a))}D(){clearTimeout(this.timeout)}} 
function Pi(a,b){if(a=a.google_adbreak_test)switch(a){case "on":return new Fi;case "adsense":break;default:throw b.error(`Unsupported data-adbreak-test value '${a}. Supported values: '${"on"}'.`),Error("unsupported test mode");}return new ti}function Qi(a){return["google_admob_interstitial_slot","google_admob_rewarded_slot"].some(b=>typeof Ri(b,a)==="string")}function Ri(a,b){if(b[a]&&typeof b[a]==="string")return String(b[a])} 
function Si(a,b){Vg==null&&(Vg=new Ug);return Vg.connect().then(c=>new ci(c,a,b))}function Ti(a){if(typeof a!=="string")return-1;a=/^(\d+)s$/.exec(a);return a==null?-1:Number(a[1])} 
function Ui(a,b){window.addEventListener("onpagehide"in self?"pagehide":"unload",K(938,()=>{if(b.first_slotcar_request_processing_time){var c=Date.now();a.g.A("sess_m",{igsl:c-b.first_slotcar_request_processing_time,afh:String(b.ad_frequency_hint),niab:Number(b.number_of_interstitial_ad_breaks),nias:Number(b.number_of_interstitial_ads_shown),opsl:c-b.adsbygoogle_execution_start_time})}}))} 
function Vi(a,b){const c=b.google_admob_ads_only;typeof c==="string"&&(c==="on"?Qi(b)?a.ca=!0:a.j.error("Cannot set data-admob-ads-only without providing at least one AdMob ad slot id."):a.j.error(`Unsupported data-admob-ads-only value '${c}'. Supported value: 'on'.`))}function Wi(a){return a.ya?"adbreaktest":a.I?"admob":"adsense"}function Xi(a,b){for(const c of[1,2]){const d=a.i.get(c);if(d||Yi(a,c))d?(d.dispose(),a.i.delete(c)):(a.u.get(c).dispose(),a.u.delete(c)),X(a,c,0,b)}} 
function Zi(a){if(!a.da||a.Ba){if(!a.na&&a.o.preloadAdBreaks){var b=V(ef)?[1]:[1,2];for(const c of b)if(!a.i.has(c)&&!a.Z.has(c))return}for(a.na=!0;a.oa.length>0;)b=a.oa.pop(),$i(a,"onReady",b)}}function aj(a,b){b=b.google_ad_frequency_hint;const c=W(df);if(typeof b!=="string")return c;const d=/^(\d+)s$/.exec(b);return d==null?(a.j.error(`Invalid data-ad-frequency-hint value: '${b}'. It must be in format 'Xs' where X is a number.`),c):Math.max(W(ff),Number(d[1]))} 
function bj(a){return V(jf)?W(Xe):a.C/2}function cj(a,b){for(const c in b)if(b[c]!==a.N[c])return!0;return!1}function dj(a,b){!a.ca||a.I?b():a.g.A("adcf_afni")}function ej(a,b,c,d=!0){const e=a.i.get(b);e&&(e.dispose(),X(a,b,10,c),d&&a.i.delete(b))}function Yi(a,b){return a.u.has(b)&&!a.u.get(b).g}function X(a,b,c,d){if(Yi(a,b))throw Error("already scheduled");c=new Oi(c,()=>{fj(a,b,d)});a.u.set(b,c);return c}function $i(a,b,c){Vc(()=>{gj(a,b,c)})} 
function Z(a,b,c,d){const e={breakType:b.type,breakFormat:c===2?"reward":b.type==="preroll"?"preroll":"interstitial",breakStatus:d};b.name&&(e.breakName=b.name);a.g.A("adbr_dn",{breakType:e.breakType,breakFormat:e.breakFormat,breakStatus:e.breakStatus,breakName:e.breakName??""});const f=b.adBreakDone;f!=null&&$i(a,"adBreakDone",()=>{f(e)})} 
async function hj(a,b){if(a.aa)return a.g.A("pr_rr"),Z(a,b,1,"frequencyCapped"),!1;a.aa=!0;const c=await ij(a,1,W($e),2);return c===1?(a.g.A("adbr_noad"),Z(a,b,1,"noAdPreloaded"),!1):c===2?(a.g.A("pr_to",{source:"slotcar"}),Z(a,b,1,"timeout"),!1):!0}async function jj(a,b){const c=await ij(a,2,W(af),3);return c===1?(a.g.A("adbr_noad"),Z(a,b,2,"noAdPreloaded"),!1):c===3?(a.g.A("r_to",{}),Z(a,b,2,"timeout"),!1):!0} 
async function kj(a,b){const c=new U;a.za=c;gj(a,"beforeReward",()=>{b.beforeReward(()=>{c.resolve(0)})});return await c.promise===0}function gj(a,b,c){if(c)try{c()}catch(d){return a.j.error(`'${b}' callback threw an error:`,d),!1}return!0}function lj(a,b){return V(We)&&b===1&&a.M!==null}function mj(a,b){b={type:1,nias:Number(b),asr:Number((b*a.C*1E3/(Date.now()-a.pa-a.ka)).toFixed(2)),bc:Number(W(hf)),md:Number(bj(a))};a.g.A("ad_sr",b)} 
async function ij(a,b,c,d){a.da&&await a.ja;a=Yi(a,b)?a.u.get(b):X(a,b,0,2);return await Promise.race([a.promise,Xc(c*1E3,d)])}function nj(a,b,c,d,e){const f=a.Ia.get(c),g=b?1:-1,h=f.length>0?f[f.length-1]:0;Math.sign(h)===g?f[f.length-1]=h+g:f.push(g);a.g.A(b?"prf_suc":"prf_fail",{type:c,src:d,stats:f.join(","),timing:Date.now()-e})} 
function fj(a,b,c){const d=Date.now();a.B.fetch({type:b,Ea:a.o.sound==="on",callback:e=>{a.Z.delete(b);const f=a.u.get(b);e?(f.resolve(0),a.i.set(b,e),M(e,()=>{a.i.delete(b)})):(f.resolve(1),a.Z.add(b),X(a,b,W(gf),5));nj(a,e!=null,b,c,d);c!==1&&c!==7||Zi(a)}},a.N)} 
var oj=class extends L{constructor(a,b){super();this.j=a;this.g=b;this.B=null;this.ia="";this.la=this.aa=this.da=this.na=this.ya=!1;this.C=0;this.ba=!1;this.za=null;this.oa=[];this.J=new dd;this.ca=this.Ba=this.I=!1;this.ma=0;this.ja=Promise.resolve();this.pa=0;this.N={};this.M=null;this.ka=0;this.o={sound:"on"};this.i=new Map;this.u=new Map;this.Aa=new Ni;this.Z=new Set;this.Ia=new Map([[1,[]],[2,[]]]);M(this,ma(Se,this.Aa))}init(a){this.ia=String(a.google_ad_client);if(this.B!=null)this.g.A("dbl_init", 
{ad_client:this.ia});else{this.N={...a};var b=uh();b.in_game_session_length=0;b.number_of_interstitial_ad_breaks=0;b.number_of_interstitial_ads_shown=0;b.ad_frequency_hint=a.google_ad_frequency_hint?String(a.google_ad_frequency_hint):"";Ui(this,b);b=navigator.userAgent;var c=RegExp("\\bwv\\b");this.da=b.includes("Android")&&c.test(b);a.google_adbreak_test==="on"&&(this.ya=!0);Vi(this,a);this.g.ha(this.ia);this.B=new Th(Pi(a,this.j));this.g.L(Wi(this));if(Qi(a)){this.g.X({Da:Ri("google_admob_interstitial_slot", 
a),Ga:Ri("google_admob_rewarded_slot",a)});const e=Date.now();b=Si(this.j,this.g).then(f=>{this.B!=null&&this.B.dispose();this.B=new Th(f);this.I=!0;this.g.L(Wi(this));Xi(this,7)}).catch(f=>{this.g.A("admb_fetfail",{error:f})}).finally(()=>{this.g.A("admb_tm",{timing:Date.now()-e})});this.da&&(this.ja=Promise.race([b,Xc(W(Ye)*1E3)]),this.ja.finally(()=>{this.Ba=!0;Zi(this)}))}this.C=aj(this,a);V(We)&&(this.M=new Li(this.C,bj(this)));this.ma=Ti(a.google_ad_start_delay_hint);this.J=new dd;var d=ic(K(791, 
()=>{if(this.J.width!==window.innerWidth||this.J.height!==window.innerHeight)if(!this.I||this.J.width!==window.innerWidth){var e=new dd,f=this.J;if(V(Ve)?e.width<f.width||e.height<f.height:1){this.g.A("ad_rdtr",{cvw:Number(e.width),cvh:Number(e.height),ovw:Number(f.width),ovh:Number(f.height)});for(const g of this.i.keys())ej(this,g,4,!1);this.i.clear()}this.J=new dd}}));window.addEventListener("resize",d);M(this,()=>{window.removeEventListener("resize",d)});this.pa=Date.now()}}handleAdConfig(a){ni(a, 
this.j)?(this.g.A("adcf_cl",{preloadAdBreaks:a.preloadAdBreaks||"",sound:a.sound||"",onReady:a.onReady?"true":"false",h5AdsConfig:a.h5AdsConfig?"true":"false"}),a.h5AdsConfig&&(cj(this,a.h5AdsConfig)&&(this.N={...this.N,...a.h5AdsConfig},dj(this,()=>{Xi(this,6)}),this.na=!1),this.la=this.aa=!1),a.sound&&this.o.sound!==a.sound&&(this.o.sound=a.sound,dj(this,()=>{ej(this,1,6)})),a.preloadAdBreaks&&!this.o.preloadAdBreaks?dj(this,()=>{this.o.preloadAdBreaks=a.preloadAdBreaks;if(this.o.preloadAdBreaks=== 
"on"){const b=V(ef)?[1]:[1,2];for(const c of b)this.i.has(c)||Yi(this,c)||X(this,c,0,1)}}):a.preloadAdBreaks&&this.o.preloadAdBreaks&&this.j.error("'adConfig' was already called to set 'preloadAdBreaks' with value "+`'${this.o.preloadAdBreaks}'`),a.onReady&&(this.oa.push(a.onReady),Zi(this))):this.g.A("inv_adcnf")}async handleAdBreak(a,b){if(V(bf)||!this.ca||this.I)if(a=pi(a,this.j,this.g),a.ua){var c=a.xa,d=c.type==="reward"?2:1;if(V(bf)&&this.ca&&!this.I)this.g.A("adbr_naf"),Z(this,c,d,"other"); 
else if(d!==1||this.ma<=0||Date.now()-this.pa>this.ma*1E3)if(a=uh(),d===1&&a.number_of_interstitial_ad_breaks++,this.g.A("adbr_cl",{type:c.type,name:c.name||"",frequency_cap:d===2?0:this.C,last_intr:Date.now()-this.Aa.g}),b&&c.type!=="preroll")Z(this,c,d,"notReady");else{if(d===2&&this.za?.resolve(1),this.i.get(d)||c.type!=="preroll"||await hj(this,c)){if(V(ef)&&c.type==="reward"&&!this.i.get(d)&&!this.la&&(this.la=!0,!await jj(this,c)))return;var e=this.i.get(d);if(e)if(d!==2||await kj(this,c))if(this.ba)this.j.error("Cannot show ad while another ad is already visible."), 
Z(this,c,d,"frequencyCapped");else if(gj(this,"beforeAd",c.beforeAd))if(lj(this,d)&&!Ki(this.M))this.g.A("adbr_noad"),Z(this,c,d,"frequencyCapped");else{this.ba=!0;d===1&&a.number_of_interstitial_ads_shown++;this.aa=!0;var f=Date.now(),g=h=>{this.ba=!1;lj(this,d)&&(this.M.g=Date.now());h===2||d===2&&h===4?$i(this,"adDismissed",c.adDismissed):h===3&&$i(this,"adViewed",c.adViewed);$i(this,"afterAd",c.afterAd);d===1?Z(this,c,d,"viewed"):Z(this,c,d,h===4?"other":h===2?"dismissed":"viewed");if(h!==4)if(e.dispose(), 
lj(this,d)){const k=Math.max(0,bj(this)-5);X(this,d,k,3)}else X(this,d,d===2?0:this.C,3);lj(this,d)&&(this.ka+=Date.now()-f);this.g.A("ad_cls",{result:h,adType:d,dur:Date.now()-f})};M(e,()=>{this.ba&&g(4)});lj(this,d)&&(Ji(this.M),mj(this,a.number_of_interstitial_ads_shown));e.show(g)}else $i(this,"afterAd",c.afterAd),Z(this,c,d,"error");else Z(this,c,d,"ignored");else Yi(this,d)?(this.g.A("adbr_noad"),Z(this,c,d,this.Z.has(d)?"other":"frequencyCapped")):(X(this,d,0,2),Z(this,c,d,"noAdPreloaded"))}}else this.g.A("adbr_tepgai"), 
V(bf)&&Z(this,c,d,"other")}else this.g.A("inv_plcnf");else this.g.A("adbr_naf")}handleAdBreakBeforeReady(a){return this.handleAdBreak(a,!0)}D(){for(const a of this.u.values())a.dispose();this.u.clear();for(const a of this.i.values())a.dispose();this.i.clear();this.B&&this.B.dispose();super.D()}};function pj(a){{p.google_llp||(p.google_llp={});var b=p.google_llp;let c=b[7];if(!c){const {promise:d,resolve:e}=new U;c={promise:d,resolve:e};b[7]=c}b=c}b.resolve(a)};Je(723,()=>{const a=new me;try{Oa(d=>{ve(a,1196,d)})}catch(d){}var b=Qh(),c=Oh();Nh(b,c);Ih();b=c.g().g()||(Jb(z(b,2))??"");b=new Hi(new Uh(b));c={error(...d){console.error("[Ad Placement API]",...d)},warn(...d){console.warn("[Ad Placement API]",...d)}};(Aa()?0:u("Trident")||u("MSIE"))?c.warn("Internet Explorer is not supported."):pj(new oj(c,b))}); 
}).call(this,"");
