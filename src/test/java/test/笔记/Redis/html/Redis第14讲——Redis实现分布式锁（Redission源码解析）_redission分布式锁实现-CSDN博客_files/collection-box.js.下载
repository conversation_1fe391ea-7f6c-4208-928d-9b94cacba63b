"use strict";function _toConsumableArray(t){if(Array.isArray(t)){for(var n=0,e=Array(t.length);n<t.length;n++)e[n]=t[n];return e}return Array.from(t)}!function(){function t(t){var n={};return document.cookie.replace(/([^=;\s]+)=([^=;\s]+)/g,function(){for(var t=arguments.length,e=Array(t),o=0;o<t;o++)e[o]=arguments[o];var i=e[1],s=e[2];n[i]=s}),t?n[t]:n}function n(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.options=n,this.userName=t("UserName"),this.collectedFolderIds=[],this.unCollectIds=[],this.needCollectId=0,this.curFolderId=0,this.curCheckId=0,this.checkOptions()&&this.init()}!function(t){var n=document.createElement("link");n.rel="stylesheet",n.type="text/css",n.href=t,document.getElementsByTagName("head")[0].appendChild(n)}("https://g.csdnimg.cn/collection-box/2.1.2/collection-box.css"),window.csdn&&window.csdn.userLogin||function(t,n){var e=document.createElement("script");e.type="text/javascript",e.readyState?e.onreadystatechange=function(){"loaded"!=e.readyState&&"complete"!=e.readyState||(e.onreadystatechange=null,n&&n())}:e.onload=function(){n&&n()},e.src=t,document.getElementsByTagName("head")[0].appendChild(e)}("https://g.csdnimg.cn/user-login/3.0.1/user-login.js"),n.prototype.init=function(){this.render().bindEvent().getList(this.renderList),this.data={url:this.options.url,source:this.options.source,sourceId:Number(this.options.source_id,10),author:this.options.author,title:this.options.title,description:this.options.description||"",fromType:"PC",username:this.userName}},n.prototype.sortListByTime=function(t){var n=this;if(!t.length)return t;var e=t.length?t[0]:"";t=t.slice(1);var o=this.getNearItemFolderId(),i=n.findNearCollectionItem(o,t),s=t.filter(function(t){return t.ID!==i.ID}),c=s.filter(function(t){return t.CheckStatus}),l=s.filter(function(t){return!t.CheckStatus});return[e,i].concat(_toConsumableArray(c),_toConsumableArray(l)).filter(function(t){return t})},n.prototype.findNearCollectionItem=function(t,n){var e="";return n.forEach(function(n){e=n.ID===t?n:e}),e},n.prototype.checkOptions=function(){var t=!0,n=this.options;return n?"url"in n&&""!==n.url?"source"in n&&""!==n.source?"source_id"in n&&""!==n.source_id?"author"in n&&""!==n.author?"title"in n&&""!==n.title||(void 0,t=!1):(void 0,t=!1):(void 0,t=!1):(void 0,t=!1):(void 0,t=!1):(void 0,t=!1),t},n.prototype.render=function(){var t=this.renderCreateFolder(),n=this.getThemeClass(),e=$('<div id="csdn-collection" class="'+n+'">\n                  <div class="csdn-collection-mask"></div>\n                  <div class="csdn-collection-container">\n                    <div class="csdn-collection-header">添加收藏夹<span class="csdn-collection-close">x</span></div>\n                    <div class="csdn-collection-body">\n                      <div class="csdn-collection-folder">\n                        <span class="csdn-collection-btn"><i></i>创建收藏夹</span>\n                        <div class="csdn-collection-input">'+t+'</div>\n                      </div>\n                      <p class="csdn-collection-msg"></p>\n                      <ul class="csdn-collection-items"></ul>\n                    </div>\n                  </div>\n                </div>');return this.box=e,this.list=this.box.find(".csdn-collection-items"),this.toggleBtn=this.box.find(".csdn-collection-btn"),this.inputBox=this.box.find(".csdn-collection-input"),this.inputTitle=this.box.find('.csdn-collection-input input[name="title"]'),this.inputDesc=this.box.find('.csdn-collection-input textarea[name="desc"]'),this.addBtn=this.box.find(".csdn-collection-input button.submit"),this.cancelBtn=this.box.find(".csdn-collection-input button.cancel"),this.privateSwitch=this.box.find(".switch-component #privateSwitch"),this.defaultSwitch=this.box.find(".switch-component #defaultSwitch"),this.closeBtn=this.box.find(".csdn-collection-close"),this.msg=this.box.find(".csdn-collection-msg"),$("body").append(e),this},n.prototype.bindEvent=function(){var t=this;this.list.on("click","li",function(n){t.clickItemHandler.call(t,$(this))}),this.toggleBtn.on("click",function(n){t.needCollectId=0,t.clickToggleBtnHandler.call(t,n)}),t.box.on("click",function(t){return t.stopPropagation(),!1}),this.addBtn.on("click",function(n){t.addBtn.hasClass("disable")||window.csdn.userLogin.loadAjax(t.clickAddBtnHandler.bind(t))}),this.cancelBtn.on("click",function(n){t.hideInputBox().hideMsg()}),this.closeBtn.on("click",t.clickCloseBtnHandler.bind(t)),this.box.find(".switch-component label").on("click",function(){var t=$(this).attr("for"),n=$("#"+t).prop("checked");$("#"+t).prop("checked",!n)});var n=function(){var n=$(this),e=n.val(),o=n.parent().find("span.num-limit"),i=+n.attr("maxlength");"title"===n.attr("name")&&(e?t.addBtn.removeClass("disable"):t.addBtn.addClass("disable")),o.text(i-e.length>0?i-e.length:0)};return this.inputTitle.on("input",n),this.inputDesc.on("input",n),this},n.prototype.getThemeClass=function(){var t="csdn-collection-theme-default";return("black"===this.options.theme||window.csdn&&window.csdn.toolbarIsBlack)&&(t="csdn-collection-theme-dark"),t},n.prototype.trim=function(t){return t.replace(/^\s*/g,"").replace(/\s*$/g,"")},n.prototype.getList=function(t){var n=this,e=n.options.url;return $.ajax({type:"get",url:"https://mp-action.csdn.net/interact/wrapper/pc/favorite/v1/api/folderListWithCheck?url="+e,crossDomain:!0,xhrFields:{withCredentials:!0},success:function(e){if(200===e.code){var o=n.sortListByTime(e.data.result);t&&t.call(n,o||[])}else n.showMsg(reponse.msg)},error:function(t){function n(n){return t.apply(this,arguments)}return n.toString=function(){return t.toString()},n}(function(t){if(t&&t.responseText){var e=JSON.parse(t.responseText).message;e&&n.showMsg(e)}void 0})}),this},n.prototype.renderCreateFolder=function(){return'<div>\n      <div class="input-wrap">\n        <input class="text" name="title" type="text" placeholder="收藏夹标题" maxlength="15" />\n        <span class="num-limit">15</span>\n      </div>\n      <div class="textarea-wrap">\n        <textarea class="text" name="desc" rows="3" placeholder="收藏夹描述（可选）" maxlength="200"></textarea> \n        <span class="num-limit">200</span>\n      </div>\n\n      <div class="switch-wrap open-folder-switch">\n        <div class="switch-wrap_left">\n          <span>私密</span>\n          <i class="question">\n            <div class="tip">公开的收藏夹可被其他人关注,当该收藏夹被关注后,无法修改为私密</div>\n          </i>\n        </div>\n        <div class="switch-wrap_right">\n          <span></span>\n          <div class="switch-component">\n            <input type="checkbox" id="privateSwitch" class="switch">\n            <label for="privateSwitch"></label>\n          </div> \n        </div>\n      </div>\n\n      <div class="switch-wrap default-folder-switch">\n        <div class="switch-wrap_left">\n          <span>默认</span>\n          <i class="question">\n            <div class="tip">所有收藏操作会默认收藏至该收藏夹,且同时仅存在一个默认收藏夹</div>\n          </i>\n        </div>\n        <div class="switch-wrap_right">\n          <span></span>\n          <div class="switch-component">\n            <input type="checkbox" id="defaultSwitch" class="switch">\n            <label for="defaultSwitch"></label>\n          </div> \n        </div>\n      </div>\n\n      <div class="collect-box-btn-groups">\n        <button class="cancel">返回</button>\n        <button class="submit disable">确认创建</button>\n      </div>\n    </div>'},n.prototype.renderList=function(t){function n(t){return t?t.replace(/</g,"&lt;").replace(/>/g,"&gt;"):""}this.collectedFolderIds=t.filter(function(t){return t.CheckStatus}).map(function(t){return t.ID});var e=this.getNearItemFolderId(),o=""+t.map(function(t){return'\n        <li class="csdn-collection-item '+(t.CheckStatus?"collected":"")+'" '+(t.CheckStatus?"data-check-id="+t.CheckStatus:"")+"  data-folder-id="+t.ID+'>\n          <div class="csdn-collection-item_left">\n            <div class="csdn-collection-item_title">\n              <span class="title-m">'+n(t.Name)+"</span>\n              "+(t.isDefault?'<span class="def">默认</span>':"")+"\n              "+(e===t.ID?'<span class="near">最近使用</span>':"")+'\n            </div>\n            <span class="csdn-collection-item_ext">\n              <span>'+t.FavoriteNum+'条内容</span>\n              <span class="dot">・</span>\n              <span>'+(t.IsPrivate?"私密":"公开")+'</span>\n            </span>\n          </div>\n          <span class="collect-btn">'+(t.CheckStatus?"已收藏":"收藏")+"\n        </span></li>"}).join("");return this.list.html(o),this},n.prototype.setDafaultItem=function(){var t=this.list.find(".select");0===t.length&&this.list.find("li").first().click()||(this.curFolderId=1*t.attr("data-folder-id"),this.curCheckId=1*t.attr("data-check-id")),void 0},n.prototype.renderItem=function(t){var n='<li class="csdn-collection-item" data-folder-id='+t.id+'>\n      <div class="csdn-collection-item_left">\n        <div class="csdn-collection-item_title">\n          <span class="title-m">'+function(t){return t.replace(/</g,"&lt;").replace(/>/g,"&gt;")}(t.name)+'</span>\n        </div>\n        <span class="csdn-collection-item_ext">\n          <span>'+t.favoriteNum+'条内容</span>\n          <span class="dot">・</span>\n          <span>'+(t.isPrivate?"私密":"公开")+'</span>\n        </span>\n      </div>\n      <span class="collect-btn">收藏</span>\n    </li>',e=this.list.find("li:first");return $(n).insertAfter(e),this.list.scrollTop(0),this},n.prototype.getNearItemFolderId=function(){return+localStorage.getItem("csdn_collection_"+this.userName)},n.prototype.setNearItemFolderId=function(t){return localStorage.setItem("csdn_collection_"+this.userName,t),this},n.prototype.setTip=function(){return localStorage.setItem("csdn_collection_tip",(new Date).getTime()),this},n.prototype.getTip=function(){return localStorage.getItem("csdn_collection_tip")},n.prototype.showInputBox=function(){return this.toggleBtn.hide(),this.list.hide(),this.inputBox.show(),this.inputTitle.focus(),this},n.prototype.hideInputBox=function(){return this.inputBox.hide(),this.inputTitle.val(""),this.toggleBtn.show(),this.list.show(),this.privateSwitch&&this.privateSwitch.prop("checked",!1),this.defaultSwitch&&this.defaultSwitch.prop("checked",!1),this},n.prototype.showMsg=function(t){var n=this;return t&&this.msg.html(t).show(),setTimeout(function(){n.hideMsg()},1500),this},n.prototype.hideMsg=function(t){return this.msg.hide().html(),this},n.prototype.checkName=function(t){var n=!0,e="";return n=!!(t&&t.length<=15&&t.length>=2),""===t?e="收藏夹标题不能为空":t&&t.length>15?e="收藏夹标题不能超过15个字":t&&t.length<2&&(e="收藏夹标题在2到15个字之间"),void 0,e&&this.showMsg(e)||this.hideMsg(),n},n.prototype.checkCollectionStatus=function(t){var n=this,e=this.options.url;$.ajax({type:"get",url:"https://mp-action.csdn.net/interact/wrapper/pc/favorite/v1/api/checkFavoriteByUrl?url="+e,crossDomain:!0,xhrFields:{withCredentials:!0},success:function(e){if(200===e.code){var o=!1,i=e.data;for(var s in i)i.hasOwnProperty(s)&&i[s]>0&&(o=!0);n.options.collectionCallBack&&n.options.collectionCallBack(o),t&&t()}else n.showMsg(e.msg)},error:function(t){void 0}})},n.prototype.clickItemHandler=function(t){var n=this,e=1*t.attr("data-folder-id"),o=1*(t.attr("data-check-id")||0);n.needCollectId=e,n.unCollectIds=0===o?[]:[e],void 0,window.csdn.userLogin.loadAjax(function(){n.unCollectIds.length>0&&n.deleteCollectionItems(),!n.unCollectIds.length>0&&n.needCollectId&&n.createCollectionItems()})},n.prototype.clickToggleBtnHandler=function(t){var n=this;t.stopPropagation(),n.showInputBox()},n.prototype.clickAddBtnHandler=function(t){var n=this,e=n.trim(n.inputTitle.val()),o={name:e,source:n.options.source,description:n.inputDesc.val()||"",isPrivate:+n.privateSwitch.prop("checked"),isDefault:+n.defaultSwitch.prop("checked"),username:n.userName};n.checkName(e)&&$.ajax({type:"post",url:"https://mp-action.csdn.net/interact/wrapper/pc/favorite/v1/api/createFolder",data:JSON.stringify(o),dataType:"json",contentType:"application/json",crossDomain:!0,xhrFields:{withCredentials:!0},success:function(t){200===t.code?n.getList(n.renderList).hideInputBox().hideMsg():n.showMsg(t.msg)},error:function(t){if(t&&t.responseText){var e=JSON.parse(t.responseText).message;e&&n.showMsg(e)}void 0}})},n.prototype.keyupInputHandler=function(t){var n=t.keyCode;27===n&&this.hideInputBox().hideMsg(),13===n&&this.addBtn.click()},n.prototype.clickCloseBtnHandler=function(){this.box.remove()},n.prototype.clickSubmitBtnHandler=function(){var t=this;window.csdn.userLogin.loadAjax(function(){void 0,t.unCollectIds.length>0&&t.deleteCollectionItems(),t.needCollectId&&t.createCollectionItems()}),t.clickCloseBtnHandler()},n.prototype.changeCollectionItems=function(t,n){var e=this;return $.ajax({type:"post",url:"https://mp-action.csdn.net/interact/wrapper/pc/favorite/v1/api/addFavoriteInFolds",contentType:"application/json",data:JSON.stringify(t),crossDomain:!0,xhrFields:{withCredentials:!0},success:function(t){200===t.code?n&&n(t):e.showMsg(t.msg)},error:function(t){if(t&&t.responseText){var n=JSON.parse(t.responseText).message;n&&e.showMsg(n)}void 0}}),this},n.prototype.createCollectionItems=function(){var t=this,n=Object.assign({},this.data);if(0!==t.needCollectId)return n.folderIdList=this.collectedFolderIds.concat(t.needCollectId),this.changeCollectionItems(n,function(n){var e=t.needCollectId?t.needCollectId:n.data.folderId;t.clickCloseBtnHandler(),t.setNearItemFolderId(e).checkCollectionStatus()}),this},n.prototype.deleteCollectionItems=function(){var t=this,n=t.unCollectIds,e=Object.assign({},this.data);if(n.length)return e.folderIdList=this.collectedFolderIds.filter(function(t,e){return-1===n.indexOf(t)}),this.changeCollectionItems(e,function(){t.unCollectIds=[],void 0,t.checkCollectionStatus(),t.clickCloseBtnHandler()}),this},window.csdn=window.csdn||{},window.csdn.collectionBox=window.csdn.collectionBox||{},window.csdn.collectionBox.show=function(t){window.csdn.userLogin.loadAjax(function(){window.csdn.collectionBox.box=new n(t)})},window.csdn.collectionBox.collect=function(n,e){window.csdn.userLogin.loadAjax(function(){var o={url:n.url,source:n.source,sourceId:Number(n.source_id,10),author:n.author,title:n.title,description:n.description||"",fromType:"PC",username:t("UserName")};$.ajax({type:"post",url:"https://mp-action.csdn.net/interact/wrapper/pc/favorite/v1/api/addFavorite",contentType:"application/json",data:JSON.stringify(o),crossDomain:!0,xhrFields:{withCredentials:!0},success:function(t){e&&e(t)},error:function(t){e&&e(t)}})})},window.csdn.collectionBox.getCollectInfo=function(n,e){window.csdn.userLogin.loadAjax(function(){var o={url:n.url||"",username:t("UserName"),source:n.source||"",sourceId:n.source_id||n.sourceId||"",author:n.author||""};if(!o.sourceId)return void e(null);$.ajax({type:"get",url:"https://mp-action.csdn.net/interact/wrapper/pc/favorite/v1/api/getFavoriteInfo",crossDomain:!0,xhrFields:{withCredentials:!0},data:o,success:function(t){200===t.code?e&&e(t.data):e&&e(null)},error:function(t){e&&e(null)}})})},window.csdn.collectionBox.close=function(){window.csdn.collectionBox.box&&window.csdn.collectionBox.box.clickCloseBtnHandler()},$(document).on("click","[data-bind-collection=true]",function(t){try{var n=window.csdn.collectionBox.params;n&&window.csdn.collectionBox.show(n)}catch(t){void 0}})}();