function _newArrowCheck(E,e){if(E!==e)throw new TypeError("Cannot instantiate an arrow function")}var _this=this;(function(){var E=this;_newArrowCheck(this,_this);var e=function(){"use strict";var e=this;return _newArrowCheck(this,E),function(E){return _newArrowCheck(this,e),{name:"COBOL",aliases:["standard-cobol","cobol"],case_insensitive:!0,keywords:{$pattern:/[a-zA-Z]+(?:-[a-zA-Z0-9]+)*/,keyword:["ACCEPT","ACCESS","ACTIVE-CLASS","ADD","ADDRESS","ADVANCING","AFTER","ALIGNED","ALLOCATE","ALPHABET","ALPHABETIC","ALPHABETIC-LOWER","ALPHABETIC-UPPER","ALPHANUMERIC","ALPHANUMERIC-EDITED","ALSO","ALTERNATE","AND","ANY","ANYCASE","ARE","AREA","AREAS","AS","ASCENDING","ASSIGN","AT","B-AND","B-NOT","B-OR","B-SHIFT","B-SHIFT-LC","B-SHIFT-RC","BY","B-XOR","BASED","BEFORE","BINARY","BINARY-CHAR","BINARY-DOUBLE","BINARY-LONG","BINARY-SHORT","BIT","BLANK","BLOCK","BOOLEAN","BOTTOM","CALL","CANCEL","CF","CH","CHARACTER","CHARACTERS","CLASS","CLASS-ID","CLOSE","CODE","CODE-SET","COL","COLLATING","COLS","COLUMN","COLUMNS","COMMA","COMMIT","COMMON","COMP","COMPUTATIONAL","COMPUTE","CONFIGURATION","CONSTANT","CONTAINS","CONTENT","CONTINUE","CONTROL","CONTROLS","CONVERTING","COPY","CORR","CORRESPONDING","COUNT","CRT","CURRENCY","CURSOR","DATA","DATA-POINTER","DATE","DAY","DAY-OF-WEEK","DE","DECIMAL-POINT","DECLARATIVES","DEFAULT","DELETE","DELIMITED","DELIMITER","DEPENDING","DESCENDING","DESTINATION","DETAIL","DISPLAY","DIVIDE","DIVISION","DOWN","DUPLICATES","DYNAMIC","EC","EDITING","ELSE","EMI","END","END-ACCEPT","END-ADD","END-CALL","END-COMPUTE","END-DELETE","END-DISPLAY","END-DIVIDE","END-EVALUATE","END-IF","END-MULTIPLY","END-OF-PAGE","END-PERFORM","END-RECEIVE","END-READ","END-RETURN","END-REWRITE","END-SEARCH","END-START","END-STRING","END-SUBTRACT","END-UNSTRING","END-WRITE","ENVIRONMENT","EOL","EOP","EQUAL","ERROR","EVALUATE","EXCEPTION","EXCEPTION-OBJECT","EXCLUSIVE-OR","EXIT","EXTEND","EXTERNAL","FACTORY","FARTHEST-FROM-ZERO","FALSE","FD","FILE","FILE-CONTROL","FILLER","FINAL","FINALLY","FIRST","FLOAT-BINARY-32","FLOAT-BINARY-64","FLOAT-BINARY-128","FLOAT-DECIMAL-16","FLOAT-DECIMAL-34","FLOAT-EXTENDED","FLOAT-INFINITY","FLOAT-LONG","FLOAT-NOT-A-NUMBER","FLOAT-NOT-A-NUMBER-QUIET","FLOAT-NOT-A-NUMBER-SIGNALING","FOOTING","FOR","FORMAT","FREE","FROM","FUNCTION","FUNCTION-ID","FUNCTION-POINTER","GENERATE","GET","GIVING","GLOBAL","GO","GOBACK","GREATER","GROUP","GROUP-USAGE","HEADING","I-O","I-O-CONTROL","IDENTIFICATION","IF","IN","IN-ARITHMETIC-RANGE","INDEX","INDEXED","INDICATE","INHERITS","INITIAL","INITIALIZE","INITIATE","INPUT","INPUT-OUTPUT","INSPECT","INTERFACE","INTERFACE-ID","INTO","INVALID","INVOKE","IS","JUST","JUSTIFIED","KEY","LAST","LEADING","LEFT","LENGTH","LESS","LIMIT","LIMITS","LINAGE","LINAGE-COUNTER","LINE","LINE-COUNTER","LINES","LINKAGE","LOCAL-STORAGE","LOCALE","LOCATION","LOCK","MERGE","MESSAGE-TAG","METHOD","METHOD-ID","MINUS","MODE","MOVE","MULTIPLY","NATIONAL","NATIONAL-EDITED","NATIVE","NEAREST-TO-ZERO","NESTED","NEXT","NO","NOT","NULL","NUMBER","NUMERIC","NUMERIC-EDITED","OBJECT","OBJECT-COMPUTER","OBJECT-REFERENCE","OCCURS","OF","OFF","OMITTED","ON","OPEN","OPTIONAL","OPTIONS","OR","ORDER","ORGANIZATION","OTHER","OUTPUT","OVERFLOW","OVERRIDE","PACKED-DECIMAL","PAGE","PAGE-COUNTER","PERFORM","PF","PH","PIC","PICTURE","PLUS","POINTER","POSITIVE","PRESENT","PRINTING","PROCEDURE","PROGRAM","PROGRAM-ID","PROGRAM-POINTER","PROPERTY","PROTOTYPE","RAISE","RAISING","RANDOM","RD","READ","RECEIVE","RECORD","RECORDS","REDEFINES","REEL","REF","REFERENCE","RELATIVE","RELEASE","REMAINDER","REMOVAL","RENAMES","REPLACE","REPLACING","REPORT","REPORTING","REPORTS","REPOSITORY","RESERVE","RESET","RESUME","RETRY","RETURN","RETURNING","REWIND","REWRITE","RF","RH","RIGHT","ROLLBACK","ROUNDED","RUN","SAME","SCREEN","SD","SEARCH","SECTION","SELECT","SEND","SELF","SENTENCE","SEPARATE","SEQUENCE","SEQUENTIAL","SET","SHARING","SIGN","SIZE","SORT","SORT-MERGE","SOURCE","SOURCE-COMPUTER","SOURCES","SPECIAL-NAMES","STANDARD","STANDARD-1","STANDARD-2","START","STATUS","STOP","STRING","SUBTRACT","SUM","SUPER","SUPPRESS","SYMBOLIC","SYNC","SYNCHRONIZED","SYSTEM-DEFAULT","TABLE","TALLYING","TERMINATE","TEST","THAN","THEN","THROUGH","THRU","TIME","TIMES","TO","TOP","TRAILING","TRUE","TYPE","TYPEDEF","UNIT","UNIVERSAL","UNLOCK","UNSTRING","UNTIL","UP","UPON","USAGE","USE","USE","USER-DEFAULT","USING","VAL-STATUS","VALID","VALIDATE","VALIDATE-STATUS","VALUE","VALUES","VARYING","WHEN","WITH","WORKING-STORAGE","WRITE","XOR"],literal:["ZERO","ZEROES","ZEROS","SPACE","SPACES","HIGH-VALUE","HIGH-VALUES","LOW-VALUE","LOW-VALUES","QUOTE","QUOTES","ALL"]},contains:[{scope:"comment",begin:/(^[ 0-9a-zA-Z]{1,6}[*])/,end:/\n/},{scope:"doctag",begin:/>>/,end:/\n/},{scope:"type",begin:/(9|S9|V9|X|A)+(\([0-9]*\))+/},{scope:"operator",begin:/(\+| - |\*\*|\*|\/|<>|>=|<=|>|<|=|&|::)/},{scope:"number",begin:/([0-9]+(?:(\.|,)[0-9]+)*)/},{scope:"string",begin:'"',end:'"'},{scope:"string",begin:"'",end:"'"}]}}.bind(this)}.bind(this)();hljs.registerLanguage("cobol",e)}).bind(this)(),function(E,e){"use strict";function n(){var E=e.createElement("style");E.type="text/css",E.innerHTML=L(".{0}{border-collapse:collapse}            .{0} td{padding:0}            .{1}{text-align: right;padding-right: 8px;}            .{1}:before{content:attr({2})}",[S,c,d]),e.getElementsByTagName("head")[0].appendChild(E)}function T(n){"complete"===e.readyState?N(n):E.addEventListener("DOMContentLoaded",function(){N(n)})}function N(n){try{var T=e.querySelectorAll("code.hljs");for(var N in T)T.hasOwnProperty(N)&&A(T[N],n)}catch(I){E.console.error("LineNumbers error: ",I)}}function A(E,e){if("object"==typeof E){e=e||{singleLine:!1};var n=e.singleLine?0:1;r(function(){R(E),E.innerHTML=I(E.innerHTML,n),Array.apply(null,E.childNodes).forEach(s)})}}function I(E,e){var n=O(E);if(""===n[n.length-1].trim()&&n.pop(),n.length>e){for(var T="",N=0,A=n.length;N<A;N++)T+=L('<li><div class="{0}"><div class="{1} {2}" {3}="{5}"></div></div><div class="{4}"><div class="{1}">{6}</div></div></li>',[l,a,c,d,D,N+1,n[N].length>0?n[N]:" "]);return L('<ol class="{0}">{1}</ol>',[S,T])}return E}function R(E){var e=E.childNodes;for(var n in e)if(e.hasOwnProperty(n)){var T=e[n];i(T.textContent)>0&&(T.childNodes.length>0?R(T):t(T.parentNode))}}function t(E){var e=E.className;if(/hljs-/.test(e)){for(var n=O(E.innerHTML),T=0,N="";T<n.length;T++)N+=L('<span class="{0}">{1}</span>\n',[e,n[T]]);E.innerHTML=N.trim()}}function O(E){return 0===E.length?[]:E.split(h)}function i(E){return(E.trim().match(h)||[]).length}function r(e){E.setTimeout(e,0)}function L(E,e){return E.replace(/\{(\d+)\}/g,function(E,n){return e[n]?e[n]:""})}function s(E,e,n){var T,N={id:0,length:0},A=Array.apply(null,E.childNodes),I={hundred:100,thousand:1e3};A.forEach(function(E,e,n){if(1==E.nodeType&&E.getElementsByClassName("hljs-ln-code").length){var A=E.getElementsByClassName("hljs-ln-code")[0].offsetWidth;switch(e){case I.hundred:T="hundred";break;case I.thousand:T="thousand"}A>N.length&&(N.length=A,N.id=e)}});var R=E.parentNode.offsetWidth,t=A[N.id];if(void 0!=t&&1==t.nodeType&&(void 0!=T&&(E.className=E.className+" "+T),t.getElementsByClassName("hljs-ln-numbers").length)){var O=t.getElementsByClassName("hljs-ln-numbers")[0].offsetWidth,i=t.getElementsByClassName("hljs-ln-code")[0].offsetWidth;R<O+i+U?E.setAttribute("style","width:"+(O+i+U)+"px"):o()||C()||E.setAttribute("style","width:100%")}}function o(){return!!window.navigator.userAgent.toLowerCase().match(/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone)/i)}function C(){return!!window.navigator.userAgent.toLowerCase().match(/(csdn)/i)}var S="hljs-ln",a="hljs-ln-line",D="hljs-ln-code",l="hljs-ln-numbers",c="hljs-ln-n",d="data-line-number",h=/\r\n|\r|\n/g,U=50;E.hljs?(E.hljs.initLineNumbersOnLoad=T,E.hljs.lineNumbersBlock=A,E.hljs.getLines=O,n()):E.console.error("highlight.js not detected!")}(window,document),$(function(){function E(){hljs.initHighlighting(),"ie"!==A&&hljs.initCopyButtonOnLoad(),hljs.initLineNumbersOnLoad(),$("pre .language-plain").length>0&&$("pre .language-plain").each(function(E,e){var n=hljs.highlightAuto(N(e.innerHTML));e.innerHTML=n.value,e.className="language-"+n.language})}var e=/&(lt|gt|amp|quot|nbsp|shy|#\d{1,5});/g,n={lt:"<",gt:">",amp:"&",quot:'"',nbsp:" ",shy:"­"},T=function(E,e){return n[e]},N=function(E){return E.replace(e,T)},A=function(){var E=window.navigator.userAgent,e=function(e){return E.indexOf(e)>=0},n=function(){return"ActiveXObject"in window}();return e("MSIE")||n?"ie":e("Firefox")&&!n?"Firefox":e("Chrome")&&!n?E.indexOf("Edge")>-1?"Edge":"Chrome":e("Opera")&&!n?"Opera":e("Safari")&&!n?"Safari":void 0}();$("#content_views").hasClass("htmledit_views")&&($("#content_views pre").find("code").addClass("hljs"),E())});