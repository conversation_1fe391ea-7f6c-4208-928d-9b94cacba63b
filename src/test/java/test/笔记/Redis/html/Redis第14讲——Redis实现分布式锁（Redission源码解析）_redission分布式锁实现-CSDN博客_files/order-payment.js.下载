"use strict";function _classCallCheck(e,o){if(!(e instanceof o))throw new TypeError("Cannot call a class as a function")}var _createClass=function(){function e(e,o){for(var t=0;t<o.length;t++){var n=o[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(o,t,n){return t&&e(o.prototype,t),n&&e(o,n),o}}();!function(e,o,t){function n(e,o){var n;t(o).hide(),e.addEventListener("mouseover",function(){t(o).show(),setTimeout(function(){o.classList.add("show")},0),clearTimeout(n)}),e.addEventListener("mouseout",function(){n=setTimeout(function(){o.classList.remove("show"),setTimeout(function(){t(o).hide()},600)},1e3)}),o.addEventListener("mouseover",function(){clearTimeout(n)}),o.addEventListener("mouseout",function(){n=setTimeout(function(){o.classList.remove("show"),t(o).hide()},1e3)})}function s(e){if(e&&S[e])return e;var o=window.location.host;L=-1!==o.indexOf("blog.csdn.net")?"blog":-1!==o.indexOf("download.csdn.net")?"download":"default"}function i(){for(var e={},o=0;o<arguments.length;o++)t.extend(!0,e,arguments[o]);return e}function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1500,n=o.createElement("div");n.innerHTML=e,n.style.cssText="min-width:124px;padding:0 8px;opacity: 0.8;height: 40px;background:rgba(34,34,38,1);color: rgb(255, 255, 255);line-height: 40px;text-align: center;border-radius: 4px;position: fixed;top: 35%;left:50%;transform: translateX(-50%);z-index: 999999;font-size: 16px;",o.getElementById("order-payment").appendChild(n),setTimeout(function(){n.style.webkitTransition="-webkit-transform 0.5s ease-in, opacity 0.5s ease-in",n.style.opacity="0",setTimeout(function(){o.getElementById("order-payment")&&o.getElementById("order-payment").removeChild(n)},500)},t)}function r(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1500,o='\n      <div class="loading_warp" id="order-payment-loading">\n        <div class="icon_box">\n          <img class="rotating" src="'+k+'/icon-paying.png"/>\n        </div>\n        <div class="pay_msg">查询中...</div>\n      </div>\n    ';t(".orderpayment_dialog").append(o).find(".orderpayment_content").addClass("noScroll"),setTimeout(function(){t("#order-payment-loading").remove(),t(".orderpayment_dialog .orderpayment_content").removeClass("noScroll")},e)}function c(){var e=d("api_env")||"",o="https://mall.csdn.net/",t=/^beta|test|loc[a-z]*/;return e.match(t)?o="https://test-mall.csdn.net/":e.match(/^pre-|pre[a-z]*/)&&(o="https://pre-mall.csdn.net/"),o}function d(e){var o=new RegExp("(^|&)"+e+"=([^&]*)(&|$)","i"),t=window.location.search.substr(1).match(o);return null!=t?unescape(t[2]):""}function l(e,n){if(QRCode){if(n.pay_url){if(O.priceInfo=n,t("#ordertip_qr_code").html(""),t("#ordertip_notify").hide(),t("#pay_btn").attr("href",n.pay_url),new QRCode(o.getElementById("ordertip_qr_code"),{text:n.pay_url,width:132,height:132}),G||(G=new P({el:"#payingCountdown",leftTime:3e5})),0==I.price)return void G.stopCountdown();G.startCountdown()}}else void 0}function p(){g("pay_error","获取失败,点击重试","code_2")}function h(){g("pay_time_out","点击重新获取","")}function u(){g("pay_error","已扫码<br>请在手机端操作","")}function m(e){O.show_config.needLoading&&r(),setTimeout(function(){O.checkBalacneGoods?(a("余额充值成功，使用余额支付完成购买吧","1500"),t(".orderpayment_c_l_goodsitem").eq(0).trigger("click")):(a("支付成功","1000"),setTimeout(function(){C?(O.close(),C(e)):"reload"===O.show_config.successProcess?window.location.reload():"jump"===O.show_config.successProcess&&(window.location.href=_(e))},1e3))},O.show_config.needLoading?1500:0)}function _(e){return"success"===e.errorMessage&&e.jumpUrl&&1===e.status?e.jumpUrl:!e.need_third_pay&&e.paySuccessUrl?e.paySuccessUrl:"https://mall.csdn.net/myorder"}function g(e,o,n){t("#ordertip_notify").show().html('<img class="pay_icon" src="https://csdnimg.cn/release/download/images/'+e+'.png"/><span class="pay_tip">'+o+"</span>"),t("#ordertip_qr_code").html('<img src="https://csdnimg.cn/public/static/img/csdn-userimg250.gif" width="145" height="145"/>'),"pay_time_out"==e?t("#ordertip_notify .pay_icon,#ordertip_notify .pay_tip").on("click",function(){O.getPayCode()}):t("#ordertip_notify .pay_icon,#ordertip_notify .pay_tip").off("click")}function y(){this.checkBalacneGoods=!1,this.goodsInfo={},this.goodsList=[],this.balanceList={},this.balanceGoodsInfo={},this.activeGoodsId="",this.activeGoodsActivity="",this.voucherKeysList=[],this.voucherKeysObj={},this.temporaryVoucherKeysList=[],this.temporaryVoucherKeysObj={},this.isUseBalance=!0,this.priceInfo={},this.errType="",this.reportExt={},this.navList=[],this.payMethods=[],this.price=0,this.payUrl="",this.params={},this._cart=null,this.show_params={},this.show_in_goodsobj={},this.show_config={showHeads:!0,showBalance:!0,showGoods:!0,needLoading:!0,successProcess:"reload"},this.transX=0,this.listBoxWidth=626,this.listScrollWidth=0,this.goodsTabWidth=136,I=this}function v(e){return new Promise(function(o,n){t.ajax({url:c()+"mp/mallorder/ab/getExperiment",type:"GET",data:e,xhrFields:{withCredentials:!0},success:function(e){o(200==e.code?e.data.treatment||"control":"control")},error:function(e){void 0,o("control")}})})}function f(e){void 0,""==j?v({testId:342}).then(function(o){j=o,b(e)}):b(e)}function b(e){T=(new Date).getTime(),O=new y,O.show(e),window._orderPayment=O}function w(){O.close()}var x="https://g.csdnimg.cn/order-payment/",k=x+"4.0.5/images",I=null,C=null,L="default",T="",V=!0,S={blog:"直接购买<br />无限次学习",download:"直接购买<br />立即下载资料",default:"放弃优惠<br />直接购买"},G=null,j="",B=function(e){var t=o.cookie;return t&&function(){var o,n={};t=t.split("; ");for(var s=0,i=t.length;s<i&&(o=t[s].split("="),!(o.length>0&&(o[0]===e&&(n.key=o[0],n.value=o[1],n.status=!0),"key"in n)));s++);return"key"in n&&n}()};!function(e){var t=o.createElement("link");t.rel="stylesheet",t.type="text/css",t.href=e,o.getElementsByTagName("head")[0].appendChild(t)}("https://g.csdnimg.cn/order-payment/4.0.5/order-payment.css"),y.prototype={constructor:y,close:function(){this._cart.clearTimer(),t("#order-payment-box").remove()},show:function(){var e=this,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return window.csdn.cart?window.csdn.cartClass?(s(o.type),this.show_params=o,this.goodsExtraInfo=o.goodsExtraInfo||{},this.show_config=Object.assign(this.show_config,o.config||{}),"function"==typeof o.get_pay_success_callback&&(C=o.get_pay_success_callback),o.params.forEach(function(o){e.show_in_goodsobj[o.goodsId]=o}),this.sale=o.sale_source||d("sale_source"),this.reportExt=o.report_ext||{},this.init(o.params||{}),void this.reportDialogView()):void void 0:void void 0},reportDialogView:function(){try{window.csdn.report&&window.csdn.report.reportView({spm:"3001.10044"})}catch(e){void 0}},init:function(e){this._cart=new window.csdn.cartClass,this.getGoodsShowListByIds(e,function(e){I.getGoodsInfo(e,function(o){I.goodsInfo=i(e,o),I.goodsCodeData(o.new_price,o.available_amount),I.show_config.showBalance&&I.show_config.showGoods?I.getGoodsShowList():(I.activeGoodsId=I.goodsInfo.goods_id,I.checkBalacneGoods=!1,I.initDialog(),I.getPayCode())},!1)})},bindEvents:function(){t(o).off("click",".orderpayment_dialog .user_balance").on("click",".orderpayment_dialog .user_balance",function(){if(t(".orderpayment_dialog .user_balance").hasClass("disable"))return void(I.isUseBalance=!1);t(".orderpayment_dialog .user_balance").hasClass("active")?(t(".orderpayment_dialog .user_balance").removeClass("active"),I.isUseBalance=!1):(t(".orderpayment_dialog .user_balance").addClass("active"),I.isUseBalance=!0),I.goodsCodeData(I.goodsInfo.new_price,I.goodsInfo.available_amount,I.countVoucher()),I.changePriceHtml(I.price),I.getPayCode(I.goodsInfo)}),t(o).off("click","#pay_btn").on("click","#pay_btn",function(e){var o=B("UserName").value;if(V){V=!1;var n=i(I.show_in_goodsobj[I.goodsInfo.goods_id],{product_id:I.goodsInfo.product_id,goods_id:I.goodsInfo.goods_id,flag:I.goodsInfo.flag,goodsSource:I.goodsInfo.goodsSource||"",is_use_balance:2,sale_source:I.sale,request_id:o+"_"+T+"_"+I.goodsInfo.product_id+"_"+I.goodsInfo.goods_id+"_"+I.goodsInfo.flag});return void I._cart.quickBuy({params:n,get_pay_success_callback:function(e){V=!0,200==e.code&&!1===e.data.need_third_pay&&m(e.data)},error_function:function(e){V=!0,400103012==e.status&&t(".orderpayment_item.active").trigger("click"),void 0}})}}),t(o).off("click",".order-payment #useVoucherBtn").on("click",".order-payment #useVoucherBtn",function(e){I.temporaryVoucherKeysObj=JSON.parse(JSON.stringify(I.voucherKeysObj)),I.temporaryVoucherKeysList=JSON.parse(JSON.stringify(I.voucherKeysList)),t(".voucher_warp .v_w_desc").html(I.changeVoucherPriceHtml(!0)),t(".order-payment .voucher_warp").removeClass("none")}),t(o).off("click",".order-payment .voucher_card").on("click",".order-payment .voucher_card",function(e){var o=t(this).attr("data-key")||"";I.temporaryVoucherKeysObj[o]=!t(this).hasClass("checked"),I.temporaryVoucherKeysList=[],t(this).toggleClass("checked");for(var n in I.temporaryVoucherKeysObj)I.temporaryVoucherKeysObj[n]&&I.temporaryVoucherKeysList.push(n);t(".voucher_warp .v_w_desc").html(I.changeVoucherPriceHtml(!0))}),t(o).off("click",".order-payment #sureUseVoucher").on("click",".order-payment #sureUseVoucher",function(e){I.voucherKeysObj=JSON.parse(JSON.stringify(I.temporaryVoucherKeysObj)),I.voucherKeysList=JSON.parse(JSON.stringify(I.temporaryVoucherKeysList)),I.goodsInfo.cash_coupon_keys=I.voucherKeysList.join(","),t("#useVoucherBtn .voucher-title").html(I.changeVoucherPriceHtml()),I.goodsCodeData(I.goodsInfo.new_price,I.goodsInfo.available_amount,I.countVoucher()),I.changePriceHtml(I.price),I.getPayCode(I.goodsInfo),t(".order-payment .voucher_warp").addClass("none")}),t(o).on("click",".orderpayment_header_btn",function(e){I.close()}),t(o).off("click",".order-payment .list_btn.btn_left").on("click",".order-payment .list_btn.btn_left",function(e){I.moveTab("left")}),t(o).off("click",".order-payment .list_btn.btn_right").on("click",".order-payment .list_btn.btn_right",function(e){I.moveTab("right")}),t(o).off("click",".orderpayment_item").on("click",".orderpayment_item",function(e){var o=JSON.parse(t(this).attr("data-goods")||"{}");if(o="balance"===o.type?i(o,I.balanceList[o.index].ext,I.show_in_goodsobj[I.balanceList[o.index].ext.goodsId]):i(o,I.goodsList[o.index].ext,I.show_in_goodsobj[I.goodsList[o.index].ext.goodsId]),I.activeGoodsActivity=o.activityContent,t(this).has("active"))return t(this).addClass("active").siblings().removeClass("active"),void I.getGoodsInfo(o,function(e){"balance"===o.type?(I.checkBalacneGoods=!0,I.balanceGoodsInfo=e):(I.goodsInfo=e,I.checkBalacneGoods=!1,e.available_amount>0&&(I.isUseBalance=!0)),I.activeGoodsId=e.goods_id,I.goodsInfo.goodsType=o.type,I.goodsCodeData(e.new_price,e.available_amount),t(".orderpayment_dialog .orderpayment_c_goodsinfo").html(I.setGoodsInfos()),t(".orderpayment_dialog .commodity_box").html("").append(I.setPayPriceHtml()),I.resetVoucherHtml(),t(".orderpayment_dialog .scan_code").html("").append(I.setPaylistHtml())})})},initDialog:function(){this.bindEvents(),this.renderDialog(),this.autoFind_activityDesc2_Sku_showTips()},setGoodsInfos:function(){var e=this.checkBalacneGoods?this.balanceGoodsInfo:this.goodsInfo,o=this.goodsExtraInfo,t=o.columnArticlesNum,n=void 0===t?0:t,s=o.columnSubscribeNum,i=void 0===s?0:s,a=o.hotSellingRank,r=void 0===a?0:a,c=o.viewCount,d=void 0===c?0:c,l=o.nickName,p=void 0===l?"":l,h=o.level,u=void 0===h?0:h,m="",_="",g="";return _="<span class="+(r>0&&r<=100?"orderpayment_c_goods_t_rank_tips":"none")+">Top热销榜第"+r+"名</span>","exp"==j?(g+="<span>作者："+p+'</span><img class="'+(u>0?"":"none")+'" src="https://csdnimg.cn/identity/blog'+u+'.png" style="width:22px;height:22px;" />',m+='<span style="margin-left:16px;">',n>0&&(m+='该专栏共<span class="num-color"> '+n+" </span>篇文章"),Number(d)>0&&(m+='，已有<span class="num-color"> '+Number(d).toLocaleString()+" </span>人学习"),m+="</span>"):(m+='<span">',n>0&&(m+="该专栏文章总数<span> "+n+" </span>篇"),i>=50&&(m+="，<span> "+i+" </span>位同学订阅"),""!==m&&(m+="，赶快与大家一起学习吧~"),m+="</span>"),'\n          <div class="orderpayment_c_goods_warp">\n            <img class="orderpayment_c_goods_img" src="'+(e.product_pic&&e.product_pic.split("|")[0]||"https://img-home.csdnimg.cn/images/20210910025238.png")+'" />\n          </div>\n          <div class="orderpayment_c_goods_t">\n            <p>'+e.name+_+'</p>\n            <p class="orderpayment_c_goods_t_info">'+g+m+"</span></p>\n          </div>\n    "},setGoodsListHtml:function(){for(var e="",o=0;o<this.balanceList.length;o++){var t=this.balanceList[o],n=t.ext||{};e+='<div class="orderpayment_c_l_bitem orderpayment_item '+(this.activeGoodsId==n.goodsId?"active":"")+"\" data-goods='"+JSON.stringify({index:o,type:"balance"})+"'>\n          <span class=\"orderpayment_c_l_b_tips  "+("1024.00"===n.originalPrice?"special":"")+"  "+(n.activityContent?"":"none")+'">'+(n.activityContent||"")+'</span>\n          <div class="orderpayment_c_l_b_price"><span>¥</span>'+n.price+'</div>\n          <div class="orderpayment_c_l_b_dis">'+(n.activityDiscountDesc||"")+'</div>\n          <div class="orderpayment_c_l_b_activity '+(n.activityDesc?"":"none")+'"><p>'+(n.activityDesc||"")+"</p></div>\n        </div>"}for(var s="",o=0;o<this.goodsList.length;o++){var i=this.goodsList[o].ext.activityContent;s+='<div class="orderpayment_c_l_goodsitem orderpayment_c_l_bitem orderpayment_item '+(this.activeGoodsId==this.goodsList[o].ext.goodsId?"active":"")+" "+(I.show_config.showGoods?"":"none")+"\" data-goods='"+JSON.stringify({index:o,type:"other"})+"'>\n          <span class=\"orderpayment_c_l_b_tips "+(i?"default":"none")+'" >'+i+'</span>\n            <div class="orderpayment_c_l_b_price"><span>¥</span>'+this.goodsList[o].ext.price+'</div>\n            <div style="text-decoration: line-through; " class="orderpayment_c_l_b_dis"><span>¥</span>'+this.goodsList[o].ext.originalUnitPrice+'</div>\n            <div class="orderpayment_c_l_b_activity '+("string"!=typeof this.show_params.params[o].aloneDesc||this.show_params.params[o].aloneDesc?"":"none")+'"><p>'+(this.show_params.params[o].aloneDesc?this.show_params.params[o].aloneDesc:S[L])+"</p></div>\n        </div>"}return e+s},setPaylistHtml:function(){var e=I.checkBalacneGoods?I.balanceGoodsInfo:I.goodsInfo,o=[];e.payTypeList&&e.payTypeList.forEach(function(e){"unionpay"!==e.name&&o.push(e)});for(var t="",n=0;n<o.length;n++)t+='<img class="icon_item '+o[n].name+'" src='+JSON.stringify(o[n].image)+' alt="img">';return t+'<span class="pay_intro">扫码支付</span>'},autoFind_activityDesc2_Sku_showTips:function(){var e=I.balanceList.filter(function(e){return e.ext&&e.ext.activityDesc2});if(e.length>0){var s=e[0].ext;if(s.activityDesc2&&t(".orderpayment_dialog .orderpayment_c_activity .desc-text").text(s.activityDesc2),s.activityTips){t(".orderpayment_dialog .orderpayment_c_activity .activity_tips_box").show(),t(".orderpayment_dialog .orderpayment_c_activity .tip-content").html(s.activityTips.replace(/\\n/g,"<br>"));n(o.querySelector(".orderpayment_dialog .activity_tips_box"),o.querySelector(".orderpayment_dialog .activity_tips"))}}},setVoucherListHtml:function(){for(var e=I.goodsInfo.cashCouponVoList||[],o="",t=0;t<e.length;t++)o+='<div class="voucher_card '+(I.temporaryVoucherKeysObj[e[t].couponKey]?"checked":"")+'" data-key="'+e[t].couponKey+'">\n          <div class="c1">\n            余¥<span class="v_c_num">'+e[t].residualValue/100+'</span><span class="v_c_all">面值:¥'+e[t].groupValue/100+'</span>\n          </div>\n          <div class="c2 ellipis" alt="'+e[t].description+'">'+e[t].description+'</div>\n          <div class="c3">有效期至'+e[t].validDate+"</div>\n        </div>";return o},setVoucherHtml:function(){return'<div class="voucher_warp none">\n        <div class="v_w_head">\n          <div class="v_w_desc">\n            <div>\n              金额抵用：<span class="v_unit">¥ </span><span class="v_price">0</span>使用代金券0张，抵用0元\n            </div>\n          </div>\n          <div class="hide_warp" id="sureUseVoucher">\n            <span class="voucher-title">确认使用</span>\n          </div>\n        </div>\n        <div class="v_w_content">\n          '+this.setVoucherListHtml()+"\n        </div>\n      </div>"},setPayPriceHtml:function(){var e=(I.priceInfo,I.checkBalacneGoods?I.balanceGoodsInfo:I.goodsInfo),o="";return"exp"==j&&(o='<li class="gifts-info '+("balance"===I.goodsInfo.goodsType&&I.activeGoodsActivity?"":"none")+'">\n          <div class="gifts-icon">\n            <img src="'+k+'/fire.svg" style="width:32px;height:32px" />\n            <img src="'+k+'/present-text.svg" style="width:14px;heigh:14px;right: 50%;bottom: 50%;transform: translate(50%, 50%);" />\n          </div>\n          <div class="gifts-info-desc">'+I.activeGoodsActivity.replace(/.?\d+/g,function(e){return'<span style="color: #FC5531;"> '+e+" </span>"}).replace("赠","价值")+"</div>\n        </li>"),'<ul class="commodity_desc">\n                    <li class="amount_actually">\n                      待支付金额<span class="num">¥ <b>'+I.price+"</b></span>"+(parseFloat(e.old_price)<=parseFloat(I.price)?"":"<em><i>"+e.old_price+"</i>元</em>")+'<span class="'+(e.num>1?"":"none")+'">（共'+e.num+'件）</span>\n                      <span class="'+(e.totalDiscountPrice>0?"block":"none")+' price_discount_msg">已优惠￥'+e.totalDiscountPrice+'</span>\n                    </li>\n                    <li class="voucher '+(e.cashCouponVoList&&e.cashCouponVoList.length>0?"":"none")+'" id="useVoucherBtn"><div class="voucher-title ">有'+e.cashCouponVoList.length+'张代金券可用，选择代金券</div>\n                    </li>\n                    <li class="gift '+(e.discount_msg?"block":"none")+'">\n                      <img src="'+k+'/enjoy.png" alt="">\n                      <span>'+e.discount_msg+'</span>\n                    </li>\n\n                    <li class="user_balance '+(e.available_amount>0?I.isUseBalance?"active":"":"disable")+" "+(19===e.flag?"none":"")+'" >\n                      <img src="'+k+'/unchecked.png" alt="" class="unchecked">\n                      <img src="'+k+'/checked.png" alt="" class="checked">\n                      钱包余额 <b class="num">'+e.available_amount+'</b>\n                      <div class="balance_tips_box">\n                        <img src="'+k+'/help.png" alt="" class="help">\n                        <span class="balance_tips">\n                          <i class="chat"></i>\n                          <b>抵扣说明：</b>\n                          <em>1、余额是钱包充值的虚拟货币，按照1:1的比例进行支付金额的抵扣；</em>\n                          <em>2、余额无法直接购买下载，可以购买VIP、课程；</em>\n                        </span>\n                      </div>\n                      <span class="'+(I.price>0?"block":"none")+'" style="font-size: 12px; color: #999aaa;">钱包余额不足</span>\n                    </li>\n                    <li id="countdownLine" class="'+(0==I.price?"none":"block")+'"><span>支付倒计时：<span id="payingCountdown"></span></span></li>\n                    '+o+'\n                    <div class="btn '+(0!=I.price?"none":"block")+'" id="pay_btn">确认余额支付</div>\n                  </ul>'},renderDialog:function(){this.createMask();var o=this.setGoodsInfos(),n=this.setGoodsListHtml(),s=this.setPaylistHtml(),i=this.setPayPriceHtml(),a=this.setVoucherHtml(),r="";"exp"===j&&(r='<div class="purchase-tips">该专栏内容为虚拟产品，一经购买不得退款</div>');var c='<div id="order-payment" class="order-payment noselect">\n          <div class="orderpayment_dialog">\n              <div class="orderpayment_header">\n                <div class="orderpayment_header_title">扫码支付</div>\n                <span class="orderpayment_header_btn"> + </span>\n              </div>\n              <div class="orderpayment_content">\n                <div class="orderpayment_c_goodsinfo '+(I.show_config.showHeads?"":"none")+'">\n                  '+o+'\n                </div>\n                <div class="orderpayment_c_goodslist '+(I.show_config.showGoods?"":"none")+'">\n                  <span class="list_btn btn_left">\n                    <img src="'+k+'/icon-left.png">\n                  </span>\n                  <span class="list_btn btn_right">\n                    <img src="'+k+'/icon-right.png">\n                  </span>\n                  <div class="orderpayment_c_goodslist_scroll">\n                    '+n+'\n                  </div>\n                </div>\n                <div class="orderpayment_c_activity">\n                   <span class="desc-text"">'+(this.goodsInfo.activityDesc||"")+'</span>\n                   <div style="display:none" class="activity_tips_box">\n                        <img src="'+k+'/help.png" alt="" class="help">\n                        <span class="activity_tips">\n                          <i class="chat"></i>\n                           <div class="tip-content"></div>\n                        </span>\n                      </div>\n                </div>\n                <div class="orderpayment_paybox">\n                  <div class="recharge_mode '+(0!=I.price?"block":"visnone")+'">\n                    <div class="recharge_mode_qr_code" id="ordertip_qr_code">\n                      <img class="loading" src="'+k+'/checked.png" width="50" height="50">\n                    </div>\n                    <div id="ordertip_notify" class="pay_notify"></div>\n                    <p class="scan_code">\n                      '+s+'\n                    </p>\n                  </div>\n                  <div class="commodity_box">\n                    '+i+'\n                  </div>\n                  <div class="voucher_warp_box">\n                    '+a+'\n                  </div>\n                </div>\n                <div class="orderpayment_custombox '+(this.show_config.customBox?"":"none")+'"">'+I.show_config.customBox+"</div>\n                "+r+'\n              </div>\n            </div>\n          <div class="orderpayment_mask"></div>\n      </div>\n      ',d=t(c);t("#order-payment-box").append(d),I.listScrollWidth=t(".orderpayment_c_goodslist_scroll").width()||0,I.listBoxWidth=t(".orderpayment_c_goodslist").width()||626,I.listBoxWidth>=I.listScrollWidth&&t("#order-payment-box .list_btn").hide(),e.report&&window.csdn.report.viewCheck()},createMask:function(){var e=o.createElement("div");e.id="order-payment-box",o.body.appendChild(e)},getGoodsShowList:function(){t.ajax({url:c()+"mp/mallorder/api/internal/goods/showListV2?showType=balancePopup",type:"GET",dataType:"json",contentType:"application/json",xhrFields:{withCredentials:!0},success:function(e){e.data?(I.balanceList=e.data.balancePopup||[],I.activeGoodsId=I.goodsInfo.goods_id,I.checkBalacneGoods=!1,I.initDialog(),I.getPayCode()):void 0},error:function(e){void 0}})},getGoodsShowListByIds:function(e,o){t.ajax({url:c()+"mp/mallorder/api/internal/goods/listForPopup",type:"POST",dataType:"json",contentType:"application/json",data:e?JSON.stringify({goodsList:e}):"",async:!1,xhrFields:{withCredentials:!0},success:function(e){e.data?(I.goodsList=e.data,o&&o(I.findDefaultGood(I.goodsList))):void 0},error:function(e){void 0}})},getGoodsInfo:function(e,o){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];e.ext&&delete e.ext,t.ajax({url:c()+"mp/mallorder/api/internal/goods/getGoodsInfo",type:"GET",dataType:"json",data:i(e,{goods_id:e.goodsId,product_id:e.productId}),contentType:"application/json",xhrFields:{withCredentials:!0},success:function(e){200==e.code&&e.data?(19===e.data.flag&&(I.isUseBalance=!1),o&&o(e.data),I.changePriceHtml(I.price),n&&I.getPayCode(e.data)):void 0},error:function(e){void 0}})},countVoucher:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],o=0,t=[];for(var n in e?I.temporaryVoucherKeysObj:I.voucherKeysObj)(e?I.temporaryVoucherKeysObj[n]:I.voucherKeysObj[n])&&t.push(n);return I.goodsInfo.cashCouponVoList.forEach(function(e){t.indexOf(e.couponKey)>-1&&(o+=Number(e.residualValue))}),o/100},resetVoucherHtml:function(){this.voucherKeysList=[],this.voucherKeysObj={},this.temporaryVoucherKeysList=[],this.temporaryVoucherKeysObj={},t(".orderpayment_dialog .voucher_warp_box").html(I.setVoucherHtml())},changeVoucherPriceHtml:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],o=I.countVoucher(e),t=I.goodsInfo.new_price,n=t>o?o:Number(t).toFixed(2);return'<div>金额抵用：<span class="v_unit">¥</span><span class="v_price">'+n+"</span>使用代金券"+(e?I.temporaryVoucherKeysList:I.voucherKeysList).length+"张，抵用"+n+"元</div>"},goodsCodeData:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=0;n=this.isUseBalance?e-t-o<0?0:(100*e-100*t-100*o)/100:e-t<0?0:(100*e-100*t)/100,this.price=Number(n).toFixed(2)},changePriceHtml:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;Number(e)<=0?(t(".orderpayment_dialog .recharge_mode").addClass("visnone"),t(".orderpayment_dialog #pay_btn").addClass("show").removeClass("none"),t(".orderpayment_dialog #countdownLine").removeClass("show").addClass("none")):(t(".orderpayment_dialog .recharge_mode").removeClass("visnone"),t(".orderpayment_dialog #pay_btn").removeClass("show").addClass("none"),t(".orderpayment_dialog #countdownLine").addClass("show").removeClass("none")),t(".orderpayment_dialog .amount_actually .num b").html(e)},getPayCode:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:I.checkBalacneGoods?I.balanceGoodsInfo:I.goodsInfo,o={product_id:e.product_id,goods_id:e.goods_id,goodsSource:e.goodsSource||"",flag:e.flag,sale_source:I.sale,report_ext:Object.assign(I.reportExt,{abTest342:j}),is_use_balance:Number(I.isUseBalance),coupon_key:e.coupon_key,cash_coupon_keys:e.cash_coupon_keys,use_cache:!0,success_function:l,error_function:p,timeout_function:h,payment_function:u,get_pay_success_callback:m},t=I.show_params.params.find(function(o){return o.goodsId==e.goods_id})||{};I._cart.qrPay(Object.assign(t,o))},findDefaultGood:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],o=e.find(function(e){return 1===e.ext.default})||e[0];return i({goods_id:o.ext.goodsId||"",flag:o.ext.flag||"",product_id:o.ext.productId||""},o.ext,I.show_in_goodsobj[o.ext.goodsId])},moveTab:function(e){var o=t(".orderpayment_c_goodslist_scroll");"left"==e?(I.transX>=0||Math.abs(I.transX)<2.5*I.goodsTabWidth?(I.transX=0,t("#order-payment .btn_left").hide()):I.transX+=I.goodsTabWidth,t("#order-payment .btn_right").show(),o.css("transform","translateX("+I.transX+"px)")):(Math.abs(I.transX)+I.listBoxWidth>=I.listScrollWidth||I.listScrollWidth-I.listBoxWidth-Math.abs(I.transX)<2.5*I.goodsTabWidth?(I.transX=-(I.listScrollWidth-I.listBoxWidth),t("#order-payment .btn_right").hide()):I.transX-=I.goodsTabWidth,t("#order-payment .btn_left").show(),I.transX-=10,o.css("transform","translateX("+I.transX+"px)"))}};var P=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{el:"",leftTime:0};_classCallCheck(this,e);var n=o.querySelector(t.el);if(!n)return void void 0;this.el=t.el,this.targetElement=n,this.time2Show="",this.leftTime=t.leftTime,this.time2Stop=this.leftTime,this.timer=null,this.lastRecordTime=performance.now(),this.startCountdown=this.startCountdown.bind(this),this.step=this.step.bind(this)}return _createClass(e,[{key:"step",value:function(){this.timer&&cancelAnimationFrame(this.timer),this.time2Stop<=0||(this.changeTime(),this.timer=requestAnimationFrame(this.step))}},{key:"changeTime",value:function(){var e=performance.now();if(!(e-this.lastRecordTime<80)){var o=this.formatDate(this.time2Stop),t=(o.dd,o.hh,o.mm),n=void 0===t?0:t,s=o.ss,i=void 0===s?0:s,a=o.mss,r=void 0===a?0:a;this.time2Show=n+":"+i+":"+r,this.targetElement.innerText=this.time2Show,this.time2Stop-=e-this.lastRecordTime,this.lastRecordTime=e}}},{key:"startCountdown",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3e5;this.timer&&(cancelAnimationFrame(this.timer),this.targetElement=o.querySelector(this.el),this.lastRecordTime=performance.now()),this.time2Stop=e,requestAnimationFrame(this.step)}},{key:"stopCountdown",value:function(){this.timer&&cancelAnimationFrame(this.timer),this.time2Stop=0}},{key:"formatDate",value:function(e){if(e<=0)return{mss:"0",ss:"00",mm:"00",hh:"00"};var o={},t=e;return o.mss=Math.floor(t%1e3/100),t/=1e3,o.ss=("0"+Math.floor(t%60)).slice(-2),t/=60,o.mm=("0"+Math.floor(t%60)).slice(-2),t/=60,o.hh=("0"+Math.floor(t%24)).slice(-2),t/=24,o}}]),e}(),O=void 0;window.csdn.userOrderPayment={show:f,close:w}}(window.csdn=window.csdn||{},document,jQuery);