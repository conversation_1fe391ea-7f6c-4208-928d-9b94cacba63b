if (typeof __googlefc.fcKernelManager.run === 'function') {"use strict";this.default_ContributorServingResponseClientJs=this.default_ContributorServingResponseClientJs||{};(function(_){var window=this;
try{
var Qs,Rs;_.Ns=function(a){a=_.p(a);for(var b=a.next();!b.done;b=a.next()){b=b.value;var c=_.K(b,1);if(Ms.has(c)&&_.K(b,3)===1)switch(_.K(b,2)){case 2:return 3;case 3:case 4:return 2;default:return 1}}return 1};_.Ps=function(a){a=_.p(a);for(var b=a.next();!b.done;b=a.next()){b=b.value;var c=_.K(b,1);if(Os.has(c)&&_.K(b,3)===1)switch(_.K(b,2)){case 2:return 3;case 3:case 4:return 2;default:return 1}}return 1};Qs={UNKNOWN:0,CCPA_DOES_NOT_APPLY:1,NOT_OPTED_OUT:2,OPTED_OUT:3};
Rs={UNKNOWN:0,DOES_NOT_APPLY:1,NOT_OPTED_OUT:2,OPTED_OUT:3};_.Ts=function(a,b,c,d,e,f){this.j=a;this.F=b;this.o=c;this.localizedDnsText=d===void 0?null:d;this.localizedDnsCollapseText=e===void 0?null:e;this.l=f;this.overrideDnsLink=_.Ss(a);this.InitialCcpaStatusEnum=Qs};_.m=_.Ts.prototype;
_.m.load=function(){_.Fo("ccpa",this,!0,this.j);if(this.j.frameElement){var a=_.Eo(this.j).callbackQueue||[];if(Array.isArray(a)){a=_.p(a);for(var b=a.next();!b.done;b=a.next())b=b.value.INITIAL_CCPA_DATA_READY,typeof b==="function"&&b()}}else _.Ko(this.F,"initialCcpaData")};_.m.getInitialCcpaStatus=function(){return this.o};_.m.openConfirmationDialog=function(a){this.l?this.l(a):window.console.error("CCPA override API was used incorrectly! The CCPA message does not exist in this context.")};
_.m.getLocalizedDnsText=function(){return this.localizedDnsText};_.m.getLocalizedDnsCollapseText=function(){return this.localizedDnsCollapseText};_.Ss=function(a){var b,c;return((b=_.Eo(a))==null?void 0:(c=b.ccpa)==null?void 0:c.overrideDnsLink)===!0};_.Vs=function(a,b){this.j=a;this.F=b;this.overrideDnsLink=_.Us(a)};_.Vs.prototype.load=function(a,b,c){a=a===void 0?null:a;b=b===void 0?null:b;_.Fo("__fcusi",this,!0,this.j);var d=null;a&&b&&c&&(d={localizedDnsText:a,localizedDnsCollapseText:b,openConfirmationDialog:c});b={};a=this.F;b=(b.initialUsStatesData=d,b);d=_.Jo(a);a=_.p(Object.entries(b));for(b=a.next();!b.done;b=a.next())c=_.p(b.value),b=c.next().value,c=c.next().value,d.executeRemainingFunctionsWithArgument(b,c)};
_.Us=function(a){var b,c;return((b=a.googlefc)==null?void 0:(c=b.__fcusi)==null?void 0:c.overrideDnsLink)===!0};_.Xs=function(a,b,c,d){this.o=a;this.F=b;this.l=c;this.j=d;this.overrideDnsLink=_.Ws(a);this.InitialUsStatesOptOutStatusEnum=Rs};_.Xs.prototype.load=function(){_.Fo("usstatesoptout",this,!0,this.o);_.Ko(this.F,"initialUsStatesOptOutData")};_.Xs.prototype.getInitialUsStatesOptOutStatus=function(){return this.l};_.Xs.prototype.openConfirmationDialog=function(a){this.j?this.j(a):window.console.error("US States opt out override API was used incorrectly! The US states message does not exist in this context.")};
_.Ws=function(a){var b,c;return((b=a.googlefc)==null?void 0:(c=b.usstatesoptout)==null?void 0:c.overrideDnsLink)===!0};var Ms=new Set([6,7]),Os=new Set([6,1]);_.Ys=function(a){this.A=_.u(a)};_.w(_.Ys,_.H);
}catch(e){_._DumpException(e)}
try{
var du=function(a){this.A=_.u(a)};_.w(du,_.H);var eu=_.v(du);var fu=function(a,b,c,d){this.l=a;this.params=b;this.o=c;this.F=d;this.C=new _.lh(this.l.document,_.G(this.params,3),new _.dh(_.Zk(this.o)));this.j=a.__gppManager;this.B=_.E(this.params,_.Ys,5,_.B())};
fu.prototype.run=function(){var a=this,b;return _.z(function(c){if(a.j){for(var d=[],e=_.p(_.E(a.params,_.Ys,5,_.B())),f=e.next();!f.done;f=e.next()){f=f.value;var g=_.K(f,1);_.sq.has(g)&&_.K(f,2)!==2&&(d.push(_.sq.get(g)),g===1&&(f=a.C,g=_.Ke(_.mh(f),5),_.qh(f,g)))}d.length>0&&(a.j.setCmpSignalStatusNotReady(),a.j.clearSectionValues(d),a.j.setCmpSignalStatusReady())}d=_.Ps(a.B);d===2?_.za(Error("Invalid user initial status for CCPA (NOT_OPTED_OUT).")):(new _.Ts(a.l,a.F,d)).load();(new _.Vs(a.l,a.F)).load();
d=_.Ns(a.B);d===2?_.za(Error("Invalid user initial status for US states opt-out (NOT_OPTED_OUT).")):(new _.Xs(a.l,a.F,d)).load();b=_.Xc(_.A(a.o,_.Wc,6)?_.Zd(_.$k(a.o)):new _.Wc,10);return c.return(b)})};var gu=function(){};gu.prototype.run=function(a,b,c){var d,e;return _.z(function(f){if(f.j==1)return d=eu(b),_.hd(f,(new fu(a,d,_.D(d,_.Yk,2),c)).run(),2);e=f.l;return f.return({ha:_.J(e)})})};_.bl(11,new gu);
}catch(e){_._DumpException(e)}
}).call(this,this.default_ContributorServingResponseClientJs);
// Google Inc.

//# sourceURL=/_/mss/boq-content-ads-contributor/_/js/k=boq-content-ads-contributor.ContributorServingResponseClientJs.zh_CN.EDLi-x4AtIM.es5.O/d=1/exm=ad_blocking_detection_executable,kernel_loader,loader_js_executable/ed=1/rs=AJlcJMzPlS7L9ridl7Ym2GmFaLS9n1PssQ/m=web_iab_us_states_signal_executable
__googlefc.fcKernelManager.run('\x5b\x5b\x5b11,\x22\x5bnull,\x5b\x5bnull,null,null,\\\x22https:\/\/fundingchoicesmessages.google.com\/f\/AGSKWxWWy2hvzKF5VuR5bnksiTj2vWICFa3KCt1R0q5A9KySwUFBjWhptkTAFeorve1KQ-q2nH4uZRfCRVqGRa4dSnl9ZUMDKw_boMud2jNYCmOytOuFw3UUJG5JRR_fL4Nb_ix4qNFWzA\\\\u003d\\\\u003d\\\x22\x5d,null,null,\x5bnull,null,null,\\\x22https:\/\/fundingchoicesmessages.google.com\/el\/AGSKWxXsU-m9VxstkzgH3C1Fc8fBflgQwomo9PKP0E9k-ZCrA43aYCBGNkBUQp6tJvalJQ3L0b6iHBfhfJRXmeZyzsU_4e-MmZQbsRBhF_i2gdhYQTJjzLC6YGXD3a7kabRmn3tnoib_cQ\\\\u003d\\\\u003d\\\x22\x5d,null,\x5bnull,\x5b7,6\x5d,null,null,null,null,null,null,null,null,null,1\x5d\x5d,\\\x22csdn.net\\\x22\x5d\x22\x5d\x5d,\x5bnull,null,null,\x22https:\/\/fundingchoicesmessages.google.com\/f\/AGSKWxWvCs4AbqAP6-FD7pXp5wtUXeFAMLTUl_w2XJFRY_8sfeG8m-mNRfsWGZIa-ZMVHBDZzs7QoUf9siCSAvs0JfMLLR0cyiwViDoogd4r6qSHnDCvSw9OvzE_Y3qa_OmnNScZS6dtfg\\u003d\\u003d\x22\x5d\x5d');}