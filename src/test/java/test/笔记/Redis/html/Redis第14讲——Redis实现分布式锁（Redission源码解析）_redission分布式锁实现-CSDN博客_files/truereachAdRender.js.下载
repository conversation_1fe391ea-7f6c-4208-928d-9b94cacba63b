// vN-1.46.13 18-03-2025
window.pbjs=window.pbjs||{que:[]};const FAILSAFE_TIMEOUT_TR=3e3;let trAdsContent=[];const addedScriptsArr=[],maxRetries=60;let arpsExpiration,urlMatchCond,delayedRenderIntervalFlag=!0,documentInViewport=!0,poweredByTimer=5e3,delayerRenderInterval=2e3,trackElemViewabilityInterval=2e3,logEnable=!1,useExternalJSON=0,truereachLogURL="https://test.truereach.co.in/test-folder/log-it-refresh.json",pubAdsRefreshInterval=5,adNextTimerId="",callbackRefreshTime=5e3,viaGamDataArr=[],preventIntDefaultFlag=!0,publisherId="",setPubAdsUrl="",defaultArpsExpTime=60,adDelayTimer=3e4,targetDivIds=[],multiAdMarginTop=10,multiAdRetryTimer=500,trSizeMapping={MOBILE:{sizes:[[300,250],[336,280],[320,480],[320,250],[300,400],[320,400],[250,250],[200,200],[250,360],[240,400],[300,600],[160,600],[120,600],[320,100],[300,100],[320,75],[300,75],[320,50],[300,50],[234,60],[120,60]]},DESKTOP:{sizes:[[300,250],[336,280],[320,480],[320,250],[300,400],[480,320],[250,250],[200,200],[320,400],[250,360],[240,400],[300,600],[160,600],[120,600],[750,300],[580,400],[468,60],[728,90],[970,90],[970,250],[960,90],[960,60],[640,60],[460,60],[580,90]]}};const trAdsJSON={"data":"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","time":1742297299961} ;let exclusionUrlList=[];function fetchPubAdData(){truereachLog("script-loaded");const e=getDeviceType();if(useExternalJSON){let t=(document.getElementById("interactive_js_adcode")||document.getElementById("interactive_js_ampcode")).src,i=t.split("/"),n=i[i.length-1];publisherId=i[i.length-2];let r=new Date,l=Math.ceil(r.getUTCMinutes()/pubAdsRefreshInterval)*pubAdsRefreshInterval,a=r.getUTCFullYear()+""+r.getUTCMonth()+r.getUTCDate()+r.getUTCHours()+l;const d=(new Date).getTime();let s="";s="true"===localStorage.getItem("truereachDebugMode")?t.replace(n,"pubAdsNew.json?v="+d):t.replace(n,"pubAdsNew.json?v="+a),fetch(s).then((e=>e.json())).then((t=>{if(trAdsContent=JSON.parse(atob(t.data))[e].data,exclusionUrlList=JSON.parse(atob(t.data)).EPU,"c36e1701-3774-11ee-beef-21e73d0cd59b"==publisherId){let e="https://securepubads.g.doubleclick.net/tag/js/gpt.js?network-code=22081762831",t=document.createElement("script");t.src=e,t.async="true",addedScriptsArr.push(e),document.head.appendChild(t),manageAdPushWithDelay()}else manageAdPush()}))}else{let t=(document.getElementById("interactive_js_adcode")||document.getElementById("interactive_js_ampcode")).src.split("/");if(publisherId=t[t.length-2],trAdsContent=JSON.parse(atob(trAdsJSON.data))[e].data,exclusionUrlList=JSON.parse(atob(trAdsJSON.data)).EPU,"c36e1701-3774-11ee-beef-21e73d0cd59b"==publisherId){let e="https://securepubads.g.doubleclick.net/tag/js/gpt.js?network-code=22081762831",t=document.createElement("script");t.src=e,t.async="true",addedScriptsArr.push(e),document.head.appendChild(t),manageAdPushWithDelay()}else manageAdPush()}}function manageAdPushWithDelay(){trAdsContent.map((function(e,t){targetDivIds.push(e.di)})),observeDynamicDivs(),checkExistingDivs(),manageDelayedRender()}function adDelay(e){return new Promise((t=>setTimeout(t,e)))}async function processDivById(e){document.getElementById(e)&&(await adDelay(adDelayTimer),truereachRenderAd(e))}function checkExistingDivs(){targetDivIds.forEach((e=>{processDivById(e)}))}function observeDynamicDivs(){new MutationObserver((e=>{e.forEach((e=>{e.addedNodes.forEach((async e=>{e.nodeType===Node.ELEMENT_NODE&&"div"===e.tagName.toLowerCase()&&e.id&&targetDivIds.includes(e.id)&&(await adDelay(adDelayTimer),truereachRenderAd(e.id))}))}))})).observe(document.body,{childList:!0,subtree:!0})}function manageAdPush(){truereachLog("content-loaded"),trAdsContent=trAdsContent.map((function(e,t){if(0===t&&e.tc&&(callbackRefreshTime=1e3*e.tc),e.dynamicUpd=!1,!!!e.r&&e.ncnt){if(sessionStorage.getItem("tr-navCounter"+e.guid)?(sessionStorage.setItem("tr-navCounter"+e.guid,parseInt(sessionStorage.getItem("tr-navCounter"+e.guid))+1),localStorage.getItem("tr-navCounter"+e.guid)&&localStorage.setItem("tr-navCounter"+e.guid,parseInt(localStorage.getItem("tr-navCounter"+e.guid))+1)):1==e.intlr?manageNavCounter(e,t):2==e.arps?localStorage.getItem("tr-navCounter"+e.guid)?localStorage.setItem("tr-navCounter"+e.guid,parseInt(localStorage.getItem("tr-navCounter"+e.guid))+1):localStorage.setItem("tr-navCounter"+e.guid,0):sessionStorage.setItem("tr-navCounter"+e.guid,0),("interstitial"==e.ft||"multiplex_int"==e.ft||"VignetteAds"==e.ft||"RewardedAds"==e.ft)&&-1==parseInt(localStorage.getItem("tr-navCounter"+e.guid))&&1==parseInt(e.intlr))return"true"===localStorage.getItem("tr-vignette")&&"multiplex_int"==e.ft&&"1"==e.bint&&localStorage.setItem("tr-vignette",!1),"true"===localStorage.getItem("tr-vignette")&&"interstitial"==e.ft&&"1"==e.bint&&localStorage.setItem("tr-vignette",!1),e.rendered=!0,e;if(sessionStorage.getItem("tr-navCounter"+e.guid)&&2!=e.arps&&parseInt(sessionStorage.getItem("tr-navCounter"+e.guid))%parseInt(e.ncnt)!=0)return"true"===localStorage.getItem("tr-vignette")&&"multiplex_int"==e.ft&&"1"==e.bint&&localStorage.setItem("tr-vignette",!1),"true"===localStorage.getItem("tr-vignette")&&"interstitial"==e.ft&&"1"==e.bint&&localStorage.setItem("tr-vignette",!1),e.rendered=!0,e;if(localStorage.getItem("tr-navCounter"+e.guid)&&2==e.arps&&parseInt(localStorage.getItem("tr-navCounter"+e.guid))%parseInt(e.ncnt)!=0)return"true"===localStorage.getItem("tr-vignette")&&"multiplex_int"==e.ft&&"1"==e.bint&&localStorage.setItem("tr-vignette",!1),"true"===localStorage.getItem("tr-vignette")&&"interstitial"==e.ft&&"1"==e.bint&&localStorage.setItem("tr-vignette",!1),e.rendered=!0,e}return"multiplex_int"==e.ft?(e.priAdrCnt=e.pri_adr,e.multiAdrCnt=e.multi_adr,e.ismultiAdStart=!1,initMultiplexAds(t,"")):pushAds(t,"")})),manageDelayedRender()}function manageNavCounter(e,t){if("interstitial"==e.ft||"multiplex_int"==e.ft||"VignetteAds"==e.ft||"RewardedAds"==e.ft)if(localStorage.getItem("lastNavRenderedTime-"+e.guid)){let t=new Date(parseInt(localStorage.getItem("lastNavRenderedTime-"+e.guid)));var i=new Date(t.getFullYear(),t.getMonth(),t.getDate());let r=new Date;var n=new Date(r.getFullYear(),r.getMonth(),r.getDate());i.getTime()===n.getTime()?sessionStorage.setItem("tr-navCounter"+e.guid,parseInt(localStorage.getItem("tr-navCounter"+e.guid))+1):(localStorage.setItem("lastNavRenderedTime-"+e.guid,(new Date).getTime()),localStorage.setItem("tr-navCounter"+e.guid,-1),sessionStorage.setItem("tr-navCounter"+e.guid,-1))}else localStorage.setItem("lastNavRenderedTime-"+e.guid,(new Date).getTime()),sessionStorage.setItem("tr-navCounter"+e.guid,-1),localStorage.setItem("tr-navCounter"+e.guid,-1);return!0}function setDateTimeToCurrent(e,t){let i=new Date;return i.setHours(e.getHours()),i.setMinutes(e.getMinutes()),i.setSeconds(e.getSeconds()),t&&""!=t&&i.setDate(i.getDate()+t),i}function isCurrentTimeInRange(e,t){let i=new Date;return e=new Date(e),t=new Date(t),i>=e&&i<=t&&(t=e.getHours()>t.getHours()||e.getHours()===t.getHours()&&e.getMinutes()>t.getMinutes()||e.getHours()===t.getHours()&&e.getMinutes()===t.getMinutes()&&e.getSeconds()>t.getSeconds()?setDateTimeToCurrent(t,1):setDateTimeToCurrent(t),i>=(e=setDateTimeToCurrent(e))&&i<=t)}function manageAdFormatSetting(e,t){if(t)!1!==e.e_cft&&void 0!==e.e_cft||(e.e_cft=!0,e.e_lst_ft=e.ft,e.e_lst_r=e.r,e.e_lst_tr=e.tr,e.e_lst_intl_spr=e.intl_spr,e.e_lst_pos=e.pos,e.e_lst_e_iv=e.e_iv,e.e_lst_ref_eiv=e.r_eiv),"interstitial"==e.dft&&(e.ft=e.dft,e.e_iv="",e.dftr&&(e.r=e.dftr,e.tr=e.dftrt,e.intl_spr=1,e.r_eiv="")),"sticky"==e.dft&&(e.ft=e.dft,e.pos="bottom"),"interscroller"==e.dft&&(e.ft=e.dft);else if(1==e.e_cft){let t="";e.ft=e.e_lst_ft,e.di?(t=document.getElementById(e.di),t&&t.setAttribute("style","")):e.dcl&&e.dcli&&(t=document.getElementsByClassName(e.dcl)[e.dcli],t&&t.setAttribute("style","")),"interstitial"==e.dft&&(e.e_iv=e.e_lst_e_iv,e.dftr&&(e.r=e.e_lst_r,e.tr=e.e_lst_tr,e.intl_spr=e.e_lst_intl_spr,e.r_eiv=e.e_lst_ref_eiv)),"sticky"==e.dft&&(e.pos=e.e_lst_pos),e.e_cft=!1}}function pushAds(e,t,i=!1,n){let r=trAdsContent[e],l=!!r.r;if(1==r.dynamicUpd&&"RewardedAds"==r.ft&&(r.ft="interstitial",r.dynamicUpd=!1),r.pt&&(poweredByTimer=1e3*r.pt),"true"===localStorage.getItem("tr-vignette")&&"interstitial"==r.ft&&"1"==r.bint)return r.rendered=!0,localStorage.setItem("tr-vignette",!1),r;if(null!=r.gmc&&""!=r.gmc&&(poweredByTimer=10),!r.e_cbh&&"2"==r.uac)return r;if(!r.arps||1!=r.arps||"interstitial"!=r.ft&&1!=r.dynamicUpd){if(r.arps&&2==r.arps&&("interstitial"==r.ft||1==r.dynamicUpd)&&(checkArpsExpiration(),localStorage.getItem("tr-arps")&&"true"===localStorage.getItem("tr-arps")&&0==parseInt(localStorage.getItem("tr-arpsCnt"))))return r}else if(sessionStorage.getItem("tr-arps")&&"true"===sessionStorage.getItem("tr-arps")&&0==parseInt(sessionStorage.getItem("tr-arpsCnt")))return r;if(r.hmt){"string"==typeof r.mta&&(r.mta=JSON.parse(r.mta));let t=Math.floor(Math.random()*trAdsContent[e].mta.length);null==r.e_multadindx?r.e_multadindx=t:(r.e_multadindx++,r.e_multadindx==r.mta.length&&(r.e_multadindx=0)),r.trht=r.mta[r.e_multadindx].trht,r.trbd=r.mta[r.e_multadindx].trbd,r.gid=r.mta[r.e_multadindx].gid,r.madU=r.mta[r.e_multadindx].mAdU,r.refAdU=r.mta[r.e_multadindx].refAdU,r.trht.includes("OutOfPageFormat.REWARDED")?(r.ft="RewardedAds",r.dynamicUpd=!0):r.trht.includes("OutOfPageFormat.INTERSTITIAL")&&(r.ft="VignetteAds",r.dynamicUpd=!0)}""!==r.sdt&&""!==r.edt&&(isCurrentTimeInRange(r.sdt,r.edt)?manageAdFormatSetting(r,!0):manageAdFormatSetting(r,!1));const a=i?(new Date).getTime():"";if(r.e_prev_rid=a,"string"==typeof r.fb&&(r.fb=JSON.parse(r.fb)),t&&!r.fb[t-1]?.head&&!r.fb[t-1]?.body)return r;const d=r.as?.split("*"),s=updateUrlList(getCurrentPageUrl(),!1);if("f8ed7706-55f3-11ee-a69e-5d852a779460"!=publisherId&&(setPubAdsUrl=r.cur_pgurl&&"1"==r.cur_pgurl?"":'googletag.pubads().set("page_url","'+s+'");'),r.pu.length&&"object"==typeof r.pu?r.pu=updateUrlList(r.pu,!0):r.pu=updateUrlList(r.pu,!1),r.epu.length&&"object"==typeof r.epu?r.epu=updateUrlList(r.epu,!0):"1"==r.isepu&&exclusionUrlList.length&&"object"==typeof exclusionUrlList?r.epu=updateUrlList(exclusionUrlList,!0):r.epu=updateUrlList(r.epu,!1),urlMatchCond=1==parseInt(r.uem)?!("string"==typeof r.pu&&r.pu&&s!==r.pu||"object"==typeof r.pu&&r.pu.length&&!(r.pu.indexOf(s)>=0)||"string"==typeof r.epu&&r.epu&&s===r.epu||"object"==typeof r.epu&&r.epu.length&&!(r.epu.indexOf(s)<0)):("string"!=typeof r.pu||!r.pu||s===r.pu)&&("object"!=typeof r.pu||!r.pu.length||r.pu.some((e=>s.includes(e))))&&("string"!=typeof r.epu||!r.epu||s!==r.epu)&&("object"!=typeof r.epu||!r.epu.length||!r.epu.some((e=>s.includes(e)))),urlMatchCond){let s="";if(r.di){if(s=document.getElementById(r.di),!s)return r;documentInViewport&&truereachLog(i?"divId-found-refresh&id="+r.sv:"divId-found&id="+r.sv)}else if(r.dcl&&r.dcli){if(s=document.getElementsByClassName(r.dcl)[r.dcli],!s)return r;truereachLog(i?"dcl-found-refresh&id="+r.sv:"dcl-found&id="+r.sv)}if(r.e_inv||!r.e_iv&&!r.r_eiv||(r.rmv?trackElementActiveViewWithMargin(s,e):trackElementActiveView(s,e),r.e_inv=elementIsVisibleInViewportInitial(s,!0,r)?"IN":"OUT"),i){if(r.r_eiv&&!elementIsVisibleInViewport(s,r)||r.r_div&&!documentInViewport)return r.rendered&&trackElementViewability(e,t||0,!0,a),r}else{if(r.e_iv&&!elementIsVisibleInViewport(s,r)||r.d_iv&&!documentInViewport)return r;if("interstitial"===r.ft||1==r.dynamicUpd)if(localStorage.getItem("lastRenderedTime-"+r.guid)){var o=new Date(parseInt(localStorage.getItem("lastRenderedTime-"+r.guid))),g=(((new Date).getTime()-o.getTime())/1e3).toFixed(0);if(parseInt(g)<parseInt(r.tr))return r.intl_act&&(r.rendered=!0),r;localStorage.setItem("lastRenderedTime-"+r.guid,(new Date).getTime())}else localStorage.setItem("lastRenderedTime-"+r.guid,(new Date).getTime())}if(s.setAttribute("tr-id",r.guid),t?r.fb[t-1].head:r.trht){const d=document.createElement("div");let s=t?r.fb[t-1].head:r.trht;if(t?r.fb[t-1].tt:"ADX"===r.tt){let n=t?t+1:1,l="RewardedAds"===r.ft,a="4371e928-eaba-11ef-a0ca-9f9e49469b51"==publisherId;r.r?i||(s=s.replace("googletag.enableServices()",setPubAdsUrl+'; googletag.enableServices(); googletag.pubads().addEventListener("slotRequested", function (event) { localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+", event); if (window.SLOT_VARIABLE === event.slot) { handleExtTriggerRequest(event, "+e+');}}); googletag.pubads().addEventListener("impressionViewable", function (event) { localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+", event); if(window.SLOT_VARIABLE === event.slot) {handleExtTrigger(event, "+e+");handleVignetteAd(event, "+e+');}}); googletag.pubads().addEventListener("slotRenderEnded", function (event) { localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+", event); if(window.SLOT_VARIABLE === event.slot) {manageSlotEmpty(event, "+e+"); manageRefreshAdRendered(event, "+e+"); manageGPTTag(event, "+e+") }});if("+a+'){googletag.pubads().addEventListener("gameManualInterstitialSlotReady", function (event) { localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+', event); if(window.SLOT_VARIABLE === event.slot) {const triggeredSlot = event.slot;event.makeGameManualInterstitialVisible(); }});googletag.pubads().addEventListener("gameManualInterstitialSlotClosed",function (event) {const triggeredSlot = event.slot;const debugMode = localStorage.getItem("truereachDebugMode") === "true";if (debugMode) {console.log("gameManualInterstitialSlotClosed listener calling for slot:", triggeredSlot.getAdUnitPath());}googletag.destroySlots([triggeredSlot]);});}if('+l+'){ googletag.pubads().addEventListener("rewardedSlotReady", function (event) {const triggeredSlot = event.slot;event.makeRewardedVisible();});googletag.pubads().addEventListener("rewardedSlotGranted", function (event) {const triggeredSlot = event.slot;const debugMode = localStorage.getItem("truereachDebugMode") === "true";if (debugMode) {console.log("rewardedSlotGranted listener calling for slot:", triggeredSlot.getAdUnitPath(),", payload:", JSON.stringify(event.payload));}});googletag.pubads().addEventListener("rewardedSlotClosed", function (event) {const triggeredSlot = event.slot;const debugMode = localStorage.getItem("truereachDebugMode") === "true";if (debugMode) {console.log("rewardedSlotClosed listener calling for slot:", triggeredSlot.getAdUnitPath());}googletag.destroySlots([triggeredSlot]);});}')):s=s.replace("googletag.enableServices()",setPubAdsUrl+'; googletag.enableServices(); googletag.pubads().addEventListener("slotRequested", function (event) {localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+", event); if (window.SLOT_VARIABLE === event.slot) { handleExtTriggerRequest(event, "+e+');}}); googletag.pubads().addEventListener("impressionViewable", function (event) { localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+", event); if(window.SLOT_VARIABLE === event.slot) {handleExtTrigger(event, "+e+"); handleVignetteAd(event, "+e+');}}); googletag.pubads().addEventListener("slotRenderEnded", function (event) { localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+", event); if(window.SLOT_VARIABLE === event.slot) { if(event.isEmpty === true){ manageFallbackADX("+e+","+n+"); manageAdRenderedEmpty(event, "+e+"); } else { manageAdRendered(event, "+e+");} manageGPTTag(event, "+e+") }}); if("+a+'){googletag.pubads().addEventListener("gameManualInterstitialSlotReady", function (event) { localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+', event); if(window.SLOT_VARIABLE === event.slot) {const triggeredSlot = event.slot;event.makeGameManualInterstitialVisible(); }});googletag.pubads().addEventListener("gameManualInterstitialSlotClosed",function (event) {const triggeredSlot = event.slot;const debugMode = localStorage.getItem("truereachDebugMode") === "true";if (debugMode) {console.log("gameManualInterstitialSlotClosed listener calling for slot:", triggeredSlot.getAdUnitPath());}googletag.destroySlots([triggeredSlot]);});} if('+l+'){ googletag.pubads().addEventListener("rewardedSlotReady", function (event) {const triggeredSlot = event.slot;event.makeRewardedVisible();});googletag.pubads().addEventListener("rewardedSlotGranted", function (event) {const triggeredSlot = event.slot;const debugMode = localStorage.getItem("truereachDebugMode") === "true";if (debugMode) {console.log("rewardedSlotGranted listener calling for slot:", triggeredSlot.getAdUnitPath(),", payload:", JSON.stringify(event.payload));}});googletag.pubads().addEventListener("rewardedSlotClosed", function (event) {const triggeredSlot = event.slot;const debugMode = localStorage.getItem("truereachDebugMode") === "true";if (debugMode) {console.log("rewardedSlotClosed listener calling for slot:", triggeredSlot.getAdUnitPath());}googletag.destroySlots([triggeredSlot]);});}')}"ADX"===r.tt&&(s=s.replaceAll("DIV_GPT_ID",i?r.gid+a:r.gid),s=s.replaceAll("SLOT_VARIABLE",r.sv)),1==l&&r.madU&&r.refAdU&&1==i&&r.madU!=r.refAdU&&(s=s.replace(r.madU,r.refAdU)),d.innerHTML=s;let o=d.getElementsByTagName("script");Array.from(o).map(((e,t)=>{let{src:d,id:s,type:o}=e,g=e.text,c=e.crossOrigin,u=e.async;if(!addedScriptsArr.includes(d)){const e=document.createElement("script");if(d&&(e.src=d,addedScriptsArr.push(d)),s&&(e.id=s),o&&(e.type=o),1===t&&"ADX"===r.tt&&l&&r.ft,g&&(e.text=g),c&&(e.crossOrigin=c),u&&(e.async=u),l&&1===t&&"ADX"===r.tt&&e.setAttribute("data-tr",i?r.gid+a:r.gid),i&&"ADX"===r.tt&&l){let e=i?r.gid+n:r.gid;Array.from(document.querySelectorAll("[data-tr="+e+"]")).map((e=>{e.remove()}))}document.head.appendChild(e),finalHeadScript=e}}))}if(t?r.fb[t-1].body:r.trbd){let n,s,o,g;const c=document.createElement("div");let u=t?r.fb[t-1].body:r.trbd,p=!(!r.hb||""==r.hb);"true"===localStorage.getItem("truereachDebugMode")&&console.log("headerbidding setting:"+p),"ADX"===r.tt&&1!=p&&(u=u.replace("googletag.display('DIV_GPT_ID');","googletag.display('DIV_GPT_ID'); window.googletag.pubads().isInitialLoadDisabled() && window.googletag.pubads().refresh(["+r.sv+"])"),u=u.replace("googletag.display(window.SLOT_VARIABLE);","googletag.display(window.SLOT_VARIABLE); window.googletag.pubads().isInitialLoadDisabled() && window.googletag.pubads().refresh([window.SLOT_VARIABLE])")),"ADX"===r.tt&&(u=u.replaceAll("DIV_GPT_ID",i?r.gid+a:r.gid),u=u.replaceAll("SLOT_VARIABLE",r.sv)),c.innerHTML=u;const m=document.createElement("div");if(m.style="bottom: 0; position: relative;",(t?r.fb[t-1].tt:"Adsense"==r.tt)&&(m.style.height=d[1]+"px",m.style.width=d[0]+"px"),"sticky"===r.ft)switch(m.style.position="fixed",r.pos){case"top":(t?r.fb[t-1].tt:"Adsense"!==r.tt)&&(m.style.left="0",m.style.right="0",m.style.display="flex"),m.style["justify-content"]="center",m.style.top="0",m.style.bottom="auto",m.style["z-index"]="999",m.style.margin="0 auto";break;case"bottom":(t?r.fb[t-1].tt:"Adsense"!==r.tt)&&(m.style.left="0",m.style.right="0",m.style.display="flex"),m.style["justify-content"]="center",m.style["z-index"]="999",m.style.margin="0 auto";break;case"left":(t?r.fb[t-1].tt:"Adsense"!==r.tt)&&(m.style.left="0",m.style.display="flex"),m.style.left="0",m.style.top="0",m.style.bottom="auto",m.style["z-index"]="999";break;case"right":(t?r.fb[t-1].tt:"Adsense"!==r.tt)&&(m.style.right="0",m.style.display="flex"),m.style.right="0",m.style["justify-content"]="center",m.style.top="0",m.style.bottom="auto",m.style["z-index"]="999",m.style.margin="0 auto"}if("sticky-scroll"===r.ft&&(m.style.position="fixed",(t?r.fb[t-1].tt:"Adsense"!==r.tt)&&(m.style.left="0",m.style.right="0",m.style.display="flex"),m.style["justify-content"]="center",m.style["z-index"]="999",m.style.margin="0 auto",m.style.height=d[1]+"px",m.style.width="auto",m.style["overflow-y"]="hidden",m.style["background-color"]="#ece5e578","top"===r.pos))m.style.top="0",m.style.bottom="auto";if("interstitial"===r.ft||r.sk&&("sticky"===r.ft||"sticky-scroll"===r.ft)){const t=document.createElement("button");t.id="button-"+a+e,t.style.position="absolute",t.style.right="0",t.style.width="44px",t.style.height="27px",t.style.padding="0 1px 0 2px",t.style.visibility="hidden",t.style.background="#f5f5f5",t.style.color="#333",t.style.border="1px solid #333",t.style.cursor="pointer","CROSS"===r.acb?(t.innerHTML="X",t.style.width="32px",t.style.height="19px",t.style.border="0px",t.style["font-weight"]="bold",t.style.display="flex",t.style["align-items"]="center",t.style["justify-content"]="center",t.style["z-index"]="9999999",t.style["font-size"]="13px"):"CLOSE"===r.acb||"LFCLOSE"===r.acb?(t.innerHTML="x",t.style["z-index"]="9999999",t.style["font-size"]="13px"):(t.style["border-radius"]="50px",t.innerHTML="SKIP",t.style["z-index"]="9999999",t.style["font-size"]="13px"),"sticky-scroll"===r.ft&&(t.style.position="fixed",t.style["z-index"]="9999999",t.style["font-size"]="13px"),"interstitial"===r.ft&&manageInterstitialBtn(r,t),m.appendChild(t),setTimeout((function(){document.getElementById("button-"+a+e).addEventListener("click",(function(t){closeAd(m,r,e)}))}),0)}"interscroller"===r.ft&&(n=document.createElement("div"),n.className="wrapper_in",n.classList=n.classList.length?n.classList.value+" del-"+r.guid:"del-"+r.guid,n.style="position: relative;",n.style.height=d[1]+"px",s=document.createElement("div"),s.className="bg-wrapper_in",d&&(s.style="clip: rect(0px, "+d[0]+"px, "+d[1]+"px, 0px); position: absolute; margin-top: 5px;"),m.className="bg_in",m.style.position="fixed",m.style.top="0",m.style.bottom="0",(t?r.fb[t-1].tt:"Adsense"!==r.tt)&&(m.style.display="flex"),m.style["align-items"]="center",n.appendChild(s).appendChild(m));let f=c.getElementsByTagName("script")[0],{src:y,id:h,type:b}=f,v=f.getAttribute("data-width"),x=f.getAttribute("data-height"),A=f.text,I=c.getElementsByTagName("ins")[0];if(I){let e=I.className,t=I.getAttribute("style"),i=I.getAttribute("data-ad-format"),n=I.getAttribute("data-ad-layout-key"),r=I.getAttribute("data-ad-client"),l=I.getAttribute("data-ad-slot");o=document.createElement("ins"),e&&(o.className=e),t&&o.setAttribute("style",t),i&&o.setAttribute("data-ad-format",i),n&&o.setAttribute("data-ad-layout-key",n),r&&o.setAttribute("data-ad-client",r),l&&o.setAttribute("data-ad-slot",l)}const _=document.createElement("script");f&&(y&&(_.src=y),h&&(_.id=h),b&&(_.type=b),v&&_.setAttribute("data-width",v),x&&_.setAttribute("data-height",x),A&&(_.text=A));let w=c.getElementsByTagName("div")[0];if(w){let e=w.id,t=w.getAttribute("style");const i=document.createElement("div");e&&(i.id=e),t&&(i.style=t),_&&i.appendChild(_),g&&i.appendChild(g),m.appendChild(i)}else o&&m.appendChild(o).appendChild(_),m.appendChild(_);if("interscroller"!==r.ft&&(m.classList=m.classList.length?m.classList.value+" del-"+r.guid:"del-"+r.guid),r.di){let s,o=document.getElementById(r.di);if(t&&Array.from(document.getElementsByClassName("del-"+r.guid)).map(((e,t)=>{e.remove()})),i&&"ADX"===r.tt&&l&&Array.from(document.getElementsByClassName("del-"+r.guid)).map(((e,t)=>{e.style.display="none"})),t?r.fb[t-1].tt:"Adsense"===r.tt){if(s=document.createElement("div"),s.classList=s.classList.length?s.classList.value+" del-"+r.guid:"del-"+r.guid,o.style="max-width: "+d[0]+"px; max-height: "+d[1]+"px; padding: 10px 0px;margin: 0px auto;display: flex;justify-content: center;",o.appendChild(s),s.style="bottom: 0px; margin: 0px auto; left: 1px; z-index: 99;","interstitial"===r.ft&&(s.style.height="100%",s.style.margin="0px auto",s.style.width="100%",s.style.position="fixed",s.style.top="0",s.style.left="0",s.style["z-index"]="99999999999999",s.style.background="#000000e6",s.style["justify-content"]="center",s.style["align-items"]="center",s.style.visibility="hidden"),r.ws){const e=document.createElement("style");e.classList="del-style-"+r.guid,e.innerHTML=r.ws,s.appendChild(e)}"interscroller"===r.ft?(s.style.display="block",s.style.width=d[0]+"px",s?.appendChild(n)):(s.style["justify-content"]="center",s?.appendChild(m)),truereachLog(i?"tag-pushed-refresh&id="+r.sv:"tag-pushed&id="+r.sv)}else{if(1==r.vgm&&"interstitial"==r.ft?manageAdViaGam("ad_style",e):(o.style.display="c36e1701-3774-11ee-beef-21e73d0cd59b"==publisherId?"none":"flex",o.style.margin="0 auto",o.style.overflow="unset","interstitial"===r.ft&&(o.style.height="100%",o.style.margin="0px auto",o.style.width="100%",null!=r.gmc&&""!=r.gmc?(o.style.position="unset",o.style.background="none"):(o.style.position="fixed",o.style.background="#000000e6"),o.style.top="0",o.style.left="0",o.style["z-index"]="99999999999999",o.style["justify-content"]="center",o.style["align-items"]="center",o.style.visibility="hidden")),r.ws&&(!l||!i)){const e=document.createElement("style");e.classList="del-style-"+r.guid,e.innerHTML=r.ws,o.appendChild(e)}"interscroller"===r.ft?(o.style.display="block",o?.appendChild(n)):(o.style["justify-content"]="center",o?.appendChild(m)),truereachLog(i?"tag-pushed-refresh&id="+r.sv:"tag-pushed&id="+r.sv)}r.rendered=!0;let g=!1,c=setInterval((function(){if(Array.from(m.querySelectorAll("video")).length||Array.from(m.querySelectorAll("iframe")).length||Array.from(m.querySelectorAll("[id^=fd_ad_]")).length){if(g||(clearInterval(c),g=!0),r.isEmpty)return!0;if("interscroller"!==r.ft){let e=m.querySelectorAll("iframe")[0]?.height,t=m.querySelectorAll("iframe")[0]?.width;e&&t&&"sticky-scroll"===r.ft&&(m.style.width="auto")}if("sticky-scroll"!==r.ft||l&&i||manageStickyScroll(r,o),(""==r.ft||"interstitial"==r.ft||"sticky"==r.ft||"sticky-scroll"==r.ft)&&!r.dynamicUpd){checkAndUpdateIframe(m.querySelector("iframe"),r)}if((t?r.fb[t-1].tt:"Adsense"===r.tt)?(s.style.display="flex",s.style.visibility="visible"):(o.style.display="flex","interstitial"===r.ft&&r.intl_onact),document.getElementById("button-"+a+e)&&("interstitial"===r.ft&&r.intl_onact||null!=r.gmc&&""!=r.gmc||(document.getElementById("button-"+a+e).style.visibility="visible")),r.pbl||r.pbt||r.pbi){const e=document.createElement("div");"interscroller"===r.ft?e.className="powered_ads_is":e.className="powered_ads";const t=document.createElement("style");let i,n,l;"interscroller"===r.ft?t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads_is { display: flex; align-items: center; bottom: 0; z-index: 999; position: relative; background: #000000b5;padding: 2px 4px;} [tr-id="'+r.guid+'"] .powered_ads_is a { height: 15px; display: flex; align-items: center; padding: 0; width: max-content; } [tr-id="'+r.guid+'"] .powered_ads_is p { color: #fff; font-size: 12px;line-height:15px; margin: 0; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads_is img { width: 70px !important;padding: 0 0 0 5px;}':"sticky"===r.ft||"sticky-scroll"===r.ft?"bottom"===r.pos?t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads { display: flex; align-items: center; width: fit-content; top: -19px; left: 0; z-index: 999; background: #000000b5;padding: 2px 4px; position: absolute;} [tr-id="'+r.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+r.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}':t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads { display: flex; align-items: center; width: fit-content; bottom: -19px; left: 0; z-index: 999; background: #000000b5;padding: 2px 4px; position: absolute;} [tr-id="'+r.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+r.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}':null!=r.gmc&&""!=r.gmc?"top"===r.pbp?t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads {visibility:hidden; display: flex; align-items: center; width: fit-content; position:absolute; bottom: auto; top:-19px; z-index: 99999999; background: #000000b5;padding: 2px 4px;} [tr-id="'+r.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+r.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}':t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads {visibility:hidden; display: flex; align-items: center; width: fit-content; bottom: 0; z-index: 999; background: #000000b5;padding: 2px 4px;} [tr-id="'+r.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+r.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}':"top"===r.pbp?t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads { display: flex; align-items: center; width: fit-content; position:absolute; top:-19px;  bottom: auto; z-index: 99999999; background: #000000b5;padding: 2px 4px;} [tr-id="'+r.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+r.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}':t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads { display: flex; align-items: center; width: fit-content; bottom: 0; z-index: 999; background: #000000b5;padding: 2px 4px;} [tr-id="'+r.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+r.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}',e.appendChild(t),r.pbl&&(l=document.createElement("a"),l.href=r.pbl,l.target="_blank",e.appendChild(l)),r.pbt&&(i=document.createElement("p"),i.innerHTML=r.pbt,l?l.appendChild(i):e.appendChild(i)),r.pbi&&(n=document.createElement("img"),n.src=r.pbi,l?l.appendChild(n):e.appendChild(n)),"interscroller"===r.ft?Array.from(m.children)[0].appendChild(e):m.appendChild(e)}}}),poweredByTimer),u=t?1e3*r.fb[t-1].timeDelay:1e3*r.td;if(setTimeout((function(){g||(clearInterval(c),g=!0,(t?r.fb[t-1].tt:"ADX"!==r.tt)&&pushAds(e,t?++t:1))}),u),l&&"ADX"===r.tt&&!r.e_skref&&!r.dynamicUpd&&("interstitial"!==r.ft||r.intl_spr)){let i=setTimeout((()=>{r.e_skref||pushAds(e,t||0,!0,a)}),1e3*r.tr);r.e_arftid=i}}r.dcl&&Array.from(document.getElementsByClassName(r.dcl)).map(((d,s)=>{if(s!=r.dcli)return!0;let o="interscroller"===r.ft?n.cloneNode(!0):m.cloneNode(!0);if(t&&Array.from(document.getElementsByClassName("del-"+r.guid)).map(((e,t)=>{e.remove()})),i&&"ADX"===r.tt&&l&&Array.from(document.getElementsByClassName("del-"+r.guid)).map(((e,t)=>{e.style.display="none"})),r.ws&&(!l||!i)){const e=document.createElement("style");e.classList="del-style-"+r.guid,e.innerHTML=r.ws,d.appendChild(e)}d.appendChild(o),truereachLog(i?"tag-pushed-refresh&id="+r.sv:"tag-pushed&id="+r.sv),r.rendered=!0,1==r.vgm&&"interstitial"==r.ft?manageAdViaGam("ad_style",e):(d.style.display="flex",d.style.margin="0 auto",d.style.overflow="unset","interstitial"===r.ft&&(d.style.height="100%",d.style.margin="0px auto",d.style.width="100%",d.style.position="fixed",d.style.top="0",d.style.left="0",d.style["z-index"]="2147483647",d.style.background="#000000b3",d.style["justify-content"]="center",d.style["align-items"]="center",d.style.visibility="hidden")),"interscroller"===r.ft?d.style.display="block":d.style["justify-content"]="center";let g=!1,c=setInterval((function(){if(Array.from(o.querySelectorAll("video")).length||Array.from(o.querySelectorAll("iframe")).length||Array.from(o.querySelectorAll("[id^=fd_ad_]")).length){if(g||(clearInterval(c),g=!0),r.isEmpty)return!0;if("interscroller"!==r.ft){let e=o.querySelectorAll("iframe")[0]?.height,t=o.querySelectorAll("iframe")[0]?.width;e&&t&&"sticky-scroll"===r.ft&&(o.style.width="auto")}if((""==r.ft||"interstitial"==r.ft||"sticky"==r.ft||"sticky-scroll"==r.ft)&&!r.dynamicUpd){checkAndUpdateIframe(m.querySelector("iframe"),r)}if("interstitial"===r.ft&&(d.style.display="flex","interstitial"===r.ft&&r.intl_onact),"sticky-scroll"!==r.ft||l&&i||manageStickyScroll(r,d),document.getElementById("button-"+a+e)&&("interstitial"===r.ft&&r.intl_onact||(document.getElementById("button-"+a+e).style.visibility="visible")),(r.pbl||r.pbt||r.pbi)&&("interscroller"!==r.ft||!r.e_ispbs)){const e=document.createElement("div");"interscroller"===r.ft?e.className="powered_ads_is":e.className="powered_ads";const t=document.createElement("style");let i,n,l;"interscroller"===r.ft?t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads_is { display: flex; align-items: center; bottom: 0; z-index: 999; position: relative; background: #000000b5;padding: 2px 4px;} [tr-id="'+r.guid+'"] .powered_ads_is a { height: 15px; display: flex; align-items: center; padding: 0; width: max-content; } [tr-id="'+r.guid+'"] .powered_ads_is p { color: #fff; font-size: 12px;line-height:15px; margin: 0; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads_is img { width: 70px !important;padding: 0 0 0 5px;}':"sticky"===r.ft||"sticky-scroll"===r.ft?"bottom"===r.pos?t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads { display: flex; align-items: center; width: fit-content; top: -19px; left: 0; z-index: 999; background: #000000b5;padding: 2px 4px; position: absolute;} [tr-id="'+r.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+r.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}':t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads { display: flex; align-items: center; width: fit-content; bottom: -19px; left: 0; z-index: 999; background: #000000b5;padding: 2px 4px; position: absolute;} [tr-id="'+r.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+r.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}':"top"===r.pbp?t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads { display: flex; align-items: center; width: fit-content; bottom: auto; position:absolute; top:-19px; z-index: 99999999; background: #000000b5;padding: 2px 4px;} [tr-id="'+r.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+r.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}':t.innerHTML='[tr-id="'+r.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+r.guid+'"] .powered_ads { display: flex; align-items: center; width: fit-content; bottom: 0; z-index: 999; background: #000000b5;padding: 2px 4px;} [tr-id="'+r.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+r.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}',e.appendChild(t),r.pbl&&(l=document.createElement("a"),l.href=r.pbl,l.target="_blank"),r.pbt&&(i=document.createElement("p"),i.innerHTML=r.pbt,l?l.appendChild(i):e.appendChild(i)),r.pbi&&(n=document.createElement("img"),n.src=r.pbi,l?l.appendChild(n):e.appendChild(n)),l&&e.appendChild(l),"interscroller"===r.ft?(d.appendChild(e),r.e_ispbs=!0):o.appendChild(e)}}}),poweredByTimer),u=t?1e3*r.fb[t-1].timeDelay:1e3*r.td;if(setTimeout((function(){g||(clearInterval(c),g=!0,(t?r.fb[t-1].tt:"ADX"!==r.tt)&&pushAds(e,t?++t:1))}),u),l&&"ADX"===r.tt&&!r.e_skref&&!r.dynamicUpd&&("interstitial"!==r.ft||r.intl_spr)){let i=setTimeout((()=>{r.e_skref||pushAds(e,t||0,!0,a)}),1e3*r.tr);r.e_arftid=i}}))}}else r.rendered=!0;return r}function manageAdViaGam(e,t,i){let n=trAdsContent[t],r="";if(n.di?r=document.querySelector("#"+n.di):n.dcl&&n.dcli&&(r=document.getElementsByClassName(n.dcl)[n.dcli]),!r)return n.rendered=!0,n;{let t=r.ownerDocument.defaultView.frameElement,l=r.ownerDocument.defaultView.frameElement.parentElement.parentElement;"ad_style"==e&&(r.style.justifyContent="center",r.style.display="flex",r.style.alignItems="center",r.style.top="0",r.style.bottom="0",r.style.position="absolute","CLOSE"===n.acb||"RTCLOSE"===n.acb?r.style.left="0":"LFCLOSE"!==n.acb&&"LFTCLOSE"!==n.acb||(r.style.right="0",r.style.left="auto"),l.style.display="flex",l.style.margin="0 auto",l.style.overflow="unset",l.style.height="100%",l.style.margin="0px auto",l.style.width="100%",l.style.position="fixed",l.style.top="0",l.style.left="0",l.style["z-index"]="2147483647",l.style.background="#000000b3",l.style["justify-content"]="center",l.style["align-items"]="center",l.style.visibility="hidden"),"visible"==e&&("CLOSE"===n.acb||"LFCLOSE"===n.acb?t.width=Math.ceil(i.iframeWidth)+25:"LFTCLOSE"!==n.acb&&"RTCLOSE"!==n.acb||(t.width=Math.ceil(i.iframeWidth)+39),t.height=Math.ceil(i.iframeHeight),l.style.visibility="visible","14003da0-a253-11ef-8e5b-7dcdd86e328e"!=publisherId&&"55be3d61-a257-11ef-8e5b-17c0b482ee0a"!=publisherId||(t.removeAttribute("style"),t&&t.hasAttribute("style")&&t.removeAttribute("style"),setTimeout((()=>{t&&t.hasAttribute("style")&&t.removeAttribute("style")}),2e3))),"closeAd"==e&&(l.style.display="none",r.style.display="none")}return!0}function manageDelayedRender(){let e=0,t=setInterval((function(){let i=!0;trAdsContent.map(((e,t)=>{e.rendered||("true"===localStorage.getItem("truereachDebugMode")&&console.log("pushAds from manageDelayedRender called"),"multiplex_int"==e.ft?initMultiplexAds(t,""):pushAds(t,""),i=!1)})),i&&(clearInterval(t),"true"===localStorage.getItem("truereachDebugMode")&&console.log("manageDelayedRender cleared")),e++,e===maxRetries&&(delayedRenderIntervalFlag=!1,"true"===localStorage.getItem("truereachDebugMode")&&console.log("manageDelayedRender cleared"),clearInterval(t))}),delayerRenderInterval)}localStorage.getItem("truereachDebugMode")||localStorage.setItem("truereachDebugMode",!1);const trackElementActiveView=(e,t)=>{try{trAdsContent[t].e_inv||(trAdsContent[t].e_inv="OUT"),new IntersectionObserver((e=>{e.forEach((e=>{e.intersectionRatio<parseInt(trAdsContent[t].ivp)/100?(trAdsContent[t].e_inv="OUT","true"===localStorage.getItem("truereachDebugMode")&&console.log("out of view")):("true"===localStorage.getItem("truereachDebugMode")&&console.log("in view"),trAdsContent[t].e_inv="IN")}))}),{threshold:parseInt(trAdsContent[t].ivp)/100}).observe(e)}catch(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log(e)}},trackElementActiveViewWithMargin=(e,t)=>{try{trAdsContent[t].e_inv||(trAdsContent[t].e_inv="OUT");let{innerHeight:i,innerWidth:n}=window,r=i*trAdsContent[t].ivp/100;new IntersectionObserver((e=>{e.forEach((e=>{e.isIntersecting?("true"===localStorage.getItem("truereachDebugMode")&&console.log("in view"),trAdsContent[t].e_inv="IN"):(trAdsContent[t].e_inv="OUT","true"===localStorage.getItem("truereachDebugMode")&&console.log("out of view"))}))}),{rootMargin:parseInt(r)+"px 0px "+parseInt(r)+"px 0px"}).observe(e)}catch(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log(e)}},elementIsVisibleInViewport=(e,t)=>"IN"===t.e_inv;function elementIsVisibleInViewportInitial(e,t,i){try{let{top:n,left:r,bottom:l,right:a}=e.getBoundingClientRect(),{innerHeight:d,innerWidth:s}=window,o=!1;if(i.rmv){let e=d*i.ivp/100;o=t?(n>0&&n-parseInt(e)<d||l+parseInt(e)>0&&l<d)&&(r>0&&r<s||a>0&&a<s):n>=0&&r>=0&&l<=d&&a<=s}else{let g=e.style.height||"0px";g=g.replace("px","");let c=g*parseInt(i.ivp)/100;o=t?(n>0&&n<=d-c||l>=c&&l<d)&&(r>0&&r<s||a>0&&a<s):n>=0&&r>=0&&l<=d&&a<=s}return o}catch(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log(e)}}function restartDelayedRender(){"true"===localStorage.getItem("truereachDebugMode")&&console.log("before restartDelayedRender called"),delayedRenderIntervalFlag||(delayedRenderIntervalFlag=!0,manageDelayedRender(),"true"===localStorage.getItem("truereachDebugMode")&&console.log("after manageDelayedRender called"))}function trackElementViewability(e,t,i,n){let r=trAdsContent[e],l=setInterval((()=>{let a="";r.di?a=document.getElementById(r.di):r.dcl&&r.dcli&&(a=document.getElementsByClassName(r.dcl)[r.dcli]),!a||r.r_eiv&&!elementIsVisibleInViewport(a,r)&&r.r_div&&!documentInViewport||("true"===localStorage.getItem("truereachDebugMode")&&console.log("trackElementViewability cleared"),clearInterval(l),"multiplex_int"==r.ft?initMultiplexAds(e,""):pushAds(e,t,i,n))}),trackElemViewabilityInterval)}function closeAd(e,t,i){return t.di&&(1==t.vgm&&"interstitial"==t.ft?manageAdViaGam("closeAd",i):(document.getElementById(t.di).style.display="none","sticky"!==t.ft&&"sticky-scroll"!==t.ft||(t.e_skref=!0))),t.dcl&&(1==t.vgm&&"interstitial"==t.ft?manageAdViaGam("closeAd",i):Array.from(document.getElementsByClassName(t.dcl)).map(((e,i)=>{if(i!=t.dcli)return!0;e.style.display="none","sticky"!==t.ft&&"sticky-scroll"!==t.ft||(t.e_skref=!0)}))),!0}function getDeviceType(){let e;return e=navigator.userAgent.match(/Android/i)||navigator.userAgent.match(/webOS/i)||navigator.userAgent.match(/iPhone/i)||navigator.userAgent.match(/iPad/i)||navigator.userAgent.match(/iPod/i)||navigator.userAgent.match(/BlackBerry/i)||navigator.userAgent.match(/Windows Phone/i)?"MOBILE":"DESKTOP",e}function manageFallbackADX(e,t){let i=trAdsContent[e];!!i.r||(i.isEmpty=!0,pushAds(e,t))}function getCurrentPageUrl(){try{let e=window.context.location.href;return"d3d258cb-0c66-11ef-94e2-b5edc72792e6"==publisherId&&e.includes("/amp")&&(e=e.replace("/amp","")),e}catch(e){return window.location.href}}function hasSpecificChildIds(e,t){return Array.from(e.children).some((e=>t.includes(e.id)))}function specificChildNode(e,t){return Array.from(e.children).find((e=>t.includes(e.id)))}function manageGPTTag(e,t){let i=trAdsContent[t];if(i.pt&&(poweredByTimer=1e3*i.pt),"interstitial"==i.ft&&!i.dynamicUpd&&!i.intl_onact&&!1===e.isEmpty)try{setTimeout((()=>{if(1==i.vgm){let e="";Array.from(document.getElementsByClassName("del-"+i.guid)).length&&(e=Array.from(document.getElementsByClassName("del-"+i.guid))[0]),viaGamDataArr={iframeHeight:e.querySelectorAll("iframe")[0]?.height,iframeWidth:e.querySelectorAll("iframe")[0]?.width},manageAdViaGam("visible",t,viaGamDataArr)}else{let e="";i.di?e=document.getElementById(i.di):i.dcl&&i.dcli&&(e=document.getElementsByClassName(i.dcl)[i.dcli]),e.style.visibility="visible"}}),poweredByTimer)}catch(e){console.log(e)}if("interstitial"!=i.ft&&1!=i.dynamicUpd||i.intl_onact||!1===e.isEmpty&&(i.arps&&1==i.arps?sessionStorage.getItem("tr-arps")||(sessionStorage.getItem("tr-arpsCnt")?sessionStorage.setItem("tr-arpsCnt",parseInt(sessionStorage.getItem("tr-arpsCnt"))-1):sessionStorage.setItem("tr-arpsCnt",parseInt(i.arpsc)-1),0==parseInt(sessionStorage.getItem("tr-arpsCnt"))&&sessionStorage.setItem("tr-arps",!0)):(arpsExpiration=i.arpset&&""!=i.arpset?Date.now()+60*parseInt(i.arpset)*1e3:Date.now()+60*defaultArpsExpTime*1e3,localStorage.getItem("tr-arpsExp")||localStorage.setItem("tr-arpsExp",arpsExpiration),localStorage.getItem("tr-arps")||(localStorage.getItem("tr-arpsCnt")?localStorage.setItem("tr-arpsCnt",parseInt(localStorage.getItem("tr-arpsCnt"))-1):localStorage.setItem("tr-arpsCnt",parseInt(i.arpsc)-1),0==parseInt(localStorage.getItem("tr-arpsCnt"))&&localStorage.setItem("tr-arps",!0)))),"c36e1701-3774-11ee-beef-21e73d0cd59b"==publisherId){i.di?clientDivElement=document.getElementById(i.di):i.dcl&&i.dcli&&(clientDivElement=document.getElementsByClassName(i.dcl)[i.dcli]);let t=clientDivElement.parentElement.querySelectorAll("div[id^='div-gpt-ad-1616']")[0];!1===e.isEmpty?setTimeout((()=>{t.style.display="none",clientDivElement.style.display="flex"}),1e3):i.e_iv?(t.style.display="none",clientDivElement.style.display="flex"):t.style.display="block"}}function manageSlotEmpty(e,t){let i=trAdsContent[t];return null!=i.vgc&&""!=i.vgc?i.isEmpty=!0:2===Array.from(document.getElementsByClassName("del-"+i.guid)).length?!0===e.isEmpty?(Array.from(document.getElementsByClassName("del-"+i.guid))[1].remove(),i.e_arftid&&i.ufrt&&!i.e_prev_tr&&manageRefreshCalling("unfill",t)):(Array.from(document.getElementsByClassName("del-"+i.guid))[0].remove(),i.isEmpty=!1,i.e_arftid&&""!=i.e_prev_tr&&i.ufrt&&manageRefreshCalling("fill",t)):!1===e.isEmpty?(i.e_resp_recv_onc=!0,i.e_prev_tr=""):(i.isEmpty=!0,i.e_arftid&&!i.e_prev_tr&&i.ufrt&&manageRefreshCalling("unfill",t)),manageInterscroller(i),!0}function manageRefreshCalling(e,t){let i=trAdsContent[t];if("fill"==e){clearTimeout(i.e_arftid),i.e_arftid="",i.tr=i.e_prev_tr,i.e_prev_tr="";let e=setTimeout((()=>{i.e_skref||pushAds(t,"",!0,i.e_prev_rid)}),1e3*i.tr);i.e_arftid=e}else{if("unfill"!=e)return!1;{clearTimeout(i.e_arftid),i.e_arftid="",i.e_prev_tr=i.tr,i.tr=i.ufrt;let e=setTimeout((()=>{i.e_skref||pushAds(t,"",!0,i.e_prev_rid)}),1e3*i.tr);i.e_arftid=e}}}function manageInterscroller(e){let t=0,i=0,n=0,r="",l="";if("interscroller"===e.ft){e.di?(r=document.querySelector("#"+e.di+" .wrapper_in"),l=document.querySelector("#"+e.di+" .bg_in")):e.dcl&&e.dcli&&(r=document.querySelectorAll("."+e.dcl)[e.dcli].querySelector(".wrapper_in"),l=document.querySelectorAll("."+e.dcl)[e.dcli].querySelector(".bg_in")),t=window.scrollY||document.documentElement.scrollTop,n=r.getBoundingClientRect().top+20,n>0&&(l.style.top=n+"px",l.style.alignItems="flex-start",document.addEventListener("scroll",(function e(){i=window.scrollY||document.documentElement.scrollTop,i>t&&(l.style.top="0px",l.style.alignItems="center",document.removeEventListener("scroll",e))})))}return!0}function manageAdRendered(e,t){try{let i=trAdsContent[t];if(i.isEmpty=!1,"sticky-scroll"!==i.ft&&"interscroller"!==i.ft&&"VignetteAds"!=i.ft&&"RewardedAds"!=i.ft){let t=Array.from(document.getElementsByClassName("del-"+i.guid))[0];if(i.hb&&""!=i.hb)if(1!=e.size[1]&&1!=e.size[0])t.style.height=e.size[1]+"px",t.style.width=e.size[0]+"px";else try{let i=e.slot.getTargetingMap();i&&i.hb_size&&(t.style.height=i.hb_size[1]+"px",t.style.width=i.hb_size[0]+"px")}catch(i){t.style.height=e.size[1]+"px",t.style.width=e.size[0]+"px"}else t.style.height=e.size[1]+"px",t.style.width=e.size[0]+"px"}"interstitial"===i.ft&&i.intl_onact&&manageInterstitialOnScroll(e,t);let n="";if(i.di?n=document.getElementById(i.di):i.dcl&&i.dcli&&(n=document.getElementsByClassName(i.dcl)[i.dcli]),"interstitial"!==i.ft&&"VignetteAds"!=i.ft&&"RewardedAds"!=i.ft&&(n.style.width=e.size[0]+"px"),"interstitial"===i.ft){if(1!=i.vgm){let n={acb:i.acb};e.size[1]>=600&&(n.acb="CORNER"),manageInterstitialBtn(n,document.getElementById("button-"+i.e_prev_rid+t))}if(null!=i.gmc&&""!=i.gmc){if(i.gmc.split(",").includes(e.creativeId+""));else if(n.style.position="fixed",n.style.background="#000000e6",document.getElementById("button-"+t).style.visibility="visible",i.pbl||i.pbt||i.pbi){let e=!1,t=setInterval((function(){document.querySelector('[tr-id="'+i.guid+'"] .powered_ads')&&(e||(clearInterval(t),e=!0),document.querySelector('[tr-id="'+i.guid+'"] .powered_ads').style.visibility="visible")}),1e3)}}}if("interscroller"===i.ft){const t=i.as?.split("*");document.querySelector("div.wrapper_in.del-"+i.guid+" > div").style.clip="rect(0px, "+e.size[0]+"px, "+t[1]+"px, 0px)",i.ibp&&i.ibp?.split("*")[1]>e.size[1]&&(document.querySelector("div.wrapper_in.del-"+i.guid+" > div > div").style.position="unset",document.querySelector("div.wrapper_in.del-"+i.guid+" > div").style.clip="unset",document.querySelector("div.wrapper_in.del-"+i.guid+" > div").style.marginTop="unset",document.querySelector("div.wrapper_in.del-"+i.guid).style.height=e.size[1]+"px")}return!0}catch(e){console.log(e)}}function manageAdRenderedEmpty(e,t){try{let i=trAdsContent[t];return i.isEmpty=!0,"interstitial"===i.ft&&i.intl_onact&&manageInterstitialOnScrollEmpty(e,t),!0}catch(e){console.log(e)}}function manageRefreshAdRendered(e,t){try{let i=trAdsContent[t];if("true"===localStorage.getItem("truereachDebugMode")&&console.log("event - ",e),"interscroller"===i.ft&&!1===e.isEmpty){let t="";i.di?t=document.getElementById(i.di):i.dcl&&i.dcli&&(t=document.getElementsByClassName(i.dcl)[i.dcli]),t.style.width=e.size[0]+"px";const n=i.as?.split("*");document.querySelector("div.wrapper_in.del-"+i.guid+" > div").style.clip="rect(0px, "+e.size[0]+"px, "+n[1]+"px, 0px)",i.ibp&&i.ibp?.split("*")[1]>e.size[1]&&(document.querySelector("div.wrapper_in.del-"+i.guid+" > div > div").style.position="unset",document.querySelector("div.wrapper_in.del-"+i.guid+" > div").style.clip="unset",document.querySelector("div.wrapper_in.del-"+i.guid+" > div").style.marginTop="unset",document.querySelector("div.wrapper_in.del-"+i.guid).style.height=e.size[1]+"px")}if(!1===e.isEmpty&&"interstitial"===i.ft&&1!=i.vgm){let n={acb:i.acb};e.size[1]>=600&&(n.acb="CORNER"),manageInterstitialBtn(n,document.getElementById("button-"+i.e_prev_rid+t))}if(!1===e.isEmpty&&"sticky-scroll"!==i.ft&&"interscroller"!==i.ft){let t=Array.from(document.getElementsByClassName("del-"+i.guid))[0];if(i.hb&&""!=i.hb)if(1!=e.size[1]&&1!=e.size[0])t.style.height=e.size[1]+"px",t.style.width=e.size[0]+"px";else try{let i=e.slot.getTargetingMap();i&&i.hb_size&&(t.style.height=i.hb_size[1]+"px",t.style.width=i.hb_size[0]+"px")}catch(i){t.style.height=e.size[1]+"px",t.style.width=e.size[0]+"px"}else"VignetteAds"!=i.ft&&"RewardedAds"!=i.ft&&(t.style.height=e.size[1]+"px",t.style.width=e.size[0]+"px")}return!0}catch(e){console.log(e)}}function manageInterstitialOnScroll(e,t){let i;manageInterstitialOnAction(e,t),window.addEventListener("scroll",(function(){clearTimeout(i),i=setTimeout((()=>{manageInterstitialOnAction(e,t)}),250)}))}function manageInterstitialOnScrollEmpty(e,t){let i;manageInterstitialOnActionEmpty(e,t),window.addEventListener("scroll",(function(){clearTimeout(i),i=setTimeout((()=>{manageInterstitialOnActionEmpty(e,t)}),250)}))}function manageInterstitialOnAction(e,t){try{let e=trAdsContent[t];return document.querySelectorAll("a:not([data-tr-bound])").forEach((i=>{let n=i.getAttribute("href");if(n&&"javascript:void(0);"!==n&&"#"!==n&&0!==n.trim().length){let n=i;i.classList.contains("oiHyperLink")&&(n=i.cloneNode(!0),i.parentNode.replaceChild(n,i)),n.setAttribute("data-tr-bound",!0),n.addEventListener("click",(function(i){if(preventIntDefaultFlag&&(i.preventDefault(),!e.e_intl_onact_disp)){e.e_intl_onact_disp=!0;let r="";e.di?r=document.getElementById(e.di):e.dcl&&e.dcli&&(r=document.getElementsByClassName(e.dcl)[e.dcli]),r.style.visibility="visible","7ca85928-f5eb-11ec-83bf-afb6a6e8a7b0"==publisherId&&document.getElementById("aniBox")&&(document.getElementById("aniBox").style.display="none"),document.getElementById("button-"+t).style.visibility="visible",document.getElementById("button-"+t).addEventListener("click",(function(){e.arps&&1==e.arps?sessionStorage.getItem("tr-arps")||(sessionStorage.getItem("tr-arpsCnt")?sessionStorage.setItem("tr-arpsCnt",parseInt(sessionStorage.getItem("tr-arpsCnt"))-1):sessionStorage.setItem("tr-arpsCnt",parseInt(e.arpsc)-1),0==parseInt(sessionStorage.getItem("tr-arpsCnt"))&&sessionStorage.setItem("tr-arps",!0)):localStorage.getItem("tr-arps")||(localStorage.getItem("tr-arpsCnt")?localStorage.setItem("tr-arpsCnt",parseInt(localStorage.getItem("tr-arpsCnt"))-1):localStorage.setItem("tr-arpsCnt",parseInt(e.arpsc)-1),0==parseInt(localStorage.getItem("tr-arpsCnt"))&&localStorage.setItem("tr-arps",!0)),n.href&&(n.classList.contains("oiHyperLink")&&(n.href=n.href+"?"+n.getAttribute("data-uparam")),n.target?window.open(n.href,n.target):window.open(n.href,"_self")),preventIntDefaultFlag=!1})),handleExtTriggerIntlAction(i,t)}}))}})),!0}catch(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log(e)}}function manageInterstitialOnActionEmpty(e,t){try{trAdsContent[t];return document.querySelectorAll("a:not([data-tr-bound])").forEach((e=>{let i=e.getAttribute("href");if(i&&"javascript:void(0);"!==i&&"#"!==i&&0!==i.trim().length){let i=e;e.classList.contains("oiHyperLink")&&(i=e.cloneNode(!0),e.parentNode.replaceChild(i,e)),i.setAttribute("data-tr-bound",!0),i.addEventListener("click",(function(e){preventIntDefaultFlag&&(e.preventDefault(),i.href&&(i.target?window.open(i.href,i.target):window.open(i.href,"_self")),handleExtTriggerIntlAction(e,t),preventIntDefaultFlag=!1)}))}})),!0}catch(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log(e)}}function manageStickyScroll(e,t){try{e.e_lst_st=0,window.addEventListener("scroll",(function(){var t=window.scrollY||document.documentElement.scrollTop;t>e.e_lst_st?Array.from(document.getElementsByClassName("del-"+e.guid))[0].scrollBy(0,1):t<e.e_lst_st&&Array.from(document.getElementsByClassName("del-"+e.guid))[0].scrollBy(0,-1),e.e_lst_st=t<=0?0:t}),!1)}catch(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log(e)}return!0}function handleExtTrigger(e,t){try{let e=trAdsContent[t];if("interstitial"!==e.ft&&!e.intl_onact&&"object"==typeof e.et&&e.et.length>0){let t=(new Date).getTime();e.et.map(((e,i)=>{let n=new Image;e=e.indexOf("?")>-1?e+"&"+t:e+"?"+t,n.src=e}))}return!0}catch(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log(e)}}function handleExtTriggerRequest(e,t){try{let e=trAdsContent[t];if("object"==typeof e.ert&&e.ert.length>0){let t=(new Date).getTime();e.ert.map(((e,i)=>{let n=new Image;e=e.indexOf("?")>-1?e+"&"+t:e+"?"+t,n.src=e}))}return!0}catch(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log(e)}}function handleExtTriggerIntlAction(e,t){try{let e=trAdsContent[t];if("object"==typeof e.et&&e.et.length>0){let t=(new Date).getTime();e.et.map(((e,i)=>{let n=new Image;e=e.indexOf("?")>-1?e+"&"+t:e+"?"+t,n.src=e}))}return!0}catch(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log(e)}}function truereachLog(e){window.location.href;if(logEnable){let t=new Image,i=(new Date).getTime();t.src=truereachLogURL+(e?"?"+e+"&dt="+i:"?dt="+i)}}window.onload=fetchPubAdData();let scrolling=!1;function getInterstitialAdIndex(){let e="";return trAdsContent.map(((t,i)=>{"interstitial"===t.ft&&(e=!t.r&&t.ncnt?"":i)})),e}function refreshTruereachAd(){adNextTimerId&&clearTimeout(adNextTimerId),adNextTimerId=setTimeout((function(){trAdsContent.map(((e,t)=>{e.uac&&(e.e_arftid&&(clearTimeout(e.e_arftid),e.e_arftid=""),e.e_cbh||2!=e.uac?"multiplex_int"==e.ft?e.ismultiAdStart?console.log("already started"):(e.adTimerId&&(clearTimeout(e.adTimerId),e.adTimerId=""),e.priAdrCnt=e.pri_adr,e.multiAdrCnt=e.multi_adr,initMultiplexAds(t,!0)):pushAds(t,"",!0,e.e_prev_rid):(e.e_cbh=!0,"multiplex_int"==e.ft?(e.adTimerId&&(clearTimeout(e.adTimerId),e.adTimerId=""),initMultiplexAds(t,"")):pushAds(t,"",!1,e.e_prev_rid)))}))}),callbackRefreshTime)}function refreshTruereachTargetAd(e){if(!e)return!1;{let t=trAdsContent.findIndex((t=>t.di===e)),i=trAdsContent[t];i.uac&&(i.e_arftid&&(clearTimeout(i.e_arftid),i.e_arftid=""),i.e_cbh||2!=i.uac?"multiplex_int"==i.ft?i.ismultiAdStart?console.log("already started"):(i.adTimerId&&(clearTimeout(i.adTimerId),i.adTimerId=""),i.priAdrCnt=i.pri_adr,i.multiAdrCnt=i.multi_adr,initMultiplexAds(index,!0)):pushAds(t,"",!0,i.e_prev_rid):(i.e_cbh=!0,"multiplex_int"==i.ft?(i.adTimerId&&(clearTimeout(i.adTimerId),i.adTimerId=""),initMultiplexAds(t,"")):pushAds(t,"",!1,i.e_prev_rid)))}}function truereachRenderAd(e){if(!e)return!1;{let t=trAdsContent.findIndex((t=>t.di===e)),i=trAdsContent[t];i.uac&&(i.e_arftid&&(clearTimeout(i.e_arftid),i.e_arftid=""),i.e_cbh||2!=i.uac?"multiplex_int"==i.ft?i.ismultiAdStart?console.log("already started"):(i.adTimerId&&(clearTimeout(i.adTimerId),i.adTimerId=""),i.priAdrCnt=i.pri_adr,i.multiAdrCnt=i.multi_adr,initMultiplexAds(index,!0)):pushAds(t,"",!0,i.e_prev_rid):(i.e_cbh=!0,"multiplex_int"==i.ft?(i.adTimerId&&(clearTimeout(i.adTimerId),i.adTimerId=""),i.priAdrCnt=i.pri_adr,i.multiAdrCnt=i.multi_adr,i.ismultiAdStart=!1,initMultiplexAds(t,"")):pushAds(t,"",!1,i.e_prev_rid)))}}function showTruereachAd(e){let t;return trAdsContent.map(((i,n)=>{if("interstitial"===i.ft&&i.intl_onact&&i.rendered&&!i.isEmpty){let r="";i.di?r=document.getElementById(i.di):i.dcl&&i.dcli&&(r=document.getElementsByClassName(i.dcl)[i.dcli]),r&&(r.style.visibility="visible",document.getElementById("button-"+n).style.visibility="visible",t||(t=!0),document.getElementById("button-"+n).addEventListener("click",(function(){i.arps&&1==i.arps?sessionStorage.getItem("tr-arps")||sessionStorage.setItem("tr-arps",!0):localStorage.getItem("tr-arps")||localStorage.setItem("tr-arps",!0),e?window.open(e,"_self"):window.open(window.location.origin,"_self")})))}})),!!t}function getNearbySize(e,t){const i=getDeviceType();var n=e,r=t,l=trSizeMapping[i].sizes,a=[];return l.forEach((function(e){var t=e[0],i=e[1],l=Math.abs(n-t),d=Math.abs(r-i);l>=-40&&l<=40&&d>=0&&i<=r&&a.push(e)})),a}function initMultiplexAds(e,t=!1){let i=trAdsContent[e];if(i.max_height=600,i.adTimerId="",i.nearbySize="",i.targetWidth="",i.targetHeight="",!i.e_cbh&&"2"==i.uac)return i;if("true"===localStorage.getItem("tr-vignette")&&"multiplex_int"==i.ft&&"1"==i.bint)return i.rendered=!0,localStorage.setItem("tr-vignette",!1),i;if(i.arps&&1==i.arps&&"multiplex_int"==i.ft){if(sessionStorage.getItem("tr-arps")&&"true"===sessionStorage.getItem("tr-arps"))return i}else if(i.arps&&2==i.arps&&"multiplex_int"==i.ft&&(checkArpsExpiration(),localStorage.getItem("tr-arps")&&"true"===localStorage.getItem("tr-arps")))return i;if(i.hmt){"string"==typeof i.mta&&(i.mta=JSON.parse(i.mta));let t=Math.floor(Math.random()*trAdsContent[e].mta.length);null==i.e_multadindx?i.e_multadindx=t:(i.e_multadindx++,i.e_multadindx==i.mta.length&&(i.e_multadindx=0)),i.trht=i.mta[i.e_multadindx].trht,i.trbd=i.mta[i.e_multadindx].trbd,i.gid=i.mta[i.e_multadindx].gid}i.ismultiAdStart=!!i.ismultiAdStart;const n=t?(new Date).getTime():"";i.e_prev_rid=n;const r=updateUrlList(getCurrentPageUrl(),!1);if("f8ed7706-55f3-11ee-a69e-5d852a779460"!=publisherId&&(setPubAdsUrl=i.cur_pgurl&&"1"==i.cur_pgurl?"":'googletag.pubads().set("page_url","'+r+'");'),i.pu.length&&"object"==typeof i.pu?i.pu=updateUrlList(i.pu,!0):i.pu=updateUrlList(i.pu,!1),i.epu.length&&"object"==typeof i.epu?i.epu=updateUrlList(i.epu,!0):"1"==i.isepu&&exclusionUrlList.length&&"object"==typeof exclusionUrlList?i.epu=updateUrlList(exclusionUrlList,!0):i.epu=updateUrlList(i.epu,!1),"string"==typeof i.pu&&i.pu&&r!==i.pu||"object"==typeof i.pu&&i.pu.length&&!i.pu.some((e=>r.includes(e)))||"string"==typeof i.epu&&i.epu&&r===i.epu||"object"==typeof i.epu&&i.epu.length&&i.epu.some((e=>r.includes(e))))i.rendered=!0;else{let r="";if(i.di){if(r=document.getElementById(i.di),!r)return i;documentInViewport&&truereachLog(t?"divId-found-refresh&id="+i.sv:"divId-found&id="+i.sv)}else if(i.dcl&&i.dcli){if(r=document.getElementsByClassName(i.dcl)[i.dcli],!r)return i;truereachLog(t?"dcl-found-refresh&id="+i.sv:"dcl-found&id="+i.sv)}if(i.e_inv||!i.e_iv&&!i.r_eiv||(i.rmv?trackElementActiveViewWithMargin(r,e):trackElementActiveView(r,e),i.e_inv=elementIsVisibleInViewportInitial(r,!0,i)?"IN":"OUT"),t){if(i.r_eiv&&!elementIsVisibleInViewport(r,i)||i.r_div&&!documentInViewport)return i.rendered&&trackElementViewability(e,0,!0,n),i}else if(i.e_iv&&!elementIsVisibleInViewport(r,i)||i.d_iv&&!documentInViewport)return i;if(r.setAttribute("tr-id",i.guid),i.trht){const r=document.createElement("div");let l=i.trht;t||(l=l.replace("googletag.enableServices()",setPubAdsUrl+'; googletag.enableServices(); googletag.pubads().addEventListener("slotRequested", function (event) {localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+", event); if (window.SLOT_VARIABLE === event.slot) { handleExtTriggerRequest(event, "+e+');}}); googletag.pubads().addEventListener("impressionViewable", function (event) { localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+", event); if(window.SLOT_VARIABLE === event.slot) {handleExtTrigger(event, "+e+');}}); googletag.pubads().addEventListener("slotRenderEnded", function (event) { if(window.SLOT_VARIABLE === event.slot) { manageMultiplexAds(event, '+e+", true) }});")),"ADX"===i.tt&&(l=l.replaceAll("DIV_GPT_ID",t?i.gid+n:i.gid),l=l.replaceAll("SLOT_VARIABLE",i.sv)),r.innerHTML=l;let a=r.getElementsByTagName("script");Array.from(a).map(((e,t)=>{let{src:n,id:r,type:l}=e,a=e.text,d=e.crossOrigin,s=e.async;if(!addedScriptsArr.includes(n)){const e=document.createElement("script");if(n&&(e.src=n,addedScriptsArr.push(n)),r&&(e.id=r),l&&(e.type=l),a&&(e.text=a),d&&(e.crossOrigin=d),s&&(e.async=s),"ADX"===i.tt&&e.setAttribute("data-tr",i.gid),"ADX"===i.tt){let e=i.gid;Array.from(document.querySelectorAll("[data-tr="+e+"]")).map((e=>{e.remove()}))}document.head.appendChild(e),finalHeadScript=e}}))}if(i.trbd){let r;const l=document.createElement("div");let a=i.trbd,d=!(!i.hb||""==i.hb);"true"===localStorage.getItem("truereachDebugMode")&&console.log("headerbidding setting:"+d),"ADX"===i.tt&&1!=d&&(a=a.replace("googletag.display('DIV_GPT_ID');","googletag.display('DIV_GPT_ID'); window.googletag.pubads().isInitialLoadDisabled() && window.googletag.pubads().refresh(["+i.sv+"])"),a=a.replace("googletag.display(window.SLOT_VARIABLE);","googletag.display(window.SLOT_VARIABLE); window.googletag.pubads().isInitialLoadDisabled() && window.googletag.pubads().refresh([window.SLOT_VARIABLE])")),"ADX"===i.tt&&(a=a.replaceAll("DIV_GPT_ID",t?i.gid+n:i.gid),a=a.replaceAll("SLOT_VARIABLE",i.sv)),l.innerHTML=a;const s=document.createElement("div");s.style="bottom: 0; position: relative;";const o=document.createElement("button");o.id="button-"+n+e,o.style.position="absolute",o.style.right="0",o.style.width="44px",o.style.height="27px",o.style.padding="0 1px 0 2px",o.style.visibility="hidden",o.style.background="#f5f5f5",o.style.color="#333",o.style.border="1px solid #333",o.style.cursor="pointer","CROSS"===i.acb?(o.innerHTML="X",o.style.width="32px",o.style.height="19px",o.style.border="0px",o.style["font-weight"]="bold",o.style.display="flex",o.style["align-items"]="center",o.style["justify-content"]="center",o.style["z-index"]="9999999",o.style["font-size"]="13px"):"CLOSE"===i.acb||"LFCLOSE"===i.acb?(o.innerHTML="x",o.style["z-index"]="9999999",o.style["font-size"]="13px"):(o.style["border-radius"]="50px",o.innerHTML="SKIP",o.style["z-index"]="9999999",o.style["font-size"]="13px"),manageInterstitialBtn(i,o),s.appendChild(o),setTimeout((function(){document.getElementById("button-"+n+e).addEventListener("click",(function(t){closeAd(s,i,e)}))}),0);let g=l.getElementsByTagName("script")[0],{src:c,id:u,type:p}=g,m=g.getAttribute("data-width"),f=g.getAttribute("data-height"),y=g.text;const h=document.createElement("script");g&&(c&&(h.src=c),u&&(h.id=u),p&&(h.type=p),m&&h.setAttribute("data-width",m),f&&h.setAttribute("data-height",f),y&&(h.text=y));let b=l.getElementsByTagName("div")[0],v=document.createElement("div");if(v.id="multiDiv-"+i.guid,v.style.border="1px solid #fff",v.style.background="white",v.style.display="flex",v.style.flexFlow="column",v.style.alignItems="center",b){let e=b.id,t=b.getAttribute("style");const i=document.createElement("div");e&&(i.id=e),t&&(i.style=t),h&&i.appendChild(h),r&&i.appendChild(r),v.appendChild(i),s.appendChild(v)}if(s.classList=s.classList.length?s.classList.value+" del-"+i.guid:"del-"+i.guid,i.di){let n=document.getElementById(i.di);if(t&&"ADX"===i.tt&&Array.from(document.getElementsByClassName("del-"+i.guid)).map(((e,t)=>{e.style.display="none"})),"ADX"===i.tt){if(1==i.vgm&&"multiplex_int"==i.ft?manageAdViaGam("ad_style",e):(n.style.display="flex",n.style.margin="0 auto",n.style.overflow="unset",n.style.height="100%",n.style.margin="0px auto",n.style.width="100%",n.style.position="fixed",n.style.background="#000000e6",n.style.top="0",n.style.left="0",n.style["z-index"]="99999999999999",n.style["justify-content"]="center",n.style["align-items"]="center",n.style.visibility="hidden"),i.ws){const e=document.createElement("style");e.classList="del-style-"+i.guid,e.innerHTML=i.ws,n.appendChild(e)}n.style["justify-content"]="center",n?.appendChild(s)}i.rendered=!0}i.dcl&&(Array.from(document.getElementsByClassName(i.dcl)).map(((t,n)=>{if(n!=i.dcli)return!0;let r=s.cloneNode(!0);if("ADX"===i.tt&&Array.from(document.getElementsByClassName("del-"+i.guid)).map(((e,t)=>{e.style.display="none"})),i.ws){const e=document.createElement("style");e.classList="del-style-"+i.guid,e.innerHTML=i.ws,t.appendChild(e)}t.appendChild(r),i.rendered=!0,1==i.vgm&&"multiplex_int"==i.ft?manageAdViaGam("ad_style",e):(t.style.display="flex",t.style.margin="0 auto",t.style.overflow="unset",t.style.height="100%",t.style.margin="0px auto",t.style.width="100%",t.style.position="fixed",t.style.top="0",t.style.left="0",t.style["z-index"]="2147483647",t.style.background="#000000b3",t.style["justify-content"]="center",t.style["align-items"]="center",t.style.visibility="hidden"),t.style["justify-content"]="center"})),i.rendered=!0)}}return i}function manageMultiplexAds(e,t,i){let n=trAdsContent[t];if(null==n.isEmpty&&(n.isEmpty=e.isEmpty),"true"===localStorage.getItem("truereachDebugMode")&&console.log(parseInt(n.priAdrCnt),parseInt(n.multiAdrCnt),n.max_height),e.isEmpty)if(i)"true"===localStorage.getItem("truereachDebugMode")&&console.log(" + adIndex + ",e),n.priAdrCnt&&0!=parseInt(n.priAdrCnt)&&(n.priAdrCnt=parseInt(n.priAdrCnt)-1,0!=Array.from(document.getElementsByClassName("del-"+n.guid)).length&&(Array.from(document.getElementsByClassName("del-"+n.guid))[0].remove(),n.adTimerId&&clearTimeout(n.adTimerId),n.adTimerId=setTimeout((()=>{initMultiplexAds(t,!0)}),multiAdRetryTimer)));else if(n.multiAdrCnt&&0!=parseInt(n.multiAdrCnt)){n.multiAdrCnt=parseInt(n.multiAdrCnt)-1;let e=n.targetWidth;n.targetHeight;if(n.max_height=n.max_height,!(n.max_height>=50))return n.ismultiAdStart=!1,!1;n.nearbySize=getNearbySize(e,n.max_height),n.nearbySize.length>0?setTimeout((()=>{pushMultiAds(t)}),multiAdRetryTimer):(n.ismultiAdStart=!1,showMultiplexAd(t))}else n.ismultiAdStart=!1,showMultiplexAd(t);else n.targetWidth=e.size[0],n.targetHeight=e.size[1],n.max_height=n.max_height-n.targetHeight,n.multiAdrCnt&&0==parseInt(n.multiAdrCnt)?(n.ismultiAdStart=!1,showMultiplexAd(t)):(i||(n.max_height=n.max_height-multiAdMarginTop),n.max_height>=50?(n.nearbySize=getNearbySize(n.targetWidth,n.max_height),n.nearbySize.length>0?(n.multiAdrCnt=parseInt(n.multi_adr),setTimeout((()=>{pushMultiAds(t)}),multiAdRetryTimer)):(n.ismultiAdStart=!1,showMultiplexAd(t))):(n.ismultiAdStart=!1,showMultiplexAd(t)))}function pushMultiAds(e){let t=trAdsContent[e];nearbySize=JSON.stringify(t.nearbySize);const i=(new Date).getTime();if(t.hmt){"string"==typeof t.mta&&(t.mta=JSON.parse(t.mta));let i=Math.floor(Math.random()*trAdsContent[e].mta.length);null==t.e_multadindx?t.e_multadindx=i:(t.e_multadindx++,t.e_multadindx==t.mta.length&&(t.e_multadindx=0)),t.trht=t.mta[t.e_multadindx].trht,t.trbd=t.mta[t.e_multadindx].trbd,t.gid=t.mta[t.e_multadindx].gid}const n=document.createElement("div");let r=t.trht;const l=updateUrlList(getCurrentPageUrl(),!1);"f8ed7706-55f3-11ee-a69e-5d852a779460"!=publisherId&&(setPubAdsUrl=t.cur_pgurl&&"1"==t.cur_pgurl?"":'googletag.pubads().set("page_url","'+l+'");'),r=r.replace(t.madSize,nearbySize),r=r.replace("googletag.enableServices()",setPubAdsUrl+'; googletag.enableServices(); googletag.pubads().addEventListener("slotRequested", function (event) {localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+", event); if (window.SLOT_VARIABLE === event.slot) { handleExtTriggerRequest(event, "+e+');}}); googletag.pubads().addEventListener("impressionViewable", function (event) { localStorage.getItem("truereachDebugMode") === "true" && console.log('+e+", event); if(window.SLOT_VARIABLE === event.slot) {handleExtTrigger(event, "+e+');}});googletag.pubads().addEventListener("slotRenderEnded", function (event) { if(window.SLOT_VARIABLE === event.slot) { manageMultiplexAds(event, '+e+", false) }});"),"ADX"===t.tt&&(r=r.replaceAll("DIV_GPT_ID",t.gid+"-"+i),r=r.replaceAll("SLOT_VARIABLE",t.sv+i)),n.innerHTML=r;let a=n.getElementsByTagName("script");if(Array.from(a).map(((e,n)=>{let{src:r,id:l,type:a}=e,d=e.text,s=e.crossOrigin,o=e.async;if(!addedScriptsArr.includes(r)){const e=document.createElement("script");if(r&&(e.src=r,addedScriptsArr.push(r)),l&&(e.id=l),a&&(e.type=a),d&&(e.text=d),s&&(e.crossOrigin=s),o&&(e.async=o),"ADX"===t.tt){e.setAttribute("data-tr",t.gid+"-"+i);let n=t.gid;Array.from(document.querySelectorAll("[data-tr="+n+"-"+i+"]")).map((e=>{e.remove()}))}document.head.appendChild(e),finalHeadScript=e}})),t.trbd){let e;const n=document.createElement("div");let r=t.trbd,l=!(!t.hb||""==t.hb);"true"===localStorage.getItem("truereachDebugMode")&&console.log("headerbidding setting:"+l),"ADX"===t.tt&&1!=l&&(r=r.replace("googletag.display('DIV_GPT_ID');","googletag.display('DIV_GPT_ID'); window.googletag.pubads().isInitialLoadDisabled() && window.googletag.pubads().refresh(["+t.sv+i+"])"),r=r.replace("googletag.display(window.SLOT_VARIABLE);","googletag.display(window.SLOT_VARIABLE); window.googletag.pubads().isInitialLoadDisabled() && window.googletag.pubads().refresh([window.SLOT_VARIABLE])")),"ADX"===t.tt&&(r=r.replaceAll("DIV_GPT_ID",t.gid+"-"+i),r=r.replaceAll("SLOT_VARIABLE",t.sv+i)),n.innerHTML=r;let a=n.getElementsByTagName("script")[0],{src:d,id:s,type:o}=a,g=a.getAttribute("data-width"),c=a.getAttribute("data-height"),u=a.text;const p=document.createElement("script");a&&(d&&(p.src=d),s&&(p.id=s),o&&(p.type=o),g&&p.setAttribute("data-width",g),c&&p.setAttribute("data-height",c),u&&(p.text=u));let m=n.getElementsByTagName("div")[0],f=document.getElementById("multiDiv-"+t.guid);if(m){let t=m.id,i=m.getAttribute("style");const n=document.createElement("div");t&&(n.id=t),i&&(n.style=i,n.style.marginTop=multiAdMarginTop+"px"),p&&n.appendChild(p),e&&n.appendChild(e),f.appendChild(n)}}}function showMultiplexAd(e){let t=trAdsContent[e],i=t.e_prev_rid,n="",r="";if(t.di?n=document.getElementById(t.di):t.dcl&&t.dcli&&(n=document.getElementsByClassName(t.dcl)[t.dcli]),t.max_height<=10){let n={acb:(t.acb,"CORNER")};manageInterstitialBtn(n,document.getElementById("button-"+i+e))}"multiplex_int"===t.ft&&t.intl_onact&&(t.isEmpty?manageInterstitialOnScrollEmpty("",e):manageInterstitialOnScroll("",e)),poweredByTimer=t.pt?1e3*t.pt:100,Array.from(document.getElementsByClassName("del-"+t.guid)).length&&(r=Array.from(document.getElementsByClassName("del-"+t.guid))[0]);let l=!1,a=setInterval((function(){if(l||(clearInterval(a),l=!0),"multiplex_int"!==t.ft||t.intl_onact||(n.style.visibility="visible",document.getElementById("button-"+i+e)&&(document.getElementById("button-"+i+e).style.visibility="visible")),t.pbl||t.pbt||t.pbi){const e=document.createElement("div");e.className="powered_ads";const i=document.createElement("style");let n,l,a;"top"===t.pbp?i.innerHTML='[tr-id="'+t.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+t.guid+'"] .powered_ads { display: flex; align-items: center; width: fit-content; bottom: auto; position:absolute; top:-19px; z-index: 99999999; background: #000000b5;padding: 2px 4px;} [tr-id="'+t.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+t.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}':i.innerHTML='[tr-id="'+t.guid+'"] .powered_ads a { height: 15px; display: flex; align-items: center; z-index: 999; padding: 0; } [tr-id="'+t.guid+'"] .powered_ads { display: flex; align-items: center; width: fit-content; bottom: 0; z-index: 999; background: #000000b5;padding: 2px 4px;} [tr-id="'+t.guid+'"] .powered_ads img { width: 70px !important;padding: 0 0 0 5px;} [tr-id="'+t.guid+'"] .powered_ads p { color: #fff; font-size: 12px; margin: 0; padding: 0;line-height:15px;}',e.appendChild(i),t.pbl&&(a=document.createElement("a"),a.href=t.pbl,a.target="_blank",e.appendChild(a)),t.pbt&&(n=document.createElement("p"),n.innerHTML=t.pbt,a?a.appendChild(n):e.appendChild(n)),t.pbi&&(l=document.createElement("img"),l.src=t.pbi,a?a.appendChild(l):e.appendChild(l)),r.appendChild(e)}}),poweredByTimer)}function checkArpsExpiration(){try{if(localStorage.getItem("tr-arpsExp")){if(Date.now()>localStorage.getItem("tr-arpsExp"))return localStorage.removeItem("tr-arps"),localStorage.removeItem("tr-arpsCnt"),localStorage.removeItem("tr-arpsExp"),null}}catch(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log("ARPS Expiration Error Log",e)}}function momagicAmpInit(e){return!0}function manageInterstitialBtn(e,t){t.style.bottom="",t.style.top="",t.style.border="",t.style["font-weight"]="",t.style.left="",t.style.right="",t.style["text-align"]="","CLOSE"===e.acb||"LFCLOSE"===e.acb?(t.style.width="25px",t.style.height="25px",t.style.bottom="0",t.style.top="0",t.style["border-radius"]="unset",t.style.background="#fff",t.style.color="#000",t.style["z-index"]="999999999999",t.style["font-size"]="20px",t.style.border="1px solid #000",t.style["font-weight"]="bold",t.style.padding="0",t.style["text-align"]="center","LFCLOSE"===e.acb?t.style.left="-25px":(t.style.right="-25px",t.style.left="auto")):"CORNER"===e.acb?(t.innerHTML="X",t.style.width="25px",t.style.height="25px",t.style.top="5px",t.style.right="5px",t.style.padding="0px",t.style["border-radius"]="0px",t.style["font-size"]="15px",t.style.background="#fff",t.style.color="#222",t.style["z-index"]="999999999999",t.style["font-family"]="sans-serif",t.style.position="fixed"):"RTCLOSE"===e.acb||"LFTCLOSE"===e.acb?(t.innerHTML="close",t.style.width="min-content",t.style.height="min-content",t.style.padding="3px 4px",t.style["font-size"]="13px",t.style["text-align"]="center",t.style["border-radius"]="unset",t.style.background="#000000",t.style.color="#fff",t.style.border="0px",t.style["line-height"]="initial",t.style["z-index"]="999999999999","LFTCLOSE"===e.acb?t.style.left="-39px":(t.style.right="-39px",t.style.left="auto")):(t.style.width="100%",t.style["text-align"]="right",t.style.top="-24px",t.style.padding="0 5px 0 0",t.style["border-radius"]="unset",t.style.height="24px",t.style.background="#000000",t.style.color="#ffffff",t.style["z-index"]="9999999",t.style["font-size"]="13px",t.style.border="0px",t.style.display="block")}function callHeaderBidding(){let e=[];trAdsContent.map(((t,i)=>{window[t.sv]&&e.push(window[t.sv])})),console.log(e,"slopMap obj"),e.length>0&&sendDemandManagerRequest(e)}function demandManagerRequest(e){sendDemandManagerRequest(e)}function sendDemandManagerRequest(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log("calling sendDemandManagerRequest from callback");const t=function(e,t){let i=!1;return n=>{i||(i=!0,e(n||t))}}((e=>{googletag.pubads().refresh(e)}),e);pbjs.que.push((function(){pbjs.rp.requestBids({callback:t,gptSlotObjects:e})})),setTimeout(t,FAILSAFE_TIMEOUT_TR)}function updateUrlList(e,t){let i=e;if(t){let t=[];try{t=decodeURIComponent(e).split(",")}catch(t){return e.reduce(((e,t)=>{try{const i=decodeURIComponent(t);e.push(i)}catch(e){"true"===localStorage.getItem("truereachDebugMode")&&console.log("malformed url:"+t)}return e}),[])}}else try{i=decodeURIComponent(e)}catch(t){"true"===localStorage.getItem("truereachDebugMode")&&console.log("malformed url:"+e),i=""}return i}function removeGoogleVignette(){const e=window.location.hash;if("#google_vignette"!==e&&"#goog_rewarded"!==e||history.replaceState(null,"",window.location.pathname+window.location.search),document.querySelector('ins[id^="gpt_unit_/22081762831,1008496"]'))try{"scroll"==document.querySelector('ins[id^="gpt_unit_/22081762831,1008496"]').style.overflow&&(document.querySelector('ins[id^="gpt_unit_/22081762831,1008496"]').style.overflow="hidden",document.querySelector('ins[id^="gpt_unit_/22081762831,1008496"] iframe').style.width="100vw")}catch{}}function containsVignetteAds(){return trAdsContent.some((e=>"VignetteAds"===e.ft||"RewardedAds"===e.ft))}function handleVignetteAd(e,t){try{return"VignetteAds"===trAdsContent[t].ft&&localStorage.setItem("tr-vignette",!0),!0}catch(e){console.log(e)}}function checkAndUpdateIframe(e,t){if("ADX"===t.tt){const t=setInterval((()=>{if(e)try{const i=e.style,n=e.height;"-10000px"===i.left&&(e.setAttribute("style",`height: ${n}px !important; left: unset !important; position: unset !important;`),clearInterval(t))}catch(e){console.error("Unable to access iframe content:",e),clearInterval(t)}}),500)}}document.onscroll=()=>{scrolling=!0},setInterval((()=>{scrolling&&(scrolling=!1,restartDelayedRender())}),1e3),document.addEventListener("visibilitychange",(()=>{documentInViewport="visible"===document.visibilityState})),window.addEventListener("hashchange",(function(){if(containsVignetteAds()){if(document.querySelectorAll('ins[id^="gpt_unit_/22081762831"] iframe')){const e=document.querySelectorAll('ins[id^="gpt_unit_/22081762831"] iframe');for(let t=0;t<e.length;t++)e[t].style.inset="0px"}removeGoogleVignette()}}));
