var middleJump=null;!function(e){"use strict";function t(){return!!window.navigator.userAgent.toLowerCase().match(/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone)/i)}function n(){return!!window.navigator.userAgent.toLowerCase().match(/(csdn)/i)}function o(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(e==o[0])return o[1]}return null}function i(e){var t=e.target||e.srcElement,n=document.documentElement.scrollTop;if(t.className.indexOf(y)>-1){e.preventDefault();var o=document.getElementById("hljs-copy-el");o||(o=document.createElement("textarea"),o.style.position="absolute",o.style.left="-9999px",o.style.top=n+"px",o.id="hljs-copy-el",document.body.appendChild(o)),o.textContent=$(e.currentTarget)[0].innerText.replace(/[\u00A0]/gi," "),u($(e.currentTarget.parentNode).attr("data-index")),a("#hljs-copy-el");try{var i=document.execCommand("copy");o.remove(),t.dataset.title=i?C:b,i&&setTimeout(function(){t.dataset.title=v},3e3)}catch(r){t.dataset.title=b}}}function r(e){var t=e.target||e.srcElement,n=document.documentElement.scrollTop;if(t.className.indexOf(y)>-1){e.preventDefault();var o=document.getElementById("hljs-copy-el");o||(o=document.createElement("textarea"),o.style.position="absolute",o.style.left="-9999px",o.style.top=n+"px",o.id="hljs-copy-el",document.body.appendChild(o)),o.textContent=$(e.currentTarget.parentNode).find("code")[0].innerText.replace(/[\u00A0]/gi," "),u($(e.currentTarget.parentNode).attr("data-index")),a("#hljs-copy-el");try{var i=document.execCommand("copy");o.remove(),t.dataset.title=i?C:b,i&&setTimeout(function(){t.dataset.title=v},3e3)}catch(r){t.dataset.title=b}}}function a(e){if(e="string"==typeof e?document.querySelector(e):e,navigator.userAgent.match(/ipad|ipod|iphone/i)){var t=e.contentEditable,n=e.readOnly;e.contentEditable=!0,e.readOnly=!0;var o=document.createRange();o.selectNodeContents(e);var i=window.getSelection();i.removeAllRanges(),i.addRange(o),e.setSelectionRange(0,999999),e.contentEditable=t,e.readOnly=n}else e.select()}function d(){var e="z-index: 20;";n()&&(e="z-index: 100002;");var t=document.createElement("style");t.type="text/css",window.navigator.userAgent.toLowerCase().match(/(csdn)/i)&&isShowCodeFull?t.innerHTML=["pre{position: relative}","pre:hover .code-full-screen{display:block !important;}",".code-full-screen{","display: none !important;","position: absolute;","right: 4px;","top: 4px;","width: 24px !important;","height: 24px !important;","margin: 4px !important;",e,"}","pre:hover .{0}{display: block}",".{0}{","display: none;","position: absolute;","right: 34px;","top: 4px;","font-size: 12px;","color: #ffffff;","background-color: #9999AA;","width:58px;","text-align: center;","padding:2px 0;","margin: 4px !important;","border-radius: 4px;",e,"cursor: pointer;"," box-shadow: 0 2px 4px rgba(0,0,0,0.05), 0 2px 4px rgba(0,0,0,0.05);","}",".{0}:after{","content: attr(data-title)","}","code .{0}{","margin: 2px 8px;","}"].join("").format(y):t.innerHTML=["pre{position: relative}","pre:hover .code-full-screen{display:none !important;}",".code-full-screen{","display: none !important;","position: absolute;","right: 4px;","top: 3px;","width: 24px !important;","height: 24px !important;","margin: 4px !important;",e,"}","pre:hover .{0}{display: block}",".{0}{","display: none;","position: absolute;","right: 4px;","top: 4px;","font-size: 12px;","color: #ffffff;","background-color: #9999AA;","width:58px;","text-align: center;","padding:2px 0;",e,"margin: 8px;","border-radius: 4px;","cursor: pointer;"," box-shadow: 0 2px 4px rgba(0,0,0,0.05), 0 2px 4px rgba(0,0,0,0.05);","}",".{0}:after{","content: attr(data-title)","}","code .{0}{","margin: 2px 8px;","}"].join("").format(y),document.getElementsByTagName("head")[0].appendChild(t)}function c(){"complete"===document.readyState?s():e.addEventListener("DOMContentLoaded",s)}function s(){try{var e;if("ckeditor"==j){e=document.querySelectorAll("code.hljs");for(var o in e)e.hasOwnProperty(o)&&p(e[o].parentNode)}else{e=A;for(var o in e)e.hasOwnProperty(o)&&p(e[o])}!t()&&!n()}catch(i){console.error("CopyButton error: ",i)}}function l(e){var i=e.target||e.srcElement;i.className.indexOf(y)>-1&&(n()?window.location.href="https://passport.csdn.net/account/login?ref=codecopy":t()?toobarFlag(20,21)?$(".ab-app-shadowbox").show():window.csdn.loginBox.show({spm:"1001.2101.3001.7759"}):o("UserName")||window.csdn.loginBox.show({spm:"1001.2101.3001.4334"}))}function p(e){var t="";if("object"==typeof e&&null!==e){var n=".signin(event)",o="hljs",i=".copyCode(event)";"mdeditor"===j&&(o="mdcp"),i=o+i,k&&(i=o+n,t='data-report-click=\'{"spm":"1001.2101.3001.4334"}\''),window.navigator.userAgent.toLowerCase().match(/(csdn)/i)&&isShowCodeFull?e.innerHTML=e.innerHTML+('<img class="code-full-screen app_remove_content no-enlarge-img" src="'+blogStaticHost+'dist/app/img/codeAmplify.png"><div class="{0} {2}" data-title="{1}" '+t+"></div>").format(y,v,O):e.innerHTML=e.innerHTML+('<div class="{0} {2}" data-title="{1}" '+t+"></div>").format(y,v,O),"登录复制"==v&&$(".hljs-button").addClass("active"),"hljs"===o?e.querySelector(".hljs-button").setAttribute("onclick",i):(e.setAttribute("onclick",i),e.style.position="unset")}}function u(e){var t="1001.2101.3001.4259",n=JSON.stringify({codeId:e});if(window.csdn.report&&"function"==typeof window.csdn.report.reportClick)window.csdn.report.reportClick({spm:t,extra:n,name:"复制代码"});else if(window.isApp){var o={spm:t,extra:n,name:"复制代码"},i={trackingInfo:JSON.stringify(o),action:"n_blog_detail_click"};x&&window.jsCallBackListener.csdntrackevent(JSON.stringify(i)),h&&window.webkit.messageHandlers.csdntrackevent.postMessage(JSON.stringify(i))}}function m(e){return e&&e.el&&e.url}function f(e){return e=e.split("?")[0],e.indexOf(".csdn.net")>-1}function g(e){const t=/^#/g;return t.test(e)}if(window.ActiveXObject||"ActiveXObject"in window)return!1;var w=navigator.userAgent,x=w.indexOf("Android")>-1||w.indexOf("Adr")>-1||w.indexOf("OHOS")>-1,h=!!w.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),y="hljs-button",v="复制",b="复制失败",C="已复制",j=void 0,O="",k=!1;n()||o("UserName")||(k=!0),k&&(v="登录复制",O="signin"),$("#content_views").find("pre").each(function(e,t){$(t).attr("data-index",e)}),String.prototype.format=String.prototype.f=function(){var e=arguments;return!!this&&this.replace(/\{(\d+)\}/g,function(t,n){return e[n]?e[n]:t})};var A=document.querySelectorAll("pre code");document.querySelectorAll("div.htmledit_views").length>0?(e.hljs.initCopyButtonOnLoad=c,e.hljs.addCopyButton=p,e.hljs.copyCode=r,e.hljs.signin=l,d(),j="ckeditor"):A.length>0&&(window.mdcp?window.mdcp:window.mdcp={},window.mdcp.copyCode=i,window.mdcp.signin=l,c(),d(),j="mdeditor"),$("#content_views").on("copy",function(e){if(!n()){var o=t()?"3001.10057":"3001.10056";window.csdn.report.reportClick({spm:o})}}),middleJump=function(e){m(e)?(console.log("taodawang 增加拦截a链接跳转的事件 ===>",e),$(e.el).on("click","a",function(t){const o=$(this).attr("href")||"",i=e.url.indexOf("?")>-1?"&":"?",r=f(o)?o:e.url+i+"from_id="+articleId+"&target="+encodeURIComponent(o);if(t.preventDefault(),console.log("taodawang 当前跳转地址是否合法 ===>",f(o)&&!g(o),r),o&&!g(o)){var a={url:r};n()?(x&&window.jsCallBackListener.csdnjumpnewpage(JSON.stringify(a)),h&&window.webkit.messageHandlers.csdnjumpnewpage.postMessage(JSON.stringify(a))):window.open(r,"_blank")}})):console.warn(" middleJump 参数不全",e)}}(window);