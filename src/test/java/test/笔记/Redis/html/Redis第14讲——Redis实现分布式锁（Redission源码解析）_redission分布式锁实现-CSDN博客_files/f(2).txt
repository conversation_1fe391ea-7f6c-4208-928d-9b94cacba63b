(function(){'use strict';var ca=Object.defineProperty,da=globalThis;function ea(a,b){if(b)a:{var c=da;a=a.split(".");for(var d=0;d<a.length-1;d++){var f=a[d];if(!(f in c))break a;c=c[f]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ca(c,a,{configurable:!0,writable:!0,value:b})}}ea("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var m=this||self;function fa(a,b,c){return a.call.apply(a.bind,arguments)}function t(a,b,c){t=fa;return t.apply(null,arguments)}function ha(a,b){function c(){}c.prototype=b.prototype;a.L=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.M=function(d,f,g){for(var e=Array(arguments.length-2),h=2;h<arguments.length;h++)e[h-2]=arguments[h];return b.prototype[f].apply(d,e)}};function ia(a){m.setTimeout(()=>{throw a;},0)};function u(a,b){Array.prototype.forEach.call(a,b,void 0)};let ja=void 0;function ka(a,b){if(a!=null){var c=ja??(ja={});var d=c[a]||0;d>=b||(c[a]=d+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity="incident",ia(a))}};function v(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var la=v(),ma=v("m_m",!0),na=v();const w=v("jas",!0);var oa;const ra=[];ra[w]=55;oa=Object.freeze(ra);const sa=typeof ma==="symbol";var ta={};function ua(a){a=a[ma];const b=a===ta;sa&&a&&!b&&ka(na,3);return b}function va(a){return a!==null&&typeof a==="object"&&!Array.isArray(a)&&a.constructor===Object}var wa=Object.freeze({});const xa=BigInt(Number.MIN_SAFE_INTEGER),ya=BigInt(Number.MAX_SAFE_INTEGER);const za=Number.isFinite;function Aa(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return za(a)?a|0:void 0}function y(a){return a==null||typeof a==="string"?a:void 0}function Ba(a,b,c){if(a!=null&&typeof a==="object"&&ua(a))return a;if(Array.isArray(a)){var d=a[w]|0,f=d;f===0&&(f|=c&32);f|=c&2;f!==d&&(a[w]=f);return new b(a)}};function Ca(a){return a};function z(a,b,c,d,f){d=d?!!(b&32):void 0;const g=[];var e=a.length;let h,k,l,n=!1;b&64?(b&256?(e--,h=a[e],k=e):(k=4294967295,h=void 0),f||b&512||(n=!0,l=(Da??Ca)(h?k- -1:b>>15&1023||536870912,-1,a,h),k=l+-1)):(k=4294967295,b&1||(h=e&&a[e-1],va(h)?(e--,k=e,l=0):h=void 0));let p=void 0;for(let q=0;q<e;q++){let r=a[q];r!=null&&(r=c(r,d))!=null&&(q>=k?(p??(p={}))[q- -1]=r:g[q]=r)}if(h)for(let q in h)a=h[q],a!=null&&(a=c(a,d))!=null&&(e=+q,e<l?g[e+-1]=a:(p??(p={}))[q]=a);p&&(n?g.push(p):g[k]=p);f&&(g[w]= 
b&33522241|(p!=null?290:34));return g}function Ea(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=xa&&a<=ya?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[w]|0;return a.length===0&&b&1?void 0:z(a,b,Ea,!1,!1)}if(ua(a))return Fa(a);return}return a}let Da;function Fa(a){a=a.g;return z(a,a[w]|0,Ea,void 0,!1)};function Ga(){ka(la,5)};function Ha(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){const d=a[w]|0;if(a.length===0&&d&1)return;if(d&2)return a;var c;if(c=b)c=d===0||!!(d&32)&&!(d&64||!(d&16));return c?(a[w]|=34,d&4&&Object.freeze(a),a):z(a,d,Ha,b!==void 0,!0)}if(ua(a))return b=a.g,c=b[w]|0,c&2?a:z(b,c,Ha,!0,!0)}function Ia(a){var b=a.g;if(!((b[w]|0)&2))return a;a=new a.constructor(z(b,b[w]|0,Ha,!0,!0));b=a.g;b[w]&=-3;return a};function A(a,b){a=a.g;return Ja(a,a[w]|0,b)}function Ja(a,b,c){if(c===-1)return null;const d=c+(b&512?0:-1),f=a.length-1;if(d>=f&&b&256)a=a[f][c];else if(d<=f)a=a[d];else return;return a}function B(a,b,c){const d=b&512?0:-1,f=1+d;var g=a.length-1;if(f>=g&&b&256)return a[g][1]=c,b;if(f<=g)return a[f]=c,b;c!==void 0&&(g=b>>15&1023||536870912,1>=g?c!=null&&(a[g+d]={[1]:c},b|=256,a[w]=b):a[f]=c);return b}function C(a){return!!(2&a)&&!!(4&a)||!!(1024&a)} 
function Ka(a){var b=La,c=a.g,d=c[w]|0,f=Ja(c,d,1);b=Ba(f,b,d);b!==f&&b!=null&&B(c,d,b);c=b;if(c==null)return c;a=a.g;d=a[w]|0;d&2||(f=Ia(c),f!==c&&(c=f,B(a,d,c)));return c}function Ma(a,b){2&a&&(a|=16);a=(2&b?a|2:a&-3)|32;return a&=-1025}function D(a,b){a=A(a,b);return(a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0)??!1}function F(a,b){return Aa(A(a,b))??0}function G(a,b){a=A(a,b);return(a==null?a:za(a)?a|0:void 0)??1};var H=class{constructor(a){a:{if(a==null){var b=96;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[w]|0;8192&b||!(64&b)||2&b||Ga();if(b&1024)throw Error("farr");if(b&64){b&16384||(a[w]=b|16384);var c=a;break a}var d=a;b|=64;var f=d.length;if(f){var g=f-1;f=d[g];if(va(f)){b|=256;const e=b&512?0:-1;g-=e;if(g>=1024)throw Error("pvtlmt");for(c in f){const h=+c;if(h<g)d[h+e]=f[c],delete f[c];else break}b=b&-33521665|(g&1023)<<15}}}a[w]=b|16384;c=a}this.g=c}toJSON(){return Fa(this)}}; 
H.prototype[ma]=ta;H.prototype.toString=function(){return this.g.toString()};var Na=class extends H{};function Oa(a){var b=a.g[w]|0,c=b,d=!(2&b);b=void 0===wa?2:4;a=a.g;var f=!!(2&c),g=f?1:b;d&&(d=!f);b=Ja(a,c,1);b=Array.isArray(b)?b:oa;var e=b[w]|0;f=!!(4&e);if(!f){var h=e;h===0&&(h=Ma(h,c),h|=16);e=b;h|=1;var k=c,l=!!(2&h);l&&(k|=2);let n=!l,p=!0,q=0,r=0;for(;q<e.length;q++){const E=Ba(e[q],Na,k);if(E instanceof Na){if(!l){const x=!!((E.g[w]|0)&2);n&&(n=!x);p&&(p=x)}e[r++]=E}}r<q&&(e.length=r);h|=4;h=p?h|16:h&-17;h=n?h|8:h&-9;e[w]=h;l&&Object.freeze(e);e=h}if(d&&!(8&e||!b.length&&(g===1||g===4&& 
32&e))){C(e)&&(b=[...b],e=Ma(e,c),c=B(a,c,b));d=b;for(h=0;h<d.length;h++)k=d[h],l=Ia(k),k!==l&&(d[h]=l);e|=8;e=d.length?e&-17:e|16;d[w]=e}g===1||g===4&&32&e?C(e)||(c=e,a=!!(32&e),e|=!b.length||16&e&&(!f||a)?2:1024,e!==c&&(b[w]=e),Object.freeze(b)):(g===2&&C(e)&&(b=[...b],g=e=Ma(e,c),e=g&=-33,b[w]=e,c=B(a,c,b)),C(e)||(a=c=e,e=a&=-33,e!==c&&(b[w]=e)));return b}var La=class extends H{};var Pa=class extends H{};function Qa(){}function Ra(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}};var Sa={capture:!0},Ta={passive:!0},Ua=Ra(function(){let a=!1;try{const b=Object.defineProperty({},"passive",{get:function(){a=!0}});m.addEventListener("test",null,b)}catch(b){}return a});function Va(a){return a?a.passive&&Ua()?a:a.capture||!1:!1}function I(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,Va(d))};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var J=class{constructor(a){this.g=a}toString(){return this.g}},Wa=new J("about:invalid#zClosurez");class Xa{constructor(a){this.K=a}}function K(a){return new Xa(b=>b.substr(0,a.length+1).toLowerCase()===a+":")}const Ya=new Xa(a=>/^[^:]*([/?#]|$)/.test(a));var Za=K("http"),$a=K("https"),ab=K("ftp"),bb=K("mailto");const cb=[K("data"),Za,$a,bb,ab,Ya];function db(a=cb){for(let b=0;b<a.length;++b){const c=a[b];if(c instanceof Xa&&c.K("#"))return new J("#")}}function eb(a=cb){return db(a)||Wa}var fb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function gb(a,b,c){if(Array.isArray(b))for(let d=0;d<b.length;d++)gb(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};function hb(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}let L=[];const ib=()=>{const a=L;L=[];for(const b of a)try{b()}catch{}};var jb=a=>{var b=M;b.readyState==="complete"||b.readyState==="interactive"?(L.push(a),L.length==1&&(window.Promise?Promise.resolve().then(ib):window.setImmediate?setImmediate(ib):setTimeout(ib,0))):b.addEventListener("DOMContentLoaded",a)};function kb(a=document){return a.createElement("img")};function lb(a,b,c=null,d=!1){mb(a,b,c,d)}function mb(a,b,c,d){a.google_image_requests||(a.google_image_requests=[]);const f=kb(a.document);if(c||d){const g=e=>{c&&c(e);if(d){e=a.google_image_requests;var h=Array.prototype.indexOf.call(e,f,void 0);h>=0&&Array.prototype.splice.call(e,h,1)}f.removeEventListener&&f.removeEventListener("load",g,Va());f.removeEventListener&&f.removeEventListener("error",g,Va())};I(f,"load",g);I(f,"error",g)}f.src=b;a.google_image_requests.push(f)};function nb(a=null){return a&&a.getAttribute("data-jc")==="23"?a:document.querySelector('[data-jc="23"]')} 
function ob(){if(!(Math.random()>.01)){var a=nb(document.currentScript);a=a&&a.getAttribute("data-jc-rcd")==="true"?"pagead2.googlesyndication-cn.com":"pagead2.googlesyndication.com";var b=(b=nb(document.currentScript))&&b.getAttribute("data-jc-version")||"unknown";a=`https://${a}/pagead/gen_204?id=jca&jc=${23}&version=${b}&sample=${.01}`;b=window;var c;if(c=b.navigator)c=b.navigator.userAgent,c=/Chrome/.test(c)&&!/Edge/.test(c)?!0:!1;c&&typeof b.navigator.sendBeacon==="function"?b.navigator.sendBeacon(a): 
lb(b,a,void 0,!1)}};var M=document,N=window;var pb=(a=[])=>{m.google_logging_queue||(m.google_logging_queue=[]);m.google_logging_queue.push([12,a])};var qb=()=>{var a=M;try{return a.querySelectorAll("*[data-ifc]")}catch(b){return[]}},rb=(a,b)=>{a&&hb(b,(c,d)=>{a.style[d]=c})},sb=a=>{var b=M.body;const c=document.createDocumentFragment(),d=a.length;for(let f=0;f<d;++f)c.appendChild(a[f]);b.appendChild(c)};let O=null;function tb(){const a=m.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function ub(){const a=m.performance;return a&&a.now?a.now():null};var vb=class{constructor(a,b){var c=ub()||tb();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const P=m.performance,wb=!!(P&&P.mark&&P.measure&&P.clearMarks),Q=Ra(()=>{var a;if(a=wb){var b;a=window;if(O===null){O="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(O=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=O;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function xb(a){a&&P&&Q()&&(P.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),P.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};function yb(a,b,c,d,f){const g=[];hb(a,(e,h)=>{(e=zb(e,b,c,d,f))&&g.push(`${h}=${e}`)});return g.join(b)}function zb(a,b,c,d,f){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const g=[];for(let e=0;e<a.length;e++)g.push(zb(a[e],b,c,d+1,f));return g.join(c[d])}}else if(typeof a==="object")return f||(f=0),f<2?encodeURIComponent(yb(a,b,c,d,f+1)):"...";return encodeURIComponent(String(a))} 
function Ab(a){let b=1;for(const c in a.h)c.length>b&&(b=c.length);return 3997-b-a.i.length-1}function Bb(a){let b="https://pagead2.googlesyndication.com/pagead/gen_204?id=fccs&",c=Ab(a)-24;if(c<0)return"";a.g.sort((g,e)=>g-e);let d=null,f="";for(let g=0;g<a.g.length;g++){const e=a.g[g],h=a.h[e];for(let k=0;k<h.length;k++){if(!c){d=d==null?e:d;break}let l=yb(h[k],a.i,",$");if(l){l=f+l;if(c>=l.length){c-=l.length;b+=l;f=a.i;break}d=d==null?e:d}}}a="";d!=null&&(a=`${f}${"trn"}=${d}`);return b+a} 
var Cb=class{constructor(){this.i="&";this.h={};this.l=0;this.g=[]}};class Db{};function Eb(){var a=Fb,b=window.google_srt;b>=0&&b<=1&&(a.g=b)}function Gb(a){if(Fb.g<1)try{let b;a instanceof Cb?b=a:(b=new Cb,hb(a,(d,f)=>{var g=b;const e=g.l++,h={};h[f]=d;d=[h];g.g.push(e);g.h[e]=d}));const c=Bb(b);c&&lb(m,c)}catch(b){}}var Hb=class{constructor(){this.g=Math.random()}};const Ib=[Za,$a,bb,ab,Ya,K("market"),K("itms"),K("intent"),K("itms-appss")]; 
function Jb(){var a=`${N.location.protocol==="http:"?"http:":"https:"}//${"pagead2.googlesyndication.com"}/pagead/gen_204`;return b=>{b={id:"unsafeurl",ctx:625,url:b};var c=[];for(d in b)gb(d,b[d],c);var d=c.join("&");if(d){b=a.indexOf("#");b<0&&(b=a.length);c=a.indexOf("?");let f;c<0||c>b?(c=b,f=""):f=a.substring(c+1,b);b=[a.slice(0,c),f,a.slice(b)];c=b[1];b[1]=d?c?c+"&"+d:d:c;d=b[0]+(b[1]?"?"+b[1]:"")+b[2]}else d=a;navigator.sendBeacon&&navigator.sendBeacon(d,"")}};let Fb; 
const R=new class{constructor(a,b){this.g=[];this.i=b||m;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.g=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.h=Q()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.h)return null;a=new vb(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;P&&Q()&&P.mark(b);return a}end(a){if(this.h&&typeof a.value==="number"){a.duration=(ub()||tb())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;P&&Q()&&P.mark(b);!this.h||this.g.length> 
2048||this.g.push(a)}}}(1,window);function Kb(){window.google_measure_js_timing||(R.h=!1,R.g!==R.i.google_js_reporting_queue&&(Q()&&u(R.g,xb),R.g.length=0))}(function(a){Fb=a??new Hb;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());Eb();window.document.readyState==="complete"?Kb():R.h&&I(window,"load",()=>{Kb()})})();function Lb(a){I(N,"message",b=>{let c;try{c=JSON.parse(b.data)}catch(d){return}!c||c.googMsgType!=="ig"||a(c,b)})};function S(){this.h=this.h;this.i=this.i}S.prototype.h=!1;S.prototype.dispose=function(){this.h||(this.h=!0,this.l())};S.prototype[Symbol.dispose]=function(){this.dispose()};S.prototype.l=function(){if(this.i)for(;this.i.length;)this.i.shift()()};function T(a,b,c){S.call(this);this.m=a;this.B=b||0;this.o=c;this.u=t(this.v,this)}ha(T,S);T.prototype.g=0;T.prototype.l=function(){T.L.l.call(this);this.isActive()&&m.clearTimeout(this.g);this.g=0;delete this.m;delete this.o}; 
T.prototype.start=function(a){this.isActive()&&m.clearTimeout(this.g);this.g=0;var b=this.u;a=a!==void 0?a:this.B;if(typeof b!=="function")if(b&&typeof b.handleEvent=="function")b=t(b.handleEvent,b);else throw Error("Invalid listener argument");this.g=Number(a)>2147483647?-1:m.setTimeout(b,a||0)};T.prototype.isActive=function(){return this.g!=0};T.prototype.v=function(){this.g=0;this.m&&this.m.call(this.o)};const Mb={display:"inline-block",position:"absolute"},Nb={display:"none",width:"100%",height:"100%",top:"0",left:"0"};function U(a,b){a&&(a.style.display=b?"inline-block":"none")}function Ob(a,b){if(a)return N.getComputedStyle(a).getPropertyValue(b)}function Pb(a=""){const b={top:0,right:0,bottom:0,left:0};a&&(a=a.split(","),a.length===4&&a.reduce((c,d)=>c&&!isNaN(+d),!0)&&([b.top,b.right,b.bottom,b.left]=a.map(c=>+c)));return b} 
function Qb(a,b,c=2147483647){const d=M.createElement("div");rb(d,{...Mb,"z-index":String(c),...b});D(a.data,10)&&I(d,"click",Qa);if(D(a.data,11)){a=M.createElement("a");b=Jb();c=eb(Ib);c===Wa&&b("#");if(c instanceof J)if(c instanceof J)b=c.g;else throw Error("");else b=fb.test(c)?c:void 0;b!==void 0&&(a.href=b);a.appendChild(d);return a}return d} 
function Rb(a,b){switch(G(b.j,5)){case 2:N.AFMA_Communicator?.addEventListener?.("onshow",()=>{V(a,b)});break;case 10:I(N,"i-creative-view",()=>{V(a,b)});break;case 4:I(M,"DOMContentLoaded",()=>{V(a,b)});break;case 8:Lb(c=>{c.rr&&V(a,b)});break;case 9:if("IntersectionObserver"in N){const c=new IntersectionObserver(d=>{for(const f of d)if(f.intersectionRatio>0){V(a,b);break}});c.observe(M.body);a.J.push(c)}break;case 11:N.AFMA_Communicator?.addEventListener?.("onAdVisibilityChanged",()=>{V(a,b)})}} 
function Sb(a,b){b=Pb(b);const c=F(a.data,9);a.l=[{width:"100%",height:b.top+c+"px",top:-c+"px",left:"0"},{width:b.right+c+"px",height:"100%",top:"0",right:-c+"px"},{width:"100%",height:b.bottom+c+"px",bottom:-c+"px",left:"0"},{width:b.left+c+"px",height:"100%",top:"0",left:-c+"px"}].map(d=>Qb(a,d,9019))} 
function Tb(a){var b=0;for(const d of a.I){const f=d.j,g=a.v[G(f,5)];d.s||g===void 0||(b=Math.max(b,g+F(f,2)))}a.m&&a.m.dispose();b-=Date.now();const c=a.h;b>0?(U(c,!0),a.m=new T(()=>{U(c,!1)},b),a.m.start()):U(c,!1)}function V(a,b){if(!b.s){var c=G(b.j,5);a.v[c]=Date.now();D(b.j,9)&&(a.I.push(b),Tb(a))}} 
function Ub(a,b,c){if(!a.g||!a.u||b.timeStamp-a.g.timeStamp>=300)return!1;const d=new Map;u(a.u.changedTouches,f=>{d.set(f.identifier,{x:f.clientX,y:f.clientY})});b=F(c.j,11)||10;for(const f of a.g.changedTouches)if(a=d.get(f.identifier),!a||Math.abs(a.x-f.clientX)>b||Math.abs(a.y-f.clientY)>b)return!0;return!1} 
var Vb=class{constructor(){var a=W;this.l=[];this.m=this.h=null;this.I=[];this.data=null;this.B=[];this.i=[];this.o=[];this.v={};this.J=[];this.u=this.g=null;this.F="";this.G=a["send-fccs"]==="true";this.F=a.qid||""}init(a){pb([a]);this.data=new Pa(a);a=Ka(this.data);u(Oa(a),g=>{this.o.push({C:0,s:!1,D:0,j:g,A:-1})});this.i=qb();let b=!1;a=this.i.length;for(let g=0;g<a;++g){var c=new La(JSON.parse(this.i[g].getAttribute("data-ifc")||"[]"));u(Oa(c),e=>{this.o.push({C:0,s:!1,D:0,j:e,A:g});G(e,4)=== 
1&&(b=!0)})}c=a=!1;let d=D(this.data,12);for(var f of this.o){const g=f.j;F(g,2)>0&&G(g,5)>0?(!this.h&&D(g,9)&&(this.h=Qb(this,Nb)),Rb(this,f)):(y(A(g,1))??"")&&D(g,9)&&Sb(this,y(A(g,1))??"");(y(A(g,1))??"")&&(a=!0);F(g,11)>0&&(c=!0);D(g,12)&&(d=!0)}f=[];this.h&&f.push(this.h);!b&&f.push(...this.l);M.body&&sb(f);D(this.data,13)&&jb(()=>{const g=M.body.querySelectorAll(".amp-fcp, .amp-bcp");for(let e=0;e<g.length;++e)Ob(g[e],"position")==="absolute"&&U(g[e],!1)});I(M,"click",g=>{if(this.G){var e={cx:g.clientX, 
cy:g.clientY,et:Date.now(),qid:this.F};var h=Db;var k="H";h.H&&h.hasOwnProperty(k)||(k=new h,h.H=k);h=[];!e.eid&&h.length&&(e.eid=h.toString());Gb(e);this.G=!1}if(g.isTrusted===!1&&D(this.data,15))g.preventDefault?g.preventDefault():g.returnValue=!1,g.stopImmediatePropagation(),ob();else{e=-1;h=[];for(var l of this.o){k=l.A;var n=k!==-1;if(!(F(l.j,3)<=e||l.s||n&&h[k]===!1)){var p=!n||h[k]||this.i[k].contains(g.target);n&&p&&(h[k]=!0);if(k=p)if(k=g,n=l.j,F(n,2)>0&&G(n,5)>0)k=this.v[G(n,5)],k=k!==void 0&& 
Date.now()<k+F(n,2);else if(y(A(n,1))??""){{const x=(l.A>=0?this.i[l.A]:M.body).getBoundingClientRect(),X=Number(Ob(M.body,"zoom")||"1"),[Zb,$b]=[k.clientX,k.clientY],[Y,Z,pa,qa]=[Zb/X-x.left,$b/X-x.top,x.width,x.height];if(!(pa>0&&qa>0)||isNaN(Y)||isNaN(Z)||Y<0||Z<0)k=!1;else{n=Pb(y(A(l.j,1))??"");p=!(Y>=n.left&&pa-Y>n.right&&Z>=n.top&&qa-Z>n.bottom);var q=D(l.j,12);if(this.g&&(D(this.data,12)||q)&&k.timeStamp-this.g.timeStamp<300){k=this.g.changedTouches[0];const [aa,ba]=[k.clientX/X-x.left,k.clientY/ 
X-x.top];!isNaN(aa)&&!isNaN(ba)&&aa>=0&&ba>=0&&(p=(p=D(this.data,16)||q?p:!1)||!(aa>=n.left&&pa-aa>n.right&&ba>=n.top&&qa-ba>n.bottom))}k=p}}}else k=F(n,11)>0?Ub(this,k,l):!0;if(k){var r=l;e=F(l.j,3)}}}if(r)switch(l=r.j,G(l,4)){case 2:case 3:g.preventDefault?g.preventDefault():g.returnValue=!1;e=Date.now();e-r.D>500&&(r.D=e,++r.C);e=r.j;if(F(e,8)&&r.C>=F(e,8))if(r.s=!0,this.h&&F(e,2)>0)Tb(this);else if(this.l.length>0&&(y(A(e,1))??""))for(var E of this.l)U(E,!1);ob();E=Fa(l);for(const x of this.B)x(g, 
E)}}},Sa);c&&I(M,"touchstart",g=>{this.u=g},Ta);(a&&d||c)&&I(M,"touchend",g=>{this.g=g},Ta)}registerCallback(a){this.B.push(a)}};const Wb=nb(document.currentScript);if(Wb==null)throw Error("JSC not found 23");var W;const Xb={},Yb=Wb.attributes;for(let a=Yb.length-1;a>=0;a--){const b=Yb[a].name;b.indexOf("data-jcp-")===0&&(Xb[b.substring(9)]=Yb[a].value)}W=Xb;const ac=window;ac.googqscp=new Vb;W["init-data"]&&ac.googqscp.init(JSON.parse(W["init-data"]));}).call(this);
