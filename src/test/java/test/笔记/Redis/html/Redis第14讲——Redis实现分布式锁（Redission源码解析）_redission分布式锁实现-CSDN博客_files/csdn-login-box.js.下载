"use strict";function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var _slicedToArray=function(){function e(e,t){var i=[],o=!0,n=!1,s=void 0;try{for(var a,r=e[Symbol.iterator]();!(o=(a=r.next()).done)&&(i.push(a.value),!t||i.length!==t);o=!0);}catch(e){n=!0,s=e}finally{try{!o&&r.return&&r.return()}finally{if(n)throw s}}return i}return function(t,i){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return e(t,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),_createClass=function(){function e(e,t){for(var i=0;i<t.length;i++){var o=t[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,i,o){return i&&e(t.prototype,i),o&&e(t,o),t}}();!function(){function e(e){for(var t=document.cookie.split("; "),i=0;i<t.length;i++){var o=t[i].split("=");if(o[0]==e)return decodeURIComponent(o[1])}}function t(e){window.csdn&&window.csdn.report&&window.csdn.report.reportClick(e)}function i(e){window.csdn&&window.csdn.report&&window.csdn.report.reportView(e)}function o(){var e=/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone|csdn)/i.test(navigator.userAgent);return/(MicroMessenger)/i.test(navigator.userAgent)?!/(WindowsWechat|MacWechat)/i.test(navigator.userAgent):e}function n(){var e=/micromessenger/.test(navigator.userAgent.toLowerCase()),t=/wxwork/.test(navigator.userAgent.toLowerCase());if("undefined"!=typeof WeixinJSBridge||e)return!t}function s(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{toUn:""};this.app=h(),this.isMobile=o(),this.cb=void 0,this.payInfo=void 0,this.reloadOnClose=!1,this.closeOnClickMask=!0,this.showClose=!0,this.loginTip="",this.reportExtra={autoPopup:!1,pageType:""},e&&e.tip&&(this.loginTip=e.tip,delete e.tip),e&&e.reportExtra&&(this.reportExtra=Object.assign({},this.reportExtra,e.reportExtra),delete e.reportExtra),e&&e.hasOwnProperty("closeOnClickMask")&&(this.closeOnClickMask=!!e.closeOnClickMask,delete e.closeOnClickMask),e&&e.hasOwnProperty("showClose")&&(this.showClose=!!e.showClose,delete e.showClose),e&&e.cb&&(this.cb=e.cb),e&&e.biz&&"pay"===e.biz&&e.payInfo&&(this.payInfo=e.payInfo),this.cb&&(delete e.cb,e.hascb="yes"),this.payInfo&&delete e.payInfo,this.inputData=e;var t=410,i=566,n="";this.isMobile?(t=e.toUn?335:343,i=e.toUn?445:410,n=e.toUn?"https://passport.csdn.net/waploginv4":"https://passport.csdn.net/waplogin"):n=e.toUn?"https://passport.csdn.net/loginv4":"https://passport.csdn.net/account/login",this.defaultParams={domain:"csdn.net",isIframe:!0,frameWidth:t,frameHeight:i,append:"#passportbox",iframeName:"passport_iframe",from:encodeURIComponent(window.location.href),pvSource:"",service:"",loginService:n};var s=r&&r.spm?r.spm:"",a=e&&e.spm?e.spm:"",p=a||s;p&&(this.inputData=this.inputData||{},this.inputData.spm=g(p)),this.options=Object.assign({},this.defaultParams,this.inputData),this.extend="",this.version=this.isMobile?"popupv1":"loginv3",this.renderCss(),this.fileExtends(),this.init(this.options)}var a=null,r={},p=null;window.csdn=window.csdn||{};var l="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAHhlWElmTU0AKgAAAAgABAEaAAUAAAABAAAAPgEbAAUAAAABAAAARgEoAAMAAAABAAIAAIdpAAQAAAABAAAATgAAAAAAAACQAAAAAQAAAJAAAAABAAOgAQADAAAAAQABAACgAgAEAAAAAQAAACCgAwAEAAAAAQAAACAAAAAAfgvaUgAAAAlwSFlzAAAWJQAAFiUBSVIk8AAAAalJREFUWAntlr9OwzAQxm2XAV4DIfEQTFHE2p2B5+hSiQ5d+jh0BKlZUJ6i8BxUUBt/up5kOU7iM38W4sWxc3ffz2dfYqWm9t8zoOME7HYv1859Lo3Re6Xspqqq99hGMm6a5lwps7DWXSl1XNV1/Rr6n4UDerYPWus755TS2tz4APNSiLZtLw6Hj0cf69bHVM7NIHEfappwgGfn3BvPwdHTb2kVPJvXw4fF2YOyyiPqOwDG2LWHfWazEohT2rfkS5Eopt1wXO47ZwAvBgKMbofUNwlQCiEVh04vgBSiRHwUIBeiVDwLYAwC71EpiQM3el7gObgFMODWv0qULsqV2um0Z4nDIxsAxikIzHOTisNPBACHPogSccTrfIgw+ZdNlIG+1TNwSRayM9Aj/kSihFDy2c7KQEqcV0vSv1iGQ+L8m86x4W2K+8EMSAJLbEOIXoCSgCU+SYCSQLwqqW8HQBqAhcNeEiNRhmbxnW87QOhw2nlcotaaZQiK5w4A3V7JjEuNT3vsPDROQfiL6WXsk7gVH1e4vf7EtRwQfjt8JuharvVsHQNM4ykDX94FYhBKOJraAAAAAElFTkSuQmCC";if(window.csdn&&window.csdn.loginBox&&window.csdn.loginBox.show)return void void 0;var c=function(e,t,i){var o=new Date;if(i)"number"==typeof i?o.setTime(o.getTime()+i):o=new Date(o.getFullYear(),o.getMonth(),o.getDate()+1,0,0,0);else{o.setTime(o.getTime()+2592e6)}document.cookie=e+"="+encodeURIComponent(t)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"},d=function(e){var t=/([^?#*&=]+)=([^?#*&=]+)/g,i={};return location.href.replace(t,function(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];var n=t[1],s=t[2];i[n]=s}),i[e]},h=function(){return navigator.userAgent.toLowerCase().indexOf("csdn")>-1},u=function(){if(h()){var e=navigator.userAgent.toLowerCase(),t=JSON.stringify({url:"csdnapp://app.csdn.net/login/quick"});/iphone|ipad|ipod|ios/i.test(e)?window.webkit.messageHandlers.csdnjumpnewpage.postMessage(t):window.jsCallBackListener.csdnjumpnewpage(t)}},m=function(){if(h()){/iphone|ipad|ipod|ios/i.test(navigator.userAgent.toLowerCase())?window.webkit.messageHandlers.csdnLogOut.postMessage(null):window.jsCallBackListener.csdnLogOut()}},g=function(e){e=String(e);var t=e.split(".").length;if(2===t||3===t){var i=document.querySelector('meta[name="report"]'),o=i&&i.getAttribute("content")||"{}",n=JSON.parse(o);return n.spm?n.spm+"."+e:e}return e};s.prototype.init=function(e){if(this.app){var t=setTimeout(function(){window.csdn.loginBox.self=void 0,clearTimeout(t)});return u(),!1}this.wapDistribute(e)},s.prototype.fileExtends=function(){for(var e in this.inputData)this.defaultParams.hasOwnProperty(e)||(this.extend=this.extend+"&"+e+"="+this.inputData[e])},s.prototype.renderCss=function(){var e=window.document.head,t=e.firstElementChild||e.firstChild,i=document.createElement("style");i.innerText=".passport-login-container{position: fixed;top: 0;left: 0;z-index: 9999;width: 100%;height: 100%;}.passport-login-box{position: absolute;display: block;border-radius: 8px;left: 50%;top: 50%;z-index: 10001;-webkit-transform: translate(-50%, -50%);-ms-transform: translate(-50%, -50%);-o-transform: translate(-50%, -50%);-moz-transform: translate(-50%, -50%);transform: translate(-50%, -50%);background-color: #fff;}.passport-login-mark{position: absolute;top: 0;left: 0;z-index: 9999;background-color: rgba(0, 0, 0, 0.5);width: 100%;height: 100%;}.passport-login-box3{position: absolute;display: block;border-radius: 8px;left: 0;bottom: 0;z-index: 10001;background-color: #fff;}",e.insertBefore(i,t)},s.prototype.renderHtml=function(e){var t=this,o=this.options.loginService,n=this.options.frameWidth,s=this.options.frameHeight;if(this.$markDom=$('<div class="passport-login-mark"></div>'),window.document.domain=this.options.domain,o=o+(-1===o.indexOf("?")?"?from=":"&from=")+this.options.from,o=this.options.service?o+"&service="+this.options.service:o,o+="&iframe=true",o+="&newframe=true",o=o+"&parentWidth="+document.documentElement.clientWidth,o=this.options.pvSource?o+"&"+this.options.pvSource:o,o=this.version?o+"&version="+this.version:o,o=this.extend?o+this.extend:o,"goApplets"===this.options.type?(this.$loginDom=$('<div class="passport-login-container"><div id='+this.options.append.replace(/[#\.]/,"")+' class="passport-login-box3" style="display: block;'+(this.options.frameHeight?"height:"+this.options.frameHeight+"px":"")+";"+(this.options.frameWidth?"width:"+this.options.frameWidth:"")+'"></div></div>'),this.$iframeHtml=$('<iframe  width="'+this.options.frameWidth+'" height="'+this.options.frameHeight+'" name="'+this.options.iframeName+'" src="'+o+'" style="border-radius: 20px 20px 0px 0px;" frameborder="0" scrolling="no"></iframe>')):(this.$loginDom=$('<div class="passport-login-container"><div id='+this.options.append.replace(/[#\.]/,"")+' class="passport-login-box" style="display: block;'+(s?"height:"+s+"px":"")+";"+(window.outerWidth<375?"transform: scale("+window.outerWidth/375+") translate(-50%, -50%)":"")+'"></div></div>'),this.$iframeHtml=$('<iframe  width="'+n+'" height="'+s+'" name="'+this.options.iframeName+'" src="'+o+'" style="border-radius: 8px;" frameborder="0" scrolling="no"></iframe>'),this.$closeBtn=$('<img style="position: absolute;top: 16px;left: -20px;width: 16px;height: 16px" src='+l+" />"),this.$closeBtn.on("click",function(){t.close()})),this.options.showProgressCd){var a=4,r=2*Math.PI*12,p='<svg width="24" height="24" style="\n          position: absolute;\n          left: -28px;\n          top: 4px;\n      ">\n        <circle cx="12" cy="12" r="11" stroke="#fff" stroke-width="2" stroke-linecap="round" fill-opacity="0"></circle>\n        <circle cx="12" cy="12" r="11" stroke-width="2" stroke-dasharray="0 '+r+'" stroke="rgba(0, 0, 0, 0.5)"  fill-opacity="0"></circle> \n        <text style="fill: #fff;font-size: 12px" x="12" dy="4"  y="12" text-anchor="middle">'+a+"s</text>\n      </svg>";this.$closeProgress=$(p);var c=this.$closeProgress.find("circle")[1],d=this.$closeProgress.find("text")[0];c&&d&&function e(){var i=setTimeout(function(){a>0&&(a-=.3,a<=0?(t.$closeProgress.remove(),$(t.options.append).append(t.$closeBtn),clearTimeout(i)):(c.setAttribute("stroke-dasharray",r*((4-a)/4)+" "+r),d.innerHTML=Math.ceil(a)+"s",e()))},300)}()}this.$loginDom.append(this.$markDom),$("body").append(this.$loginDom),$(this.options.append).append(this.$iframeHtml);var h=this;this.loginTip&&window.addEventListener("message",function(e){"login_onload"===e.data&&h.$iframeHtml[0].contentWindow.postMessage("JSON_"+JSON.stringify({data:h.loginTip,name:"setLoginTip"}),"*")}),this.showClose&&(this.$closeProgress?$(this.options.append).append(this.$closeProgress):$(this.options.append).append(this.$closeBtn));var u={};return this.reportExtra&&(u.extra=JSON.stringify(this.reportExtra)),this.inputData.spm&&(u.spm=this.inputData.spm),i(u),!0},s.prototype.changeCloseReLoadSwitch=function(e){this.reloadOnClose=!!e},s.prototype.close=function(){var e={spm:"3001.6428"};this.reportExtra&&(Object.assign(this.reportExtra,{action:"close"}),e.extra=JSON.stringify(this.reportExtra)),this.inputData.spm&&(e.spm=this.inputData.spm),t(e),this.$loginDom.remove(),this.reloadOnClose&&window.location.reload(),window.csdn.loginBox.self=void 0},s.prototype.getVersion=function(e){var t=this;$.ajax({type:"get",url:"https://passport.csdn.net/v1/register/pc/iframe/login/version",crossDomain:!0,xhrFields:{withCredentials:!0},contentType:"application/json",success:function(i){i.status?(t.options.frameWidth=i.data.width,t.options.frameHeight=i.data.height,t.version=i.data.controlVersion,t.renderHtml(e)):t.renderHtml(e)},error:function(i){t.renderHtml(e)}})},s.prototype.wapDistribute=function(e){var t=this,i={platform:t.isMobile?"WAP":"PC",source:t.options.pvSource,spm:t.options.spm};this.reportExtra.popupType&&(i.popupType=this.reportExtra.popupType),$.ajax({type:"get",url:"https://passport.csdn.net/v1/login/distribute/login/route?from="+t.options.from,data:i,crossDomain:!0,xhrFields:{withCredentials:!0},contentType:"application/json",success:function(i){if(i.status){if(t.reportExtra.pageType=i.data.popup?"popup":"page",!i.data.popup){if("wxapplets"==i.data.strategy){var o={frameHeight:"283",frameWidth:"100%",type:"goApplets",append:"#passportbox3",loginService:"https://passport.csdn.net/wapactivatelogin?time=1345"};t.inputData.spm=g("3001.10042"),Object.assign(t.options,o),t.renderHtml(e)}else window.csdn.loginBox.self=void 0,window.location.href=i.data.routeUrl;return!1}e.toUn||(t.options.frameWidth=i.data.width,t.options.frameHeight=i.data.height,t.version=i.data.version),t.isMobile&&i.data&&i.data.img&&(t.options.loginService=t.options.loginService+"?popimg="+i.data.img),t.renderHtml(e)}else t.renderHtml(e)},error:function(i){t.renderHtml(e)}})};var f=function(){function t(e){_classCallCheck(this,t),this.callBackFn=e&&e.cb||null,this.errorFn=e&&e.error||null,e&&delete e.cb,e&&delete e.error,this.biz="",this.subBiz="",e&&e.biz&&(this.biz=e.biz,delete e.biz),e&&e.subBiz&&(this.subBiz=e.subBiz,delete e.subBiz),this.app=h(),this.isMobile=o(),this.inputData=e,this.defaultParams={status:"activate",domain:"csdn.net",isIframe:!0,frameWidth:this.isMobile?295:366,frameHeight:this.isMobile?370:408,append:"#passportbox2",iframeName:"passport_iframe2",from:encodeURIComponent(window.location.href),loginService:"https://passport.csdn.net/key"},e&&e.spm&&(this.inputData.spm=g(e.spm)),this.options=Object.assign({},this.defaultParams,this.inputData),this.extend="",this.renderCss(),this.fileExtends(),this.init(this.options)}return _createClass(t,[{key:"getUserStatus",value:function(t){var i=this;$.ajax({url:"https://passport.csdn.net/v1/api/check/userstatus",timeout:5e3,type:"POST",contentType:"application/json",xhrFields:{withCredentials:!0},data:JSON.stringify({username:e("UserName")||"",biz:this.biz,subBiz:this.subBiz}),dataType:"json",success:function(t){if(t.status)if(t.detail){switch(t.detail){case"deleted":i.options.status="deleted",i.options.frameHeight=178;break;case"activate":i.options.status="activate",i.options.loginService="https://passport.csdn.net/waploginv4",i.options.nickName=e("UserNick");break;case"speechForbidden":i.options.status="forbidden";break;default:return!0}i.renderHtml()}else i.executeCallBack();else i.close(),window.csdn.loginBox.show({spm:"3001.9944"})},error:function(e){throw i.executeError(e),void 0,new Error(e.responseText)}})}},{key:"renderCss",value:function(){var e=window.document.head,t=e.firstElementChild||e.firstChild,i=document.createElement("style");i.innerText=".passport-login-container2{position: fixed;top: 0;left: 0;z-index: 9999;width: 100%;height: 100%;}.passport-login-box2{position: absolute;display: block;border-radius: 8px;left: 50%;top: 50%;z-index: 10001;-webkit-transform: translate(-50%, -50%);-ms-transform: translate(-50%, -50%);-o-transform: translate(-50%, -50%);-moz-transform: translate(-50%, -50%);transform: translate(-50%, -50%);background-color: #fff;}.passport-login-mark2{position: absolute;top: 0;left: 0;z-index: 9999;background-color: rgba(0, 0, 0, 0.5);width: 100%;height: 100%;}",e.insertBefore(i,t)}},{key:"renderHtml",value:function(e){var t=this,o=this.options.loginService;if(this.$loginDom=$('<div class="passport-login-container2"><div id='+this.options.append.replace(/[#\.]/,"")+' class="passport-login-box2" style="display: block;'+(this.options.frameHeight?"height:"+this.options.frameHeight+"px":"")+'"></div></div>'),this.$markDom=$('<div class="passport-login-mark2"></div>'),window.document.domain=this.options.domain,o=o+(-1===o.indexOf("?")?"?status=":"&status=")+this.options.status,o=this.extend?o+this.extend:o,this.options.nickName&&(o=o+"&nickname="+this.options.nickName),this.$iframeHtml=$('<iframe  width="'+this.options.frameWidth+'" height="'+this.options.frameHeight+'" name="'+this.options.iframeName+'" src="'+o+'" style="border-radius: 8px;" frameborder="0" scrolling="no"></iframe>'),this.$closeBtn=$('<img style="position: absolute;top: 16px;left: -20px;width: 16px;height: 16px" src='+l+" />"),this.$closeBtn.on("click",function(){t.close()}),this.$loginDom.append(this.$markDom),$("body").append(this.$loginDom),$(this.options.append).append(this.$iframeHtml),$(this.options.append).append(this.$closeBtn),this.options.spm){i({spm:this.options.spm})}}},{key:"close",value:function(){this.$loginDom&&this.$loginDom.remove(),window.csdn.loginBox.self2=void 0}},{key:"fileExtends",value:function(){for(var e in this.inputData)this.defaultParams.hasOwnProperty(e)||(this.extend=this.extend+"&"+e+"="+this.inputData[e])}},{key:"init",value:function(e){this.getUserStatus()}},{key:"executeCallBack",value:function(){this.callBackFn&&this.callBackFn(),this.close()}},{key:"executeError",value:function(e){this.errorFn&&this.errorFn(e),this.close()}}]),t}(),w=function(){function e(t){_classCallCheck(this,e),this.inputData=t,this.defaultParams={width:368,height:210,right:"24px",bottom:"24px",title:"登录后您可以享受以下权益："},this.reportExtra={autoPopup:!1,pageType:""},t&&t.reportExtra&&(this.reportExtra=Object.assign({},this.reportExtra,t.reportExtra),delete t.reportExtra),this.icons={code:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAvRJREFUWEfVl+1P0lEUx79XDGNubZqJqCssk2zOHqyVgWTYQgm1rcWW2kt//gf9Ef0H4bss22y9yMdcSgqStqzmG582Agsf2Cy3pjYV+LV7lZ9AKOCgX51XwL33nM/5nnPPLgQiGxE5PkIAWltf5vO8/xEPvhKAIsFwiwRkiJCUh83Nd90B3wIADe7nfRMAMhMcONzdjxQiOReAEADM5hftPPj7SQ7O3BOQ5xx3r2H78449NncsJEH2vfJZbOFMueEA/N/IPhCjhTOx5IMV+D8BcnKycEZVgNGxCWxsbMYsYtwKpKfLcLOqnAV53T8iBLpTX4Xs7Ex09wyjtLQIaVIpBgZHsbb2a1+YuAAyMo7AUKMFhVhaWkZn11vmnH5vbDDC6/XhSdsrtocqsrq2jr4+G1ZWfu4JETNAjjwLer0aaWlSFpxmv7m5xRyXlJzGtfLzcDrdeDMwCqn0EKr1GgYRUMrj+R4RIiYA5Ylc6HRXkZoqgcs1j0HLe/h8PsFhrbESCsUxDFrG4HB8Y79LJBJU6a5AqcxjytC1uTl6w0MtKoCqSAmt9hIIIZiccsBu/wye370oMtlhNDUa4ffzTP6tLa8QgZ7RqC+iuPgkOzNsHcfsrCuEICoArS2ruWcZnZ3bNQ826rxCU4a5rwvo77dHlLm+Tge5/ChryGft3fEBqFQF0FaUbSsw6YD9XagCBoMW+XlyDA1/+CM7ekatvoCzxaeYAlbrOGbiVYDi0jrSetK6Ol3zsOz0AG3IB011AHi0Pe0Kuf/hPWCxjMF1kB4I6EU7Wn8r9BZQsMrrl+F2e9DbZxWkDb8FtDS0hJEsag8EHwqfA6ur6ygsPA6b7SOmpr8IW+tqbyR+DgS8B0/C6RknCpR5sI18gte72/3V1ZrkTMJ9Z+oBF+MqwQFj7HssEoC4DxLRn2SiP0ppwUR9liej0WLx+W/9MwomNps7enjAEEsW0fYQoJfjTLcj7dtTgUQCAOhp4UzGuACiZZWoddF74De0FoIwwHUb7wAAAABJRU5ErkJggg==",download:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAYZJREFUWEftVbtOw0AQnEsRrIhP4NHnTyhS5ighBXchfAAdBR30oPgoQlqbLkL8SWok8gnIilNk0UkYodjxrZ3EbuLO8uzs7Mx5T6DmR9TcH2wBxoRnBDIAjhyiZwJCKdX94AzHFuCb4IvRPOk500oeb1sAcQgTjFaSNRwLZEl9E+wF7B3YO1CNAwU2XpG1ABDsIrvWWr7/L0ztgYIbr7AIreWJS8AngNNizGx0akWnHPiN4A1Ai03LA0YgSGcElms4DNqigRBAm8ftRE1piW6/L6eryLV3ge9PWmjMn0F04aTPAwgxxtIbaN2JsmDOy8iY4JKApxKRRCAaaH0+ztXHma5EJGstZ0ewCmRH4rC8tICkMCeSSAA3SslXjqsJxnkGssgyImFbvrEDCcFoNPIWi8OOfW82vye9Xm9eZPKNHCjTaF1NqQh2IsB/Ce9BdGsd3WaDDK4YQjzqq+6d/fbngG+CuILmiZ5YK+mtCrCH6GDH0+cIqC6CBYR4SEVQ0eSpNrX/BT/MdqghTZsnOgAAAABJRU5ErkJggg==",article:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAqVJREFUWEfFlktoE1EUhv+ThynioxV1KQq6URAUunDpQrAVLLZmTBHRlGQyYqlQ3LgIWuxCBDdBk8xMJCBVYRIXgrb42goKLgTRhSKI2k0RWkhJW5s5MsG0SazJncxIZjXMPed8/51z/zNDaPNFbebjvwhIp4295MVNMA4BmAFxdvr79htjY4eX6zfsugBVzUVAfAtAoA72bJ1/vi8cDi9UP3dNQDab7Vj6teE2wEP/aisBzwsF9I2OSsVKjCsCUqkHOz1e70MABwXO1IvpH9t6Ku1wLEDTcj0MngCwRQBeDmFQXJGD49Z9ywKYmXQ9F2fgCgCPKPxP3OeYLO1pWUAyea/L5/NPMNBrE1wJn43JUldLAlKZ/AGPaVr93tUi3EqbislSWbytFmiacY6BFIAOB/B5D5nd0Wjoo7CARGIyEAgUEiDIDsBW6ifTY/afj4TeC9tQVY0dIOQBdDuBE/CIGWdjMWlOeBCldeMIMe4D2OoAXiJQPBo9eZ2IWGgUWxbTMsZlMF1rwWLVjBk2aVBRgi8bTMfaJVU1NoNwF8BxB7u2Ul8v+0rBC0OD3xrVqXGBruf3m1y22G4ncAKlOzv5oiRJS83qrAjQtPw+hvkGwPpmSQ3Wi0ykKNGg9QaFrhUBqm5MgXFUKGutIMYXZrNfUULv7NRYFaAZlj022UmuxDLwOOD3nwmHT8zaza8W8JdFBIqZYL4qy9L4WhYTyF8dxapm2BNA+EnMp2X51FMRUFMb2hHAwFufxzsQiQx8dQK3cu23gDizWNw4PDLSu+gUXi+g1GTqLTBhWIlKd9wAV2pU2zAJLn/tvHUAyx2v2MQlRZE+uAmveQNuFxatZ+uHRLSonbiqUWw8cfCPZ4dpnfxJWZaO1bRA09oswNYWXAxu+xn4DRv63CEjK31nAAAAAElFTkSuQmCC",v:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAApJJREFUWEfNl7tuE0EUhv+z5AGChZAwPEAiCnAKGmioIKLgIrLKAyRrJ6VdUtimJ6KK7YkfIFoiEooooaKBhiJAgZwXSCIhZPIAZg6awRt5zV5mxomEq5XnzH++c5nZswTHX6ezNQOSa3o7e9Vy+fmhixTZbup2w8JvpjqYVwFMDfcPQLR+ibi5tOT3bTSNAer1D1PXrv9cIXADQCHFSZ9BjZOjK61m8/7ABMQIQIg38wx+BWDWRBRAjyWqlYq/n2efCbCxsTkr2VOO5/OEUtb3PJK15eXFXtr+RABVZym5waCVkTo7MmBA4JbnUSOpP2IAhnV2BUnsjzMAhzq7gvQIVAuChT0lcAbQESGnKjK+6DVCycirgX058LVvE4CDy9O4o4z7p9gl4EEWBAPvC9N4pGx+neIzgLkke2MAAj4FgX9PibS6WyVPyoMsAAJKQeB/VTZChB8ZuDsRgLpoWcpSpbL4TQl1RLgN4EkKxE458J+qtXZ78xZ5nipd4kkzzoC+6oHtSuA/y8kCEzAXRd8W4VsCNMykGdAMBlkwjl4JWmXAIAtW0TsB5GTBKnpXgFgvCBHeZuj7wTp6Z4BxZ8MToeqpm20IpY5p7lvWugeiTh49Ecqh+j/q/JwjGjsMzgDjWYhUbaKfpATaHwHvgsCPXUZChDsMPM66JUfXJsmA1mGgdnL0/bV6Lt64+RKMF6bOEzMwfB2rKXfGQigaQNNmxCSpQwJV/3kdK0s1kBSLP1ZBVM8YPC34YqZ9MDePj6+ujw6s/9dINh6aHkrhrYHx0Clswr4HWbUeSsedOYxrsbErCz73xoo2Gw6sF/NhMhpBysieOXqfSwYS++PvRwvyPj4uBMCpKRM2GffAeTkc1/kDgiVyMIKxs7oAAAAASUVORK5CYII="},this.theme="default",this.options=Object.assign({},this.defaultParams,this.inputData),this.getThemeName(),this.renderCss(),this.renderHtml(this.options)}return _createClass(e,[{key:"renderHtml",value:function(){var e=this;this.$dialogDom=$('<div class="passport-login-tip-container '+("dark"==this.theme&&"dark")+'" style="display:none;">\n                            <p class="tit">'+this.options.title+'</p>\n                            <ul>\n                              <li><img src="'+this.icons.code+'" alt="" /><span>免费复制代码</span></li>\n                              <li><img src="'+this.icons.v+'" alt="" /><span>和博主大V互动</span></li>\n                              <li><img src="'+this.icons.download+'" alt="" /><span>下载海量资源</span></li>\n                              <li><img src="'+this.icons.article+'" alt="" /><span>发动态/写文章/加入社区</span></li>\n                            </ul>            \n                          </div>'),this.$closeBtn=$('<span style="display: inline-block; color: #999; font-size: 22px; cursor: pointer; position:absolute; top:6px; right:18px;-moz-user-select:none; -webkit-user-select:none; user-select:none;">&times</span>'),this.$closeBtn.on("click",function(){e.$dialogDom.fadeOut()}),this.$dialogDom.append(this.$closeBtn),this.$loginBtn=$("<button>立即登录</button>"),this.$loginBtn.on("click",function(){e.$dialogDom.fadeOut();var i={spm:"3001.9568"},o={spm:i.spm};p&&p.curCookie&&(i.reportExtra={popupType:p.taskId+"-"+p.curCookie.version},o.extra=JSON.stringify(i.reportExtra)),t(o),window.csdn.loginBox.show(i)}),this.$dialogDom.append(this.$loginBtn),$("body").append(this.$dialogDom),this.$dialogDom.fadeIn();var o={spm:"3001.9568"};this.reportExtra&&(o.extra=JSON.stringify(this.reportExtra)),i(o)}},{key:"getThemeName",value:function(){var e=$('meta[name="toolbar"]');if(e.length){var t=e.attr("content")||{};t=JSON.parse(t),1==t.type&&(this.theme="dark")}}},{key:"renderCss",value:function(){var e=window.document.head,t=e.firstElementChild||e.firstChild,i=".passport-login-tip-container{\n        position: fixed;\n        font-family: -apple-system,SF UI Text,Arial,PingFang SC,Hiragino Sans GB,Microsoft YaHei,WenQuanYi Micro Hei,sans-serif;\n        bottom: "+this.options.bottom+";\n        right: "+this.options.right+";\n        width: "+this.options.width+"px;\n        padding: 24px 16px;\n        background: #fff;\n        color: #555666;\n        box-shadow: 0px 0px 10px 2px rgba(0,0,0,0.06);\n        border-radius: 4px;\n        z-index: 9999;\n      }\n      .passport-login-tip-container.dark { background: #404041; color: #fff; } \n      .passport-login-tip-container p.tit { margin-bottom:16px; font-size: 14px; font-weight: 500;color: #222226; line-height: 22px;} \n      .passport-login-tip-container.dark p.tit { color: #fff; } \n      .passport-login-tip-container ul { display: flex; flex-wrap: wrap; } \n      .passport-login-tip-container ul li { flex: 0 0 50%; margin-bottom: 16px; font-size: 0; }  \n      .passport-login-tip-container ul li span { font-size: 14px; font-weight: 400;  line-height: 22px; vertical-align: middle; }\n      .passport-login-tip-container ul li img { margin-right: 3px; width: 16px; height: 16px; vertical-align: middle; }\n      .passport-login-tip-container button { border: none;margin-top: 8px; width: 100%; height: 40px; background: #FC5531; border-radius: 20px; font-size: 14px; font-weight: 500; color: #FFFFFF; transition: all .2s; line-height: 40px;}\n      .passport-login-tip-container button:hover { background: #FC1944; }\n      ",o=document.createElement("style");o.innerText=i,e.insertBefore(o,t)}}]),e}(),A=function(){function e(t){_classCallCheck(this,e),this.inputData=t,this.defaultParams={frameWidth:459,frameHeight:210,right:"24px",bottom:"24px",title:"登录可享受更多权益",loginService:"https://passport.csdn.net/account/login",pvSource:"",iframeName:"passport-auto-tip",from:encodeURIComponent(window.location.href)},this.reportExtra={autoPopup:!1,pageType:""},t&&t.reportExtra&&(this.reportExtra=Object.assign({},this.reportExtra,t.reportExtra),delete t.reportExtra),this.theme="default",this.options=Object.assign({},this.defaultParams,this.inputData),this.version="loginv3",this.extend="",this.getThemeName(),this.fileExtends(),this.renderCss(),this.renderHtml(this.options)}return _createClass(e,[{key:"fileExtends",value:function(){for(var e in this.inputData)this.defaultParams.hasOwnProperty(e)||(this.extend=this.extend+"&"+e+"="+this.inputData[e])}},{key:"close",value:function(){this.$autoTipLoginDom&&this.$autoTipLoginDom.remove(),window.csdn.loginBox.autoLoginTip=void 0}},{key:"renderCss",value:function(){var e=window.document.head,t=e.firstElementChild||e.firstChild,i=".passport-auto-tip-login-container{\n        position: fixed;\n        font-family: -apple-system,SF UI Text,Arial,PingFang SC,Hiragino Sans GB,Microsoft YaHei,WenQuanYi Micro Hei,sans-serif;\n        bottom: "+this.options.bottom+";\n        right: "+this.options.right+";\n        background: #fff;\n        color: #555666;\n        box-shadow: 0px 0px 10px 2px rgba(0,0,0,0.06);\n        border-radius: 4px;\n        z-index: 9999;\n      }\n      ",o=document.createElement("style");o.innerText=i,e.insertBefore(o,t)}},{key:"renderHtml",value:function(){var e=this,t=this.options.loginService;t+=(-1===t.indexOf("?")?"?from=":"&from=")+this.options.from,t+="&showTip=true&isBlackTheme="+("dark"===this.theme),t+="&iframe=true",t+="&newframe=true",t=this.version?t+"&version="+this.version:t,t=this.extend?t+this.extend:t,this.$autoTipLoginDom=$('<div class="passport-auto-tip-login-container" style="display: block;'+(this.options.frameHeight?"height:"+this.options.frameHeight+"px":"")+'"></div>'),this.$tipIframeHtml=$('<iframe  width="'+this.options.frameWidth+'" height="'+this.options.frameHeight+'" name="'+this.options.iframeName+'" src="'+t+'" style="border-radius: 8px;" frameborder="0" scrolling="no"></iframe>'),this.$closeBtn=$('<span style="display: inline-block; color: #999; font-size: 22px; cursor: pointer; position:absolute; top:6px; right:18px;-moz-user-select:none; -webkit-user-select:none; user-select:none;">&times</span>'),this.$closeBtn.on("click",function(){e.$autoTipLoginDom.fadeOut()}),this.$autoTipLoginDom.append(this.$closeBtn),this.$autoTipLoginDom.append(this.$tipIframeHtml),$("body").append(this.$autoTipLoginDom),this.$autoTipLoginDom.fadeIn();var o={spm:"1001.2101.3001.9568"};this.reportExtra&&(o.extra=JSON.stringify(this.reportExtra)),i(o)}},{key:"getThemeName",value:function(){var e=$('meta[name="toolbar"]');if(e.length){var t=e.attr("content")||{};t=JSON.parse(t),1==t.type&&(this.theme="dark")}}}]),e}();window.csdn.loginBox={self:void 0,self2:void 0,autoLoginTip:void 0,showTip:function(e){new w(e)},showAutoTip:function(e){this.autoLoginTip||(this.autoLoginTip=new A(e))},show:function(t){var i=n();if(e("UserName")&&!i)return void void 0;this.self||(this.self=new s(t))},key:function(e){this.self2||(this.self2=new f(e))},close:function(){return this.self.close()},loginout:function(e){if(h())return m(),!1;var t=Object.assign({},e);return new Promise(function(e,i){$.ajax({type:"post",url:"https://passport.csdn.net/account/logout",data:JSON.stringify(t),crossDomain:!0,contentType:"application/json",xhrFields:{withCredentials:!0},success:function(t){e(t)},error:function(e){i(e)}})})},setlogin:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{UserName:void 0,UserToken:void 0},t=e.UserName||d("UserName"),i=e.UserToken||d("UserToken"),o=Object.assign({},{username:t,userToken:i});return new Promise(function(e,t){$.ajax({type:"post",url:"https://passport.csdn.net/v1/login/wap/userToken/refresh",data:JSON.stringify(o),crossDomain:!0,contentType:"application/json",xhrFields:{withCredentials:!0},success:function(t){e(t)},error:function(e){t(e)}})})}};var k=[{name:"blog-threeH-dialog",time:108e5,site:["blog"],showType:"dialog",tip:"登录后有海量资源下载哦",dynamicRule:["halfArticleOrDoubleScreen"],dynamicTipRule:["autoShowTip"],platform:["pc"],ab:"exp1",showTipTimes:3},{name:"blog-threeH-dialog2",time:108e5,site:["blog"],showType:"dialog",tip:"登录后有海量资源下载哦",dynamicRule:["halfArticleOrDoubleScreen"],dynamicTipRule:["autoShowTip"],platform:["pc"],ab:"exp2",showTipTimes:3},{name:"blog-threeH-tip",platform:["pc"],site:["blog"],time:108e5,showType:"tip",dynamicRule:["halfArticleOrDoubleScreen"],ab:"exp3"},{name:"blog-auto-tip",platform:["pc"],site:["blog"],time:108e5,showType:"autoTip",dynamicRule:["autoShowTip"],ab:"exp4"},{name:"blog-threeH-default",platform:["pc"],site:["blog"],time:108e5,showType:"dialog",dynamicRule:["unloginScrollStep"],isDefault:!0,ab:"control"},{name:"blog-threeH-dialog-expa",time:108e5,site:["blog"],showType:"dialog",tip:"登录后有海量资源下载哦",dynamicRule:["halfArticleOrDoubleScreen"],platform:["pc"],params:{showThird:!0,toUn:!0},ab:"ExpA"},{name:"blog-threeH-dialog-expb",time:108e5,site:["blog"],showType:"dialog",tip:"登录后有海量资源下载哦",dynamicRule:["halfArticleOrDoubleScreen"],platform:["pc"],params:{toUn:!0},ab:"ExpB"},{name:"blog-threeH-dialog-exp11",time:[36e5,0],site:["blog"],showType:"dialog",tip:"登录后有海量资源下载哦",dynamicRule:["halfArticleOrDoubleScreen","articleViewDeepStrategy"],dynamicTipRule:["autoShowTip"],platform:["pc"],ab:"exp11",showTipTimes:3},{name:"blog-threeH-dialog-control11",time:108e5,site:["blog"],showType:"dialog",tip:"登录后有海量资源下载哦",dynamicRule:["halfArticleOrDoubleScreen"],dynamicTipRule:["autoShowTip"],platform:["pc"],ab:"control11",showTipTimes:3}],y=[{name:"blog-wap-auto",platform:["wap"],site:["blog","download"],time:0,showType:"dialog",dynamicRule:["unLoginWap"],isDefault:!0,ab:"controlWap"},{name:"blog-wap-v2",platform:["wap"],site:["blog","download"],time:0,showType:"dialog",dynamicRule:["unLoginWapByUn"],params:{unVersion:"v2",toUn:!0},ab:"expA"}],v=function(){function t(){if(_classCallCheck(this,t),!e("UserName")){this.hasUn=Boolean(e("UN"))||!1,this.debug=!1,this.taskId=this.hasUn?310:308,this.abAPICheckTime=324e5,this.COOKIE_NAME="loginbox_wap_strategy",this.onDayTimeStamp=864e5,this.loginBoxStrategy=[],this.curCookie=e(this.COOKIE_NAME)||"{}";try{this.curCookie=JSON.parse(this.curCookie),void 0}catch(e){c(this.COOKIE_NAME,""),void 0,this.curCookie={}}this.taskId?(this.curCookie.taskId&&this.taskId!==this.curCookie.taskId&&(this.curCookie={}),this.curCookie.taskId=this.taskId,this.updateCookie()):(this.curCookie.taskId||this.curCookie.version)&&(delete this.curCookie.taskId,delete this.curCookie.version,delete this.curCookie.abCheckTime,this.updateCookie()),this.writeLog(this.curCookie),this.init()}}return _createClass(t,[{key:"writeLog",value:function(){if(this.debug){var e;return(e=console).log.apply(e,arguments)}}},{key:"init",value:function(){var e=this;if(this.taskId){this.curCookie.abCheckTime=this.curCookie.abCheckTime||0;var t=+new Date-this.curCookie.abCheckTime>this.abAPICheckTime;if(this.curCookie.version&&!t){var i=y.find(function(t){return t.ab===e.curCookie.version});i||(i=y.find(function(e){return e.isDefault})),i&&this.checkStrategy(i)}else this.getVersion(function(t){var i=void 0;t&&t.version&&(e.curCookie.version=t.version,e.curCookie.nickName=t.nickname,e.curCookie.abCheckTime=+new Date,c(e.COOKIE_NAME,JSON.stringify(e.curCookie)),i=y.find(function(e){return e.ab===t.version})),i||(i=y.find(function(e){return e.isDefault})),i&&e.checkStrategy(i)})}else{var o=y.find(function(e){return e.isDefault});o&&this.checkStrategy(o)}}},{key:"updateCookie",value:function(e){e=e||this.curCookie,c(this.COOKIE_NAME,JSON.stringify(e))}},{key:"getVersion",value:function(t){var i="https://passport.csdn.net/v1/login/abtest/version/get?taskId="+this.taskId;this.hasUn&&(i+="&un="+e("UN")),$.ajax({type:"get",url:i,crossDomain:!0,xhrFields:{withCredentials:!0},contentType:"application/json",success:function(e){e.status?t(e.data):t()},error:function(){t()}})}},{key:"isCsdnSites",value:function(e){e=e||["blog","so","edu","download","ask","bbs"]
;var t=location.hostname||location.host,i=t.split("."),o=_slicedToArray(i,1),n=o[0],s=location.pathname.indexOf("/article/details")>=0,a=location.pathname.indexOf("/download/")>=0;return e.indexOf("blog")>-1?e.indexOf("download")>-1?!a:s||e.indexOf(n)>-1:e.indexOf(n)>-1}},{key:"getTomorrowTimestamp",value:function(){var e=new Date;return e.setDate(e.getDate()+1),e.setHours(0,0,0,0),e.getTime()}},{key:"checkStrategy",value:function(e){var t=e?[e]:this.loginBoxStrategy,i=!0,n=!1,s=void 0;try{for(var a,r=t[Symbol.iterator]();!(i=(a=r.next()).done);i=!0){var p=a.value,l=p.platform,c=p.site,d=p.name,h=p.time,u=o()?"wap":"pc";if(-1===l.indexOf(u))break;this.writeLog(">>>>平台检查完成");if(!this.isCsdnSites(c))break;this.writeLog(">>>>网站检查完成");var m=!1,g=+new Date,f=this.curCookie[d]||"";if("number"==typeof h&&g-f>h&&(m=!0),"string"==typeof h&&h.indexOf("day")>-1){var w=+h.replace("day","");g=this.getTomorrowTimestamp(),g-f>w*this.onDayTimeStamp&&(m=!0)}if(!m)break;this.writeLog(">>>>时间检查完成"),this.curCookie[d]=g,this.onStrategyPass(p,this.updateCookie)}}catch(e){n=!0,s=e}finally{try{!i&&r.return&&r.return()}finally{if(n)throw s}}}},{key:"onStrategyPass",value:function(e,t){var i=this,o=e.showType,n=e.dynamicRule,s=e.params,a=e.ab,r=(n||[]).reduce(function(e,t){return e[t]=!1,e},{}),p=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"dialog"===o&&(e=Object.assign({},s,{reportExtra:{autoPopup:!0,popupType:i.taskId+"-"+(i.curCookie.version||a)},nickname:i.curCookie.nickName},e),csdn.loginBox.show(e)),t.call(i)};if(!n||!n.length)return void p();var l=function(e){n.every(function(e){return r[e]})&&p(e)};n.forEach(function(e){i[e](function(t){r[e]=!0,l(t)})})}},{key:"unLoginWap",value:function(t){var i=location.pathname.indexOf("/article/details")>=0,o=location.href.indexOf("https://download.csdn.net/")>=0;(i||o)&&$(document).ready(function(){if(i){var o=e("popTimes"),n=e("popShowed10s"),s=e("popShowed3t"),a=setTimeout(function(){n?(clearTimeout(a),a=null):(t({spm:"3001.7902"}),c("popShowed10s","yes","toToday24h"),clearTimeout(a),a=null)},1e4);o?"one"===o?c("popTimes","two","toToday24h"):s||(t({spm:"3001.7961"}),c("popShowed3t","yes","toToday24h")):c("popTimes","one","toToday24h")}else{var r=e("downloadPopShowed");if(r)return;t({spm:"3001.8532"}),r||c("downloadPopShowed","yes","toToday24h")}})}},{key:"unLoginWapByUn",value:function(t){var i=location.pathname.indexOf("/article/details")>=0,o=location.href.indexOf("https://download.csdn.net/")>=0;(i||o)&&$(document).ready(function(){if(i){var o=e("popTimes"),n=e("popShowed10s"),s=e("popShowed3t"),a=setTimeout(function(){n?(clearTimeout(a),a=null):(t({spm:"3001.7902"}),c("popShowed10s","yes","toToday24h"),clearTimeout(a),a=null)},1e4);o?"one"===o?c("popTimes","two","toToday24h"):s||(t({spm:"3001.7961"}),c("popShowed3t","yes","toToday24h")):c("popTimes","one","toToday24h")}else{var r=e("downloadPopShowed");if(r)return;t({spm:"3001.8532"}),r||c("downloadPopShowed","yes","toToday24h")}}),$(document).on("visibilitychange ready",function(o){e("popShowed3m")||("visible"===o.target.visibilityState?a=window.setInterval(function(){var o=e("popShowed3m"),n=+e("unloadshowm")||0;o?(window.clearInterval(a),a=null):n>=3&&i?(t(),c("popShowed3m","yes","toToday24h")):(n++,c("unloadshowm",n,"toToday24h"))},6e4):"hidden"===o.target.visibilityState&&(window.clearInterval(a),a=null))})}}]),t}(),b=function(){function t(){if(_classCallCheck(this,t),!e("UserName")){this.hasUn=Boolean(e("UN"))||!1,this.debug=!1,this.taskId=this.hasUn?317:349,this.abAPICheckTime=324e5,this.COOKIE_NAME="loginbox_strategy",this.onDayTimeStamp=864e5,this.loginBoxStrategy=[],this.curCookie=e(this.COOKIE_NAME)||"{}";try{this.curCookie=JSON.parse(this.curCookie),void 0}catch(e){c(this.COOKIE_NAME,""),void 0,this.curCookie={}}this.taskId?(this.curCookie.taskId&&this.taskId!==this.curCookie.taskId&&(this.curCookie={}),this.curCookie.taskId=this.taskId,this.updateCookie()):(this.curCookie.taskId||this.curCookie.version)&&(delete this.curCookie.taskId,delete this.curCookie.version,delete this.curCookie.abCheckTime,this.updateCookie()),this.writeLog(this.curCookie),this.init()}}return _createClass(t,[{key:"writeLog",value:function(){if(this.debug){var e;return(e=console).log.apply(e,arguments)}}},{key:"init",value:function(){var e=this;if(this.taskId){this.curCookie.abCheckTime=this.curCookie.abCheckTime||0;var t=+new Date-this.curCookie.abCheckTime>this.abAPICheckTime;if(this.curCookie.version&&!t){var i=k.find(function(t){return t.ab===e.curCookie.version});i||(i=k.find(function(e){return e.isDefault})),i&&this.checkStrategy(i)}else this.getVersion(function(t){var i=void 0;t&&t.version&&(e.curCookie.version=t.version,e.curCookie.nickName=t.nickname,e.curCookie.abCheckTime=+new Date,c(e.COOKIE_NAME,JSON.stringify(e.curCookie)),i=k.find(function(e){return e.ab===t.version})),i||(i=k.find(function(e){return e.isDefault})),i&&e.checkStrategy(i)})}else{var o=k.find(function(e){return e.isDefault});o&&this.checkStrategy(o)}}},{key:"updateCookie",value:function(e){e=e||this.curCookie,c(this.COOKIE_NAME,JSON.stringify(e))}},{key:"getVersion",value:function(t){var i="https://passport.csdn.net/v1/login/abtest/version/get?taskId="+this.taskId;this.hasUn&&(i+="&un="+e("UN")),$.ajax({type:"get",url:i,crossDomain:!0,xhrFields:{withCredentials:!0},contentType:"application/json",success:function(e){e.status?t(e.data):t()},error:function(){t()}})}},{key:"isCsdnSites",value:function(e){e=e||["blog","so","edu","download","ask","bbs"];var t=location.hostname||location.host,i=t.split("."),o=_slicedToArray(i,1),n=o[0],s=location.pathname.indexOf("/article/details")>=0;return e.indexOf("blog")>-1?s||e.indexOf(n)>-1:e.indexOf(n)>-1}},{key:"getTomorrowTimestamp",value:function(){var e=new Date;return e.setDate(e.getDate()+1),e.setHours(0,0,0,0),e.getTime()}},{key:"checkStrategy",value:function(e){var t=this,i=e?[e]:this.loginBoxStrategy,n=!0,s=!1,a=void 0;try{for(var r,p=i[Symbol.iterator]();!(n=(r=p.next()).done);n=!0){if("break"===function(){var e=r.value,i=e.platform,n=e.site,s=e.name,a=e.time,p=e.times,l=e.showTipTimes,c=e.dynamicRule,d=o()?"wap":"pc";if(-1===i.indexOf(d))return"break";if(t.writeLog(">>>>平台检查完成"),!t.isCsdnSites(n))return"break";t.writeLog(">>>>网站检查完成");var h=!1,u=!1,m=+new Date,g=t.curCookie[s]||"";if("number"==typeof a&&m-g>a&&(h=!0,u=!0),"string"==typeof a&&a.indexOf("day")>-1){var f=+a.replace("day","");m=t.getTomorrowTimestamp(),m-g>f*t.onDayTimeStamp&&(h=!0,u=!0)}if(Array.isArray(a)){var w=[];a.forEach(function(e,t){m-g>e&&w.push(c[t])}),w.length>0&&(h=!0,u=e.dynamicRule.length==w.length,e.dynamicRule=w)}if(l>=0){var A=t.curCookie[s+"tipShowTimes"]||0;u?""===g?(A<l?(t.curCookie[s+"tipShowTimes"]=++A,t.onStrategyPassByShowTimes(e,t.updateCookie.bind(t,JSON.parse(JSON.stringify(t.curCookie)))),t.curCookie[s]=m):(t.curCookie[s+"tipShowTimes"]=++A,t.updateCookie(JSON.parse(JSON.stringify(t.curCookie))),t.curCookie[s]=m),t.onStrategyPass(e,t.updateCookie)):(t.curCookie[s+"tipShowTimes"]=1,t.curCookie[s]="",t.onStrategyPassByShowTimes(e,t.updateCookie.bind(t,JSON.parse(JSON.stringify(t.curCookie)))),t.curCookie[s]=m,t.onStrategyPass(e,t.updateCookie)):(A<l?(t.curCookie[s+"tipShowTimes"]=++A,t.onStrategyPassByShowTimes(e,t.updateCookie)):(t.curCookie[s+"tipShowTimes"]=++A,t.updateCookie()),h&&t.onStrategyPass(e,t.updateCookie))}else{if(!h)return"break";if(t.writeLog(">>>>时间检查完成"),p){var k=+t.curCookie[s+"Times"]||0;p===k+1?(t.curCookie[s]=m,t.curCookie[s+"Times"]=0,t.onStrategyPass(e,t.updateCookie)):(t.curCookie[s+"Times"]=++k,t.updateCookie())}else t.curCookie[s]=m,t.onStrategyPass(e,t.updateCookie)}}())break}}catch(e){s=!0,a=e}finally{try{!n&&p.return&&p.return()}finally{if(s)throw a}}}},{key:"halfArticleOrDoubleScreen",value:function(e){var t=this,i=function i(){var o="CSS1Compat"==document.compatMode?document.documentElement.clientHeight:document.body.clientHeight,n=Math.max(document.body.scrollTop,document.documentElement.scrollTop),s=n+o,a=s/document.documentElement.scrollHeight*100;t.writeLog("滚动高度 / 窗口高度",n,o,n/o),t.writeLog("百分比",a),(n/o>2||a>50)&&(e(),document.removeEventListener("scroll",i))};document.addEventListener("scroll",i)}},{key:"articleViewDeepStrategy",value:function(t){$(document).ready(function(){var i=e("popPageViewTimes"),o=i?++i:1;10!=o&&11!=o||t({showProgressCd:!0}),c("popPageViewTimes",o,"toToday24h")})}},{key:"unloginScrollStep",value:function(e){var t=$("div.article_content");if(t&&t.length){var i=t.offset().top,o=t.height(),n=document.body.clientHeight||document.documentElement.clientHeight,s=$(document).scrollTop(),a=function t(){((s=$(document).scrollTop())+n-i>o/2||s+n-i>2*n)&&(e(),document.removeEventListener("scroll",t))};document.addEventListener("scroll",a)}}},{key:"autoShowTip",value:function(e){e()}},{key:"showOtherBox",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};e=Object.assign({},e,{spm:"1001.2101.3001.9568",reportExtra:{autoPopup:!0,popupType:this.taskId+"-"+(this.curCookie.version||t),pageType:"popup"}}),"exp2"===t?(csdn.loginBox.showAutoTip(e),i.call(this)):(csdn.loginBox.showTip(e),i.call(this))}},{key:"onStrategyPassByShowTimes",value:function(e,t){var i=this,o=e.dynamicTipRule,n=e.ab;o.forEach(function(e){i[e](function(e){i.showOtherBox(e,n,t)})})}},{key:"onStrategyPass",value:function(e,t){var i=this,o=e.showType,n=e.dynamicRule,s=e.params,a=e.ab,r=(n||[]).reduce(function(e,t){return e[t]=!1,e},{}),p=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"dialog"===o&&(n=Object.assign({},n,s,{spm:"3001.6428",reportExtra:{autoPopup:!0,popupType:i.taskId+"-"+(i.curCookie.version||a)},nickname:i.curCookie.nickName}),e.tip&&(n.tip=e.tip),e.tabSubTitle&&e.tabSubTitleType&&(n.tabSubTitle=e.tabSubTitle,n.tabSubTitleType=e.tabSubTitleType),e.guideText&&(n.guideText=e.guideText),csdn.loginBox.show(n)),"tip"===o&&(n=Object.assign({},n,s,{spm:"1001.2101.3001.9568",reportExtra:{autoPopup:!0,popupType:i.taskId+"-"+(i.curCookie.version||a),pageType:"popup"},nickname:i.curCookie.nickName}),csdn.loginBox.showTip(n)),"autoTip"===o&&(n=Object.assign({},n,s,{spm:"1001.2101.3001.9568",reportExtra:{autoPopup:!0,popupType:i.taskId+"-"+(i.curCookie.version||a),pageType:"popup"},nickname:i.curCookie.nickName}),csdn.loginBox.showAutoTip(n)),t.call(i)};if(!n||!n.length)return void p();var l=function(e){p(e)};n.forEach(function(e){i[e](function(t){r[e]=!0,l(t)})})}}]),t}();p=o()?new v:new b,function(){if(d("showBox")){var e={guideText:"账号安全提示",tabSubTitle:["您长期未活跃，为了您的账号安全","请先登录"],spm:"3001.10094"};csdn.loginBox.show(e)}}(),$(document).on("click",".c-login-check",function(t){r=$(this).data("reportClick")||$(this).parent().data("reportClick")||$(this).parent().parent().data("reportClick")||{},e("UserName")||(t.stopPropagation(),window.csdn.loginBox.show())}),function(){var t=n(),i=window.location.pathname,s=i.indexOf("/article/details")>=0,r=e("UserName");s&&o()&&!h()&&r&&t&&($(document).ready(function(){var t=e("keyShowed10s"),i=e("keyShowed3t"),o=e("keyTimes"),n=setTimeout(function(){if(t)return clearTimeout(n),void(n=null);window.csdn.loginBox.key({spm:"3001.7962",nickname:r}),t||c("keyShowed10s","yes","toToday24h"),clearTimeout(n),n=null},1e4);return o?"one"===o?void c("keyTimes","two","toToday24h"):void(i||(window.csdn.loginBox.key({spm:"3001.7963",nickname:r}),c("keyShowed3t","yes","toToday24h"))):void c("keyTimes","one","toToday24h")}),$(document).on("visibilitychange ready",function(t){e("keyShowed3m")||("visible"===t.target.visibilityState?a=window.setInterval(function(){var t=e("keyShowed3m"),i=+e("unloadshowm")||0;t?(window.clearInterval(a),a=null):i>=3&&s?(window.csdn.loginBox.key({spm:"3001.8531",nickname:r}),c("keyShowed3m","yes","toToday24h")):(i++,c("unloadshowm",i,"toToday24h"))},6e4):"hidden"===t.target.visibilityState&&(window.clearInterval(a),a=null))}))}(),window.addEventListener("message",function(e){switch(void 0,e.data){case"key-close":window.csdn.loginBox.self2&&window.csdn.loginBox.self2.close();break;case"im_client":window.csdn.loginBox.self2&&window.csdn.loginBox.self2.close(),window.open("https://csdn.s2.udesk.cn/im_client/?web_plugin_id=29181");break;case"pop":window.csdn.loginBox.self2&&window.csdn.loginBox.self2.close(),window.csdn.loginBox.show();break;case"page_reload":window.csdn.loginBox.self2&&window.csdn.loginBox.self2.close(),window.location.reload();break;case"page_reload_onclose":window.csdn.loginBox.self&&window.csdn.loginBox.self.changeCloseReLoadSwitch(1);break;case"pay-cb":window.csdn.loginBox.self&&window.csdn.loginBox.self.cb&&window.csdn.loginBox.self.cb();break;case"pay-data":window.csdn.loginBox.self&&window.csdn.loginBox.self.$iframeHtml[0].contentWindow.postMessage({payInfo:window.csdn.loginBox.self.payInfo},"*");break;case"show_login_box_by_tip":window.csdn.loginBox.autoLoginTip&&window.csdn.loginBox.autoLoginTip.close();var i={spm:"1001.2101.3001.9568"},o={spm:i.spm};p&&p.curCookie&&(i.reportExtra={popupType:p.taskId+"-"+p.curCookie.version},o.extra=JSON.stringify(i.reportExtra)),t(o),window.csdn.loginBox.show(i);break;case"close_activate_page":window.csdn.loginBox.self&&window.csdn.loginBox.self.close();break;default:return!1}},!1)}();