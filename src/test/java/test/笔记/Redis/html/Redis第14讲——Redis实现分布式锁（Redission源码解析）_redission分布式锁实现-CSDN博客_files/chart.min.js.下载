(function(){var l=this,d=l.Chart,c=function(a){this.canvas=a.canvas;this.ctx=a;var g=function(a,g){return a["offset"+g]?a["offset"+g]:document.defaultView.getComputedStyle(a).getPropertyValue(g)};this.width=g(a.canvas,"Width")||a.canvas.width;this.height=g(a.canvas,"Height")||a.canvas.height;return this.aspectRatio=this.width/this.height,b.retinaScale(this),this};c.defaults={global:{animation:!0,animationSteps:60,animationEasing:"easeOutQuart",showScale:!0,scaleOverride:!1,scaleSteps:null,scaleStepWidth:null,
scaleStartValue:null,scaleLineColor:"rgba(0,0,0,.1)",scaleLineWidth:1,scaleShowLabels:!0,scaleLabel:"\x3c%\x3dvalue%\x3e",scaleIntegersOnly:!0,scaleBeginAtZero:!1,scaleFontFamily:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",scaleFontSize:12,scaleFontStyle:"normal",scaleFontColor:"#666",responsive:!1,maintainAspectRatio:!0,showTooltips:!0,customTooltips:!1,tooltipEvents:["mousemove","touchstart","touchmove","mouseout"],tooltipFillColor:"rgba(0,0,0,0.8)",tooltipFontFamily:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",
tooltipFontSize:14,tooltipFontStyle:"normal",tooltipFontColor:"#fff",tooltipTitleFontFamily:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",tooltipTitleFontSize:14,tooltipTitleFontStyle:"bold",tooltipTitleFontColor:"#fff",tooltipTitleTemplate:"\x3c%\x3d label%\x3e",tooltipYPadding:6,tooltipXPadding:6,tooltipCaretSize:8,tooltipCornerRadius:6,tooltipXOffset:10,tooltipTemplate:"\x3c%if (label){%\x3e\x3c%\x3dlabel%\x3e: \x3c%}%\x3e\x3c%\x3d value %\x3e",multiTooltipTemplate:"\x3c%\x3d datasetLabel %\x3e: \x3c%\x3d value %\x3e",
multiTooltipKeyBackground:"#fff",segmentColorDefault:"#A6CEE3 #1F78B4 #B2DF8A #33A02C #FB9A99 #E31A1C #FDBF6F #FF7F00 #CAB2D6 #6A3D9A #B4B482 #B15928".split(" "),segmentHighlightColorDefaults:"#CEF6FF #47A0DC #DAFFB2 #5BC854 #FFC2C1 #FF4244 #FFE797 #FFA728 #F2DAFE #9265C2 #DCDCAA #D98150".split(" "),onAnimationProgress:function(){},onAnimationComplete:function(){}}};c.types={};var b=c.helpers={},e=b.each=function(a,g,f){var b=Array.prototype.slice.call(arguments,3);if(a)if(a.length===+a.length){var e;
for(e=0;e<a.length;e++)g.apply(f,[a[e],e].concat(b))}else for(e in a)g.apply(f,[a[e],e].concat(b))},h=b.clone=function(a){var g={};return e(a,function(f,b){a.hasOwnProperty(b)&&(g[b]=f)}),g},k=b.extend=function(a){return e(Array.prototype.slice.call(arguments,1),function(g){e(g,function(f,b){g.hasOwnProperty(b)&&(a[b]=f)})}),a},u=b.merge=function(a,g){var f=Array.prototype.slice.call(arguments,0);return f.unshift({}),k.apply(null,f)},G=b.indexOf=function(a,g){if(Array.prototype.indexOf)return a.indexOf(g);
for(var f=0;f<a.length;f++)if(a[f]===g)return f;return-1},n=(b.where=function(a,g){var f=[];return b.each(a,function(a){g(a)&&f.push(a)}),f},b.findNextWhere=function(a,g,f){f||(f=-1);for(f+=1;f<a.length;f++){var b=a[f];if(g(b))return b}},b.findPreviousWhere=function(a,g,f){f||(f=a.length);for(--f;0<=f;f--){var b=a[f];if(g(b))return b}},b.inherits=function(a){var g=this,f=a&&a.hasOwnProperty("constructor")?a.constructor:function(){return g.apply(this,arguments)},b=function(){this.constructor=f};return b.prototype=
g.prototype,f.prototype=new b,f.extend=n,a&&k(f.prototype,a),f.__super__=g.prototype,f}),m=b.noop=function(){},H=b.uid=function(){var a=0;return function(){return"chart-"+a++}}(),I=b.warn=function(a){window.console&&"function"==typeof window.console.warn&&console.warn(a)},J=b.amd="function"==typeof define&&define.amd,q=b.isNumber=function(a){return!isNaN(parseFloat(a))&&isFinite(a)},x=b.max=function(a){return Math.max.apply(Math,a)},v=b.min=function(a){return Math.min.apply(Math,a)},C=(b.cap=function(a,
g,f){if(q(g)){if(a>g)return g}else if(q(f)&&f>a)return f;return a},b.getDecimalPlaces=function(a){if(0!==a%1&&q(a)){a=a.toString();if(0>a.indexOf("e-"))return a.split(".")[1].length;if(0>a.indexOf("."))return parseInt(a.split("e-")[1]);a=a.split(".")[1].split("e-");return a[0].length+parseInt(a[1])}return 0}),z=b.radians=function(a){return Math.PI/180*a},D=(b.getAngleFromPoint=function(a,g){var f=g.x-a.x,b=g.y-a.y,e=Math.sqrt(f*f+b*b),c=2*Math.PI+Math.atan2(b,f);return 0>f&&0>b&&(c+=2*Math.PI),{angle:c,
distance:e}},b.aliasPixel=function(a){return 0===a%2?0:.5}),K=(b.splineCurve=function(a,g,f,b){var e=Math.sqrt(Math.pow(g.x-a.x,2)+Math.pow(g.y-a.y,2)),c=Math.sqrt(Math.pow(f.x-g.x,2)+Math.pow(f.y-g.y,2)),d=b*e/(e+c);b=b*c/(e+c);return{inner:{x:g.x-d*(f.x-a.x),y:g.y-d*(f.y-a.y)},outer:{x:g.x+b*(f.x-a.x),y:g.y+b*(f.y-a.y)}}},b.calculateOrderOfMagnitude=function(a){return Math.floor(Math.log(a)/Math.LN10)}),r=(b.calculateScaleRange=function(a,g,f,b,c){g=Math.floor(g/(1.5*f));f=2>=g;var d=[];e(a,function(a){null==
a||d.push(a)});var h=v(d),k=x(d);k===h&&(k+=.5,.5<=h&&!b?h-=.5:k+=.5);a=Math.abs(k-h);a=K(a);k=Math.ceil(k/(1*Math.pow(10,a)))*Math.pow(10,a);b=b?0:Math.floor(h/(1*Math.pow(10,a)))*Math.pow(10,a);for(var h=k-b,k=Math.pow(10,a),p=Math.round(h/k);(p>g||g>2*p)&&!f;)if(p>g)k*=2,p=Math.round(h/k),0!==p%1&&(f=!0);else{if(c&&0<=a&&0!==k/2%1)break;k/=2;p=Math.round(h/k)}return f&&(p=2,k=h/p),{steps:p,stepValue:k,min:b,max:b+p*k}},b.template=function(a,g){if(a instanceof Function)return a(g);var f={},f=/\W/.test(a)?
new Function("obj","var p\x3d[],print\x3dfunction(){p.push.apply(p,arguments);};with(obj){p.push('"+a.replace(/[\r\t\n]/g," ").split("\x3c%").join("\t").replace(/((^|%>)[^\t]*)'/g,"$1\r").replace(/\t=(.*?)%>/g,"',$1,'").split("\t").join("');").split("%\x3e").join("p.push('").split("\r").join("\\'")+"');}return p.join('');"):f[a]=f[a];return g?f(g):f}),w=(b.generateLabels=function(a,g,f,b){var c=Array(g);return a&&e(c,function(g,e){c[e]=r(a,{value:f+b*(e+1)})}),c},b.easingEffects={linear:function(a){return a},
easeInQuad:function(a){return a*a},easeOutQuad:function(a){return-1*a*(a-2)},easeInOutQuad:function(a){return 1>(a/=.5)?.5*a*a:-.5*(--a*(a-2)-1)},easeInCubic:function(a){return a*a*a},easeOutCubic:function(a){return 1*((a=a/1-1)*a*a+1)},easeInOutCubic:function(a){return 1>(a/=.5)?.5*a*a*a:.5*((a-=2)*a*a+2)},easeInQuart:function(a){return a*a*a*a},easeOutQuart:function(a){return-1*((a=a/1-1)*a*a*a-1)},easeInOutQuart:function(a){return 1>(a/=.5)?.5*a*a*a*a:-.5*((a-=2)*a*a*a-2)},easeInQuint:function(a){return 1*
(a/=1)*a*a*a*a},easeOutQuint:function(a){return 1*((a=a/1-1)*a*a*a*a+1)},easeInOutQuint:function(a){return 1>(a/=.5)?.5*a*a*a*a*a:.5*((a-=2)*a*a*a*a+2)},easeInSine:function(a){return-1*Math.cos(a/1*(Math.PI/2))+1},easeOutSine:function(a){return 1*Math.sin(a/1*(Math.PI/2))},easeInOutSine:function(a){return-.5*(Math.cos(Math.PI*a/1)-1)},easeInExpo:function(a){return 0===a?1:1*Math.pow(2,10*(a/1-1))},easeOutExpo:function(a){return 1===a?1:1*(-Math.pow(2,-10*a/1)+1)},easeInOutExpo:function(a){return 0===
a?0:1===a?1:1>(a/=.5)?.5*Math.pow(2,10*(a-1)):.5*(-Math.pow(2,-10*--a)+2)},easeInCirc:function(a){return 1<=a?a:-1*(Math.sqrt(1-(a/=1)*a)-1)},easeOutCirc:function(a){return 1*Math.sqrt(1-(a=a/1-1)*a)},easeInOutCirc:function(a){return 1>(a/=.5)?-.5*(Math.sqrt(1-a*a)-1):.5*(Math.sqrt(1-(a-=2)*a)+1)},easeInElastic:function(a){var g=1.70158,f=0,b=1;return 0===a?0:1==(a/=1)?1:(f||(f=.3),b<Math.abs(1)?(b=1,g=f/4):g=f/(2*Math.PI)*Math.asin(1/b),-(b*Math.pow(2,10*--a)*Math.sin(2*(1*a-g)*Math.PI/f)))},easeOutElastic:function(a){var g=
1.70158,b=0,e=1;return 0===a?0:1==(a/=1)?1:(b||(b=.3),e<Math.abs(1)?(e=1,g=b/4):g=b/(2*Math.PI)*Math.asin(1/e),e*Math.pow(2,-10*a)*Math.sin(2*(1*a-g)*Math.PI/b)+1)},easeInOutElastic:function(a){var g=1.70158,b=0,e=1;return 0===a?0:2==(a/=.5)?1:(b||(b=.3*1.5),e<Math.abs(1)?(e=1,g=b/4):g=b/(2*Math.PI)*Math.asin(1/e),1>a?-.5*e*Math.pow(2,10*--a)*Math.sin(2*(1*a-g)*Math.PI/b):e*Math.pow(2,-10*--a)*Math.sin(2*(1*a-g)*Math.PI/b)*.5+1)},easeInBack:function(a){return 1*(a/=1)*a*(2.70158*a-1.70158)},easeOutBack:function(a){return 1*
((a=a/1-1)*a*(2.70158*a********)+1)},easeInOutBack:function(a){var g=1.70158;return 1>(a/=.5)?.5*a*a*(((g*=1.525)+1)*a-g):.5*((a-=2)*a*(((g*=1.525)+1)*a+g)+2)},easeInBounce:function(a){return 1-w.easeOutBounce(1-a)},easeOutBounce:function(a){return(a/=1)<1/2.75?7.5625*a*a:2/2.75>a?1*(7.5625*(a-=1.5/2.75)*a+.75):2.5/2.75>a?1*(7.5625*(a-=2.25/2.75)*a+.9375):1*(7.5625*(a-=2.625/2.75)*a+.984375)},easeInOutBounce:function(a){return.5>a?.5*w.easeInBounce(2*a):.5*w.easeOutBounce(2*a-1)+.5}}),E=b.requestAnimFrame=
function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(a){return window.setTimeout(a,1E3/60)}}(),L=(b.cancelAnimFrame=function(){return window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.oCancelAnimationFrame||window.msCancelAnimationFrame||function(a){return window.clearTimeout(a,1E3/60)}}(),b.animationLoop=function(a,
g,b,e,c,d){var h=0,k=w[b]||w.linear,p=function(){h++;var b=h/g,f=k(b);a.call(d,f,b,h);e.call(d,f,b);g>h?d.animationFrame=E(p):c.apply(d)};E(p)},b.getRelativePosition=function(a){var b,f,e=a.originalEvent||a;a=(a.currentTarget||a.srcElement).getBoundingClientRect();return e.touches?(b=e.touches[0].clientX-a.left,f=e.touches[0].clientY-a.top):(b=e.clientX-a.left,f=e.clientY-a.top),{x:b,y:f}},b.addEvent=function(a,b,f){a.addEventListener?a.addEventListener(b,f):a.attachEvent?a.attachEvent("on"+b,f):
a["on"+b]=f}),M=b.removeEvent=function(a,b,f){a.removeEventListener?a.removeEventListener(b,f,!1):a.detachEvent?a.detachEvent("on"+b,f):a["on"+b]=m},N=(b.bindEvents=function(a,b,f){a.events||(a.events={});e(b,function(b){a.events[b]=function(){f.apply(a,arguments)};L(a.chart.canvas,b,a.events[b])})},b.unbindEvents=function(a,b){e(b,function(b,g){M(a.chart.canvas,g,b)})}),O=b.getMaximumWidth=function(a){a=a.parentNode;var b=parseInt(y(a,"padding-left"))+parseInt(y(a,"padding-right"));return a?a.clientWidth-
b:0},P=b.getMaximumHeight=function(a){a=a.parentNode;var b=parseInt(y(a,"padding-bottom"))+parseInt(y(a,"padding-top"));return a?a.clientHeight-b:0},y=b.getStyle=function(a,b){return a.currentStyle?a.currentStyle[b]:document.defaultView.getComputedStyle(a,null).getPropertyValue(b)},Q=(b.getMaximumSize=b.getMaximumWidth,b.retinaScale=function(a){var b=a.ctx,f=a.canvas.width;a=a.canvas.height;window.devicePixelRatio&&(b.canvas.style.width=f+"px",b.canvas.style.height=a+"px",b.canvas.height=a*window.devicePixelRatio,
b.canvas.width=f*window.devicePixelRatio,b.scale(window.devicePixelRatio,window.devicePixelRatio))}),R=b.clear=function(a){a.ctx.clearRect(0,0,a.width,a.height)},t=b.fontString=function(a,b,f){return b+" "+a+"px "+f},A=b.longestText=function(a,b,f){a.font=b;var c=0;return e(f,function(b){b=a.measureText(b).width;c=b>c?b:c}),c},F=b.drawRoundedRectangle=function(a,b,f,e,c,d){a.beginPath();a.moveTo(b+d,f);a.lineTo(b+e-d,f);a.quadraticCurveTo(b+e,f,b+e,f+d);a.lineTo(b+e,f+c-d);a.quadraticCurveTo(b+e,
f+c,b+e-d,f+c);a.lineTo(b+d,f+c);a.quadraticCurveTo(b,f+c,b,f+c-d);a.lineTo(b,f+d);a.quadraticCurveTo(b,f,b+d,f);a.closePath()};c.instances={};c.Type=function(a,b,f){this.options=b;this.chart=f;this.id=H();c.instances[this.id]=this;b.responsive&&this.resize();this.initialize.call(this,a)};k(c.Type.prototype,{initialize:function(){return this},clear:function(){return R(this.chart),this},stop:function(){return c.animationService.cancelAnimation(this),this},resize:function(a){this.stop();var b=this.chart.canvas,
f=O(this.chart.canvas),e=this.options.maintainAspectRatio?f/this.chart.aspectRatio:P(this.chart.canvas);return b.width=this.chart.width=f,b.height=this.chart.height=e,Q(this.chart),"function"==typeof a&&a.apply(this,Array.prototype.slice.call(arguments,1)),this},reflow:m,render:function(a){(a&&this.reflow(),this.options.animation&&!a)?(a=new c.Animation,a.numSteps=this.options.animationSteps,a.easing=this.options.animationEasing,a.render=function(a,f){var e=f.currentStep/f.numSteps,c=(0,b.easingEffects[f.easing])(e);
a.draw(c,e,f.currentStep)},a.onAnimationProgress=this.options.onAnimationProgress,a.onAnimationComplete=this.options.onAnimationComplete,c.animationService.addAnimation(this,a)):(this.draw(),this.options.onAnimationComplete.call(this));return this},generateLegend:function(){return b.template(this.options.legendTemplate,this)},destroy:function(){this.stop();this.clear();N(this,this.events);var a=this.chart.canvas;a.width=this.chart.width;a.height=this.chart.height;a.style.removeProperty?(a.style.removeProperty("width"),
a.style.removeProperty("height")):(a.style.removeAttribute("width"),a.style.removeAttribute("height"));delete c.instances[this.id]},showTooltip:function(a,g){"undefined"==typeof this.activeElements&&(this.activeElements=[]);if(function(a){var b=!1;return a.length!==this.activeElements.length?b=!0:(e(a,function(a,f){a!==this.activeElements[f]&&(b=!0)},this),b)}.call(this,a)||g){if(this.activeElements=a,this.draw(),this.options.customTooltips&&this.options.customTooltips(!1),0<a.length)if(this.datasets&&
1<this.datasets.length){for(var f,d,h=this.datasets.length-1;0<=h&&(f=this.datasets[h].points||this.datasets[h].bars||this.datasets[h].segments,d=G(f,a[0]),-1===d);h--);var k=[],m=[];f=function(a){var f,e,g,c,h,B=[],l=[],u=[];return b.each(this.datasets,function(a){f=a.points||a.bars||a.segments;f[d]&&f[d].hasValue()&&B.push(f[d])}),b.each(B,function(a){l.push(a.x);u.push(a.y);k.push(b.template(this.options.multiTooltipTemplate,a));m.push({fill:a._saved.fillColor||a.fillColor,stroke:a._saved.strokeColor||
a.strokeColor})},this),h=v(u),g=x(u),c=v(l),e=x(l),{x:c>this.chart.width/2?c:e,y:(h+g)/2}}.call(this,d);(new c.MultiTooltip({x:f.x,y:f.y,xPadding:this.options.tooltipXPadding,yPadding:this.options.tooltipYPadding,xOffset:this.options.tooltipXOffset,fillColor:this.options.tooltipFillColor,textColor:this.options.tooltipFontColor,fontFamily:this.options.tooltipFontFamily,fontStyle:this.options.tooltipFontStyle,fontSize:this.options.tooltipFontSize,titleTextColor:this.options.tooltipTitleFontColor,titleFontFamily:this.options.tooltipTitleFontFamily,
titleFontStyle:this.options.tooltipTitleFontStyle,titleFontSize:this.options.tooltipTitleFontSize,cornerRadius:this.options.tooltipCornerRadius,labels:k,legendColors:m,legendColorBackground:this.options.multiTooltipKeyBackground,title:r(this.options.tooltipTitleTemplate,a[0]),chart:this.chart,ctx:this.chart.ctx,custom:this.options.customTooltips})).draw()}else e(a,function(a){var b=a.tooltipPosition();(new c.Tooltip({x:Math.round(b.x),y:Math.round(b.y),xPadding:this.options.tooltipXPadding,yPadding:this.options.tooltipYPadding,
fillColor:this.options.tooltipFillColor,textColor:this.options.tooltipFontColor,fontFamily:this.options.tooltipFontFamily,fontStyle:this.options.tooltipFontStyle,fontSize:this.options.tooltipFontSize,caretHeight:this.options.tooltipCaretSize,cornerRadius:this.options.tooltipCornerRadius,text:r(this.options.tooltipTemplate,a),chart:this.chart,custom:this.options.customTooltips})).draw()},this);return this}},toBase64Image:function(){return this.chart.canvas.toDataURL.apply(this.chart.canvas,arguments)}});
c.Type.extend=function(a){var b=this,f=function(){return b.apply(this,arguments)};if(f.prototype=h(b.prototype),k(f.prototype,a),f.extend=c.Type.extend,a.name||b.prototype.name){var e=a.name||b.prototype.name,d=c.defaults[b.prototype.name]?h(c.defaults[b.prototype.name]):{};c.defaults[e]=k(d,a.defaults);c.types[e]=f;c.prototype[e]=function(a,b){var g=u(c.defaults.global,c.defaults[e],b||{});return new f(a,g,this)}}else I("Name not provided for this chart, so it hasn't been registered");return b};
c.Element=function(a){k(this,a);this.initialize.apply(this,arguments);this.save()};k(c.Element.prototype,{initialize:function(){},restore:function(a){return a?e(a,function(a){this[a]=this._saved[a]},this):k(this,this._saved),this},save:function(){return this._saved=h(this),delete this._saved._saved,this},update:function(a){return e(a,function(a,b){this._saved[b]=this[b];this[b]=a},this),this},transition:function(a,b){return e(a,function(a,e){this[e]=(a-this._saved[e])*b+this._saved[e]},this),this},
tooltipPosition:function(){return{x:this.x,y:this.y}},hasValue:function(){return q(this.value)}});c.Element.extend=n;c.Point=c.Element.extend({display:!0,inRange:function(a,b){var e=this.hitDetectionRadius+this.radius;return Math.pow(a-this.x,2)+Math.pow(b-this.y,2)<Math.pow(e,2)},draw:function(){if(this.display){var a=this.ctx;a.beginPath();a.arc(this.x,this.y,this.radius,0,2*Math.PI);a.closePath();a.strokeStyle=this.strokeColor;a.lineWidth=this.strokeWidth;a.fillStyle=this.fillColor;a.fill();a.stroke()}}});
c.Arc=c.Element.extend({inRange:function(a,e){var f=b.getAngleFromPoint(this,{x:a,y:e}),c=f.angle%(2*Math.PI),d=(2*Math.PI+this.startAngle)%(2*Math.PI),h=(2*Math.PI+this.endAngle)%(2*Math.PI)||360,f=f.distance>=this.innerRadius&&f.distance<=this.outerRadius;return(d>h?h>=c||c>=d:c>=d&&h>=c)&&f},tooltipPosition:function(){var a=this.startAngle+(this.endAngle-this.startAngle)/2,b=(this.outerRadius-this.innerRadius)/2+this.innerRadius;return{x:this.x+Math.cos(a)*b,y:this.y+Math.sin(a)*b}},draw:function(a){a=
this.ctx;a.beginPath();a.arc(this.x,this.y,0>this.outerRadius?0:this.outerRadius,this.startAngle,this.endAngle);a.arc(this.x,this.y,0>this.innerRadius?0:this.innerRadius,this.endAngle,this.startAngle,!0);a.closePath();a.strokeStyle=this.strokeColor;a.lineWidth=this.strokeWidth;a.fillStyle=this.fillColor;a.fill();a.lineJoin="bevel";this.showStroke&&a.stroke()}});c.Rectangle=c.Element.extend({draw:function(){var a=this.ctx,b=this.width/2,e=this.x-b,b=this.x+b,c=this.base-(this.base-this.y),d=this.strokeWidth/
2;this.showStroke&&(e+=d,b-=d,c+=d);a.beginPath();a.fillStyle=this.fillColor;a.strokeStyle=this.strokeColor;a.lineWidth=this.strokeWidth;a.moveTo(e,this.base);a.lineTo(e,c);a.lineTo(b,c);a.lineTo(b,this.base);a.fill();this.showStroke&&a.stroke()},height:function(){return this.base-this.y},inRange:function(a,b){return a>=this.x-this.width/2&&a<=this.x+this.width/2&&b>=this.y&&b<=this.base}});c.Animation=c.Element.extend({currentStep:null,numSteps:60,easing:"",render:null,onAnimationProgress:null,onAnimationComplete:null});
c.Tooltip=c.Element.extend({draw:function(){var a=this.chart.ctx;a.font=t(this.fontSize,this.fontStyle,this.fontFamily);this.xAlign="center";this.yAlign="above";var b=this.caretPadding=2,e=a.measureText(this.text).width+2*this.xPadding,c=this.fontSize+2*this.yPadding,d=c+this.caretHeight+b;this.x+e/2>this.chart.width?this.xAlign="left":0>this.x-e/2&&(this.xAlign="right");0>this.y-d&&(this.yAlign="below");var h=this.x-e/2,d=this.y-d;if(a.fillStyle=this.fillColor,this.custom)this.custom(this);else{switch(this.yAlign){case "above":a.beginPath();
a.moveTo(this.x,this.y-b);a.lineTo(this.x+this.caretHeight,this.y-(b+this.caretHeight));a.lineTo(this.x-this.caretHeight,this.y-(b+this.caretHeight));a.closePath();a.fill();break;case "below":d=this.y+b+this.caretHeight,a.beginPath(),a.moveTo(this.x,this.y+b),a.lineTo(this.x+this.caretHeight,this.y+b+this.caretHeight),a.lineTo(this.x-this.caretHeight,this.y+b+this.caretHeight),a.closePath(),a.fill()}switch(this.xAlign){case "left":h=this.x-e+(this.cornerRadius+this.caretHeight);break;case "right":h=
this.x-(this.cornerRadius+this.caretHeight)}F(a,h,d,e,c,this.cornerRadius);a.fill();a.fillStyle=this.textColor;a.textAlign="center";a.textBaseline="middle";a.fillText(this.text,h+e/2,d+c/2)}}});c.MultiTooltip=c.Element.extend({initialize:function(){this.font=t(this.fontSize,this.fontStyle,this.fontFamily);this.titleFont=t(this.titleFontSize,this.titleFontStyle,this.titleFontFamily);this.titleHeight=this.title?1.5*this.titleFontSize:0;this.height=this.labels.length*this.fontSize+this.fontSize/2*(this.labels.length-
1)+2*this.yPadding+this.titleHeight;this.ctx.font=this.titleFont;var a=this.ctx.measureText(this.title).width,b=A(this.ctx,this.font,this.labels)+this.fontSize+3;this.width=x([b,a])+2*this.xPadding;a=this.height/2;0>this.y-a?this.y=a:this.y+a>this.chart.height&&(this.y=this.chart.height-a);this.x>this.chart.width/2?this.x-=this.xOffset+this.width:this.x+=this.xOffset},getLineHeight:function(a){var b=this.y-this.height/2+this.yPadding;return 0===a?b+this.titleHeight/3:b+(1.5*this.fontSize*(a-1)+this.fontSize/
2)+this.titleHeight},draw:function(){if(this.custom)this.custom(this);else{F(this.ctx,this.x,this.y-this.height/2,this.width,this.height,this.cornerRadius);var a=this.ctx;a.fillStyle=this.fillColor;a.fill();a.closePath();a.textAlign="left";a.textBaseline="middle";a.fillStyle=this.titleTextColor;a.font=this.titleFont;a.fillText(this.title,this.x+this.xPadding,this.getLineHeight(0));a.font=this.font;b.each(this.labels,function(b,e){a.fillStyle=this.textColor;a.fillText(b,this.x+this.xPadding+this.fontSize+
3,this.getLineHeight(e+1));a.fillStyle=this.legendColorBackground;a.fillRect(this.x+this.xPadding,this.getLineHeight(e+1)-this.fontSize/2,this.fontSize,this.fontSize);a.fillStyle=this.legendColors[e].fill;a.fillRect(this.x+this.xPadding,this.getLineHeight(e+1)-this.fontSize/2,this.fontSize,this.fontSize)},this)}}});c.Scale=c.Element.extend({initialize:function(){this.fit()},buildYLabels:function(){this.yLabels=[];for(var a=C(this.stepValue),b=0;b<=this.steps;b++)this.yLabels.push(r(this.templateString,
{value:(this.min+b*this.stepValue).toFixed(a)}));this.yLabelWidth=this.display&&this.showLabels?A(this.ctx,this.font,this.yLabels)+10:0},addXLabel:function(a){this.xLabels.push(a);this.valuesCount++;this.fit()},removeXLabel:function(){this.xLabels.shift();this.valuesCount--;this.fit()},fit:function(){this.startPoint=this.display?this.fontSize:0;this.endPoint=this.display?this.height-1.5*this.fontSize-5:this.height;this.startPoint+=this.padding;this.endPoint-=this.padding;var a,b=this.endPoint,e=this.endPoint-
this.startPoint;this.calculateYRange(e);this.buildYLabels();for(this.calculateXLabelRotation();e>this.endPoint-this.startPoint;)e=this.endPoint-this.startPoint,a=this.yLabelWidth,this.calculateYRange(e),this.buildYLabels(),a<this.yLabelWidth&&(this.endPoint=b,this.calculateXLabelRotation())},calculateXLabelRotation:function(){this.ctx.font=this.font;var a,b=this.ctx.measureText(this.xLabels[0]).width;if(this.xScalePaddingRight=this.ctx.measureText(this.xLabels[this.xLabels.length-1]).width/2+3,this.xScalePaddingLeft=
b/2>this.yLabelWidth?b/2:this.yLabelWidth,this.xLabelRotation=0,this.display){var e,c=A(this.ctx,this.font,this.xLabels);this.xLabelWidth=c;for(var d=Math.floor(this.calculateX(1)-this.calculateX(0))-6;this.xLabelWidth>d&&0===this.xLabelRotation||this.xLabelWidth>d&&90>=this.xLabelRotation&&0<this.xLabelRotation;)e=Math.cos(z(this.xLabelRotation)),a=e*b,a+this.fontSize/2>this.yLabelWidth&&(this.xScalePaddingLeft=a+this.fontSize/2),this.xScalePaddingRight=this.fontSize/2,this.xLabelRotation++,this.xLabelWidth=
e*c;0<this.xLabelRotation&&(this.endPoint-=Math.sin(z(this.xLabelRotation))*c+3)}else this.xLabelWidth=0,this.xScalePaddingLeft=this.xScalePaddingRight=this.padding},calculateYRange:m,drawingArea:function(){return this.startPoint-this.endPoint},calculateY:function(a){var b=this.drawingArea()/(this.min-this.max);return this.endPoint-b*(a-this.min)},calculateX:function(a){var b=(0<this.xLabelRotation,this.width-(this.xScalePaddingLeft+this.xScalePaddingRight))/Math.max(this.valuesCount-(this.offsetGridLines?
0:1),1);a=b*a+this.xScalePaddingLeft;return this.offsetGridLines&&(a+=b/2),Math.round(a)},update:function(a){b.extend(this,a);this.fit()},draw:function(){var a=this.ctx,c=(this.endPoint-this.startPoint)/this.steps,f=Math.round(this.xScalePaddingLeft);this.display&&(a.fillStyle=this.textColor,a.font=this.font,e(this.yLabels,function(e,d){var h=this.endPoint-c*d,k=Math.round(h),m=this.showHorizontalLines;a.textAlign="right";a.textBaseline="middle";this.showLabels&&a.fillText(e,f-10,h);0!==d||m||(m=
!0);m&&a.beginPath();0<d?(a.lineWidth=this.gridLineWidth,a.strokeStyle=this.gridLineColor):(a.lineWidth=this.lineWidth,a.strokeStyle=this.lineColor);k+=b.aliasPixel(a.lineWidth);m&&(a.moveTo(f,k),a.lineTo(this.width,k),a.stroke(),a.closePath());a.lineWidth=this.lineWidth;a.strokeStyle=this.lineColor;a.beginPath();a.moveTo(f-5,k);a.lineTo(f,k);a.stroke();a.closePath()},this),e(this.xLabels,function(b,e){var c=this.calculateX(e)+D(this.lineWidth),f=this.calculateX(e-(this.offsetGridLines?.5:0))+D(this.lineWidth),
d=0<this.xLabelRotation,g=this.showVerticalLines;0!==e||g||(g=!0);g&&a.beginPath();0<e?(a.lineWidth=this.gridLineWidth,a.strokeStyle=this.gridLineColor):(a.lineWidth=this.lineWidth,a.strokeStyle=this.lineColor);g&&(a.moveTo(f,this.endPoint),a.lineTo(f,this.startPoint-3),a.stroke(),a.closePath());a.lineWidth=this.lineWidth;a.strokeStyle=this.lineColor;a.beginPath();a.moveTo(f,this.endPoint);a.lineTo(f,this.endPoint+5);a.stroke();a.closePath();a.save();a.translate(c,d?this.endPoint+12:this.endPoint+
8);a.rotate(-1*z(this.xLabelRotation));a.font=this.font;a.textAlign=d?"right":"center";a.textBaseline=d?"middle":"top";a.fillText(b,0,0);a.restore()},this))}});c.RadialScale=c.Element.extend({initialize:function(){this.size=v([this.height,this.width]);this.drawingArea=this.display?this.size/2-(this.fontSize/2+this.backdropPaddingY):this.size/2},calculateCenterOffset:function(a){return this.drawingArea/(this.max-this.min)*(a-this.min)},update:function(){this.lineArc?this.drawingArea=this.display?this.size/
2-(this.fontSize/2+this.backdropPaddingY):this.size/2:this.setScaleSize();this.buildYLabels()},buildYLabels:function(){this.yLabels=[];for(var a=C(this.stepValue),b=0;b<=this.steps;b++)this.yLabels.push(r(this.templateString,{value:(this.min+b*this.stepValue).toFixed(a)}))},getCircumference:function(){return 2*Math.PI/this.valuesCount},setScaleSize:function(){var a,b,e,c,d,h,k,m=v([this.height/2-this.pointLabelFontSize-5,this.width/2]);k=this.width;var l=0;this.ctx.font=t(this.pointLabelFontSize,
this.pointLabelFontStyle,this.pointLabelFontFamily);for(b=0;b<this.valuesCount;b++)a=this.getPointPosition(b,m),e=this.ctx.measureText(r(this.templateString,{value:this.labels[b]})).width+5,0===b||b===this.valuesCount/2?(c=e/2,a.x+c>k&&(k=a.x+c,d=b),a.x-c<l&&(l=a.x-c,h=b)):b<this.valuesCount/2?a.x+e>k&&(k=a.x+e,d=b):b>this.valuesCount/2&&a.x-e<l&&(l=a.x-e,h=b);a=l;k=Math.ceil(k-this.width);d=this.getIndexAngle(d);h=this.getIndexAngle(h);d=k/Math.sin(d+Math.PI/2);h=a/Math.sin(h+Math.PI/2);d=q(d)?d:
0;h=q(h)?h:0;this.drawingArea=m-(h+d)/2;this.setCenterPoint(h,d)},setCenterPoint:function(a,b){this.xCenter=(a+this.drawingArea+(this.width-b-this.drawingArea))/2;this.yCenter=this.height/2},getIndexAngle:function(a){return 2*Math.PI/this.valuesCount*a-Math.PI/2},getPointPosition:function(a,b){var e=this.getIndexAngle(a);return{x:Math.cos(e)*b+this.xCenter,y:Math.sin(e)*b+this.yCenter}},draw:function(){if(this.display){var a=this.ctx;if(e(this.yLabels,function(b,e){if(0<e){var c;c=this.drawingArea/
this.steps*e;var d=this.yCenter-c;if(0<this.lineWidth){if(a.strokeStyle=this.lineColor,a.lineWidth=this.lineWidth,this.lineArc)a.beginPath(),a.arc(this.xCenter,this.yCenter,c,0,2*Math.PI);else{a.beginPath();for(var f=0;f<this.valuesCount;f++)c=this.getPointPosition(f,this.calculateCenterOffset(this.min+e*this.stepValue)),0===f?a.moveTo(c.x,c.y):a.lineTo(c.x,c.y)}a.closePath();a.stroke()}if(this.showLabels){if(a.font=t(this.fontSize,this.fontStyle,this.fontFamily),this.showLabelBackdrop)c=a.measureText(b).width,
a.fillStyle=this.backdropColor,a.fillRect(this.xCenter-c/2-this.backdropPaddingX,d-this.fontSize/2-this.backdropPaddingY,c+2*this.backdropPaddingX,this.fontSize+2*this.backdropPaddingY);a.textAlign="center";a.textBaseline="middle";a.fillStyle=this.fontColor;a.fillText(b,this.xCenter,d)}}},this),!this.lineArc){a.lineWidth=this.angleLineWidth;a.strokeStyle=this.angleLineColor;for(var b=this.valuesCount-1;0<=b;b--){var c=null,d=null;if(0<this.angleLineWidth&&0===b%this.angleLineInterval&&(c=this.calculateCenterOffset(this.max),
d=this.getPointPosition(b,c),a.beginPath(),a.moveTo(this.xCenter,this.yCenter),a.lineTo(d.x,d.y),a.stroke(),a.closePath()),this.backgroundColors&&this.backgroundColors.length==this.valuesCount){null==c&&(c=this.calculateCenterOffset(this.max));null==d&&(d=this.getPointPosition(b,c));var h=this.getPointPosition(0===b?this.valuesCount-1:b-1,c),k=this.getPointPosition(b===this.valuesCount-1?0:b+1,c),c=(h.x+d.x)/2,h=(h.y+d.y)/2,m=(d.x+k.x)/2,k=(d.y+k.y)/2;a.beginPath();a.moveTo(this.xCenter,this.yCenter);
a.lineTo(c,h);a.lineTo(d.x,d.y);a.lineTo(m,k);a.fillStyle=this.backgroundColors[b];a.fill();a.closePath()}d=this.getPointPosition(b,this.calculateCenterOffset(this.max)+5);a.font=t(this.pointLabelFontSize,this.pointLabelFontStyle,this.pointLabelFontFamily);a.fillStyle=this.pointLabelFontColor;h=this.labels.length;c=this.labels.length/2;m=c/2;k=m>b||b>h-m;h=b===m||b===h-m;0===b?a.textAlign="center":b===c?a.textAlign="center":c>b?a.textAlign="left":a.textAlign="right";h?a.textBaseline="middle":k?a.textBaseline=
"bottom":a.textBaseline="top";a.fillText(this.labels[b],d.x,d.y)}}}}});c.animationService={frameDuration:17,animations:[],dropFrames:0,addAnimation:function(a,e){for(var c=0;c<this.animations.length;++c)if(this.animations[c].chartInstance===a)return void(this.animations[c].animationObject=e);this.animations.push({chartInstance:a,animationObject:e});1==this.animations.length&&b.requestAnimFrame.call(window,this.digestWrapper)},cancelAnimation:function(a){var e=b.findNextWhere(this.animations,function(b){return b.chartInstance===
a});e&&this.animations.splice(e,1)},digestWrapper:function(){c.animationService.startDigest.call(c.animationService)},startDigest:function(){var a=Date.now(),e=0;1<this.dropFrames&&(e=Math.floor(this.dropFrames),this.dropFrames-=e);for(var c=0;c<this.animations.length;c++)null===this.animations[c].animationObject.currentStep&&(this.animations[c].animationObject.currentStep=0),this.animations[c].animationObject.currentStep+=1+e,this.animations[c].animationObject.currentStep>this.animations[c].animationObject.numSteps&&
(this.animations[c].animationObject.currentStep=this.animations[c].animationObject.numSteps),this.animations[c].animationObject.render(this.animations[c].chartInstance,this.animations[c].animationObject),this.animations[c].animationObject.currentStep==this.animations[c].animationObject.numSteps&&(this.animations[c].animationObject.onAnimationComplete.call(this.animations[c].chartInstance),this.animations.splice(c,1),c--);a=(Date.now()-a-this.frameDuration)/this.frameDuration;1<a&&(this.dropFrames+=
a);0<this.animations.length&&b.requestAnimFrame.call(window,this.digestWrapper)}};b.addEvent(window,"resize",function(){var a;return function(){clearTimeout(a);a=setTimeout(function(){e(c.instances,function(a){a.options.responsive&&a.resize(a.render,!0)})},50)}}());J?define("Chart",[],function(){return c}):"object"==typeof module&&module.exports&&(module.exports=c);l.Chart=c;c.noConflict=function(){return l.Chart=d,c}}).call(this);
(function(){var l=this.Chart,d=l.helpers;l.Type.extend({name:"Bar",defaults:{scaleBeginAtZero:!0,scaleShowGridLines:!0,scaleGridLineColor:"rgba(0,0,0,.05)",scaleGridLineWidth:1,scaleShowHorizontalLines:!0,scaleShowVerticalLines:!0,barShowStroke:!0,barStrokeWidth:2,barValueSpacing:5,barDatasetSpacing:1,legendTemplate:'\x3cul class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend"\x3e\x3c% for (var i\x3d0; i\x3cdatasets.length; i++){%\x3e\x3cli\x3e\x3cspan class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend-icon" style\x3d"background-color:\x3c%\x3ddatasets[i].fillColor%\x3e"\x3e\x3c/span\x3e\x3cspan class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend-text"\x3e\x3c%if(datasets[i].label){%\x3e\x3c%\x3ddatasets[i].label%\x3e\x3c%}%\x3e\x3c/span\x3e\x3c/li\x3e\x3c%}%\x3e\x3c/ul\x3e'},
initialize:function(c){var b=this.options;this.ScaleClass=l.Scale.extend({offsetGridLines:!0,calculateBarX:function(e,c,d){var l=this.calculateBaseWidth();d=this.calculateX(d)-l/2;e=this.calculateBarWidth(e);return d+e*c+c*b.barDatasetSpacing+e/2},calculateBaseWidth:function(){return this.calculateX(1)-this.calculateX(0)-2*b.barValueSpacing},calculateBarWidth:function(e){return(this.calculateBaseWidth()-(e-1)*b.barDatasetSpacing)/e}});this.datasets=[];this.options.showTooltips&&d.bindEvents(this,
this.options.tooltipEvents,function(b){b="mouseout"!==b.type?this.getBarsAtEvent(b):[];this.eachBars(function(b){b.restore(["fillColor","strokeColor"])});d.each(b,function(b){b&&(b.fillColor=b.highlightFill,b.strokeColor=b.highlightStroke)});this.showTooltip(b)});this.BarClass=l.Rectangle.extend({strokeWidth:this.options.barStrokeWidth,showStroke:this.options.barShowStroke,ctx:this.chart.ctx});d.each(c.datasets,function(b,h){var k={label:b.label||null,fillColor:b.fillColor,strokeColor:b.strokeColor,
bars:[]};this.datasets.push(k);d.each(b.data,function(d,h){k.bars.push(new this.BarClass({value:d,label:c.labels[h],datasetLabel:b.label,strokeColor:"object"==typeof b.strokeColor?b.strokeColor[h]:b.strokeColor,fillColor:"object"==typeof b.fillColor?b.fillColor[h]:b.fillColor,highlightFill:b.highlightFill?"object"==typeof b.highlightFill?b.highlightFill[h]:b.highlightFill:"object"==typeof b.fillColor?b.fillColor[h]:b.fillColor,highlightStroke:b.highlightStroke?"object"==typeof b.highlightStroke?b.highlightStroke[h]:
b.highlightStroke:"object"==typeof b.strokeColor?b.strokeColor[h]:b.strokeColor}))},this)},this);this.buildScale(c.labels);this.BarClass.prototype.base=this.scale.endPoint;this.eachBars(function(b,c,k){d.extend(b,{width:this.scale.calculateBarWidth(this.datasets.length),x:this.scale.calculateBarX(this.datasets.length,k,c),y:this.scale.endPoint});b.save()},this);this.render()},update:function(){this.scale.update();d.each(this.activeElements,function(c){c.restore(["fillColor","strokeColor"])});this.eachBars(function(c){c.save()});
this.render()},eachBars:function(c){d.each(this.datasets,function(b,e){d.each(b.bars,c,this,e)},this)},getBarsAtEvent:function(c){var b,e=[];c=d.getRelativePosition(c);for(var h=function(c){e.push(c.bars[b])},k=0;k<this.datasets.length;k++)for(b=0;b<this.datasets[k].bars.length;b++)if(this.datasets[k].bars[b].inRange(c.x,c.y))return d.each(this.datasets,h),e;return e},buildScale:function(c){var b=this,e=function(){var c=[];return b.eachBars(function(b){c.push(b.value)}),c};c={templateString:this.options.scaleLabel,
height:this.chart.height,width:this.chart.width,ctx:this.chart.ctx,textColor:this.options.scaleFontColor,fontSize:this.options.scaleFontSize,fontStyle:this.options.scaleFontStyle,fontFamily:this.options.scaleFontFamily,valuesCount:c.length,beginAtZero:this.options.scaleBeginAtZero,integersOnly:this.options.scaleIntegersOnly,calculateYRange:function(b){b=d.calculateScaleRange(e(),b,this.fontSize,this.beginAtZero,this.integersOnly);d.extend(this,b)},xLabels:c,font:d.fontString(this.options.scaleFontSize,
this.options.scaleFontStyle,this.options.scaleFontFamily),lineWidth:this.options.scaleLineWidth,lineColor:this.options.scaleLineColor,showHorizontalLines:this.options.scaleShowHorizontalLines,showVerticalLines:this.options.scaleShowVerticalLines,gridLineWidth:this.options.scaleShowGridLines?this.options.scaleGridLineWidth:0,gridLineColor:this.options.scaleShowGridLines?this.options.scaleGridLineColor:"rgba(0,0,0,0)",padding:this.options.showScale?0:this.options.barShowStroke?this.options.barStrokeWidth:
0,showLabels:this.options.scaleShowLabels,display:this.options.showScale};this.options.scaleOverride&&d.extend(c,{calculateYRange:d.noop,steps:this.options.scaleSteps,stepValue:this.options.scaleStepWidth,min:this.options.scaleStartValue,max:this.options.scaleStartValue+this.options.scaleSteps*this.options.scaleStepWidth});this.scale=new this.ScaleClass(c)},addData:function(c,b){d.each(c,function(c,d){this.datasets[d].bars.push(new this.BarClass({value:c,label:b,datasetLabel:this.datasets[d].label,
x:this.scale.calculateBarX(this.datasets.length,d,this.scale.valuesCount+1),y:this.scale.endPoint,width:this.scale.calculateBarWidth(this.datasets.length),base:this.scale.endPoint,strokeColor:this.datasets[d].strokeColor,fillColor:this.datasets[d].fillColor}))},this);this.scale.addXLabel(b);this.update()},removeData:function(){this.scale.removeXLabel();d.each(this.datasets,function(c){c.bars.shift()},this);this.update()},reflow:function(){d.extend(this.BarClass.prototype,{y:this.scale.endPoint,base:this.scale.endPoint});
var c=d.extend({height:this.chart.height,width:this.chart.width});this.scale.update(c)},draw:function(c){var b=c||1;this.clear();this.chart.ctx;this.scale.draw(b);d.each(this.datasets,function(c,h){d.each(c.bars,function(c,e){c.hasValue()&&(c.base=this.scale.endPoint,c.transition({x:this.scale.calculateBarX(this.datasets.length,h,e),y:this.scale.calculateY(c.value),width:this.scale.calculateBarWidth(this.datasets.length)},b).draw())},this)},this)}})}).call(this);
(function(){var l=this.Chart,d=l.helpers,c={segmentShowStroke:!0,segmentStrokeColor:"#fff",segmentStrokeWidth:2,percentageInnerCutout:50,animationSteps:100,animationEasing:"easeOutBounce",animateRotate:!0,animateScale:!1,legendTemplate:'\x3cul class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend"\x3e\x3c% for (var i\x3d0; i\x3csegments.length; i++){%\x3e\x3cli\x3e\x3cspan class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend-icon" style\x3d"background-color:\x3c%\x3dsegments[i].fillColor%\x3e"\x3e\x3c/span\x3e\x3cspan class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend-text"\x3e\x3c%if(segments[i].label){%\x3e\x3c%\x3dsegments[i].label%\x3e\x3c%}%\x3e\x3c/span\x3e\x3c/li\x3e\x3c%}%\x3e\x3c/ul\x3e'};
l.Type.extend({name:"Doughnut",defaults:c,initialize:function(b){this.segments=[];this.outerRadius=(d.min([this.chart.width,this.chart.height])-this.options.segmentStrokeWidth/2)/2;this.SegmentArc=l.Arc.extend({ctx:this.chart.ctx,x:this.chart.width/2,y:this.chart.height/2});this.options.showTooltips&&d.bindEvents(this,this.options.tooltipEvents,function(b){b="mouseout"!==b.type?this.getSegmentsAtEvent(b):[];d.each(this.segments,function(b){b.restore(["fillColor"])});d.each(b,function(b){b.fillColor=
b.highlightColor});this.showTooltip(b)});this.calculateTotal(b);d.each(b,function(c,d){c.color||(c.color="hsl("+360*d/b.length+", 100%, 50%)");this.addData(c,d,!0)},this);this.render()},getSegmentsAtEvent:function(b){var c=[],h=d.getRelativePosition(b);return d.each(this.segments,function(b){b.inRange(h.x,h.y)&&c.push(b)},this),c},addData:function(b,c,d){c=void 0!==c?c:this.segments.length;"undefined"==typeof b.color&&(b.color=l.defaults.global.segmentColorDefault[c%l.defaults.global.segmentColorDefault.length],
b.highlight=l.defaults.global.segmentHighlightColorDefaults[c%l.defaults.global.segmentHighlightColorDefaults.length]);this.segments.splice(c,0,new this.SegmentArc({value:b.value,outerRadius:this.options.animateScale?0:this.outerRadius,innerRadius:this.options.animateScale?0:this.outerRadius/100*this.options.percentageInnerCutout,fillColor:b.color,highlightColor:b.highlight||b.color,showStroke:this.options.segmentShowStroke,strokeWidth:this.options.segmentStrokeWidth,strokeColor:this.options.segmentStrokeColor,
startAngle:1.5*Math.PI,circumference:this.options.animateRotate?0:this.calculateCircumference(b.value),label:b.label}));d||(this.reflow(),this.update())},calculateCircumference:function(b){return 0<this.total?b/this.total*Math.PI*2:0},calculateTotal:function(b){this.total=0;d.each(b,function(b){this.total+=Math.abs(b.value)},this)},update:function(){this.calculateTotal(this.segments);d.each(this.activeElements,function(b){b.restore(["fillColor"])});d.each(this.segments,function(b){b.save()});this.render()},
removeData:function(b){b=d.isNumber(b)?b:this.segments.length-1;this.segments.splice(b,1);this.reflow();this.update()},reflow:function(){d.extend(this.SegmentArc.prototype,{x:this.chart.width/2,y:this.chart.height/2});this.outerRadius=(d.min([this.chart.width,this.chart.height])-this.options.segmentStrokeWidth/2)/2;d.each(this.segments,function(b){b.update({outerRadius:this.outerRadius,innerRadius:this.outerRadius/100*this.options.percentageInnerCutout})},this)},draw:function(b){var c=b?b:1;this.clear();
d.each(this.segments,function(b,d){b.transition({circumference:this.calculateCircumference(b.value),outerRadius:this.outerRadius,innerRadius:this.outerRadius/100*this.options.percentageInnerCutout},c);b.endAngle=b.startAngle+b.circumference;b.draw();0===d&&(b.startAngle=1.5*Math.PI);d<this.segments.length-1&&(this.segments[d+1].startAngle=b.endAngle)},this)}});l.types.Doughnut.extend({name:"Pie",defaults:d.merge(c,{percentageInnerCutout:0})})}).call(this);
(function(){var l=this.Chart,d=l.helpers;l.Type.extend({name:"Line",defaults:{scaleShowGridLines:!0,scaleGridLineColor:"rgba(0,0,0,.05)",scaleGridLineWidth:1,scaleShowHorizontalLines:!0,scaleShowVerticalLines:!0,bezierCurve:!0,bezierCurveTension:.4,pointDot:!0,pointDotRadius:4,pointDotStrokeWidth:1,pointHitDetectionRadius:20,datasetStroke:!0,datasetStrokeWidth:2,datasetFill:!0,legendTemplate:'\x3cul class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend"\x3e\x3c% for (var i\x3d0; i\x3cdatasets.length; i++){%\x3e\x3cli\x3e\x3cspan class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend-icon" style\x3d"background-color:\x3c%\x3ddatasets[i].strokeColor%\x3e"\x3e\x3c/span\x3e\x3cspan class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend-text"\x3e\x3c%if(datasets[i].label){%\x3e\x3c%\x3ddatasets[i].label%\x3e\x3c%}%\x3e\x3c/span\x3e\x3c/li\x3e\x3c%}%\x3e\x3c/ul\x3e',
offsetGridLines:!1},initialize:function(c){this.PointClass=l.Point.extend({offsetGridLines:this.options.offsetGridLines,strokeWidth:this.options.pointDotStrokeWidth,radius:this.options.pointDotRadius,display:this.options.pointDot,hitDetectionRadius:this.options.pointHitDetectionRadius,ctx:this.chart.ctx,inRange:function(b){return Math.pow(b-this.x,2)<Math.pow(this.radius+this.hitDetectionRadius,2)}});this.datasets=[];this.options.showTooltips&&d.bindEvents(this,this.options.tooltipEvents,function(b){b=
"mouseout"!==b.type?this.getPointsAtEvent(b):[];this.eachPoints(function(b){b.restore(["fillColor","strokeColor"])});d.each(b,function(b){b.fillColor=b.highlightFill;b.strokeColor=b.highlightStroke});this.showTooltip(b)});d.each(c.datasets,function(b){var e={label:b.label||null,fillColor:b.fillColor,strokeColor:b.strokeColor,pointColor:b.pointColor,pointStrokeColor:b.pointStrokeColor,points:[]};this.datasets.push(e);d.each(b.data,function(d,k){e.points.push(new this.PointClass({value:d,label:c.labels[k],
datasetLabel:b.label,strokeColor:b.pointStrokeColor,fillColor:b.pointColor,highlightFill:b.pointHighlightFill||b.pointColor,highlightStroke:b.pointHighlightStroke||b.pointStrokeColor}))},this);this.buildScale(c.labels);this.eachPoints(function(b,c){d.extend(b,{x:this.scale.calculateX(c),y:this.scale.endPoint});b.save()},this)},this);this.render()},update:function(){this.scale.update();d.each(this.activeElements,function(c){c.restore(["fillColor","strokeColor"])});this.eachPoints(function(c){c.save()});
this.render()},eachPoints:function(c){d.each(this.datasets,function(b){d.each(b.points,c,this)},this)},getPointsAtEvent:function(c){var b=[],e=d.getRelativePosition(c);return d.each(this.datasets,function(c){d.each(c.points,function(c){c.inRange(e.x,e.y)&&b.push(c)})},this),b},buildScale:function(c){var b=this,e=function(){var c=[];return b.eachPoints(function(b){c.push(b.value)}),c};c={templateString:this.options.scaleLabel,height:this.chart.height,width:this.chart.width,ctx:this.chart.ctx,textColor:this.options.scaleFontColor,
offsetGridLines:this.options.offsetGridLines,fontSize:this.options.scaleFontSize,fontStyle:this.options.scaleFontStyle,fontFamily:this.options.scaleFontFamily,valuesCount:c.length,beginAtZero:this.options.scaleBeginAtZero,integersOnly:this.options.scaleIntegersOnly,calculateYRange:function(b){b=d.calculateScaleRange(e(),b,this.fontSize,this.beginAtZero,this.integersOnly);d.extend(this,b)},xLabels:c,font:d.fontString(this.options.scaleFontSize,this.options.scaleFontStyle,this.options.scaleFontFamily),
lineWidth:this.options.scaleLineWidth,lineColor:this.options.scaleLineColor,showHorizontalLines:this.options.scaleShowHorizontalLines,showVerticalLines:this.options.scaleShowVerticalLines,gridLineWidth:this.options.scaleShowGridLines?this.options.scaleGridLineWidth:0,gridLineColor:this.options.scaleShowGridLines?this.options.scaleGridLineColor:"rgba(0,0,0,0)",padding:this.options.showScale?0:this.options.pointDotRadius+this.options.pointDotStrokeWidth,showLabels:this.options.scaleShowLabels,display:this.options.showScale};
this.options.scaleOverride&&d.extend(c,{calculateYRange:d.noop,steps:this.options.scaleSteps,stepValue:this.options.scaleStepWidth,min:this.options.scaleStartValue,max:this.options.scaleStartValue+this.options.scaleSteps*this.options.scaleStepWidth});this.scale=new l.Scale(c)},addData:function(c,b){d.each(c,function(c,d){this.datasets[d].points.push(new this.PointClass({value:c,label:b,datasetLabel:this.datasets[d].label,x:this.scale.calculateX(this.scale.valuesCount+1),y:this.scale.endPoint,strokeColor:this.datasets[d].pointStrokeColor,
fillColor:this.datasets[d].pointColor}))},this);this.scale.addXLabel(b);this.update()},removeData:function(){this.scale.removeXLabel();d.each(this.datasets,function(c){c.points.shift()},this);this.update()},reflow:function(){var c=d.extend({height:this.chart.height,width:this.chart.width});this.scale.update(c)},draw:function(c){var b=c||1;this.clear();var e=this.chart.ctx,h=function(b){return null!==b.value},k=function(b,c,e){return d.findNextWhere(c,h,e)||b},l=function(b,c,e){return d.findPreviousWhere(c,
h,e)||b};this.scale&&(this.scale.draw(b),d.each(this.datasets,function(c){var n=d.where(c.points,h);d.each(c.points,function(c,e){c.hasValue()&&c.transition({y:this.scale.calculateY(c.value),x:this.scale.calculateX(e)},b)},this);this.options.bezierCurve&&d.each(n,function(b,c){var e=0<c&&c<n.length-1?this.options.bezierCurveTension:0;b.controlPoints=d.splineCurve(l(b,n,c),b,k(b,n,c),e);b.controlPoints.outer.y>this.scale.endPoint?b.controlPoints.outer.y=this.scale.endPoint:b.controlPoints.outer.y<
this.scale.startPoint&&(b.controlPoints.outer.y=this.scale.startPoint);b.controlPoints.inner.y>this.scale.endPoint?b.controlPoints.inner.y=this.scale.endPoint:b.controlPoints.inner.y<this.scale.startPoint&&(b.controlPoints.inner.y=this.scale.startPoint)},this);e.lineWidth=this.options.datasetStrokeWidth;e.strokeStyle=c.strokeColor;e.beginPath();d.each(n,function(b,c){if(0===c)e.moveTo(b.x,b.y);else if(this.options.bezierCurve){var d=l(b,n,c);e.bezierCurveTo(d.controlPoints.outer.x,d.controlPoints.outer.y,
b.controlPoints.inner.x,b.controlPoints.inner.y,b.x,b.y)}else e.lineTo(b.x,b.y)},this);this.options.datasetStroke&&e.stroke();this.options.datasetFill&&0<n.length&&(e.lineTo(n[n.length-1].x,this.scale.endPoint),e.lineTo(n[0].x,this.scale.endPoint),e.fillStyle=c.fillColor,e.closePath(),e.fill());d.each(n,function(b){b.draw()})},this))}})}).call(this);
(function(){var l=this.Chart,d=l.helpers;l.Type.extend({name:"PolarArea",defaults:{scaleShowLabelBackdrop:!0,scaleBackdropColor:"rgba(255,255,255,0.75)",scaleBeginAtZero:!0,scaleBackdropPaddingY:2,scaleBackdropPaddingX:2,scaleShowLine:!0,segmentShowStroke:!0,segmentStrokeColor:"#fff",segmentStrokeWidth:2,animationSteps:100,animationEasing:"easeOutBounce",animateRotate:!0,animateScale:!1,legendTemplate:'\x3cul class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend"\x3e\x3c% for (var i\x3d0; i\x3csegments.length; i++){%\x3e\x3cli\x3e\x3cspan class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend-icon" style\x3d"background-color:\x3c%\x3dsegments[i].fillColor%\x3e"\x3e\x3c/span\x3e\x3cspan class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend-text"\x3e\x3c%if(segments[i].label){%\x3e\x3c%\x3dsegments[i].label%\x3e\x3c%}%\x3e\x3c/span\x3e\x3c/li\x3e\x3c%}%\x3e\x3c/ul\x3e'},
initialize:function(c){this.segments=[];this.SegmentArc=l.Arc.extend({showStroke:this.options.segmentShowStroke,strokeWidth:this.options.segmentStrokeWidth,strokeColor:this.options.segmentStrokeColor,ctx:this.chart.ctx,innerRadius:0,x:this.chart.width/2,y:this.chart.height/2});this.scale=new l.RadialScale({display:this.options.showScale,fontStyle:this.options.scaleFontStyle,fontSize:this.options.scaleFontSize,fontFamily:this.options.scaleFontFamily,fontColor:this.options.scaleFontColor,showLabels:this.options.scaleShowLabels,
showLabelBackdrop:this.options.scaleShowLabelBackdrop,backdropColor:this.options.scaleBackdropColor,backdropPaddingY:this.options.scaleBackdropPaddingY,backdropPaddingX:this.options.scaleBackdropPaddingX,lineWidth:this.options.scaleShowLine?this.options.scaleLineWidth:0,lineColor:this.options.scaleLineColor,lineArc:!0,width:this.chart.width,height:this.chart.height,xCenter:this.chart.width/2,yCenter:this.chart.height/2,ctx:this.chart.ctx,templateString:this.options.scaleLabel,valuesCount:c.length});
this.updateScaleRange(c);this.scale.update();d.each(c,function(b,c){this.addData(b,c,!0)},this);this.options.showTooltips&&d.bindEvents(this,this.options.tooltipEvents,function(b){b="mouseout"!==b.type?this.getSegmentsAtEvent(b):[];d.each(this.segments,function(b){b.restore(["fillColor"])});d.each(b,function(b){b.fillColor=b.highlightColor});this.showTooltip(b)});this.render()},getSegmentsAtEvent:function(c){var b=[],e=d.getRelativePosition(c);return d.each(this.segments,function(c){c.inRange(e.x,
e.y)&&b.push(c)},this),b},addData:function(c,b,e){this.segments.splice(b||this.segments.length,0,new this.SegmentArc({fillColor:c.color,highlightColor:c.highlight||c.color,label:c.label,value:c.value,outerRadius:this.options.animateScale?0:this.scale.calculateCenterOffset(c.value),circumference:this.options.animateRotate?0:this.scale.getCircumference(),startAngle:1.5*Math.PI}));e||(this.reflow(),this.update())},removeData:function(c){c=d.isNumber(c)?c:this.segments.length-1;this.segments.splice(c,
1);this.reflow();this.update()},calculateTotal:function(c){this.total=0;d.each(c,function(b){this.total+=b.value},this);this.scale.valuesCount=this.segments.length},updateScaleRange:function(c){var b=[];d.each(c,function(c){b.push(c.value)});c=this.options.scaleOverride?{steps:this.options.scaleSteps,stepValue:this.options.scaleStepWidth,min:this.options.scaleStartValue,max:this.options.scaleStartValue+this.options.scaleSteps*this.options.scaleStepWidth}:d.calculateScaleRange(b,d.min([this.chart.width,
this.chart.height])/2,this.options.scaleFontSize,this.options.scaleBeginAtZero,this.options.scaleIntegersOnly);d.extend(this.scale,c,{size:d.min([this.chart.width,this.chart.height]),xCenter:this.chart.width/2,yCenter:this.chart.height/2})},update:function(){this.calculateTotal(this.segments);d.each(this.segments,function(c){c.save()});this.reflow();this.render()},reflow:function(){d.extend(this.SegmentArc.prototype,{x:this.chart.width/2,y:this.chart.height/2});this.updateScaleRange(this.segments);
this.scale.update();d.extend(this.scale,{xCenter:this.chart.width/2,yCenter:this.chart.height/2});d.each(this.segments,function(c){c.update({outerRadius:this.scale.calculateCenterOffset(c.value)})},this)},draw:function(c){var b=c||1;this.clear();d.each(this.segments,function(c,d){c.transition({circumference:this.scale.getCircumference(),outerRadius:this.scale.calculateCenterOffset(c.value)},b);c.endAngle=c.startAngle+c.circumference;0===d&&(c.startAngle=1.5*Math.PI);d<this.segments.length-1&&(this.segments[d+
1].startAngle=c.endAngle);c.draw()},this);this.scale.draw()}})}).call(this);
(function(){var l=this.Chart,d=l.helpers;l.Type.extend({name:"Radar",defaults:{scaleShowLine:!0,angleShowLineOut:!0,scaleShowLabels:!1,scaleBeginAtZero:!0,angleLineColor:"rgba(0,0,0,.1)",angleLineWidth:1,angleLineInterval:1,pointLabelFontFamily:"'Arial'",pointLabelFontStyle:"normal",pointLabelFontSize:10,pointLabelFontColor:"#666",pointDot:!0,pointDotRadius:3,pointDotStrokeWidth:1,pointHitDetectionRadius:20,datasetStroke:!0,datasetStrokeWidth:2,datasetFill:!0,legendTemplate:'\x3cul class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend"\x3e\x3c% for (var i\x3d0; i\x3cdatasets.length; i++){%\x3e\x3cli\x3e\x3cspan class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend-icon" style\x3d"background-color:\x3c%\x3ddatasets[i].strokeColor%\x3e"\x3e\x3c/span\x3e\x3cspan class\x3d"\x3c%\x3dname.toLowerCase()%\x3e-legend-text"\x3e\x3c%if(datasets[i].label){%\x3e\x3c%\x3ddatasets[i].label%\x3e\x3c%}%\x3e\x3c/span\x3e\x3c/li\x3e\x3c%}%\x3e\x3c/ul\x3e'},
initialize:function(c){this.PointClass=l.Point.extend({strokeWidth:this.options.pointDotStrokeWidth,radius:this.options.pointDotRadius,display:this.options.pointDot,hitDetectionRadius:this.options.pointHitDetectionRadius,ctx:this.chart.ctx});this.datasets=[];this.buildScale(c);this.options.showTooltips&&d.bindEvents(this,this.options.tooltipEvents,function(b){b="mouseout"!==b.type?this.getPointsAtEvent(b):[];this.eachPoints(function(b){b.restore(["fillColor","strokeColor"])});d.each(b,function(b){b.fillColor=
b.highlightFill;b.strokeColor=b.highlightStroke});this.showTooltip(b)});d.each(c.datasets,function(b){var e={label:b.label||null,fillColor:b.fillColor,strokeColor:b.strokeColor,pointColor:b.pointColor,pointStrokeColor:b.pointStrokeColor,points:[]};this.datasets.push(e);d.each(b.data,function(d,k){var l;this.scale.animation||(l=this.scale.getPointPosition(k,this.scale.calculateCenterOffset(d)));e.points.push(new this.PointClass({value:d,label:c.labels[k],datasetLabel:b.label,x:this.options.animation?
this.scale.xCenter:l.x,y:this.options.animation?this.scale.yCenter:l.y,strokeColor:b.pointStrokeColor,fillColor:b.pointColor,highlightFill:b.pointHighlightFill||b.pointColor,highlightStroke:b.pointHighlightStroke||b.pointStrokeColor}))},this)},this);this.render()},eachPoints:function(c){d.each(this.datasets,function(b){d.each(b.points,c,this)},this)},getPointsAtEvent:function(c){c=d.getRelativePosition(c);c=d.getAngleFromPoint({x:this.scale.xCenter,y:this.scale.yCenter},c);var b=Math.round((c.angle-
1.5*Math.PI)/(2*Math.PI/this.scale.valuesCount)),e=[];return(b>=this.scale.valuesCount||0>b)&&(b=0),c.distance<=this.scale.drawingArea&&d.each(this.datasets,function(c){e.push(c.points[b])}),e},buildScale:function(c){this.scale=new l.RadialScale({display:this.options.showScale,fontStyle:this.options.scaleFontStyle,fontSize:this.options.scaleFontSize,fontFamily:this.options.scaleFontFamily,fontColor:this.options.scaleFontColor,showLabels:this.options.scaleShowLabels,showLabelBackdrop:this.options.scaleShowLabelBackdrop,
backdropColor:this.options.scaleBackdropColor,backgroundColors:this.options.scaleBackgroundColors,backdropPaddingY:this.options.scaleBackdropPaddingY,backdropPaddingX:this.options.scaleBackdropPaddingX,lineWidth:this.options.scaleShowLine?this.options.scaleLineWidth:0,lineColor:this.options.scaleLineColor,angleLineColor:this.options.angleLineColor,angleLineWidth:this.options.angleShowLineOut?this.options.angleLineWidth:0,angleLineInterval:this.options.angleLineInterval?this.options.angleLineInterval:
1,pointLabelFontColor:this.options.pointLabelFontColor,pointLabelFontSize:this.options.pointLabelFontSize,pointLabelFontFamily:this.options.pointLabelFontFamily,pointLabelFontStyle:this.options.pointLabelFontStyle,height:this.chart.height,width:this.chart.width,xCenter:this.chart.width/2,yCenter:this.chart.height/2,ctx:this.chart.ctx,templateString:this.options.scaleLabel,labels:c.labels,valuesCount:c.datasets[0].data.length});this.scale.setScaleSize();this.updateScaleRange(c.datasets);this.scale.buildYLabels()},
updateScaleRange:function(c){var b=function(){var b=[];return d.each(c,function(c){c.data?b=b.concat(c.data):d.each(c.points,function(c){b.push(c.value)})}),b}(),b=this.options.scaleOverride?{steps:this.options.scaleSteps,stepValue:this.options.scaleStepWidth,min:this.options.scaleStartValue,max:this.options.scaleStartValue+this.options.scaleSteps*this.options.scaleStepWidth}:d.calculateScaleRange(b,d.min([this.chart.width,this.chart.height])/2,this.options.scaleFontSize,this.options.scaleBeginAtZero,
this.options.scaleIntegersOnly);d.extend(this.scale,b)},addData:function(c,b){this.scale.valuesCount++;d.each(c,function(c,d){var k=this.scale.getPointPosition(this.scale.valuesCount,this.scale.calculateCenterOffset(c));this.datasets[d].points.push(new this.PointClass({value:c,label:b,datasetLabel:this.datasets[d].label,x:k.x,y:k.y,strokeColor:this.datasets[d].pointStrokeColor,fillColor:this.datasets[d].pointColor}))},this);this.scale.labels.push(b);this.reflow();this.update()},removeData:function(){this.scale.valuesCount--;
this.scale.labels.shift();d.each(this.datasets,function(c){c.points.shift()},this);this.reflow();this.update()},update:function(){this.eachPoints(function(c){c.save()});this.reflow();this.render()},reflow:function(){d.extend(this.scale,{width:this.chart.width,height:this.chart.height,size:d.min([this.chart.width,this.chart.height]),xCenter:this.chart.width/2,yCenter:this.chart.height/2});this.updateScaleRange(this.datasets);this.scale.setScaleSize();this.scale.buildYLabels()},draw:function(c){var b=
c||1,e=this.chart.ctx;this.clear();this.scale.draw();d.each(this.datasets,function(c){d.each(c.points,function(c,d){c.hasValue()&&c.transition(this.scale.getPointPosition(d,this.scale.calculateCenterOffset(c.value)),b)},this);e.lineWidth=this.options.datasetStrokeWidth;e.strokeStyle=c.strokeColor;e.beginPath();d.each(c.points,function(b,c){0===c?e.moveTo(b.x,b.y):e.lineTo(b.x,b.y)},this);e.closePath();e.stroke();e.fillStyle=c.fillColor;this.options.datasetFill&&e.fill();d.each(c.points,function(b){b.hasValue()&&
b.draw()})},this)}})}).call(this);