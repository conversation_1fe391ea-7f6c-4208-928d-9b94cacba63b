# 主从复制配置
## 配置
### 1. 配置文件 - 永久

```
# 修改为从库监听的端口号
port 6379
# 添加需要同步的主库信息（在从库中的配置文件中添加）
replicaof ***************(ip) 7001(端口)
```
### 2. 命令 - 临时
```
redis-cli -p 7002 （进入客户端
slaveof *************** 7001 (进入从库客户端执行)
```
## 原理
### 全量同步流程图
![img_3.png](img_3.png)
### 增量同步流程图
![img_6.png](img_6.png)
### 同步原理
![img_4.png](img_4.png)
1. Replication Id（runid），如果从节点没有保存任何主节点的运行ID（runid），它将视为第一次连接到主节点，第一次则全量同步也就是RDB（快照）。
2. offset，主节点和从节点都会维护一个offset，记录自己已经同步到主节点的数据了
（从节点的offset是已同步数据，与master的offset之间的差距则是未同步的数据，
如果两者的offset一样则不需要增量同步，反之进行增量同步。）。

## 问题
1. 如果slave太长时间未同步（像一个圆环，被覆盖）则进行全量同步（很慢）。

## 解决方案、优化方案
![img_7.png](img_7.png)

## 小结
![img_5.png](img_5.png)
![img_8.png](img_8.png)
https://www.cnblogs.com/wzh2010/p/18030905
## sentinel哨兵集群
### 基本概念
![img_9.png](img_9.png)
#### 如果主宕机了
1. 多个sentinel通过心跳检测（连接不上master）主观认为master宕机了（一般为 sentinel总/2）则客观认为master宕机了，
2. sentinel会从slave中选出一个slave作为master，然后通知其他slave成为新的master的slave（
谁发现就由谁来处理通知，顺序为 根据优先级（如果配置了）-> offset大的->id小的）。
### 配置
```
sh redis-cli --cluster create --cluster-replicas 1 
***************:7001 ***************:7002 
***************:7003 ***************:8001 
***************:8002 ***************:8003
```
命令说明：
1. redis-cli --cluster或者./redis-trib.rb：代表集群操作命令
2. create：代表是创建集群
3. --replicas 1或者--cluster-replicas 1 ：指定集群中每个master的副本个数为1，
 >此时节点总数 ÷ (replicas + 1) 得到的就是master的数量。
 >因此节点列表中的前n个就是master，其它节点都是slave节点，随机分配到不同master

## Cluster模式 分片集群（服务端进行分片）
Redis Cluster 是一种分布式集群模式，它允许将数据分散存储在多个节点上，从而提供了横向扩展、高可用性和更大存储容量。Redis Cluster 有以下关键特点：
### 特点
1. 分布式数据存储: Redis Cluster 将数据划分为多个槽（slot），这些槽分布在不同的节点上。每个节点负责管理一部分槽中的数据，这样数据可以分布在多个节点上，避免了单节点存储容量的限制。

2. 自动数据分片: Redis Cluster 使用哈希槽来分片数据，客户端根据键的哈希值将数据路由到正确的节点。这使得数据的分片和路由是自动的，不需要手动管理数据分片。

3. 高可用性: Redis Cluster 提供了内置的高可用性机制。数据会在多个节点之间复制，每个槽位都会有一个主节点和多个从节点。如果主节点失效，系统会自动选举一个从节点作为新的主节点，确保数据的可用性。

### Cluster 扩展知识
1. 需要两个端口6379、16379（同步通讯），16379也就是cluster bus同时用了二进制gossip协议，可用于故障检测、配置更新、故障转移，有点可减少占用网络带宽、处理时间。
2. 主从间同步默认是异步，可以通过配置参数cluster-require-full-coverage=no来关闭，以保证强一致性。
3. 只能使用0号数据库
### Redis 集群模式解决的问题
Redis 集群模式是为了解决在大规模数据存储和高可用性需求下出现的问题而引入的。在前面的文章中介绍了 Redis 哨兵模式，哨兵模式提供了系统的高可用性，但实际上所有的数据都需要储存到单个 master 和其对应的 slave 节点中。这种架构在面对大规模数据存储的挑战时会遇到一些问题，主要包括以下方面的挑战和限制：

1. 内存限制： Redis 是内存数据库，数据存储在内存中以提供快速访问。如果数据量非常大，接近或超出了单个 master 和其对应的 slave 节点的物理内存容量，就会出现内存不足的问题。这会导致性能下降，甚至系统崩溃。

2. 水平扩展问题： 在哨兵模式下，要扩展存储容量或处理更多的请求，通常需要升级硬件，将单个节点的内存容量增加到更大的规模。但这种垂直扩展的方式存在成本高昂和物理限制的问题，因此在大规模数据存储需求下不够灵活。

为了解决这些问题，Redis 引入了集群模式。Redis 集群模式的核心思想是利用多组 Master/Slave 节点来分散存储数据，并提供更大的内存容量和高可用性。以下是 Redis 集群模式解决的问题和优势
1. 问题1 内存限制
Redis 集群模式通过将数据分散存储在多个节点上来解决内存限制问题。每个 Master 节点都可以存储数据的一部分，因此整个集群的总内存容量可以随着添加更多节点而线性增加。这使得 Redis 集群能够处理更大规模的数据，而无需单节点内存升级。
2. 问题2 水平扩展问题
Redis 集群模式通过分片（Sharding）机制实现水平扩展。每个分片由一组 Master/Slave 节点管理，而不是依赖于单一节点的垂直扩展。这允许系统在需要时简单地添加更多分片，以满足不断增长的存储和请求需求。这种横向扩展的方式更加灵活，可以适应不同的工作负载和数据规模。
3. 问题3 高可用性
Redis 集群模式提供了内置的高可用性支持。每个 Master 节点都有对应的 Slave 节点作为备份。如果一个 Master 节点发生故障，其对应的 Slave 节点可以自动升级为新的 Master 节点，从而确保数据的可用性和系统的持续运行。这种自动故障切换机制大大提高了系统的稳定性。


## 安装和配置
ip配置 
![img_11.png](img_11.png)
|      IP      | PORT |    REMARK    |
|:------------:|:----:|:------------:|
| ***********  | 7001 | 主节点master-1  |
| ***********  | 7002 | 主节点master-2  | 
| ***********  | 7003 | 主节点master-3  |
| ***********  | 6379 |  从节点slave-1  |
| ***********  | 6379 |  从节点slave-2  |
| ***********  | 6379 |  从节点slave-3  |
### 创建集群网络
```bash
  docker network create --subnet=**********/24 redis-net
```
### 配置文件 可全都用master这份
1. master
```
port 6379
# 集群开关，默认是不开启集群模式
cluster-enabled yes
# 集群配置文件的名称，每个节点都有一个集群相关的配置文件，持久化保存集群的信息 集群配置文件(自动创建)
cluster-config-file nodes.conf
# 节点互连超时的阀值。集群节点超时毫秒数 集群超时时间(毫秒)
cluster-node-timeout 15000
# 持久化 yes
appendonly yes
# 后台运行
daemonize no
# 允许外部IP访问
protected-mode no
# requirepass 123456
# masterauth 123456
pidfile /var/run/redis.pid
#  集群节点 IP，如果想要集群可以供外网访问，需要改
cluster-announce-ip ***********
cluster-announce-port 6379
cluster-announce-bus-port 16379
```
2. slave
```
port 6379
# 不参与集群分配
cluster-enabled no
# 持久化 yes
appendonly yes
#  后台运行
daemonize no
protected-mode no
pidfile  /var/run/redis.pid
#直接指定成为对应master的从节点 需要改
slaveof *********** 6379
```
### 启动容器
```bash
docker run -d --network redis-net --ip ***********  -p 6381:6381 -p 16381:16379 --privileged=true --restart always\
-v E:/study/environment/dockerData/Container/redis/master1/conf/redis.conf:/etc/redis/redis.conf \
-v E:/study/environment/dockerData/Container/redis/master1/data:/data \
--name redis-master1 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes
   
docker run -d --network redis-net --ip ***********  -p 6391:6379 --privileged=true --restart always\
-v E:/study/environment/dockerData/Container/redis/slave1/conf/redis.conf:/etc/redis/redis.conf \
-v E:/study/environment/dockerData/Container/redis/slave1/data:/data \
--name redis-slave1 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes
```
```bash
docker run -d --network redis-net --ip ***********  -p 6382:6382 -p 16382:16379 --privileged=true --restart always\
-v E:/study/environment/dockerData/Container/redis/master2/conf/redis.conf:/etc/redis/redis.conf \
-v E:/study/environment/dockerData/Container/redis/master2/data:/data \
--name redis-master2 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes

docker run -d --network redis-net --ip ***********  -p 6392:6379 --privileged=true --restart always\
-v E:/study/environment/dockerData/Container/redis/slave2/conf/redis.conf:/etc/redis/redis.conf \
-v E:/study/environment/dockerData/Container/redis/slave2/data:/data \
--name redis-slave2 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes
```

```bash
docker run -d --network redis-net --ip ***********  -p 6383:6383 -p 16383:16379 --privileged=true --restart always\
-v E:/study/environment/dockerData/Container/redis/master3/conf/redis.conf:/etc/redis/redis.conf \
-v E:/study/environment/dockerData/Container/redis/master3/data:/data \
--name redis-master3 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes

docker run -d --network redis-net --ip *********** -p 6393:6379 --privileged=true --restart always \
-v E:/study/environment/dockerData/Container/redis/slave3/conf/redis.conf:/etc/redis/redis.conf \
-v E:/study/environment/dockerData/Container/redis/slave3/data:/data \
--name redis-slave3 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes
```
### windows端 执行
```bash
docker volume create redis-master1-data
docker volume create redis-master2-data
docker volume create redis-master3-data
docker volume create redis-slave1-data
docker volume create redis-slave2-data
docker volume create redis-slave3-data
```
```bash
docker run -d --network redis-net --ip *********** -p 6381:6379 -p 16381:16379 --privileged=true --restart always -v E:/study/environment/dockerData/Container/redis/master1/conf/redis.conf:/etc/redis/redis.conf -v redis-master1-data:/data --name redis-master1 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes
docker run -d --network redis-net --ip *********** -p 6391:6379 --privileged=true --restart always -v E:/study/environment/dockerData/Container/redis/slave1/conf/redis.conf:/etc/redis/redis.conf -v redis-slave1-data:/data --name redis-slave1 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes
docker run -d --network redis-net --ip *********** -p 6382:6379 -p 16382:16379 --privileged=true --restart always -v E:/study/environment/dockerData/Container/redis/master2/conf/redis.conf:/etc/redis/redis.conf -v redis-master2-data:/data --name redis-master2 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes
docker run -d --network redis-net --ip *********** -p 6392:6379 --privileged=true --restart always -v E:/study/environment/dockerData/Container/redis/slave2/conf/redis.conf:/etc/redis/redis.conf -v redis-slave2-data:/data --name redis-slave2 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes
docker run -d --network redis-net --ip *********** -p 6383:6379 -p 16383:16379 --privileged=true --restart always -v E:/study/environment/dockerData/Container/redis/master3/conf/redis.conf:/etc/redis/redis.conf -v redis-master3-data:/data --name redis-master3 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes
docker run -d --network redis-net --ip *********** -p 6393:6379 --privileged=true --restart always -v E:/study/environment/dockerData/Container/redis/slave3/conf/redis.conf:/etc/redis/redis.conf -v redis-slave3-data:/data --name redis-slave3 redis:6.2.6 redis-server /etc/redis/redis.conf --appendonly yes
```

### 创建集群并分配slots
此时仅仅创建了三对主从节点，但是三个主节点(6381, 6382, 6383)是孤立的，并且还未分配slots。 此时进入任意主节点执行
```bash
redis-cli --cluster create ***********:6379 ***********:6379 ***********:6379 --cluster-replicas 0
```
如果需要密码
```bash
redis-cli -a 123456 --cluster create ***********:6379 ***********:6379 ***********:6379 --cluster-replicas 0
```
### 成功显示
![img_12.png](img_12.png)

### 遇到的问题
1. Failed trying to load the MASTER synchronization DB from disk
  - 原因：估计是系统原因，取消挂卷。或者redis.conf配置文件中，dir配置项的路径不存在

## 参考
https://www.cnblogs.com/wzh2010/p/15886799.html
https://www.jianshu.com/p/339549138e07
https://blog.csdn.net/qq_61635026/article/details/133158882