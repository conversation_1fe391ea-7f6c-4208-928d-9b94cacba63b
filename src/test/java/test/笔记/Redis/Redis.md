## 什么是 Redis？
> Redis （REmote DIctionary Server）是一个基于 C 语言开发的开源 NoSQL 数据库（BSD 许可）。与传统数据库不同的是，Redis 的数据是保存在内存中的（内存数据库，支持持久化），因此读写速度非常快，被广泛应用于分布式缓存方向。并且，Redis 存储的是 KV 键值对数据。
为了满足不同的业务场景，Redis 内置了多种数据类型实现（比如 String、Hash、Sorted Set、Bitmap、HyperLogLog、GEO）。并且，Redis 还支持事务、持久化、Lua 脚本、发布订阅模型、多种开箱即用的集群方案（Redis Sentinel、Redis Cluster）。
1. string（字符串）: 基本的数据存储单元，可以存储字符串、整数或者浮点数。缓存数字、json
2. hash（哈希）:一个键值对集合，可以存储多个字段。 存储对象
3. list（列表）:一个简单的列表，可以存储一系列的字符串元素。最新消息流
4. set（集合）:一个无序集合，可以存储不重复的字符串元素。 标签存储、去重
5. zset(sorted set：有序集合): 类似于集合，但是每个元素都有一个分数（score）与之关联。排行榜
6. 位图（Bitmaps）：基于字符串类型，可以对每个位进行操作。 计数器功能、活跃用户统计
7. 超日志（HyperLogLogs）：用于基数统计，可以估算集合中的唯一元素数量。唯一访问统计（例如统计网站的日独立访问量）、大规模去重（例如统计日志中的独立 IP）。
8. 地理空间（Geospatial）：用于存储地理位置信息。附近的人或位置
9. 发布/订阅（Pub/Sub）：一种消息通信模式，允许客户端订阅消息通道，并接收发布到该通道的消息。消息订阅
10. 流（Streams）：用于消息队列和日志存储，支持消息的持久化和时间排序。实时消息系统（例如聊天消息流）、数据处理管道（例如日志处理和分析）。
11. 模块（Modules）：Redis 支持动态加载模块，可以扩展 Redis 的功能。

![img.png](img.png)
## Redis 除了做缓存，还能做什么？
> -  分布式锁：通过 Redis 来做分布式锁是一种比较常见的方式。通常情况下，我们都是基于 Redisson 来实现分布式锁。关于 Redis 实现分布式锁的详细介绍，可以看我写的这篇文章：分布式锁详解 。
> - 限流：一般是通过 Redis + Lua 脚本的方式来实现限流。如果不想自己写 Lua 脚本的话，也可以直接利用 Redisson 中的 RRateLimiter 来实现分布式限流，其底层实现就是基于 Lua 代码+令牌桶算法。
> - 消息队列：Redis 自带的 List 数据结构可以作为一个简单的队列使用。Redis 5.0 中增加的 Stream 类型的数据结构更加适合用来做消息队列。它比较类似于 Kafka，有主题和消费组的概念，支持消息持久化以及 ACK 机制。
> - 延时队列：Redisson 内置了延时队列（基于 Sorted Set 实现的）。
> - 分布式 Session ：利用 String 或者 Hash 数据类型保存 Session 数据，所有的服务器都可以访问。
> - 复杂业务场景：通过 Redis 以及 Redis 扩展（比如 Redisson）提供的数据结构，我们可以很方便地完成很多复杂的业务场景比如通过 Bitmap 统计活跃用户、通过 Sorted Set 维护排行榜。
> -  ……

## Redis 持久化机制（重要）
Redis 持久化机制（RDB 持久化、AOF 持久化、RDB 和 AOF 的混合持久化）。
- 快照（snapshotting，RDB）
> - RDB 文件存储的内容是经过压缩的二进制数据， 保存着某个时间点的数据集，文件很小，适合做数据的备份，灾难恢复。AOF 文件存储的是每一次写命令，类似于 MySQL 的 binlog 日志，通常会比 RDB 文件大很多。当 AOF 变得太大时，Redis 能够在后台自动重写 AOF。新的 AOF 文件和原有的 AOF 文件所保存的数据库状态一样，但体积更小。不过， Redis 7.0 版本之前，如果在重写期间有写入命令，AOF 可能会使用大量内存，重写期间到达的所有写入命令都会写入磁盘两次。
> - 使用 RDB 文件恢复数据，直接解析还原数据即可，不需要一条一条地执行命令，速度非常快。而 AOF 则需要依次执行每个写命令，速度非常慢。也就是说，与 AOF 相比，恢复大数据集的时候，RDB 速度更快。

- 只追加文件（append-only file, AOF）
> - RDB 的数据安全性不如 AOF，没有办法实时或者秒级持久化数据。生成 RDB 文件的过程是比较繁重的， 虽然 BGSAVE 子进程写入 RDB 文件的工作不会阻塞主线程，但会对机器的 CPU 资源和内存资源产生影响，严重的情况下甚至会直接把 Redis 服务干宕机。AOF 支持秒级数据丢失（取决 fsync 策略，如果是 everysec，最多丢失 1 秒的数据），仅仅是追加命令到 AOF 文件，操作轻量。
> - RDB 文件是以特定的二进制格式保存的，并且在 Redis 版本演进中有多个版本的 RDB，所以存在老版本的 Redis 服务不兼容新版本的 RDB 格式的问题。
> - AOF 以一种易于理解和解析的格式包含所有操作的日志。你可以轻松地导出 AOF 文件进行分析，你也可以直接操作 AOF 文件来解决一些问题。比如，如果执行FLUSHALL命令意外地刷新了所有内容后，只要 AOF 文件没有被重写，删除最新命令并重启即可恢复之前的状态。

- RDB 和 AOF 的混合持久化(Redis 4.0 新增),
> 由于 RDB 和 AOF 各有优势，于是，Redis 4.0 开始支持 RDB 和 AOF 的混合持久化（默认关闭，可以通过配置项 aof-use-rdb-preamble 开启）。如果把混合持久化打开，AOF 重写的时候就直接把 RDB 的内容写到 AOF 文件开头。这样做的好处是可以结合 RDB 和 AOF 的优点, 快速加载同时避免丢失过多的数据。当然缺点也是有的， AOF 里面的 RDB 部分是压缩格式不再是 AOF 格式，可读性较差。恢复时默认优先AOF。

- 综上：
> - Redis 保存的数据丢失一些也没什么影响的话，可以选择使用 RDB。
> - 不建议单独使用 AOF，因为时不时地创建一个 RDB 快照可以进行数据库备份、更快的重启以及解决 AOF 引擎错误。
> - 如果保存的数据要求安全性比较高的话，建议同时开启 RDB 和 AOF 持久化或者开启 RDB 和 AOF 混合持久化。


## Redis 线程模型（重要）
对于读写命令来说，Redis 一直是单线程模型。不过，在 Redis 4.0 版本之后引入了多线程来执行一些大键值对的异步删除操作， Redis 6.0 版本之后引入了多线程来处理网络请求（提高网络 IO 读写性能）。

## Redis 过期 key 删除策略了解么？
![img_1.png](img_1.png)
## Redis 内存淘汰策略了解么？
![img_2.png](img_2.png)
```
config get maxmemory
maxmemory
0
```
#### Redis提供了6种内存淘汰策略：
1. volatile-lru(least recently used):从已设置过期时间的数据集(server.db[i].expires)中挑选
最近最少使用的数据淘汰。
2. allkeys-lru(least recently used):从数据集(server.db[i].dict)中移除最近最少使用的数据淘汰。
3. volatile-random:从已设置过期时间的数据集(server.db[i].expires)中任意选择数据淘汰。
4. allkeys-random:从数据集(server.db[i].dict)中任意选择数据淘汰。
5. volatile-ttl:从已设置过期时间的数据集(server.db[i].expires)中挑选将要过期的数据淘汰。
6. no-eviction(默认内存淘汰策略)：禁止驱逐数据，当内存不足以容纳新写入数据时，新写入操作会报
错。
#### 4.0版本后增加以下两种：
7. volatile-lfu(least frequently used):从已设置过期时间的数据集(server.db[i].expires)中挑
选最不经常使用的数据淘汰。
8. allkeys-lfu(least frequently used):从数据集(server.db[i].dict)中移除最不经常使用的数据
淘汰。
- allkeys-xxx 表示从所有的键值中淘汰数据，而 volatile-xxx 表示从设置了过期时间的键值中淘汰数据。
## 集群方式
Redis提供了多种集群方式，每种方式都有其特点和适用场景。以下是一些常见的Redis集群方式：

1. **主从复制（Master-Slave Replication）**：这是最简单的Redis集群方式，它将数据复制到多个副本中，以提高数据的可用性和读取性能。主节点负责处理写操作，从节点负责处理读操作。主节点可以将数据同步到从节点，以保持数据的一致性。主从复制的主要特点是简单易用，但是它不支持自动故障转移，如果主节点发生故障，需要手动切换到从节点。

2. **哨兵模式（Sentinel）**：哨兵模式是主从复制的一种扩展，它通过监控主节点的状态，并在主节点发生故障时自动切换到从节点。哨兵模式的主要特点是自动故障转移，但是它仍然不支持自动的负载均衡和水平扩展。

3. **Redis Cluster**：Redis Cluster是Redis官方提供的集群方式，它将数据分布在多个节点上，以实现自动的负载均衡和水平扩展。Redis Cluster使用哈希槽（hash slots）来将数据分布在多个节点上，每个节点负责处理一部分哈希槽。Redis Cluster的主要特点是高性能、高可用性和可扩展性，但是它不支持事务和发布/订阅功能。

4. **Redis Sharding**：Redis Sharding是一种自定义的集群方式，它将数据分布在多个节点上，以实现负载均衡和水平扩展。Redis Sharding可以通过客户端或代理来实现，它支持事务和发布/订阅功能。Redis Sharding的主要特点是灵活性和可扩展性，但是它需要额外的配置和管理。

总的来说，选择哪种集群方式取决于你的具体需求，例如你的数据量、读写比例、可用性要求等。

## Redis 生产问题（重要）
1. 缓存穿透
   - 缓存穿透说简单点就是大量请求的 key 是不合理的，根本不存在于缓存中，也不存在于数据库中 。这就导致这些请求直接到了数据库上，根本没有经过缓存这一层，对数据库造成了巨大的压力，可能直接就被这么多请求弄宕机了。
   - 解决办法：
   1. 缓存无效 key
   2. 布隆过滤器
   3. 限流
2. 缓存击穿
    - 缓存击穿中，请求的 key 对应的是 热点数据 ，该数据 存在于数据库中，但不存在于缓存中（通常是因为缓存中的那份数据已经过期） 。这就可能会导致瞬时大量的请求直接打到了数据库上，对数据库造成了巨大的压力，可能直接就被这么多请求弄宕机了。
    - 解决办法：
   1. 永不过期（不推荐）：设置热点数据永不过期或者过期时间比较长。
   2. 提前预热（推荐）：针对热点数据提前预热，将其存入缓存中并设置合理的过期时间比如秒杀场景下的数据在秒杀结束之前不过期。
   3. 加锁（看情况）：在缓存失效后，通过设置互斥锁确保只有一个请求去查询数据库并更新缓存。
3. 缓存雪崩
   - 实际上，缓存雪崩描述的就是这样一个简单的场景：缓存在同一时间大面积的失效，导致大量的请求都直接落到了数据库上，
   对数据库造成了巨大的压力。 这就好比雪崩一样，摧枯拉朽之势，数据库的压力可想而知，可能直接就被这么多请求弄宕机了。另外，缓存服务宕机也会导致缓存雪崩现象，导致所有的请求都落到了数据库上。
    - 解决办法：
    针对Redis服务不可用的情况：
   > 1. Redis集群：采用Redis集群，避免单机出现问题整个缓存服务都没办法使用。Redis Cluster和Redis
    Sentinel是两种最常用的Redis集群实现方案，详细介绍可以参考：Redis集群详解（付费）心。
   > 2. 多级缓存：设置多级缓存，例如本地缓存+Rdis缓存的二级缓存组合，当Redis缓存出现问题时，还可
       以从本地缓存中获取到部分数据。
    
    针对大量缓存同时失效的情况：
   > 1. 设置随机失效时间（可选）：为缓存设置随机的失效时间，例如在固定过期时间的基础上加上一个随机
   值，这样可以避免大量缓存同时到期，从而减少缓存雪崩的风险。
   > 2. 提前预热（推荐）：针对热点数据提前预热，将其存入缓存中并设置合理的过期时间比如秒杀场景下的
   数据在秒杀结束之前不过期。
   > 3. 持久缓存策略（看情况）：虽然一般不推荐设置缓存永不过期，但对于某些关键性和变化不频繁的数
   据，可以考虑这种策略。

### 缓存预热如何实现？
常见的缓存预热方式有两种：
1. 使用定时任务，比如xxl-job,来定时触发缓存预热的逻辑，将数据库中的热点数据查询出来并存入缓存
中。
2. 使用消息队列，比如Kaka,来异步地进行缓存预热，将数据库中的热点数据的主键或者ID发送到消息
队列中，然后由缓存服务消费消息队列中的数据，根据主键或者D查询数据库并更新缓存。

## Redis使用规范
实际使用Rdis的过程中，我们尽量要准守一些常见的规范，比如：
1. 使用连接池：避免频繁创建关闭客户端连接。
2. 尽量不使用O(n)指令，使用On)命令时要关注n的数量：像 KEYS *、HGETALL、LRANGE、SMEMBERS、SINTER/SUNION/SDIFF等 O(n) 命令并非不能使用，但是需要明确n的值。另外，有遍历 的需求可以使用HSCAN、SSCAN、 ZSCAN代替。
3. 使用批量操作减少网络传输：原生批量操作命令（比如MGT、 MsET等等)、pipeline、Lua脚本。
4. 尽量不适用Redis事务：Redis事务实现的功能比较鸡肋，可以使用Lua脚本代替。
5. 禁止长时间开启monitor:对性能影响比较大。
6. 控制key的生命周期：避免Redis中存放了太多不经常被访问的数据。
7. …
## 在springboot中的使用
### 1. 配置连接池
   1. Lettuce更适合需要异步处理、线程安全以及支持哨兵和集群模式的场景（线程安全）；默认
      1. 特点: 基于Netty实现，支持异步和响应式编程。
      2. 连接池: Lettuce使用LettucePoolingClientConfiguration来配置连接池。
      3. 默认配置: 在Spring Boot 2.x及以上版本中，默认使用Lettuce作为Redis客户端。
   2. Jedis则更适合简单的同步操作，以及在不需要哨兵和集群模式的场景中使用（线程不安全）。
      1. 特点: 传统的Java同步客户端，不支持异步和响应式编程。
      2. 连接池: Jedis使用JedisPoolConfig来配置连接池。
      3. 需要在spring-boot-starter-data-redis中排包lettuce-core，并引入jedis的包
```
    lettuce:
      pool:
        max-idle: 16
        max-active: 32
        min-idle: 8
        max-wait: 2000ms
```
### *tip* *还需要添加这个依赖连接池才生效*
```pom
<dependency>
    <groupId>org.apache.commons</groupId>
    <artifactId>commons-pool2</artifactId>
</dependency>
```
### 为什么使用Lettuce
使用 **Lettuce** 的主要原因如下：

1. **线程安全**  
   Lettuce 的连接是线程安全的，可以被多个线程共享，而 Jedis 的连接不是线程安全的，每个线程需要单独的连接。

2. **支持异步操作**  
   Lettuce 支持异步和响应式编程（基于 Netty），适合高并发场景，而 Jedis 只支持同步操作。

3. **更好的集群支持**  
   Lettuce 对 Redis 集群模式支持更好，能自动处理槽位映射变化，减少因槽位迁移导致的问题。

4. **Spring Boot 默认选择**  
   从 Spring Boot 2.x 开始，默认使用 Lettuce 作为 Redis 客户端，方便快速集成。

5. **性能更优**  
   Lettuce 使用 Netty 框架进行网络通信，具有更高的性能和更低的延迟。



### 2. 集群配置
```yaml
spring:
  redis:
    password: 123456
    cluster:
      nodes: redisIp1:port,redisIp2:port,......
      max-redirects: 3
```

### 3. 序列化方式
   序列化 （正常都需要自定义序列化）
   1. Redis本身提供了一下一种序列化的方式：
   2. GenericToStringSerializer: 可以将任何对象泛化为字符串并序列化
   3. Jackson2JsonRedisSerializer: 跟JacksonJsonRedisSerializer实际上是一样的
   4. <span style="color:red;">JacksonJsonRedisSerializer: 序列化object对象为json字符串  常用</span> 
   5. JdkSerializationRedisSerializer: 序列化java对象 
   6. StringRedisSerializer: 简单的字符串序列化
#### tip 
如果我们存储的是String类型，默认使用的是StringRedisSerializer 这种序列化方式。 
如果我们存储的是对象，默认使用的是 JdkSerializationRedisSerializer，
也就是Jdk的序列化方式（通过ObjectOutputStream和ObjectInputStream实现，缺点是我们无法直观看到存储的对象内容）。
<span style="color:red;">所以我们经常使用Jackson2JsonRedisSerializer </span> 可读性、兼容性、灵活性和性能方面表现优异，特别适合在基于 Spring 的项目中使用。它不仅能让 Redis 数据更易于理解和维护

