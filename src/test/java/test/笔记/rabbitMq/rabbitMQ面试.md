# 概念和特点特点
RabbitMQ 是一个由 Erlang 语言开发的 AMQP 的开源实现。

AMQP ：Advanced Message Queue，高级消息队列协议。它是应用层协议的一个开放
标准，为面向消息的中间件设计，基于此协议的客户端与消息中间件可传递消息，并不
受产品、开发语言等条件的限制。

RabbitMQ 最初起源于金融系统，用于在分布式系统中存储转发消息，在易用性、扩展
性、高可用性等方面表现不俗。具体特点包括：

1. 可靠性（Reliability）
RabbitMQ 使用一些机制来保证可靠性，如持久化、传输确认、发布确认。

2. 灵活的路由（Flexible Routing）
在消息进入队列之前，通过 Exchange 来路由消息的。对于典型的路由功能，RabbitMQ
已经提供了一些内置的 Exchange 来实现。针对更复杂的路由功能，可以将多个
Exchange 绑定在一起，也通过插件机制实现自己的 Exchange 。

3. 消息集群（Clustering）
多个 RabbitMQ 服务器可以组成一个集群，形成一个逻辑 Broker 。

4. 高可用（Highly Available Queues）
队列可以在集群中的机器上进行镜像，使得在部分节点出问题的情况下队列仍然可用。

5. 多种协议（Multi-protocol）
RabbitMQ 支持多种消息队列协议，比如 STOMP、MQTT 等等。

6. 多语言客户端（Many Clients）
RabbitMQ 几乎支持所有常用语言，比如 Java、.NET、Ruby 等等。

7. 管理界面（Management UI）
RabbitMQ 提供了一个易用的用户界面，使得用户可以监控和管理消息 Broker 的许多方
面。
8. 跟踪机制（Tracing）
如果消息异常，RabbitMQ 提供了消息跟踪机制，使用者可以找出发生了什么。

9. 插件机制（Plugin System）
RabbitMQ 提供了许多插件，来从多方面进行扩展，也可以编写自己的插件。
   https://www.cnblogs.com/helf/p/13566103.html

# 流程 
生产者发送消息的流程：
1. 生产者连接RabbitMQ，建立TCP连接(Connection)，开启信道（Channel）。
2. 生产者声明一个Exchange（交换器），并设置相关属性，比如交换器类型、是否持久化等。
3. 生产者声明一个队列井设置相关属性，比如是否排他、是否持久化、是否自动删除等。
4. 生产者通过bindingKey（绑定Key）将交换器和队列绑定（binding）起来。
5. 生产者发送消息至RabbitMQBroker，其中包含routingKey（路由键）、交换器等信息。
6. 相应的交换器根据接收到的routingKey查找相匹配的队列。
7. 如果找到，则将从生产者发送过来的消息存入相应的队列中。
8. 如果没有找到，则根据生产者配置的属性选择丢弃还是回退给生产者
9. 关闭信道。
10. 关闭连接。

# 消息顺序保证
1. 单一队列和单一消费者
   - 优点：
      确保消息按顺序处理，因为队列是 FIFO（先进先出）结构，且单一消费者按接收到的顺序处理消息。

   - 缺点：
      容量和性能瓶颈：单一消费者处理能力有限，可能导致性能瓶颈。
      单点故障风险：如果消费者失败，会导致消息处理中断。
2. 消息队列分区（分片）：按属性分区，每个队列顺序处理。
   如通过orderId取模 = 1/2/3 那么这个订单的所有操作都会落到同一个队列中。
# 消息幂等性
1. 全局唯一id 一般插入场景 
2. cas 通过version控制更新操作 
3. 业务控制 插入判断是否存在
![img_18.png](img_18.png)
# 消息堆积问题 
1. 临时扩容
可以扩容临时队列 临时队列设置容量是10倍容量 将堆积的消息添加临时队列
改生产者 消息投递到别的分区（可以是虚拟host，也可以新的mq） 新的消费者处理新的分区，旧的继续处理堆积的消息，处理完之后再回复原来的消费者
2. 