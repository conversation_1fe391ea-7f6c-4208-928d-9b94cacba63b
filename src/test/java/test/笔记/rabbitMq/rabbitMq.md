# docker
```docker
docker run -itd  --name rabbitmq  -p 5672:5672 -p 15672:15672 -e RABBITMQ_DEFAULT_USER=guest -e RABBITMQ_DEFAULT_PASS=guest  rabbitmq:3.9-
management
```
# 机构图 、原理
![img_17.png](img_17.png)
# 什么是MQ
> MQ（message queue），从字面意思上看，本质是个队列，FIFO先入先出，只不过队列中存放的内容是message而已，还是一种跨进程的通信机制，用于上下游传递消息。在互联网架构中，MQ是一种非常常见的上下游“逻辑解耦+物理解耦”的消息通信服务。使用了MQ之后，消息发送上游只需要依赖MQ，不用依赖其他服务。
# 作用
![img_12.png](img_12.png)
# 核心概念
![img_13.png](img_13.png)
# 交换机类型
1. Default Exchange：
   这是 RabbitMQ 默认实现的一种交换机，它不需要手动创建。当消息发布到默认交换机时，路由键会被解释为队列的名称，消息会被路由到与路由键名称相同的队列。默认交换机通常用于点对点通信，但不支持复杂的路由策略。
2. Direct Exchange：
这种交换机根据消息的路由键（Routing Key）将消息发送到与之完全匹配的队列。只有当消息的路由键与队列绑定时指定的路由键完全相同时，消息才会被路由到队列。这是一种简单的路由策略，适用于点对点通信。
3. Headers Exchange：
这种交换机根据消息的标头信息（Headers）来决定消息的路由，而不是使用路由键。队列和交换机之间的绑定规则是根据标头键值对来定义的，只有当消息的标头与绑定规则完全匹配时，消息才会被路由到队列。适用于需要复杂消息匹配的场景。
4. Topic Exchange：
   这种交换机根据消息的路由键与队列绑定时指定的路由键模式（通配符）匹配程度，将消息路由到一个或多个队列。路由键可以使用通配符符号 *（匹配一个单词）和 #（匹配零个或多个单词），允许更灵活的消息路由。用于发布/订阅模式和复杂的消息路由需求。
5. Fanout Exchange：
   这种交换机将消息广播到与之绑定的所有队列，无论消息的路由键是什么。用于发布/订阅模式，其中一个消息被广播给所有订阅者。

这些不同类型的交换机允许你在 RabbitMQ 中实现各种不同的消息路由和分发策略，以满足不同的应用需求。选择适当的交换机类型对于有效的消息传递非常重要。

# 消息持久化
1. 消息设置 delivery_mode=2 即可持久化的消息
2. lazy queue 3.6引入 3.12中默认的方式（无法更改）也就是官方推荐
>tip rabbitmq默认消息是持久化的 pagedOut策略：最新的消息优先保留在内存中,较旧的消息会被分页到磁盘上。
> 队列配置持久化 但是消息非持久化 当队列满的时候将消息pageOut（将数据写入磁盘 依然有丢失风险） pageOut时会阻塞消息
> 消息发送时设置delivery_mode=2或在java中setDeliveryModes设置持久（默认） 才会全部直接写入磁盘
> lazy queue 是直接写入磁盘 内存中默认是2024条 消费读取时从磁盘中加载 

![img_11.png](img_11.png)

原文链接：https://blog.csdn.net/weixin_43310500/article/details/135925830

# 持久化
![img_8.png](img_8.png)

# 消息可靠性
##  Confirm 机制
Confirm 机制 是 RabbitMQ 提供的一种可靠性保障机制，用于确认消息是否成功发送到 RabbitMQ。
通过启用 Confirm 模式，生产者可以实时获取消息的确认状态，并根据结果进行重试或其他处理。
在实际应用中，可以根据业务需求权衡 Confirm 机制带来的可靠性和性能开销。
## 发送端
### 重连机制
![img_5.png](img_5.png)
### 确认机制 非常浪费资源 建议不用
![img_6.png](img_6.png)
![img_7.png](img_7.png)
## 消费端
### 重试机制
![img_10.png](img_10.png)

### 确认机制
![img_9.png](img_9.png)
#### 失败消息处理
![img.png](img.png)
## 幂等判断 避免重复执行
1. 唯一id 问题->但消费端代码侵入了
![img_1.png](img_1.png)
2. 业务判断
![img_2.png](img_2.png)

# 延迟消息
## 1. 死信交换机
![img_3.png](img_3.png)
```
缺点
   1. RabbitMQ的消息过期是基于追溯方式来实现的，也就是说当一个消息的TTL到期以后不一定会被移除或投递到死信交换机，而是在消息恰好处于队首时才会被处理。
   2. 当队列中消息堆积很多的时候，过期消息可能不会被按时处理，因此你设置的TTL时间不一定准确。
```
## 2. DelayExchange插件
![img_4.png](img_4.png)

# 集群 都是基于主从复制
1. 单机
2. 普通集群（无高可用性）
![img_14.png](img_14.png)
3. 镜像
![img_15.png](img_15.png)
4. 仲裁队列
![img_16.png](img_16.png)

# 比较好且详细的入门文章
https://www.cnblogs.com/wdadwa/articles/18848073