## 1 语句顺序
form -> join -> where -> group by -> having -> select -> order by -> limit
## 2 mysql的结构
表空间 (Tablespace): 数据库对象的物理存储区域。
段 (Segment): 逻辑存储单元，有数据段、索引段、回滚段，由多个区组成。
区 (Extent): 由连续的数据页组成的空间分配单元，为段提供扩展空间，大小为1m，主要是减少夸多页查询时减少IO次数。
页 (Page): InnoDB最小存储单元（默认16KB），存储行数据、索引等。
行 (Row): 表中的一条记录，存储实际的数据。
## 3 MVCC 概念

MVCC（Multi-Version Concurrency Control，多版本并发控制）是数据库管理系统中用于实现高并发场景下数据一致性和隔离性的关键技术。它通过保存数据的历史版本，允许多个事务同时访问不同的数据版本，从而减少锁的使用，提高并发性能。

以下是 MVCC 的核心概念和特点：

1. **多版本**
    - 每次数据更新时，数据库会生成一个新的数据版本，而不是直接覆盖原有数据。
    - 旧版本的数据会被保留一段时间，直到不再需要为止。

2. **读写不阻塞**
    - 在 MVCC 中，读操作不会阻塞写操作，写操作也不会阻塞读操作。
    - 这是因为读操作可以基于历史版本的数据进行，而写操作则创建新的版本。

3. **隔离级别支持**
    - MVCC 主要用于实现 `READ COMMITTED` 和 `REPEATABLE READ` 隔离级别。
        - **READ COMMITTED**：事务只能读取到已经提交的数据版本。
        - **REPEATABLE READ**：事务在执行期间看到的数据版本是一致的，即使其他事务对数据进行了修改。

4. **Undo Log 和 Version Chain**
    - 数据库通过 Undo Log 来记录数据的历史版本。
    - 每个数据行可能包含一个指向其旧版本的指针，形成版本链（Version Chain）。

5. **Read View**
    - 每个事务在开始时会生成一个 Read View，用于确定可见的数据版本。
    - 根据 Read View，事务可以判断某个版本的数据是否可见。

6. **适用场景**
    - MVCC 常见于支持高并发的数据库系统，如 MySQL 的 InnoDB 存储引擎、PostgreSQL 等。

---

#### MySQL InnoDB 中的 MVCC 实现
在 MySQL 的 InnoDB 存储引擎中，MVCC 是通过以下机制实现的：
- **隐藏字段**：InnoDB 为每行数据添加了两个隐藏字段：
    - `DB_TRX_ID`：<span style="color:red;"> 记录最后一次对该行修改的事务 ID。</span>
    - `DB_ROLL_PTR`：<span style="color:red;"> 指向该行的Undo Log记录。</span>
- **Undo Log**：存储数据的历史版本。
- **Read View**：事务启动时生成，用于判断数据版本的可见性。

---
## 锁
### mysql包含的锁
都包含读锁（S）写锁(X)，意向锁（IS，IX）是行级锁，仅支持表锁和行锁。
<span style="color:red;"> 行级锁都是基于索引实现的 </span>
1. 全局锁 数据备份时触发
2. 表锁 修改表结构时触发，读多写少的数据表也会上读锁
3. 行锁（常用）<span style="color:red;"> 事物中 </span>
    -  select ... for update 加入x锁
    -  update insert delete 加入x锁
    -  select ... LOCK IN SHARE MODE 加入s锁
   
    ### 拓展
    ```
   1.  mvcc 保证 <span style="color:red;"> 数据</span> 的一致性、保证 <span style="color:red;"> 事物 </span> 的隔离性 
   2. 如果锁的行太多 也会升级为表锁
   ```
       
4. 间隙锁（Gap Lock） RR隔离级别才有的，主要解决幻读（但不彻底），不允许区间内insert delete
![img_8.png](img_8.png) 
缺点
    ```
    1. 性能影响，如果范围查询较大，锁的区间太大导致这个区间无法插入，是否会升级为表锁？
    2. 死锁的可能，两个事务同时想在同个区间插入？
   ```
5. 临键锁 也是一致间隙锁 <span style="color:red;">左开右闭</span>
![img_7.png](img_7.png)
6. 意向锁
  ![img_6.png](img_6.png)

    tip： 有行锁时会加入表的意向锁，其他事务如果要锁表就可以直接直接判断表是否有意向锁
    问题？：明明是行级锁为什么通常用来锁表？ 


### 4 MySQL 事务隔离级别及解决的问题

MySQL 提供了四种标准的事务隔离级别，每种隔离级别解决了特定的并发问题，同时也可能引入其他问题。以下是详细的说明：

| 隔离级别         | 脏读 | 不可重复读 | 幻读 | 锁机制          | 适用场景               |
|------------------|------|------------|------|-----------------|-----------------------|
| READ UNCOMMITTED | 是   | 是         | 是   | 无              | 对一致性要求低，高并发 |
| READ COMMITTED   | 否   | 是         | 是   | 行锁            | 大多数 OLTP 系统      |
| REPEATABLE READ  | 否   | 否         | 部分 | 临键锁 + 间隙锁 | 默认隔离级别          |
| SERIALIZABLE     | 否   | 否         | 否   | 表级锁          | 数据一致性要求极高    |

---

## MySQL InnoDB 默认隔离级别
- **默认隔离级别**：`REPEATABLE READ`
- **原因**：在 `REPEATABLE READ` 隔离级别下，InnoDB 使用 **临键锁** 和 **间隙锁** 来部分解决幻读问题，同时保持较高的并发性能。

---

### 设置
1. **隔离级别的设置**：
    - 可以通过以下命令临时或永久修改隔离级别：
      ```sql
      -- 设置当前会话的隔离级别
      SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
 
      -- 设置全局隔离级别
      SET GLOBAL TRANSACTION ISOLATION LEVEL REPEATABLE READ;
      ```
