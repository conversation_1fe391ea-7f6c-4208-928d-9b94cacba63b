## explain
`EXPLAIN` 是 MySQL 中用于分析查询执行计划的命令，通过它可以看到 SQL 查询是如何被执行的，包括表的访问方式、使用的索引、连接顺序等信息。以下是 `EXPLAIN` 的各种含义和关键字段解释：


### (1) **id**
- 表示查询中每个 SELECT 子句的序列号。
- 数值越小，优先级越高，先执行。
- 如果有子查询，外层查询的 id 通常比内层查询大。

### (2) **select_type**  重点
- 描述查询的类型：
   - `SIMPLE`：简单查询，不包含子查询或 UNION。
   - `PRIMARY`：最外层的查询。
   - `SUBQUERY`：子查询中的第一个 SELECT。
   - `DERIVED`：派生表（子查询在 FROM 子句中）。
   - `UNION`：UNION 中的第二个或后续查询。
   - `UNION RESULT`：UNION 的结果。

### (3) **table**
- 显示查询涉及的表名。

### (4) **partitions**
- 显示匹配的分区（如果表是分区表）。

### (5) **possible_keys**
- 列出可能使用的索引。

### (6) **key** 重点
- 实际使用的索引。

### (7) **key_len**
- 使用的索引长度（字节数）。

### (8) **ref**
- 显示索引的比较项，可能是常量、字段或其他表达式。

### (9) **rows**
- 估算查询返回的行数。

### (10) **filtered** 
- 按照条件过滤后剩余的百分比。


### （11） explain type 重点
type列：system>const>eq_ref>ref>fulltext>ref_or_null>index_merge_unique_subquery>index_subquery>range>index>all(性能由高到低排列)

null：聚合函数查询，直接索引树上拿数据；

system：很少见，比如用MyIsam做存储引擎时，查询表中的一条记录，或者count(*)时，因为MyIsam存储引擎会将表中的数据用一个常量统计，用时直接取；

const：使用主键索引或唯一索引和常量进行比较；

eq_ref：多表连接查询，如果被驱动表是通过主键或者唯一二级索引列等值匹配的方式进行访问的
，（如果该主键或者唯一二级索引是联合索引的话，所有的索引列都必须进行等值比较），则
，对该被驱动表的访问方法就是`eq_ref`。
ref：查询条件是普通的索引列和常量进行比较；

ref_or_null：当对普通二级索引进行等值匹配查询，该索引列的值也可以是`NULL`值时，那么对该表的访问方法；就可能是`ref_or_null`。

range：主键索引做范围查找；索引优化，最少也要达到range级别

index：查询没有进行条件判断，所有数据可以从索引树上拿；

all：全表扫描。
### （12）extra性能排名 重点 
1. Using index 使用索引
这是最理想的情况,表示直接通过索引就可以获取数据,无需访问表。
2. Using index condition 索引下推
MySQL 5.6引入的新特性,可以先条件过滤索引,过滤完索引后找到所需数据行,再去读取表中的数据。这种方式可以大大减少因为读取表数据而带来的性能消耗。
3. Select tables optimized away / No tables used
查询语句中不需要访问表或者通过某些优化直接返回结果,性能非常好。
4. Using where
在存储引擎检索行后再进行过滤,需要读取表数据,性能较Using index差一些。
5. Using join buffer (Block Nested Loop)
MySQL需要创建一个临时缓冲区来处理连接查询。如果查询中有大量数据需要连接,而且没有好的连接键,性能会受到影响。
6. Distinct
找到第一个匹配的行后就停止搜索,性能一般。
7. Using temporary
创建临时表来处理查询,通常发生在对没有索引的列进行GROUP BY时,性能较差。
8. Using filesort
对结果使用外部排序而不是索引排序,性能较差。
9. Range checked for each record(index map: N)
没有好用的索引,MySQL需要扫描每一行,性能最差。


###  **优化建议**
- **选择合适的索引**：确保 `key` 字段使用了有效的索引。
- **避免全表扫描**：尽量减少 `type=all` 的情况。
- **减少临时表和排序**：避免 `Using temporary` 和 `Using filesort`。
- **覆盖索引**：尽量让查询只使用索引，避免回表。

## 怎么确定是否发生回表
extra中 using index 表示使用了覆盖索引,不需要回表
## 索引下推
select * from user where name like '张%' and age=10;

MySQL 5.6 以后， 存储引擎根据（name，age）联合索引，先找到 ‘张%’的，
由于联合索引中包含列age，所以存储引擎直接再联合索引里按照age=10过滤。
按照过滤后的数据再一一进行回表扫描。

## InnoDB中的锁
共享锁 (S Lock): 允许事务读取一行数据,阻止其他事务获得相同数据集的排他锁。
排他锁 (X Lock): 允许事务删除或更新一行数据,阻止其他事务获得相同数据集的共享锁或排他锁。
意向共享锁 (IS Lock): 事务在获得共享锁之前必须先获得相同数据集的IS锁。
意向排他锁 (IX Lock): 事务在获得排他锁之前必须先获得相同数据集的IX锁。
间隙锁 (Gap Lock): 锁定索引记录之间的“间隙”,确保索引记录之间的数据不变。
临键锁 (Next-Key Lock): 锁定索引记录之前的间隙和索引记录本身。
行锁 (Record Lock): 锁定索引记录本身。
![img_5.png](img_5.png)
### tip
>mysql 间隙锁+临键锁解决幻读（部分）
## 语句执行顺序
- FROM：从数据表中选择数据。这个步骤确定了查询的基本数据来源。如果有多个表，它们将被引入查询。
- JOIN：如果查询中包含多个表，它们会在这里进行连接操作。MySQL根据连接条件（如INNER JOIN, LEFT JOIN, RIGHT JOIN）将表中的行组合在一起。
- WHERE：过滤数据表中的行。MySQL在这一阶段应用WHERE条件，以排除不符合条件的行。此步骤减少了需要处理的数据量。
- GROUP BY：对结果集进行分组。根据一个或多个列的值，将结果集中的行分组。在这个阶段，MySQL创建了分组后的中间结果集。
- HAVING：对分组后的结果进行过滤。与WHERE子句类似，HAVING子句用于过滤满足GROUP BY条件后的分组数据。HAVING通常用于过滤聚合函数的结果。
- SELECT：选择需要的列。MySQL在这一阶段选择特定的列或表达式，生成最终的结果集。此步骤中，还可能计算派生列或应用聚合函数（如SUM, AVG）。
- DISTINCT：去除重复的记录。如果查询指定了DISTINCT关键字，MySQL在这里会删除结果集中的重复行，确保每一行都是唯一的。
- ORDER BY：对结果集进行排序。MySQL根据指定的列对结果集进行排序。默认是升序排列，可以通过ASC或DESC关键字指定升序或降序。
- LIMIT：限制返回的行数。LIMIT子句用于限制返回的行数，使查询只返回指定数量的行。这在分页或控制返回结果集大小时非常有用。

## 事务的保证、日志 
### 基本概念
- redo Log是数据库引擎的一种日志，主要用于崩溃恢复以及用于记录数据库的物理变更操作，例如数据页的修改。它以顺序方式记录，通常是追加写入磁盘上的日志文件。
- undo Log是数据库引擎的一种日志，用于记录事务的回滚信息，即撤销已提交事务所做的修改操作。它记录了事务执行过程中旧值的备份，以支持事务的回滚操作。
#### 回滚
> 未提交的事务，即事务未执行 commit。但事务内修改的脏页中，有一部分已刷盘。此时数据库宕机重启，需要回滚来将先前那部分已经刷盘的脏块从磁盘上撤销。
#### 前滚
>未完全提交的事务，即事务已经执行 commit，但该事务内修改的脏页中只有一部分数据被刷盘，另一部分还在 buffer pool，此时数据库宕机重启，就要用前滚来将未来得及刷盘的数据从 redo log 中恢复出来并刷盘。
#### tip
只有保证了事务的 原子性(A)、隔离性(I)之后，持久性（D），一致性(C)才能得到保障。也就是说 A、I、D 是手段，C 是目的！
## 缓存
MySQL 数据存在磁盘中，每次读写数据需做磁盘随机IO，并发场景下性能差。对此 MySQL 引入缓存 Buffer Pool 做优化。其包含磁盘中部分数据页（page）的映射，来缓解数据库的磁盘压力。

- 缓冲池（buffer pool）:主内存中的一个区域，里面可以缓存磁盘上经常操作的真实数据，在执行增删改查操作时，先操作缓冲池中的数据（若缓冲池没有数据，则从磁盘加载并缓存），以一定频率刷新到磁盘，从而减少磁盘IO，加快处理速度

- 缓存池中包含了磁盘中部分数据页（page）的映射，以此来缓解数据库的磁盘压力。当从数据库读数据时，首先从缓存中读取，如果缓存中没有，则从磁盘读取后放入缓存；当向数据库写入数据时，先向缓存写入，此时缓存中的数据页数据变更，这个数据页称为脏页，Buffer Pool中修改完数据后会按照设定的更新策略，定期刷到磁盘中，或是脏页达到75%（默认），这个过程称为刷脏页。
![img_4.png](img_4.png)
### 重做日志（redo log） 主要用于崩溃恢复 保证数据持久化
#### 作用 
MySQL由于某些原因宕机重启，此时Buffer Pool中修改的数据还没有及时的刷到磁盘中，就会导致数据丢失，无法保证事务的持久性。为了解决这个问题引入了redo log。
>日志文件由两部分组成：重做日志缓冲（redo log buffer）以及重做日志文件（redo log file）,前者是在内存中，后者在磁盘中。当事务提交之后会把所有修改信息都存到该日志文件中,用于在刷新脏页到磁盘,发生错误时,进行数据恢复使用。

redo log用到了WAL（Write-Ahead Logging）技术，这个技术的核心就在于修改记录前，一定要先写日志，并保证日志先落盘，
才能算事务提交完成。有了redo log再修改数据时，InnoDB引擎会把更新记录先写在redo log（先写入redoLog的缓存 才写盘）中，再修改Buffer Pool（）中的数据，
当提交事务时，调用fsync把redo log刷入磁盘。至于缓存中更新的数据文件何时刷入磁盘，则由后台线程异步处理。

>redo log 采用大小固定，循环写入的方式，当写满后，会重新从头开始循环写，类似一个环状。这样设计原因是 redo log 记录的是数据页上的修改，如果 Buffer Pool 中数据页已经刷到磁盘，这些记录就失效了，新日志会将这些失效的记录覆盖擦除。
> 
>注意：redo log 满了，在擦除之前，要确保这些要被擦除记录都已经刷到磁盘中了。在擦除旧记录释放新空间期间，不能再接收新的更新请求，此时 MySQL 性能会下降。因此高并发情况下，合理调整 redo log 大小很重要。

### 回滚日志（undo log） 用于回滚原子性 隔离性
undo Log（回滚日志）是MySQL中的一种重要数据结构，用于实现事务的ACID特性中的"Atomicity"（原子性）和"Isolation"（隔离性）。
> 事务提交成功由redo log保证数据持久性，而事务可以进行回滚从而保证事务操作原子性则是通过undo log 来保证的。

#### undo log产生和销毁：
Undo Log在事务开始前产生；事务在提交时，并不会立刻删除undo log，innodb会将该事务对应的undo log放入到删除列表中，后面会通过后台线程purge thread进行回收处理
![img.png](img.png)
- 事务A执行update更新操作，在事务没有提交之前，会将旧版本数据备份到对应的undo buffer中，然后再由undo buffer持久化到磁盘中的undo log文件中, 之后才会对user进行更新操作,然后持久化到磁盘
- 在事务A执行的过程中,事务B对User进行了查询

> 在Mysql里数据每次修改前，都首先会把修改之前的数据作为历史保存一份到undo log里面的，数据里面会记录操作该数据的事务ID，然后我们可以通过事务ID来对数据进行回滚。

可以认为当delete一条记录时，undo log中会记录一条对应的insert记录，反之亦然，当update一条记录时，它记录一条对应相反的update记录。当执行rollback时，就可以从undo log中的逻辑记录读取到相应的内容并进行回滚。
#### 拓展 MVCC
![img_1.png](img_1.png)
### 二进制日志（binlog）
#### 作用
用于复制，在主从复制中，从库利用主库上的binlog进行重播，实现主从同步。
用于数据库的基于时间点的还原。数据备份 数据恢复 数据同步。
### 总结
redo log日志记录的是数据页的物理变化，服务宕机可用来同步数据，而undo log 不同，它主要记录的是逻辑日志，当事务回滚时，通过逆操作恢复原来的数据，比如我们删除一条数据的时候，就会在undo log日志文件中新增一条delete语句，如果发生回滚就执行逆操作。

redo log保证了事务的持久性，undo log保证了事务的原子性和一致性

原文链接：https://blog.csdn.net/m0_62436868/article/details/134869326
还有一文章：https://www.cnblogs.com/xuwc/p/13873611.html
# 主从复制
![img_2.png](img_2.png)

###  tip 并行复制

![img_3.png](img_3.png)

### MRR
MRR（Multi-Range Read）是MySQL的一种优化技术，主要用于优化范围扫描和排序操作。以下是关于MRR优化的解释：

1. **基本概念**：
    - MRR通过将随机I/O转换为顺序I/O来提高查询性能。
    - 它适用于需要对非主键索引进行范围扫描并回表获取数据的情况。

2. **工作原理**：
    - 首先根据非主键索引获取符合条件的记录的主键值。
    - 然后将这些主键值排序，使得后续从主键索引中读取数据时可以按顺序读取，减少磁盘I/O。
    - 最后根据排序后的主键值从主键索引中读取完整的记录。

3. **启用MRR**：
    - 可以通过设置系统变量`optimizer_switch`中的`mrr`选项来启用或禁用MRR。
    - 例如：`SET optimizer_switch = 'mrr=on';`

4. **适用场景**：
    - 当查询涉及非主键索引的范围扫描，并且需要回表获取更多列的数据时，MRR可以显著提升性能。
    - 特别是在数据量较大、磁盘I/O成为瓶颈的情况下。

5. **示例代码**：
   ```sql
   -- 假设有一个表t1，包含一个非主键索引idx_a和主键id
   SELECT * FROM t1 WHERE a BETWEEN 10 AND 20;
   ```

   在上述查询中，如果启用了MRR，MySQL会首先根据`idx_a`获取符合条件的记录的`id`，然后按`id`排序后再从主键索引中读取完整记录。

6. **注意事项**：
    - MRR的性能提升依赖于具体的查询和数据分布情况。
    - 在某些情况下，MRR可能会引入额外的开销，因此需要根据实际情况进行测试和调优。

通过理解MRR的工作原理和适用场景，可以在适当的查询中应用该优化技术，从而提高MySQL查询的性能。