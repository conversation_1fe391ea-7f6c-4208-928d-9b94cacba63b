# TC->TM-RM
## 关系
![img.png](img.png)

## 工作流程
![img_1.png](img_1.png)

# 原理
## 第一阶段
![img_2.png](img_2.png)
## 第二阶段
![img_3.png](img_3.png)
## 回滚
![img_4.png](img_4.png)
## 视频地址
https://www.bilibili.com/video/BV1br4y187QH/?p=76&vd_source=8dd9b380966cca1b92a3c39f2c9b2027
## tip 锁原理
在数据库本地事务隔离级别 读已提交（Read Committed） 或以上的基础上，Seata（AT 模式）的默认全局隔离级别是 读未提交（Read Uncommitted） 。
在 Seata AT 模式中，确保全局事务的一致性和隔离性，是通过全局锁+本地锁实现，这两种锁都是通过SELECT FOR UPDATE 语句的代理。

只有加入@GlobalTransactional才参与seata事物，

锁获取顺序 本地锁->全局锁。 这样可以保证在全局锁未释放前，本地事务可以获取锁执行但是还无法提交，有利于并发。
- Seata的本地锁是通过业务表的实现的，使用的是行锁，主要作用是防止并发修改相同的业务数据。

- Seata 的全局锁是通过数据库表实现的。具体来说，Seata 使用一个叫做 global_table 的数据库表来实现全局锁定机制，这张表用于记录全局事务的状态和锁定信息，以确保分布式事务的一致性和隔离性。
通过使用数据库表 global_table 和 branch_table，Seata 实现了全局事务的锁定和管理机制。这种机制确保了分布式事务中的数据一致性，避免了并发修改引起的数据冲突。

