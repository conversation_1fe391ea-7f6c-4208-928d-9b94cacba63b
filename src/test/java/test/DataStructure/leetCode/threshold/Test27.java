package test.DataStructure.leetCode.threshold;

import org.junit.Test;

/**
 * @Description：给你一个数组 nums 和一个值 val，你需要 原地 移除所有数值等于 val 的元素，并返回移除后数组的新长度。
 *
 * 不要使用额外的数组空间，你必须仅使用 O(1) 额外空间并 原地 修改输入数组。
 *
 * 元素的顺序可以改变。你不需要考虑数组中超出新长度后面的元素。
 * 来源：力扣（LeetCode）
 * 链接：https://leetcode.cn/problems/remove-element
 * 著作权归领扣网络所有。商业转载请联系官方授权，非商业转载请注明出处。
 * @Author：Administrator
 * @Date：2023/5/30 19:00
 **/
public class Test27 {
    @Test
    public void start(){
//        [0,1,2,2,3,0,4,2]  [3,2,2,3]
//        2   3
        int[] nums = {0,1,2,2,3,4,8};
        System.out.println(searchInsert(nums, 5));
    }
    public int removeElement(int[] nums, int val) {
        // 声明一个整型变量 length，用于存储数组的长度
        int length;
        if (nums==null || (length = nums.length)==0)
            return 0;

        int prev = 0;
        for (int i = 0; i < length; i++) {
            int cur = nums[i];
            if (cur != val) {
                nums[prev] = cur;
                prev++;
            }
        }
        return prev;
    }
    public int searchInsert(int[] arr,int target){
        for (int i = 0; i < arr.length; i++) {
            if (arr[i]==target ||arr[i]>target){
                return i;
            }
        }
        return arr.length;
    }
}

