package test.DataStructure.leetCode.threshold;

import org.junit.Test;

import java.util.*;

/**
 * @Description：给你一个整数数组 arr 和一个整数 k 。现需要从数组中恰好移除 k 个元素，请找出移除后数组中不同整数的最少数目。
 * @Author：Administrator
 * @Date：2023/3/29 15:57
 **/
public class Test2 {
    public int findLeastNumOfUniqueInts(int[] arr, int k) {
        int length = arr.length;
        Map<Integer,Integer> map = new HashMap();
        for (int i = 0; i < length; i++) {
            int num = arr[i];
            map.put(num, map.getOrDefault(num,0)+1);
        }
        ArrayList<Integer> list = new ArrayList<>(map.values());
        Collections.sort(list);
        int size = list.size();
        for (Integer integer : list) {
            if ((k -=integer)<0){
                return size;
            }
            size--;
        }
        return size;
    }

    public static void main(String[] args) {
        System.out.println(new Test2().findLeastNumOfUniqueInts(new int[]{1, 1, 4, 4, 4, 5, 2}, 2));
    }
    //两数之和
    public int[] twoSum(int[] nums, int target) {
        int length = nums.length;
        for (int i = 0; i < length; i++) {
            for (int j = 0; j < length; j++) {
                if (i!=j&&(nums[i]+nums[j])==target){
                    return new int[]{i,j};
                }
            }
        }
        return null;
    }
    @Test
    public void test1(){
        int[] ints = new Test2().twoSum(new int[]{3,2,4}, 6);
        for (int anInt : ints) {
            System.out.println(anInt);
        }
    }
    public boolean isValid(String s) {
        int length = s.length();



        return false;
    }


}
