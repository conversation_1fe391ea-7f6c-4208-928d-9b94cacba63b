package test.DataStructure.leetCode.threshold;

import org.junit.Test;

import javax.servlet.Servlet;
import javax.servlet.http.HttpServlet;

/**
 * @Description：最长公共前缀
 * @Author：Administrator
 * @Date：2023/5/30 0:57
 **/
public class Test14  {

    @Test
    public void test(){
      /*  String[] arr = {"abcdefg","abcde","abcdee","abcdd","abcdddd","abcccccccccc","ab"};*/
        String[] arr ={"AB","A"};
        String pub = arr[0];
        String next;
        for (int i = 1; i < arr.length; i++) {
            next = arr[i];
            pub = getPublic(pub,next);
        }
        System.out.println(pub);
    }

    private String getPublic(String min, String max) {
        if (min==null || max==null){
            return "";
        }

        int minLength = min.length();
        int maxLength = max.length();
        if (minLength>maxLength){
            minLength = minLength ^ maxLength;
            maxLength = minLength ^ maxLength;
            minLength = minLength ^ maxLength;
            String temp = min;
            min = max;
            max = temp;
        }
        if (min.equals(max.substring(0,minLength))){
            return min;
        }else {
            for (int i = 0; i < minLength; i++) {
               if (min.charAt(i)!=max.charAt(i)){
                   return min.substring(0,i);
               }
            }
        }
        return min;
    }


}
