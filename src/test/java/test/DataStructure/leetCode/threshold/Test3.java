package test.DataStructure.leetCode.threshold;

/**
 * @Description：要制作 3 束花m，每束需要 2 朵花k，也就是一共需要 6 朵花。而花园中只有 5 朵花 bloomDay.length，无法满足制作要求，返回 -1 。
 * @Author：Administrator
 * @Date：2023/3/29 16:52
 **/
public class Test3 {
    public int minDays(int[] bloomDay, int m, int k) {
        if (m * (long)k > (long) bloomDay.length) return -1; // 当花的数量不够制作花束时，直接返回-1
        int n = bloomDay.length;
        int[] queue = new int[n]; // 存储滑动窗口的队列
        int[] max = new int[n]; // 存储每个滑动窗口中最大的花期
        int l = 0;
        int r = 0;
        for (int i = 0; i < n; i++) {
            if (l < r && queue[l] <= i - k) ++l; // 如果队列头已经超出窗口范围，将队列头出队
            while (l < r && bloomDay[queue[r-1]] <= bloomDay[i]) --r; // 如果当前花期小于等于队列尾的花期，将队列尾出队
            queue[r++] = i; // 将当前位置入队
            max[i] = bloomDay[queue[l]]; // 记录当前窗口中最大的花期
        }

        int lo = 0;
        int hi = 1000000001; // 最大花期是10^9，将hi设为这个值
        while (lo < hi) {
            int mi = (lo + hi) >> 1; // 取中间值
            int cnt = 0; // 记录能够制作的花束数量
            for (int i = k - 1; i < max.length && cnt < m; ) {
                if (max[i] <= mi) { // 如果当前滑动窗口中最大的花期小于等于mi，则这个窗口中的花可以制作成花束
                    i += k; // 将i加上k，表示i之前的花都已经被用于制作花束了
                    ++cnt; // 将制作的花束数量加1
                } else {
                    ++i; // 否则，将i向后移动一位
                }
            }
            if (cnt < m) lo = mi + 1; // 如果能够制作的花束数量不够m，将lo设置为mi+1
            else hi = mi; // 否则，将hi设置为mi
        }

        return lo; // 返回最小花期
    }

    public static void main(String[] args) {
        int[] arr = {1,10,2,9,3,8,4,7,5,6};
        System.out.println(new Test3().minDays(arr, 4,  2));
    }
}