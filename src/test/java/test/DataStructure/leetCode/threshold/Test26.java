package test.DataStructure.leetCode.threshold;

import org.junit.Test;

/**
 * @Description：
【双指针】删除重复
 * @Author：Administrator
 * @Date：2023/5/30 2:31
 **/
public class Test26 {

    @Test
    public void start(){
        int[] nums = {1,2};
        removeDuplicates(nums);
    }
    public int removeDuplicates(int[] nums) {
        int length = nums.length;
        if (length == 0){
            return 0;
        }
        int prev = 0;
        for (int i = 1; i < length; i++) {
            if (nums[prev]!=nums[i]){
                if(i - prev > 1){
                    nums[prev + 1] = nums[i];
                }
            }
            nums[i] = 0;
        }
        return prev+1;
    }
}
