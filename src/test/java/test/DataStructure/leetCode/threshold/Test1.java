package test.DataStructure.leetCode.threshold;

import org.junit.Test;

/**
 * @Description：给你一个数组 nums 。数组「动态和」的计算公式为：runningSum[i] = sum(nums[0]…nums[i]) 。
 * 请返回 nums 的动态和。
 * @Author：Administrator
 * @Date：2023/3/29 15:03
 **/
public class Test1 {
    public int[] runningSum(int[] nums) {
        int numLength = nums.length;
        for (int i=1 ;i < numLength; i++) {
           nums[i] +=nums[i-1];
        }
        return nums;
    }


    @Test
    public void leetCodeTest() {

    }


    public int maxSubArray(int[] nums) {
        int len = nums.length;
        // dp[i] 表示：以 nums[i] 结尾的连续子数组的最大和
        int[] dp = new int[len];
        dp[0] = nums[0];

        for (int i = 1; i < len; i++) {
            if (dp[i - 1] > 0) {
                dp[i] = dp[i - 1] + nums[i];
            } else {
                dp[i] = nums[i];
            }
        }
        // 也可以在上面遍历的同时求出 res 的最大值，这里我们为了语义清晰分开写，大家可以自行选择
        int res = dp[0];
        for (int i = 1; i < len; i++) {
            res = Math.max(res, dp[i]);
        }
        return res;
    }

}
