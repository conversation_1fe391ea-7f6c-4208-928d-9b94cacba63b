package test.DataStructure.leetCode.test;

import org.junit.Test;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2023/4/11 17:39
 **/
public class MyPwdTest {
    private static String fileAddress1 = "E:\\study\\code\\javaBase\\JavaDemo\\" +
            "src\\test\\java\\test\\leetCode\\test\\img\\1.png";
    private static String fileAddress2 = "E:\\study\\code\\javaBase\\JavaDemo\\" +
            "src\\test\\java\\test\\leetCode\\test\\img\\2.png";

    private static File file1 = new File(fileAddress1);
    private static File file2 = new File(fileAddress2);
    public static void main(String[] args) {
        identical();
    }


    /**
     * start
     */
    @Test
    public void start(){
        String password = "123456"; // 待破解的密码
        boolean found = false; // 标识是否找到密码
        long startTime = System.currentTimeMillis(); // 记录开始时间
        for (int i = 0; i < 1000000; i++) { // 枚举所有 6 位数字密码
            String guess = String.format("%06d", i); // 生成 6 位密码
            if (numberKeyEvent(guess)){
                System.out.println("正确密码："+guess);
                found = true;
                break;
            }

        }
        long endTime = System.currentTimeMillis(); // 记录结束时间
        if (!found) { // 如果没有找到密码
            System.out.println("Password not found"); // 输出提示信息
        }
        System.out.println("Time: " + (endTime - startTime) + "ms"); // 输出破解时间
    }

    private static boolean numberKeyEvent(String guess) {
        try {
            Thread.sleep(1000);
            for (int i = 0; i < guess.length(); i++) {
                Thread.sleep(500);
                int value = Integer.parseInt(String.valueOf(guess.charAt(i)));
                int numberDown = 48+value;
                Robot robot = new Robot();
                robot.keyPress(numberDown);
                Thread.sleep(100);
                robot.keyRelease(numberDown);
            }
            System.out.println("输入完毕:"+guess);
            return !identical();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private static boolean  identical(){
        // 获取屏幕尺寸
    /*    Rectangle screenRect = new Rectangle(Toolkit.getDefaultToolkit().getScreenSize());*/
        // 创建 Robot 对象
        Robot robot = null;
        try {
            robot = new Robot();
            // 截取屏幕矩形区域
            BufferedImage image = robot.createScreenCapture(
                    new Rectangle(10, 500, 400, 100));
            // 保存屏幕截图到文件
            File file;
            if (!file1.exists()) {
                file = file1;
                ImageIO.write(image, "png", file);
                file1 = new File(fileAddress1);
            }else {
                file = new File(fileAddress2);
                ImageIO.write(image, "png", file);
                file2 = new File(fileAddress2);
                return imgIdentical(file1,file2);
            }


        } catch (Exception e) {
            e.printStackTrace();
            return true;
        }
        return true;
    }
    /*
    判断两张图片内容是否一致
     */
    public static boolean imgIdentical(File file1,File file2){
        try {

            // 读取两张图片
            BufferedImage image1 = ImageIO.read(file1);
            BufferedImage image2 = ImageIO.read(file2);

            // 获取两张图片的宽度和高度
            int width1 = image1.getWidth();
            int height1 = image1.getHeight();
            int width2 = image2.getWidth();
            int height2 = image2.getHeight();

            // 如果两张图片的宽度和高度不相等，则它们内容不一致
            if (width1 != width2 || height1 != height2) {
                System.out.println("Content is not identical.");
                return false;
            }

            // 比较两张图片每个像素的RGB值是否相等
            for (int x = 0; x < width1; x++) {
                for (int y = 0; y < height1; y++) {
                    int rgb1 = image1.getRGB(x, y);
                    int rgb2 = image2.getRGB(x, y);
                    if (rgb1 != rgb2) {
                        System.out.println("Content is not identical.");
                        return false;
                    }
                }
            }

            System.out.println("Content is identical.");
            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }


    public static void moveByNumber(String pwd){
        for (int i = 0; i < pwd.length(); i++) {
            char charAt = pwd.charAt(i);
            String parseStr = String.valueOf(charAt);
            int parseInt = Integer.parseInt(parseStr);
            // index = 1
            int x = 1920/2 ,y = 1080/2;
            int cross = 50;//横
            int vertical = 30;//竖
            if (parseInt==0){
        /*     1 2 3
               4 5 6
               7 8 9*/
                x+=cross;
                y+=cross*3;
            }else if (parseInt>0 && parseInt <=3){
                x +=cross*(parseInt-1);
            } if (parseInt>3&& parseInt <=6){
                x +=cross*(parseInt-3-1);
                y +=vertical;
            }else if (parseInt>6){
                x +=cross*(parseInt-6-1);
                y +=vertical*2;
            }

            System.out.println("x="+x+"  y="+y);
            moveMouse(x,y);
        }
    }

    /*
    移动鼠标并点击
     */
    public static void moveMouse(int x,int y){
        // 创建一个Robot对象
        Robot robot = null;
        try {
            robot = new Robot();
            // 移动鼠标到指定的位置（x，y）
            robot.mouseMove(x, y);
            // 等待1秒钟，以便你能够将鼠标移动到指定的位置
    /*        Thread.sleep(300);
            // 模拟鼠标左键点击
            robot.mousePress(InputEvent.BUTTON1_DOWN_MASK);
            robot.mouseRelease(InputEvent.BUTTON1_DOWN_MASK);*/

        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
