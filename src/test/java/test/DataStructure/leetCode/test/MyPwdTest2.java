package test.DataStructure.leetCode.test;

import org.junit.Test;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2023/4/11 17:39
 **/
public class MyPwdTest2 {
    private static String fileAddress1 = "E:\\study\\code\\javaBase\\JavaDemo\\" +
            "src\\test\\java\\test\\leetCode\\test\\img\\1.png";
    private static String fileAddress2 = "E:\\study\\code\\javaBase\\JavaDemo\\" +
            "src\\test\\java\\test\\leetCode\\test\\img\\2.png";
    private static String fileAddress3 = "E:\\study\\code\\javaBase\\JavaDemo\\" +
            "src\\test\\java\\test\\leetCode\\test\\img\\pwd.txt";
    private static File file1 = new File(fileAddress1);
    private static File file2 = new File(fileAddress2);
    private static File filePwd = new File(fileAddress3);
    public static void main(String[] args) {
        identical();
    }

    private static int indexPwd = 500000;
    @Test
    public void tt(){
        int indexX = 1;
        int indexY = 1;
        int single = 7;

        int x = ((int)(single%3.5)+1)*indexX;
        int y = ((int)(single/3.5)+1)*indexY;
        System.out.println("x="+x+" y="+y);
    }
    @Test
    public  void start(){
        boolean found = false; // 标识是否找到密码
        String backPwd = null;
        String line;
        try {
            BufferedReader bufferedReader = new BufferedReader(new FileReader(filePwd));
            while ((line = bufferedReader.readLine()) != null) {
                if (line!=null){
                    backPwd = line;
                    System.out.println("读取上次保存密码："+backPwd);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        long startTime = System.currentTimeMillis(); // 记录开始时间
        int top;//556309  497872
        int bottom;//554801
        boolean isTop;
        if (backPwd!=null){
            int parseInt = Integer.parseInt(backPwd);
            if (parseInt>indexPwd){
                isTop =true;
                top = parseInt;
                bottom = 2*indexPwd-top;
            }else {
                isTop = false;
                bottom = parseInt;
                top = 2*indexPwd-bottom;
            }
        }else {
            top = indexPwd;
            bottom = indexPwd;
            isTop = true;
        }

        out:for (int i = 0; i < 1000000; i++) {
            int index;
            if (isTop){
                ++top;
                index =top;
                isTop = false;
            }else {
                --bottom;
                index = bottom;
                isTop = true;
            }
            //判断下一位数字是否相同 相同跳过
            String pwd = String.valueOf(index);
            String item="";
            for (int j = 0; j < pwd.length(); j++) {
                String str = String.valueOf(pwd.charAt(j));
                if (item.equals(str)){
                    continue out;
                }else {
                    item = str;
                }
            }
           /* try {
                Thread.sleep(100);
                System.out.println(isTop?"top"+index:"bottom"+index);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }*/
            if (numberKeyEvent(index)){
                System.out.println("正确密码："+index);
                break;
            }
        }
        long endTime = System.currentTimeMillis(); // 记录结束时间
        System.out.println("Time: " + (endTime - startTime) + "ms"); // 输出破解时间
    }

    private static boolean numberKeyEvent(int pwd) {
        String guess = String.valueOf(pwd);
        try {
            Thread.sleep(800);
            for (int i = 0; i < 6; i++) {
                Thread.sleep(100);
                int value = Integer.parseInt(String.valueOf(guess.charAt(i)));
                int numberDown = 48+value;
                Robot robot = new Robot();
                robot.keyPress(numberDown);
                Thread.sleep(100);
                robot.keyRelease(numberDown);
            }
            System.out.println(pwd>indexPwd?"top输入完毕:"+guess:"bottom输入完毕:"+guess);
            FileWriter fileWriter = new FileWriter(filePwd);
            fileWriter.write(guess);
            fileWriter.close();
            return !identical();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private static boolean  identical(){
        // 获取屏幕尺寸
    /*    Rectangle screenRect = new Rectangle(Toolkit.getDefaultToolkit().getScreenSize());*/
        // 创建 Robot 对
        Robot robot = null;
        try {
            robot = new Robot();
            // 截取屏幕矩形区域
            BufferedImage image = robot.createScreenCapture(
                    new Rectangle(10, 500, 400, 100));
            // 保存屏幕截图到文件
            File file;
            if (!file1.exists()) {
                file = file1;
                ImageIO.write(image, "png", file);
                file1 = new File(fileAddress1);
            }else {
                file = new File(fileAddress2);
                ImageIO.write(image, "png", file);
                file2 = new File(fileAddress2);
                return imgIdentical(file1,file2);
            }


        } catch (Exception e) {
            e.printStackTrace();
            return true;
        }
        return true;
    }
    /*
    判断两张图片内容是否一致
     */
    public static boolean imgIdentical(File file1,File file2){
        try {

            // 读取两张图片
            BufferedImage image1 = ImageIO.read(file1);
            BufferedImage image2 = ImageIO.read(file2);

            // 获取两张图片的宽度和高度
            int width1 = image1.getWidth();
            int height1 = image1.getHeight();
            int width2 = image2.getWidth();
            int height2 = image2.getHeight();

            // 如果两张图片的宽度和高度不相等，则它们内容不一致
            if (width1 != width2 || height1 != height2) {
                System.out.println("Content is not identical.");
                return false;
            }

            // 比较两张图片每个像素的RGB值是否相等
            for (int x = 0; x < width1; x++) {
                for (int y = 0; y < height1; y++) {
                    int rgb1 = image1.getRGB(x, y);
                    int rgb2 = image2.getRGB(x, y);
                    if (rgb1 != rgb2) {
                        System.out.println("Content is not identical.");
                        return false;
                    }
                }
            }

            return true;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }
}
