package test.DataStructure.Three;

import cn.hutool.core.lang.tree.Tree;
import org.junit.Test;

/**
 * @Description：平衡树
 * @Author：Administrator
 * @Date：2023/5/24 15:06
 **/
public class isTree {

    static TreeNode root;
    static {
        // 构建平衡二叉树
        root = new TreeNode(4);
        root.left = new TreeNode(2);
        root.right = new TreeNode(6);
        root.left.left = new TreeNode(1);
        root.left.right = new TreeNode(3);
        root.right.left = new TreeNode(5);
        root.right.right = new TreeNode(7);
        /*  4
          2   6
         1 3 5 7
         */

    }
    @Test
    public void isBalancedThree(){
        // 打印树的结构
        printTree(root);
        System.out.println(isBalanced(root));
    }
    public boolean isBalanced(TreeNode root) {
        if(root == null){
            return true;
        }
        int left = depth(root.left);
        int right = depth(root.right);
        boolean isLeft = isBalanced(root.left);
        boolean isRight = isBalanced(root.right);
        return Math.abs(left-right) <= 1 &&  isLeft && isRight ;
    }
    public int depth(TreeNode root){
        if(root == null){
            return 0;
        }
        int left = depth(root.left);
        int right = depth(root.right);

        int max = Math.max(left, right);
        return  max+ 1;
    }
    public static void printTree(TreeNode root) {
        if (root == null)
            return;
        System.out.println(root.val);
        printTree(root.left);
        printTree(root.right);
    }



    @Test
    public void test(){
        int[][] arr = {{1,2,3,4,1,2,3,4},{2,3,4,5,1,2,3,4},{3,4,5,6,1,2,3,4},{4,5,6,7,1,2,3,4},
                {4,5,6,7,1,2,3,4},{12,8,9,4,1,2,3,4},{12,8,9,4,1,2,3,4},{12,8,9,4,1,2,3,4}
                ,{12,8,9,4,1,2,3,4},{12,8,9,4,1,2,3,4},{12,8,9,4,1,2,3,4}};
        int sum = 0;
        for (int i = 0; i < arr.length; i++) {
            int[] ints = arr[i];
            for (int j = 0; j < ints.length; j++) {
                System.out.print("-"+ints[j]);
                if (i==0 || j==0 || i==arr.length-1 || j==arr[i].length-1){
                    sum+=ints[j];
                }
            }
            System.out.println();
        }
        System.out.println(sum);
        System.out.println(getSum(arr));

    }
    public int getSum(int[][] arr){
        int column = arr.length-1,row=arr[0].length-1,sum;
        //四个顶点 ->可减少2次for
        sum=arr[0][0]+arr[0][row]+arr[column][0]+arr[column][row];
        int max = Math.max(column,row);
        for (int i=1; i < max ; i++) {
            //上边+下边
            if (i<column)
                sum+= arr[i][0]+arr[i][row];
            //左边+右边
            if (i<row)
                sum+= arr[0][i]+arr[column][i];
        }
        return sum;
    }
}
