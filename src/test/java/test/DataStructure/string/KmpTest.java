package test.DataStructure.string;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2023/6/7 16:00
 **/
public class KmpTest {


    public static void main(String[] args) {
        KmpTest kmpTest = new KmpTest();
        String str = "abaabaabacacaabaabcc";
        String str2 = "abaabc";
        int start = kmpTest.strStr(str, str2);
        System.out.println(str.substring(start,start+str2.length()));
    }
    public int strStr(String S, String T) {
        int i=0,j=0;
        int[] next=getNext(T);
        while (i< S.length() && j<T.length()){
            if (j==-1 || S.charAt(i)==T.charAt(j)){
                i++;
                j++;
            }else {
                j=next[j];
            }
        }
        if (j==T.length())
            return i-j;
        else
            return -1;
    }


    //求next[]数组,next[0]=-1 当遇到冲突时直接查next数组并且返回到相应位置
    private static int[] getNext(String sub){
        int[] next=new int[sub.length()+1];
        int i=0;
        int j=-1;
        next[0]=-1;
        while (i<sub.length()){
            if (j==-1 || sub.charAt(i) == sub.charAt(j)){
                next[++i]=++j;
            }else {
                j=next[j];
            }
        }
        return next;

    }
}
