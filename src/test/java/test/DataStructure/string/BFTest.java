package test.DataStructure.string;

import org.junit.Test;

import java.util.Scanner;

/**
 * @Description：
 * @Author：hh
 * @Date：2023/6/15 23:42
 **/
public class BFTest {
    public static void main(String[] args) {
        //主串
        String S="abaaabbaacxftawdaaxz";
        //子串
        String T="aaa";
        //转换为字符数组操作
        char[] s = S.toCharArray();
        char[] t = T.toCharArray();
        //i，j为比较的起始地址
        int i=0,index=0,j=0;
        //当主串或子串模式匹配结束后，BF暴力查找结束，
        while(i<s.length&&j<t.length){
            if(s[i]==t[j]){
                i++;
                j++;
            }else{
                index++;
                i=index;
                j=0;
            }
        }
        //若主串先遍历结束，则未找到，反之成功模式匹配
        //注： 特殊情况：主串和子串同时结束！
        if(i>=s.length){
            System.out.println("未找到该字符串！");
        } else {
            System.out.println("该串的索引位置为："+index);
        }
    }

    @Test
    public void BFTest2(){
        // 主串
        String S = "abaaabbaacxftawdaaxz";
        // 子串
        String T = "axz";

        // 转换为字符数组操作
        char[] s = S.toCharArray();
        char[] t = T.toCharArray();

        // i，j为比较的起始地址
        int i = 0;
        int j = 0;

        while (i < s.length && j < t.length) {
            if (s[i] == t[j]) {
                i++;
                j++;
            } else {
                // 在不匹配的情况下，将主串的索引后移一位，并将子串的索引重置为0
                i = i - j + 1;
                j = 0;
            }
        }

        // 若主串先遍历结束，则未找到，反之成功模式匹配
        if (j >= t.length) {
            int index = i - t.length;
            System.out.println("该串的索引位置为：" + index);
        } else {
            System.out.println("未找到该字符串！");
        }
    }
}
