package test.DataStructure.sort;

import org.junit.Test;

import java.util.Arrays;


public class SimpleSorts {



  /**
   * <AUTHOR>
   * @Description 冒泡排序
   * @Date 2022/10/12 21:30
   * @Param []
   * @return void
   **/
    @Test
    public void bubbling(){
        int a[]={3,44,38,5,47,15,36,26,27,2,46,4,19,50,48};
        for (int i = 0; i < a.length-1; i++) {
            boolean flag=true;
            for (int j = 0; j < a.length-1-i; j++) {
                if (a[j]>a[j+1]) {
                    int temp=a[j];
                    a[j]=a[j+1];
                    a[j+1]=temp;
                    flag=false;
                }
            }
            if (flag) {
                break;
            }
        }
        System.out.println(Arrays.toString(a));// [2, 3, 4, 5, 15, 19, 26, 27, 36, 38, 44, 46, 47, 48, 50]
    }

    /**
     * <AUTHOR>
     * @Description 选择
     * @Date 2022/10/12 21:32
     * @Param []
     * @return void
     **/
    @Test
    public void select(){
        int a[]={3,44,38,5,47,15,36,26,27,2,46,4,19,50,48};
        for (int i = 0; i < a.length-1; i++) {
            int index=i;//标记第一个为待比较的数
            for (int j = i+1; j < a.length; j++) { //然后从后面遍历与第一个数比较
                if (a[j]<a[index]) {  //如果小,就交换最小值
                    index=j;//保存最小元素的下标
                }
            }
            //找到最小值后，将最小的值放到第一的位置，进行下一遍循环
            int temp=a[index];
            a[index]=a[i];
            a[i]=temp;
        }
        System.out.println(Arrays.toString(a));//[2, 3, 4, 5, 15, 19, 26, 27, 36, 38, 44, 46, 47, 48, 50]
    }
    /**
     * <AUTHOR>
     * @Description 插入
     * @Date 2022/10/12 21:33
     * @Param []
     * @return void
     **/
    @Test
    public void insert(){
        int a[]={3,44,38,5,47,15,36,26,27,2,46,4,19,50,48};

        for (int i = 0; i < a.length; i++) {  //长度不减1，是因为要留多一个位置方便插入数
            //定义待插入的数
            int insertValue=a[i];
            //找到待插入数的前一个数的下标
            int insertIndex=i-1;
            while (insertIndex>=0 && insertValue <a[insertIndex]) {//拿a[i]与a[i-1]的前面数组比较
                a[insertIndex+1]=a[insertIndex];
                insertIndex--;
            }
            a[insertIndex+1]=insertValue;
        }
        System.out.println(Arrays.toString(a));//[2, 3, 4, 5, 15, 19, 26, 27, 36, 38, 44, 46, 47, 48, 50]
    }

    /**
     * <AUTHOR>
     * @Description 希尔
     * @Date 2022/10/12 22:27
     * @Param []
     * @return void
     **/
    @Test
    public void shell(){
        int a[]={3,44,38,5,47,15,36,26,27,2,46,4,19,50,48};
        int count=0;//比较次数
        for (int gap=a.length / 2; gap > 0; gap = gap / 2) {
            //将整个数组分为若干个子数组
            for (int i = gap; i < a.length; i++) {
                //遍历各组的元素
                for (int j = i - gap; j>=0; j=j-gap) {
                    //交换元素
                    if (a[j]>a[j+gap]) {
                        int temp=a[j];
                        a[j]=a[j+gap];
                        a[j+gap]=temp;
                        count++;
                    }
                }
            }
        }
        System.out.println(count);//16
        System.out.println(Arrays.toString(a));//[2, 3, 4, 5, 15, 19, 26, 27, 36, 38, 44, 46, 47, 48, 50]
    }
    /**
     * <AUTHOR>
     * @Description 快速
     * @Date 2022/10/12 21:34
     * @Param []
     * @return void
     **/
    @Test
    public void quick(){
        //int a[]={50,1,12,2};
        int a[]={3,44,38,5,47,15,36,26,27,2,46,4,19,50,48};
        quicksort(a,0,a.length-1);
        System.out.println(Arrays.toString(a));
    }

    public static int[] quicksort(int[] nums,Integer left,Integer right){
        int i,j,t,temp;
        if(left>right){
            return nums;
        }
        //获取基准数
        temp = nums[left];
        i = left;
        j = right;
        //从右边往左遍历,获取第一个小于基准数的下标
        while(nums[j]>=temp && i<j){
            j--;
        }

        //从左往右遍历，获取第一个大于基准数的下标
        while(nums[i]<=temp && i<j){
            i++;
        }
        //然后交换两个数在数组中的位置
        if(i<j){//判断两数下标有没有相遇
            //若没有相遇
            //交换两数的位置
            t = nums[i];
            nums[i] = nums[j];
            nums[j] = t;
            return quicksort(nums,left,right);
        }else{
            nums[left] = nums[i];
            nums[i] = temp;
            quicksort(nums, left, i-1);
            quicksort(nums, j+1, right);
            return nums;
        }
    }

}
