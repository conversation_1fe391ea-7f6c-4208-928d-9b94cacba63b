package test.javaBase.threadTest.JUC;

import org.junit.Test;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicStampedReference;

/**
 * @Description：CAS
 * @Author：hh
 * @Date：2024/3/5 17:19
 **/
public class CAS_Test {
    AtomicInteger atomicInteger = new AtomicInteger(0);
    //加锁

    //版本号 防止aba问题
    AtomicStampedReference  atomicStampedReference= new AtomicStampedReference(0,0);
    @Test
    public void test1() throws InterruptedException {

        long start = System.currentTimeMillis();
        Thread t1 = new Thread(() -> {
            for (int i = 0; i < 10000000; i++) {
                increment();
            }
        });
        t1.start();

        for (int i = 0; i < 10000000; i++) {
           increment();
        }
        t1.join();

        long ent = System.currentTimeMillis();
        System.out.println(ent-start+"ms");
        System.out.println("sum:"+ atomicInteger.get());
    }
    public void increment(){
        //CAS
        while (true){
            int oldValue = atomicInteger.get();
            int newValue = oldValue+1;
            if (atomicInteger.compareAndSet(oldValue,newValue)) {
                break;
            }
        }

    }
}
