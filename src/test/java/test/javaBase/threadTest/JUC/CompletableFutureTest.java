package test.javaBase.threadTest.JUC;

import org.junit.Test;

import java.util.concurrent.*;

/**
 * @Description：
 * CompletableFuture APl
 * ①.获得结果和触发计算(get、getNow、join、complete)
 * ②.对计算结果进行处理(thenApply、handle)
 * ③.对计算结果进行消费(thenRun、thenAccept、thenapply)
 * ④.对计算速度进行选用(applyToEither、.acceptEither、runAfterEither)
 * ⑤.对计算结果进行合并(thenCombine、.thenAcceptBoth、runAfterBoth)
 * ⑥.多任务组合(allof、anyof)
 * @Author：hh
 * @Date：2024/4/28 上午1:17
 **/
public class CompletableFutureTest {
    // 创建一个固定大小的线程池
    int nThreads = 5; // 线程池中的线程数
    ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(
            nThreads, // 核心线程数
            nThreads, // 最大线程数
            0L, // 空闲时间
            TimeUnit.MILLISECONDS, // 空闲时间单位
            new LinkedBlockingQueue<Runnable>(10000), // 任务队列
            Executors.defaultThreadFactory(), // 线程工厂
            new ThreadPoolExecutor.AbortPolicy() // 拒绝策略
    );

    @Test
    public void test() throws ExecutionException, InterruptedException {
        CompletableFuture<String> task1 = CompletableFuture.supplyAsync(() -> {
            sleep(1);

            return "task1";
        },threadPoolExecutor);
        CompletableFuture<String> task2 = CompletableFuture.supplyAsync(() -> {
            sleep(2);
            return "task2";
        }, threadPoolExecutor);
        CompletableFuture<String> task3 = task2.thenApplyAsync((preResult) -> {
            sleep(2);
            return "task2-1";
        }, threadPoolExecutor);

        /*
        thenCompose() 可以链接两个 CompletableFuture 对象，并将前一个任务的返回结果作为下一个任务的参数，它们之间存在着先后顺序。
        thenCombine() 会在两个任务都执行完成后，把两个任务的结果合并。两个任务是并行执行的，它们之间并没有先后依赖顺序。
         */
/*        //合并两个任务结果
        CompletableFuture<String> combineResult = task1.thenCombine(task2, (result1, result2) -> result1 + result2);
        System.out.println(combineResult.get());*/

     /*   //所以任务完成才继续执行 anyOf 只要有一个完成就继续执行
        CompletableFuture.allOf(task1,task2);*/
        task1.whenComplete((r,e) -> {
            System.out.println(r);
            if (e != null) {
                //异常处理
                System.out.println(e);
            }
        }).join();

    }
    @Test
    public void baseTest() throws ExecutionException, InterruptedException {
        System.out.println("主线程开始执行");
        CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
            sleep(1);
            return "Result of the asynchronous computation";
        },threadPoolExecutor);
        // 模拟从数据库获取用户信息
        CompletableFuture<String> getUserInfoFuture = CompletableFuture.supplyAsync(() -> {
            System.out.println("获取用户信息 - " + Thread.currentThread().getName());
            sleep(2); // 模拟数据库操作耗时2秒
            return "用户信息";
        }, threadPoolExecutor);

        // 处理用户信息
        CompletableFuture<String> processUserInfoFuture = getUserInfoFuture.thenApplyAsync((userInfo) -> {

            System.out.println("拿到userInfo:"+userInfo+"处理用户信息 - " + Thread.currentThread().getName());
            sleep(3); // 模拟处理耗时3秒
            return "处理后的用户信息";
        }, threadPoolExecutor);

        // 将处理结果发送到消息队列
        CompletableFuture<Void> sendToQueueFuture = processUserInfoFuture.thenAcceptAsync((processedUserInfo) -> {
            System.out.println("拿到processedUserInfo："+processedUserInfo+"发送到消息队列 - " + Thread.currentThread().getName());
            sleep(1); // 模拟发送耗时1秒
            System.out.println("消息已发送: " + processedUserInfo);
        }, threadPoolExecutor);

        // 主线程继续执行其他操作
        System.out.println("主线程继续执行其他操作");
        sleep(10);
        // 等待所有异步任务完成
        sendToQueueFuture.get();

        System.out.println("主线程执行结束");

        // 关闭线程池
        threadPoolExecutor.shutdown();
    }



    private static void sleep(int time) {
        try {
            Thread.sleep(time * 1000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
    private static void printName(){
        System.out.println(Thread.currentThread().getName());
    }
}
