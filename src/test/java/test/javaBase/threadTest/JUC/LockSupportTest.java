package test.javaBase.threadTest.JUC;

import org.junit.Test;

import java.util.concurrent.locks.LockSupport;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @Description：LockSupportDemo
 * LockSupport：
 * 功能： LockSupport 提供了基本的线程阻塞和唤醒的功能，它不是一个锁，而是一个工具类，用于实现线程的阻塞和唤醒。
 * 阻塞和唤醒： 通过 park() 方法来阻塞当前线程，通过 unpark(Thread thread) 方法来唤醒指定线程。
 * 无锁： LockSupport 没有显式的锁对象，它是基于许可（permit）的机制，每个线程最多只有一个许可，因此连续调用 unpark()方法多次也只会让线程获得一个许可，而不会累积许可。
 * 使用场景： 通常用于实现一些复杂的同步机制，比如自定义的线程同步器。
 * ReentrantLock： 依赖于LockSupport
 * 功能： ReentrantLock 是一个重入锁，用于实现独占式的锁定和解锁。
 * 独占锁： ReentrantLock 提供了 lock() 和 unlock() 方法来手动控制锁的获取和释放，线程获取锁后可以多次调用 lock() 方法而不会导致死锁。
 * 可重入性： 支持同一个线程多次获取锁而不会产生死锁。
 * 锁的公平性： ReentrantLock 可以选择是否公平地获取锁，即等待时间最长的线程优先获取锁。
 * 条件变量： ReentrantLock 提供了条件变量（Condition）的功能，可以用来实现更复杂的线程通信。
 * 使用场景： 适用于需要更细粒度控制和更复杂同步逻辑的场景，比如生产者-消费者问题。
 * @Author：hh
 * @Date：2024/3/5 17:11
 **/
public class LockSupportTest {
    ReentrantLock lock = new ReentrantLock();

    @Test
    public void test1(){
        Thread t1 = new Thread(() -> {
            lock.lock();
            System.out.println("start");
            LockSupport.park();
            System.out.println("stop");
        });

        t1.start();
        lock.unlock();
        LockSupport.unpark(t1);


    }
}
