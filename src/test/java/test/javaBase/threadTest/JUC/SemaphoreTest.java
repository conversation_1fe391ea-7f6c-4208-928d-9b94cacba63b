package test.javaBase.threadTest.JUC;


import org.junit.Test;

import java.util.Queue;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.locks.LockSupport;
/**
 * Semaphore 类在 Java 的 java.util.concurrent 包中，用于控制同时访问特定资源的线程数量。以下是 Semaphore 类的一些常用 API：
 * 构造方法：
 * Semaphore(int permits)：创建一个具有指定许可数的信号量。
 * Semaphore(int permits, boolean fair)：创建一个具有指定许可数的信号量，并指定是否采用公平模式。
 * 获取许可：
 * void acquire()：获取一个许可，如果当前没有可用的许可，当前线程将阻塞，直到有可用的许可。
 * void acquireUninterruptibly()：获取一个许可，如果当前没有可用的许可，当前线程将阻塞，直到有可用的许可。与 acquire() 不同的是，该方法不会响应中断。
 * int availablePermits()：返回当前可用的许可数。
 * boolean tryAcquire()：尝试获取一个许可，如果成功获取，则返回 true，否则立即返回 false。
 * boolean tryAcquire(long timeout, TimeUnit unit)：尝试在指定的等待时间内获取一个许可，如果成功获取，则返回 true，否则返回 false。
 * 释放许可：
 * void release()：释放一个许可，增加可用的许可数。
 * void release(int permits)：释放指定数量的许可，增加可用的许可数。
 * 其他方法：
 * boolean hasQueuedThreads()：查询是否有线程在等待获取许可。
 * int getQueueLength()：返回正在等待获取许可的线程估计数。
 * protected Collection<Thread> getQueuedThreads()：返回一个包含正在等待获取许可的线程集合。
 * String toString()：返回一个标识此信号量的字符串，包括许可数和等待线程的信息。
 */

/**
 * @Description：
 * @Author：hh
 * @Date：2025/3/23 23:54
 **/
public class SemaphoreTest {
    Semaphore semaphore = new Semaphore(1,true);
    volatile static ArrayBlockingQueue<Integer> queue = new ArrayBlockingQueue<>(10);
    static {
        for (int i = 0; i < 10; i++) {
            queue.add(i);
        }
    }



    // 信号量
    @Test
    public void test() throws InterruptedException {
        long start = System.currentTimeMillis();
        Thread t1 = new Thread(new TestTask(semaphore));
        Thread t2 = new Thread(new TestTask(semaphore));
        t1.start();
        t2.start();

        t1.join();
        t2.join();
        System.out.println(System.currentTimeMillis()-start);
    }
}

class TestTask implements Runnable {
    Semaphore semaphore ;
    public TestTask(Semaphore semaphore ) {
        this.semaphore = semaphore;
    }
    @Override
    public void run() {
        try {
            for (int i = 0; i < 5; i++) {
                semaphore.acquire();
                Integer first = SemaphoreTest.queue.poll();
                System.out.println(Thread.currentThread().getName()+": "+first);
                semaphore.release();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}

