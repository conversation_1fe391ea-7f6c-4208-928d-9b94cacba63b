package test.javaBase.threadTest.JUC;

import java.util.concurrent.CountDownLatch;

public class CountDownLatchExample {
    public static void main(String[] args) {
        // 创建一个 CountDownLatch 实例，计数为 3
        CountDownLatch latch = new CountDownLatch(3);


        // 创建三个线程，每个线程完成任务后调用 countDown()
        for (int i = 0; i < 3; i++) {
            new Thread(new Task(latch)).start();
        }

        try {
            // 主线程等待 latch 计数变为 0
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 所有任务完成后，主线程继续执行
        System.out.println("所有任务已完成，主线程继续执行");
    }
}

class Task implements Runnable {
    private CountDownLatch latch;

    public Task(CountDownLatch latch) {
        this.latch = latch;
    }

    @Override
    public void run() {
        try {
            // 模拟任务执行时间
            System.out.println(Thread.currentThread().getName() + " 开始任务");
            Thread.sleep((long) (Math.random() * 5000));
            System.out.println(Thread.currentThread().getName() + " 完成任务");
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            // 任务完成后，countDown 一次
            latch.countDown();
        }
    }
}
