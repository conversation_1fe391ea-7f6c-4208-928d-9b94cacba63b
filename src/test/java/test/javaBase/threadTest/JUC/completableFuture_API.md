## API

1. **supplyAsync()**: 异步执行任务并返回 CompletableFuture。
2. **thenApply()**: 对上一个 CompletableFuture 的结果应用函数。
3. **thenAccept()**: 对上一个 CompletableFuture 的结果执行操作。
4. **thenCombine()**: 对两个 CompletableFuture 的结果应用函数。
5. **thenCompose()**: 对上一个 CompletableFuture 的结果应用函数，并返回新的 CompletableFuture。
6. **exceptionally()**: 处理异常完成的 CompletableFuture，并返回默认值。
7. **handle()**: 处理 CompletableFuture 的结果或异常。
8. **thenAcceptBoth()**: 当两个 CompletableFuture 都完成时，对它们的结果执行操作。
9. **runAsync()**: 异步执行任务，但不返回结果。
10. **thenRun()**: 当上一个 CompletableFuture 完成时执行操作，不关心结果。
11. **thenComposeAsync()**: 类似于 thenCompose()，但在新的线程上执行函数。
12. **thenCombineAsync()**: 类似于 thenCombine()，但在新的线程上执行函数。
13. **anyOf()**: 在多个 CompletableFuture 中任意一个完成时返回结果。
14. **allOf()**: 在多个 CompletableFuture 全部完成时返回结果。
15. **cancel()**: 取消 CompletableFuture 的执行。
16. **complete()**: 手动完成 CompletableFuture，并设置其结果。
17. **completeExceptionally()**: 手动完成 CompletableFuture，并设置异常结果。
18. **isDone()**: 检查 CompletableFuture 是否已完成。
19. **isCancelled()**: 检查 CompletableFuture 是否已取消。
 
 异步编程

1. **thenApplyAsync()**: 类似于 thenApply()，但在新的线程上执行函数。
2. **thenAcceptAsync()**: 类似于 thenAccept()，但在新的线程上执行操作。
3. **thenRunAsync()**: 类似于 thenRun()，但在新的线程上执行操作。
4. **exceptionallyAsync()**: 类似于 exceptionally()，但在新的线程上执行处理。
5. **handleAsync()**: 类似于 handle()，但在新的线程上执行处理。
6. **acceptEitherAsync()**: 在两个 CompletableFuture 中的任意一个完成时执行操作，并在新的线程上执行。

7. **acceptEither()**: 在两个 CompletableFuture 中的任意一个完成时执行操作，但不在新的线程上执行。



## 什么是 CompletableFuture

`CompletableFuture` 是 Java 8 引入的一个强大的类，位于 `java.util.concurrent` 包中，用于处理异步编程和组合多个异步操作。它提供了丰富的 API 来简化异步任务的编排和处理，支持链式调用、异常处理和多任务组合等特性。

### 主要特点

1. **异步执行**：
    - **`supplyAsync`** 和 **`runAsync`** 方法用于异步执行任务。
    - **`supplyAsync`** 返回一个 `CompletableFuture`，并且任务的返回结果会被封装在这个 `CompletableFuture` 中。
    - **`runAsync`** 不返回结果，只是异步执行某个任务。

2. **结果处理**：
    - **`thenApply`** 和 **`thenApplyAsync`** 方法用于对计算结果进行处理，返回一个新的 `CompletableFuture`。
    - **`thenAccept`** 和 **`thenAcceptAsync`** 方法用于对计算结果进行消费，不返回新的结果，只是执行一个动作。
    - **`thenRun`** 和 **`thenRunAsync`** 方法用于在计算完成后执行某个动作，不依赖计算结果，也不返回新的结果。

3. **异常处理**：
    - **`whenComplete`** 和 **`whenCompleteAsync`** 方法用于在任务完成（包括成功和失败）后执行某个动作。
    - **`exceptionally`** 方法用于在任务失败后进行异常处理，并返回一个备用结果。
    - **`handle`** 和 **`handleAsync`** 方法用于处理计算结果或异常。

4. **组合多个任务**：
    - **`thenCompose`** 和 **`thenComposeAsync`** 方法用于链接两个 `CompletableFuture` 对象，前一个任务的返回结果作为下一个任务的参数，它们之间存在先后顺序。
    - **`thenCombine`** 和 **`thenCombineAsync`** 方法用于在两个任务都完成后，将它们的结果合并。两个任务是并行执行的，没有顺序依赖。
    - **`applyToEither`**、**`acceptEither`** 和 **`runAfterEither`** 方法用于在两个任务中任何一个完成时执行操作。
    - **`allOf`** 和 **`anyOf`** 静态方法用于组合多个 `CompletableFuture` 对象，`allOf` 在所有任务完成时返回，`anyOf` 在任意一个任务完成时返回。

5. **线程池支持**：
    - 可以指定线程池来执行异步任务，默认情况下使用 `ForkJoinPool.commonPool()`。

### 示例代码

以下是一些 `CompletableFuture` 的示例代码，帮助你理解其用法：

1. **异步执行任务**：
    ```java
    CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
        sleep(1);
        return "Result of the asynchronous computation";
    }, threadPoolExecutor);
    ```


2. **结果处理**：
    ```java
    CompletableFuture<String> future2 = CompletableFuture.supplyAsync(() -> {
        sleep(1);
        System.out.println(Thread.currentThread().getName());
        return "任务2";
    }, threadPoolExecutor);

    CompletableFuture<String> future3 = future2.thenApplyAsync((result) -> {
        System.out.println(Thread.currentThread().getName());
        return "任务3-》任务2回调1:" + result;
    }, threadPoolExecutor).thenApplyAsync((result) -> {
        System.out.println(Thread.currentThread().getName());
        return "任务3-》任务2回调2:" + result;
    });
    ```


3. **异常处理**：
    ```java
    future2.whenComplete((r, e) -> {
        System.out.println(r);
        if (e != null) {
            System.out.println(e);
        }
    }).join();
    ```


4. **组合多个任务**：
    ```java
    CompletableFuture<String> task1 = CompletableFuture.supplyAsync(() -> {
        sleep(1);
        return "task1";
    }, threadPoolExecutor);

    CompletableFuture<String> task2 = CompletableFuture.supplyAsync(() -> {
        sleep(2);
        return "task2";
    }, threadPoolExecutor);

    CompletableFuture<String> combineResult = task1.thenCombine(task2, (result1, result2) -> result1 + result2);
    System.out.println(combineResult.get());
    ```


5. **多任务组合**：
    ```java
    CompletableFuture<Void> allTasks = CompletableFuture.allOf(task1, task2);
    allTasks.join();
    System.out.println("所有任务完成");

    CompletableFuture<Object> anyTask = CompletableFuture.anyOf(task1, task2);
    System.out.println("任意一个任务完成: " + anyTask.get());
    ```


### 为什么使用 CompletableFuture

- **简化异步编程**：提供了丰富的 API 来处理异步任务，避免了传统的回调地狱。
- **高效的组合**：可以通过多种方式组合多个异步任务，提高代码的可读性和性能。
- **强大的异常处理**：支持在任务完成或失败时进行异常处理。
- **与函数式编程结合**：可以与 Java 8 的函数式编程特性结合，使用 lambda 表达式来简化代码。

通过这些特点和示例，你可以更好地理解和使用 `CompletableFuture` 来处理复杂的异步任务。