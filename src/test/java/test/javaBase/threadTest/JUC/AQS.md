## 什么是AQS?
> AQS(Abstract Queued Synchronizer)翻译过来就是抽象队列同步器，是juc并发包下locks中的一个抽象class。包括ReentrantLock(独占)，ReentrantReadWriteLock（读写锁），Semaphore（共享锁），CountDownLatch（共享锁），CyclicBarrier等都用到了AQS。

> 它其实就是多个线程在同时执行时，通过CAS的方式去更新AQS的state值。CAS在硬件层面保证了同时只有一个线程可操作，一个线程加锁成功，其他的失败。加锁成功的线程会设置exclusiveOwnerThread = 当前线程，并state=1。重入次数的话会在state值上做累加。
> 加锁失败的线程则会放入一个锁池队列，这样当上一个线程释放锁时，下一个线程就能够直接上去把锁拿到。
![AQS流程图.png](img/img.png)
## 什么是独占锁（排他锁） 读写锁 共享锁
独占锁、读写锁和共享锁是Java并发编程中用于管理线程访问共享资源的不同类型的锁机制。它们的主要区别如下：

1. **独占锁（Exclusive Lock）**
    - **特点**：独占锁也称为排他锁或互斥锁，一次只能被一个线程持有。
    - **适用场景**：适用于写操作，即当多个线程需要修改共享资源时，使用独占锁可以确保同一时间只有一个线程能够进行修改，避免数据不一致。
    - **示例**：`ReentrantLock`是一个典型的独占锁实现。

2. **读写锁（Read-Write Lock）**
    - **特点**：读写锁允许多个线程同时读取共享资源，但只允许一个线程写入共享资源。读操作和写操作是互斥的，即写操作需要独占锁。
    - **适用场景**：适用于读多写少的场景，例如多个线程需要频繁地读取某个数据结构，但较少地对其进行修改。读写锁可以提高并发性能。
    - **示例**：`ReentrantReadWriteLock`是一个典型的读写锁实现。

3. **共享锁（Shared Lock）**
    - **特点**：共享锁允许多个线程同时持有该锁，用于允许多个线程并发地访问共享资源。
    - **适用场景**：适用于多个线程需要读取或执行某些共享操作而不需要修改共享资源的情况。
    - **示例**：`Semaphore`和`CountDownLatch`可以用于实现共享锁的行为，但它们更常用于其他类型的同步控制。`ReentrantReadWriteLock`的读锁也可以视为一种共享锁。

总结：

- **独占锁**：单个线程独占资源，适用于写操作。
- **读写锁**：允许多个线程并发读取，但写操作互斥，适用于读多写少的场景。
- **共享锁**：允许多个线程并发访问，适用于读操作或不需要修改共享资源的场景。

## tips
> AQS默认是非公平锁，当线程A锁释放state=0，exclusiveOwnerThread = null，然后唤醒线程B去获取锁，但是这个时候突然来了一个线程D，二话不说直接执行CAS把锁抢占了，更新state=1，加锁成功。所以线程B只能再次回到等待队列中。

> AQS的公平锁指的是公平竞争，会提前判断等待队列中是否有线程正在等待，若有等待，直接进入等待队列的队尾。哪怕state=0，exclusiveOwnerThread = null也是如此。
## 常见AQS实现类的特点

以下是 `ReentrantLock`、`ReentrantReadWriteLock`、`Semaphore` 和 `CountDownLatch` 的特点：

1. **ReentrantLock**
   - **特点**：
      - **独占锁**：一次只能被一个线程持有。
      - **可重入**：同一个线程可以多次获取同一把锁而不会引起死锁。
      - **可中断**：获取锁的操作可以被中断。
      - **可超时**：可以设置尝试获取锁的超时时间。
   - **适用场景**：适用于需要独占访问共享资源的场景，如线程间的互斥访问。
   - **示例**：
       ```java
       ReentrantLock lock = new ReentrantLock();
       lock.lock();
       try {
           // 执行逻辑代码
       } finally {
           lock.unlock();
       }
       ```


2. **ReentrantReadWriteLock**
   - **特点**：
      - **读写锁**：允许多个线程同时读取共享资源，但只允许一个线程写入共享资源。
      - **可重入**：读锁和写锁都是可重入的。
      - **支持公平锁和非公平锁**：可以根据构造函数参数选择公平锁或非公平锁。
   - **适用场景**：适用于读多写少的场景，例如多个线程需要频繁地读取某个数据结构，但较少地对其进行修改。读写锁可以提高并发性能。
   - **示例**：
       ```java
       ReentrantReadWriteLock rwLock = new ReentrantReadWriteLock();
       rwLock.readLock().lock();
       try {
           // 执行读操作
       } finally {
           rwLock.readLock().unlock();
       }

       rwLock.writeLock().lock();
       try {
           // 执行写操作
       } finally {
           rwLock.writeLock().unlock();
       }
       ```


3. **Semaphore**
   - **特点**：
      - **共享锁**：允许多个线程同时持有该锁，用于允许多个线程并发地访问共享资源。
      - **计数器**：通过一个计数器来控制同时访问资源的线程数量。
      - **可中断**：获取许可的操作可以被中断。
      - **公平性可选**：可以根据构造函数参数选择公平锁或非公平锁。
   - **适用场景**：适用于需要限制同时访问某一资源的线程数量的场景，如数据库连接池的最大连接数控制。
   - **示例**：
       ```java
       Semaphore semaphore = new Semaphore(3);
       semaphore.acquire();
       try {
           // 执行逻辑代码
       } finally {
           semaphore.release();
       }
       ```


4. **CountDownLatch**
   - **特点**：
      - **共享锁**：允许多个线程并发地访问共享资源。
      - **倒计数器**：通过一个倒计数器来控制主线程等待其他线程执行完毕。
      - **一次性**：一旦倒计数器变为 0，就不能重置。如果需要重置，可以考虑使用 `CyclicBarrier`。
   - **适用场景**：适用于需要等待多个线程完成任务后再继续执行的场景，如主线程等待多个子线程完成初始化工作。
   - **示例**：
       ```java
       CountDownLatch latch = new CountDownLatch(3);
       for (int i = 0; i < 3; i++) {
           new Thread(() -> {
               try {
                   // 执行任务
                   latch.countDown();
               } catch (Exception e) {
                   e.printStackTrace();
               }
           }).start();
       }

       latch.await();
       // 所有任务完成后，主线程继续执行
       ```


这些特点和示例可以帮助你更好地理解和使用这些同步工具类。
## 跟踪代码原理

```
public void method(){
    ReentrantLock lock = new ReentrantLock();
    lock.lock();
    //执行逻辑代码
    lock.unlock();
}
```
 ReentrantLock构造器
````
public ReentrantLock() {
		//默认是非公平锁
        sync = new NonfairSync();
    }

````
 ReentrantLock#lock
````
public void lock() {
	//调用非公平锁的lock方法
      sync.lock();
   }

final void lock() {
	//执行CAS
   if (compareAndSetState(0, 1))
   		//成功获取锁，设置exclusiveOwnerThread = 当前线程
        setExclusiveOwnerThread(Thread.currentThread());
    else
    	//否则再尝试一次
        acquire(1);
}

````
AbstractQueuedSynchronizer#acquire
````
public final void acquire(int arg) {
	//尝试再次加锁，若不成功则把当前线程放入等待队列中
	//最后阻塞该队列
    if (!tryAcquire(arg) &&
        acquireQueued(addWaiter(Node.EXCLUSIVE), arg))
        selfInterrupt();
}

````
Reentrant.NonfairSync#tryAcquire
````
protected final boolean tryAcquire(int acquires) {
  return nonfairTryAcquire(acquires);
}

final boolean nonfairTryAcquire(int acquires) {
   final Thread current = Thread.currentThread();
   int c = getState();
   //再判断一次，state=0
   if (c == 0) {
   		//进行加锁
       if (compareAndSetState(0, acquires)) {
           //加锁成功
           setExclusiveOwnerThread(current);
           return true;
       }
   }
   //判断当前线程是否重入
   else if (current == getExclusiveOwnerThread()) {
       //重入次数++
       int nextc = c + acquires;
       if (nextc < 0) // overflow
           throw new Error("Maximum lock count exceeded");
       //设置重入次数
       setState(nextc);
       return true;
   }
   return false;
}
````
AbstractQueuedSynchronizer#addWaiter
````
private Node addWaiter(Node mode) {
	//创建node元素
     Node node = new Node(Thread.currentThread(), mode);
     Node pred = tail;
     //若最后一个node不为空
     if (pred != null) {
     	//该node的上一个就是最后一个(当前node加在最后)
         node.prev = pred;
         if (compareAndSetTail(pred, node)) {
             pred.next = node;
             return node;
         }
     }
     //初始化设置，
     enq(node);
     return node;
 }

````
AbstractQueuedSynchronizer#acquireQueued
```
final boolean acquireQueued(final Node node, int arg) {
   boolean failed = true;
   try {
       boolean interrupted = false;
       //for无限循环
       for (;;) {
       	   //获取最后一个节点(当前节点)的上一个
           final Node p = node.predecessor();
           //若上一个是头部节点，并且加锁成功了
           if (p == head && tryAcquire(arg)) {
               //就把当前节点设置头节点
               setHead(node);
               p.next = null; // help GC
               failed = false;
               //这样不阻塞，一会再试一次看看能不能加锁成功
               return interrupted;
           }
           if (shouldParkAfterFailedAcquire(p, node) &&
               parkAndCheckInterrupt())
               interrupted = true;
       }
   } finally {
       if (failed)
           cancelAcquire(node);
   }
}

```
## 为什么使用双向队列
在 `AbstractQueuedSynchronizer` (AQS) 中使用双向队列的主要原因是为了高效地管理等待线程。双向队列使得每个节点都能直接引用其前驱和后继节点，这样在插入和删除节点时可以做到常数时间复杂度的操作，提升了并发环境下的性能。

具体来说，双向队列的好处包括：

1. **高效的插入和删除**：使用 CAS 操作更新尾节点时，双向引用简化了插入过程，同时也方便了释放锁时从等待队列中删除节点。
2. **简化 GC 工作**：当一个节点被释放时，可以通过前驱节点将 `next` 指针置为 `null`，帮助垃圾回收器更快地回收内存。
3. **公平锁的实现**：在公平锁的情况下，可以轻松地遍历等待队列以确保按照线程的加入顺序来分配锁。
4. **线程状态的跟踪**：双向队列使得线程状态的跟踪更加灵活，每个线程节点都能轻易地查看其前面的线程是否已经获取了锁。

我们可以将这些解释添加到文档中，以便更好地说明为什么 AQS 使用双向队列。以下是需要修改的文档内容：


https://blog.csdn.net/p874593785/article/details/125411153