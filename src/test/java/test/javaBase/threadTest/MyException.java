package test.javaBase.threadTest;

/**
 * @Description：
 * @Author：hh
 * @Date：2024/4/28 上午2:03
 **/
public class MyException extends Exception {

    //异常的描述信息
    private String msg;

    //构造器
    public MyException(String msg) {
       super(msg);
       this.msg = msg;
       System.err.println("自定义异常："+msg);
    }
    public MyException(String msg, Throwable cause) {
        super(msg, cause);
        this.msg = msg;
        System.err.println("自定义异常："+msg);
    }
    public MyException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        System.err.println("自定义异常 code:"+errorCode.getCode()+"，msg:"+errorCode.getMessage());
    }
    //toString：异常的打印信息
    @Override
    public String toString() {
        return "捕获自定义异常 ExecutionException {"+msg+"}";
    }
}
