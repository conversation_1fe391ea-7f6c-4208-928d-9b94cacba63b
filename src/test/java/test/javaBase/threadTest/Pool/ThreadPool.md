## Thread的一些知识
* stop() 停止线程太暴力线程立马停止并且释放锁（synchronized,而ReentrantLock不会释放，其他线程拿不到对象的锁）
* 可以用interrupt()，改变锁的状态，线程不会立马停止，可以通过判断该状态是否要关闭，并加入判断条件，以及自己的业务代码
* 自定义拒绝策略 实现RejectedExecutionHandler接口
## 线程池分类
* FixedThreadPool（有限线程数的线程池）
* CachedThreadPool 缓存线程池 （无限线程数的线程池）
* ScheduledThreadPool 延时线程池（定时线程池）
* SingleThreadExecutor （单一线程池）
* SingleThreadScheduledExecutor（单一定时线程池）
* ForkJoinPool 大任务拆分为多个小任务 （窃取策略->一个线程计算完后 会去窃取其他线程的任务（从队列尾部窃取））（孕妇线程池j）
## 线程池状态 https://blog.csdn.net/p874593785/article/details/125411153
![img_4.png](img/img_4.png)
![img_5.png](img/img_5.png)
* STOP状态：停掉线程任务，并准备进入TIDYING状态。
* TIDYING状态：将线程池中的线程都停掉，然后进入TIDYING状态，
* TERMINATED状态：线程池中的线程都停止了，线程池也停止了，进入TERMINATED状态 。在改为这个状态前可以通过从写terminated()方法执行自己的业务。
## 
## 第六种线程池ForkJoinPool
第六种线程池为什么要单独拎出来说呢，是因为这个线程池是在JDK7加入的，
他的名字其实也大概的描述了他的执行机制。我们先看下面这张图

![img.png](img/img.png)
task任务为一个线程任务，但是这个任务下会有三个子任务，这三个任务又可以分为三个子线程去执行到这个就可以理解为Fork，
但是这个task任务需要拿到结果，就需要他的三个子线程都执行完成才能拿到结果，
这里其实就是将他的三个子任务去Join了，等到子任务都执行完了， 才会返回task任务的执行结果。
另外，在ForkJoinPool线程池中，他们每个线程都有自己的独立的任务队列， 例如下面这张图所示


![img_1.png](img/img_1.png)
这里的理解其实就是，ForkJoinPool会有一个自己的公共队列，当这个公共队列执行的线程任务所Fork
出来的子线程任务将不会放到公共队列中，而是放在自己单独的队列中，这样就互相不影响。 这种设计其实就是为了减少线程间的竞争和切换，是很高效的一种方式。
当发生某一个线程任务的子任务很繁重，但另外一个线程的子任务却很少时，ForkJoinPool会怎么去处理呢，
其实在ForkJoinPool的子任务线程队列中使用的是一种双端队列，再加上运行work-stealing偷线程任务的功能完美的平衡了各个线程的负载

![img_2.png](img/img_2.png)
看到上面这个图，当Thread1的线程任务满了，而Thread0的线程队列空闲的时候，Thread0的线程就会去Thread1那帮忙，
这时Thread0线程就会跟Thread1访问同一个队列，也就是访问Thread1的WorkQueue来帮他完成任务。
最后我们再看一下ForkJoinPool的内部结构
![img_3.png](img/img_3.png)
ForkJoinPool.png
你可以看到 ForkJoinPool 线程池和其他线程池很多地方都是一样的，但重点区别在于它每个线程都有一个自己的双端队列来存储分裂出来的子任务。
ForkJoinPool 非常适合用于递归的场景，例如树的遍历、最优路径搜索等场景。
https://www.cnblogs.com/maoyx/p/13991828.html

在 `ForkJoinPool` 中，`fork` 方法用于将一个任务拆分为多个子任务，并将这些子任务提交到当前线程的任务队列中。具体来说：

1. **拆分任务**：`fork` 方法通常在递归任务中使用，将一个大任务拆分为多个小任务（子任务）。
2. **提交子任务**：拆分后的子任务会被提交到当前线程的任务队列中。
3. **异步执行**：这些子任务可以异步执行，从而提高任务的并行处理能力。

下面是一个简单的例子来说明 `fork` 方法的使用：

```java
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.RecursiveTask;

public class ForkJoinExample {
    public static void main(String[] args) {
        ForkJoinPool forkJoinPool = new ForkJoinPool();
        int result = forkJoinPool.invoke(new MyTask(10));
        System.out.println("最终结果: " + result);
        forkJoinPool.shutdown();
    }

    static class MyTask extends RecursiveTask<Integer> {
        private final int n;

        MyTask(int n) {
            this.n = n;
        }

        @Override
        protected Integer compute() {
            if (n <= 1) {
                return n;
            } else {
                MyTask leftTask = new MyTask(n - 1);
                MyTask rightTask = new MyTask(n - 2);

                // 提交并异步执行左子任务
                leftTask.fork();

                // 同步执行右子任务
                int rightResult = rightTask.compute();

                // 等待左子任务完成并返回结果
                int leftResult = leftTask.join();

                return leftResult + rightResult;
            }
        }
    }
}
```


在这个例子中：

1. **递归任务**：`MyTask` 是一个递归任务，继承自 `RecursiveTask<Integer>`。
2. **拆分任务**：在 `compute` 方法中，如果 `n` 大于 1，则将任务拆分为两个子任务 `leftTask` 和 `rightTask`。
3. **提交子任务**：使用 `leftTask.fork()` 方法将左子任务异步提交到当前线程的任务队列中。
4. **同步执行**：右子任务 `rightTask` 使用 `compute` 方法同步执行。
5. **等待结果**：使用 `leftTask.join()` 方法等待左子任务完成并获取其结果。

通过这种方式，`ForkJoinPool` 可以高效地并行处理递归任务，充分利用多核处理器的能力。`fork` 方法在这里起到了将任务拆分并异步提交的关键作用。
