package test.javaBase.threadTest.Pool;

import org.junit.jupiter.api.Test;
import test.javaBase.threadTest.ErrorCode;
import test.javaBase.threadTest.MyException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.*;
import java.util.stream.LongStream;

/**
 * @Description：线程池
 * FixedThreadPool（有限线程数的线程池）
 * CachedThreadPool 缓存 （无限线程数的线程池）
 * ScheduledThreadPool （定时线程池）
 * SingleThreadExecutor （单一线程池）
 * SingleThreadScheduledExecutor（单一定时线程池）
 * ForkJoinPool （孕妇线程池）
 * @Author：hh
 * @Date：2024/4/28 上午1:56
 **/
public class ThreadPoolExecutorTest {
    volatile static int count = 0;



    //计算相加
    @Test
    public void ForkJoinPoolTest() {
        // 也可以jdk8提供的通用线程池ForkJoinPool.commonPool
        // 可以在构造函数内指定线程数
        ForkJoinPool forkJoinPool = new ForkJoinPool();
        long[] numbers = LongStream.rangeClosed(1, 10000).toArray();
        // 这里可以调用submit方法返回的future，通过future.get获取结果
        Long result = forkJoinPool.invoke(new SumTask(numbers, 0, numbers.length - 1));
        forkJoinPool.shutdown();
        System.out.println("最终结果："+result);
        System.out.println("活跃线程数："+forkJoinPool.getActiveThreadCount());
        System.out.println("窃取任务数："+forkJoinPool.getStealCount());
    }
    /**
     * 求和任务类继承RecursiveTask
     * ForkJoinTask一共有3个实现：
     * RecursiveTask：有返回值
     * RecursiveAction：无返回值
     * CountedCompleter：无返回值任务，完成任务后可以触发回调
     */
    private static class SumTask extends RecursiveTask<Long> {
        private long[] numbers;
        private int from;
        private int to;


        public SumTask(long[] numbers, int from, int to) {
            this.numbers = numbers;
            this.from = from;
            this.to = to;
        }

        /**
         * ForkJoin执行任务的核心方法
         * @return
         */
        @Override
        protected Long compute() {
            // 设置拆分的最细粒度，即阈值，如果满足条件就不再拆分，执行计算任务
            //也可以说是临界值 也就是一个子任务处理10个数值 并开始计算
            if (to - from < 10) {
                long total = 0;
                for (int i = from; i <= to; i++) {
                    total += numbers[i];
                }
                return total;
            } else { // 否则继续拆分，递归调用
                int middle = (from + to) / 2;
                SumTask taskLeft = new SumTask(numbers, from, middle);
                SumTask taskRight = new SumTask(numbers, middle + 1, to);
                taskLeft.fork();
                taskRight.fork();
                return taskLeft.join() + taskRight.join();
            }
        }
    }
    //定时ScheduledThreadPool
    volatile int errorCount = 0;
    static class  MyThreadPoolServic extends ScheduledThreadPoolExecutor {

        public MyThreadPoolServic(int corePoolSize, RejectedExecutionHandler handler) {
            super(corePoolSize, handler);
        }

        @Override
        protected void afterExecute(Runnable r, Throwable t) {
            super.afterExecute(r,t);
            System.out.println("我的异常  处理任务：" + r.toString());
        }
    }

    ScheduledFuture<?> future = null;
    @Test
    public void ScheduledThreadPoolTest2() throws Exception, InterruptedException {

        ScheduledThreadPoolExecutor executorService = new ScheduledThreadPoolExecutor(2,new ThreadPoolExecutor.AbortPolicy());
        executorService.setThreadFactory((Runnable r)->{
            Thread thread = new Thread(r);
            thread.setName("executorService:"+thread.getId());
            return thread;
        });
        executorService.setMaximumPoolSize(5);

        Runnable task01 = new Runnable() {
            @Override
            public void run() {
                String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
                sleep(5000);
                future.cancel(true);
                System.out.println("任务1 "+ Thread.currentThread().getName()+" 执行" + " 队列："+executorService.getQueue().size()+ " " );

            }
        };
        Runnable task02 = new Runnable() {
            @Override
            public void run() {
                String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
                sleep(3000);
                System.out.println("任务2" +Thread.currentThread().getName()+" 执行" + " 队列："+executorService.getQueue().size()+ " " );

            }
        };
        Runnable task04 = new Runnable() {
            @Override
            public void run() {
                String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
                sleep(3000);
                System.out.println("任务4" +Thread.currentThread().getName()+" 执行" + " 队列："+executorService.getQueue().size()+ " " );

            }
        };
      /*  Runnable task02 = new ThreadTask();*/
        Runnable task03 = new ThreadTask();

        future = executorService.scheduleWithFixedDelay(task01, 1, 1, TimeUnit.SECONDS);
        executorService.scheduleAtFixedRate(task02, 1, 1, TimeUnit.SECONDS);
        executorService.scheduleAtFixedRate(task04, 1, 1, TimeUnit.SECONDS);

//        executorService.setCorePoolSize(6);
//        future.cancel(true);
     /*   executorService.scheduleWithFixedDelay(task02, 0, 1, TimeUnit.SECONDS);*/
      /*  executorService.scheduleWithFixedDelay(task03, 0, 1, TimeUnit.SECONDS);*/
   /*     executorService.remove(task01);*/
        sleep(20000);

        System.out.println("队列中任务数：" + executorService.getQueue().size());

   /*     executorService.remove(task01);*/
        executorService.shutdownNow();
    }
    @Test
    public void ScheduledThreadPoolTest() throws ExecutionException, InterruptedException {
        ScheduledExecutorService executorService  =  Executors.newScheduledThreadPool(1);
        Callable<String> task01 = () -> {
            String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            System.out.println(time+":任务1 执行了"+Thread.currentThread().getName());
            return "1";
        };
        Runnable task02 = () -> {
            String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            count++;
            System.out.println(time+":任务2 执行了"+Thread.currentThread().getName()+" "+count);
            int t = 1/0;

        };
        Runnable task03 = () -> {
            String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            count++;
            System.out.println(time+":任务3 执行了"+Thread.currentThread().getName()+" "+count);
        };
        Runnable task04 = () -> {
            String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            count++;
            System.out.println(time+":任务4 执行了"+Thread.currentThread().getName()+" "+count);
        };
        String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        System.out.println(time+"：开始执行");
        // 2秒后执行一次任务，从任务提交开始计算时间
        ScheduledFuture<String> schedule = executorService.schedule(task01, 2, TimeUnit.SECONDS);
        String result = schedule.get();
        System.out.println("result:"+result);
        //1s
        ScheduledFuture<?> schedule2 = executorService.scheduleAtFixedRate(task02, 0, 1, TimeUnit.SECONDS);
        ScheduledFuture<?> schedule3 = executorService.scheduleAtFixedRate(task03, 0, 3, TimeUnit.SECONDS);
        ScheduledFuture<?> schedule4 = executorService.scheduleAtFixedRate(task04, 0, 3, TimeUnit.SECONDS);
        // 在一段时间后取消任务并删除
       /* executorService.schedule(() -> {
            boolean isCancelled = schedule2.cancel(true); // true 表示即使任务正在执行也应该中断
            if (isCancelled) {
                System.out.println("Task canceled and removed from queue");
            } else {
                System.out.println("Unable to cancel task");
            }
        }, 5, TimeUnit.SECONDS);*/
        sleep(100000);
        executorService.shutdownNow();

    }



    //futureTask
    @Test
    public void FutureTaskTest() {

      /* 断言 为true时程序继续进行 否则报AssertionError:异常 并输出value should be greater than 200
       int value = 100;
        assert value > 200 : "value should be greater than 200";*/
        Callable<String> task01 = () -> {
            String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            System.out.println(time+":schedule 执行了");
            return "666";
        };
        FutureTask<String> futureTask = new FutureTask<>(task01);
        futureTask.run();
//        new Thread(futureTask).start();
        try {
            String result = futureTask.get();
            if (result==null || result.equals(""))
                throw new MyException(ErrorCode.Is_Null);
            System.out.println(result);
        } catch (Exception e) {
           //异常处理
            e.printStackTrace();
        }
    }
    //自定义线程池
    @Test
    public void test(){
        // 创建一个固定大小的线程池
        int nThreads = 5; // 线程池中的线程数
        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(
                nThreads, // 核心线程数
                nThreads, // 最大线程数
                0L, // 空闲时间
                TimeUnit.MILLISECONDS, // 空闲时间单位
                new LinkedBlockingQueue<Runnable>(10000), // 任务队列
                Executors.defaultThreadFactory(), // 线程工厂
                new ThreadPoolExecutor.AbortPolicy() // 拒绝策略
        );

        // 执行任务
        for (int i = 0; i < 10; i++) {
            threadPoolExecutor.execute(() -> {
                System.out.println("Task executed on thread: " + Thread.currentThread().getName());
            });
        }

        // 关闭线程池
        threadPoolExecutor.shutdown();
    }


    public void sleep(int time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
