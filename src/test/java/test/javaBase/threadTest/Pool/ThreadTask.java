package test.javaBase.threadTest.Pool;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.locks.ReentrantLock;

/**
 * @Description：
 * @Author：hh
 * @Date：2024/5/24 下午5:44
 **/
public class ThreadTask implements Runnable{


    //公平锁
    ReentrantLock lock = new ReentrantLock(true);
    volatile int errorCount = 0;

    @Override
    public void run() {
        String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        System.out.println(time+":任务执行了 name："+Thread.currentThread().getName());
        try {
            //请求超时
            System.out.println("请求");
            sleep(1000);
            //转换异常
            System.out.println("转换");
            sleep(1000);

            int error = 1/0;
            //数据库异常  加锁
            lock.lock();
            System.out.println("插入数据库");
            sleep(1000);
            lock.unlock();
        }catch (Exception e){
            System.out.println( ++errorCount);
            throw  new RuntimeException("异常");
        }

    }
    public void sleep(int time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}
