package test.javaBase.threadTest.Pool;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.concurrent.ForkJoinPool;

/**
 * 时间轮算法是一种用于高效处理定时任务的数据结构，特别适用于需要处理大量超时任务的场景。以下是时间轮算法的一些关键点：
 *
 * - **基本概念**: 时间轮是一个环形结构，分为多个槽位（buckets）。每个槽位代表一个时间间隔（例如，1秒）。随着时间的推移，时间轮会不断向前移动，并检查当前槽位中的任务。
 *
 * - **任务管理**: 每个定时任务会被分配到一个合适的槽位中，这个槽位对应于任务需要触发的时间。当时间轮到达该槽位时，检查并执行槽位中的任务。
 *
 * - **多级时间轮**: 为了处理更长的定时任务，可以使用多级时间轮。每一级时间轮的时间间隔都比上一级大，这样可以减少槽位数量，提高空间利用效率。
 *
 * - **优点**:
 *   - 高效性：时间轮通过环形结构和槽位来管理任务，使得定时任务的插入和删除操作都非常高效。
 *   - 空间节约：相比其他定时任务管理方式（如优先队列），时间轮可以更有效地利用空间，尤其是在定时任务的数量很大的情况下。
 *
 * - **缺点**:
 *   - 精确度：时间轮的时间精度取决于时间轮的槽位间隔，如果间隔较大，可能会导致任务执行时间不够精确。
 *   - 复杂性：对于某些复杂的定时任务，时间轮可能需要额外的逻辑来处理，例如任务的重新分配等。
 *
 * - **应用场景**：时间轮算法常用于负载均衡系统、分布式系统、游戏服务器等需要处理大量定时任务的场景。
 *
 * 时间轮算法的核心思想是通过空间换时间，用一个固定大小的数组来维护定时任务，从而实现高效的管理和执行。
 */
public class MultiLevelTimeWheel {
    private final int slots; // 每级时间轮的槽数
    private final long tickDuration; // 每个槽位的时间间隔（单位：毫秒）
    private final Queue<Runnable>[] buckets; // 当前级的槽位队列
    private int currentTick; // 当前级的时间槽
    private MultiLevelTimeWheel nextWheel; // 下一级时间轮
    public MultiLevelTimeWheel(int slots, long tickDuration) {
        this.slots = slots;
        this.tickDuration = tickDuration;
        this.currentTick = 0;
        this.buckets = new Queue[slots];
        for (int i = 0; i < slots; i++) {
            this.buckets[i] = new LinkedList<>();
        }
    }

    public void addTask(Runnable task, long delay) {
        // 计算延迟时间对应的刻度数
        long ticks = delay / tickDuration;
        // 如果刻度数小于轮子的槽数
        if (ticks < slots) {
            // 计算任务应该被添加到的槽位
            int slot = (int) ((currentTick + ticks) % slots);
            // 将任务添加到对应的槽位
            buckets[slot].add(task);
        } else {
            // 如果下一级轮子为空
            if (nextWheel == null) {
                // 创建下一级轮子
                nextWheel = new MultiLevelTimeWheel(slots, tickDuration * slots);
            }
            // 将任务添加到下一级轮子
            nextWheel.addTask(task, delay - ticks * tickDuration);
        }
    }

    public void advanceClock() {
        // 获取当前时间片的任务队列
        Queue<Runnable> currentBucket = buckets[currentTick];
        // 遍历当前时间片的任务队列
        while (!currentBucket.isEmpty()) {
            // 获取任务
            Runnable task = currentBucket.poll();
            // 执行任务
            task.run();
        }
        // 更新当前时间片
        currentTick = (currentTick + 1) % slots;
        // 如果存在下一轮时间轮，则递归调用advanceClock方法
        if (nextWheel != null) {
            nextWheel.advanceClock();
        }
    }

    public static void main(String[] args) throws InterruptedException {
        MultiLevelTimeWheel timeWheel = new MultiLevelTimeWheel(60, 1000); // 60个槽位，每个槽位1秒

        // 添加一些定时任务
        timeWheel.addTask(() -> System.out.println("Task 1 1 executed"), 5000); // 5秒后执行
        timeWheel.addTask(() -> System.out.println("Task 1 2 executed"), 6000); // 5秒后执行
        timeWheel.addTask(() -> System.out.println("Task 1 3 executed"), 7000); // 5秒后执行
        timeWheel.addTask(() -> System.out.println("Task 1 4 executed"), 8000); // 5秒后执行
        timeWheel.addTask(() -> System.out.println("Task 1 5 executed"), 8000); // 5秒后执行
        timeWheel.addTask(() -> System.out.println("Task 2 executed"), 120000); // 120秒后执行

        // 模拟时间流逝
        for (int i = 0; i < 60; i++) {
            timeWheel.advanceClock();
            Thread.sleep(1000); // 每秒前进一个时间槽
        }
    }
}
