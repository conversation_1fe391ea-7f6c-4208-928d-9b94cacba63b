package test.javaBase.threadTest.thread;

import java.io.*;

/**
 * @Description：线程流通信
 * @Author：Administrator
 * @Date：2022/10/13 21:03
 **/
public class ThreadTest {
    public static void main(String[] args) throws IOException, InterruptedException {
        WriteData writeData = new WriteData();
        ReadData readData = new ReadData();

        //　PipedReader和PipedWriter即管道输入流和输出流，可用于线程间管道通信。
        // 它们和PipedInputStream/PipedOutputStream区别是前者操作的是字符后者是字节。
        PipedReader reader = new PipedReader();
        PipedWriter writer = new PipedWriter();
        PipedInputStream pipedInputStream = new PipedInputStream();
        PipedOutputStream pipedOutputStream = new PipedOutputStream();

        pipedOutputStream.connect(pipedInputStream);
        //绑定
        writer.connect(reader);

        new Thread(() -> {
            writeData.writeMethod(writer);
        }).start();


        new Thread(() -> {
            readData.readMethod(reader);
        }).start();
    }

    static class WriteData {
        public void writeMethod(PipedWriter writer) {
            try {
                System.out.println("write :");
             /*   for (int i = 0; i < 300; i++) {
                    String outData = " " + (i + 1);
                    writer.write(outData);
                    System.out.print(outData);
                }*/
                String outData = "hh他666";
                writer.write(outData);
                System.out.print(outData);
                System.out.println();
                writer.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    static class ReadData {
        public void readMethod(PipedReader reader) {
            try {
                System.out.println("read :");
                char[] byteArray = new char[20];
                int readLength = reader.read(byteArray);
                while (readLength != -1) {
                    String newData = new String(byteArray, 0, readLength);
                    System.out.print(newData);
                    readLength = reader.read(byteArray);
                }
                System.out.println();
                reader.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
