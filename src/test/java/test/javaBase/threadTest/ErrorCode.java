package test.javaBase.threadTest;

/**
 * 异常枚举
 */
public enum ErrorCode {
    Is_Null(1001, "缺少参数"),
    ILLEGAL_PARAMETER(2001, "非法参数");
    private final Integer code;
    private final String message;
    ErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    public Integer getCode() {
        return code;
    }
    public String getMessage() {
        return message;
    }
}