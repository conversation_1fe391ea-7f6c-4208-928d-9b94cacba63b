package test.javaBase.collect;

import org.junit.jupiter.api.Test;

import java.util.ArrayList;

/**
 * @Description：
 * @Author：hh
 * @Date：2024/3/7 0:20
 **/
public class ListTest1 {

    @Test
    public void test() {
        ArrayList<Object> list = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            list.add(i);
        }
        list.add(10);
        list.remove(1);
        for (Object o : list) {
            System.out.println(o);
        }

    }
}
