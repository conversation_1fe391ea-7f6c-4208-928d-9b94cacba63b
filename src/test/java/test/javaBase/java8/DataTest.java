package test.javaBase.java8;

import org.junit.Test;

import java.time.*;
import java.time.format.DateTimeFormatter;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2022/11/28 0:10
 **/
public class DataTest {

    @Test
    public void localDate(){
        //默认UTC时区
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime time = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        System.err.println(time.format(dtf));
        //添加
        LocalDateTime time2 = time.plusYears(2);
        System.err.println(time2.format(dtf));
        //减少
        LocalDateTime time3 = time.minusYears(2);
        System.err.println(time3.format(dtf));
        System.err.println(time.getYear()+"年-"+time.getMonthValue()+"月-"+time.getDayOfMonth()+"日"+  time.getHour()+"时");

        //两个时间比较
        System.err.println(time.isBefore(time2));
        System.err.println(time.isAfter(time2));
        //计算间隔
        Period between = Period.between(time.toLocalDate(), time2.toLocalDate());
        System.err.println(between.getYears());
        System.err.println(between.getMonths());

        //指定日期
        LocalDateTime of = LocalDateTime.of(2022, 11, 26, 20, 14, 10);
        System.err.println(of.format(dtf));

        //时间戳
        Instant instant = Instant.now();
        Instant instant2 = time2.toInstant(ZoneOffset.of("+08:00"));
        System.err.println("时间戳-"+instant.toEpochMilli());
        System.err.println("时间戳-"+instant2.toEpochMilli());
        System.err.println(LocalDateTime.ofInstant(instant2, ZoneOffset.UTC).format(dtf));
        //间隔
        Duration durBetween = Duration.between(instant, instant2);
        System.err.println(durBetween.toMillis());


    }
}
