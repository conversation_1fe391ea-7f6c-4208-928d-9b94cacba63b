# consumer - 消费者
Java 8 中的 Consumer 是一个函数接口，它可以接受一个泛型 <T> 类型参数，进行处理后无任何返回值。
- accept(T) 方法是 Consumer 函数式接口的函数方法，传入一个任意类型，无返回值，可以用于 Lambda 表达式和方法引用。
- and<PERSON>hen(Consumer) 方法可以传入一个 Consumer，返回组合了两个 Consumer 后的 Consumer ，传入的 Consumer 不能为 null，否则会得到 NullPointerException。


通俗的来说Consumer相当于一个放东西的容器，你可以在这个容器里放有一个入参的代码块，然后返回T类型，当调用accept(T)方法的时候才会去执行容器里的代码。

# 两个接口 
- Consumer
- BiConsumer

BiConsumer和上面的Consumer非常相似，只不过BiConsumer可以接受两个泛型 <T> 类型参数，进行处理后无任何返回值。

三、其他和Consumer相关的接口
- BiConsumer<T, U> 处理两个参数

- DoubleConsumer 处理一个double类型的参数

- IntConsumer 处理一个int类型的参数

- LongConsumer 处理一个long类型的参数

- ObjIntConsumer 处理两个参数,且第二个参数必须为int类型

- ObjLongConsumer 处理两个参数,且第二个参数必须为long类型
