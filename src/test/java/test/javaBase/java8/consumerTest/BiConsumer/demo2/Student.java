package test.javaBase.java8.consumerTest.BiConsumer.demo2;

import java.util.List;

public class Student  {
    public String name;
    public int score;
    public List<Integer> sc;

    public Student (String name ,int score,List<Integer> sc){
        this.name=name;
        this.score=score;
        this.sc=sc;
    }

    public Student (){}

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    public List<Integer> getSc() {
        return sc;
    }

    public void setSc(List<Integer> sc) {
        this.sc = sc;
    }

    @Override
    public String toString() {
        return "Student{" +
                "name='" + name + '\'' +
                ", score=" + score +
                ", sc=" + sc +
                '}';
    }
}
