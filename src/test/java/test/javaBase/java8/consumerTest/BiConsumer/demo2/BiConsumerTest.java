package test.javaBase.java8.consumerTest.BiConsumer.demo2;

import java.util.function.BiConsumer;
import static java.util.Objects.requireNonNull;

public interface BiConsumerTest extends BiConsumer<Student, String> {
    @Override
    default BiConsumerTest andThen(final BiConsumer<? super Student, ? super String> after) {
        requireNonNull(after, "after");
        return (l, r) -> {
            accept(l, r);
            after.accept(l, r);
        };
    }
}
