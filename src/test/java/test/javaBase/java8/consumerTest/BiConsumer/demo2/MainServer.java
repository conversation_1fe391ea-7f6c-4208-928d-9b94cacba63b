package test.javaBase.java8.consumerTest.BiConsumer.demo2;

import java.util.*;

public class MainServer {
    public static void main(String[] args) throws Exception {
        Student student1 = new Student("张三", 85, Arrays.asList(95, 87));
        BiConsumerTest biConsumerTest1 = myBiConsumerTest1("姓名-");
        BiConsumerTest biConsumerTest2 = myBiConsumerTest2("成绩-");
        biConsumerTest1.andThen(biConsumerTest2).accept(student1, "序号1:");
    }

    static BiConsumerTest myBiConsumerTest1(String str) {
        //(student, number) -> System.out.println(number + str + student.name)相当于accept方法
        return (student, number) -> System.out.println(number + str + student.name);
    }

    static BiConsumerTest myBiConsumerTest2(String str) {
        //(student, number) -> System.out.println(number + student.name + str + student.score);相当于accept方法
        return (student, number) -> System.out.println(number + student.name + str + student.score);
    }
}
