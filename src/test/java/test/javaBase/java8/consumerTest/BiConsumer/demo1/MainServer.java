package test.javaBase.java8.consumerTest.BiConsumer.demo1;

import java.util.*;
import java.util.function.BiConsumer;
public class MainServer {
    public static void main(String[] args) throws Exception {
        List<Integer> lista = new ArrayList<>();
        lista.add(2);
        lista.add(1);
        lista.add(3);

        List<Integer> listb = new ArrayList<>();
        listb.add(2);
        listb.add(1);
        listb.add(2);

        // 第一个BiConsumer
        BiConsumer<List<Integer>, List<Integer>> equals = (list1, list2) -> {
            if (list1.size() != list2.size()) {
                System.out.println("False");
            } else {
                for (int i = 0; i < list1.size(); i++){
                    if (list1.get(i) != list2.get(i)) {
                        System.out.println("False");
                        return;
                    }
                }
                System.out.println("True");
            }
        };
        // 第二个BiConsumer
        BiConsumer<List<Integer>, List<Integer> > disp = (list1, list2) -> {
            list1.stream().forEach(a -> System.out.print(a + " "));
            System.out.println();
            list2.stream().forEach(a -> System.out.print(a + " "));
            System.out.println();
        };
        System.out.println("开始执行：");
        // 先执行equals再执行equals，接收的参数是lista和listb
        equals.andThen(disp).accept(lista, listb);
    }
 }   
