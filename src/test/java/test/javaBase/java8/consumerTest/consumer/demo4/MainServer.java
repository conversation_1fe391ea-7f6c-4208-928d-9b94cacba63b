package test.javaBase.java8.consumerTest.consumer.demo4;


import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

public class MainServer {
    public static void main(String[] args) {
        List<Student> students = Arrays.asList(
                new Student( "<PERSON>",5),
                new Student("<PERSON>",2),
                new Student("<PERSON>",3)
        );

        Consumer<Student> raiser = e -> {e.score = e.score * 2;};
        raiseStudents(students, System.out::println);
        System.out.println("开始andThen");
        raiseStudents(students, raiser.andThen(System.out::println));
    }

    private static void raiseStudents(List<Student> employees, Consumer<Student> fx) {
        for (Student e : employees) {
            fx.accept(e);
        }
    }
}
