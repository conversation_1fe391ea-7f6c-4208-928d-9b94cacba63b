package test.javaBase.java8.consumerTest.consumer.demo3;

import com.alibaba.fastjson.JSON;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Stream;

public class MainServer {
    public static void main(String[] args) {
        List<Student> lisiList = new ArrayList<>();
        Consumer <Student> consumer  = x -> {
            if (x.name.equals("aa")){
                lisiList.add(x);
            }
        };

        consumer = consumer.andThen(
                x -> lisiList.removeIf(y -> y.score > 90)
        );
        Stream.of(
                new  Student("aa",99),
                new  Student("bb",98),
                new  Student("cc",85),
                new  Student("dd",69),
                new  Student("ee",77),
                new  Student("ff",83),
                new  Student("aa",82)
        ).forEach(consumer);

        System.out.println(JSON.toJSONString(lisiList));
        }
    }
