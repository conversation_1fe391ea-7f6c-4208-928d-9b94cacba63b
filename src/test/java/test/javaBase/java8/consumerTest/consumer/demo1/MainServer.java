package test.javaBase.java8.consumerTest.consumer.demo1;

import java.util.function.Consumer;

/**
 * Java 8 中的 Consumer 是一个函数接口，它可以接受一个泛型 <T> 类型参数，进行处理后无任何返回值。
 * - accept(T) 方法是 Consumer 函数式接口的函数方法，传入一个任意类型，无返回值，可以用于 Lambda 表达式和方法引用。
 * - andThen(Consumer) 方法可以传入一个 Consumer，返回组合了两个 Consumer 后的 Consumer ，传入的 Consumer 不能为 null，
 * 否则会得到 NullPointerException。
 *
 * 例如传入一个字符串，打印一个字符串。
 *
 * 通俗的来说Consumer相当于一个放东西的容器，你可以在这个容器里放有一个入参的代码块，然后返回T类型，当调用accept(T)方法的时候才会去执行容器里的代码。

 */
public class MainServer {
    public static void main(String[] args) throws InterruptedException {
        Consumer<String> first = x -> System.out.println("1."+x.toLowerCase());
        Consumer<String> second = y -> System.out.println("2." + y);
        System.out.println("开始");
        Consumer<String> result = first.andThen(second).andThen(first);
        result.accept("A");
    }
}
