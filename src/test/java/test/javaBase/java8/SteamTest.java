package test.javaBase.java8;

import org.junit.Test;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.BinaryOperator;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class SteamTest {
    static ArrayList<HashMap<String,Object>> list;
    static {
        list = new ArrayList<>();
        for (int i = 1; i <= 5; i++) {
            HashMap map = new HashMap();
            map.put("id",i);
            map.put("name","阿蛋"+i);
            double random = Math.random()*100;
            BigDecimal bd = new BigDecimal(random).setScale(4,BigDecimal.ROUND_HALF_UP);
            map.put("age",bd);
            list.add(map);
        }
    }

    @Test
    public void optionalTest(){
        Optional<HashMap<String, Object>> first = list.stream().filter(item -> Integer.valueOf(item.get("id").toString()) > 3).findAny();
        System.out.println(first);
        HashSet<HashMap<String, Object>> setCollect = list.stream().collect(Collectors.toCollection(HashSet::new));
        System.out.println(setCollect);
    }
    @Test
    public void reduceTest(){
        BigDecimal age = list.stream().map(x -> new BigDecimal(x.get("age").toString()))
                .reduce(BigDecimal.valueOf(0), BigDecimal::add);
        System.out.println(age);
        BigDecimal age2 = list.stream().map(x -> new BigDecimal(x.get("age").toString()))
                .reduce(BigDecimal.valueOf(1000000), BigDecimal::add);
        System.out.println(age2);

        ArrayList<Object> list2 = new ArrayList<>();
        Stream.of(1,2,3,4,5).reduce(list2,(x,y)->{
            System.out.println("x"+x);
            System.out.println("y" + y);
            return x;
        },(x,y)->y);
        System.out.println(list2);
    }
    @Test
    public void test2() {
        int a = 128;
        int b = 129;
        System.out.println(a|=b);
        ArrayList<Integer> accResult_ = Stream.of(1, 2, 3, 4)
                //第一个参数，初始值为ArrayList
                .reduce(new ArrayList<Integer>(),
                        //第二个参数，实现了BiFunction函数式接口中apply方法，并且打印BiFunction
                        new BiFunction<ArrayList<Integer>, Integer, ArrayList<Integer>>() {
                            @Override
                            public ArrayList<Integer> apply(ArrayList<Integer> acc, Integer item) {

                                acc.add(item);
                                System.out.println("item: " + item);
                                System.out.println("acc+ : " + acc);
                                System.out.println("BiFunction");
                                return acc;
                            }
                            //第三个参数---参数的数据类型必须为返回数据类型，改参数主要用于合并多个线程的result值
                            // （Stream是支持并发操作的，为了避免竞争，对于redu                                                                                                                                                        ce线程都会有独立的result）
                        }, new BinaryOperator<ArrayList<Integer>>() {
                            @Override
                            public ArrayList<Integer> apply(ArrayList<Integer> acc, ArrayList<Integer> item) {
                                System.out.println("BinaryOperator");
                                acc.addAll(item);
                                System.out.println("item: " + item);
                                System.out.println("acc+ : " + acc);
                                System.out.println("--------");
                                return acc;
                            }
                        });
        System.out.println("accResult_: " + accResult_);

        System.out.println("------------------lambda优化代码-----------------");

        ArrayList<Integer> newList = new ArrayList<>();

        ArrayList<Integer> accResult_s = Stream.of(1,2,3,4)
                .reduce(newList,
                        (acc, item) -> {
                            acc.add(item);
                            System.out.println("item: " + item);
                            System.out.println("acc+ : " + acc);
                            System.out.println("BiFunction");
                            return acc;
                        }, (acc, item) -> null);
        System.out.println("accResult_s: " + accResult_s);
    }

    @Test
    public void stream(){
        BigDecimal bigDecimal = new BigDecimal(0);
        System.out.println(list.toString());
        double sum = list.stream().mapToDouble(map ->Double.parseDouble(map.get("age").toString())).sum();
//        list.stream().forEach(map -> {
//            map.put("age", new BigDecimal( map.get("age").toString()).setScale(3,BigDecimal.ROUND_HALF_UP));
//
//        });
        System.out.println(list.toString());
        System.out.println(sum);
        for (HashMap map : list) {
            bigDecimal =bigDecimal.add(new BigDecimal(map.get("age").toString())).setScale(4,BigDecimal.ROUND_HALF_UP);
        }
        System.out.println(bigDecimal);
    }

    @Test
    public void steamTest(){
        List<Stu> list = new ArrayList<>();
        for (int i = 1; i <= 50; i++) {
            Stu stu = new Stu(i,"小明"+i);

            list.add(stu);
        }
        Stu stu = new Stu(1, "小明1");
        list.add(stu);
        list.stream().distinct().limit(5).sorted(Comparator.comparing(Stu::getId,(a,b)->a>b?-1:1)).forEach(item->{
            System.out.println( item.getName());
        });
        Map<String, List<Stu>> collect = list.stream().collect(Collectors.groupingBy(Stu::getName));
        Integer number = 1;
        Integer number2 = 2;
        System.out.println(number.compareTo(number2));
        System.out.println(Integer.compare(number,number2));

    }

}
