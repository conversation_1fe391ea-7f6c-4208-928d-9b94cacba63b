package test.javaBase.java8;

import java.util.Objects;

public class Stu{
    public int id;
    public String name;
        Stu(){

        }
        Stu(int id,String name){
            this.id = id;
            this.name = name;
        }

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            Stu stu = (Stu) o;
            return id == stu.id &&
                    Objects.equals(name, stu.name);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id, name);
        }
    }