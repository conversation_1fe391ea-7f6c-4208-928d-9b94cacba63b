package test.javaBase.abstractTest;

import org.junit.Test;

public  class AbstractTest {


    public abstract class MyAbstract{
        abstract void eat();
        void test(Object object){

            System.out.println("test:"+object);
        }

    }
    public class Dog extends MyAbstract{

        @Override
        void eat() {
            System.out.println("dog eat");
        }
    }

    @Test
    public void mainTest(){
        MyAbstract dog = new Dog();
        dog.test(dog);
    }

}
