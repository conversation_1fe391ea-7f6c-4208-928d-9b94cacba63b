package test.javaBase.instfaceTest;

import org.junit.Test;

public class InterfaceTest {

    public interface MyInterface{
        void eat();


    }

    public class Dog implements MyInterface{

        @Override
        public void eat() {
            System.out.println("dog eat");
        }
    }
    public class Cat implements MyInterface{

        @Override
        public void eat() {
            System.out.println("cat eat");
        }
    }

    MyInterface myInterface;
    @Test
    public void eatMain(){
        myInterface = new Dog();
        myInterface.eat();
        System.out.println(myInterface);
        myInterface = new Cat();
        myInterface.eat();
        System.out.println(myInterface);
    }
}
