package test.javaBase.genericTest;

import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description：泛型 上下边界
 * @Author：HH
 * @Date：2023/6/8 10:05
 **/
public class GenericTest {
    ArrayList<Cat> cats = new ArrayList<Cat>(){{
        add(new Cat());add(new Cat());
    }};
    ArrayList<RedCat> redCats = new ArrayList<RedCat>(){{
        add(new RedCat());add(new RedCat());
    }};
    ArrayList<BlackCat> blackCats = new ArrayList<BlackCat>(){{
        add(new BlackCat());
    }};
    ArrayList<Animal> animals = new ArrayList<Animal>(){{
        add(new Animal());
    }};

    @Test
    public void upTest(){
        up(cats);
        up(redCats);
        up(blackCats);
        /*
        //报错 -》超出上界区间的类型
        up(animals);
         */

    }

    /**
     * 上边界
     * 限制泛型 Cat为上限 泛型类型 只能传入他的子类
     * 也就是[其子类,cat]这个区间  能取出的数据类型是由上界决定的
     * add() 添加类型是由下界决定（其子类） 但编译器无法确定其最子类（可能有多个子类）所以无法add（）/set()
     * */
    public static void up(List<? extends Cat> list){
        for (int i = 0; i < list.size(); i++) {
            Cat cat = list.get(i);
            cat.miaoMiao();
            cat.eat();
            /*
            //报错 -》无法取出超出上界的类型
            RedCat redCat= list.get(i);*/
        }
    }


    @Test
    public void bottomTest(){
        bottom(cats);
        bottom(animals);
        up(cats);
        //报错 ->超出下界区间的类型
//        bottom(redCats);


    }


    /**
     * 下边界
     * Cat为下限 泛型中的类型只能是Cat的父类..至Object
     * 数据类型区间[Cat,Object] java是采用单继承 而Object为所以类的最最最父类
     * 所以 上界和下界是确定的 取出类型由上界决定(OBJ) 添加类型是由下界决定（Cat）进而[Cat,Object]区间的类型都可以add()
     */
    public static void bottom(List<? super Cat> list){
        list.add(new Cat());
        list.add(new RedCat());
        list.add(new BlackCat());
        //报错-》无法添加超出下界的类型
        //list.add(new Animal());
        for (int i = 0; i < list.size(); i++) {
            Object object = list.get(i);
            System.out.println(object);
          /*
          //都报错 只能取出为OBJ 也就是上界
           Cat object1 = list.get(i);
            Animal object2 = list.get(i);
            */
        }
    }
    /**
     * 总结：取出数据就用extends 添加或set数据就用super
     */

}
