package test.javaBase.innerClass;

// 3 可以实现多重继承 而实现多重继承 接口就必须实现它里面的所有方法。而有了内部类就不一样了。它可以使我们的类继承多个具体类或抽象类
public class ExampleMain {
   /**
    * 内部类1继承ExampleOne
    */
   private class InnerOne extends ExampleMain {
       public String name() {
           return super.name();
       }
   }
   /**
    * 内部类2继承ExampleTwo
    */
   private class InnerTwo extends ExampleTwo {
       public int age() {
           return super.age();
       }
   }

   public String name() {
       return new InnerOne().name();
   }
   public int age() {
       return new InnerTwo().age();
   }
   public static void main(String[] args) {
       ExampleMain mi = new ExampleMain();
       System.out.println("姓名:" + mi.name());
       System.out.println("年龄:" + mi.age());
   }
}
