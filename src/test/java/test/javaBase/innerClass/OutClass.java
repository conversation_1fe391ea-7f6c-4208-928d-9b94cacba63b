package test.javaBase.innerClass;

/**
 * @Description： 内部类
 * @Author：Administrator
 * @Date：2022/10/28 16:19
 **/


public class OutClass {

    private String data = "外部类数据";


    // 2 普通的类不能使用 private protected 访问权限符来修饰的，而内部类则可以使用 private 和 protected 来修饰。
    private class InnerClass implements InnerInterface{
        // 无法使用 static 除非为静态内部类
        public InnerClass() {
            //1 可访问 外部类资源
            System.out.println(data);
        }

        //2.1 当内部类实现某个接口的时候，在进行向上转型，对外部来说，就完全隐藏了接口的实现了
        @Override
        public void innerMethod() {
            System.out.println("实现内部类隐藏");
        }
    }
/*    public void getInner() {
        new InnerClass();
    }*/
    public InnerInterface getInner() {
        return new InnerClass();
    }

    public static void main(String[] args) {
        OutClass outClass = new OutClass();
        InnerInterface inner = outClass.getInner();
        inner.innerMethod();
    }
}
