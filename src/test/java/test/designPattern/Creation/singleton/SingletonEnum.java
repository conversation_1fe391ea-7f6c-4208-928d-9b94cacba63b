package test.designPattern.Creation.singleton;

/**
 * Created by ji<PERSON><PERSON> on 2016/10/28.
 * 8. 枚举[极推荐使用]
 *
 * 这里SingletonEnum.instance
 * 这里的instance即为SingletonEnum类型的引用所以得到它就可以调用枚举中的方法了。
 借助JDK1.5中添加的枚举来实现单例模式。不仅能避免多线程同步问题，而且还能防止反序列化重新创建新的对象
 */

/**
 * 过关键字enum创建枚举类型在编译后生成一个继承自java.lang.Enum类的类和另外一个类，
 * 其中Enum是抽象类，编译器还为我们生成了两个静态方法，分别是values()和 valueOf()，values()方法的作用
 * 就是获取枚举类中的所有变量，并作为数组返回，而valueOf(String name)方法与Enum类中的valueOf方法的作用类似根据名称获取枚举变量，
 * 上述两个方法需枚举实例向上转型为Enum。
 *
 * 使用枚举单例的写法，我们完全不用考虑序列化和反射的问题。枚举序列化是由JVM保证的，
 * 每一个枚举类型和定义的枚举变量在JVM中都是唯一的，在枚举类型的序列化和反序列化上，Java做了特殊的规定：
 * 在序列化时Java仅仅是将枚举对象的name属性输出到结果中，反序列化的时候则是通过java.lang.Enum的valueOf方法来根据名字查找枚举对象。
 * 同时，编译器是不允许任何对这种序列化机制的定制的并禁用了writeObject、readObject、readObjectNoData、writeReplace
 * 和readResolve等方法，从而保证了枚举实例的唯一性。
 *
 * 优点：线程安全，支持序列化机制，实现单例模式最佳做法（少数使用）
 *
 * 缺点：未实现懒加载
 */

public enum SingletonEnum {

    instance;
    private String name;
    public String getName(){
        return name;
    }
    public void setName(String name){
        this.name = name;
    }

}