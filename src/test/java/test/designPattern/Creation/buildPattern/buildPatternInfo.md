# Builder Pattern 建造者模式
## 建造者模式适用场景 :
- 当创造一个对象 需要很多步骤时 , 适合使用建造者模式 ;
- 当创造一个对象 只需要一个简单的方法就可以完成 , 适合使用工厂模式 ;
- --
- 结构复杂 : 对象 有 非常复杂的内部结构 , 有很多属性 ;
- 分离创建和使用 : 想把 复杂对象 的 创建 和 使用 分离 ;
### 建造者（Builder）模式的主要角色如下。
- 1、产品角色（Product）：它是包含多个组成部件的复杂对象，由具体建造者来创建其各个零部件。
- 2、抽象建造者（Builder）：它是一个包含创建产品各个子部件的抽象方法的接口，通常还包含一个返回复杂产品的方法 getResult()。
- 3、具体建造者(Concrete Builder）：实现 Builder 接口，完成复杂产品的各个部件的具体创建方法。
- 4、指挥者（Director）：它调用建造者对象中的部件构造与装配方法完成复杂对象的创建，在指挥者中不涉及具体产品的信息。

## 优点：
- 建造者独立，易扩展。
- 便于控制细节风险。
## 缺点：
- 产品必须有共同点，范围有限制。
- 如内部变化复杂，会有很多的建造类。
四个角色
- Product（产品角色）：一个具体的产品对象
- Builder（抽象建造者）：创建一个Product对象的各个部件指定的接口/抽象类
- ConcreteBuilder（具体建造者）：实现接口，构建和装配各个部件
- Director（指挥者）：构建一个使用Builder的对象，它主要是用于创建一个复杂的对象，它主要有两个作用，一是：隔离了客户与对象的生产过程，二是：负责控制产品对象的生产过程

# Builder 固定建造步骤
- 优点：好理解 简单易操作
- 缺点：设计的程序结构过于简单，没有设计缓存层对象，程序的扩展和维护不好，也就是说，这种设计方案，把产品（即：房子）和创建产品的过程（房子创建流程）封装在了一起，耦合性更强

# BuilderUp 自定义建造顺序 更灵活
- 将建造顺序交给客户端