package test.designPattern.Creation.buildPattern.BuilderUp;

//ConcreteBuilder（具体建造者）
//构建房子的各步的具体实现
public class Worker extends Builder {

    House house;

    public Worker(){
        house = new House();
    }

    @Override
    public Builder buildBasic(String material) {
        house.setBuildBasic(material);
       return this;
    }

    @Override
    public Builder buildWalls(String material ) {
        house.setBuildWalls(material);
        return this;
    }

    @Override
    public Builder roofed(String material) {
        house.setRoofed(material);
        return this;
    }

    @Override
    House getHouse() {
        //默认材料
        house.setBuildBasic(house.getBuildBasic()==null?"泥土":house.getBuildBasic());
        house.setBuildWalls(house.getBuildWalls()==null?"泥土":house.getBuildWalls());
        house.setRoofed(house.getRoofed()==null?"泥土":house.getRoofed());
        return house;
    }

}

