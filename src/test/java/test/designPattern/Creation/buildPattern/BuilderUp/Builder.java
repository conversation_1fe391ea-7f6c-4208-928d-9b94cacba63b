package test.designPattern.Creation.buildPattern.BuilderUp;

//AbstractBuilder 抽象缔造者 定义接口
//建房子需要做的事
public abstract class Builder {

    //直接返回建造者 将步骤顺序直接交给客户端
    //可指定材料

    //打地基
    public abstract Builder buildBasic(String material);
    //砌墙
    public abstract Builder buildWalls(String material);
    //封顶
    public abstract Builder roofed(String material);

    //成品 获取产品
    abstract House getHouse();
}
