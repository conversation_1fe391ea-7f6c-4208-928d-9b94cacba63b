package test.designPattern.Creation.buildPattern.Builder;

//ConcreteBuilder（具体建造者）
//构建房子的各步的具体实现
public class Worker implements Builder {

    House house;

    public Worker(){
        house = new House();
    }
    @Override
    public void buildBasic() {
        house.setBuildBasic("地基");
        System.out.println("地基");
    }

    @Override
    public void buildWalls() {
        house.setBuildWalls("砌砖");
        System.out.println("砌砖");
    }

    @Override
    public void roofed() {
        house.setRoofed("封顶");
        System.out.println("封顶");
    }

    @Override
    public House getHouse() {
        return this.house;
    }
}

