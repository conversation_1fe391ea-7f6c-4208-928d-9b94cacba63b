package test.designPattern.Creation.buildPattern.Builder;

//具体产品房子
public class House {

    String buildBasic;
    String buildWalls;
    String roofed;

    public String getBuildBasic() {
        return buildBasic;
    }

    public void setBuildBasic(String buildBasic) {
        this.buildBasic = buildBasic;
    }

    public String getBuildWalls() {
        return buildWalls;
    }

    public void setBuildWalls(String buildWalls) {
        this.buildWalls = buildWalls;
    }

    public String getRoofed() {
        return roofed;
    }

    public void setRoofed(String roofed) {
        this.roofed = roofed;
    }
}
