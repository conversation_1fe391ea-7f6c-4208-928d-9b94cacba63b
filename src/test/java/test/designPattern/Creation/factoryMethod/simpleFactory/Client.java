package test.designPattern.Creation.factoryMethod.simpleFactory;

/**
 * 简单工厂模式也叫静态工厂模式，就是工厂类一般是使用静态方法，通过接收的参数的不同来返回不同的对象实例。
 * 对于增加新产品无能为力！不修改代码的话，是无法扩展的。
 */
public class Client {   //调用者
	public static void main(String[] args) {
		Car c1 =CarFactory.createCar("奥迪");
		Car c2 = CarFactory.createCar("比亚迪");
		c1.run();
		c2.run();
		System.out.println("===================");
		Car audi = CarFactory2.createAudi();
		Car byd = CarFactory2.createByd();
		audi.run();
		byd.run();
	}
}
