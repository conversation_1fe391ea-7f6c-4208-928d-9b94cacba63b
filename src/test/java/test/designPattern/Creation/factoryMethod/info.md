# factoryPattern 工厂模式
特点：

- 提供一种创建对象的最佳方式，在创建对象时不提供对外暴露创建逻辑，并且通过一个共同的接口来指向新创建的对象

- 定义一个创建对象的接口，让子类来决定实例化哪一个具体的工厂类，延迟到子类去执行

- 主要解决选择接口的问题

- 扩展性高，只增加相应工厂类即可，知道名称即可创建对象，屏蔽具体的实现，调用者只关心接口

- 增加需求时，需要增加具体类与工厂实现，导致类个数成倍增加，增加系统复杂度

- 只有需要生成复杂类对象时才需要使用工厂模式，且简单工厂模式不属于23种设计模式

分为
- simpleFactory 简单工厂
- factoryMethods 方法工厂
- abstractFactory 抽象工厂

- 简单工厂模式(静态工厂模式)
虽然某种程度不符合设计原则，但实际使用最多。
- 工厂方法模式
不修改已有类的前提下，通过增加新的工厂类实现扩展。
- 抽象工厂模式
不可以增加产品，可以增加产品族！