package test.designPattern.Creation.factoryMethod.factoryMethods;

/**
 * 结构复杂度
 *   从这个角度比较，显然简单工厂模式要占优。简单工厂模式只需一个工厂类，而工厂方法模式的工厂类随着产品类个数增加而增加，
 * 这无疑会使类的个数越来越多，从而增加了结构的复杂程度。
 *
 * 代码复杂度
 *   代码复杂度和结构复杂度是一对矛盾，既然简单工厂模式在结构方面相对简洁，那么它在代码方面肯定是比工厂方法模式复杂的了。
 * 简单工厂模式的工厂类随着产品类的增加需要增加很多方法（或代码），而工厂方法模式每个具体工厂类只完成单一任务，代码简洁。
 *
 * 客户端编程难度
 *   工厂方法模式虽然在工厂类结构中引入了接口从而满足了OCP，但是在客户端编码中需要对工厂类进行实例化。
 * 而简单工厂模式的工厂类是个静态类，在客户端无需实例化，这无疑是个吸引人的优点。
 *
 * 管理上的难度
 * 这是个关键的问题。
 * 我们先谈扩展。众所周知，工厂方法模式完全满足OCP，即它有非常良好的扩展性。那是否就说明了简单工厂模式就没有扩展性呢？答案是否定的。
 * 简单工厂模式同样具备良好的扩展性——扩展的时候仅需要修改少量的代码（修改工
 * 厂类的代码）就可以满足扩展性的要求了。尽管这没有完全满足OCP，但我们不需要太拘泥于设计理论，要知道，
 * sun提供的java官方工具包中也有想到多没有满足OCP的例子啊。然后我们从维护性的角度分析下。假如某个具体产品类需要进行一定的修改，
 * 很可能需要修改对应的工厂类。当同时需要修改多个产品类的时候，对工厂类的修改会变得相当麻烦（对号入座已经是个问题了）。
 * 反而简单工厂没有这些麻烦，当多个产品类需要修改是，简单工厂模式仍然仅仅需要修改唯一的工厂类（无论怎样都能改到满足要求吧？大不了把这个类重写）。
 *
 * 根据设计理论建议：工厂方法模式。但实际上，我们一般都用简单工厂模式。

 */
public class Client {
	public static void main(String[] args) {
		Car c1 = new AudiFactory().createCar();
		Car c2 = new BydFactory().createCar();
		
		c1.run();
		c2.run();
	}
}
