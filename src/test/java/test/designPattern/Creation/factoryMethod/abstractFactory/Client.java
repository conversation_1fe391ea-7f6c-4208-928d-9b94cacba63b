package test.designPattern.Creation.factoryMethod.abstractFactory;

/**
 * @Description：客户端
 * @Author：Administrator
 * @Date：2022/10/29 20:41
 **/
public class Client {

    public static void main(String[] args) {

        //小米的产品 创建对应的工厂 即可生产其对应的产品
        ProductFactory xiaomiFactory=new XiaomiFactory();
        //小米手机
        Phone xiaomiPhone = xiaomiFactory.createPhone();
        xiaomiPhone.run();
        //小米路由
        Route xiaomiRoute = xiaomiFactory.createRoute();
        xiaomiRoute.wife();

        //华为的产品 创建对应的工厂 即可生产其对应的产品
        ProductFactory huaweiFactory=new HuaweiFactory();
        //华为手机
        Phone huaweiPhone = huaweiFactory.createPhone();
        huaweiPhone.run();
        //华为路由
        Route huaweiRoute = huaweiFactory.createRoute();
        huaweiRoute.wife();


    }
}
