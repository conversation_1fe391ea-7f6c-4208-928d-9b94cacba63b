package test.designPattern.Structural.Composite;

/**
 * @Description：Composite Pattern
 * @Author：hh
 * @Date：2023/11/6 8:28
 **/
public class Client {
    public static void main(String[] args) {
        // Example usage
        Customer customer = new Customer("John");
        TakeOrderCommand command = new TakeOrderCommand(new Order() {
            @Override
            public void placeOrder() {
                System.out.println("Placing omelette...");
            }
        });
        customer.takeOrder(command);
    }
}
