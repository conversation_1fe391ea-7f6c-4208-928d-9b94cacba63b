https://blog.csdn.net/qq_29917503/article/details/126608758
# 简介
组合多个对象形成树形结构以表示“整体-部分”的关系的层次结构。组合模式对叶子节点和容器节点的处理具有一致性，又称为整体-部分模式。

#角色职责
* 抽象构件（Component）角色：它的主要作用是为树叶构件和树枝构件声明公共接口，并实现它们的默认行为。在透明式的组合模式中抽象构件还声明访问和管理子类的接口；在安全式的组合模式中不声明访问和管理子类的接口，管理工作由树枝构件完成。（总的抽象类或接口，定义一些通用的方法，比如新增、删除）。

* 树枝构件（Composite）角色 / 中间构件：是组合中的分支节点对象，它有子节点，用于继承和实现抽象构件。它的主要作用是存储和管理子部件，通常包含 Add()、Remove()、GetChild() 等方法。

* 树叶构件（Leaf）角色：是组合中的叶节点对象，它没有子节点，用于继承或实现抽象构件。


# 应用场景
部分、整体场景，如树形菜单，文件、文件夹的管理。

# 组合模式优缺点
## 优点

* 高层模块调用简单：组合模式使得客户端代码可以一致地处理单个对象和组合对象，无须关心自己处理的是单个对象，还是组合对象，这简化了客户端代码。

* 节点自由增加：更容易在组合体内加入新的对象，客户端不会因为加入了新的对象而更改源代码。

## 缺点

* 在使用组合模式时，其叶子和树枝的声明都是实现类，而不是接口，违反了依赖倒置原则。

* 设计较复杂，客户端需要花更多时间理清类之间的层次关系。

* 不容易限制容器中的构件。
