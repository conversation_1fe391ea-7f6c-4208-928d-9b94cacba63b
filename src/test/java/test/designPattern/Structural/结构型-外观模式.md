## 概述
外观（Facade）模式又叫作门面模式，是一种通过为多个复杂的子系统提供一个一致的接口，而使这些子系统更加容易被访问的模式。该模式对外有一个统一接口，外部应用程序不用关心内部子系统的具体细节，这样会大大降低应用程序的复杂度，提高了程序的可维护性。

外观模式是为了解决类与类之家的依赖关系的，像spring一样，可以将类和类之间的关系配置到配置文件中，而外观模式就是将他们的关系放在一个Facade类中，降低了类类之间的耦合度，该模式中没有涉及到接口，看下类图：（我们以一个计算机的启动过程为例）

## 模式的结构
* 外观（Facade）角色：为多个子系统对外提供一个共同的接口。
* 子系统（Sub System）角色：实现系统的部分功能，客户可以通过外观角色访问它。
* 客户（Client）角色：通过一个外观角色访问各个子系统的功能。

## 外观模式的特点
外观（Facade）模式是“迪米特法则”的典型应用，
####它有以下主要优点：
降低了子系统与客户端之间的耦合度，使得子系统的变化不会影响调用它的客户类。
对客户屏蔽了子系统组件，减少了客户处理的对象数目，并使得子系统使用起来更加容易。
降低了大型软件系统中的编译依赖性，简化了系统在不同平台之间的移植过程，因为编译一个子系统不会影响其他的子系统，也不会影响外观对象。
####外观（Facade）模式的主要缺点如下：
不能很好地限制客户使用子系统类，很容易带来未知风险。
增加新的子系统可能需要修改外观类或客户端的源代码，违背了“开闭原则”。
## 外观模式的应用场景
通常在以下情况下可以考虑使用外观模式。

对分层结构系统构建时，使用外观模式定义子系统中每层的入口点可以简化子系统之间的依赖关系。
当一个复杂系统的子系统很多时，外观模式可以为系统设计一个简单的接口供外界访问。
当客户端与多个子系统之间存在很大的联系时，引入外观模式可将它们分离，从而提高子系统的独立性和可移植性。
外观模式的结构与实现
外观（Facade）模式的结构比较简单，主要是定义了一个高层接口。

它包含了对各个子系统的引用，客户端可以通过它访问各个子系统的功能。现在来分析其基本结构和实现方法。


模式的实现
####————————————————
版权声明：本文为CSDN博主「Java技术债务」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
原文链接：https://blog.csdn.net/qq_40124555/article/details/125882033
###---- CPU
    public class CPU {
 	
        public void startup(){
            System.out.println("cpu startup!");
        }
        
        public void shutdown(){
            System.out.println("cpu shutdown!");
        }
    }
 ####-------Memory
    public class Memory {
 	
        public void startup(){
            System.out.println("memory startup!");
        }
        
        public void shutdown(){
            System.out.println("memory shutdown!");
        }
    }
  ####-------Disk
    public class Disk {
 	
        public void startup(){
            System.out.println("disk startup!");
        }
        
        public void shutdown(){
            System.out.println("disk shutdown!");
        }
    }
  ####-------Computer
    public class Computer {
        private CPU cpu;
        private Memory memory;
        private Disk disk;
        
        public Computer(){
            cpu = new CPU();
            memory = new Memory();
            disk = new Disk();
        }
 	
        public void startup(){
            System.out.println("start the computer!");
            cpu.startup();
            memory.startup();
            disk.startup();
            System.out.println("start computer finished!");
        }
        
        public void shutdown(){
            System.out.println("begin to close the computer!");
            cpu.shutdown();
            memory.shutdown();
            disk.shutdown();
            System.out.println("computer closed!");
        }
    }
 
 ####-------  测试
     public class boot.test.modules.user.bean.User {
      
        public static void main(String[] args) {
            Computer computer = new Computer();
            computer.startup();
            computer.shutdown();
        }
     }
