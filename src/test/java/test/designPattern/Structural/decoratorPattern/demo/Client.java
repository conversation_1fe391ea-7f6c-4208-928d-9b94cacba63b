package test.designPattern.Structural.decoratorPattern.demo;

import java.io.*;

public class Client {
    public static void main(String[] args) throws FileNotFoundException {
        Car car=new ICar();
        car.move();
        System.out.println("！！！！！！！");
        FlyCar flyCar=new FlyCar(car);
        flyCar.move();
        WaterCar waterCar=new WaterCar(new FlyCar(car));
        waterCar.move();

 /*       InputStream is = new BufferedInputStream((new FileInputStream(new File("asd"))));*/

    }
}
