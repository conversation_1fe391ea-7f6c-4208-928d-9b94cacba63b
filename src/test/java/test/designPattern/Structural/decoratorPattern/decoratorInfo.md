# 一，什么是装饰模式
动态的为一个类增加新的功能，装饰模式是一种用于代替继承技术的模式，无需通过继承增加子类就能够灵活的扩展新功能。

# 二、角色职责
- **Component抽象构件角色：** 真实对象和装饰对象有相同的接口。这样，客户端对象就能够以与真实对象相同的方式同装饰对象交互。
- **ConcreteComponent 具体构件角色(真实对象)：** • io流中的FileInputStream、FileOutputStream
- **Decorator装饰角色：** 持有一个抽象构件的引用。装饰对象接受所有客户端的请求，并把这些请求转发给真实的对象 。这样，就能在真实对象调用前后增加新的功能。
- **ConcreteDecorator具体装饰角色：** 负责给构件对象增加新的责任。

# 应用
![](img\img1.png)

# 总结
- 总结：装饰器模式（Decorator）也叫包装器模式（Wrapper） 装饰模式降低系统的耦合度，可以动态的增加或删除对象的职责，并使得需要装饰的具体构建类和具体装饰类可以独立变化，以便增加新 的具体构建类和具体装饰类。
- 优点： 扩展对象功能，比继承灵活，不会导致类个数急剧增加 – 可以对一个对象进行多次装饰，创造出不同行为的组合，得到功能更 加强大的对象 – 具体构建类和具体装饰类可以独立变化，用户可以根据需要自己增加 新的具体构件子类和具体装饰子类。
- 缺点： 产生很多小对象。大量小对象占据内存，一定程度上影响性能。 – 装饰模式易于出错，调试排查比较麻烦 。
