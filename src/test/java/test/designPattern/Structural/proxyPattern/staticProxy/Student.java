package test.designPattern.Structural.proxyPattern.staticProxy;

/**
 * 委托类 被代理对象
 */
public class Student implements Person {
    private String name;
    public Student(String name) {
        this.name = name;
    }
    
    @Override
    public void giveMoney() {
       System.out.println("委托角色--学生："+name + "上交班费50元");
    }

    @Override
    public void homework() {
        System.out.println("委托角色--学生："+name + "交了作业");
    }
}