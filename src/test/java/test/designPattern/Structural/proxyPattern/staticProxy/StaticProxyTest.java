package test.designPattern.Structural.proxyPattern.staticProxy;

public class StaticProxyTest {
    public static void main(String[] args) {
        //这里并没有直接通过张三（被代理对象）来执行上交班费的行为，而是通过班长（代理对象）来代理执行了，这就是代理模式。

        //被代理的学生张三，他的班费上交有代理对象monitor（班长）完成
        Person zhangsan = new Student("张三");
        
        //生成代理对象，并将张三传给代理对象
        Person monitor = new StudentsProxy(zhangsan);
        
        //班长代理上交班费
        monitor.giveMoney();

        //交作业
        monitor.homework();
    }
}