package test.designPattern.Structural.proxyPattern.staticProxy;

/**
 * 学生代理类，也实现了Person接口，保存一个学生实体，这样既可以代理学生产生行为
 *
 * 代理类
 *
 */
public class StudentsProxy implements Person{
    //被代理的学生
    Person stu;
    
    public StudentsProxy(Person stu) {
        // 只代理学生对象
        if(stu.getClass() == Student.class) {
            this.stu = stu;
        }
    }
    
    //代理上交班费，调用被代理学生的上交班费行为
    public void giveMoney() {
        stu.giveMoney();
    }

    @Override
    public void homework() {
        //这里可对 委托角色增强前置处理
        System.out.println("666");
        stu.homework();
        //增强后置处理
        System.out.println("还考了95分");
    }
}