# proxy 代理模式
代理设计模式主要对方法前后实现增强
## 角色职责
- 抽象主题角色：可以是接口，也可以是抽象类； 被代理、代理对象都需要实现、继承这个接口、抽象类；
- 委托类（被代理类）角色：真实主题角色，业务逻辑的具体执行者；
- 代理类角色：内部含有对真实对象的引用，负责对真实主题角色的调用，并在真实主题角色处理前后做预处理和后处理。
- 客户端：访问代理对象

##主要分为
### 1 静态代理
- 优点

使得真实角色处理的业务更加纯粹，不再去关注一些公共的事情。公共的业务由代理来完成，实现业务的分工，公共业务发生扩展时变得更加集中和方便。
- 缺点

1 如果有多个类需要代理，那么就需要创建多个代理类分别代理目标对象，工作量较大，不利于维护。（动态代理就是为了解决这个问题）

### 2 动态代理 

代理类在程序运行时创建的代理方式被成为动态代理。(利用了反射)
![](img\dhcpProxy.png)
相比于静态代理， 动态代理的优势在于可以很方便的对代理类的函数进行统一的处理，而不用修改每个代理类中的方法。
#### 2.1 jdk动态代理
利用在java的java.lang.reflect包下提供了一个Proxy类和一个InvocationHandler接口，通过这个类和这个接口可以生成JDK动态代理类和动态代理对象。

实现InvocationHandler 利用 invoke方法中增强 Proxy生成委托对象的实例   --实现具体类接口       

![](img\dhcpProxy2.png)

![](img\dhcpProxy3.png)
#### 2.2 cglib动态代理
![img_1.png](img_1.png)
https://blog.csdn.net/qq_64170174/article/details/136644141?sharetype=blog&shareId=136644141&sharerefer=APP&sharesource=wdnmd1479&sharefrom=qq
导入第三方包cglib、asm
cglib 实现methodinterceptor接口  intercept方法中进行增强  Enhancer设置具体类和回调  ---生成的代理类为继承具体类的子类
![](img\dhcpProxy4.png)

![img.png](img.png)
示例代码例子：假如一个班的同学要向老师交班费，但是都是通过班长把自己的钱转交给老师。这里，班长代理学生上交班费，班长就是学生的代理。
springAop


查看生成的代理class文件
```
byte[] classFile = ProxyGenerator.generateProxyClass("$Proxy0", Student.class.getInterfaces());
        String path = "G:/javacode/javase/Test/bin/proxy/StuProxy.class";
        try(FileOutputStream fos = new FileOutputStream(path)) {
            fos.write(classFile);
            fos.flush();
            System.out.println("代理类class文件写入成功");
        } catch (Exception e) {
           System.out.println("写文件错误");
        }
```
https://www.cnblogs.com/mengbin0546/p/12553270.html