package test.designPattern.Structural.proxyPattern.dhcpProxy.jdk;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;

/**
 * 实现InvocationHandler接口，这个类中持有一个被代理对象的实例target
 * @param <T>
 */
public class StuInvocationHandler<T> implements InvocationHandler {
    //其中T叫做通配符，常用的通配符有T,E,K,V分别表示类型、元素、键、值，当然这并不是硬性规定，而是大家形成的一种通识。
    //invocationHandler持有的被代理对象
    T target;

    public StuInvocationHandler(T target) {
       this.target = target;
    }


    /**
     * 在invoke方法中执行被代理对象target的相应方法。
     * 在代理过程中，我们在真正执行被代理对象的方法前加入自己其他处理。
     * 这也是Spring中的AOP实现的主要原理，这里还涉及到一个很重要的关于java反射方面的基础知识。
     *
     * proxy:代表动态代理对象
     * method：代表正在执行的方法
     * args：代表调用目标方法时传入的实参
     */
    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        System.out.println("代理执行" +method.getName() + "方法");
        //代理过程中插入监测方法,计算该方法耗时
        MonitorUtil.start();//前置增强
        Object result = method.invoke(target, args);
        MonitorUtil.finish(method.getName());//后置增强
        return result;
    }
}