package test.designPattern.Structural.proxyPattern.dhcpProxy.jdk;

public class Student implements Person {
    private String name;
    public Student(String name) {
        this.name = name;
    }
    
    @Override
    public void giveMoney() {
        try {
            //假设数钱花了一秒时间
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println("委托角色--学生："+name + "上交班费50元");
    }

    @Override
    public void homework() {
        System.out.println("委托角色--学生："+name + "交了作业");
    }
}