package test.designPattern.Structural.proxyPattern.dhcpProxy.cglib;


import net.sf.cglib.proxy.Enhancer;
import net.sf.cglib.proxy.MethodInterceptor;
import net.sf.cglib.proxy.MethodProxy;
 
import java.lang.reflect.Method;
 
/**
 * Cglib动态代理：
 * (需要导入两个jar包，asm-5.0.3.jar,cglib-3.1.jar 版本可自行选择)
 */
 
//Cglib动态代理，实现MethodInterceptor接口
public class CglibProxy implements MethodInterceptor {
    private Object target;//需要代理的目标对象
 
    //重写拦截方法
    @Override
    public Object intercept(Object o, Method method, Object[] args, MethodProxy methodProxy) throws Throwable {
        System.out.println("Cglib动态代理，监听开始！");
        Object result = method.invoke(target,args);//方法执行参数：target 目标对象 arr参数数组
        System.out.println("Cglib动态代理，监听结束！");
        return result;
    }
 
    //定义获取代理对象的方法 获取代理对象
    public Object getCglibProxy(Object targetObject) {
        this.target = targetObject;//为目标对象target赋值
        Enhancer enhancer = new Enhancer();
        //设置父类,因为Cglib是针对指定的类生成一个子类，所以需要指定父类
        enhancer.setSuperclass(targetObject.getClass()); //UserManagerImpl
        enhancer.setCallback(this);//设置回调
        Object result = enhancer.create();//创建并返回代理对象
        return result;
    }
 
 
//    public static void main(String[] args) {
//        CglibProxy cglibProxy = new CglibProxy(); //实例化CglibProxy对象
//        UserManager user = cglibProxy.getCglibProxy(new UserManagerImpl());//获取代理对象
//        user.delUser("admin");
//    }
 
}