#简介
在Java领域的软件开发中，设计模式是提高代码可维护性和可扩展性的重要工具。其中，享元模式是一种被广泛使用的设计模式，它通过优化对象的重用来提升系统性能。

享元模式是一种结构型设计模式，旨在通过共享对象来减少系统中的对象数量，从而提升性能和减少内存消耗。在享元模式中，对象分为两类：内部状态（Intrinsic State）和外部状态（Extrinsic State）。内部状态是对象可共享的部分，而外部状态是对象的变化部分，每个对象都有自己的外部状态。

与其他设计模式的区别：

享元模式与单例模式：单例模式关注于只有一个实例的创建，而享元模式关注于对象的重用。享元模式允许多个对象存在，但通过共享内部状态来减少重复对象的创建。
享元模式与原型模式：原型模式通过复制现有对象来创建新的对象，而享元模式通过共享现有对象来避免创建新对象。享元模式在多个对象之间共享相同的状态，而原型模式则不共享。####// 定义享元接口
    
#模式的结构
* 抽象享元类
* 具体享元类
* 享元工厂类
#优缺点
##优点
减少对象的数量：享元模式通过共享内部状态来减少系统中的对象数量，从而减少内存消耗。
提升性能：由于重复对象的创建被避免，享元模式可以显著提升系统的性能。
简化对象结构：通过将对象的状态划分为内部状态和外部状态，享元模式可以简化对象的结构，使得系统更易于理解和维护。
##缺点
需要维护共享池：享元模式需要维护一个共享对象的池，这可能会增加代码的复杂性和维护成本。
对象状态共享可能引发线程安全问题：如果多个线程同时访问共享对象并修改其外部状态，需要确保线程安全性。
#运用场景
###享元模式适用于以下场景：

当系统中存在大量相似对象且消耗大量内存时，可以考虑使用享元模式来减少内存消耗。
当需要频繁创建和销毁对象时，可以使用享元模式提升系统性能。
当对象的内部状态与外部状态分离，并且外部状态相对较少时，可以考虑使用享元模式。
总结
享元模式是Java设计模式中一种被广泛应用的优化性能的设计模式。通过共享内部状态来减少对象的数量，从而降低内存消耗和提升系统性能。与单例模式和原型模式相比，享元模式注重对象的重用而不是单例或复制。使用Java编程语言实现享元模式可以通过共享池来管理对象的共享和创建。

该模式的优点在于减少对象数量、提升性能和简化对象结构。通过共享对象，系统内存占用减少，同时避免了重复创建对象的开销，从而提高了系统的性能。通过划分内部状态和外部状态，对象结构更清晰，更易于理解和维护。

然而，享元模式也存在一些缺点和限制。维护共享池可能增加代码的复杂性和维护成本。并且，共享对象的状态共享可能引发线程安全问题，需要注意并发访问和修改共享对象的外部状态。

适用场景包括系统中存在大量相似对象且消耗大量内存的情况，通过共享对象可以减少内存消耗。在需要频繁创建和销毁对象的情况下，享元模式可以提升系统性能。当对象的内部状态与外部状态分离，且外部状态相对较少时，也可以考虑使用该模式。

通过合理应用享元模式，开发人员可以优化系统设计，提高代码的可维护性和可扩展性。通过共享对象，我们可以更有效地管理系统资源，提升系统的性能和响应能力。因此，享元模式在Java开发中具有重要的应用和意义。

#实现 
    ####//抽象享元接口
    public interface Shape {
        void draw();
    }

####// 具体享元类
    public class Circle implements Shape {
        private String color;
        
        public Circle(String color) {
            this.color = color;
        }
        
        public void draw() {
            System.out.println("Drawing a circle with color: " + color);
        }
    }

####// 享元工厂类
    public class ShapeFactory {
        private static final Map<String, Shape> circleMap = new HashMap<>();
        
        public static Shape getCircle(String color) {
            Circle circle = (Circle) circleMap.get(color);
            
            if (circle == null) {
                circle = new Circle(color);
                circleMap.put(color, circle);
                System.out.println("Creating a new circle with color: " + color);
            }
            
            return circle;
        }
    }

####// 客户端代码
     public class Client {
        private static final String[] colors = { "Red", "Green", "Blue" };
        
        public static void main(String[] args) {
            for (int i = 0; i < 20; i++) {
                Circle circle = (Circle) ShapeFactory.getCircle(getRandomColor());
                circle.draw();
            }
        }
        
        private static String getRandomColor() {
            return colors[(int)(Math.random() * colors.length)];
        }
    }


