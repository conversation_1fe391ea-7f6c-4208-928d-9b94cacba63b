https://www.jb51.net/article/266199.htm
#简介
Java中的桥接模式（Bridge Pattern）是一种结构性设计模式，
<font color="#FF0000">它将抽象部分和实现部分分离，使它们可以独立变化</font>，
同时通过桥接对象将它们连接起来。桥接模式通过将继承关系转变为对象组合，以实现抽象和实现的解耦。

在桥接模式中，抽象部分指的是高层的抽象类或接口，定义了抽象方法和对实现部分的引用。实现部分指的是低层的实现类或接口，提供了具体的实现细节。桥接模式通过桥接对象（也称为连接器）将抽象部分和实现部分连接在一起，使它们可以独立地变化。
#角色职责
桥接（Bridge）模式包含以下主要角色

* （1）抽象化（Abstraction）角色：定义抽象类，并包含一个对实现化对象的引用。
* （2）扩展抽象化（Refined Abstraction）角色：是抽象化角色的子类，实现父类中的业务方法，并通过组合关系调用实现化角色中的业务方法。
* （3）实现化（Implementor）角色：定义实现化角色的接口，供扩展抽象化角色调用。
* （4）具体实现化（Concrete Implementor）角色：给出实现化角色接口的具体实现。

#优缺点
###优点
* 桥接模式偶尔类似于多继承方案，但是多继承方案违背了类的单一职责原则，复用性比较差，类的个数也非常多，桥接模式是比多继承方案更好的解决方法。极大的减少了子类的个数，从而降低管理和维护的成本。
* 桥接模式提高了系统的可扩充性，在两个变化维度中任意拓展一个维度，都不需要修改原有的系统。符合开闭原则，就像一座桥，可以把两个变化的维度连接起来。

### 缺点
* 桥接模式的引入会增加系统的理解与设计难度，由于聚合关联关系建立在抽象层，要求开发者针对抽象进行设计与编程。
* 桥接模式要求正确识别出系统中两个独立变化的维度，因此其使用范围具有一定的局限性。
# 适用环境
如果一个系统需要在构件的抽象化角色和具体化角色之间增加更多的灵活性，避免在两个层次之间建立静态的继承联系，通过桥接模式可以使它们在抽象层建立一个关联关系。

抽象化角色和实现化角色可以继承的方式独立扩展而不相互影响，在程序运行时可以动态讲一个抽象化子类的对象和一个实现化子类的对象进行组合，即系统需要对抽象化角色和实现化角色进行动态耦合。

一个类存在两个独立变化的维度，且这两个维度都需要进行扩展。虽然在系统中使用继承是没有问题的，但是由于抽象化角色和具体化角色需要独立变化，设计需求需要独立管理这两者。

对于那些不希望使用继承或因为多层次继承导致系统类的个数急剧增加的系统，桥接模式尤为适用。


# 场景
* Java语言通过Java虚拟机实现跨平台性
* AWT中的Peer架构
然在系统中使用继承是没有问题的，但是由于抽象化角色和具体化角色需要独立变化，设计需求需要独立管理这两者。

对于那些不希望使用继承或因为多层次继承导致系统类的个数急剧增加的系统，桥接模式尤为适用。

* Java语言通过Java虚拟机实现跨平台性
* AWT中的Peer架构
* JDBC驱动程序也是桥接模式的应用之一