package test.designPattern.Structural.adapterPattern.objectAdapter;

//客户端类 ：想上网，插不上网线 电脑接口为USB
public class ClientComputer {
    //电脑需要连接上转接器才可以上网
    public void net(NetToUsb adapter) {
        //上网的具体实现：找一个转接头
        adapter.handleRequest();
    }
  public static void main(String[] args) {
        //电脑，适配器，网线
        ClientComputer clientComputer = new ClientComputer();//电脑
        Adaptee adaptee = new Adaptee();//网线
        Adapter2 adapter2 = new Adapter2(adaptee);//转接器
        clientComputer.net(adapter2);
    }
}