# adapter 适配者模式 
## 适用场景
(有时，现有类可以满足客户端类的需要，但所提供接口不一定是客户端所期望的，可能因为现有类中方法名与目标类中定义的方法名不一致。
 
 这时，现有接口需要转化为客户端的期望接口，保证复用现有类。若不进行这样转化，客户端就不能利用现有类所提供功能，适配器模式就可以完成这样的转化。
)
- 系统需要使用一些现有的类，而这些类的接口（如方法名）不符合系统的需要，甚至没有这些类的源代码

- 像创建一个重复使用的类，用于与一些彼此之间没有太大联系的类，包括一些可能子啊将来一起工作的类

- 比如GUI编程，springboot

## 角色职责
- Target：目标抽象类
该角色定义把其他类转换为何种接口，也就是我们的期望接口。
- Adapter：适配器 适配器模式的核心角色，其他两个角色都是已经存在的角色，而适配器角色是需要新建立的，它的职责非常简单：通过继承或是类关联的方式把源角色转换为目标角色。
- Adaptee：适配者 你想把谁转换成目标角色，这个“谁”就是源角色，它是已经存在的、运行良好的类或对象。
- Client：客户类

## 类适配器 
因为 Adapter 类既继承了 Adaptee （被适配类），也实现了 Target 接口（因为 Java 不支持多继承，所以这样来实现）所以被称为类适配器
## 对象适配器 推荐使用
- 对象适配器的优点：

一个对象适配器可以把多个不同的适配者适配到同一个目标
可以适配一个适配者的子类，由于适配器和适配者之间是关联关系，根据’里氏替换原则’适配者的子类也可以通过该适配器进行适配

- 类适配器的缺点 ：

对于java,c#等不支持多重类继承的语言，一次最多只能适配一个适配者类，不同同时适配多个适配者
在java,c#等语言中，类适配器模式中的目标抽象类只能为接口，不能为类，其使用有一定的局限性
