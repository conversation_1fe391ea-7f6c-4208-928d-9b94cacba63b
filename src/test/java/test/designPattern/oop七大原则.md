
#一 开闭原则(The Open-Closed Principle ，OCP)
## 扩展性 复用性 可靠性 高内聚低耦合 可读性 ##
><font color=red size=4>**强调扩展 扩展开放 修改关闭 及易扩展禁止修改**</font> 

> 扩展开放：**某模块的功能是可扩展的，则该模块是扩展开放的。软件系统的功能上的可扩展性要求模块是扩展开放的**。
> 修改关闭：**某模块被其他模块调用，如果该模块的源代码不允许修改，则该模块修改关闭的。软件系统的功能上的稳定性，持续性要求模块是修改关闭的。**

# 二 单一职责原则
> <font color=red size=4>**强调高内聚低耦合 单个对象只干一件事  --** </font> </br>

> 单一职责原则：**一个对象应该只包含单一的职责，并且该职责被完整地封装在一个类中。**</br>
>**单一职责原则的另一种定义方式：就一个类而言，应该仅有一个引起它变化的原因。**



# 三 里氏替换原则（Liskov Substitution Principle ，LSP）
> <font color=red size=4>**子类可以扩展父类功能 但不覆盖父类方法** </font></br>
> 里氏代换原则：所有引用基类的地方必须能透明地使用其子类的对象
> 也就是说，只有满足以下2个条件的OO设计才可被认为是满足了LSP原则：
> 
> 不应该在代码中出现if/else之类对派生类类型进行判断的条件。
> 
> 派生类应当可以替换基类并出现在基类能够出现的任何地方，或者说如果我们把代码中使用基类的地方用它的派生类所代替，代码还能正常工作。
> 
> 里式替换原则的引申意义：**子类可以扩展父类的功能，但不能改变父类原有的功能。**
# 四 迪米特原则（最少知道原则）（Law of Demeter ，LoD）
> <font color=red size=4>**好好处对象 不要找对象的兄弟做小三**</font>
> 
> 第米特原则：迪米特原则（Law of Demeter）又叫最少知道原则（Least Knowledge Principle），可以简单说成：talk only to your immediate friends，**只与你直接的朋友们通信，不要跟“陌生人”说话。**

# 五 接口隔离原则(Interface Segregation Principle, ISP)
> <font color=red size=4>**一个类对一个类的依赖应该建立在最小的接口上 </br>
> 建立单一接口，不要建立庞大臃肿的接口</br>
> 尽量细化接口，接口中的方法尽量少</br>** </font>

> 接口隔离原则Interface Segregation Principle, ISP)：**可读性 使用多个专门的接口，而不使用单一 的总接口，即客户端不应该依赖那些它不需要的接口。**
> 
> 接口的设计原则：**接口的设计应该遵循最小接口原则，不要把用户不使用的方法塞进同一个接口里。如果一个接口的方法没有被使用到，则说明该接口过胖，应该将其分割成几个功能专一的接口。**</br>
> 接口的依赖（继承）原则：**如果一个接口a继承另一个接口b，则接口a相当于继承了接口b的方法，那么继承了接口b后的接口a也应该遵循上述原则：不应该包含用户不使用的方法。 反之，则说明接口a被b给污染了，应该重新设计它们的关系。**

# 六 依赖倒置原则（Dependency Inversion Principle ，DIP）
> <font color=red size=4>
> ** 依赖于高层的抽象 不依赖底层的实现 **
> </font> </br>
> 依赖倒转原则定义如下：
> **高层模块不应该依赖底层模块，它们都应该依赖抽象。抽象不应该依赖于细节，细节应该依赖于抽象**

#七 合成复用原则（Composite/Aggregate Reuse Principle ，CARP）
> <font color=red size=4>**强调复用性 应用对象的组合 并不是使用继承**</font> </br>
> **合成复用原则又称为组合/聚合复用原则**(Composition/Aggregate Reuse Principle, CARP)，其定 义如下：
> 合成复用原则(Composite Reuse Principle, CRP)**：尽量使用对象组合，而不是继承来达到复 用的目的。**

###一篇文章所有设计模式
    https://zhuanlan.zhihu.com/p/427324132
###创建型
* 单例模式、原型模式、工厂方法模式、抽象工厂模式、建造者模式
###结构型
* 适配器模式、装饰模式、代理模式、外观模式、桥接模式、组合模式、享元模式。
###行为型
* 模版方法模式
* 策略模式
* 命令模式
* 责任链模式
* 状态模式
* 观察者模式
* 中介者模式
* 迭代器模式
* 访问者模式
* 备忘录模式
* 解释器模式
<br/>
* |设计模式|一句话归纳|         目 的 |   生活 案例 |框架源码举例|
----除了模版方法模式和解释器模式是类行为模式，其他全部属于对象行为模式。

|设计模式|      一句话归纳      |目的|生活案例|                           框架源码举例                           |
|:-:|:---------------:|:-:|:-:|:----------------------------------------------------------:|
|单例模式（Singleton Pattern）|     世上只有一个我     |保证独一无二|CEO|                 Runtime、ApplicationContext                 |
|工厂模式（Factory Pattern）|   产品标准化，生产更高效   |封装创建细节|实体工厂|             LoggerFactory、Calender、BeanFactory             |
|原型模式（Prototype Pattern）|   拔一根猴毛，吹出千万个   |高效创建对象|克隆|           Cloneable、Serialization 、PrototypeBean           |
|建造者模式（Builder Pattern）| 高配中配与低配（想选哪配就哪配）、构架顺序  |开放个性配置步骤|选配|            StringBuilder、BeanDefinitionBuilder             |
|代理模式（Proxy Pattern）| 没有资源没时间，得找媒婆来帮忙 |增强职责|媒婆|     ProxyFactoryBean、JdkDynamicAopProxy、CglibAopProxy      |
|门面模式（Facade Pattern）|   打开一扇门，通向全世界   |统一访问入口|前台|                  JdbcUtils、RequestFacade                   |
|装饰器模式（Decorator Pattern）|   他大舅他二舅，都是他舅   |灵活扩展、同宗同源|煎饼|           BeanWrapper、BufferedReader、InputStream           |
|享元模式（Flyweight Pattern）|  优化资源配置，减少重复浪费  |共享资源池|全国社保联网|                 String、Integer、ObjectPool                  |
|组合模式（Composite Pattern）| 人在一起叫团伙，心在一起叫团队 |统一整体和个体|组织架构树|                      HashMap、SqlNode                       |
|适配器模式（Adapter Pattern）|      万能充电器      |兼容转换|电源适配|               AdvisorAdapter、HandlerAdapter                |
|桥接模式（Bridge Pattern）|     约定优于配置      |不允许用继承|桥|                       DriverManager                        |
|委派模式（Delegate Pattern）| 这个需求很简单，怎么实现我不管 |只对结果负责|授权委托书| DispatcherServlet、ClassLoader、BeanDefinitionParserDelegate |
|模板模式（Template Pattern）| 流程全部标准化，需要微调请覆盖 |逻辑复用|把大象装进冰箱|                  JdbcTemplate、HttpServlet                  |
|策略模式（Strategy Pattern）| 条条大道通北京，具体哪条你来定 |把选择权交给用户|选择支付方式|      HandlerMapping、 Comparator、InstantiationStrategy      |
|责任链模式（Chain of Responsibility Pattern）| 各人自扫门前雪，莫管他人瓦上霜 |解耦处理逻辑|踢皮球|                    FilterChain、Pipeline                    |
|迭代器模式（Iterator Pattern）| 流水线上坐一天，每个包裹扫一遍 |统一对集合的访问方式|逐个检票进站|                          Iterator                          |
|命令模式（Command Pattern）|  运筹帷幄之中，决胜千里之外  |解耦请求和处理|遥控器|                     Runnable、TestCase                      |
|状态模式（State Pattern）|  状态驱动行为，行为决定状态  |绑定状态和行为|订单状态跟踪|                         Lifecycle                          |
|备忘录（Memento Pattern）| 失足不成千古恨，想重来时就重来 |备份，后悔机制|草稿箱|               StateManageableMessageContext                |
|中介者（Mediator Pattern）| 联系方式我给你，怎么搞定我不管 |统一管理网状资源|朋友圈|                           Timer                            |
|解释器模式（Interpreter Pattern）| 我想说“方言”，一切解释权归我 |实现特定语法解析|摩斯密码|                  Pattern、ExpressionParser                  |
|观察者模式（Observer Pattern）|     到点就通知我      |解耦观察者与被观察者|闹钟|                   ContextLoaderListener                    |
|访问者模式（Visitor Pattern）| 横看成岭侧成峰，远近高低各不同 |解耦数据结构和数据操作|KPI考核|                 FileVisitor、BeanDefinitio                  |