# observer 观察者模式
 观察者模式又称为发布/订阅(Publish/Subscribe)模式,在对象之间定义了一对多的依赖，
这样一来，当一个对象改变状态，依赖它的对象会收到通知并自动更新.
#
相关源码类包含 Listener、event的类
# 角色职责
- 抽象被观察者角色：
  也就是一个抽象主题，它把所有对观察者对象的引用保存在一个集合中，每个主题都可以有任意数量的观察者。抽象主题提供一个接口，可以增加和删除观察者角色。一般用一个抽象类和接口来实现。
- 抽象观察者角色：
  为所有的具体观察者定义一个接口，在得到主题通知时更新自己。
- 具体被观察者角色：
  也就是一个具体的主题，在集体主题的内部状态改变时，所有登记过的观察者发出通知。
- 具体观察者角色：
  实现抽象观察者角色所需要的更新接口，一边使本身的状态与制图的状态相协调。


# 例子
- demo1 手动实现观察者模式
- demo2 Java内置观察者模式实现
在java.util包中包含有基本的Observer接口和Observable抽象类.
功能上和Subject接口和Observer接口类似.不过在使用上,
就方便多了,因为许多功能比如说注册,删除,通知观察者的那些功能已经内置好了.

- Observer
![](img\img1.png)
- Observable
![](img\img2.png)
# 应用场景：
  聊天室程序的，服务器转发给所有客户端
  网络游戏(多人联机对战)场景中，服务器将客户端的状态进行分发
  邮件订阅
  Servlet中，监听器的实现
  Android中，广播机制
  JDK的AWT中事件处理模型,基于观察者模式的委派事件模型(DelegationEventModel)
  • 事件源----------------目标对象
  • 事件监听器------------观察者
  京东商城中，群发某商品打折信息
 