package test.designPattern.Behavioral.observer.demo2;

import java.util.Observable;
import java.util.Observer;
/**
 * 观察者模式：具体观察者(消息订阅者)
 * 实现Observer接口
 * <AUTHOR>
 *
 */
public class ConcreteObserver implements Observer {

	private int myState;


	@Override
	public void update(Observable o, Object arg) {
		myState = ((ConcreteSubject)o).getState();
	}
	public int getMyState() {

		return myState;
	}
	public void setMyState(int myState) {
		this.myState = myState;
	}
}
