package test.designPattern.Behavioral.Responsibility.demo;

import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2022/11/2 22:03
 **/
public class ResponsibilityClient {

    @DisplayName("责任链模式")
    @Test
    public void testChain() {
        FirstPassHandler firstPassHandler = new FirstPassHandler();
        SecondPassHandler secondPassHandler = new SecondPassHandler();
        ThirdPassHandler thirdPassHandler = new ThirdPassHandler();

        firstPassHandler.setNext(secondPassHandler);
        secondPassHandler.setNext(thirdPassHandler);

        firstPassHandler.handler();
    }


}
