# Responsibility 责任链模式
责任链模式：Chain of Responsibility Patten 。就是将链中的每一个结点看做是一个对象，每个结点处理请求均不同，且内部自动维护一个下一个结点对象。当请求从链条的首端出发时，会沿着链的路径依次传递给每一个结点的对象，直到有对象处理这个请求为止。

就是说每个结点会处理一件事情，如果结点间出现异常，那么链路就会中断。

一般比如说一个请假需要多个负责任审批，过五关斩六将等这些，都是责任链模式。

# 角色职责
- 抽象处理者(Handler)角色：定义一个处理请求的接口，包含抽象处理方法和一个后继连接
- 具体处理者(Concrete Handler)角色：实现抽象处理者的处理方法，判断能否处理本次请求，如果可以处理请求则处理，否则将该请求转给它的后继者
- 客户类(Client)角色：创建处理链，并向链头的具体处理者对象提交请求，它不关心处理细节和请求的传递过程

# 案例
#### demo 责任链简单实现
#### demoUp 责任链+工厂
   通过枚举以及抽象工厂，我们将每个处理器连接起来，让客户端只关系自己的诉求部分，具体实现逻辑交由枚举来定义。这样做简化客户端的业务。相对来说比较友好，而且容易拓展。 

# 使用场景案例
- RBAC 模型 ：多条件流程判断
- ERP 系统流程审批：总经理、销售总监、产品部经理、项目组长
- Java MVC 过滤器的底层实现 Filter

# 优缺点
### 优点
降低耦合度。它将请求的发送者和接收者解耦
简化了对象，使得对象不需要知道链的结构
增强给对象指派职责的灵活性，允许动态地新增或者删除责任链
增加新的请求处理类方便
### 缺点
不能保证请求一定被接收；
系统性能将受到一定影响，调试时不方便，可能会造成循环调用


原文链接：
https://blog.csdn.net/rothchil/article/details/126174982
