package test.designPattern.Behavioral.Responsibility.demoUp;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FilterHandlerEnumFactory {

    private static FilterDao filterDao = new FilterImpl();

    /**
     * 提供静态方法，获取第一个handler
     *
     * @return AbstractFilterHandler
     * <AUTHOR> href="https://github.com/rothschil">Sam</a>
     * @date 2022/8/4-23:50
     **/
    public static AbstractFilterHandler getFirstHandler() {

        FilterEntity firstGatewayEntity = filterDao.getFirstGameEntity();
        AbstractFilterHandler firstAbstractFilterHandler = newGatewayHandler(firstGatewayEntity);
        if (firstAbstractFilterHandler == null) {
            return null;
        }

        FilterEntity tempFilterEntity = firstGatewayEntity;
        Integer nextHandlerId = null;
        AbstractFilterHandler tempHandler = firstAbstractFilterHandler;
        // 迭代遍历所有handler，以及将它们链接起来
        while ((nextHandlerId = tempFilterEntity.getNextHandlerId()) != null) {
            //获取下一节点的 实体类
            FilterEntity filterEntity = filterDao.getGameEntity(nextHandlerId);
            //反射创建 下一节点的具体处理者
            AbstractFilterHandler abstractFilterHandler = newGatewayHandler(filterEntity);
            //当前节点具体处理者与下一具体处理中关联
            tempHandler.setNext(abstractFilterHandler);


            //准备下一节点的循环
            tempHandler = abstractFilterHandler;
            tempFilterEntity = filterEntity;

            log.info("Init GatewayHandler: "+tempFilterEntity.getHandlerId());
        }
        // 返回第一个handler
        return firstAbstractFilterHandler;
    }

    /**
     * 反射实体化具体的处理者
     *
     * @param filterEntity
     * @return AbstractFilterHandler
     */
    private static AbstractFilterHandler newGatewayHandler(FilterEntity filterEntity) {
        try {
            String clazzName = filterEntity.getConference();
            log.info(clazzName);
            Class<?> clazz = Class.forName(clazzName);
            return (AbstractFilterHandler) clazz.newInstance();
        } catch (IllegalAccessException | InstantiationException | ClassNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }
}


