package test.designPattern.Behavioral.Responsibility.demoUp;

import org.junit.Test;
import org.junit.jupiter.api.DisplayName;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2022/11/2 22:03
 **/
public class ResponsibilityClient {

    @DisplayName("责任链模式-工厂")
    @Test
    public void testChainFactory() {
        AbstractFilterHandler handler = FilterHandlerEnumFactory.getFirstHandler();
        handler.handler();
    }


}
