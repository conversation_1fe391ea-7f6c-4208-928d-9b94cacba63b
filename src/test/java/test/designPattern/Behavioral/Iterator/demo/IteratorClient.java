package test.designPattern.Behavioral.Iterator.demo;

public class IteratorClient {
	public static void main(String[] args) {
		//创建聚合对象
		Aggregate aggregate = new ConcreteAggregate();
		aggregate.add("中山大学");
		aggregate.add("华南理工");
		aggregate.add("韶关学院");
		System.out.print("聚合的内容有：");
		//聚合对象获取迭代器
		Iterator it = aggregate.getIterator();
		while (it.hasNext()) {
			Object ob = it.next();
			System.out.print(ob.toString() + "\t");
		}
		Object ob = it.first();
		System.out.println("\nFirst：" + ob.toString());
	}
}
 
/**
聚合的内容有：中山大学	华南理工	韶关学院	
First：中山大学
*/