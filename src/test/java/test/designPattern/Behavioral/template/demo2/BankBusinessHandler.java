package test.designPattern.Behavioral.template.demo2;

import cn.hutool.core.util.RandomUtil;

/**
 * 模板方法设计模式的抽象类
 * 模板设计模式主要用来处理相同处理流程中的不同点，例如银行办理业务，无论什么业务，首先需要取号，其次处理业务，然后评价不同之处在于处理不同的业务使用不同的方式。
 * BankBusinessHandler作为抽象模板
 * BankSaveMoneyHandler作为存钱业务处理类，继承抽象模板BankBusinessHandler
 * BankDrawMoneyHandler作为理财业务处理类，继承抽象模板BankBusinessHandler
 *
 * <AUTHOR>
 * @since 2022/2/8
 **/
public abstract class BankBusinessHandler {

    /**
     * 模板方法，执行器
     */
    public final void execute() {
        getNumber();
        handle();
        judge();
    }

    /**
     * 取号
     */
    private void getNumber() {
        System.out.println("取号：" + RandomUtil.randomNumbers(8));
    }

    /**
     * 办理业务
     */
    public abstract void handle();

    /**
     * 评价
     */
    private void judge() {
        System.out.println("评价：五星好评！");
    }

}

