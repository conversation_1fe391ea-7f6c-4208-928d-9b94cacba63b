package test.designPattern.Behavioral.template.demo2;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2022/2/8
 **/
public class BankBusinessHandlerCustomerTest {
    public static void main(String[] args) {
        //构建银行业务处理对象
        BankBusinessHandlerCustomer businessHandler = new BankBusinessHandlerCustomer();

        //存钱业务处理
        businessHandler.save(new BigDecimal("3000"));

        System.out.println("------------------------------");

        //理财业务处理
        businessHandler.draw(new BigDecimal("6000"));

    }

}

