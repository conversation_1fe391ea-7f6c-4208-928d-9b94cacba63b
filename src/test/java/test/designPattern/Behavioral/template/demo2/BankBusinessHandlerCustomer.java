package test.designPattern.Behavioral.template.demo2;

import cn.hutool.core.util.RandomUtil;

import java.math.BigDecimal;
import java.util.function.Consumer;

/**
 * 模板设计模式的抽象类
 * Java8函数式编程消费者模式Customer实现模板设计模式
 *
 * <AUTHOR>
 * @since 2022/2/8
 **/
public class BankBusinessHandlerCustomer {
    /**
     * 存钱
     */
    public void save(BigDecimal amount) {
        execute(a -> System.out.println("存钱：" + amount));
    }

    /**
     * 理财
     */
    public void draw(BigDecimal amount) {
        execute(a -> System.out.println("理财：" + amount));
    }

    /**
     * 模板方法，执行器
     */
    public void execute(Consumer<BigDecimal> consumer) {
        getNumber();
        consumer.accept(null);
        judge();
    }

    /**
     * 取号
     */
    private void getNumber() {
        System.out.println("取号：" + RandomUtil.randomNumbers(8));
    }

    /**
     * 评价
     */
    private void judge() {
        System.out.println("评价：五星好评！");
    }
}

