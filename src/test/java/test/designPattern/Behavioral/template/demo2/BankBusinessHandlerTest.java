package test.designPattern.Behavioral.template.demo2;

/**
 * 模板方法测试类
 *
 * <AUTHOR>
 * @since 2022/2/8
 **/
public class BankBusinessHandlerTest {
    public static void main(String[] args) {

        //存钱业务处理
        BankSaveMoneyHandler bankSaveMoneyHandler = new BankSaveMoneyHandler();
        bankSaveMoneyHandler.execute();

        System.out.println("------------------------------");

        //理财业务处理
        BankDrawMoneyHandler bankDrawMoneyHandler = new BankDrawMoneyHandler();
        bankDrawMoneyHandler.execute();

    }
}

