package test.designPattern.Behavioral.template.demo1;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2022/11/1 19:01
 **/
public class TemplateClient {
    public static void main(String[] args) {
        /**
         * 模板方法模式
         */
        ZhangWuJi zhangWuJi = new ZhangWuJi();
        zhangWuJi.fighting();
        System.out.println("======");
        ZhangSanFeng zhangSanFeng = new ZhangSanFeng();
        zhangSanFeng.fighting();
    }
}
