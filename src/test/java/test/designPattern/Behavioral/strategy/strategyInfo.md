# strategy 策略者

策略模式是一种行为型模式，它将对象和行为分开，将行为定义为 
一个行为接口 和 具体行为的实现。
定义一系列的算法，把它们一个个封装起来，并且可以使他们互相替换。
本模式可以使得算法可独立与使用它的客户而变化。


# 角色职责
环境角色(Context)：持有一个策略类的引用，提供给客户端使用。
抽象策略角色(Strategy)：这是一个抽象角色，通常由一个接口或抽象类实现。此角色给出所有的具体策略类所需的接口。
具体策略角色(ConcreteStrategy)：包装了相关的算法或行为。实战代码中很多Handler、Processor、Loader实现类，一般就是策略角色，实现了不同的行为。


![](img\img1.png)


### 案例为：使用不同支付方式
# 策略模式的优缺点：
###优点：

1、定义一系列算法

2、避免多重条件语句

3、更好的扩展性

### 缺点：

1、客户必须了解每一种策略的不同

2、增加了对象数目

3、只适合扁平的算法结构

# 应用场景
1、出现有许多相关的类，仅仅是行为有差别的情况下，可以使用策略模式来使用多个行为中的一个来配置一个类的方法，实现算法动态切换。

2、实现同一个算法，有很多不同实现的情况下，可以使用策略模式来把这些“不同的实现”实现成为一个算法的类层次。

3、需要封装算法中，有与算法相关数据的情况下，可以使用策略模式来避免暴露这些跟算法相关的数据结构。

4、出现抽象一个定义了很多行为的类，并且是通过多个if-else语句来选择这些行为的情况下，可以使用策略模式来代替这些条件语句。


###后记：
策略模式与代理模式、装饰者模式有些类似，主要是因为这三个模式都是在 辅助类中持有一个目标类的对象，通过操作这个目标对象，完成实际任务。但他们的主要区别如下：

- 策略模式：

通过将一些易变的计算抽象为策略实体，通过多态，让实际调用者尽可能的不用感知这种变化。

- 代理模式：

其与策略模式不同的是，代理类必须和被代理类具有相同的父类或接口，即代理类要么继承或实现被代理类的父类，要么直接继承被代理类本身。
代理模式不是为了封装变化，而是为了隐藏实际执行者本身。

- 装饰者模式：

其与策略模式的不同在于，原有类和装饰器类必须继承同一个父类。装饰器对象除了需要完成持有对象的操作外，还有一些附加操作，这些附加操作随着装饰器的不同而变化。
 持有对象本身的操作是主体，装饰器的操作是补充。而策略模式中，具体策略才是主体。

- 工厂模式

工厂模式是创建型模式，注重对象的创建，并提供获取子类方法。策略模式则注重子类实现的行为，是行为型模式。


地址:
https://blog.csdn.net/u012440207/article/details/102884462