package test.designPattern.Behavioral.strategy.demo.extendUp1;

import test.designPattern.Behavioral.strategy.demo.IPaymentStrategy;

/**
 * 扩展方式1 -- -
 * 扩展上下文的方式
 * 1、新增一个继承于PaymentContext的子类NewPaymentContext，生成一个新的构造方法，构造参数中包含账户account参数。
 * 2、新增一个支付策略类，其pay中，需要将PaymentContext转换成所需的NewPaymentContext实例，然后进行后续操作。
 * @Date：2022/11/2 18:58
 **/
public class StrategyClientUp1 {
    public static void main(String[] args) {
        IPaymentStrategy creditCardStrategy = new CreditCardStrategy();
        NewPaymentContext newPaymentContext = new NewPaymentContext("王五", 9000.0, "**********",creditCardStrategy);
        newPaymentContext.payNow();
    }
}
