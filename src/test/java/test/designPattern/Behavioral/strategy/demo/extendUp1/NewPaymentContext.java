package test.designPattern.Behavioral.strategy.demo.extendUp1;


import test.designPattern.Behavioral.strategy.demo.IPaymentStrategy;
import test.designPattern.Behavioral.strategy.demo.PaymentContext;

/**
 * 新扩展的支付策略的上下文
 * <AUTHOR>
 * @date 2019-11-03
 */
public class NewPaymentContext extends PaymentContext {
    private String mAccount;
 
     /**
     * 构造方法，新增了一个账户(account)参数
     * @param userName
     * @param money
     * @param account
     * @param paymentStrategy
     */
    public NewPaymentContext(String userName, double money, String account,
                             IPaymentStrategy paymentStrategy) {
        super(userName, money, paymentStrategy);
        mAccount = account;
    }
 
    public String getAccount() {
        return mAccount;
    }
}