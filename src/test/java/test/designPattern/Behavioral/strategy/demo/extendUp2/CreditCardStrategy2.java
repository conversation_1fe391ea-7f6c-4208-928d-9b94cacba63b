package test.designPattern.Behavioral.strategy.demo.extendUp2;

import test.designPattern.Behavioral.strategy.demo.IPaymentStrategy;
import test.designPattern.Behavioral.strategy.demo.PaymentContext;

/**
 * 信用卡支付策略实现类2
 *
 * <AUTHOR>
 * @date 2019-11-03
 */
public class CreditCardStrategy2 implements IPaymentStrategy {
 
    private String mAccount;
 
    public CreditCardStrategy2(String account) {
        mAccount = account;
    }
 
    public String getAccount() {
        return mAccount;
    }
 
    @Override
    public void pay(PaymentContext context) {
        System.out.println(
            "支付方式：信用卡支付2 " + "收款人---姓名：" + context.getUserName() + " 账号："
                + getAccount() + " 工资：" + context.getMoney()
                + "\n");
    }
}