package test.designPattern.Behavioral.strategy.demo.extendUp2;

import test.designPattern.Behavioral.strategy.demo.IPaymentStrategy;
import test.designPattern.Behavioral.strategy.demo.PaymentContext;

/**
 * 扩展方式二：在策略的算法实现上添加自己需要的数据的方式
 *
 * 直接新增一个支付策略类，并新增一个全局account变量，当调用支付策略的时候，需要将账户account传递过来。
 * @Date：2022/11/2 19:02
 **/
public class StrategyClientUp2 {
    public static void main(String[] args) {
        IPaymentStrategy creditCardStrategy2 = new CreditCardStrategy2("**********");
        PaymentContext paymentContext3 = new PaymentContext("王五", 9000.0, creditCardStrategy2);
        paymentContext3.payNow();
    }
}
