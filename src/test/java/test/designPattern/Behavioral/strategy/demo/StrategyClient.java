package test.designPattern.Behavioral.strategy.demo;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2022/11/2 18:34
 **/
public class StrategyClient {
    public static void main(String[] args) {
        IPaymentStrategy rmbCashStrategy = new RMBCashStrategy();
        PaymentContext paymentContext1 = new PaymentContext("张三", 6000.0, rmbCashStrategy);
        paymentContext1.payNow();

        IPaymentStrategy dollarCashStrategy = new DollarCashStrategy();
        PaymentContext paymentContext2 = new PaymentContext("Jack", 8000.0, dollarCashStrategy);
        paymentContext2.payNow();
    }
}
