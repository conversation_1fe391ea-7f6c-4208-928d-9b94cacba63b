package test;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.DigestUtil;
import org.junit.Test;

import java.security.KeyPair;

public class RightTest {
    public static void main(String[] args) {
        int[] ans = new int[3];//答案
        int[] data = {0,1,2,3,4,5,6,7,8,9};

        int[] yes1 = {2,4,6};//一个号码和位置正确
        int[] oneRight1 = {2,5,8};//号码正确
        int[] oneRight2 = {4,1,9};//号码正确
        int[] noIn = {1,7,4};//不在
        int[] twoRight = {6,9,2};//两个正确
        RightTest test = new RightTest();
        for (int i = 0; i < data.length; i++) {
            ans[0] = data[i];
            for (int i1 = 0; i1 < data.length; i1++) {
                ans[1] = data[i1];
                for (int i2 = 0; i2 < data.length; i2++) {
                    ans[2] = data[i2];

                    for (int an = 0; an < ans.length; an++) {
                        if (test.noInt(ans,noIn)){
                            continue;
                        }
                        if (ans[an]==yes1[an]  && test.contains2(ans,twoRight) && test.contains(ans,oneRight1) &&
                                test.contains(ans,oneRight2)) {
                            System.out.println(ans[0]+","+ans[1]+","+ans[2]);
                        }
                    }

                }
            }
        }



    }

    //判断是否包含切位置不正确
    private boolean contains(int[] data1,int[] data2){

        for (int i = 0; i < data1.length; i++) {
            for (int j = 0; j < data2.length; j++) {
                if (i!=j && data1[i]==data2[j]){
                        return true;
                }
            }
        }
        return false;
    }

    //判断是否包含两个切位置不正确
    private boolean contains2(int[] data1,int[] data2){
        int count = 0;
        for (int i = 0; i < data1.length; i++) {
            for (int j = 0; j < data2.length; j++) {
                if (i!=j && data1[i]==data2[j]){
                        count ++;
                }
            }
        }
        if (count>1){
            return true;
        }
        return false;
    }


    //判断是否包含切位置不正确
    private boolean noInt(int[] data1,int[] data2){
        for (int i = 0; i < data1.length; i++) {
            for (int j = 0; j < data2.length; j++) {
                if (data1[i]==data2[j]){
                    return true;
                }
            }
        }
        return false;
    }
    @Test
    public void testt(){
        String sha256 = DigestUtil.md5Hex16("yz771420975");
         System.out.println(sha256);
    }
}
