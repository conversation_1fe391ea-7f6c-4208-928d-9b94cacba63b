package test.myTest.myUnit;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
/**
 * @Description:
 * @Author: hh
 * @Date: 2025/6/5 21:27
 **/
public class FileCopyUtil {

    static  List<String> fileNames = Arrays.asList(
            "IServiceContentService.java",
            "ServiceContentController.java",
            "ServiceContent.java",
            "ServiceContentTemplate.java"
    );
    public static void main(String[] args) throws IOException {
        String sourceDir = "E:\\study\\code\\Ai\\test\\boot\\PrivateMall\\src\\main\\java\\com\\privatemall";
        String targetDir = "E:/study/code/Ai/test/boot/test";
        copyFilesWithStructure(fileNames, sourceDir, targetDir);
        System.out.println("文件复制完成！");
    }
    public static void copyFilesWithStructure(List<String> fileNames, String sourceDir, String targetDir) throws IOException {
        Set<String> fileNameSet = new HashSet<>(fileNames);
        Path sourcePath = Paths.get(sourceDir);
        Path targetPath = Paths.get(targetDir);
        //遍历字文件夹中文件
        Files.walkFileTree(sourcePath, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes basicFileAttributes) throws IOException {
                String name = file.getFileName().toString();
                // 补尾缀
                if (!name.contains(".")) {
                    name = name + ".java";
                }
                if (fileNameSet.contains(name)) {
                    // 计算相对路径
                    Path relativePath = sourcePath.relativize(file);
                    Path targetFile = targetPath.resolve(relativePath);
                    // 创建目标文件夹
                    Files.createDirectories(targetFile.getParent());
                    // 复制文件
                    Files.copy(file, targetFile, StandardCopyOption.REPLACE_EXISTING);
                }
                return FileVisitResult.CONTINUE;
            }
        });
    }
}