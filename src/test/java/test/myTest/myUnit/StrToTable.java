package test.myTest.myUnit;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import org.junit.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2022/10/21 22:13
 **/
public class StrToTable {

    private String str = "Param\tValue\n" +
            "–name\t容器名（自定义）\n" +
            "–net\t指定容器的网络模式\n" +
            "–ip\t指定容器ip\n" +
            "-v\t挂载宿主机的一个目录。\n" +
            "–privileged\t使容器内的root拥有真正的root权限\n" +
            "–restart\t设置自动重启\n" +
            "-e\t设置容器环境变量\n" +
            "-p\t宿主机端口号 : 容器端口号\n" +
            "-d\t后台运行容器，并返回容器ID\n" +
            "nginx\t镜像名称/镜像ID\n" +
            "————————————————\n";

    @Test
    public void toTable(){
        String[] rows = str.split("\n");//行
        StringBuilder sb = new StringBuilder();
        int headSum = 0;
        sb.append("<table>");
        for (int i = 0; i < rows.length; i++) {
            String row = rows[i];
            sb.append("\n<tr> \n");
            //列
            String[] columns = row.split("\t");
            if (i==0)
                headSum = columns.length;
            if (columns.length<headSum){
                columns = Arrays.copyOf(columns, headSum);
                for (int j = 0; j < columns.length; j++) {
                    if (columns[j]==null)
                        columns[j]="";
                }
            }
            for (String column : columns) {
                String td;

                if (0==i){
                    td = "   <th>"+column+"</th> \n";
                }else {
                    td = "   <td>"+column+"</td> \n";
                }
                sb.append(td);
            }
            sb.append("</tr>");
        }
        sb.append("\n</table>");

        System.out.println(sb.toString());
    }
    private String markDownTableStr = "工厂模式（Factory Pattern）\t产品标准化，生产更高效\t封装创建细节\t实体工厂\tLoggerFactory、Calender\n" +
            "单例模式（Singleton Pattern）\t世上只有一个我\t保证独一无二\tCEO\tBeanFactory、Runtime\n" +
            "原型模式（Prototype Pattern）\t拔一根猴毛，吹出千万个\t高效创建对象\t克隆\tArrayList、PrototypeBean\n" +
            "建造者模式（Builder Pattern）\t高配中配与低配，想选哪配就哪配\t开放个性配置步骤\t选配\tStringBuilder、BeanDefinitionBuilder\n" +
            "代理模式（Proxy Pattern）\t没有资源没时间，得找媒婆来帮忙\t增强职责\t媒婆\tProxyFactoryBean、JdkDynamicAopProxy、CglibAopProxy\n" +
            "门面模式（Facade Pattern）\t打开一扇门，通向全世界\t统一访问入口\t前台\tJdbcUtils、RequestFacade\n" +
            "装饰器模式（Decorator Pattern）\t他大舅他二舅，都是他舅\t灵活扩展、同宗同源\t煎饼\tBufferedReader、InputStream\n" +
            "享元模式（Flyweight Pattern）\t优化资源配置，减少重复浪费\t共享资源池\t全国社保联网\tString、Integer、ObjectPool\n" +
            "组合模式（Composite Pattern）\t人在一起叫团伙，心在一起叫团队\t统一整体和个体\t组织架构树\tHashMap、SqlNode\n" +
            "适配器模式（Adapter Pattern）\t万能充电器\t兼容转换\t电源适配\tAdvisorAdapter、HandlerAdapter\n" +
            "桥接模式（Bridge Pattern）\t约定优于配置\t不允许用继承\t桥\tDriverManager\n" +
            "委派模式（Delegate Pattern）\t这个需求很简单，怎么实现我不管\t只对结果负责\t授权委托书\tClassLoader、BeanDefinitionParserDelegate\n" +
            "模板模式（Template Pattern）\t流程全部标准化，需要微调请覆盖\t逻辑复用\t把大象装进冰箱\tJdbcTemplate、HttpServlet\n" +
            "策略模式（Strategy Pattern）\t条条大道通北京，具体哪条你来定\t把选择权交给用户\t选择支付方式\tComparator、InstantiationStrategy\n" +
            "责任链模式（Chain of Responsibility Pattern）\t各人自扫门前雪，莫管他人瓦上霜\t解耦处理逻辑\t踢皮球\tFilterChain、Pipeline\n" +
            "迭代器模式（Iterator Pattern）\t流水线上坐一天，每个包裹扫一遍\t统一对集合的访问方式\t逐个检票进站\tIterator\n" +
            "命令模式（Command Pattern）\t运筹帷幄之中，决胜千里之外\t解耦请求和处理\t遥控器\tRunnable、TestCase\n" +
            "状态模式（State Pattern）\t状态驱动行为，行为决定状态\t绑定状态和行为\t订单状态跟踪\tLifecycle\n" +
            "备忘录（Memento Pattern）\t失足不成千古恨，想重来时就重来\t备份，后悔机制\t草稿箱\tStateManageableMessageContext\n" +
            "中介者（Mediator Pattern）\t联系方式我给你，怎么搞定我不管\t统一管理网状资源\t朋友圈\tTimer\n" +
            "解释器模式（Interpreter Pattern）\t我想说“方言”，一切解释权归我\t实现特定语法解析\t摩斯密码\tPattern、ExpressionParser\n" +
            "观察者模式（Observer Pattern）\t到点就通知我\t解耦观察者与被观察者\t闹钟\tContextLoaderListener\n" +
            "访问者模式（Visitor Pattern）\t横看成岭侧成峰，远近高低各不同\t解耦数据结构和数据操作\tKPI考核\tFileVisitor、BeanDefinitio";
   @Test
    public void MDtoTable(){
        String[] rows = markDownTableStr.split("\n");//行
        StringBuilder sb = new StringBuilder();
        int headSum = 0;
        for (int i = 0; i < rows.length; i++) {
            String row = rows[i];
            sb.append("\n|");
            //列
            String[] columns = row.split("\t");
            if (i==0)
                headSum = columns.length;
            if (columns.length<headSum){
                columns = Arrays.copyOf(columns, headSum);
                for (int j = 0; j < columns.length; j++) {
                    if (columns[j]==null)
                        columns[j]="";
                }
            }
            for (String column : columns) {
                String td;
                td = column+"|";
                sb.append(td);
            }
        }
        System.out.println(sb);
    }
    String mdToListMsg = "生产者发送消息的流程：1. 生产者连接RabbitMQ，建立TCP连接( Connection)，开启信道（Channel）。2. 生产者声明一个Exchange（交换器），并设置相关属性，比如交换器类型、是否持久化等。3. 生产者声明一个队列井设置相关属性，比如是否排他、是否持久化、是否自动删除等。4. 生产者通过bindingKey （绑定Key）将交换器和队列绑定（ binding ）起来。5. 生产者发送消息至RabbitMQ Broker，其中包含routingKey （路由键）、交换器等信息。6. 相应的交换器根据接收到的routingKey 查找相匹配的队列。7. 如果找到，则将从生产者发送过来的消息存入相应的队列中。8. 如果没有找到，则根据生产者配置的属性选择丢弃还是回退给生产者9. 关闭信道。10. 关闭连接。";
    String listFlag = "。"; //分行标记
    @Test
    public void MDtoList(){
        //去除字符串空格
        mdToListMsg = mdToListMsg.replaceAll("\\s+", "").replaceAll("\\t","");
        StringBuilder sb = new StringBuilder();

        String[] split = mdToListMsg.split(listFlag);
        for (String str : split) {
            int indexOf = str.indexOf(".");
            if (indexOf>0 && StrUtil.isNumeric(str.substring(indexOf-1,indexOf))){
                if (indexOf>3){
                   str = str.substring(0,indexOf-1)+"\n"+str.substring(indexOf-1);
                    indexOf = str.indexOf(".");
                }
                str = StrUtil.replace(str, indexOf , indexOf+1 , ". ");
            }

            sb.append(str).append("。").append("\n");

         }
        System.out.println(sb);
    }
    @Test
    public void tryTest (){
        try {
            int result = 10 / 0; // 除数为0, 这将抛出ArithmeticException

        } catch (ArithmeticException e) {
            System.out.println("捕获到了除以零的异常");
        }
        // 如果异常被上面的catch块捕获并处理, 下面的代码将会执行
        System.out.println("catch块之后的代码");
    }

}
