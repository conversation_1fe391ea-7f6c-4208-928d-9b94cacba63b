package test.myTest.myUnit.mytest;

import org.junit.Test;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2022/10/18 23:03
 **/
public class MoneyTest {


    @Test
    public void test(){
        int money = 455555;
        StringBuilder sb = new StringBuilder(7*2);
        for (int i = 8; i >= 1 ; i--) {

            int temp = 1;
            for (int j = 0; j < i-1; j++) {
                temp *= 10;
            }
            String flag = this.getFlag(i);
            String str = this.getChines((money / temp) % 10);
            sb.append(str).append(flag);
        }
        System.out.println(sb.toString());
    }
    public String getFlag(int number){
        String[] arr = {"元","十","百","千","万","十万","百万","千万"};
        return arr[number-1];
    }
    public String getChines(int number){
        String[] arr = {"零","一","二","三","四","五"};
        return arr[number];
    }
}
