package test.myTest.myUnit.mytest;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.Test;

import java.util.*;

public class TimeTest {

    @Test
    public void forTimeTest(){

        ArrayList<Integer> list = new ArrayList<Integer>( );
        for (int i = 0; i < 1000000; i++) {
            list.add(i);
        }
        long startTime1 = System.currentTimeMillis();
        for (int i = 0; i < list.size(); i++) {

        }
        long endTime1 = System.currentTimeMillis();
        System.out.println(endTime1-startTime1);

        long startTime2 = System.currentTimeMillis();
        long endTime2 = System.currentTimeMillis();
        System.out.println(endTime2-startTime2);

        String str = "1,2,3,,";
        String[] split = str.split(",");
        for (String s : split) {
            System.out.println("--"+s);
        }
    }
    @Test
    public void gsonTest(){
        Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-DD hh:mm:ss").create();
        HashMap<String,String > map = gson.fromJson("{\"实际审批金额\":\"500;800;\",\"内容与用途\":\"办公用品采购;出差费用;\",\"申请金额\":\"500;800;\",\"费用项目\":\"办公用品;管理差旅费;\"}", HashMap.class);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            System.out.println("key:"+entry.getKey()+"val:"+entry.getValue());
        }
    }
    @Test
    public void dateTest(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date start = calendar.getTime();
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date end = calendar.getTime();

        System.out.println(start);
        System.out.println(end);
    }

    @Test
    public void getDifferDate(){
        Calendar calendar  = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Integer xc = 0;
        long xcc= calendar.getTime().getTime()+xc*24*60*60*1000L;
        Date date = new Date(xcc);
        System.out.println(date);
    }
}
