package test.myTest.Out;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import test.myTest.Out.bean.FieldBean;
import test.myTest.Out.bean.FieldType;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public class OutTest {

     

    public static void main(String[] args) {
        HashMap<String,Object> jsonMap = JSONObject.parseObject(json, HashMap.class);

        //成功码 code
        Integer code = 200;
        for (Map.Entry<String, Object> entry : jsonMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            //一般都有code
            if (key.toLowerCase().equals("code") ){
                if (code==Integer.parseInt(value.toString())){
                    //成功后 开始转换

                }else {
                    System.out.println("请求失败了");
                }


            }
        }
    }

    /**
     * 判断为空
     * @param data
     * @return
     */
    private boolean isNull(Object data){
        if (data==null)
            return true;
        else if (StrUtil.isEmptyIfStr(data))
            return true;
        else if (data instanceof Map && MapUtil.isEmpty((Map)data))
            return true;

        return false;
    }
    /*public Map changeData2(Map<String,Object> sourceData,Map<String,Object> toData,List<FieldBean> fieldBeanList) throws Exception{
        for (FieldBean fieldBean : fieldBeanList) {
            int type = fieldBean.getToType();
            Object sourceValue = sourceData.get(fieldBean.getSourceFiledName());
            Object toValue;
            if (fieldBean.getLevel() && CollUtil.isNotEmpty(fieldBean.getChildList()) && sourceValue!=null){
                List<FieldBean> childList = fieldBean.getChildList();
                //有下级
                if (type== FieldType.list_fieldType && ((List)sourceValue).size()>0){
                    if (!(sourceValue instanceof List)){
                        throw new RuntimeException();
                    }
                    List<FieldBean> fieldBeanChildList = fieldBean.getChildList();
                    List<Map> sourceValueList = Convert.toList(Map.class, sourceValue);
                    ArrayList<Map> toValueList = new ArrayList<>(sourceValueList.size());
                    toData.put(fieldBean.getToFiledName(),toValueList);
                    for (int i = 0; i < fieldBeanChildList.size(); i++) {
                        HashMap<String, Object> childValue = new HashMap<>();
                        toValueList.add(childValue);
                        return changeData2(sourceValueList.get(i),childValue,fieldBeanChildList);
                    }
                }else if (type== FieldType.map_fieldType && sourceValue instanceof Map && ((Map)sourceValue).size()>0){
                    Map<String,Object> map = (Map)sourceValue;
                    toValue = changeData2(map, childList);
                }else if (type==FieldType.constant_type){
                    toValue = fieldBean.getTypeValue();
                }

                putData(values,fieldBean,toData);
            }

        }
        return null;
    }*/
    public Map changeData(Map data,List<FieldBean> fieldBeanList){
        Map<String,Object> values = MapUtil.newHashMap(data.size());
        for (FieldBean fieldBean : fieldBeanList) {
            int type = fieldBean.getToType();
            Object sourceData = data.get(fieldBean.getSourceFiledName());
            Object toData = null;
            if (fieldBean.getLevel() && CollUtil.isNotEmpty(fieldBean.getChildList()) && sourceData!=null){
                List<FieldBean> childList = fieldBean.getChildList();
                //有下级
                if (type== FieldType.list_fieldType && sourceData instanceof List && ((List)sourceData).size()>0){
                    List<Map> list = Convert.toList(Map.class, sourceData);
                    List dataList = CollUtil.newArrayList();
                    for (Map map : list) {
                        Map result = changeData(map, childList);
                        dataList.add(result);
                    }
                    toData = dataList;
                }else if (type== FieldType.map_fieldType && sourceData instanceof Map && ((Map)sourceData).size()>0){
                    Map<String,Object> map = (Map)sourceData;
                    toData = changeData(map, childList);
                }else if (type==FieldType.constant_type){
                    toData = fieldBean.getTypeValue();
                }

                putData(values,fieldBean,toData);
            }

        }
        return null;
    }

    public void putData(Map data,FieldBean fieldBean,Object toValue){
        int type = fieldBean.getToType();
        if (toValue==null){
            return;
        }
        if (type==FieldType.integer_fieldType && Pattern.compile("^[-\\+]?[\\d]*$").matcher(toValue.toString()).matches()){
            toValue = Integer.parseInt(toValue.toString());
        }else if (type==FieldType.bigDecimal_fieldType && Pattern.compile("^[-\\+]?[\\d]*$").matcher(toValue.toString()).matches()){
            toValue = new BigDecimal(Integer.parseInt(toValue.toString())).setScale(2,BigDecimal.ROUND_HALF_UP);
        }else if (type==FieldType.boolean_fieldType){
            toValue = Boolean.valueOf(toValue.toString());
        }else if (type==FieldType.json_fieldType){
            toValue= JSONObject.toJSONString(toValue);
        }else if (type==FieldType.string_fieldType){
            toValue = toValue.toString();
        }
        data.put(fieldBean.getToFiledName(),toValue);
    }


    public Object change(String outDetId,Object data){
        List<FieldBean> fieldBeanList = getFieldInfo(outDetId);
        //分类处理
        /*fieldBeanList.stream().collect(Collectors.groupingBy());*/
        return null;
    }

    /**
     * 获取字段关系
     * @param outDetId
     * @return
     */
    public List<FieldBean> getFieldInfo(String outDetId){
        ArrayList<FieldBean> list = new ArrayList<>();
        return list;
    }


    static String json =  "  {\n" +
            "            \"data\": {\n" +
            "            \"access_token\": \"37126f15684ee5580e4098f7d9abe5341016\",\n" +
            "                    \"expires_in\": 2592000,\n" +
            "                    \"scope\": \"basic push report\",\n" +
            "                    \"refresh_token\": \"5ef8ff9dc0abb693ffc0bed6affa9912\",\n" +
            "                    \"create_time\": *************,\n" +
            "                    \"nodeCode\": \"mh\",\n" +
            "                    \"extra\": {\n" +
            "                \"userId\": 486,\n" +
            "                        \"userName\": \"kafka\",\n" +
            "                        \"userType\": 1,\n" +
            "                        \"enabledPassPort\": false,\n" +
            "                        \"hasMoreAccount\": false\n" +
            "            }\n" +
            "        },\n" +
            "            \"code\": 200,\n" +
            "                \"message\": \"操作成功\"\n" +
            "        }";
}
