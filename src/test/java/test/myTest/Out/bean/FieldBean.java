package test.myTest.Out.bean;

import java.util.List;

public class FieldBean {


   /**类型  0 normal 1request请求获取数据 2枚举 3常量*/
    private int type;
    private String typeValue;//request时存 请求名 4 枚举名 常量值

/**字段类型 0未知 1 Int 2 BigDecimal 3 String 4boolean 5json 6List 7Map */
/*    private int sourceType;//获取的字段类型*/
    private String sourceFiledName;//获取的字段名
    private int toType;
    private String toFiledName;


    private boolean level;//是否有下级 0无 1有
    private List<FieldBean> childList;

    public List<FieldBean> getChildList() {
        return childList;
    }

    public void setChildList(List<FieldBean> childList) {
        this.childList = childList;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getSourceFiledName() {
        return sourceFiledName;
    }

    public void setSourceFiledName(String sourceFiledName) {
        this.sourceFiledName = sourceFiledName;
    }

    public int getToType() {
        return toType;
    }

    public void setToType(int toType) {
        this.toType = toType;
    }

    public String getToFiledName() {
        return toFiledName;
    }

    public void setToFiledName(String toFiledName) {
        this.toFiledName = toFiledName;
    }


    public boolean getLevel() {
        return level;
    }

    public void setLevel(boolean level) {
        this.level = level;
    }

    public String getTypeValue() {
        return typeValue;
    }

    public void setTypeValue(String typeValue) {
        this.typeValue = typeValue;
    }
}
