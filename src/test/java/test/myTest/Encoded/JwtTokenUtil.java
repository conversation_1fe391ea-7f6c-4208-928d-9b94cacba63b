package test.myTest.Encoded;

import javax.crypto.Cipher;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * RSA时间混淆
 * <AUTHOR>
 * @date 2023/6/13 16:09
 */
public class JwtTokenUtil {

    // 生成 RSA 密钥对
    public static Map<String, String> generateKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        PrivateKey privateKey = keyPair.getPrivate();
        PublicKey publicKey = keyPair.getPublic();

        Map<String, String> keyMap = new HashMap<>();
        keyMap.put("privateKey", Base64.getEncoder().encodeToString(privateKey.getEncoded()));
        keyMap.put("publicKey", Base64.getEncoder().encodeToString(publicKey.getEncoded()));

        return keyMap;
    }

    // 使用公钥加密（包含时间戳）
    public static String encryptByPublicKeyWithTimestamp(String data, String publicKey) throws Exception {
        // 添加当前时间戳
        long timestamp = System.currentTimeMillis();
        String dataWithTimestamp = data + ":" + timestamp;

        byte[] keyBytes = Base64.getDecoder().decode(publicKey);
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        cipher.init(Cipher.ENCRYPT_MODE, KeyFactory.getInstance("RSA").generatePublic(new java.security.spec.X509EncodedKeySpec(keyBytes)));
        byte[] encryptedData = cipher.doFinal(dataWithTimestamp.getBytes());
        return Base64.getEncoder().encodeToString(encryptedData);
    }

    // 使用私钥解密并验证时间戳
    public static String decryptByPrivateKeyWithTimestamp(String encryptedData, String privateKey) throws Exception {
        // 解码 Base64 编码的私钥字符串为字节数组
        byte[] keyBytes = Base64.getDecoder().decode(privateKey);
        // 创建 Cipher 实例并指定使用 "RSA/ECB/PKCS1Padding" 加密模式
        Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
        // 使用 KeyFactory 生成对应的私钥对象
        cipher.init(Cipher.DECRYPT_MODE, KeyFactory.getInstance("RSA").generatePrivate(new PKCS8EncodedKeySpec(keyBytes)));
        // 对 Base64 编码的加密数据进行解密
        byte[] decryptedData = cipher.doFinal(Base64.getDecoder().decode(encryptedData));
        // 将解密后的字节数组转换为字符串
        String decrypted = new String(decryptedData);

        // 拆分数据与时间戳
        String[] parts = decrypted.split(":");
        if (parts.length < 2) {
            throw new IllegalArgumentException("Decrypted data is invalid.");
        }

        String originalData = parts[0];
        long timestamp = Long.parseLong(parts[1]);

        // 验证时间戳是否在允许范围内（如5分钟内）
        long currentTime = System.currentTimeMillis();
        if (currentTime - timestamp > 5 * 60 * 1000) { // 5分钟有效期
            throw new RuntimeException("Timestamp expired, possible replay attack.");
        }

        return originalData;
    }


    public static void main(String[] args) throws Exception {
        // 生成密钥对
        Map<String, String> keyMap = generateKeyPair();
        String publicKey = keyMap.get("publicKey");
        String privateKey = keyMap.get("privateKey");

        System.out.println("公钥: " + publicKey);
        System.out.println("私钥: " + privateKey);

        // 要加密的数据
        String originalData = "Hello, this is a secret message!";

        // 公钥加密
        String encryptedData = encryptByPublicKeyWithTimestamp(originalData, publicKey);
        System.out.println("加密后的数据: " + encryptedData);

        // 私钥解密
        String decryptedData = decryptByPrivateKeyWithTimestamp(encryptedData, privateKey);
        System.out.println("解密后的数据: " + decryptedData);

        // 验证结果
        System.out.println("原始数据与解密数据是否一致: " + originalData.equals(decryptedData));
    }
}
