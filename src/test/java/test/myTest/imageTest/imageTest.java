package test.myTest.imageTest;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Random;

/**
 * <AUTHOR>
 * @Description 绘制验证码
 * @Date 2022/10/28 15:13
 * @Param
 * @return
 **/
public class imageTest {

    public static void main(String[] args) throws IOException {
        int bufferHigh = 50;
        int bufferWith = 120;
        BufferedImage bufferedImage = new BufferedImage(bufferWith,bufferHigh,BufferedImage.TYPE_3BYTE_BGR);
        //画笔
        Graphics graphics = bufferedImage.getGraphics();
        graphics.setColor(Color.white);
        graphics.fillRect(0,0,bufferWith,bufferHigh);

        graphics.setColor(Color.BLACK);

        String[] values = "0123456789ABCDEFG".split("");
        Random random = new Random();
        for (int i = 0; i < 4; i++) {
            int index = random.nextInt(values.length);
            graphics.drawString(values[index],10+30*i,30);
        }
        ImageIO.write(bufferedImage,"jpg",new File("D://mytest//test.jpg"));

    }
}
