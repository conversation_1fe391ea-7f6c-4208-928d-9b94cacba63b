package test.myTest.mybatis;

import boot.MyBootApplication;
import boot.test.modules.order.mapper.OrderMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.Map;

/**
 * @Description：
 * @Author：hh
 * @Date：2025/6/23 02:30
 **/
@SpringBootTest(classes = MyBootApplication.class)
public class Test1 {

    @Autowired
    public OrderMapper orderMapper;

    @Test
    public void test() {
        Map<String, Long> map = orderMapper.selectTest();
        for (Map.Entry<String, Long> entry : map.entrySet()) {
            System.out.println(entry.getKey() + ":" + entry.getValue());
        }
        System.out.println(map.get("黄宇宁"));
    }
}
