package test.myTest.gson;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.junit.Test;

import java.util.HashMap;

public class GsonTest {
    Gson gson = new GsonBuilder().create();
    @Test
    public void test1(){
        String str = "{code:0,data:[{data1:t,data2:a},{data1:a,data2:b}]}";
        HashMap map = gson.fromJson(str, HashMap.class);
        map.get("code");
    }

    @Test
    public void SQLTest(){
        String sql = "AbcdDDw";
        System.out.println(sql.toLowerCase());
        System.out.println(sql);
    }
}
