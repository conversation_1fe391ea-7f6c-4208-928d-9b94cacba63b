package test;


public class ContainsTest {
    public static void main(String[] args) {
       /* String[] timeSqls = {"where  dateDiff(day,CONVERT(datetime, b.<PERSON><PERSON>ate, 120),getdate()) = 1",
                "where  dateDiff(day,CONVERT(datetime, b.<PERSON><PERSON><PERSON>, 120),getdate()) <= 7",
                "where  dateDiff(day,CONVERT(datetime, b.BillDate, 120),getdate()) <= 30"};
        for (String timeSql: timeSqls) {
            if (timeSql.contains("= 1")){
                System.out.println("1");
            }else if (timeSql.contains("<= 7"))
                System.out.println("7");
            else
                System.out.println("30");
        }*/
        String joinSql  = "(select GoodsNumber as Number,GoodsFullName as FullName,classCode," +
                "isCatalog from tblGoods where workFlowNodeName= 'finish' ) as a on b.GoodsCode like a.classCode+'%' ";
        String tableName = joinSql.substring(joinSql.indexOf("from") + "from".length(), joinSql.indexOf("where")).trim();
        System.out.println(tableName);
    }
}
