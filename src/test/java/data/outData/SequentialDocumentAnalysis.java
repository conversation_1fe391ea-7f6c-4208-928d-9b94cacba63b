package data.outData;

import java.util.*;
import java.util.stream.Collectors;

public class SequentialDocumentAnalysis {
    public static void main(String[] args) {
        // 输入数据
        List<Map<String, Object>> data = Arrays.asList(
                createEntry(1, "安装电车充电桩申请", "项目非经营用章", 1, "安装电车充电桩申请", "21-402业主车位B1374"),
                createEntry(2, "居住证明", "项目非经营用章", 1, "居住证明", "6-2-2601"),
                createEntry(1, "南宁恒大华府项目使用柴油/汽油储备金台账", "物业服务中心“非经营用章”",
                        1, "南宁恒大华府使用汽油储备金申请（保洁四害消杀弥雾机及园林打药机、推草机使用）", ""),
                createEntry(1, "关于规范物业各类费用缴费渠道的公示", "项目非经营用章", 13, "张贴二期楼栋大堂", ""),
                createEntry(1, "安装电车充电桩申请", "项目非经营用章", 2, "安装电车充电桩申请", "19-1405业主车位B1631"),
                createEntry(1, "装修许可证", "项目非经营用章", 1, "用于5-2-701装修许可证盖章", ""),
                createEntry(1, "物业服务费催缴函", "项目非经营用章", 16, "用于16户物业服务费催缴函盖章", ""),
                createEntry(1, "缴费渠道表", "项目非经营用章", 16, "用于缴费渠道表张贴盖章", ""),
                createEntry(2, "催缴函", "项目非经营章", 11, "用于4-2502等催缴函盖章", ""),
                createEntry(1, "装修许可证", "项目非经营用章", 1, "用于7-2-801装修许可证盖章", ""),
                createEntry(1, "装修许可证", "项目非经营用章", 1, "用于8-1-1501装修许可证盖章", ""),
                createEntry(1, "车位车牌", "南宁恒大华府物业服务中心非经营用章", 2, "粤BB49H9、粤BTB161车牌盖章", "")
        );

        // 按序号合并相邻记录
        List<Map<String, Object>> mergedData = mergeSequentialRecords(data);

        // 输出合并结果
        for (Map<String, Object> mergedDatum : mergedData) {
            System.out.println(mergedDatum.get("文件资料名称"));
        }

        for (Map<String, Object> mergedDatum : mergedData) {
            System.out.println(mergedDatum.get("用印份数"));
        }
        for (Map<String, Object> mergedDatum : mergedData) {
            System.out.println(mergedDatum.get("用印事由"));
        }
    }

    // 合并相邻序号的记录
    private static List<Map<String, Object>> mergeSequentialRecords(List<Map<String, Object>> data) {
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> currentGroup = null;

        for (Map<String, Object> entry : data) {
            if (currentGroup == null) {
                // 初始化第一组
                currentGroup = new LinkedHashMap<>(entry);
            } else {
                // 提取当前和上一组的序号
                String previousSerialString = currentGroup.get("序号").toString();
                int previousSerial = Integer.parseInt(previousSerialString.split("、")[previousSerialString.split("、").length - 1]); // 获取最后一个序号
                int currentSerial = (int) entry.get("序号");

                if (currentSerial == previousSerial + 1) {
                    // 合并当前记录到现有组
                    currentGroup.put("序号", previousSerialString + "、" + currentSerial); // 合并序号
                    currentGroup.put("文件资料名称", currentGroup.get("文件资料名称") + "、" + entry.get("文件资料名称")); // 合并用印事由
                    currentGroup.put("用印份数", (int) currentGroup.get("用印份数") + (int) entry.get("用印份数")); // 累加复印份数
                    currentGroup.put("用印事由", currentGroup.get("用印事由") + "、" + entry.get("用印事由")); // 合并用印事由
                } else {
                    // 不连续，保存当前组并开始新组
                    result.add(currentGroup);
                    currentGroup = new LinkedHashMap<>(entry);
                }
            }
        }

        // 保存最后一组
        if (currentGroup != null) {
            result.add(currentGroup);
        }
        return result;
    }

    // 创建数据条目
    private static Map<String, Object> createEntry(int serial, String name, String sealName, int copies, String reason, String remarks) {
        Map<String, Object> entry = new LinkedHashMap<>();
        entry.put("序号", serial);
        entry.put("文件资料名称", name);
        entry.put("印章名称", sealName);
        entry.put("用印份数", copies);
        entry.put("用印事由", reason);
        entry.put("备注", remarks);
        return entry;
    }
}
