package boot;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2022/10/28 23:28
 **/
@SpringBootApplication
@EnableCaching //启动缓存
@EnableAspectJAutoProxy //启动切面自动代理
@MapperScan("boot.test.*.mapper")
@MapperScan("boot.test.*.*.mapper")
public class MyBootApplication  {
    public static void main(String[] args) {

        ApplicationContext run= new SpringApplication(MyBootApplication.class).run(args);
    }


}
