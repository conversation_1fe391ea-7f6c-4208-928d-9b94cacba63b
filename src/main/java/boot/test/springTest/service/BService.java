package boot.test.springTest.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description：
 * @Author：hh
 * @Date：2024/6/8 上午9:52
 **/
@Service
public class BService {
/*    private AService aService;
    private CService cService;

    @Autowired
    public BService ( AService aService,CService cService) {
        this.aService = aService;
        this.cService = cService;
    }*/
}
