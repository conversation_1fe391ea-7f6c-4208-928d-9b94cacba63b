package boot.test.springTest.service;

import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;

/**
 * @Description：
 * @Author：hh
 * @Date：2024/6/14 上午1:18
 **/
public class RabbitMqListener {

    @RabbitListener(queues = "queue1" )
    public void queue1(String data){
        System.out.println(data);
    }
    @RabbitListener(queues = "queue2")
    public void queue2(String data){
        System.out.println(data);
    }
    @RabbitListener(bindings =  @QueueBinding(
            value = @Queue( name= "direct.queue1",durable = "true"),
            exchange = @Exchange(name= "myExchange.direct",type =  ExchangeTypes.DIRECT),
            key = {"red","blue"}
    ))
    public void directQueue1(String data){
        System.out.println(data);
    }
}
