package boot.test.springTest.service.impl;

import boot.test.modules.order.entity.Order;
import boot.test.modules.order.mapper.OrderMapper;
import boot.test.springTest.service.TestService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2023/5/24 1:10
 **/
@Service
public class TestServiceImpl extends ServiceImpl<OrderMapper, Order>implements TestService{

    @Override
    public String getData(String string) {
        return string;
    }
}
