package boot.test.springTest.aop;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpInputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.ParameterizedType;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map; // 添加Map的导入语句

@Aspect
@Component
@Order(1) // 切面执行顺序，数值越小优先级越高
public class ShopIdInterceptorAspect {
    private static final Logger logger = LoggerFactory.getLogger(ShopIdInterceptorAspect.class);

    // 定义切点：拦截所有控制器方法（可根据包路径自定义）
    @Pointcut("execution(public * boot.test.springTest.controller.TestController*.*(..))")
    public void controllerPointcut() {}

    @Around("controllerPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取请求对象
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        // 打印请求信息
        logger.info("请求URL：{}", request.getRequestURL());
        logger.info("请求方法：{}", request.getMethod());

        // 获取JSON请求体
        String jsonData = getRequestJsonData(request);
        if (jsonData != null) {
            logger.info("请求JSON数据：{}", jsonData);
        }

        // 执行原方法
        Object result = joinPoint.proceed();
        logger.info("响应结果：{}", result);

        return result;
    }

    // 解析请求体中的JSON数据
    private String getRequestJsonData(HttpServletRequest request) {
        try {
            // 读取请求体输入流
            InputStream inputStream = request.getInputStream();
            if (inputStream.available() == 0) {
                return null;
            }

            // 使用Spring的JSON转换器解析
            HttpInputMessage inputMessage = new HttpInputMessage() {
                @Override
                public InputStream getBody() throws IOException {
                    return inputStream;
                }

                @Override
                public HttpHeaders getHeaders() {
                    HttpHeaders headers = new HttpHeaders();
                    // 设置请求头（根据实际情况调整）
                    headers.setContentType(MediaType.APPLICATION_JSON);
                    return headers;
                }
            };

            List<HttpMessageConverter<Object>> converters = new ArrayList<>();
            converters.add(new MappingJackson2HttpMessageConverter());

            // 解析JSON为Object（可根据需要转换为具体类型）
            Object obj = converters.stream()
                    .filter(converter -> converter.canRead(Object.class, inputMessage.getHeaders().getContentType()))
                    .findFirst()
                    .map(converter -> {
                        try {
                            // 打印输入消息的内容以便调试
                            InputStream body = inputMessage.getBody();
                            byte[] bodyBytes = new byte[body.available()];
                            body.read(bodyBytes);
                            logger.info("Input message content: {}", new String(bodyBytes, StandardCharsets.UTF_8));

                            // 修改为具体的类
                            logger.warn("Target type is not parameterized, using Map.class");
                            return converter.read(Object.class, inputMessage); // 使用默认的Map.class
                        } catch (IOException e) {
                            logger.error("解析JSON失败", e);
                            return null;
                        }
                    })
                    .orElse(null);

            return obj != null ? obj.toString() : null;
        } catch (Exception e) {
            logger.error("获取请求JSON数据失败", e);
            return null;
        }
    }
}
