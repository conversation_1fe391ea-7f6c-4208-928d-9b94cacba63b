package boot.test.springTest.aop;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2023/5/24 0:45
 **/
@Component
@Aspect
public class AopTest {

    @Pointcut("@annotation(boot.test.springTest.annotation.AutoLog)")
    public void autoLog(){
    }


    @Around("autoLog()")
    public Object around(ProceedingJoinPoint point) throws Throwable {
        long beginTime = System.currentTimeMillis();
        //执行方法
        Object result = point.proceed();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;
        System.out.println(result);
        System.out.println(time);
        return result;
    }
}
