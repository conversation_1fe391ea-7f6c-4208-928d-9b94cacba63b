package boot.test.springTest.aop;

import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2023/5/24 1:33
 **/
@Component
@Aspect
public class AopTest2 {
    @Pointcut("execution(* boot.service.*.*(..))")
    public void myServiceTest(){};

    @After("myServiceTest()")
    public void getData() throws Throwable {
        long beginTime = System.currentTimeMillis();
        //执行时长(毫秒)
        long time = System.currentTimeMillis() - beginTime;
        System.out.println("After");

    }
}
