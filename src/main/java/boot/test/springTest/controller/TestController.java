package boot.test.springTest.controller;

import boot.test.redis.annotation.RedisAutoByParams;
import boot.test.springTest.annotation.AutoLog;

import boot.test.springTest.service.TestService;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.amqp.core.MessageBuilder;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2023/5/24 0:52
 **/
@RequestMapping("/test")
@RestController
public class TestController {

    private final String key = "test";

    @Qualifier("testServiceImpl2")
    @Autowired
    TestService testService;

    @PostMapping("/testShopId")
    public void getData(@RequestBody String data){
        System.out.println(data);

    }
    @AutoLog("ttt")
    @RedisAutoByParams(key = key,expire = 60)
    @GetMapping("/getData")
    public String getData(){
        String data = testService.getData("123456");
        return data;
    }

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @GetMapping("/sendMQ")
    public String sendMQ(@RequestParam String data){
        String queueName = "queue1";
        rabbitTemplate.convertAndSend(queueName,data);
        MessageBuilder.withBody(data.getBytes()).setDeliveryMode(MessageProperties.DEFAULT_DELIVERY_MODE);//非持久
        return "OK";
    }

    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Autowired
    private DataSource dataSource;

    @PostConstruct
    public void test() throws SQLException {
        Connection connection = dataSource.getConnection();
        System.out.println(connection);
    }
}
