package boot.test.modules.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName my_order
 */
@TableName(value ="my_order")
@Data
public class Order {
    /**
     * 
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    @TableField(value = "bill_no")
    private String billNo;

    /**
     * 
     */
    @TableField(value = "bill_date")
    private Date billDate;

    /**
     * 
     */
    @TableField(value = "customer_id")
    private String customerId;

    /**
     * 
     */
    @TableField(value = "total_amount")
    private BigDecimal totalAmount;

    /**
     * 
     */
    @TableField(value = "status")
    private String status;
}