package boot.test.modules.order.mapper;

import boot.test.modules.order.entity.Order;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【my_order】的数据库操作Mapper
* @createDate 2025-03-27 02:48:05
* @Entity springTest.boot/springTest/gemerate.MyOrder
*/
@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    @MapKey("customer_id")
    Map<String, Long > selectTest();

}




