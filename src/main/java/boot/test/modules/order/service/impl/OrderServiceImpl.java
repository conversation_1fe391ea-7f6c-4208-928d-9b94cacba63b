package boot.test.modules.order.service.impl;

import boot.test.modules.order.entity.Order;
import boot.test.modules.order.mapper.OrderMapper;
import boot.test.modules.order.service.OrderService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【my_order】的数据库操作Service实现
* @createDate 2025-03-27 02:48:05
*/
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order>
    implements OrderService {

}




