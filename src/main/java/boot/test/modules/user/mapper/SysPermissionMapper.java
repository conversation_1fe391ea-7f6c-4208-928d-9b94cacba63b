package boot.test.modules.user.mapper;

import boot.test.modules.user.entity.SysPermission;
import boot.test.modules.user.entity.SysUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_permission(权限表)】的数据库操作Mapper
* @createDate 2025-03-28 02:30:57
* @Entity boot.test.modules.user.entity.SysPermission
*/
@Mapper
public interface SysPermissionMapper extends BaseMapper<SysPermission> {
    List<SysPermission> getUserRolesByUserId(String username);
}




