package boot.test.modules.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 用户表
 * @TableName sys_user
 */
@TableName(value ="sys_user")
@Data
public class SysUser implements Serializable {
    /**
     *
     */
    @TableId(value = "id")
    private String id;

    /**
     * 账号
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 用户名
     */
    @TableField(value = "nick_name")
    private String nickName;

    @TableField(value = "mobile_number")
    private String mobileNumber;

    /**
     * 用户密码
     */
    @TableField(value = "password")
    private String password;

    /**
     * 最后一次登录时间
     */
    @TableField(value = "last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 账号是否可用。默认为1（可用）
     */
    @TableField(value = "enabled")
    private Integer enabled;

    /**
     * 是否过期。默认为1（没有过期）
     */
    @TableField(value = "not_expired")
    private Integer notExpired;

    /**
     * 账号是否锁定。默认为1（没有锁定）
     */
    @TableField(value = "account_not_locked")
    private Integer accountNotLocked;

    /**
     * 证书（密码）是否过期。默认为1（没有过期）
     */
    @TableField(value = "credentials_not_expired")
    private Integer credentialsNotExpired;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_user")
    private String createUser;

    /**
     * 修改人
     */
    @TableField(value = "update_user")
    private String updateUser;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}