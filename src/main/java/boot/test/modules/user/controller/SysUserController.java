package boot.test.modules.user.controller;

import boot.test.common.entity.JsonResult;
import boot.test.modules.user.entity.SysUser;
import boot.test.modules.user.service.SysUserService;
import boot.test.redis.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/sysUser")
public class SysUserController {

    @Autowired
    SysUserService sysUserService;

    @Autowired
    PasswordEncoder passwordEncoder;

    @Autowired
    AuthenticationManager authenticationManager;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 获取用户列表
     * @return
     */
    @PostMapping("/getUser")
    @PreAuthorize("hasAuthority('sys:user:list')")
    public JsonResult list(){
        List<SysUser> list = sysUserService.list();
        return new JsonResult(true, list);
    }


    /**
     * 删除用户
     * @return
     */
    @PostMapping("/deleteUser")
    @PreAuthorize("hasAuthority('sys:user:delete')")
    public JsonResult deleteUser(){
        List<SysUser> list = sysUserService.list();
        return new JsonResult(true, list);
    }


    /**
     * 注册用户
     * @param sysUser
     * @return
     */
    @PostMapping("/signUp")
    public JsonResult signUp(SysUser sysUser){
        String password = passwordEncoder.encode(sysUser.getPassword());
        sysUser.setPassword(password);
        sysUserService.save(sysUser);
        return new JsonResult(true, sysUser);
    }
/*    @PostMapping("/login")
    public JsonResult<String> login( SysUser sysUser) {

       //使用AuthenticationManager的认证接口惊醒用户认证
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(sysUser.getUserName(), sysUser.getPassword());
        Authentication authenticate = authenticationManager.authenticate(authenticationToken);
        if (authenticate == null) {
            throw new RuntimeException("登录失败");
        }
        // 认证成功则通过jwt创建token
        LoginUser loginUser = (LoginUser) authenticate.getPrincipal();
        String token = JwtUtils.getJwtToken(loginUser.getSysUser().getId());

        // 把用户信息存入到redis
        String key = CacheConst.LOGIN_USER_KEY + loginUser.getSysUser().getId();
        if (redisUtil.get(key)!=null) {
            redisUtil.delete(key);
        }
        redisUtil.set(key, loginUser,60*60);
//        User user = (User)redisUtil.get("token_" + loginUser.getUser().getId());
        return JsonResult.success("tt");

    }*/
    /**
     * 获取当前在线用户
     * @return
     */
    @PostMapping("/onlineUser")
    @PreAuthorize("hasAuthority('sys:user:online')")
    public JsonResult onlineUser(){
        List<SysUser> userList = sysUserService.onlineUser();
        if (userList == null){
            JsonResult.success("当前无登录用户");
        }
        return JsonResult.success(userList);
    }

    @PostMapping("/hello")
    public JsonResult hello(){
        return JsonResult.success("hello");
    }
}
