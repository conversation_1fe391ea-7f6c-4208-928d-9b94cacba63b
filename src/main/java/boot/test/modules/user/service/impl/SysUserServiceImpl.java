package boot.test.modules.user.service.impl;



import boot.test.auth.dto.LoginUser;
import boot.test.common.constant.CacheConst;
import boot.test.modules.user.entity.SysUser;
import boot.test.modules.user.mapper.SysUserMapper;
import boot.test.modules.user.service.SysUserService;
import boot.test.redis.util.RedisUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【sys_user(用户表)】的数据库操作Service实现
* @createDate 2025-03-28 02:23:30
*/
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser>
    implements SysUserService {

    @Autowired
    RedisUtil redisUtil;

    public List<SysUser> onlineUser() {
        //获取redis所有在线用户key值、

        Set<String> keys =  redisUtil.getByType("*" + CacheConst.LOGIN_USER_KEY + "*");
        if (keys == null){
            return null;
        }

        List<SysUser> userList = new ArrayList<>();
        keys.stream().forEach( key -> {
            LoginUser loginUser = (LoginUser) redisUtil.get(key);
            userList.add(loginUser.getSysUser());
        });
        return userList;
    }
}




