package boot.test.modules.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import boot.test.modules.user.entity.SysPermission;
import boot.test.modules.user.service.SysPermissionService;
import boot.test.modules.user.mapper.SysPermissionMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;

import java.util.*;

/**
* <AUTHOR>
* @description 针对表【sys_permission(权限表)】的数据库操作Service实现
* @createDate 2025-03-28 02:30:57
*/
@Service
public class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper, SysPermission>
    implements SysPermissionService{
    @Autowired
    private SysPermissionMapper sysPermissionMapper;
    public List<GrantedAuthority> getUserRolesByUserId(String id) {
        // 获取用户权限
        List<SysPermission> sysPermissions = sysPermissionMapper.getUserRolesByUserId(id);
        // 将用户权限存入UserDetails的authorities中，以便Spring Security进行权限判断
        List<GrantedAuthority> grantedAuthorities = new ArrayList<>();
        sysPermissions.stream().forEach(sysPermission -> {
            GrantedAuthority grantedAuthority = new SimpleGrantedAuthority(sysPermission.getPermissionCode());
            grantedAuthorities.add(grantedAuthority);
        });
        return grantedAuthorities;
    }
}




