package boot.test.modules.user.service;

import boot.test.modules.user.entity.SysPermission;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.security.core.GrantedAuthority;

import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【sys_permission(权限表)】的数据库操作Service
* @createDate 2025-03-28 02:30:57
*/
public interface SysPermissionService extends IService<SysPermission> {
    List<GrantedAuthority> getUserRolesByUserId(String id);
}
