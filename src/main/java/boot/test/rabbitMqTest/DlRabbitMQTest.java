package boot.test.rabbitMqTest;

import boot.MyBootApplication;
import boot.test.rabbitMqTest.conf.DlRabbitMQConfig;
import boot.test.rabbitMqTest.handler.MqSendHelper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;

import org.junit.jupiter.api.Test;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description：
 * @Author：hh
 * @Date：2025/4/28 20:59
 **/
@Slf4j
@SpringBootTest(classes = MyBootApplication.class)
public class DlRabbitMQTest {
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private MqSendHelper mqSendHelper;


    long startTime ;
    // 死信测试 发送端
    @Test
    public void testMainQueue() throws InterruptedException {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("name", "tt");
        mqSendHelper.sendMessageTTL(DlRabbitMQConfig.MAIN_EXCHANGE, DlRabbitMQConfig.MAIN_QUEUE,DlRabbitMQConfig.MAIN_ROUTING_KEY, map,"5000");
        startTime = System.currentTimeMillis();
        Thread.sleep(30000);
    }
 /*   @RabbitListener(queues = DlRabbitMQConfig.MAIN_QUEUE)
    @RabbitHandler
    public void mainQueueConsumer(Map<String, Object>  message, Channel channel, Message amqpMessage) throws Exception {
        try {
            log.info("mainQueue 收到: {}", message);
            // 模拟消费失败
            throw new Exception("Simulated exception");
        } catch (Exception e) {
            log.error("Failed to process message: {}", message, e);
            channel.basicNack(amqpMessage.getMessageProperties().getDeliveryTag(), false, false);
        }
    }*/

    @RabbitListener(queues = DlRabbitMQConfig.DL_QUEUE)
    @RabbitHandler
    public void dlQueueConsumer(Map<String, Object>  message, Channel channel, Message amqpMessage) throws Exception {
        log.info("死信队列: {}", message);
        channel.basicAck(amqpMessage.getMessageProperties().getDeliveryTag(), false);
    }


}
