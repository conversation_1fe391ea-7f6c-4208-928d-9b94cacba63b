package boot.test.rabbitMqTest.conf;

/**
 * @Description：
 * @Author：hh
 * @Date：2025/4/20 17:56
 **/

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MediaMergeMqConfig {

    public static final String MERGE_MEDIA_EXCHANGE = "media.video.merge.exchange";

    public static final String MERGE_MEDIA_QUEUE = "media.video.merge.queue";

    public static final String MERGE_MEDIA_ROUTING_PREFIX = "media.merge";

    @Bean
    public TopicExchange topicExchange(){
        return new TopicExchange(MERGE_MEDIA_EXCHANGE);
    }

    @Bean
    public Queue queue_media_video_merge(){
        return new Queue(MERGE_MEDIA_QUEUE);
    }

    @Bean
    public Binding binding_media_video_merge(){
        return BindingBuilder.bind(queue_media_video_merge()).to(topicExchange()).with(MERGE_MEDIA_ROUTING_PREFIX+".#");
    }
}
