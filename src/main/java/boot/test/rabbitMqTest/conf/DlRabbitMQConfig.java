package boot.test.rabbitMqTest.conf;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description：死信队列
 * @Author：hh
 * @Date：2025/4/28 20:53
 **/
@Configuration
public class DlRabbitMQConfig {

    // 主队列
    public static final String MAIN_QUEUE = "mainQueue";
    // 主交换机
    public static final String MAIN_EXCHANGE = "mainExchange";
    // 主队列的路由键
    public static final String MAIN_ROUTING_KEY = "mainRoutingKey";

    // 死信队列
    public static final String DL_QUEUE = "dlQueue";
    // 死信交换机
    public static final String DL_EXCHANGE = "dlExchange";
    // 死信队列的路由键
    public static final String DL_ROUTING_KEY = "dlRoutingKey";
    @Bean
    public Exchange mainExchange() {
        return ExchangeBuilder.topicExchange(MAIN_EXCHANGE)
                .durable(true)  // 设置交换机为持久化
                .build();
    }
    @Bean
    public Queue mainQueue() {
        return QueueBuilder.durable(MAIN_QUEUE)
                .lazy()
                .deadLetterExchange(DL_EXCHANGE)
                .deadLetterRoutingKey(DL_ROUTING_KEY)
                .build();
    }
    @Bean
    public Binding mainBinding() {
        return BindingBuilder.bind(mainQueue()).to(mainExchange()).with(MAIN_ROUTING_KEY).noargs();
    }
    @Bean
    public Exchange deadLetterExchange() {
        return ExchangeBuilder.topicExchange(DL_EXCHANGE)
                .durable(true)  // 设置交换机为持久化
                .build();
    }
    @Bean
    public Queue deadLetterQueue() {
        return QueueBuilder.durable(DL_QUEUE)
                .lazy()
                .build();
    }

    @Bean
    public Binding deadLetterBinding() {
        return BindingBuilder.bind(deadLetterQueue())
                .to(deadLetterExchange())
                .with(DL_ROUTING_KEY).noargs();
    }


}
