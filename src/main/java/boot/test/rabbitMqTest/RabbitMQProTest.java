package boot.test.rabbitMqTest;




import boot.test.rabbitMqTest.conf.DlRabbitMQConfig;
import boot.test.rabbitMqTest.handler.MqSendHelper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description：
 * @Author：hh
 * @Date：2024/6/13 上午8:20
 **/
@Slf4j
@SpringBootTest
public class RabbitMQProTest {
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private MqSendHelper mqSendHelper;


    @Test
    public void test() throws InterruptedException {
        final HashMap<String, Object> map = new HashMap<String, Object>() {{
            put("test", "test");
            put("test2", "test2");
        }};
        mqSendHelper.sendMessage("testEX", "testQE", "testRK",map);
        Thread.sleep(10000);
    }

    @RabbitListener(queues = "testQE")
    @RabbitHandler
    public String testConsumer(Map<String,Object> tt, Channel channel, Message message) throws Exception {
        log.info("消费消息:{}",new String( message.getBody()));
        /**
         *
         * ACK，用的最多的一种 消费成功-让Mq删除消息
         * deliveryTag:该消息的index
         * false:表示不是批量
         */
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        /**
         * Nack:手动拒绝
         * deliveryTag:该消息的index
         * false:表示不是批量
         * false：被拒绝的是否重新入队列，一般默认false,因为第一次被拒绝后，后面多次肯定也被拒绝
         */
//        channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        /**
         * Reject:手动拒绝,和Nack相比少一个参数
         * deliveryTag:该消息的index
         * false：被拒绝的是否重新入队列，一般默认false,因为第一次被拒绝后，后面多次肯定也被拒绝
         */
//        channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
        return "消费-result";
    }
}
