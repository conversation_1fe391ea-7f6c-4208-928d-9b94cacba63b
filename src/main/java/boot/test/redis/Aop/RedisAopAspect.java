package boot.test.redis.Aop;

import boot.test.redis.annotation.RedisAutoByParams;
import boot.test.redis.util.RedisLock;
import boot.test.redis.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * @Description：
 * @Author：hh
 * @Date：2025/3/29 02:18
 **/
@Slf4j
@Aspect
@Component
public class RedisAopAspect {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private RedisLock redisLock;

    RedisAopAspect() {
        log.info("redis aop init");
    }

    // 切入点
    @Pointcut("@annotation(boot.test.redis.annotation.RedisAutoByParams)")
    public void autoData() {
        // 切入点表达式
    }

    @Around("autoData()")
    public Object around(ProceedingJoinPoint point) throws Throwable {

        // 拿到参数
        Object[] args = point.getArgs();
        // 拿到方法签名
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        RedisAutoByParams annotation = method.getAnnotation(RedisAutoByParams.class);
        // 拿到key前缀
        String keyPre = annotation.key();
        // 拿到过期时间
        long expireTime = annotation.expire();
        StringBuilder params = new StringBuilder();
        // 解析参数
        for (Object arg : args) {
            if (arg != null) {
                params.append(":").append(arg);
            }
        }
        String key = keyPre + params;
        // 查询缓存
        Object result = redisUtil.get(key);
        if (result != null) {
            log.info("缓存命中: {}", key);
            return result;
        }

        long start = System.currentTimeMillis();
        String lockKey = "lock:" + key;
        RLock lock = redisLock.getLock(lockKey);
        boolean lockAcquired = false;
        try {
            // 尝试获取分布式锁
            lock.tryLock(10, expireTime * 60, TimeUnit.SECONDS);
            lockAcquired = lock.tryLock();
            if (lockAcquired || redisLock.tryLockOnNum(lockKey, 10, expireTime * 60, TimeUnit.SECONDS, 3)) {
                log.info("获取锁成功: {}", lockKey);
                // 再次检查缓存
                result = redisUtil.get(key);
                if (result != null) {
                    log.info("缓存命中: {}", key);
                    return result;
                } else {
                    // 执行方法
                    result = point.proceed();
                    // 写入缓存
                    redisUtil.set(key, result, expireTime * 60);
                    log.info("redis写入缓存: {}", key);
                }
            }
        } catch (InterruptedException e) {
            log.error("锁获取被中断: {}", lockKey, e);
            Thread.currentThread().interrupt();
            throw new RuntimeException("锁获取被中断", e);
        } finally {
            //当前锁存在且被当前线程持有
            if((lockAcquired || lock.isLocked()) && lock.isHeldByCurrentThread()){
                // 释放锁
                lock.unlock();
                log.info("释放锁: {}", lockKey);
            }

        }

        log.info("方法执行时间: {} ms", System.currentTimeMillis() - start);
        return result;
    }
}
