package boot.test.redis.annotation;

import java.lang.annotation.*;

/**
 * @Description：
 * @Author：Administrator
 * @Date：2023/5/24 1:00
 **/
@Target({ElementType.METHOD,ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RedisAutoByParams {
    /**
     * key前缀
     *
     * @return
     */
    String key() default "default";

    /**
     * 过期时间
     *
     * @return
     */
    long expire() default 60;

}
