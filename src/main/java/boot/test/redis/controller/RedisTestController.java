package boot.test.redis.controller;

import boot.test.redis.util.RedisUtil;
import boot.test.redis.vo.RedisVo;
import boot.test.modules.order.entity.Order;
import boot.test.modules.order.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * @Description：
 * @Author：hh
 * @Date：2025/3/26 23:55
 **/

@RequestMapping("/redis")
@RestController
public class RedisTestController {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private OrderService orderService;

    private  final String key_prefix = "order:";
    @PostMapping("/set")
    public ResponseEntity<String> set(RedisVo<String> redisVo) {
        redisUtil.set(redisVo.getKey(), redisVo.getValue());
        return ResponseEntity.ok("ok");
    }

    @Cacheable(cacheNames = "orderCache", key = "#redisVo.key")
    @PostMapping("/getData")
    public Order get(RedisVo<Object> redisVo) {
        Order result = orderService.getBaseMapper().selectById(redisVo.getKey());
        System.out.println("走了数据库");
        return result;
    }


    @PostMapping("/incrementCounter")
    public ResponseEntity<String> incrementCounter(@RequestParam String key) {
        // 增加计数器
        long count = redisUtil.increment(key,1);
        return ResponseEntity.ok("Counter incremented to: " + count);
    }


}
