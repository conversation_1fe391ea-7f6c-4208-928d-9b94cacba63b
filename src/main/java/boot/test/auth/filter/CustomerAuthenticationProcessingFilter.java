package boot.test.auth.filter;

import boot.test.auth.dto.LoginParams;
import boot.test.auth.token.CustomerAuthenticationToken;
import boot.test.common.constant.CommonConstant;
import boot.test.common.constant.LoginType;
import org.springframework.lang.Nullable;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.security.web.util.matcher.RequestMatcher;
import org.springframework.util.Assert;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;

/**
 * @Description：
 * @Author：hh
 * @Date：2025/4/1 12:05
 **/
public class CustomerAuthenticationProcessingFilter  extends
        AbstractAuthenticationProcessingFilter {
    private Map<String, String[]> data ;
    private static String method = "POST";
    // ~ Constructors
    // ===================================================================================================

    public CustomerAuthenticationProcessingFilter() {
        super(new AntPathRequestMatcher("/Login"));
    }
    public CustomerAuthenticationProcessingFilter(String path, String method) {
        super(new AntPathRequestMatcher(path, CustomerAuthenticationProcessingFilter.method = method.toUpperCase()));
    }

    // ~ Methods
    // ========================================================================================================

    public Authentication attemptAuthentication(HttpServletRequest request,
                                                HttpServletResponse response) throws AuthenticationException {
        if (!request.getMethod().equals(method)) {
            throw new AuthenticationServiceException(
                    "Authentication method not supported: " + request.getMethod());
        }
        final Integer loginType = "".equals(request.getParameter(CommonConstant.LOGIN_TYPE))?0:Integer.parseInt(request.getParameter(CommonConstant.LOGIN_TYPE));
        AbstractAuthenticationToken authenticationToken;
        if (LoginType.USERNAME.getCode().equals(loginType)){
            if ("".equals(getData(request,"username")) || "".equals(getData(request,"password")))
                throw new AuthenticationServiceException("用户名或密码不能为空");
            authenticationToken = new UsernamePasswordAuthenticationToken(getData(request,"username"),getData(request,"password"));
        }else {
            authenticationToken =new CustomerAuthenticationToken(request.getParameterMap());
        }
        setDetails(request, authenticationToken);
        return this.getAuthenticationManager().authenticate(authenticationToken);
    }

    protected void setDetails(HttpServletRequest request,
                              AbstractAuthenticationToken authRequest) {
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }
    private String getData(HttpServletRequest request,String key){
        return  request.getParameter(key)==null?"":request.getParameter(key);
    }
}
