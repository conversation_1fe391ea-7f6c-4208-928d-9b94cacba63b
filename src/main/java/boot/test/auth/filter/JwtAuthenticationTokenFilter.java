package boot.test.auth.filter;

import boot.test.auth.util.JwtUtils;
import boot.test.auth.dto.LoginUser;
import boot.test.common.constant.CacheConst;
import boot.test.redis.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;

/**
 * * 继承了OncePerRequestFilter抽象类，该抽象类在每次请求时只执行一次过滤，即它的作用就是保证一次请求只通过一次filter,
 * * 重写其doFilterInternal方法来自定义处理逻辑
 */
@Component
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {
    /**
     * 直接将我们前面写好的service注入进来，通过service获取到当前用户的权限
     * */
    @Autowired
    private RedisUtil redisCache;

    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, FilterChain filterChain) throws ServletException, IOException {
        // 验证当前请求中的JWT token是否有效
        if (JwtUtils.checkToken(httpServletRequest)) {
            // 从请求中提取用户的ID
            String id = JwtUtils.getUseridByJwtToken(httpServletRequest);
            // 根据用户的ID从Redis缓存中获取登录用户的信息 todo
//            ParserConfig.getGlobalInstance().addAccept("org.springframework.security.core.authority.SimpleGrantedAuthority.");
            LoginUser userDetails = (LoginUser)redisCache.get(CacheConst.LOGIN_USER_KEY + id);
            // 如果从Redis中获取的用户信息不为空，并且当前的SecurityContextHolder中没有认证信息，则进行JWT token验证
            if (!Objects.isNull(userDetails) && Objects.isNull(SecurityContextHolder.getContext().getAuthentication())) {
                // 打印从用户信息中获取的用户名
                System.out.println("自定义JWT过滤器获得用户名为-" + userDetails.getUsername());
                // 创建一个UsernamePasswordAuthenticationToken对象，包含用户信息和权限，但密码为空
                UsernamePasswordAuthenticationToken authentication =
                        new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                // 设置认证对象的详细信息，包括当前的HTTP请求信息
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(httpServletRequest));
                // 将认证对象放入SecurityContextHolder中，以便后续使用 //Context底层使用的是ThreadLocal
                SecurityContextHolder.getContext().setAuthentication(authentication);
            }
        }
        Map<String, String[]> parameterMap = httpServletRequest.getParameterMap();
        // 将请求和响应传递给过滤器链中的下一个过滤器
        filterChain.doFilter(httpServletRequest, httpServletResponse);
    }
}