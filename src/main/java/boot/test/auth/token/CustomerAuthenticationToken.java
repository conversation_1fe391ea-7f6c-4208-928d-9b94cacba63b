package boot.test.auth.token;

import boot.test.auth.dto.LoginParams;
import boot.test.common.constant.LoginType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Map;

/**
 * @Description：描述该类的功能，即手机认证令牌类，用于手机号和验证码的认证。
 * @Author：hh
 * @Date：2025/4/1 06:29
 **/

public class CustomerAuthenticationToken extends AbstractAuthenticationToken {
    // 定义序列化版本号，用于序列化和反序列化时保持类的兼容性
    private static final long serialVersionUID = 530L;

    private Object principal;
    private Object credentials;
    // 构造函数，用于创建未认证的令牌
    public CustomerAuthenticationToken(Map<String,String[]> principal) {
        // 调用父类构造函数，传入null权限集合
        super(null);
        this.principal= principal;
        // 设置令牌为未认证状态
        this.setAuthenticated(false);
    }
    // 构造函数，用于创建已认证的令牌
    public CustomerAuthenticationToken(Map<String,String[]> principal, Object credentials, Collection<? extends GrantedAuthority> authorities) {
        // 调用父类构造函数，传入权限集合
        super(authorities);
        // 初始化用户主体信息和凭证信息
        this.principal = principal;
        this.credentials = credentials;
        // 设置令牌为已认证状态
        this.setAuthenticated(true);
    }


    // 重写获取凭证信息的方法
    @Override
    public Object getCredentials() {
        return this.credentials;
    }

    // 重写获取用户主体信息的方法
    @Override
    public Object getPrincipal() {
        return this.principal;
    }

}
