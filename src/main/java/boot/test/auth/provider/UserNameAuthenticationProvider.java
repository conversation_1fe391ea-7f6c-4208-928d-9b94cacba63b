package boot.test.auth.provider;

import boot.test.auth.dto.LoginUser;
import boot.test.auth.token.CustomerAuthenticationToken;
import boot.test.common.constant.CacheConst;
import boot.test.common.constant.ResultCode;
import boot.test.modules.user.entity.SysUser;
import boot.test.modules.user.mapper.SysUserMapper;
import boot.test.modules.user.service.SysPermissionService;
import boot.test.modules.user.service.SysUserService;
import boot.test.redis.util.RedisUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.AbstractUserDetailsAuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.List;
import java.util.Map;

/**
 * @Description：使用默认的认证 直接实现UserDetailsService接口就可以
 * @Author：hh
 * @Date：2025/4/1 06:26
 **/
@AllArgsConstructor
@Slf4j
public class UserNameAuthenticationProvider implements UserDetailsService {
    private SysUserService sysUserService;
    private SysPermissionService sysPermissionService;
    /**
     * 根据用户名查找数据库，判断是否存在这个用户
     *
     * @param username 要查找的用户名
     * @return 包含用户信息和权限的UserDetails对象
     * @throws UsernameNotFoundException 如果用户名未在数据库中找到，则抛出此异常
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 用户名必须是唯一的，不允许重复
        SysUser sysUser = sysUserService.getBaseMapper().selectOne(new QueryWrapper<SysUser>().eq("user_name", username));
        // 如果用户未找到，抛出UsernameNotFoundException异常
        if (sysUser == null) {
            throw new UsernameNotFoundException("用户名未找到: " + username);
        }
        List<GrantedAuthority> grantedAuthorities = sysPermissionService.getUserRolesByUserId(sysUser.getId());

        // 创建LoginUser对象，包含用户信息和权限
        LoginUser loginUser = new LoginUser(sysUser, grantedAuthorities);
        // 返回LoginUser对象
        return loginUser;
    }


}
