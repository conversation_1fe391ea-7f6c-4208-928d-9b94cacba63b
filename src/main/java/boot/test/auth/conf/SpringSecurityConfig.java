package boot.test.auth.conf;

import boot.test.auth.filter.CustomerAuthenticationProcessingFilter;
import boot.test.auth.filter.JwtAuthenticationTokenFilter;
import boot.test.auth.handler.*;
import boot.test.auth.provider.CustomerAuthenticationProvider;
import boot.test.auth.provider.MobileAuthenticationProvider;
import boot.test.auth.provider.UserNameAuthenticationProvider;
import boot.test.modules.user.service.SysPermissionService;
import boot.test.modules.user.service.SysUserService;
import boot.test.redis.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.logout.LogoutFilter;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)  //此注解用于开启controller接口注解权限认证
public class SpringSecurityConfig extends WebSecurityConfigurerAdapter  {

    @Autowired
    private SecurityDeniedHandler deniedHandler;

    @Autowired
    private SecurityFailureHandler failureHandler;

    @Autowired
    private SecurityLogOutHandler logOutHandler;

    @Autowired
    private SecuritySuccessHandler successHandler;

    @Autowired
    private SecurityNotLoginHandler notLoginHandler;



    @Autowired
    private JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private SysUserService userService;
    @Autowired
    private SysPermissionService sysPermissionService;
    /**
     * 对请求进行鉴权的配置
     *
     * @param http
     * @throws Exception
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        //关闭cors
        http.cors()
                .and().csrf().disable().sessionManagement()
                // 不通过session保存securityContext
                .sessionCreationPolicy(SessionCreationPolicy.STATELESS);

        /**
         * anyRequest          |   匹配所有请求路径
         * access              |   SpringEl表达式结果为true时可以访问
         * anonymous           |   匿名可以访问
         * denyAll             |   用户不能访问
         * fullyAuthenticated  |   用户完全认证可以访问（非remember-me下自动登录）
         * hasAnyAuthority     |   如果有参数，参数表示权限，则其中任何一个权限可以访问
         * hasAnyRole          |   如果有参数，参数表示角色，则其中任何一个角色可以访问
         * hasAuthority        |   如果有参数，参数表示权限，则其权限可以访问
         * hasIpAddress        |   如果有参数，参数表示IP地址，如果用户IP和参数匹配，则可以访问
         * hasRole             |   如果有参数，参数表示角色，则其角色可以访问
         * permitAll           |   用户可以任意访问
         * rememberMe          |   允许通过remember-me登录的用户访问
         * authenticated       |   用户登录后可访问
         */
        http.authorizeRequests()
//                .antMatchers("/sysUser/login").anonymous()   // 允许匿名访问，登录后不能访问
                .antMatchers("/sysUser/signUp").permitAll()  //这是测试代码。。注册用户
                .antMatchers("/api/videos/*").permitAll()  //这是测试代码。。注册用户
                .antMatchers("/test/*").permitAll()  //这是测试代码。。注册用户
                .antMatchers("/*.html").permitAll()
                .anyRequest().authenticated() //其他请求都需要鉴权认证
                .and()
                .exceptionHandling()//异常处理
                .authenticationEntryPoint(notLoginHandler)
                .accessDeniedHandler(deniedHandler)
                .and()
                .formLogin()  // 登录
              /*  .loginPage("/sysUser/login")*/  //登录页面
                .loginProcessingUrl("/sysUser/login")//登录路径
                .permitAll()  //允许所有用户
                .successHandler(successHandler)  //登录成功处理逻辑
                .failureHandler(failureHandler)  //登录失败处理逻辑
                .and()
                .logout()      // 退出
                .permitAll()   //允许所有用户
                .logoutSuccessHandler(logOutHandler)  //退出成功处理逻辑
                .deleteCookies("JSESSIONID") ;  //登出之后删除cookie

        //JWD        //将jwtAuthenticationTokenFilter过滤器添加到UsernamePasswordAuthenticationFilter之前
        http.addFilterBefore(jwtAuthenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(customerAuthenticationProcessingFilter(), UsernamePasswordAuthenticationFilter.class);
        http.addFilterBefore(jwtAuthenticationTokenFilter, LogoutFilter.class);
        http.headers().cacheControl();
    }

    //配置securityUserService用户认证
    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        auth.userDetailsService(userNameAuthenticationProvider());
        auth.authenticationProvider(mobileAuthenticationProvider());
        auth.authenticationProvider(customerAuthenticationProvider());
    }
    @Bean
    public  UserNameAuthenticationProvider userNameAuthenticationProvider(){
        return new UserNameAuthenticationProvider(userService,sysPermissionService);
    }
    @Bean
    public CustomerAuthenticationProvider customerAuthenticationProvider() {
        return new CustomerAuthenticationProvider(userService,redisUtil);
    }

    @Bean
    public MobileAuthenticationProvider mobileAuthenticationProvider(){
        return new MobileAuthenticationProvider(userService,redisUtil);
    }

    /**
     * 默认开启密码加密，前端传入的密码Security会在加密后和数据库中的密文进行比对，一致的话就登录成功
     * 所以必须提供一个加密对象，供security加密前端明文密码使用
     * @return
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        //加密解密对象
        return new BCryptPasswordEncoder();
    }


    @Bean
    public CustomerAuthenticationProcessingFilter customerAuthenticationProcessingFilter() throws Exception {
        CustomerAuthenticationProcessingFilter filter = new CustomerAuthenticationProcessingFilter("/sysUser/customLogin","POST");
        filter.setAuthenticationManager(authenticationManagerBean());
        return filter;
    }
    // 将 AuthenticationManagerBuilder 注入
    // 用于处理认证请求。它定义了一个authenticate方法，该方法接受一个Authentication对象作为参数，并返回一个经过认证的Authentication对象。
    @Override
    @Bean
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }
}
