package boot.test.common.constant;


public enum LoginType {


    USERNAME(1, "账号密码登录"),
    MOBILE(2, "手机号登录")
    ;
    private  Integer code;
    private  String message;

    LoginType(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    public Integer getCode() {
        return code;
    }
    public String getMessage() {
        return message;
    }

    public static String getMessageByCode(Integer code) {
        for (LoginType ele : values()) {
            if (ele.getCode().equals(code)) {
                return ele.getMessage();
            }
        }
        return null;
    }
}
