<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="boot.test.modules.user.entity.SysUser">

    <resultMap id="BaseResultMap" type="boot.test.modules.user.entity.SysUser">
            <id property="id" column="id" />
            <result property="userName" column="user_name" />
            <result property="userName" column="nick_name" />
            <result property="password" column="password" />
             <result property="mobileNumber" column="mobile_number" />
            <result property="lastLoginTime" column="last_login_time" />
            <result property="enabled" column="enabled" />
            <result property="notExpired" column="not_expired" />
            <result property="accountNotLocked" column="account_not_locked" />
            <result property="credentialsNotExpired" column="credentials_not_expired" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="createUser" column="create_user" />
            <result property="updateUser" column="update_user" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_name,nick_name,password,mobile_number,last_login_time,enabled,
        not_expired,account_not_locked,credentials_not_expired,create_time,update_time,
        create_user,update_user
    </sql>
</mapper>
