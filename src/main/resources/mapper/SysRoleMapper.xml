<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="boot.test.modules.user.mapper.SysRoleMapper">

    <resultMap id="BaseResultMap" type="boot.test.modules.user.entity.SysRole">
            <id property="id" column="id" />
            <result property="roleCode" column="role_code" />
            <result property="roleName" column="role_name" />
            <result property="roleDescription" column="role_description" />
    </resultMap>

    <sql id="Base_Column_List">
        id,role_code,role_name,role_description
    </sql>

</mapper>
