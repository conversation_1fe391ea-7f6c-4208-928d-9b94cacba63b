<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="boot.test.modules.order.mapper.OrderMapper">

    <resultMap id="BaseResultMap" type="boot.test.modules.order.entity.Order">
            <id property="id" column="id" />
            <result property="billNo" column="bill_no" />
            <result property="billDate" column="bill_date" />
            <result property="customerId" column="customer_id" />
            <result property="totalAmount" column="total_amount" />
            <result property="status" column="status" />
    </resultMap>

    <sql id="Base_Column_List">
        id,bill_no,bill_date,customer_id,total_amount,status
    </sql>
    <select id="selectTest" resultType="java.util.Map">
        select customer_id ,count(total_amount) as total_amount
        from my_order
        group by customer_id
    </select>
</mapper>
