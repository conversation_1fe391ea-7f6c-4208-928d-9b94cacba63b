<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="boot.test.modules.user.mapper.SysPermissionMapper">

    <resultMap id="BaseResultMap" type="boot.test.modules.user.entity.SysPermission">
            <id property="id" column="id" />
            <result property="permissionCode" column="permission_code" />
            <result property="permissionName" column="permission_name" />
    </resultMap>

    <sql id="Base_Column_List">
        id,permission_code,permission_name
    </sql>
    <select id="getUserRolesByUserId" resultType="boot.test.modules.user.entity.SysPermission">
        SELECT
            p.*
        FROM
            sys_user AS u
                LEFT JOIN sys_user_role_relation AS ur
                          ON u.id = ur.user_id
                LEFT JOIN sys_role AS r
                          ON r.id = ur.role_id
                LEFT JOIN sys_role_permission_relation AS rp
                          ON r.id = rp.role_id
                LEFT JOIN sys_permission AS p
                          ON p.id = rp.permission_id
        WHERE u.id = #{userId}

    </select>

</mapper>
