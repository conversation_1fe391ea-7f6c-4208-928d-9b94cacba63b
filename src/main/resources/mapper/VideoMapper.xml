<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="boot.test.io.mapper.VideoMapper">

    <!-- 新增：updateStatusById 方法的 SQL 实现 -->
    <update id="updateStatusById">
        UPDATE video
        SET status = #{status}
        WHERE id = #{id}
    </update>

</mapper>{
        "mcpServers": {
        "chrome-mcp-server": {
        "command": "type:",
        "args": [
        "streamable-http",
        "",
        "",
        "",
        "",
        "",
        "",
        "",
        "url:",
        "http://127.0.0.1:12306/mcp"
        ]
        }
        }
        }