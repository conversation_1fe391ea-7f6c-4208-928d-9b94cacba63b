server:
  servlet:
    context-path: /javaDemo
  tomcat:
    connection-timeout: 20000
#    threads:
#      max: 300
#      min-spare: 10
  port: 8081
spring:
  application:
    name: javaDemo
  rabbitmq:
    # 配置RabbitMQ的连接信息
    host: localhost
    port: 5672
    username: admin
    password: admin
    virtual-host: /
    connection--timeout: 1s #设置MQ的连接超时时间
    template: # 生产者重试
      retry:
        enabled: true #开启超时重试机制
        initial-interval: 10ms #失败后的初始等待时间
        multiplier: 1 #失败后下次的等待时长倍数，下次等待时长=interval-interval*multiplier
        max-attempts: 3 #最大重试次数

    publisher-confirm-type: correlated #处理NACK 开启 publisher confirm确认机制，并设置confirm.类型 -》不推荐 网络开销大 需要实现ApplicationContextAware(也就是Spring容器加载完成之后) 获取RabbitTemplate设置ReturnCallback
    publisher-returns: true #开启publisher return.机制
    listener:
      simple:
        prefetch: 2 # 设置每个消费者能处理的消息数量
        acknowledge-mode: auto # 消费端由spring(这里设置auto的话)自己处理消息确认机制 1 ack 2 Nack不消费消息 3 nack 删除消息
        retry:
          enabled: true #开启消费者失败重试 默认启动但是是无限重试 这里主要需要限制重试次数
          initial-interval: 1000ms #初始的失败等待时长为1秒
          multiplier: 1 #下次失败的等待时长倍数，下次等待时长=multiplier*last-interval
          maX-attempts: 3 #最大重试次数
          stateless: false #true无状态，：false有状态。如果业务中包含事务，这里改为false
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *************************************************************************************************************
    username: root
    password: 123456
  redis:
    host: 127.0.0.1
    port: 6379
    password: 123456
    timeout: 5000ms
    # 配置Lettuce连接池的参数
    lettuce:
      pool:
        # 最大空闲连接数，表示在任何时间点上，连接池中最多可以保持的空闲连接数量
        max-idle: 16
        # 最大活动连接数，表示在任何时间点上，连接池中最多可以有的活跃（即正在被使用的）连接数量
        max-active: 32
        # 最小空闲连接数，表示在任何时间点上，连接池中至少会保持的空闲连接数量
        min-idle: 8
        # 最长等待时间，表示当没有可用连接时，调用者愿意等待获取连接的最大时间
        max-wait: 2000ms


mybatis-plus:
  # 指定sql映射文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体对象的扫描包
  type-aliases-package: boot.test.modules.*.entity
  # 行为配置
  configuration:
    mapUnderscoreToCamelCase: true
    aggressiveLazyLoading: true
    autoMappingBehavior: PARTIAL

    autoMappingUnknownColumnBehavior: NONE
    localCacheScope: SESSION
    # 开启Mybatis二级缓存，默认为 true
    cacheEnabled: true
    # 关闭 sql 日志【注释即可打印日志】
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  global-config:
    db-config:
      # id 生成策略全局配置, 雪花算法
      id-type: ASSIGN_ID
    # 不显示 banner
    banner: false
  # 开启驼峰命名

  # MinIO 配置
minio:
  endpoint: http://localhost:9000
  accessKey: minioadmin
  secretKey: minioadmin
  bucketName: defaultbucket

  # FFmpeg 路径
ffmpeg:
  path: E:/study/code/framework/springcloud-learn/cloud2025-xuecheng/ffmpeg/bin/ffmpeg.exe

  # 上传临时目录
upload:
  tempDir: D:/study/code/javaBase/JavaDemo/temp
