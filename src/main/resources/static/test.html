<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Upload and Stream Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-group input[type="file"] {
            margin-bottom: 10px;
        }
        .form-group button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        .form-group button:hover {
            background-color: #0056b3;
        }
        .video-player {
            margin-top: 20px;
        }
        .video-player video {
            width: 100%;
            max-width: 800px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Video Upload and Stream Test</h1>

        <div class="form-group">
            <label for="file">Select a video file:</label>
            <input type="file" id="file" accept="video/*">
            <button onclick="uploadVideo()">Upload Video</button>
        </div>

        <div class="form-group">
            <label for="chunked-file">Select a video file for chunked upload:</label>
            <input type="file" id="chunked-file" accept="video/*">
            <button onclick="uploadChunkedVideo()">Upload Chunked Video</button>
        </div>

        <div class="video-player">
            <h2>Video Stream</h2>
            <video controls>
                <source id="video-source" src="" type="video/mp4">
                Your browser does not support the video tag.
            </video>
        </div>
    </div>

    <script>
        function uploadVideo() {
            const fileInput = document.getElementById('file');
            const file = fileInput.files[0];
            if (!file) {
                alert('Please select a file first.');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);

            fetch('/test/api/videos/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                alert('Video uploaded successfully!');
                console.log(data);
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to upload video.');
            });
        }

        function uploadChunkedVideo() {
            const fileInput = document.getElementById('chunked-file');
            const file = fileInput.files[0];
            if (!file) {
                alert('Please select a file first.');
                return;
            }

            const chunkSize = 1024 * 1024 * 5; // 5MB per chunk
            const totalChunks = Math.ceil(file.size / chunkSize);
            let currentChunk = 1;

            function uploadNextChunk() {
                const start = (currentChunk - 1) * chunkSize;
                const end = Math.min(currentChunk * chunkSize, file.size);
                const chunk = file.slice(start, end);

                const formData = new FormData();
                formData.append('file', chunk);
                formData.append('chunkNumber', currentChunk);
                formData.append('totalChunks', totalChunks);
                formData.append('fileName', file.name);

                fetch('/test/api/videos/upload/chunk', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.text())
                .then(message => {
                    console.log(message);
                    if (currentChunk < totalChunks) {
                        currentChunk++;
                        uploadNextChunk();
                    } else {
                        alert('Chunked video uploaded successfully!');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Failed to upload chunked video.');
                });
            }

            uploadNextChunk();
        }

        function playVideo(fileName) {
            const videoSource = document.getElementById('video-source');
            videoSource.src = `/test/api/videos/${fileName}`;
            videoSource.parentElement.load();
        }
    </script>
</body>
</html>