<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Upload and Play Demo</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>Video Upload and Play Demo</h1>
    
    <h2>Upload Video</h2>
    <input type="file" id="videoFile" />
    <button onclick="uploadVideo()">Upload</button>
    
    <h2>Play Video</h2>
    <button onclick="playVideo()">Play</button>
    <video id="videoPlayer" controls></video>

    <script>
        let currentChunk = 0;
        let totalChunks = 0;
        let videoId = 'testVideo';

        function uploadVideo() {
            const fileInput = document.getElementById('videoFile');
            const file = fileInput.files[0];
            const chunkSize = 1024 * 1024 * 5; // 5MB per chunk
            const totalChunks = Math.ceil(file.size / chunkSize);
            axios.get('/javaDemo/api/video/test')
            for (let i = 0; i < totalChunks; i++) {
                const chunk = file.slice(i * chunkSize, (i + 1) * chunkSize);
                const formData = new FormData();
                formData.append('file', chunk);
                formData.append('fileName', file.name);
                formData.append('chunkIndex', i);
                formData.append('totalChunks', totalChunks);

                axios.post('/javaDemo/api/video/chunk', formData)
                    .then(response => {
                        console.log(response.data);
                        if (i === totalChunks - 1) {
                            mergeChunks(file.name, totalChunks);
                        }
                    })
                    .catch(error => console.error('Error uploading chunk:', error));
            }
        }

        function mergeChunks(fileName, totalChunks) {
            axios.post('/javaDemo/api/video/merge', {
                fileName: fileName,
                totalChunks: totalChunks
            })
            .then(response => {
                console.log(response.data);
                transcodeVideo(`/uploads/${fileName}.mp4`);
            })
            .catch(error => console.error('Error merging chunks:', error));
        }

        function transcodeVideo(filePath) {
            axios.post('/javaDemo/api/video/transcode', {
                filePath: filePath
            })
            .then(response => {
                console.log(response.data);
                console.log('Video transcoded successfully');
            })
            .catch(error => console.error('Error transcoding video:', error));
        }

        function playVideo() {
            axios.get(`/javaDemo/api/video/play/${videoId}`, {
                params: { chunkSize: 1024 * 1024 * 5 } // 5MB per chunk
            })
            .then(response => {
                const chunkUrls = response.data;
                totalChunks = chunkUrls.length;
                loadNextChunk();
            })
            .catch(error => console.error('Error getting chunk play urls:', error));
        }

        function loadNextChunk() {
            if (currentChunk < totalChunks) {
                const videoPlayer = document.getElementById('videoPlayer');
                videoPlayer.src = chunkUrls[currentChunk];
                videoPlayer.addEventListener('ended', () => {
                    currentChunk++;
                    loadNextChunk();
                });
            }
        }
    </script>
</body>
</html>